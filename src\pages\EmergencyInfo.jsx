import React, { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../contexts/SimpleAuthContext";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";

const EmergencyInfo = () => {
  const { user } = useAuth();
  const [selectedCountry, setSelectedCountry] = useState("general");

  const emergencyNumbers = {
    general: {
      name: "International",
      numbers: [
        { service: "International Emergency", number: "112", description: "Works in most countries worldwide" },
        { service: "Medical Emergency", number: "112", description: "International standard for medical emergencies" }
      ]
    },
    kenya: {
      name: "Kenya",
      numbers: [
        { service: "Emergency Services", number: "999", description: "Police, Fire, Medical" },
        { service: "International Emergency", number: "112", description: "Alternative emergency number" },
        { service: "Police", number: "999", description: "Kenya Police Service" },
        { service: "Medical Emergency", number: "999", description: "Ambulance services" }
      ]
    },
    nigeria: {
      name: "Nigeria",
      numbers: [
        { service: "Emergency Services", number: "199", description: "National emergency number" },
        { service: "International Emergency", number: "112", description: "GSM emergency number" },
        { service: "Police", number: "199", description: "Nigeria Police Force" },
        { service: "Fire Service", number: "199", description: "Federal Fire Service" }
      ]
    },
    southafrica: {
      name: "South Africa",
      numbers: [
        { service: "Emergency Services", number: "10177", description: "National emergency number" },
        { service: "Police", number: "10111", description: "South African Police Service" },
        { service: "Medical Emergency", number: "10177", description: "Emergency Medical Services" },
        { service: "Fire Department", number: "10177", description: "Fire and rescue services" }
      ]
    },
    ghana: {
      name: "Ghana",
      numbers: [
        { service: "Emergency Services", number: "191", description: "National emergency number" },
        { service: "Police", number: "191", description: "Ghana Police Service" },
        { service: "Fire Service", number: "192", description: "Ghana National Fire Service" },
        { service: "Ambulance", number: "193", description: "National Ambulance Service" }
      ]
    },
    usa: {
      name: "United States",
      numbers: [
        { service: "Emergency Services", number: "911", description: "Police, Fire, Medical" },
        { service: "Poison Control", number: "1-************", description: "24/7 poison emergency hotline" }
      ]
    },
    uk: {
      name: "United Kingdom",
      numbers: [
        { service: "Emergency Services", number: "999", description: "Police, Fire, Medical" },
        { service: "Non-Emergency Police", number: "101", description: "Non-urgent police matters" },
        { service: "NHS Non-Emergency", number: "111", description: "Medical advice and guidance" }
      ]
    }
  };

  const emergencySymptoms = [
    {
      category: "Immediate Life-Threatening",
      symptoms: [
        "Chest pain or pressure",
        "Difficulty breathing or shortness of breath",
        "Severe bleeding that won't stop",
        "Loss of consciousness or fainting",
        "Severe allergic reaction (anaphylaxis)",
        "Signs of stroke (face drooping, arm weakness, speech difficulty)",
        "Severe burns",
        "Suspected poisoning"
      ],
      action: "Call emergency services immediately"
    },
    {
      category: "Urgent Medical Attention",
      symptoms: [
        "High fever (over 103°F/39.4°C)",
        "Severe abdominal pain",
        "Persistent vomiting",
        "Signs of dehydration",
        "Severe headache with vision changes",
        "Broken bones or suspected fractures",
        "Deep cuts requiring stitches",
        "Severe mental health crisis"
      ],
      action: "Seek immediate medical care or call emergency services"
    },
    {
      category: "Seek Medical Care Soon",
      symptoms: [
        "Persistent fever",
        "Moderate pain that doesn't improve",
        "Unusual rash or skin changes",
        "Persistent cough",
        "Minor cuts or injuries",
        "Mild to moderate anxiety or depression"
      ],
      action: "Contact healthcare provider or use VoiceHealth AI for guidance"
    }
  ];

  const preparednessChecklist = [
    {
      category: "Emergency Contacts",
      items: [
        "Local emergency services numbers saved in phone",
        "Primary healthcare provider contact information",
        "Emergency contact person (family/friend)",
        "Poison control center number",
        "Insurance information readily available"
      ]
    },
    {
      category: "Medical Information",
      items: [
        "List of current medications and dosages",
        "Known allergies and medical conditions",
        "Blood type and important medical history",
        "Emergency medical information card in wallet",
        "Medical alert bracelet/necklace if applicable"
      ]
    },
    {
      category: "Emergency Supplies",
      items: [
        "First aid kit with basic supplies",
        "Emergency medications (inhalers, EpiPens, etc.)",
        "Flashlight and extra batteries",
        "Emergency water and non-perishable food",
        "Battery-powered or hand-crank radio"
      ]
    }
  ];

  return (
    <>
      <Navbar />
      <main className="bg-white">
        {/* Hero Section */}
        <section className="bg-red-600 text-white">
          <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <h1 className="text-4xl sm:text-5xl font-extrabold mb-6">
                Emergency Information
              </h1>
              <p className="text-xl max-w-3xl mx-auto mb-8 opacity-90">
                Critical emergency contacts, symptoms to watch for, and preparedness information to keep you and your loved ones safe.
              </p>
              <div className="bg-red-700 rounded-lg p-6 max-w-2xl mx-auto">
                <p className="text-lg font-semibold mb-2">⚠️ MEDICAL EMERGENCY?</p>
                <p className="mb-4">If you're experiencing a life-threatening emergency, don't wait. Call emergency services immediately.</p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a href="tel:112" className="bg-white text-red-600 px-6 py-3 rounded-lg font-bold text-lg hover:bg-gray-100">
                    📞 Call 112 (International)
                  </a>
                  <a href="tel:911" className="bg-white text-red-600 px-6 py-3 rounded-lg font-bold text-lg hover:bg-gray-100">
                    📞 Call 911 (US/Canada)
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Emergency Numbers by Country */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Emergency Numbers by Country</h2>
              <p className="text-lg text-gray-600">
                Select your country to view local emergency contact information
              </p>
            </div>

            {/* Country Selector */}
            <div className="max-w-md mx-auto mb-8">
              <select
                value={selectedCountry}
                onChange={(e) => setSelectedCountry(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="general">International / General</option>
                <option value="kenya">Kenya</option>
                <option value="nigeria">Nigeria</option>
                <option value="southafrica">South Africa</option>
                <option value="ghana">Ghana</option>
                <option value="usa">United States</option>
                <option value="uk">United Kingdom</option>
              </select>
            </div>

            {/* Emergency Numbers Display */}
            <div className="max-w-4xl mx-auto">
              <div className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
                <div className="bg-red-50 px-6 py-4 border-b border-gray-200">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Emergency Numbers - {emergencyNumbers[selectedCountry].name}
                  </h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {emergencyNumbers[selectedCountry].numbers.map((emergency, index) => (
                      <div key={index} className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-semibold text-gray-900">{emergency.service}</h4>
                          <a
                            href={`tel:${emergency.number}`}
                            className="bg-red-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-red-700"
                          >
                            📞 {emergency.number}
                          </a>
                        </div>
                        <p className="text-gray-600 text-sm">{emergency.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* When to Seek Emergency Care */}
        <section className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">When to Seek Emergency Care</h2>
              <p className="text-lg text-gray-600">
                Recognize the signs and symptoms that require immediate medical attention
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {emergencySymptoms.map((category, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className={`px-6 py-4 ${
                    index === 0 ? 'bg-red-100' : index === 1 ? 'bg-yellow-100' : 'bg-green-100'
                  }`}>
                    <h3 className={`text-lg font-semibold ${
                      index === 0 ? 'text-red-800' : index === 1 ? 'text-yellow-800' : 'text-green-800'
                    }`}>
                      {category.category}
                    </h3>
                  </div>
                  <div className="p-6">
                    <ul className="space-y-2 mb-4">
                      {category.symptoms.map((symptom, symptomIndex) => (
                        <li key={symptomIndex} className="flex items-start">
                          <span className={`w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 ${
                            index === 0 ? 'bg-red-500' : index === 1 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}></span>
                          <span className="text-gray-700 text-sm">{symptom}</span>
                        </li>
                      ))}
                    </ul>
                    <div className={`p-3 rounded-lg ${
                      index === 0 ? 'bg-red-50 border border-red-200' : 
                      index === 1 ? 'bg-yellow-50 border border-yellow-200' : 
                      'bg-green-50 border border-green-200'
                    }`}>
                      <p className={`text-sm font-medium ${
                        index === 0 ? 'text-red-800' : index === 1 ? 'text-yellow-800' : 'text-green-800'
                      }`}>
                        Action: {category.action}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Emergency Preparedness */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Emergency Preparedness Checklist</h2>
              <p className="text-lg text-gray-600">
                Be prepared for medical emergencies with these essential items and information
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {preparednessChecklist.map((category, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">{category.category}</h3>
                  </div>
                  <div className="p-6">
                    <ul className="space-y-3">
                      {category.items.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start">
                          <input
                            type="checkbox"
                            className="mt-1 mr-3 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                          />
                          <span className="text-gray-700 text-sm">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* VoiceHealth AI Emergency Features */}
        <section className="bg-blue-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">VoiceHealth AI Emergency Features</h2>
              <p className="text-lg text-gray-600">
                How our platform helps in emergency situations
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm text-center">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">2-Second Response</h3>
                <p className="text-gray-600 text-sm">Emergency detection and response within 2 seconds</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm text-center">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Auto-Dial Emergency</h3>
                <p className="text-gray-600 text-sm">Automatic connection to local emergency services</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm text-center">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Location Sharing</h3>
                <p className="text-gray-600 text-sm">Share your location with emergency responders</p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm text-center">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Medical History</h3>
                <p className="text-gray-600 text-sm">Instant access to your medical information</p>
              </div>
            </div>

            {user && (
              <div className="mt-8 text-center">
                <Link
                  to="/emergency-offline-consultation"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  Access Emergency Consultation
                </Link>
              </div>
            )}
          </div>
        </section>

        {/* Important Disclaimers */}
        <section className="bg-yellow-50 py-12">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-yellow-100 border-l-4 border-yellow-400 p-6 rounded-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-yellow-800 mb-2">Important Disclaimers</h3>
                  <div className="text-yellow-700 space-y-2">
                    <p>• VoiceHealth AI is not a substitute for emergency medical services</p>
                    <p>• Always call local emergency numbers for life-threatening situations</p>
                    <p>• Emergency numbers may vary by region - verify local numbers</p>
                    <p>• Keep this information updated and easily accessible</p>
                    <p>• Practice emergency procedures with family members</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default EmergencyInfo;
