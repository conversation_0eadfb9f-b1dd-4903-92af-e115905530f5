import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LogoutButton from './LogoutButton';

const FloatingLogoutButton = () => {
  const { user, userProfile } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Show button after user has been logged in for a few seconds
  useEffect(() => {
    if (user) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [user]);

  // Hide on authentication pages - using window.location to avoid Router dependency
  const shouldHide = (() => {
    try {
      const currentPath = window.location.pathname;
      return currentPath.includes('authentication') || currentPath === '/';
    } catch {
      return false;
    }
  })();

  if (!user || !isVisible || shouldHide) {
    return null;
  }

  const getRoleColor = (role) => {
    switch (role) {
      case 'patient':
        return 'bg-blue-500 hover:bg-blue-600';
      case 'provider':
        return 'bg-green-500 hover:bg-green-600';
      case 'admin':
        return 'bg-purple-500 hover:bg-purple-600';
      default:
        return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'patient':
        return 'Patient Account';
      case 'provider':
        return 'Provider Account';
      case 'admin':
        return 'Admin Account';
      default:
        return 'User Account';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg whitespace-nowrap">
          <div className="font-medium">{getRoleLabel(userProfile?.role)}</div>
          <div className="text-xs opacity-75">Click to sign out</div>
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}

      {/* Floating Button */}
      <div
        className="relative"
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <LogoutButton
          variant="primary"
          size="sm"
          showIcon={true}
          showText={false}
          className={`rounded-full shadow-lg ${getRoleColor(userProfile?.role)} text-white border-none hover:shadow-xl transition-all duration-300 transform hover:scale-105`}
          onLogoutSuccess={() => {
            console.log('Logout successful from floating button');
          }}
          onLogoutError={(error) => {
            console.error('Logout error from floating button:', error);
          }}
        />
        
        {/* Role indicator badge */}
        {user.email?.includes('<EMAIL>') && (
          <div className="absolute -top-1 -left-1 w-3 h-3 bg-yellow-400 rounded-full border-2 border-white animate-pulse"></div>
        )}
      </div>
    </div>
  );
};

export default FloatingLogoutButton;
