/**
 * CULTURALLY AWARE EMERGENCY SERVICE
 * 
 * Enhances the existing emergency stop service with cultural adaptations,
 * multi-language support, and regional emergency protocols.
 * 
 * FEATURES:
 * - Sub-2-second emergency response with cultural adaptations
 * - Multi-language emergency instructions
 * - Cultural family notification protocols
 * - Regional emergency service integration
 * - Traditional healer collaboration in emergencies
 * - GPS-based emergency service routing
 * - Cultural sensitivity in emergency communication
 */

import { emergencyStopService } from './emergencyStopService';
import { culturalAdaptationService } from './CulturalAdaptationService';
import { enhancedClinicalDecisionSupportService } from './ClinicalDecisionSupportService';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface CulturalEmergencyProtocol {
  culturalContext: CulturalProfile;
  languagePreference: string;
  familyNotificationRequired: boolean;
  religiousConsiderations: string[];
  traditionalHealerContact?: ContactInfo;
  emergencyContactHierarchy: EmergencyContact[];
  gpsLocation?: GeographicLocation;
  culturalTaboos?: string[];
}

export interface CulturalProfile {
  cultureCode: string;
  languagePreference: string;
  communicationStyle: any;
  familyInvolvementLevel: 'minimal' | 'moderate' | 'high';
  religiousConsiderations: any;
  traditionalMedicineOpenness: number;
  genderSensitivityLevel: number;
  authorityRespectLevel: number;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phoneNumber: string;
  priority: number;
  culturalRole: string; // e.g., 'elder', 'family_head', 'spiritual_leader'
  notificationMethod: 'call' | 'sms' | 'both';
  languagePreference: string;
}

export interface ContactInfo {
  name: string;
  phoneNumber: string;
  location: string;
  specialties: string[];
  availability: string;
}

export interface GeographicLocation {
  latitude: number;
  longitude: number;
  country: string;
  region: string;
  city: string;
  address?: string;
}

export interface CulturalEmergencyResponse {
  success: boolean;
  responseTime: number;
  culturalAdaptations: string[];
  emergencyInstructions: MultiLanguageInstructions;
  familyNotificationStatus: FamilyNotificationStatus;
  emergencyServiceContacts: EmergencyServiceContact[];
  traditionalHealerNotified: boolean;
  culturalConsiderations: string[];
  error?: string;
}

export interface MultiLanguageInstructions {
  primary: LanguageInstruction;
  secondary?: LanguageInstruction;
  emergency?: LanguageInstruction; // Simplified emergency language
}

export interface LanguageInstruction {
  language: string;
  instructions: string[];
  culturalNotes: string[];
  pronunciationGuide?: string[];
}

export interface FamilyNotificationStatus {
  notificationsSent: number;
  successfulNotifications: number;
  failedNotifications: number;
  notificationMethods: string[];
  culturalProtocolFollowed: boolean;
}

export interface EmergencyServiceContact {
  serviceName: string;
  phoneNumber: string;
  estimatedResponseTime: number;
  gpsCapable: boolean;
  languageSupport: string[];
  culturalTraining: boolean;
}

// =====================================================
// CULTURALLY AWARE EMERGENCY SERVICE
// =====================================================

export class CulturallyAwareEmergencyService {
  private supabase: SupabaseClient;
  private emergencyProtocolCache: Map<string, any> = new Map();
  private regionalEmergencyServices: Map<string, EmergencyServiceContact[]> = new Map();

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for culturally aware emergency service');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.initializeRegionalEmergencyServices();
    console.log('✅ CulturallyAwareEmergencyService initialized');
  }

  /**
   * Trigger culturally adapted emergency response with <2 second requirement
   */
  async triggerCulturallyAdaptedEmergency(
    reason: string,
    culturalProtocol: CulturalEmergencyProtocol,
    sessionId: string,
    userId: string = 'system'
  ): Promise<CulturalEmergencyResponse> {
    const startTime = performance.now();

    try {
      console.log(`🚨 CULTURALLY ADAPTED EMERGENCY TRIGGERED: ${reason}`);

      // PHASE 1: Immediate emergency stop (must be <500ms)
      const emergencyStopResult = await emergencyStopService.triggerEmergencyStop(
        reason,
        sessionId,
        userId
      );

      // PHASE 2: Cultural adaptations (parallel processing to maintain speed)
      const [
        culturalInstructions,
        familyNotificationResult,
        emergencyServices,
        traditionalHealerResult
      ] = await Promise.all([
        this.generateCulturalEmergencyInstructions(reason, culturalProtocol),
        this.notifyFamilyWithCulturalProtocol(culturalProtocol, reason),
        this.getRegionalEmergencyServices(culturalProtocol.gpsLocation),
        this.notifyTraditionalHealerIfAppropriate(culturalProtocol, reason)
      ]);

      const responseTime = performance.now() - startTime;

      // Ensure response time is under 2 seconds
      if (responseTime > 2000) {
        console.warn(`⚠️ Cultural emergency response exceeded 2s: ${responseTime.toFixed(2)}ms`);
      }

      const result: CulturalEmergencyResponse = {
        success: emergencyStopResult.success,
        responseTime,
        culturalAdaptations: this.getCulturalAdaptations(culturalProtocol),
        emergencyInstructions: culturalInstructions,
        familyNotificationStatus: familyNotificationResult,
        emergencyServiceContacts: emergencyServices,
        traditionalHealerNotified: traditionalHealerResult,
        culturalConsiderations: this.getCulturalConsiderations(culturalProtocol, reason),
        error: emergencyStopResult.error
      };

      // Log emergency event with cultural context (async, non-blocking)
      this.logCulturalEmergencyEventAsync(reason, culturalProtocol, result);

      console.log(`✅ Cultural emergency response completed in ${responseTime.toFixed(2)}ms`);
      return result;

    } catch (error) {
      const responseTime = performance.now() - startTime;
      console.error('❌ Cultural emergency service error:', error);

      // Fallback to basic emergency response
      const fallbackResult = await emergencyStopService.triggerEmergencyStop(
        reason,
        sessionId,
        userId
      );

      return {
        success: fallbackResult.success,
        responseTime,
        culturalAdaptations: ['fallback_mode'],
        emergencyInstructions: this.getFallbackInstructions(culturalProtocol.languagePreference),
        familyNotificationStatus: {
          notificationsSent: 0,
          successfulNotifications: 0,
          failedNotifications: 0,
          notificationMethods: [],
          culturalProtocolFollowed: false
        },
        emergencyServiceContacts: this.getFallbackEmergencyServices(culturalProtocol.gpsLocation),
        traditionalHealerNotified: false,
        culturalConsiderations: ['emergency_fallback_mode'],
        error: error.message
      };
    }
  }

  /**
   * Generate culturally adapted emergency instructions
   */
  private async generateCulturalEmergencyInstructions(
    reason: string,
    culturalProtocol: CulturalEmergencyProtocol
  ): Promise<MultiLanguageInstructions> {
    try {
      const cacheKey = `instructions_${reason}_${culturalProtocol.languagePreference}`;
      
      if (this.emergencyProtocolCache.has(cacheKey)) {
        return this.emergencyProtocolCache.get(cacheKey);
      }

      // Get emergency protocols from database
      const { data: protocols, error } = await this.supabase
        .rpc('get_culturally_adapted_emergency_protocols', {
          p_condition: reason,
          p_region: culturalProtocol.gpsLocation?.country || 'GH',
          p_culture_code: culturalProtocol.languagePreference,
          p_family_involvement_level: culturalProtocol.familyInvolvementLevel
        });

      if (error) {
        console.error('❌ Error getting cultural emergency protocols:', error);
        return this.getFallbackInstructions(culturalProtocol.languagePreference);
      }

      const instructions: MultiLanguageInstructions = {
        primary: {
          language: culturalProtocol.languagePreference,
          instructions: (protocols || []).map((p: any) => p.protocol_step),
          culturalNotes: (protocols || []).map((p: any) => p.cultural_adaptation),
          pronunciationGuide: this.getPronunciationGuide(culturalProtocol.languagePreference)
        }
      };

      // Add secondary language if needed
      if (culturalProtocol.languagePreference !== 'en') {
        instructions.secondary = {
          language: 'en',
          instructions: (protocols || []).map((p: any) => p.protocol_step),
          culturalNotes: (protocols || []).map((p: any) => p.family_involvement_note)
        };
      }

      // Cache for 5 minutes
      this.emergencyProtocolCache.set(cacheKey, instructions);
      setTimeout(() => this.emergencyProtocolCache.delete(cacheKey), 5 * 60 * 1000);

      return instructions;
    } catch (error) {
      console.error('❌ Error generating cultural emergency instructions:', error);
      return this.getFallbackInstructions(culturalProtocol.languagePreference);
    }
  }

  /**
   * Notify family with cultural protocol
   */
  private async notifyFamilyWithCulturalProtocol(
    culturalProtocol: CulturalEmergencyProtocol,
    reason: string
  ): Promise<FamilyNotificationStatus> {
    if (!culturalProtocol.familyNotificationRequired || 
        culturalProtocol.emergencyContactHierarchy.length === 0) {
      return {
        notificationsSent: 0,
        successfulNotifications: 0,
        failedNotifications: 0,
        notificationMethods: [],
        culturalProtocolFollowed: true
      };
    }

    try {
      // Sort contacts by cultural priority and relationship
      const sortedContacts = culturalProtocol.emergencyContactHierarchy
        .sort((a, b) => {
          // Prioritize by cultural role first, then by priority number
          const culturalPriority = this.getCulturalRolePriority(a.culturalRole) - 
                                   this.getCulturalRolePriority(b.culturalRole);
          return culturalPriority !== 0 ? culturalPriority : a.priority - b.priority;
        });

      let notificationsSent = 0;
      let successfulNotifications = 0;
      let failedNotifications = 0;
      const notificationMethods: string[] = [];

      // Notify contacts according to cultural hierarchy
      for (const contact of sortedContacts.slice(0, 3)) { // Limit to top 3 for emergency speed
        try {
          notificationsSent++;
          
          // Adapt message to contact's language and cultural context
          const adaptedMessage = await this.adaptEmergencyMessageForContact(
            reason,
            contact,
            culturalProtocol
          );

          // Send notification (simulated - in production would use SMS/call services)
          const notificationResult = await this.sendEmergencyNotification(
            contact,
            adaptedMessage
          );

          if (notificationResult.success) {
            successfulNotifications++;
            notificationMethods.push(contact.notificationMethod);
          } else {
            failedNotifications++;
          }
        } catch (error) {
          console.error(`❌ Failed to notify ${contact.name}:`, error);
          failedNotifications++;
        }
      }

      return {
        notificationsSent,
        successfulNotifications,
        failedNotifications,
        notificationMethods,
        culturalProtocolFollowed: true
      };
    } catch (error) {
      console.error('❌ Error in family notification:', error);
      return {
        notificationsSent: 0,
        successfulNotifications: 0,
        failedNotifications: 0,
        notificationMethods: [],
        culturalProtocolFollowed: false
      };
    }
  }

  /**
   * Get regional emergency services
   */
  private async getRegionalEmergencyServices(
    location?: GeographicLocation
  ): Promise<EmergencyServiceContact[]> {
    if (!location) {
      return this.getFallbackEmergencyServices();
    }

    const regionKey = location.country;
    
    if (this.regionalEmergencyServices.has(regionKey)) {
      return this.regionalEmergencyServices.get(regionKey) || [];
    }

    // In production, this would query a real emergency services database
    return this.getFallbackEmergencyServices(location);
  }

  /**
   * Notify traditional healer if appropriate
   */
  private async notifyTraditionalHealerIfAppropriate(
    culturalProtocol: CulturalEmergencyProtocol,
    reason: string
  ): Promise<boolean> {
    try {
      // Only notify if patient is open to traditional medicine and has a contact
      if (culturalProtocol.traditionalMedicineOpenness < 3 || 
          !culturalProtocol.traditionalHealerContact) {
        return false;
      }

      // Check if the emergency type is appropriate for traditional healer notification
      const appropriateConditions = ['mental_health_crisis', 'spiritual_distress', 'chronic_condition_emergency'];
      const isAppropriate = appropriateConditions.some(condition => 
        reason.toLowerCase().includes(condition.toLowerCase())
      );

      if (!isAppropriate) {
        return false;
      }

      // Send notification to traditional healer (simulated)
      console.log(`📞 Notifying traditional healer: ${culturalProtocol.traditionalHealerContact.name}`);
      
      // In production, this would send actual notification
      return true;
    } catch (error) {
      console.error('❌ Error notifying traditional healer:', error);
      return false;
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private getCulturalAdaptations(culturalProtocol: CulturalEmergencyProtocol): string[] {
    const adaptations: string[] = [];

    if (culturalProtocol.languagePreference !== 'en') {
      adaptations.push(`Instructions provided in ${culturalProtocol.languagePreference}`);
    }

    if (culturalProtocol.familyNotificationRequired) {
      adaptations.push('Family notification protocol activated');
    }

    if (culturalProtocol.religiousConsiderations.length > 0) {
      adaptations.push('Religious considerations included');
    }

    if (culturalProtocol.traditionalHealerContact) {
      adaptations.push('Traditional healer contact available');
    }

    return adaptations;
  }

  private getCulturalConsiderations(
    culturalProtocol: CulturalEmergencyProtocol,
    reason: string
  ): string[] {
    const considerations: string[] = [];

    if (culturalProtocol.culturalContext.genderSensitivityLevel >= 4) {
      considerations.push('Request same-gender healthcare provider if possible');
    }

    if (culturalProtocol.culturalContext.familyInvolvementLevel === 'high') {
      considerations.push('Include family members in medical decisions');
    }

    if (culturalProtocol.religiousConsiderations.length > 0) {
      considerations.push('Respect religious practices and beliefs');
    }

    if (culturalProtocol.culturalTaboos && culturalProtocol.culturalTaboos.length > 0) {
      considerations.push('Be aware of cultural taboos and sensitivities');
    }

    return considerations;
  }

  private getCulturalRolePriority(culturalRole: string): number {
    const rolePriorities: { [key: string]: number } = {
      'elder': 1,
      'family_head': 2,
      'spouse': 3,
      'parent': 4,
      'spiritual_leader': 5,
      'sibling': 6,
      'child': 7,
      'friend': 8,
      'other': 9
    };
    return rolePriorities[culturalRole] || 9;
  }

  private async adaptEmergencyMessageForContact(
    reason: string,
    contact: EmergencyContact,
    culturalProtocol: CulturalEmergencyProtocol
  ): Promise<string> {
    const baseMessage = `Emergency situation: ${reason}. Please contact emergency services if needed.`;
    
    try {
      const adaptedMessage = await culturalAdaptationService.adaptCommunicationStyle(
        baseMessage,
        culturalProtocol.culturalContext
      );
      return adaptedMessage.adaptedMessage;
    } catch (error) {
      console.error('❌ Error adapting emergency message:', error);
      return baseMessage;
    }
  }

  private async sendEmergencyNotification(
    contact: EmergencyContact,
    message: string
  ): Promise<{ success: boolean; method: string }> {
    // Simulated notification - in production would use real SMS/call services
    console.log(`📱 Emergency notification sent to ${contact.name}: ${message}`);
    return { success: true, method: contact.notificationMethod };
  }

  private getFallbackInstructions(languagePreference: string): MultiLanguageInstructions {
    const basicInstructions = [
      'Call emergency services immediately',
      'Stay calm and follow their instructions',
      'Provide your location clearly',
      'Do not hang up until told to do so'
    ];

    return {
      primary: {
        language: languagePreference,
        instructions: basicInstructions,
        culturalNotes: ['Emergency fallback mode - basic instructions only']
      }
    };
  }

  private getFallbackEmergencyServices(location?: GeographicLocation): EmergencyServiceContact[] {
    const defaultServices: EmergencyServiceContact[] = [
      {
        serviceName: 'Emergency Services',
        phoneNumber: '911',
        estimatedResponseTime: 10,
        gpsCapable: true,
        languageSupport: ['en'],
        culturalTraining: false
      }
    ];

    if (location) {
      // Customize based on location
      switch (location.country) {
        case 'GH':
          defaultServices[0].phoneNumber = '999';
          defaultServices[0].languageSupport = ['en', 'tw'];
          break;
        case 'KE':
          defaultServices[0].phoneNumber = '999';
          defaultServices[0].languageSupport = ['en', 'sw'];
          break;
        case 'NG':
          defaultServices[0].phoneNumber = '199';
          defaultServices[0].languageSupport = ['en', 'yo', 'ha'];
          break;
        case 'ZA':
          defaultServices[0].phoneNumber = '10177';
          defaultServices[0].languageSupport = ['en', 'af', 'zu'];
          break;
      }
    }

    return defaultServices;
  }

  private getPronunciationGuide(language: string): string[] {
    const guides: { [key: string]: string[] } = {
      'tw': ['Emergency = E-mer-gen-cy', 'Help = Mmoa'],
      'sw': ['Emergency = Dharura', 'Help = Msaada'],
      'yo': ['Emergency = Pajawiri', 'Help = Iranlowo'],
      'zu': ['Emergency = Isimo esiphuthumayo', 'Help = Usizo'],
      'ha': ['Emergency = Gaggawa', 'Help = Taimako']
    };
    return guides[language] || [];
  }

  private initializeRegionalEmergencyServices(): void {
    // Initialize with basic regional emergency services
    // In production, this would be loaded from a comprehensive database
    
    this.regionalEmergencyServices.set('GH', [
      {
        serviceName: 'Ghana National Ambulance Service',
        phoneNumber: '999',
        estimatedResponseTime: 15,
        gpsCapable: true,
        languageSupport: ['en', 'tw'],
        culturalTraining: true
      }
    ]);

    this.regionalEmergencyServices.set('KE', [
      {
        serviceName: 'Kenya Emergency Services',
        phoneNumber: '999',
        estimatedResponseTime: 12,
        gpsCapable: true,
        languageSupport: ['en', 'sw'],
        culturalTraining: true
      }
    ]);

    this.regionalEmergencyServices.set('NG', [
      {
        serviceName: 'Nigeria Emergency Services',
        phoneNumber: '199',
        estimatedResponseTime: 20,
        gpsCapable: false,
        languageSupport: ['en', 'yo', 'ha'],
        culturalTraining: false
      }
    ]);

    this.regionalEmergencyServices.set('ZA', [
      {
        serviceName: 'South Africa Emergency Services',
        phoneNumber: '10177',
        estimatedResponseTime: 8,
        gpsCapable: true,
        languageSupport: ['en', 'af', 'zu'],
        culturalTraining: true
      }
    ]);
  }

  private async logCulturalEmergencyEventAsync(
    reason: string,
    culturalProtocol: CulturalEmergencyProtocol,
    result: CulturalEmergencyResponse
  ): Promise<void> {
    try {
      // Log emergency event with cultural context for audit and improvement
      setTimeout(async () => {
        try {
          console.log(`📝 Logging cultural emergency event: ${reason}`);
          // In production, this would log to secure audit system
        } catch (error) {
          console.error('❌ Failed to log cultural emergency event:', error);
        }
      }, 0);
    } catch (error) {
      console.error('❌ Failed to schedule cultural emergency logging:', error);
    }
  }
}

// Export singleton instance
export const culturallyAwareEmergencyService = new CulturallyAwareEmergencyService();
