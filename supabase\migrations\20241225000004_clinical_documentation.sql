-- Clinical Documentation Database Schema
-- VoiceHealth AI - Phase 3 Implementation
-- Created: 2025-01-06

-- =====================================================
-- CLINICAL DOCUMENTATION TABLES
-- =====================================================

-- Clinical note templates
CREATE TABLE IF NOT EXISTS clinical_note_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_type VARCHAR(50) NOT NULL CHECK (note_type IN ('soap', 'progress', 'consultation', 'emergency', 'discharge', 'referral')),
    template_name VARCHAR(200) NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    cultural_context VARCHAR(50) DEFAULT 'general',
    template_structure JSONB NOT NULL,
    sections TEXT[] NOT NULL,
    cultural_sections TEXT[],
    required_fields TEXT[] NOT NULL,
    version VARCHAR(10) DEFAULT '1.0',
    active BOOLEAN DEFAULT TRUE,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(note_type, language, cultural_context)
);

-- Main clinical notes table
CREATE TABLE IF NOT EXISTS clinical_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    provider_id UUID NOT NULL,
    session_id UUID NOT NULL,
    note_type VARCHAR(50) NOT NULL,
    template VARCHAR(200) NOT NULL,
    content JSONB NOT NULL,
    metadata JSONB NOT NULL,
    cultural_adaptations JSONB DEFAULT '[]'::jsonb,
    audit_trail JSONB NOT NULL DEFAULT '[]'::jsonb,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'pending_review', 'approved', 'amended', 'archived')),
    quality_score INTEGER CHECK (quality_score >= 0 AND quality_score <= 100),
    completeness_score INTEGER CHECK (completeness_score >= 0 AND completeness_score <= 100),
    compliance_flags TEXT[],
    encryption_status VARCHAR(20) DEFAULT 'encrypted' CHECK (encryption_status IN ('encrypted', 'pending', 'failed')),
    backup_status VARCHAR(20) DEFAULT 'pending' CHECK (backup_status IN ('backed_up', 'pending', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID,
    
    -- Ensure one note per session per type
    UNIQUE(session_id, note_type)
);

-- Clinical note revisions for audit trail
CREATE TABLE IF NOT EXISTS clinical_note_revisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES clinical_notes(id) ON DELETE CASCADE,
    revision_number INTEGER NOT NULL,
    content JSONB NOT NULL,
    metadata JSONB NOT NULL,
    changed_by UUID NOT NULL,
    change_reason TEXT,
    change_summary TEXT,
    data_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for tamper detection
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(note_id, revision_number)
);

-- Voice transcription to note conversion tracking
CREATE TABLE IF NOT EXISTS voice_to_note_conversions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL,
    patient_id UUID NOT NULL,
    provider_id UUID NOT NULL,
    audio_transcription TEXT NOT NULL,
    structured_note JSONB NOT NULL,
    extracted_entities JSONB DEFAULT '[]'::jsonb,
    suggested_codes JSONB DEFAULT '[]'::jsonb,
    cultural_adaptations JSONB DEFAULT '[]'::jsonb,
    quality_assessment JSONB NOT NULL,
    confidence_score DECIMAL(5,4) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    processing_time INTEGER, -- milliseconds
    ai_model_version VARCHAR(20) DEFAULT '3.0',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ICD-10 and CPT code suggestions
CREATE TABLE IF NOT EXISTS clinical_code_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES clinical_notes(id) ON DELETE CASCADE,
    code_system VARCHAR(20) NOT NULL CHECK (code_system IN ('ICD-10', 'CPT', 'SNOMED', 'LOINC')),
    code VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    confidence_score DECIMAL(5,4) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    cultural_relevance TEXT,
    suggested_by VARCHAR(50) DEFAULT 'ai_system',
    accepted BOOLEAN DEFAULT FALSE,
    accepted_by UUID,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Clinical documentation quality metrics
CREATE TABLE IF NOT EXISTS clinical_note_quality_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    note_id UUID REFERENCES clinical_notes(id) ON DELETE CASCADE,
    completeness_score INTEGER CHECK (completeness_score >= 0 AND completeness_score <= 100),
    accuracy_score INTEGER CHECK (accuracy_score >= 0 AND accuracy_score <= 100),
    clarity_score INTEGER CHECK (clarity_score >= 0 AND clarity_score <= 100),
    cultural_sensitivity_score INTEGER CHECK (cultural_sensitivity_score >= 0 AND cultural_sensitivity_score <= 100),
    compliance_score INTEGER CHECK (compliance_score >= 0 AND compliance_score <= 100),
    improvement_suggestions TEXT[],
    assessed_by VARCHAR(50) DEFAULT 'ai_system',
    assessment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    manual_review_required BOOLEAN DEFAULT FALSE,
    reviewed_by UUID,
    review_date TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- RISK STRATIFICATION TABLES
-- =====================================================

-- Regional risk models
CREATE TABLE IF NOT EXISTS regional_risk_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    country VARCHAR(10) NOT NULL,
    region VARCHAR(100) NOT NULL,
    condition VARCHAR(200) NOT NULL,
    base_risk_score INTEGER CHECK (base_risk_score >= 0 AND base_risk_score <= 100),
    seasonal_multipliers JSONB DEFAULT '{}'::jsonb,
    age_multipliers JSONB DEFAULT '{}'::jsonb,
    gender_multipliers JSONB DEFAULT '{}'::jsonb,
    socioeconomic_multipliers JSONB DEFAULT '{}'::jsonb,
    environmental_factors JSONB DEFAULT '{}'::jsonb,
    cultural_factors JSONB DEFAULT '{}'::jsonb,
    evidence_level VARCHAR(1) CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    data_source TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(country, region, condition)
);

-- Risk assessment results
CREATE TABLE IF NOT EXISTS risk_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    session_id UUID,
    assessment_type VARCHAR(20) NOT NULL CHECK (assessment_type IN ('comprehensive', 'condition_specific', 'emergency', 'screening')),
    overall_risk_score INTEGER CHECK (overall_risk_score >= 0 AND overall_risk_score <= 100),
    risk_category VARCHAR(20) NOT NULL CHECK (risk_category IN ('low', 'moderate', 'high', 'critical')),
    condition_specific_risks JSONB DEFAULT '[]'::jsonb,
    regional_risk_factors JSONB DEFAULT '[]'::jsonb,
    modifiable_risk_factors JSONB DEFAULT '[]'::jsonb,
    non_modifiable_risk_factors JSONB DEFAULT '[]'::jsonb,
    predictive_analytics JSONB DEFAULT '{}'::jsonb,
    recommendations JSONB DEFAULT '[]'::jsonb,
    urgent_actions JSONB DEFAULT '[]'::jsonb,
    follow_up_schedule JSONB DEFAULT '{}'::jsonb,
    cultural_considerations TEXT[],
    assessment_metadata JSONB NOT NULL,
    assessed_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Emergency risk assessments (for quick access)
CREATE TABLE IF NOT EXISTS emergency_risk_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    session_id UUID,
    symptoms TEXT[] NOT NULL,
    vital_signs JSONB NOT NULL,
    medical_history TEXT[],
    emergency_risk_level VARCHAR(20) NOT NULL CHECK (emergency_risk_level IN ('low', 'moderate', 'high', 'critical')),
    urgent_actions JSONB NOT NULL,
    time_to_action INTEGER NOT NULL, -- minutes
    cultural_considerations TEXT[],
    processing_time INTEGER, -- milliseconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Clinical notes indexes
CREATE INDEX IF NOT EXISTS idx_clinical_notes_patient ON clinical_notes(patient_id);
CREATE INDEX IF NOT EXISTS idx_clinical_notes_provider ON clinical_notes(provider_id);
CREATE INDEX IF NOT EXISTS idx_clinical_notes_session ON clinical_notes(session_id);
CREATE INDEX IF NOT EXISTS idx_clinical_notes_type ON clinical_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_clinical_notes_status ON clinical_notes(status);
CREATE INDEX IF NOT EXISTS idx_clinical_notes_created ON clinical_notes(created_at);

-- Templates indexes
CREATE INDEX IF NOT EXISTS idx_templates_type_lang ON clinical_note_templates(note_type, language);
CREATE INDEX IF NOT EXISTS idx_templates_cultural ON clinical_note_templates(cultural_context);

-- Voice conversion indexes
CREATE INDEX IF NOT EXISTS idx_voice_conversion_session ON voice_to_note_conversions(session_id);
CREATE INDEX IF NOT EXISTS idx_voice_conversion_patient ON voice_to_note_conversions(patient_id);
CREATE INDEX IF NOT EXISTS idx_voice_conversion_created ON voice_to_note_conversions(created_at);

-- Risk assessment indexes
CREATE INDEX IF NOT EXISTS idx_risk_assessments_patient ON risk_assessments(patient_id);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_type ON risk_assessments(assessment_type);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_category ON risk_assessments(risk_category);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_created ON risk_assessments(created_at);

-- Emergency risk indexes
CREATE INDEX IF NOT EXISTS idx_emergency_risk_patient ON emergency_risk_assessments(patient_id);
CREATE INDEX IF NOT EXISTS idx_emergency_risk_level ON emergency_risk_assessments(emergency_risk_level);
CREATE INDEX IF NOT EXISTS idx_emergency_risk_created ON emergency_risk_assessments(created_at);

-- Regional models indexes
CREATE INDEX IF NOT EXISTS idx_regional_models_country ON regional_risk_models(country, region);
CREATE INDEX IF NOT EXISTS idx_regional_models_condition ON regional_risk_models(condition);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE clinical_note_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinical_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinical_note_revisions ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_to_note_conversions ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinical_code_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinical_note_quality_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_risk_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE emergency_risk_assessments ENABLE ROW LEVEL SECURITY;

-- Public read policies for templates and models
CREATE POLICY "public_read_templates" ON clinical_note_templates 
    FOR SELECT USING (active = true);

CREATE POLICY "public_read_risk_models" ON regional_risk_models 
    FOR SELECT USING (true);

-- Provider policies for clinical notes
CREATE POLICY "provider_clinical_notes" ON clinical_notes 
    FOR ALL USING (
        auth.uid()::text = provider_id OR 
        auth.jwt() ->> 'role' IN ('admin', 'supervisor')
    );

CREATE POLICY "provider_note_revisions" ON clinical_note_revisions 
    FOR ALL USING (
        note_id IN (
            SELECT id FROM clinical_notes 
            WHERE provider_id = auth.uid()::text OR auth.jwt() ->> 'role' IN ('admin', 'supervisor')
        )
    );

-- Patient access policies (read-only for their own records)
CREATE POLICY "patient_read_notes" ON clinical_notes 
    FOR SELECT USING (
        auth.uid()::text = patient_id AND status = 'approved'
    );

-- Risk assessment policies
CREATE POLICY "provider_risk_assessments" ON risk_assessments 
    FOR ALL USING (
        auth.uid()::text = assessed_by OR 
        auth.jwt() ->> 'role' IN ('admin', 'supervisor')
    );

CREATE POLICY "patient_read_risk_assessments" ON risk_assessments 
    FOR SELECT USING (auth.uid()::text = patient_id);

-- Emergency risk assessment policies (broader access for emergency situations)
CREATE POLICY "emergency_risk_access" ON emergency_risk_assessments 
    FOR ALL USING (
        auth.jwt() ->> 'role' IN ('provider', 'emergency', 'admin') OR
        auth.uid()::text = patient_id
    );

-- Admin policies for quality metrics and conversions
CREATE POLICY "admin_quality_metrics" ON clinical_note_quality_metrics 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'supervisor'));

CREATE POLICY "admin_voice_conversions" ON voice_to_note_conversions 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'supervisor'));

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

CREATE TRIGGER update_clinical_notes_updated_at 
    BEFORE UPDATE ON clinical_notes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at 
    BEFORE UPDATE ON clinical_note_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- FUNCTIONS FOR DATA INTEGRITY
-- =====================================================

-- Function to generate data hash for tamper detection
CREATE OR REPLACE FUNCTION generate_data_hash(data_content JSONB)
RETURNS VARCHAR(64) AS $$
BEGIN
    RETURN encode(digest(data_content::text, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Function to validate clinical note completeness
CREATE OR REPLACE FUNCTION validate_note_completeness(
    note_content JSONB,
    note_type VARCHAR(50)
)
RETURNS INTEGER AS $$
DECLARE
    required_sections TEXT[];
    section_count INTEGER := 0;
    total_sections INTEGER;
    completeness_score INTEGER;
BEGIN
    -- Define required sections based on note type
    CASE note_type
        WHEN 'soap' THEN
            required_sections := ARRAY['chiefComplaint', 'historyOfPresentIllness', 'physicalExamination', 'assessment', 'plan'];
        WHEN 'emergency' THEN
            required_sections := ARRAY['chiefComplaint', 'vitalSigns', 'assessment', 'plan', 'urgentActions'];
        ELSE
            required_sections := ARRAY['chiefComplaint', 'assessment', 'plan'];
    END CASE;
    
    total_sections := array_length(required_sections, 1);
    
    -- Count completed sections
    FOR i IN 1..total_sections LOOP
        IF note_content ? required_sections[i] AND 
           note_content ->> required_sections[i] IS NOT NULL AND 
           note_content ->> required_sections[i] != '' THEN
            section_count := section_count + 1;
        END IF;
    END LOOP;
    
    -- Calculate completeness percentage
    completeness_score := (section_count * 100) / total_sections;
    
    RETURN completeness_score;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

-- Procedure to update specialist metrics (referenced in specialist referral service)
CREATE OR REPLACE FUNCTION update_specialist_metrics(
    p_specialist_id UUID,
    p_referral_quality INTEGER,
    p_patient_satisfaction INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    -- Update specialist rating based on new feedback
    UPDATE specialists 
    SET 
        rating = (
            SELECT AVG(
                CASE 
                    WHEN p_patient_satisfaction IS NOT NULL THEN 
                        (p_referral_quality + p_patient_satisfaction) / 2.0
                    ELSE p_referral_quality 
                END
            )
            FROM (
                SELECT p_referral_quality as quality
                UNION ALL
                SELECT referral_quality FROM referral_outcomes 
                WHERE specialist_id = p_specialist_id 
                AND referral_quality IS NOT NULL
                LIMIT 10 -- Last 10 reviews
            ) recent_reviews
        ),
        review_count = review_count + 1,
        updated_at = NOW()
    WHERE id = p_specialist_id;
END;
$$ LANGUAGE plpgsql;

-- Procedure to translate medical terms (referenced in cultural adaptation service)
CREATE OR REPLACE FUNCTION translate_medical_terms(
    p_terms TEXT[],
    p_target_language VARCHAR(10)
)
RETURNS TABLE(
    english_term TEXT,
    translated_term TEXT,
    category TEXT,
    cultural_context TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mt.english_term,
        mt.translated_term,
        mt.category,
        mt.cultural_context
    FROM medical_terminology_translations mt
    WHERE mt.english_term = ANY(p_terms)
    AND mt.target_language = p_target_language;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE clinical_notes IS 'Main clinical documentation with HIPAA-compliant audit trails';
COMMENT ON TABLE clinical_note_templates IS 'Culturally adapted templates for clinical documentation';
COMMENT ON TABLE voice_to_note_conversions IS 'Voice transcription to structured note conversion tracking';
COMMENT ON TABLE risk_assessments IS 'Comprehensive risk stratification results with regional factors';
COMMENT ON TABLE emergency_risk_assessments IS 'Quick emergency risk assessments for urgent situations';
COMMENT ON TABLE regional_risk_models IS 'Regional disease risk models with cultural and environmental factors';

-- Migration completed successfully
SELECT 'Clinical Documentation and Risk Stratification schema migration completed successfully' as status;
