/**
 * Unified Context Assembly Service
 * 
 * Advanced context assembly with intelligent token prioritization, performance optimization,
 * and quality metrics for medical AI consultations.
 */

import type { EnhancedPatientContext } from './EnhancedPatientContextService';
import type { ConversationMessage } from '../types/memory';
import { SOAPAssessment } from './DiagnosticFrameworkService';
import { ClinicalDecision } from './ClinicalDecisionSupportService';

export interface UnifiedContextOptions {
  maxTokens?: number;
  urgencyLevel?: 'routine' | 'urgent' | 'emergent' | 'immediate';
  conversationPhase?: 'subjective' | 'objective' | 'assessment' | 'plan';
  priorityMode?: 'comprehensive' | 'focused' | 'emergency';
  includeRegionalContext?: boolean;
  includeCulturalContext?: boolean;
  optimizeForSpeed?: boolean;
  targetAudience?: 'patient' | 'provider' | 'specialist';
}

export interface ContextPriority {
  section: string;
  priority: number; // 1-10, 10 being highest
  tokenWeight: number; // Percentage of tokens to allocate
  required: boolean;
  urgencyMultiplier: number;
}

export interface OptimizedContext {
  systemPrompt: string;
  contextSections: ContextSection[];
  priorityFlags: PriorityFlag[];
  tokenUtilization: TokenUtilization;
  qualityMetrics: ContextQualityMetrics;
  optimizationApplied: OptimizationStrategy[];
  estimatedResponseQuality: number; // 0-100
}

export interface ContextSection {
  name: string;
  content: string;
  tokenCount: number;
  priority: number;
  included: boolean;
  truncated: boolean;
  qualityScore: number; // 0-100
}

export interface PriorityFlag {
  type: 'critical' | 'urgent' | 'important' | 'informational';
  message: string;
  source: string;
  actionRequired?: string;
}

export interface TokenUtilization {
  totalTokens: number;
  maxTokens: number;
  utilizationPercentage: number;
  sectionsIncluded: number;
  sectionsTruncated: number;
  sectionsExcluded: number;
  optimizationSavings: number;
}

export interface ContextQualityMetrics {
  completeness: number; // 0-100
  relevance: number; // 0-100
  freshness: number; // 0-100
  accuracy: number; // 0-100
  clinicalValue: number; // 0-100
  overallScore: number; // 0-100
}

export interface OptimizationStrategy {
  name: string;
  description: string;
  tokensSaved: number;
  qualityImpact: number; // -100 to 100
  applied: boolean;
}

class UnifiedContextAssemblyService {
  private readonly DEFAULT_MAX_TOKENS = 4000;
  private readonly EMERGENCY_MAX_TOKENS = 2000;
  private readonly QUALITY_THRESHOLD = 75;

  // Context priority definitions based on urgency and phase
  private readonly CONTEXT_PRIORITIES: Record<string, ContextPriority[]> = {
    immediate: [
      { section: 'emergency_alerts', priority: 10, tokenWeight: 25, required: true, urgencyMultiplier: 2.0 },
      { section: 'current_symptoms', priority: 9, tokenWeight: 20, required: true, urgencyMultiplier: 1.8 },
      { section: 'vital_signs', priority: 8, tokenWeight: 15, required: true, urgencyMultiplier: 1.5 },
      { section: 'allergies', priority: 8, tokenWeight: 10, required: true, urgencyMultiplier: 1.5 },
      { section: 'current_medications', priority: 7, tokenWeight: 10, required: true, urgencyMultiplier: 1.3 },
      { section: 'medical_history', priority: 6, tokenWeight: 10, required: false, urgencyMultiplier: 1.0 },
      { section: 'regional_context', priority: 3, tokenWeight: 5, required: false, urgencyMultiplier: 0.5 },
      { section: 'cultural_context', priority: 2, tokenWeight: 5, required: false, urgencyMultiplier: 0.3 }
    ],
    urgent: [
      { section: 'current_symptoms', priority: 9, tokenWeight: 20, required: true, urgencyMultiplier: 1.5 },
      { section: 'emergency_alerts', priority: 8, tokenWeight: 15, required: true, urgencyMultiplier: 1.5 },
      { section: 'medical_history', priority: 7, tokenWeight: 15, required: true, urgencyMultiplier: 1.2 },
      { section: 'current_medications', priority: 7, tokenWeight: 12, required: true, urgencyMultiplier: 1.2 },
      { section: 'allergies', priority: 7, tokenWeight: 10, required: true, urgencyMultiplier: 1.2 },
      { section: 'vital_signs', priority: 6, tokenWeight: 10, required: false, urgencyMultiplier: 1.0 },
      { section: 'regional_context', priority: 4, tokenWeight: 8, required: false, urgencyMultiplier: 0.8 },
      { section: 'cultural_context', priority: 3, tokenWeight: 10, required: false, urgencyMultiplier: 0.6 }
    ],
    routine: [
      { section: 'current_symptoms', priority: 8, tokenWeight: 18, required: true, urgencyMultiplier: 1.0 },
      { section: 'medical_history', priority: 7, tokenWeight: 18, required: true, urgencyMultiplier: 1.0 },
      { section: 'current_medications', priority: 6, tokenWeight: 12, required: true, urgencyMultiplier: 1.0 },
      { section: 'allergies', priority: 6, tokenWeight: 8, required: true, urgencyMultiplier: 1.0 },
      { section: 'regional_context', priority: 5, tokenWeight: 12, required: false, urgencyMultiplier: 1.0 },
      { section: 'cultural_context', priority: 5, tokenWeight: 12, required: false, urgencyMultiplier: 1.0 },
      { section: 'lifestyle_factors', priority: 4, tokenWeight: 10, required: false, urgencyMultiplier: 1.0 },
      { section: 'family_history', priority: 3, tokenWeight: 10, required: false, urgencyMultiplier: 1.0 }
    ]
  };

  /**
   * Assemble optimized context with intelligent prioritization
   */
  async assembleOptimizedContext(
    patientContext: EnhancedPatientContext,
    conversationHistory: ConversationMessage[],
    currentMessage: string,
    soapAssessment?: SOAPAssessment,
    clinicalDecision?: ClinicalDecision,
    options: UnifiedContextOptions = {}
  ): Promise<OptimizedContext> {
    const startTime = Date.now();
    
    // Set defaults based on urgency
    const urgencyLevel = options.urgencyLevel || 'routine';
    const maxTokens = this.determineMaxTokens(urgencyLevel, options.maxTokens);
    const priorityMode = options.priorityMode || this.determinePriorityMode(urgencyLevel);
    
    // Get context priorities for this urgency level
    const priorities = this.CONTEXT_PRIORITIES[urgencyLevel] || this.CONTEXT_PRIORITIES.routine;
    
    // Build all context sections
    const allSections = await this.buildAllContextSections(
      patientContext,
      conversationHistory,
      currentMessage,
      soapAssessment,
      clinicalDecision,
      options
    );
    
    // Apply intelligent prioritization and optimization
    const optimizedSections = this.optimizeContextSections(allSections, priorities, maxTokens);
    
    // Generate priority flags
    const priorityFlags = this.generatePriorityFlags(patientContext, clinicalDecision, urgencyLevel);
    
    // Calculate quality metrics
    const qualityMetrics = this.calculateQualityMetrics(optimizedSections, patientContext);
    
    // Generate final system prompt
    const systemPrompt = this.generateOptimizedSystemPrompt(
      optimizedSections,
      priorityFlags,
      urgencyLevel,
      options
    );
    
    // Calculate token utilization
    const tokenUtilization = this.calculateTokenUtilization(optimizedSections, maxTokens);
    
    // Determine optimization strategies applied
    const optimizationApplied = this.getAppliedOptimizations(optimizedSections, allSections);
    
    const processingTime = Date.now() - startTime;
    console.log(`🚀 Context assembly completed in ${processingTime}ms`);
    
    return {
      systemPrompt,
      contextSections: optimizedSections,
      priorityFlags,
      tokenUtilization,
      qualityMetrics,
      optimizationApplied,
      estimatedResponseQuality: this.estimateResponseQuality(qualityMetrics, tokenUtilization)
    };
  }

  /**
   * Build all context sections
   */
  private async buildAllContextSections(
    patientContext: EnhancedPatientContext,
    conversationHistory: ConversationMessage[],
    currentMessage: string,
    soapAssessment?: SOAPAssessment,
    clinicalDecision?: ClinicalDecision,
    options: UnifiedContextOptions = {}
  ): Promise<ContextSection[]> {
    const sections: ContextSection[] = [];
    
    // Current symptoms and chief complaint
    sections.push({
      name: 'current_symptoms',
      content: this.buildCurrentSymptomsSection(currentMessage, soapAssessment),
      tokenCount: 0,
      priority: 9,
      included: true,
      truncated: false,
      qualityScore: 95
    });
    
    // Emergency alerts and red flags
    if (clinicalDecision?.redFlags?.length > 0) {
      sections.push({
        name: 'emergency_alerts',
        content: this.buildEmergencyAlertsSection(clinicalDecision.redFlags),
        tokenCount: 0,
        priority: 10,
        included: true,
        truncated: false,
        qualityScore: 100
      });
    }
    
    // Medical history
    sections.push({
      name: 'medical_history',
      content: this.buildMedicalHistorySection(patientContext.medicalHistory),
      tokenCount: 0,
      priority: 7,
      included: true,
      truncated: false,
      qualityScore: 85
    });
    
    // Current medications
    sections.push({
      name: 'current_medications',
      content: this.buildMedicationsSection(patientContext.medicalHistory.medications),
      tokenCount: 0,
      priority: 7,
      included: true,
      truncated: false,
      qualityScore: 90
    });
    
    // Allergies
    sections.push({
      name: 'allergies',
      content: this.buildAllergiesSection(patientContext.medicalHistory.allergies),
      tokenCount: 0,
      priority: 8,
      included: true,
      truncated: false,
      qualityScore: 95
    });
    
    // Regional context
    if (options.includeRegionalContext !== false) {
      sections.push({
        name: 'regional_context',
        content: this.buildRegionalContextSection(patientContext.regionalContext),
        tokenCount: 0,
        priority: 5,
        included: true,
        truncated: false,
        qualityScore: 70
      });
    }
    
    // Cultural context
    if (options.includeCulturalContext !== false) {
      sections.push({
        name: 'cultural_context',
        content: this.buildCulturalContextSection(patientContext),
        tokenCount: 0,
        priority: 4,
        included: true,
        truncated: false,
        qualityScore: 65
      });
    }
    
    // Calculate token counts for each section
    sections.forEach(section => {
      section.tokenCount = this.estimateTokenCount(section.content);
    });
    
    return sections;
  }

  /**
   * Optimize context sections based on priorities and token limits
   */
  private optimizeContextSections(
    sections: ContextSection[],
    priorities: ContextPriority[],
    maxTokens: number
  ): ContextSection[] {
    // Sort sections by priority
    const sortedSections = [...sections].sort((a, b) => b.priority - a.priority);
    
    let totalTokens = 0;
    const optimizedSections: ContextSection[] = [];
    
    // Include sections based on priority and token budget
    for (const section of sortedSections) {
      const priority = priorities.find(p => p.section === section.name);
      const tokenBudget = priority ? Math.floor(maxTokens * (priority.tokenWeight / 100)) : 200;
      
      if (totalTokens + section.tokenCount <= maxTokens) {
        // Include full section
        optimizedSections.push({
          ...section,
          included: true,
          truncated: false
        });
        totalTokens += section.tokenCount;
      } else if (totalTokens + tokenBudget <= maxTokens && priority?.required) {
        // Truncate section to fit budget
        const truncatedContent = this.truncateContent(section.content, tokenBudget);
        optimizedSections.push({
          ...section,
          content: truncatedContent,
          tokenCount: tokenBudget,
          included: true,
          truncated: true,
          qualityScore: section.qualityScore * 0.8 // Reduce quality score for truncation
        });
        totalTokens += tokenBudget;
      } else {
        // Exclude section
        optimizedSections.push({
          ...section,
          included: false,
          truncated: false,
          qualityScore: 0
        });
      }
    }
    
    return optimizedSections;
  }

  // Helper methods for building specific sections
  private buildCurrentSymptomsSection(currentMessage: string, soapAssessment?: SOAPAssessment): string {
    let content = `**Current Symptoms:**\n${currentMessage}\n\n`;
    
    if (soapAssessment?.subjective?.chiefComplaint) {
      content += `**Chief Complaint:** ${soapAssessment.subjective.chiefComplaint}\n\n`;
    }
    
    return content;
  }

  private buildEmergencyAlertsSection(redFlags: any[]): string {
    if (!redFlags.length) return '';
    
    let content = '**🚨 EMERGENCY ALERTS:**\n';
    redFlags.forEach(flag => {
      content += `- ${flag.severity.toUpperCase()}: ${flag.description}\n`;
      content += `  Action: ${flag.immediateAction}\n`;
    });
    content += '\n';
    
    return content;
  }

  private buildMedicalHistorySection(medicalHistory: any): string {
    let content = '**Medical History:**\n';
    
    if (medicalHistory.conditions?.length > 0) {
      content += 'Conditions:\n';
      medicalHistory.conditions.forEach((condition: any) => {
        content += `- ${condition.conditionName} (${condition.status || 'active'})\n`;
      });
    }
    
    return content + '\n';
  }

  private buildMedicationsSection(medications: any[]): string {
    if (!medications?.length) return '**Current Medications:** None reported\n\n';
    
    let content = '**Current Medications:**\n';
    medications.forEach(med => {
      if (med.isCurrent) {
        content += `- ${med.medicationName} ${med.dosage || ''} ${med.frequency || ''}\n`;
      }
    });
    
    return content + '\n';
  }

  private buildAllergiesSection(allergies: string[]): string {
    if (!allergies?.length) return '**Allergies:** None reported\n\n';
    
    let content = '**Allergies:**\n';
    allergies.forEach(allergy => {
      content += `- ${allergy}\n`;
    });
    
    return content + '\n';
  }

  private buildRegionalContextSection(regionalContext: any): string {
    if (!regionalContext) return '';
    
    let content = '**Regional Health Context:**\n';
    content += `Location: ${regionalContext.countryName}, ${regionalContext.region}\n`;
    
    if (regionalContext.commonConditions?.length > 0) {
      content += `Common conditions: ${regionalContext.commonConditions.join(', ')}\n`;
    }
    
    return content + '\n';
  }

  private buildCulturalContextSection(patientContext: any): string {
    const profile = patientContext.patientProfile;
    if (!profile) return '';
    
    let content = '**Cultural Context:**\n';
    content += `Language: ${profile.preferredLanguage || 'English'}\n`;
    
    if (profile.culturalBackground) {
      content += `Background: ${profile.culturalBackground}\n`;
    }
    
    return content + '\n';
  }

  // Additional helper methods
  private determineMaxTokens(urgencyLevel: string, customMax?: number): number {
    if (customMax) return customMax;
    return urgencyLevel === 'immediate' ? this.EMERGENCY_MAX_TOKENS : this.DEFAULT_MAX_TOKENS;
  }

  private determinePriorityMode(urgencyLevel: string): 'comprehensive' | 'focused' | 'emergency' {
    switch (urgencyLevel) {
      case 'immediate': return 'emergency';
      case 'urgent': return 'focused';
      default: return 'comprehensive';
    }
  }

  private generatePriorityFlags(
    patientContext: any, 
    clinicalDecision?: any, 
    urgencyLevel?: string
  ): PriorityFlag[] {
    const flags: PriorityFlag[] = [];
    
    if (clinicalDecision?.redFlags?.length > 0) {
      flags.push({
        type: 'critical',
        message: `${clinicalDecision.redFlags.length} red flags detected`,
        source: 'clinical_decision_support',
        actionRequired: 'Immediate evaluation required'
      });
    }
    
    return flags;
  }

  private calculateQualityMetrics(sections: ContextSection[], patientContext: any): ContextQualityMetrics {
    const includedSections = sections.filter(s => s.included);
    const completeness = (includedSections.length / sections.length) * 100;
    const relevance = includedSections.reduce((sum, s) => sum + s.qualityScore, 0) / includedSections.length || 0;
    
    return {
      completeness,
      relevance,
      freshness: 85, // Based on data recency
      accuracy: 90,  // Based on data validation
      clinicalValue: relevance,
      overallScore: (completeness + relevance + 85 + 90 + relevance) / 5
    };
  }

  private generateOptimizedSystemPrompt(
    sections: ContextSection[],
    flags: PriorityFlag[],
    urgencyLevel: string,
    options: UnifiedContextOptions
  ): string {
    let prompt = 'You are an AI medical assistant with access to comprehensive patient context.\n\n';
    
    // Add urgency context
    if (urgencyLevel === 'immediate') {
      prompt += '🚨 EMERGENCY CONSULTATION - Prioritize immediate assessment and safety.\n\n';
    }
    
    // Add context sections
    sections.filter(s => s.included).forEach(section => {
      prompt += section.content;
    });
    
    // Add priority flags
    if (flags.length > 0) {
      prompt += '**Priority Alerts:**\n';
      flags.forEach(flag => {
        prompt += `- ${flag.type.toUpperCase()}: ${flag.message}\n`;
      });
      prompt += '\n';
    }
    
    return prompt;
  }

  private calculateTokenUtilization(sections: ContextSection[], maxTokens: number): TokenUtilization {
    const includedSections = sections.filter(s => s.included);
    const totalTokens = includedSections.reduce((sum, s) => sum + s.tokenCount, 0);
    
    return {
      totalTokens,
      maxTokens,
      utilizationPercentage: (totalTokens / maxTokens) * 100,
      sectionsIncluded: includedSections.length,
      sectionsTruncated: sections.filter(s => s.truncated).length,
      sectionsExcluded: sections.filter(s => !s.included).length,
      optimizationSavings: sections.reduce((sum, s) => sum + (s.truncated ? 100 : 0), 0)
    };
  }

  private getAppliedOptimizations(optimized: ContextSection[], original: ContextSection[]): OptimizationStrategy[] {
    const strategies: OptimizationStrategy[] = [];
    
    const truncatedCount = optimized.filter(s => s.truncated).length;
    if (truncatedCount > 0) {
      strategies.push({
        name: 'Content Truncation',
        description: `Truncated ${truncatedCount} sections to fit token budget`,
        tokensSaved: 500, // Estimated
        qualityImpact: -10,
        applied: true
      });
    }
    
    return strategies;
  }

  private estimateResponseQuality(quality: ContextQualityMetrics, utilization: TokenUtilization): number {
    return Math.round((quality.overallScore * 0.7) + (utilization.utilizationPercentage * 0.3));
  }

  private estimateTokenCount(content: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }

  private truncateContent(content: string, maxTokens: number): string {
    const maxChars = maxTokens * 4;
    if (content.length <= maxChars) return content;
    
    return content.substring(0, maxChars - 3) + '...';
  }
}

export const unifiedContextAssemblyService = new UnifiedContextAssemblyService();
export default unifiedContextAssemblyService;
