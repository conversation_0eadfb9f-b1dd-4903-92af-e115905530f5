@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Colors */
    --color-primary: #2563EB; /* blue-600 */
    --color-primary-50: #EFF6FF; /* blue-50 */
    --color-primary-100: #DBEAFE; /* blue-100 */
    --color-primary-500: #3B82F6; /* blue-500 */
    --color-primary-600: #2563EB; /* blue-600 */
    --color-primary-700: #1D4ED8; /* blue-700 */
    --color-primary-foreground: #FFFFFF; /* white */

    /* Secondary Colors */
    --color-secondary: #64748B; /* slate-500 */
    --color-secondary-50: #F8FAFC; /* slate-50 */
    --color-secondary-100: #F1F5F9; /* slate-100 */
    --color-secondary-200: #E2E8F0; /* slate-200 */
    --color-secondary-300: #CBD5E1; /* slate-300 */
    --color-secondary-400: #94A3B8; /* slate-400 */
    --color-secondary-500: #64748B; /* slate-500 */
    --color-secondary-600: #475569; /* slate-600 */
    --color-secondary-700: #334155; /* slate-700 */
    --color-secondary-foreground: #FFFFFF; /* white */

    /* Accent Colors */
    --color-accent: #F59E0B; /* amber-500 */
    --color-accent-50: #FFFBEB; /* amber-50 */
    --color-accent-100: #FEF3C7; /* amber-100 */
    --color-accent-500: #F59E0B; /* amber-500 */
    --color-accent-600: #D97706; /* amber-600 */
    --color-accent-foreground: #FFFFFF; /* white */

    /* Background Colors */
    --color-background: #FAFBFC; /* custom off-white */
    --color-surface: #FFFFFF; /* white */
    --color-card: #FFFFFF; /* white */
    --color-popover: #FFFFFF; /* white */

    /* Text Colors */
    --color-text-primary: #1E293B; /* slate-800 */
    --color-text-secondary: #64748B; /* slate-500 */
    --color-text-muted: #94A3B8; /* slate-400 */
    --color-text-foreground: #1E293B; /* slate-800 */

    /* Status Colors */
    --color-success: #10B981; /* emerald-500 */
    --color-success-50: #ECFDF5; /* emerald-50 */
    --color-success-100: #D1FAE5; /* emerald-100 */
    --color-success-500: #10B981; /* emerald-500 */
    --color-success-600: #059669; /* emerald-600 */
    --color-success-foreground: #FFFFFF; /* white */

    --color-warning: #F59E0B; /* amber-500 */
    --color-warning-50: #FFFBEB; /* amber-50 */
    --color-warning-100: #FEF3C7; /* amber-100 */
    --color-warning-500: #F59E0B; /* amber-500 */
    --color-warning-600: #D97706; /* amber-600 */
    --color-warning-foreground: #FFFFFF; /* white */

    --color-error: #EF4444; /* red-500 */
    --color-error-50: #FEF2F2; /* red-50 */
    --color-error-100: #FEE2E2; /* red-100 */
    --color-error-500: #EF4444; /* red-500 */
    --color-error-600: #DC2626; /* red-600 */
    --color-error-foreground: #FFFFFF; /* white */

    /* Border Colors */
    --color-border: rgba(148, 163, 184, 0.2); /* slate-400 with opacity */
    --color-border-active: rgba(148, 163, 184, 0.4); /* slate-400 with opacity */
    --color-input: #E2E8F0; /* slate-200 */
    --color-ring: #2563EB; /* blue-600 */

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);

    /* Animation Durations */
    --duration-fast: 150ms;
    --duration-normal: 200ms;
    --duration-slow: 400ms;

    /* Spacing Base Unit */
    --spacing-unit: 8px;
    --spacing-base: 16px;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-text-primary font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Breathing Animation */
  @keyframes breathe {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
  }

  .animate-breathe {
    animation: breathe 2s ease-in-out infinite;
  }

  /* Voice Visualization */
  @keyframes pulse-wave {
    0% { transform: scaleY(1); }
    50% { transform: scaleY(1.5); }
    100% { transform: scaleY(1); }
  }

  .animate-pulse-wave {
    animation: pulse-wave 0.6s ease-in-out infinite;
  }

  /* Voice Waveform Bars - Fix for missing animation */
  @keyframes waveform-pulse {
    0%, 100% { 
      height: 4px; 
      opacity: 0.6; 
    }
    50% { 
      height: 16px; 
      opacity: 1; 
    }
  }

  .voice-waveform-bar {
    @apply w-1 bg-primary-500 rounded-full;
    height: 4px;
    animation: waveform-pulse 0.8s ease-in-out infinite;
  }

  /* Ambient Loading */
  @keyframes ambient-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    33% { transform: translateY(-10px) rotate(120deg); opacity: 1; }
    66% { transform: translateY(5px) rotate(240deg); opacity: 0.8; }
  }

  .animate-ambient-float {
    animation: ambient-float 3s ease-in-out infinite;
  }

  /* Consultation Status Indicator */
  @keyframes consultation-pulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4); }
    50% { box-shadow: 0 0 0 8px rgba(37, 99, 235, 0); }
  }

  .animate-consultation-pulse {
    animation: consultation-pulse 2s ease-in-out infinite;
  }

  /* Add missing collaboration spin animation */
  @keyframes collaboration-spin {
    0% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(1.05); }
    50% { transform: rotate(180deg) scale(1.1); }
    75% { transform: rotate(270deg) scale(1.05); }
    100% { transform: rotate(360deg) scale(1); }
  }

  .animate-collaboration-spin {
    animation: collaboration-spin 4s ease-in-out infinite;
  }

  /* Add enhanced pulse animation for better preview */
  @keyframes enhanced-pulse {
    0%, 100% { 
      transform: scale(1); 
      opacity: 1; 
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); 
    }
    50% { 
      transform: scale(1.02); 
      opacity: 0.9; 
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); 
    }
  }

  .animate-enhanced-pulse {
    animation: enhanced-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Fix for smooth canvas animations */
  @keyframes canvas-glow {
    0%, 100% { filter: brightness(1) contrast(1); }
    50% { filter: brightness(1.1) contrast(1.05); }
  }

  .animate-canvas-glow {
    animation: canvas-glow 3s ease-in-out infinite;
  }
}

@layer components {
  /* Typography Classes */
  .font-heading {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .font-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .font-caption {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .font-data {
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  }

  /* Shadow Utilities */
  .shadow-minimal {
    box-shadow: var(--shadow-sm);
  }

  .shadow-elevated {
    box-shadow: var(--shadow-md);
  }

  .shadow-floating {
    box-shadow: var(--shadow-lg);
  }

  /* Transition Utilities */
  .transition-smooth {
    transition: all var(--duration-normal) ease-out;
  }

  .transition-fast {
    transition: all var(--duration-fast) ease-out;
  }

  .transition-slow {
    transition: all var(--duration-slow) ease-out;
  }

  /* Healthcare-specific Components */
  .consultation-card {
    @apply bg-surface border border-border rounded-lg p-6 shadow-minimal transition-smooth hover:shadow-elevated;
  }

  .agent-indicator {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-50 text-primary-600 border;
  }

  .session-status {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium;
  }

  .session-status.active {
    @apply bg-success-50 text-success-600 border;
  }

  .session-status.pending {
    @apply bg-warning-50 text-warning-600 border;
  }

  .session-status.completed {
    @apply bg-secondary-50 text-secondary-600 border border-secondary-200;
  }

  /* Voice Interface Specific */
  .voice-input-indicator {
    @apply w-4 h-4 rounded-full bg-primary-500 animate-breathe;
  }

  /* Settings Preview Interface */
  .settings-preview-container {
    @apply bg-surface border border-border rounded-xl overflow-hidden;
    transform: translateZ(0); /* Fix for rendering issues */
    will-change: transform; /* Optimize for animations */
  }

  .settings-preview-content {
    @apply transition-all duration-300 ease-out;
    transform: translateZ(0);
  }

  /* Voice Preview Player - Enhanced */
  .voice-preview-player {
    @apply fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-surface border border-border rounded-xl shadow-floating p-4;
    z-index: 1000; /* Ensure it appears above other content */
    transform: translateZ(0);
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.95);
  }

  @supports (backdrop-filter: blur(8px)) {
    .voice-preview-player {
      backdrop-filter: blur(8px);
      background: rgba(255, 255, 255, 0.9);
    }
  }

  /* Preview progress bar enhancement */
  .preview-progress {
    @apply w-full h-2 bg-secondary-200 rounded-full cursor-pointer transition-all duration-200;
  }

  .preview-progress-fill {
    @apply h-2 bg-primary-500 rounded-full transition-all duration-100;
    will-change: width;
  }

  /* Navigation Specific */
  .nav-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-lg text-text-secondary hover:text-text-primary hover:bg-secondary-50 transition-fast;
  }

  .nav-item.active {
    @apply text-primary-600 bg-primary-50 border;
  }

  .nav-badge {
    @apply inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-medium rounded-full bg-accent-500 text-accent-foreground;
  }

  /* Responsive preview enhancements */
  @media (max-width: 768px) {
    .voice-preview-player {
      @apply left-2 right-2 bottom-2;
      width: auto;
    }

    .settings-preview-container {
      @apply mx-2;
    }
  }
}

@layer utilities {
  /* Spacing Utilities */
  .spacing-unit {
    margin: var(--spacing-unit);
  }

  .spacing-base {
    margin: var(--spacing-base);
  }

  /* Grid Alignment */
  .grid-8 {
    background-image: linear-gradient(rgba(37, 99, 235, 0.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(37, 99, 235, 0.1) 1px, transparent 1px);
    background-size: 8px 8px;
  }

  /* Focus Utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  /* Healthcare Accessibility */
  .high-contrast {
    @apply text-text-primary bg-surface border-2 border-border-active;
  }

  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Emergency Controls */
  .emergency-control {
    @apply bg-error-500 hover:bg-error-600 text-error-foreground font-medium px-4 py-2 rounded-lg transition-fast focus-ring;
  }

  /* Preview rendering optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .smooth-transform {
    transition: transform var(--duration-normal) ease-out;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-secondary-100);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--color-secondary-300);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-secondary-400);
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .consultation-card {
    border: 1px solid #000;
    break-inside: avoid;
    margin-bottom: 16pt;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --color-border: rgba(0, 0, 0, 0.3);
    --color-border-active: rgba(0, 0, 0, 0.6);
  }

  .high-contrast {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-breathe,
  .animate-pulse-wave,
  .animate-ambient-float,
  .animate-consultation-pulse {
    animation: none;
  }

  .voice-waveform-bar {
    animation: none;
    height: 8px;
  }
}