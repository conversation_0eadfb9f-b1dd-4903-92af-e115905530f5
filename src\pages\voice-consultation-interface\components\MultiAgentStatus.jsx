import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const MultiAgentStatus = ({ 
  agents = [],
  consultationPhase = 'ready',
  isAgentsConsulting = false,
  consultationProgress = 0,
  className = ''
}) => {
  const [animationState, setAnimationState] = useState('idle');

  useEffect(() => {
    if (isAgentsConsulting) {
      setAnimationState('consulting');
    } else if (consultationPhase === 'active') {
      setAnimationState('active');
    } else {
      setAnimationState('idle');
    }
  }, [isAgentsConsulting, consultationPhase]);

  const getPhaseConfig = () => {
    switch (consultationPhase) {
      case 'active':
        return {
          color: 'success',
          icon: 'Activity',
          title: 'Consultation Active',
          description: 'AI agents are ready to assist you'
        };
      case 'consulting':
        return {
          color: 'warning',
          icon: 'Brain',
          title: 'Agents Consulting',
          description: 'AI agents are discussing your case'
        };
      case 'processing':
        return {
          color: 'primary',
          icon: 'Cpu',
          title: 'Processing',
          description: 'Analyzing your input and preparing response'
        };
      case 'completed':
        return {
          color: 'success',
          icon: 'CheckCircle',
          title: 'Consultation Complete',
          description: 'Session summary is being prepared'
        };
      default:
        return {
          color: 'secondary',
          icon: 'Users',
          title: 'Ready',
          description: 'AI medical team standing by'
        };
    }
  };

  const phaseConfig = getPhaseConfig();

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 shadow-minimal ${className}`}>
      {/* Phase Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            animationState === 'consulting' ? 'bg-warning-50 animate-ambient-float' :
            animationState === 'active'? 'bg-success-50 animate-breathe' : 'bg-secondary-50'
          }`}>
            <Icon 
              name={phaseConfig.icon}
              size={20}
              color={`var(--color-${phaseConfig.color})`}
            />
          </div>
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              {phaseConfig.title}
            </h3>
            <p className="text-sm text-text-secondary font-caption">
              {phaseConfig.description}
            </p>
          </div>
        </div>

        {/* Progress Indicator */}
        {consultationProgress > 0 && (
          <div className="text-right">
            <div className="text-lg font-semibold text-text-primary font-data">
              {Math.round(consultationProgress)}%
            </div>
            <div className="text-xs text-text-secondary">Complete</div>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {consultationProgress > 0 && (
        <div className="mb-4">
          <div className="w-full bg-secondary-100 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${
                phaseConfig.color === 'success' ? 'bg-success-500' :
                phaseConfig.color === 'warning' ? 'bg-warning-500' :
                phaseConfig.color === 'primary'? 'bg-primary-500' : 'bg-secondary-500'
              }`}
              style={{ width: `${consultationProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Agents Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {agents.map((agent, index) => (
          <div 
            key={agent.id || index}
            className={`p-4 rounded-lg border transition-all duration-300 ${
              agent.isActive 
                ? 'bg-primary-50 border-primary-200' 
                : agent.isConsulting
                ? 'bg-warning-50 border-warning-200 animate-pulse' :'bg-secondary-50 border-secondary-200'
            }`}
          >
            {/* Agent Header */}
            <div className="flex items-center space-x-3 mb-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                agent.isActive ? 'bg-primary-500' : agent.isConsulting ?'bg-warning-500': 'bg-secondary-400'
              }`}>
                <Icon 
                  name={
                    agent.specialty === 'General Practitioner' ? 'Stethoscope' :
                    agent.specialty === 'Cardiologist' ? 'Heart' :
                    agent.specialty === 'Nutritionist'? 'Apple' : 'User'
                  }
                  size={16}
                  color="white"
                />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-text-primary text-sm">
                  {agent.name}
                </h4>
                <p className="text-xs text-text-secondary">
                  {agent.specialty}
                </p>
              </div>
            </div>

            {/* Agent Status */}
            <div className="space-y-2">
              <div className={`session-status ${
                agent.isActive ? 'active' : agent.isConsulting ?'pending': 'completed'
              }`}>
                <Icon 
                  name={
                    agent.isActive ? 'Mic' : agent.isConsulting ?'MessageCircle': 'CheckCircle'
                  }
                  size={12}
                />
                {agent.isActive ? 'Speaking' : agent.isConsulting ?'Consulting': 'Ready'}
              </div>

              {/* Agent Confidence/Activity */}
              {agent.confidence && (
                <div className="flex items-center justify-between text-xs">
                  <span className="text-text-secondary">Confidence</span>
                  <span className="font-medium text-text-primary">
                    {Math.round(agent.confidence * 100)}%
                  </span>
                </div>
              )}
            </div>

            {/* Consultation Animation */}
            {agent.isConsulting && (
              <div className="mt-3 flex items-center justify-center space-x-1">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-1 bg-warning-500 rounded-full animate-bounce"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  ></div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Agents Consulting Animation */}
      {isAgentsConsulting && (
        <div className="mt-6 pt-4 border-t border-border">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-warning-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-warning-600">
                Agents are consulting privately
              </span>
            </div>
          </div>
          
          {/* Consultation Progress */}
          <div className="mt-3 flex items-center justify-center space-x-2">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="w-2 h-2 bg-warning-300 rounded-full animate-pulse"
                style={{ animationDelay: `${i * 0.3}s` }}
              ></div>
            ))}
          </div>
          
          <p className="text-center text-xs text-text-secondary mt-2">
            This may take a few moments while they analyze your case
          </p>
        </div>
      )}

      {/* Quick Stats */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {agents.filter(a => a.isActive).length}
            </div>
            <div className="text-xs text-text-secondary">Active</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {agents.filter(a => a.isConsulting).length}
            </div>
            <div className="text-xs text-text-secondary">Consulting</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {agents.length}
            </div>
            <div className="text-xs text-text-secondary">Total</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiAgentStatus;