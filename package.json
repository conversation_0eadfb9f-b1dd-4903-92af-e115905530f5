{"name": "voicehealth_ai", "version": "0.1.0", "type": "module", "private": true, "dependencies": {"@dhiwise/component-tagger": "^1.0.9", "@reduxjs/toolkit": "^2.6.1", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/forms": "^0.5.7", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "axios": "^1.8.4", "d3": "^7.9.0", "date-fns": "^4.1.0", "dexie": "^4.0.11", "dotenv": "^16.0.1", "framer-motion": "^10.16.4", "lucide-react": "^0.484.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-router-dom": "6.0.2", "react-router-hash-link": "^2.4.3", "recharts": "^2.15.2", "redux": "^5.0.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-elevation": "^2.0.0", "tailwindcss-fluid-type": "^2.0.7"}, "scripts": {"start": "vite", "dev": "vite --mode development", "build": "tsc && vite build --sourcemap", "build:prod": "tsc && vite build --mode production --sourcemap=hidden", "build:staging": "tsc && vite build --mode staging --sourcemap", "serve": "vite preview", "preview:prod": "vite preview --mode production --port 4029", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:integration": "vitest run src/tests/integration/", "test:database": "vitest run src/tests/database/", "test:regional": "vitest run src/tests/regional/", "test:e2e": "vitest run src/tests/integration/end-to-end-workflows.test.ts", "validate:critical-fixes": "tsx scripts/validate-critical-fixes.ts", "validate:phase3": "tsx scripts/validate-phase3-implementation.ts", "validate:post-implementation": "tsx scripts/validate-post-implementation.ts", "lint": "eslint src --ext ts,tsx,js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx,js,jsx --fix", "type-check": "tsc --noEmit", "cypress:open": "cypress open", "cypress:run": "cypress run", "lighthouse": "lhci autorun", "analyze": "npx vite-bundle-analyzer", "seed-demo": "node scripts/seedDemoData.js seed", "seed-knowledge": "tsx scripts/seedMedicalKnowledge.ts", "seed-knowledge:clear": "tsx scripts/seedMedicalKnowledge.ts --clear", "test:rag": "vitest run src/tests/ragSystem.test.ts", "migrate": "node scripts/run-migration.js", "test:security": "vitest run tests/security-validation.test.js", "verify:env": "vite --mode development --run scripts/verify-environment.js", "verify:db": "node scripts/test-db-connection.js", "test-demo": "node scripts/seedDemoData.js test", "cleanup-demo": "node scripts/seedDemoData.js cleanup", "docker:build": "docker build -f docker/production.Dockerfile -t voicehealth-ai .", "docker:run": "docker run -p 4028:4028 voicehealth-ai", "health-check": "curl -f http://localhost:4028/api/health || exit 1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/line-clamp": "^0.1.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "autoprefixer": "10.4.2", "jsdom": "^26.1.0", "postcss": "8.4.8", "tailwindcss": "3.4.6", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^5.0.0", "vite-tsconfig-paths": "3.6.0", "vitest": "^3.2.4"}}