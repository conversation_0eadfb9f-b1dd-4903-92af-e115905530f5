/**
 * DATABASE FOREIGN KEY CONSTRAINTS VALIDATION TEST SUITE
 * 
 * Tests to verify that the foreign key constraints added in the critical fixes
 * migration are working correctly and maintaining data integrity.
 * 
 * Tests cover:
 * - Foreign key constraint enforcement
 * - Data integrity validation
 * - Cascade behavior verification
 * - Constraint violation handling
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createClient } from '@supabase/supabase-js';

// Use test database configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'test-key';
const supabase = createClient(supabaseUrl, supabaseKey);

describe('Database Foreign Key Constraints Validation', () => {
  let testUserId: string;
  let testComponentId: string;
  let testRegionalDeploymentId: string;

  beforeEach(async () => {
    // Create test user for foreign key references
    const { data: userData, error: userError } = await supabase.auth.signUp({
      email: `test-${Date.now()}@voicehealth.ai`,
      password: 'test-password-123'
    });

    if (userError || !userData.user) {
      throw new Error('Failed to create test user');
    }

    testUserId = userData.user.id;

    // Create test system health check component
    const { data: componentData, error: componentError } = await supabase
      .from('system_health_checks')
      .insert({
        component: `test-component-${Date.now()}`,
        status: 'healthy',
        response_time: 100,
        error_rate: 0.1,
        dependencies: ['test-dep'],
        region: 'GH',
        environment: 'test'
      })
      .select()
      .single();

    if (componentError || !componentData) {
      throw new Error('Failed to create test component');
    }

    testComponentId = componentData.id;

    // Create test regional deployment
    const { data: deploymentData, error: deploymentError } = await supabase
      .from('regional_deployments')
      .insert({
        country: 'GH',
        country_code: 'GHA',
        region: 'Test Region',
        deployment_phase: 'pilot',
        status: { active: true },
        configuration: { test: true },
        created_by: testUserId
      })
      .select()
      .single();

    if (deploymentError || !deploymentData) {
      throw new Error('Failed to create test regional deployment');
    }

    testRegionalDeploymentId = deploymentData.id;
  });

  afterEach(async () => {
    // Clean up test data
    try {
      await supabase.from('performance_metrics').delete().eq('component_id', testComponentId);
      await supabase.from('medical_terminology_translations').delete().eq('verified_by', testUserId);
      await supabase.from('cultural_focus_groups').delete().eq('facilitator_id', testUserId);
      await supabase.from('regional_deployments').delete().eq('id', testRegionalDeploymentId);
      await supabase.from('system_health_checks').delete().eq('id', testComponentId);
      
      // Delete test user
      await supabase.auth.admin.deleteUser(testUserId);
    } catch (error) {
      console.warn('Cleanup error:', error);
    }
  });

  // =====================================================
  // MEDICAL TERMINOLOGY TRANSLATIONS CONSTRAINTS
  // =====================================================

  describe('Medical Terminology Translations Foreign Keys', () => {
    it('should enforce verified_by foreign key constraint', async () => {
      // Test valid foreign key
      const { error: validError } = await supabase
        .from('medical_terminology_translations')
        .insert({
          english_term: 'test-term-valid',
          translated_term: 'test-translation',
          target_language: 'en',
          category: 'symptom',
          verified_by: testUserId
        });

      expect(validError).toBeNull();

      // Test invalid foreign key
      const { error: invalidError } = await supabase
        .from('medical_terminology_translations')
        .insert({
          english_term: 'test-term-invalid',
          translated_term: 'test-translation',
          target_language: 'en',
          category: 'symptom',
          verified_by: 'invalid-user-id'
        });

      expect(invalidError).toBeDefined();
      expect(invalidError?.code).toBe('23503'); // Foreign key violation
    });

    it('should handle NULL verified_by values', async () => {
      const { error } = await supabase
        .from('medical_terminology_translations')
        .insert({
          english_term: 'test-term-null',
          translated_term: 'test-translation',
          target_language: 'en',
          category: 'symptom',
          verified_by: null
        });

      expect(error).toBeNull();
    });
  });

  // =====================================================
  // CULTURAL FOCUS GROUPS CONSTRAINTS
  // =====================================================

  describe('Cultural Focus Groups Foreign Keys', () => {
    it('should enforce facilitator_id foreign key constraint', async () => {
      // Test valid foreign key
      const { error: validError } = await supabase
        .from('cultural_focus_groups')
        .insert({
          session_data: { test: true },
          region: 'Test Region',
          country: 'GH',
          cultural_group: 'Test Group',
          session_date: new Date().toISOString(),
          facilitator_id: testUserId
        });

      expect(validError).toBeNull();

      // Test invalid foreign key
      const { error: invalidError } = await supabase
        .from('cultural_focus_groups')
        .insert({
          session_data: { test: true },
          region: 'Test Region',
          country: 'GH',
          cultural_group: 'Test Group',
          session_date: new Date().toISOString(),
          facilitator_id: 'invalid-user-id'
        });

      expect(invalidError).toBeDefined();
      expect(invalidError?.code).toBe('23503'); // Foreign key violation
    });
  });

  // =====================================================
  // PERFORMANCE METRICS CONSTRAINTS
  // =====================================================

  describe('Performance Metrics Foreign Keys', () => {
    it('should enforce component_id foreign key constraint', async () => {
      // Test valid foreign key
      const { error: validError } = await supabase
        .from('performance_metrics')
        .insert({
          component: 'test-component',
          metric_type: 'response_time',
          metric_value: 150.5,
          unit: 'ms',
          component_id: testComponentId
        });

      expect(validError).toBeNull();

      // Test invalid foreign key
      const { error: invalidError } = await supabase
        .from('performance_metrics')
        .insert({
          component: 'test-component',
          metric_type: 'response_time',
          metric_value: 150.5,
          unit: 'ms',
          component_id: 'invalid-component-id'
        });

      expect(invalidError).toBeDefined();
      expect(invalidError?.code).toBe('23503'); // Foreign key violation
    });

    it('should cascade delete when component is deleted', async () => {
      // Create performance metric linked to component
      const { data: metricData, error: metricError } = await supabase
        .from('performance_metrics')
        .insert({
          component: 'test-cascade',
          metric_type: 'response_time',
          metric_value: 200.0,
          unit: 'ms',
          component_id: testComponentId
        })
        .select()
        .single();

      expect(metricError).toBeNull();
      expect(metricData).toBeDefined();

      // Delete the component
      const { error: deleteError } = await supabase
        .from('system_health_checks')
        .delete()
        .eq('id', testComponentId);

      expect(deleteError).toBeNull();

      // Verify metric was cascade deleted
      const { data: remainingMetrics, error: checkError } = await supabase
        .from('performance_metrics')
        .select()
        .eq('id', metricData.id);

      expect(checkError).toBeNull();
      expect(remainingMetrics).toHaveLength(0);
    });
  });

  // =====================================================
  // SYSTEM HEALTH CHECKS CONSTRAINTS
  // =====================================================

  describe('System Health Checks Foreign Keys', () => {
    it('should enforce region_deployment_id foreign key constraint', async () => {
      // Test valid foreign key
      const { error: validError } = await supabase
        .from('system_health_checks')
        .insert({
          component: 'test-health-check',
          status: 'healthy',
          response_time: 100,
          error_rate: 0.0,
          dependencies: ['test'],
          region: 'GH',
          environment: 'test',
          region_deployment_id: testRegionalDeploymentId
        });

      expect(validError).toBeNull();

      // Test invalid foreign key
      const { error: invalidError } = await supabase
        .from('system_health_checks')
        .insert({
          component: 'test-health-check-invalid',
          status: 'healthy',
          response_time: 100,
          error_rate: 0.0,
          dependencies: ['test'],
          region: 'GH',
          environment: 'test',
          region_deployment_id: 'invalid-deployment-id'
        });

      expect(invalidError).toBeDefined();
      expect(invalidError?.code).toBe('23503'); // Foreign key violation
    });
  });

  // =====================================================
  // CONFIGURATION TABLES CONSTRAINTS
  // =====================================================

  describe('Configuration Tables Foreign Keys', () => {
    it('should enforce created_by constraints on configuration tables', async () => {
      // Test deployment configurations
      const { error: deploymentConfigError } = await supabase
        .from('deployment_configurations')
        .insert({
          environment: 'test',
          region: 'GH',
          configuration_type: 'application',
          configuration_data: { test: true },
          version: '1.0.0',
          created_by: testUserId
        });

      expect(deploymentConfigError).toBeNull();

      // Test monitoring dashboards
      const { error: dashboardError } = await supabase
        .from('monitoring_dashboards')
        .insert({
          name: 'Test Dashboard',
          dashboard_type: 'technical',
          widgets: [],
          layout: { test: true },
          created_by: testUserId
        });

      expect(dashboardError).toBeNull();

      // Test alert configurations
      const { error: alertError } = await supabase
        .from('alert_configurations')
        .insert({
          alert_name: 'Test Alert',
          component: 'test-component',
          metric: 'response_time',
          condition: 'greater_than',
          threshold: 1000,
          severity: 'warning',
          notification_channels: ['email'],
          created_by: testUserId
        });

      expect(alertError).toBeNull();
    });

    it('should reject invalid user IDs in configuration tables', async () => {
      const { error } = await supabase
        .from('deployment_configurations')
        .insert({
          environment: 'test',
          region: 'GH',
          configuration_type: 'application',
          configuration_data: { test: true },
          version: '1.0.0',
          created_by: 'invalid-user-id'
        });

      expect(error).toBeDefined();
      expect(error?.code).toBe('23503'); // Foreign key violation
    });
  });

  // =====================================================
  // DATA INTEGRITY VALIDATION
  // =====================================================

  describe('Data Integrity Validation', () => {
    it('should maintain referential integrity across related tables', async () => {
      // Create a chain of related records
      const { data: translationData, error: translationError } = await supabase
        .from('medical_terminology_translations')
        .insert({
          english_term: 'integrity-test',
          translated_term: 'test-integrity',
          target_language: 'en',
          category: 'symptom',
          verified_by: testUserId
        })
        .select()
        .single();

      expect(translationError).toBeNull();
      expect(translationData).toBeDefined();

      // Verify the relationship exists
      const { data: userCheck, error: userError } = await supabase
        .from('medical_terminology_translations')
        .select(`
          *,
          verified_user:verified_by (
            id,
            email
          )
        `)
        .eq('id', translationData.id)
        .single();

      expect(userError).toBeNull();
      expect(userCheck.verified_user).toBeDefined();
      expect(userCheck.verified_user.id).toBe(testUserId);
    });

    it('should handle constraint violations gracefully', async () => {
      // Attempt to create records with invalid references
      const invalidOperations = [
        supabase.from('medical_terminology_translations').insert({
          english_term: 'invalid-test',
          translated_term: 'test',
          target_language: 'en',
          category: 'symptom',
          verified_by: 'non-existent-user'
        }),
        supabase.from('performance_metrics').insert({
          component: 'invalid-test',
          metric_type: 'response_time',
          metric_value: 100,
          unit: 'ms',
          component_id: 'non-existent-component'
        })
      ];

      // All operations should fail with foreign key violations
      for (const operation of invalidOperations) {
        const { error } = await operation;
        expect(error).toBeDefined();
        expect(error?.code).toBe('23503');
      }
    });
  });

  // =====================================================
  // CONSTRAINT VALIDATION QUERIES
  // =====================================================

  describe('Constraint Validation Queries', () => {
    it('should verify all foreign key constraints exist', async () => {
      const { data: constraints, error } = await supabase
        .rpc('get_foreign_key_constraints', {
          schema_name: 'public'
        });

      if (error) {
        // If RPC doesn't exist, query information_schema directly
        const { data: constraintsData, error: constraintsError } = await supabase
          .from('information_schema.table_constraints')
          .select('constraint_name, table_name, constraint_type')
          .eq('constraint_type', 'FOREIGN KEY')
          .eq('table_schema', 'public');

        expect(constraintsError).toBeNull();
        expect(constraintsData).toBeDefined();

        // Verify specific constraints exist
        const constraintNames = constraintsData?.map(c => c.constraint_name) || [];
        expect(constraintNames).toContain('fk_medical_translations_verified_by');
        expect(constraintNames).toContain('fk_focus_groups_facilitator');
        expect(constraintNames).toContain('fk_performance_metrics_component');
      }
    });

    it('should verify constraint enforcement is active', async () => {
      // Test that constraints are actually being enforced
      const { error } = await supabase
        .from('medical_terminology_translations')
        .insert({
          english_term: 'constraint-test',
          translated_term: 'test',
          target_language: 'en',
          category: 'symptom',
          verified_by: '00000000-0000-0000-0000-000000000000' // Invalid UUID
        });

      expect(error).toBeDefined();
      expect(error?.code).toBe('23503'); // Foreign key violation
    });
  });
});
