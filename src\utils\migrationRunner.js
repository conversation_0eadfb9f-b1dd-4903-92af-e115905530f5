import { supabase } from './supabaseClient';

/**
 * Migration Runner for VoiceHealth AI Database
 * Handles programmatic execution of database migrations
 */

class MigrationRunner {
  constructor() {
    this.migrations = {
      'auth_and_consultation_system': {
        id: '20241216120000',
        name: 'Auth and Consultation System',
        dependencies: [],
        tables: ['user_profiles', 'consultation_sessions'],
        sql: this.getAuthMigrationSQL()
      },
      'payment_system': {
        id: '20241217000000', 
        name: 'Payment System',
        dependencies: ['auth_and_consultation_system'],
        tables: ['subscription_plans', 'user_subscriptions', 'payment_transactions', 'user_payment_methods', 'payment_webhooks'],
        sql: this.getPaymentMigrationSQL()
      }
    };
  }

  /**
   * Get Auth and Consultation System Migration SQL
   */
  getAuthMigrationSQL() {
    return `
      -- VoiceHealth AI Multi-Agent Consultation System with Authentication
      
      -- 1. Custom Types
      CREATE TYPE IF NOT EXISTS public.user_role AS ENUM ('patient', 'provider', 'admin');
      CREATE TYPE IF NOT EXISTS public.agent_specialty AS ENUM ('general_practitioner', 'cardiologist', 'nutritionist', 'psychiatrist', 'dermatologist', 'neurologist');
      CREATE TYPE IF NOT EXISTS public.consultation_status AS ENUM ('scheduled', 'active', 'paused', 'completed', 'cancelled');
      CREATE TYPE IF NOT EXISTS public.session_phase AS ENUM ('ready', 'active', 'agent_consultation', 'paused', 'completed');
      CREATE TYPE IF NOT EXISTS public.audio_quality AS ENUM ('high', 'medium', 'low');

      -- 2. User Profiles Table (Critical intermediary for auth relationships)
      CREATE TABLE IF NOT EXISTS public.user_profiles (
          id UUID PRIMARY KEY REFERENCES auth.users(id),
          email TEXT NOT NULL UNIQUE,
          full_name TEXT NOT NULL,
          role public.user_role DEFAULT 'patient'::public.user_role,
          avatar_url TEXT,
          phone TEXT,
          date_of_birth DATE,
          gender TEXT,
          preferred_language TEXT DEFAULT 'English',
          profile_completion_percentage INTEGER DEFAULT 0,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 3. Medical History Tables
      CREATE TABLE IF NOT EXISTS public.medical_conditions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          condition_name TEXT NOT NULL,
          diagnosed_date DATE,
          is_current BOOLEAN DEFAULT true,
          severity TEXT,
          notes TEXT,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS public.medications (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          medication_name TEXT NOT NULL,
          dosage TEXT,
          frequency TEXT,
          start_date DATE,
          end_date DATE,
          is_current BOOLEAN DEFAULT true,
          prescribed_by TEXT,
          notes TEXT,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 4. AI Agents System
      CREATE TABLE IF NOT EXISTS public.ai_agents (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          specialty public.agent_specialty NOT NULL,
          avatar_url TEXT,
          language TEXT DEFAULT 'English',
          voice_id TEXT,
          personality_traits JSONB,
          capabilities TEXT[],
          is_active BOOLEAN DEFAULT true,
          confidence_score DECIMAL(3,2) DEFAULT 0.90,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 5. Consultation Sessions
      CREATE TABLE IF NOT EXISTS public.consultation_sessions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          patient_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          primary_agent_id UUID REFERENCES public.ai_agents(id),
          session_title TEXT,
          status public.consultation_status DEFAULT 'scheduled'::public.consultation_status,
          current_phase public.session_phase DEFAULT 'ready'::public.session_phase,
          started_at TIMESTAMPTZ,
          ended_at TIMESTAMPTZ,
          duration_seconds INTEGER DEFAULT 0,
          progress_percentage INTEGER DEFAULT 0,
          is_emergency BOOLEAN DEFAULT false,
          session_notes TEXT,
          key_symptoms TEXT[],
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 6. Agent Participation in Sessions
      CREATE TABLE IF NOT EXISTS public.session_agent_participation (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
          agent_id UUID REFERENCES public.ai_agents(id),
          joined_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          left_at TIMESTAMPTZ,
          is_primary BOOLEAN DEFAULT false,
          contribution_summary TEXT,
          confidence_score DECIMAL(3,2),
          UNIQUE(session_id, agent_id)
      );

      -- 7. Conversation History
      CREATE TABLE IF NOT EXISTS public.conversation_messages (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
          speaker_type TEXT NOT NULL CHECK (speaker_type IN ('user', 'agent')),
          speaker_id UUID, -- user_id or agent_id
          speaker_name TEXT NOT NULL,
          content TEXT NOT NULL,
          timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          confidence_score DECIMAL(3,2),
          is_transcribed BOOLEAN DEFAULT false,
          audio_url TEXT,
          sequence_number INTEGER
      );

      -- 8. Audio Sessions and Files
      CREATE TABLE IF NOT EXISTS public.audio_sessions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
          audio_file_url TEXT,
          duration_seconds INTEGER,
          quality public.audio_quality DEFAULT 'medium'::public.audio_quality,
          file_size_bytes BIGINT,
          has_transcript BOOLEAN DEFAULT false,
          transcript_url TEXT,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 9. Session Recommendations
      CREATE TABLE IF NOT EXISTS public.session_recommendations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
          recommending_agent_id UUID REFERENCES public.ai_agents(id),
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          priority TEXT CHECK (priority IN ('Low', 'Medium', 'High', 'Critical')),
          category TEXT,
          is_implemented BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );
    `;
  }

  /**
   * Get Payment System Migration SQL
   */
  getPaymentMigrationSQL() {
    return `
      -- VoiceHealth AI Payment System Integration with Paystack
      
      -- 1. Payment-related Types
      DO $$ BEGIN
        CREATE TYPE IF NOT EXISTS public.payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded', 'cancelled');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
      
      DO $$ BEGIN
        CREATE TYPE IF NOT EXISTS public.subscription_status AS ENUM ('active', 'inactive', 'cancelled', 'expired', 'trial');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
      
      DO $$ BEGIN
        CREATE TYPE IF NOT EXISTS public.payment_method AS ENUM ('card', 'bank', 'ussd', 'qr', 'bank_transfer');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
      
      DO $$ BEGIN
        CREATE TYPE IF NOT EXISTS public.currency_type AS ENUM ('NGN', 'USD', 'GHS', 'ZAR', 'KES');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;

      -- 2. Subscription Plans Table
      CREATE TABLE IF NOT EXISTS public.subscription_plans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          description TEXT,
          price_ngn DECIMAL(10,2) NOT NULL,
          price_usd DECIMAL(10,2) NOT NULL,
          duration_days INTEGER NOT NULL,
          consultation_credits INTEGER DEFAULT 0, -- 0 means unlimited
          features JSONB DEFAULT '[]'::jsonb,
          is_active BOOLEAN DEFAULT true,
          is_popular BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 3. User Subscriptions Table
      CREATE TABLE IF NOT EXISTS public.user_subscriptions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          plan_id UUID REFERENCES public.subscription_plans(id),
          status public.subscription_status DEFAULT 'active'::public.subscription_status,
          started_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          expires_at TIMESTAMPTZ NOT NULL,
          is_trial BOOLEAN DEFAULT false,
          consultation_credits_remaining INTEGER DEFAULT 0,
          auto_renew BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 4. Payment Transactions Table
      CREATE TABLE IF NOT EXISTS public.payment_transactions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          subscription_id UUID REFERENCES public.user_subscriptions(id),
          consultation_session_id UUID REFERENCES public.consultation_sessions(id),
          paystack_reference TEXT UNIQUE NOT NULL,
          paystack_transaction_id TEXT,
          amount DECIMAL(10,2) NOT NULL,
          currency public.currency_type NOT NULL,
          payment_method public.payment_method,
          status public.payment_status DEFAULT 'pending'::public.payment_status,
          gateway_response JSONB,
          payment_date TIMESTAMPTZ,
          verification_date TIMESTAMPTZ,
          refund_date TIMESTAMPTZ,
          refund_amount DECIMAL(10,2),
          metadata JSONB DEFAULT '{}'::jsonb,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 5. Payment Methods Table (for saved cards)
      CREATE TABLE IF NOT EXISTS public.user_payment_methods (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          paystack_authorization_code TEXT NOT NULL,
          card_type TEXT,
          last_four TEXT,
          exp_month TEXT,
          exp_year TEXT,
          bank TEXT,
          brand TEXT,
          is_default BOOLEAN DEFAULT false,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- 6. Payment Webhooks Log
      CREATE TABLE IF NOT EXISTS public.payment_webhooks (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          event_type TEXT NOT NULL,
          paystack_event_id TEXT UNIQUE,
          reference TEXT,
          status TEXT,
          payload JSONB NOT NULL,
          processed BOOLEAN DEFAULT false,
          processed_at TIMESTAMPTZ,
          error_message TEXT,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      );

      -- Insert default plans if table is empty
      INSERT INTO public.subscription_plans (name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
      SELECT * FROM (VALUES
        ('Basic Plan', 'Perfect for occasional consultations', 5000.00, 12.00, 30, 3, 
         '["3 consultations per month", "Basic AI agents", "Text transcripts", "Email support"]'::jsonb, false),
        ('Premium Plan', 'Most popular for regular health monitoring', 12000.00, 28.00, 30, 10, 
         '["10 consultations per month", "All AI specialists", "Audio recordings", "Priority support", "Health insights"]'::jsonb, true),
        ('Professional Plan', 'Unlimited access for comprehensive care', 25000.00, 60.00, 30, 0, 
         '["Unlimited consultations", "All premium features", "Personal health dashboard", "24/7 priority support", "Advanced analytics"]'::jsonb, false)
      ) AS v(name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
      WHERE NOT EXISTS (SELECT 1 FROM public.subscription_plans LIMIT 1);
    `;
  }

  /**
   * Enhanced error handling for database operations
   */
  async handleDatabaseError(error, context = '') {
    const errorInfo = {
      code: error.code,
      message: error.message,
      details: error.details,
      hint: error.hint,
      context
    };

    console.error('Database Error:', errorInfo);

    // Handle specific error codes
    if (error.code === '42P01') { // relation does not exist
      return {
        type: 'missing_table',
        message: 'Database table does not exist. Migration required.',
        needsMigration: true,
        error: errorInfo
      };
    }

    if (error.code === '42501') { // insufficient privilege
      return {
        type: 'permission_error',
        message: 'Insufficient database privileges. Check RLS policies or database permissions.',
        needsPermissionFix: true,
        error: errorInfo
      };
    }

    return {
      type: 'unknown_error',
      message: error.message || 'Unknown database error occurred',
      error: errorInfo
    };
  }

  /**
   * Check which migrations need to be applied
   */
  async checkMigrationStatus() {
    try {
      const results = {};
      
      for (const [key, migration] of Object.entries(this.migrations)) {
        const status = await this.checkMigrationTables(migration.tables);
        results[key] = {
          ...migration,
          applied: status.allExist,
          missingTables: status.missingTables,
          needsApply: !status.allExist,
          errors: status.errors || []
        };
      }

      return {
        success: true,
        migrations: results,
        needsMigration: Object.values(results).some(m => m.needsApply)
      };
    } catch (error) {
      const handledError = await this.handleDatabaseError(error, 'checkMigrationStatus');
      return {
        success: false,
        error: handledError.message,
        errorDetails: handledError
      };
    }
  }

  /**
   * Check if migration tables exist with enhanced error handling
   */
  async checkMigrationTables(tables) {
    const results = [];
    const errors = [];
    
    for (const table of tables) {
      try {
        const exists = await this.checkTableExists(table);
        results.push(exists);
      } catch (error) {
        const handledError = await this.handleDatabaseError(error, `checkTableExists:${table}`);
        errors.push({
          table,
          error: handledError
        });
        results.push(false);
      }
    }

    const missingTables = tables.filter((table, index) => !results[index]);
    
    return {
      allExist: missingTables.length === 0,
      missingTables,
      existingTables: tables.filter((table, index) => results[index]),
      errors
    };
  }

  /**
   * Enhanced table existence check
   */
  async checkTableExists(tableName) {
    try {
      // First try to query the table
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (!error) return true;

      // Check if error indicates missing table
      if (error.code === '42P01' || error.message?.includes('does not exist')) {
        return false;
      }

      // For other errors, assume table exists but has access issues
      console.warn(`Table ${tableName} exists but has access issues:`, error);
      return true;
    } catch (error) {
      console.error(`Error checking table ${tableName}:`, error);
      return false;
    }
  }

  /**
   * Execute migration with enhanced error handling and transaction support
   */
  async executeMigration(migrationKey) {
    try {
      const migration = this.migrations[migrationKey];
      if (!migration) {
        throw new Error(`Migration ${migrationKey} not found`);
      }

      console.log(`🚀 Starting migration: ${migration.name}`);

      // Check dependencies
      for (const dep of migration.dependencies) {
        const depStatus = await this.checkMigrationTables(this.migrations[dep].tables);
        if (!depStatus.allExist) {
          throw new Error(`Dependency ${dep} not satisfied. Missing tables: ${depStatus.missingTables.join(', ')}`);
        }
      }

      // Execute the migration SQL with multiple strategies
      let executionResult;
      
      // Strategy 1: Try direct SQL execution
      try {
        executionResult = await this.executeSQLDirect(migration.sql);
        if (executionResult.success) {
          console.log(`✅ Direct SQL execution successful for ${migration.name}`);
        }
      } catch (error) {
        console.warn(`Direct SQL execution failed, trying alternative approach:`, error);
        executionResult = { success: false, error: error.message };
      }

      // Strategy 2: Try statement-by-statement execution
      if (!executionResult.success) {
        console.log(`🔄 Trying statement-by-statement execution for ${migration.name}`);
        executionResult = await this.executeMigrationStatements(migration.sql);
      }

      // Strategy 3: Try essential tables only
      if (!executionResult.success && migrationKey === 'payment_system') {
        console.log(`🔄 Trying essential tables creation for ${migration.name}`);
        executionResult = await this.createEssentialPaymentTables();
      }

      // Verify tables were created
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for DB to sync
      const verification = await this.checkMigrationTables(migration.tables);
      
      if (!verification.allExist) {
        console.warn(`Migration completed but some tables still missing: ${verification.missingTables.join(', ')}`);
        
        // Try to create missing tables individually
        for (const missingTable of verification.missingTables) {
          await this.createTableIndividually(missingTable, migrationKey);
        }
        
        // Final verification
        const finalVerification = await this.checkMigrationTables(migration.tables);
        if (!finalVerification.allExist) {
          return {
            success: false,
            error: `Migration incomplete. Still missing tables: ${finalVerification.missingTables.join(', ')}`,
            migration: migrationKey,
            missingTables: finalVerification.missingTables,
            partialSuccess: finalVerification.existingTables.length > 0
          };
        }
      }

      console.log(`✅ Migration ${migration.name} completed successfully`);
      return {
        success: true,
        migration: migrationKey,
        tablesCreated: migration.tables,
        message: `Migration ${migration.name} completed successfully`,
        executionStrategy: executionResult.strategy || 'unknown'
      };
    } catch (error) {
      console.error(`❌ Migration ${migrationKey} failed:`, error);
      const handledError = await this.handleDatabaseError(error, `executeMigration:${migrationKey}`);
      return {
        success: false,
        error: handledError.message,
        migration: migrationKey,
        errorDetails: handledError
      };
    }
  }

  /**
   * Execute SQL directly using Supabase RPC
   */
  async executeSQLDirect(sql) {
    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql_text: sql
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, strategy: 'direct_rpc' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Create essential payment tables individually
   */
  async createEssentialPaymentTables() {
    try {
      console.log('🔧 Creating essential payment tables individually...');
      
      // Create subscription_plans table first
      const subscriptionPlansSQL = `
        -- Create payment status enum
        DO $$ BEGIN
          CREATE TYPE IF NOT EXISTS public.payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded', 'cancelled');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;

        -- Create subscription status enum  
        DO $$ BEGIN
          CREATE TYPE IF NOT EXISTS public.subscription_status AS ENUM ('active', 'inactive', 'cancelled', 'expired', 'trial');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;

        -- Create subscription plans table
        CREATE TABLE IF NOT EXISTS public.subscription_plans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          description TEXT,
          price_ngn DECIMAL(10,2) NOT NULL,
          price_usd DECIMAL(10,2) NOT NULL,
          duration_days INTEGER NOT NULL,
          consultation_credits INTEGER DEFAULT 0,
          features JSONB DEFAULT '[]'::jsonb,
          is_active BOOLEAN DEFAULT true,
          is_popular BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );

        -- Insert default plans if table is empty
        INSERT INTO public.subscription_plans (name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
        SELECT * FROM (VALUES
          ('Basic Plan', 'Perfect for occasional consultations', 5000.00, 12.00, 30, 3, 
           '["3 consultations per month", "Basic AI agents", "Text transcripts", "Email support"]'::jsonb, false),
          ('Premium Plan', 'Most popular for regular health monitoring', 12000.00, 28.00, 30, 10, 
           '["10 consultations per month", "All AI specialists", "Audio recordings", "Priority support", "Health insights"]'::jsonb, true),
          ('Professional Plan', 'Unlimited access for comprehensive care', 25000.00, 60.00, 30, 0, 
           '["Unlimited consultations", "All premium features", "Personal health dashboard", "24/7 priority support", "Advanced analytics"]'::jsonb, false)
        ) AS v(name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
        WHERE NOT EXISTS (SELECT 1 FROM public.subscription_plans LIMIT 1);
      `;

      const result = await this.executeMigrationStatements(subscriptionPlansSQL);
      return { ...result, strategy: 'essential_tables' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a specific table individually
   */
  async createTableIndividually(tableName, migrationKey) {
    console.log(`🔧 Attempting to create table: ${tableName}`);
    
    const tableCreationSQL = this.getTableCreationSQL(tableName, migrationKey);
    if (!tableCreationSQL) {
      console.warn(`No creation SQL found for table: ${tableName}`);
      return false;
    }

    try {
      await this.executeMigrationStatements(tableCreationSQL);
      
      // Verify table was created
      const exists = await this.checkTableExists(tableName);
      if (exists) {
        console.log(`✅ Successfully created table: ${tableName}`);
        return true;
      }
    } catch (error) {
      console.error(`Failed to create table ${tableName}:`, error);
    }
    
    return false;
  }

  /**
   * Get specific table creation SQL
   */
  getTableCreationSQL(tableName, migrationKey) {
    const tableSQL = {
      subscription_plans: `
        CREATE TABLE IF NOT EXISTS public.subscription_plans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          description TEXT,
          price_ngn DECIMAL(10,2) NOT NULL,
          price_usd DECIMAL(10,2) NOT NULL,
          duration_days INTEGER NOT NULL,
          consultation_credits INTEGER DEFAULT 0,
          features JSONB DEFAULT '[]'::jsonb,
          is_active BOOLEAN DEFAULT true,
          is_popular BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
        
        INSERT INTO public.subscription_plans (name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
        SELECT * FROM (VALUES
          ('Basic Plan', 'Perfect for occasional consultations', 5000.00, 12.00, 30, 3, 
           '["3 consultations per month", "Basic AI agents", "Text transcripts", "Email support"]'::jsonb, false),
          ('Premium Plan', 'Most popular for regular health monitoring', 12000.00, 28.00, 30, 10, 
           '["10 consultations per month", "All AI specialists", "Audio recordings", "Priority support", "Health insights"]'::jsonb, true),
          ('Professional Plan', 'Unlimited access for comprehensive care', 25000.00, 60.00, 30, 0, 
           '["Unlimited consultations", "All premium features", "Personal health dashboard", "24/7 priority support", "Advanced analytics"]'::jsonb, false)
        ) AS v(name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
        WHERE NOT EXISTS (SELECT 1 FROM public.subscription_plans LIMIT 1);
      `,
      user_subscriptions: `
        CREATE TABLE IF NOT EXISTS public.user_subscriptions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
          plan_id UUID REFERENCES public.subscription_plans(id),
          status public.subscription_status DEFAULT 'active'::public.subscription_status,
          started_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          expires_at TIMESTAMPTZ NOT NULL,
          is_trial BOOLEAN DEFAULT false,
          consultation_credits_remaining INTEGER DEFAULT 0,
          auto_renew BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
      `
    };

    return tableSQL[tableName] || null;
  }

  /**
   * Execute migration as individual statements with enhanced error handling
   */
  async executeMigrationStatements(sql) {
    try {
      // Split SQL into individual statements
      const statements = sql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      const results = [];
      let successCount = 0;
      
      for (const statement of statements) {
        try {
          if (statement.toLowerCase().includes('select ')) {
            // Skip SELECT statements (like success messages)
            continue;
          }

          // Try to execute statement
          const { error } = await supabase.rpc('exec_sql', {
            sql_text: statement + ';'
          });

          const success = !error;
          if (success) successCount++;
          
          results.push({ 
            statement: statement.substring(0, 50), 
            success,
            error: error?.message
          });

          if (error && !this.isIgnorableError(error)) {
            console.warn(`Statement failed: ${statement.substring(0, 50)}...`, error);
          }
        } catch (err) {
          console.warn(`Statement execution error:`, err);
          results.push({ 
            statement: statement.substring(0, 50), 
            success: false, 
            error: err.message 
          });
        }
      }

      return {
        success: successCount > 0,
        results,
        successCount,
        totalStatements: statements.length,
        message: `Migration statements executed: ${successCount}/${statements.length} successful`,
        strategy: 'statement_by_statement'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        strategy: 'statement_by_statement'
      };
    }
  }

  /**
   * Check if error can be safely ignored
   */
  isIgnorableError(error) {
    const ignorableErrors = [
      'already exists',
      'duplicate_object',
      'relation already exists',
      'type already exists'
    ];
    
    return ignorableErrors.some(msg => 
      error.message?.toLowerCase().includes(msg.toLowerCase())
    );
  }

  /**
   * Quick fix for subscription_plans table specifically with enhanced error handling
   */
  async quickFixSubscriptionPlans() {
    try {
      console.log('🔧 Starting quick fix for subscription_plans table...');
      
      // Check if table exists
      const tableExists = await this.checkTableExists('subscription_plans');
      
      if (tableExists) {
        // Check if table has data
        try {
          const { data, error } = await supabase
            .from('subscription_plans')
            .select('count(*)', { count: 'exact' });
          
          if (!error && data && data.length > 0) {
            return {
              success: true,
              message: 'subscription_plans table exists and has data',
              action: 'none'
            };
          }
        } catch (error) {
          console.warn('Could not check table data, proceeding with recreation:', error);
        }
      }

      // Create types first
      const typesSQL = `
        DO $$ BEGIN
          CREATE TYPE IF NOT EXISTS public.payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded', 'cancelled');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;

        DO $$ BEGIN
          CREATE TYPE IF NOT EXISTS public.subscription_status AS ENUM ('active', 'inactive', 'cancelled', 'expired', 'trial');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;

        DO $$ BEGIN
          CREATE TYPE IF NOT EXISTS public.payment_method AS ENUM ('card', 'bank', 'ussd', 'qr', 'bank_transfer');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;

        DO $$ BEGIN
          CREATE TYPE IF NOT EXISTS public.currency_type AS ENUM ('NGN', 'USD', 'GHS', 'ZAR', 'KES');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
      `;

      await this.executeMigrationStatements(typesSQL);

      // Execute subscription plans creation
      const sql = `
        -- Create subscription plans table
        CREATE TABLE IF NOT EXISTS public.subscription_plans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          description TEXT,
          price_ngn DECIMAL(10,2) NOT NULL,
          price_usd DECIMAL(10,2) NOT NULL,
          duration_days INTEGER NOT NULL,
          consultation_credits INTEGER DEFAULT 0,
          features JSONB DEFAULT '[]'::jsonb,
          is_active BOOLEAN DEFAULT true,
          is_popular BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );

        -- Insert default plans if not exists
        INSERT INTO public.subscription_plans (name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
        SELECT * FROM (VALUES
          ('Basic Plan', 'Perfect for occasional consultations', 5000.00, 12.00, 30, 3, 
           '["3 consultations per month", "Basic AI agents", "Text transcripts", "Email support"]'::jsonb, false),
          ('Premium Plan', 'Most popular for regular health monitoring', 12000.00, 28.00, 30, 10, 
           '["10 consultations per month", "All AI specialists", "Audio recordings", "Priority support", "Health insights"]'::jsonb, true),
          ('Professional Plan', 'Unlimited access for comprehensive care', 25000.00, 60.00, 30, 0, 
           '["Unlimited consultations", "All premium features", "Personal health dashboard", "24/7 priority support", "Advanced analytics"]'::jsonb, false)
        ) AS v(name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
        WHERE NOT EXISTS (SELECT 1 FROM public.subscription_plans LIMIT 1);
      `;

      const result = await this.executeMigrationStatements(sql);
      
      if (result.success) {
        // Verify table exists and has data
        await new Promise(resolve => setTimeout(resolve, 500)); // Brief wait
        const finalExists = await this.checkTableExists('subscription_plans');
        
        return {
          success: finalExists,
          message: finalExists 
            ? 'subscription_plans table created and populated successfully' :'Table creation attempted but verification failed',
          action: 'created',
          details: result
        };
      }

      return {
        success: false,
        error: 'Failed to create subscription_plans table',
        details: result
      };
    } catch (error) {
      const handledError = await this.handleDatabaseError(error, 'quickFixSubscriptionPlans');
      return {
        success: false,
        error: handledError.message,
        errorDetails: handledError
      };
    }
  }
}

// Export singleton instance
const migrationRunner = new MigrationRunner();
export default migrationRunner;

// Named exports
export const {
  checkMigrationStatus,
  executeMigration,
  executeAllMigrations,
  quickFixSubscriptionPlans
} = migrationRunner;