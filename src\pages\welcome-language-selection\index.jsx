import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import LanguageSelector from './components/LanguageSelector';
import WelcomeHeader from './components/WelcomeHeader';
import ProgressIndicator from './components/ProgressIndicator';
import GetStartedButton from './components/GetStartedButton';

const WelcomeLanguageSelection = () => {
  const navigate = useNavigate();
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(false);

  const handleGetStarted = () => {
    setIsLoading(true);
    // Store language preference in localStorage
    localStorage.setItem('selectedLanguage', selectedLanguage);
    
    // Navigate to country selection
    setTimeout(() => {
      navigate('/country-regional-selection');
    }, 300);
  };

  const handleLanguageChange = (language) => {
    setSelectedLanguage(language);
    // Apply language change to UI immediately if needed
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Progress Indicator */}
      <div className="pt-8 px-4">
        <ProgressIndicator currentStep={1} totalSteps={5} />
      </div>

      {/* Main Content */}
      <div className="flex flex-col justify-center items-center px-4 py-12 max-w-lg mx-auto">
        {/* Language Selector */}
        <div className="absolute top-6 right-6">
          <LanguageSelector
            selectedLanguage={selectedLanguage}
            onLanguageChange={handleLanguageChange}
          />
        </div>

        {/* Welcome Header */}
        <div className="text-center space-y-8 mb-12">
          <WelcomeHeader selectedLanguage={selectedLanguage} />
        </div>

        {/* Spacer for better layout */}
        <div className="flex-grow"></div>

        {/* Get Started Button */}
        <div className="w-full max-w-sm">
          <GetStartedButton
            onClick={handleGetStarted}
            isLoading={isLoading}
            selectedLanguage={selectedLanguage}
          />
        </div>

        {/* Footer Help Text */}
        <div className="mt-8 text-center">
          <p className="text-sm text-text-muted">
            {selectedLanguage === 'en' ? 'Your AI-powered health assistant for personalized consultations' : 
             selectedLanguage === 'tw' ? 'Wo AI-powered akwahosan boafoɔ ma personalized nhwehwɛmu' :
             selectedLanguage === 'yo' ? 'Oluranlowo ilera AI-powered rẹ fun awọn ifọrọwanilẹnuwo ti ara ẹni' :
             selectedLanguage === 'sw'? 'Msaidizi wako wa afya wa AI kwa mashauriano ya kibinafsi' : 'Jou AI-aangedrewe gesondheidsassistent vir persoonlike konsultasies'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeLanguageSelection;