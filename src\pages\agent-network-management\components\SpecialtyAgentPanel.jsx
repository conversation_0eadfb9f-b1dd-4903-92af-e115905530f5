import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SpecialtyAgentPanel = ({ agent, onConfigure }) => {
  if (!agent) return null;

  const getStatusIndicator = (status) => {
    switch (status) {
      case 'active':
        return { color: 'bg-success-500', text: 'Active', textColor: 'text-success-600' };
      case 'busy':
        return { color: 'bg-warning-500', text: 'High Load', textColor: 'text-warning-600' };
      case 'offline':
        return { color: 'bg-error-500', text: 'Offline', textColor: 'text-error-600' };
      default:
        return { color: 'bg-text-secondary', text: 'Unknown', textColor: 'text-text-secondary' };
    }
  };

  const getLoadColor = (load) => {
    if (load >= 80) return 'text-error-500';
    if (load >= 60) return 'text-warning-500';
    return 'text-success-500';
  };

  const status = getStatusIndicator(agent.status);

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal p-6 hover:shadow-small transition-all duration-200">
      {/* Agent Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center text-2xl">
            {agent.avatar}
          </div>
          <div>
            <h3 className="font-semibold text-text-primary">{agent.name}</h3>
            <p className="text-sm text-text-secondary">{agent.shortName}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${status.color}`}></div>
          <span className={`text-sm font-medium ${status.textColor}`}>{status.text}</span>
        </div>
      </div>

      {/* Agent Description */}
      <p className="text-sm text-text-secondary mb-4 line-clamp-2">
        {agent.description}
      </p>

      {/* Performance Metrics */}
      <div className="space-y-3 mb-6">
        {/* Confidence Score */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium text-text-primary">Confidence Score</span>
            <span className="text-sm font-bold text-text-primary">{agent.confidenceScore}%</span>
          </div>
          <div className="w-full bg-background rounded-full h-2">
            <div 
              className="bg-success-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${agent.confidenceScore}%` }}
            ></div>
          </div>
        </div>

        {/* Current Load */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <span className="text-sm font-medium text-text-primary">Current Load</span>
            <span className={`text-sm font-bold ${getLoadColor(agent.currentLoad)}`}>
              {agent.currentLoad}%
            </span>
          </div>
          <div className="w-full bg-background rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                agent.currentLoad >= 80 ? 'bg-error-500' :
                agent.currentLoad >= 60 ? 'bg-warning-500' : 'bg-success-500'
              }`}
              style={{ width: `${agent.currentLoad}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Key Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="text-center p-3 bg-background rounded-lg">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Icon name="MessageCircle" size={16} className="text-primary-500" />
            <span className="text-lg font-bold text-text-primary">{agent.consultationsToday}</span>
          </div>
          <p className="text-xs text-text-secondary">Today's Consults</p>
        </div>
        
        <div className="text-center p-3 bg-background rounded-lg">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Icon name="Clock" size={16} className="text-success-500" />
            <span className="text-lg font-bold text-text-primary">{agent.averageResponseTime}</span>
          </div>
          <p className="text-xs text-text-secondary">Avg Response</p>
        </div>
      </div>

      {/* Queue Status */}
      {agent.queueLength > 0 && (
        <div className="flex items-center gap-2 p-3 bg-warning-50 border border-warning-200 rounded-lg mb-4">
          <Icon name="Users" size={16} className="text-warning-600" />
          <span className="text-sm text-warning-700">
            {agent.queueLength} patient{agent.queueLength !== 1 ? 's' : ''} in queue
          </span>
        </div>
      )}

      {/* Expertise Tags */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-text-primary mb-2">Key Expertise</h4>
        <div className="flex flex-wrap gap-2">
          {agent.expertise?.slice(0, 3).map((skill, index) => (
            <span 
              key={index}
              className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
            >
              {skill}
            </span>
          ))}
          {agent.expertise?.length > 3 && (
            <span className="px-2 py-1 bg-surface border border-border text-text-secondary text-xs rounded-full">
              +{agent.expertise.length - 3} more
            </span>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onConfigure}
          iconName="Settings"
          className="flex-1"
        >
          Configure
        </Button>
        
        <Button
          variant="primary"
          size="sm"
          iconName="Activity"
          className="flex-1"
        >
          Monitor
        </Button>
      </div>
    </div>
  );
};

export default SpecialtyAgentPanel;