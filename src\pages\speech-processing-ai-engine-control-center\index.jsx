import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import SpeechProcessingPanel from './components/SpeechProcessingPanel';
import AIEngineConfiguration from './components/AIEngineConfiguration';
import AgentOrchestrationPanel from './components/AgentOrchestrationPanel';
import ConversationManager from './components/ConversationManager';
import RealTimeLogsPanel from './components/RealTimeLogsPanel';

const SpeechProcessingAIEngineControlCenter = () => {
  const navigate = useNavigate();
  const [activeView, setActiveView] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState({
    speechProcessing: 'active',
    aiEngines: 'active',
    agentOrchestration: 'active',
    conversationManager: 'active',
    overallHealth: 'healthy'
  });

  const [systemMetrics, setSystemMetrics] = useState({
    activeConsultations: 12,
    speechProcessingLatency: 145,
    aiEngineLoad: 67,
    queueLength: 8,
    systemUptime: '99.9%',
    errorRate: 0.02
  });

  const [maintenanceMode, setMaintenanceMode] = useState(false);

  useEffect(() => {
    // Simulate loading system data
    const loadSystemData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      setIsLoading(false);
    };

    loadSystemData();
  }, []);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemMetrics(prev => ({
        ...prev,
        activeConsultations: prev.activeConsultations + Math.floor(Math.random() * 3) - 1,
        speechProcessingLatency: prev.speechProcessingLatency + Math.floor(Math.random() * 20) - 10,
        aiEngineLoad: Math.max(20, Math.min(90, prev.aiEngineLoad + Math.floor(Math.random() * 10) - 5)),
        queueLength: Math.max(0, prev.queueLength + Math.floor(Math.random() * 3) - 1)
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': case 'healthy': return 'text-success-600 bg-success-100';
      case 'warning': return 'text-warning-600 bg-warning-100';
      case 'error': case 'critical': return 'text-error-600 bg-error-100';
      case 'maintenance': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleEmergencyStop = () => {
    console.log('Emergency stop initiated');
    setSystemStatus(prev => ({
      ...prev,
      overallHealth: 'maintenance'
    }));
    setMaintenanceMode(true);
  };

  const handleMaintenanceMode = () => {
    const newMode = !maintenanceMode;
    setMaintenanceMode(newMode);
    setSystemStatus(prev => ({
      ...prev,
      overallHealth: newMode ? 'maintenance' : 'healthy'
    }));
  };

  const handleSystemReset = () => {
    console.log('System reset initiated');
    setIsLoading(true);
    setTimeout(() => {
      setSystemStatus({
        speechProcessing: 'active',
        aiEngines: 'active',
        agentOrchestration: 'active',
        conversationManager: 'active',
        overallHealth: 'healthy'
      });
      setMaintenanceMode(false);
      setIsLoading(false);
    }, 3000);
  };

  const navigationTabs = [
    { id: 'overview', label: 'System Overview', icon: 'BarChart3' },
    { id: 'speech', label: 'Speech Processing', icon: 'Mic' },
    { id: 'ai-engines', label: 'AI Engines', icon: 'Brain' },
    { id: 'orchestration', label: 'Agent Orchestration', icon: 'Users' },
    { id: 'conversation', label: 'Conversation Manager', icon: 'MessageCircle' },
    { id: 'logs', label: 'Real-time Logs', icon: 'FileText' }
  ];

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
              <Icon name="Cpu" size={32} color="var(--primary-500)" />
            </div>
            <p className="text-text-secondary text-lg">Loading AI Engine Control Center...</p>
            <p className="text-text-secondary text-sm mt-2">Initializing system components</p>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'speech':
        return <SpeechProcessingPanel />;
      
      case 'ai-engines':
        return <AIEngineConfiguration />;
      
      case 'orchestration':
        return <AgentOrchestrationPanel />;
      
      case 'conversation':
        return <ConversationManager />;
      
      case 'logs':
        return <RealTimeLogsPanel />;
      
      default:
        return (
          <div className="space-y-8">
            {/* System Health Overview */}
            <div className="bg-surface rounded-lg border border-border shadow-minimal p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-text-primary font-heading">
                  System Health Overview
                </h3>
                <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(systemStatus.overallHealth)}`}>
                  <Icon name="Activity" size={16} className="mr-2" />
                  {systemStatus.overallHealth}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { key: 'speechProcessing', label: 'Speech Processing', icon: 'Mic' },
                  { key: 'aiEngines', label: 'AI Engines', icon: 'Brain' },
                  { key: 'agentOrchestration', label: 'Agent Orchestration', icon: 'Users' },
                  { key: 'conversationManager', label: 'Conversation Manager', icon: 'MessageCircle' }
                ].map((component) => (
                  <div key={component.key} className="bg-secondary-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Icon name={component.icon} size={20} className="text-primary-500" />
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(systemStatus[component.key])}`}>
                        {systemStatus[component.key]}
                      </span>
                    </div>
                    <h4 className="font-medium text-text-primary text-sm">{component.label}</h4>
                  </div>
                ))}
              </div>
            </div>

            {/* Key Metrics */}
            <div className="bg-surface rounded-lg border border-border shadow-minimal p-6">
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-6">
                Real-time Performance Metrics
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon name="Users" size={20} className="text-success-500 mr-2" />
                    <span className="text-sm font-medium text-text-secondary">Active Consultations</span>
                  </div>
                  <p className="text-2xl font-bold text-text-primary">{systemMetrics.activeConsultations}</p>
                  <p className="text-sm text-success-600 mt-1">+2 from last hour</p>
                </div>

                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon name="Zap" size={20} className="text-warning-500 mr-2" />
                    <span className="text-sm font-medium text-text-secondary">Speech Processing Latency</span>
                  </div>
                  <p className="text-2xl font-bold text-text-primary">{systemMetrics.speechProcessingLatency}ms</p>
                  <p className="text-sm text-success-600 mt-1">-15ms improvement</p>
                </div>

                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon name="Cpu" size={20} className="text-primary-500 mr-2" />
                    <span className="text-sm font-medium text-text-secondary">AI Engine Load</span>
                  </div>
                  <p className="text-2xl font-bold text-text-primary">{systemMetrics.aiEngineLoad}%</p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        systemMetrics.aiEngineLoad >= 80 ? 'bg-error-500' :
                        systemMetrics.aiEngineLoad >= 60 ? 'bg-warning-500' : 'bg-success-500'
                      }`}
                      style={{ width: `${systemMetrics.aiEngineLoad}%` }}
                    />
                  </div>
                </div>

                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon name="Clock" size={20} className="text-purple-500 mr-2" />
                    <span className="text-sm font-medium text-text-secondary">Queue Length</span>
                  </div>
                  <p className="text-2xl font-bold text-text-primary">{systemMetrics.queueLength}</p>
                  <p className="text-sm text-text-secondary mt-1">patients waiting</p>
                </div>

                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon name="Shield" size={20} className="text-success-500 mr-2" />
                    <span className="text-sm font-medium text-text-secondary">System Uptime</span>
                  </div>
                  <p className="text-2xl font-bold text-success-600">{systemMetrics.systemUptime}</p>
                  <p className="text-sm text-text-secondary mt-1">Last 30 days</p>
                </div>

                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon name="AlertTriangle" size={20} className="text-error-500 mr-2" />
                    <span className="text-sm font-medium text-text-secondary">Error Rate</span>
                  </div>
                  <p className="text-2xl font-bold text-success-600">{systemMetrics.errorRate}%</p>
                  <p className="text-sm text-success-600 mt-1">Below threshold</p>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-surface rounded-lg border border-border shadow-minimal p-6">
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-6">
                System Controls
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  iconName="Mic"
                  className="justify-center h-16"
                  onClick={() => setActiveView('speech')}
                >
                  Speech Processing
                </Button>
                <Button
                  variant="outline"
                  iconName="Brain"
                  className="justify-center h-16"
                  onClick={() => setActiveView('ai-engines')}
                >
                  AI Engine Config
                </Button>
                <Button
                  variant="outline"
                  iconName="Users"
                  className="justify-center h-16"
                  onClick={() => setActiveView('orchestration')}
                >
                  Agent Orchestration
                </Button>
                <Button
                  variant="outline"
                  iconName="MessageCircle"
                  className="justify-center h-16"
                  onClick={() => setActiveView('conversation')}
                >
                  Conversation Manager
                </Button>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-text-primary font-heading">
              Speech Processing & AI Engine Control Center
            </h1>
            <p className="text-text-secondary mt-2">
              Centralized management and monitoring of core AI processing components
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/analytics-insights-dashboard')}
              iconName="BarChart3"
            >
              Analytics
            </Button>
            <Button
              variant={maintenanceMode ? "primary" : "outline"}
              onClick={handleMaintenanceMode}
              iconName="Wrench"
            >
              {maintenanceMode ? 'Exit Maintenance' : 'Maintenance Mode'}
            </Button>
            <Button
              variant="outline"
              onClick={handleEmergencyStop}
              iconName="AlertTriangle"
              className="text-error-600 border-error-300 hover:bg-error-50"
            >
              Emergency Stop
            </Button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-border">
            <nav className="flex space-x-8 overflow-x-auto">
              {navigationTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveView(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                    activeView === tab.id
                      ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-gray-300'
                  }`}
                >
                  <Icon name={tab.icon} size={18} className="mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Maintenance Mode Banner */}
        {maintenanceMode && (
          <div className="bg-warning-50 border border-warning-200 rounded-lg p-4 mb-8">
            <div className="flex items-center">
              <Icon name="AlertTriangle" size={20} className="text-warning-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-warning-800">System in Maintenance Mode</h3>
                <p className="text-sm text-warning-600 mt-1">
                  New consultations are temporarily disabled. Existing sessions will continue normally.
                </p>
              </div>
              <div className="ml-auto">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleSystemReset}
                  iconName="RotateCcw"
                >
                  Reset System
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {renderMainContent()}
        </div>
      </div>
    </div>
  );
};

export default SpeechProcessingAIEngineControlCenter;