-- <PERSON><PERSON><PERSON> BACKUP SYSTEM MIGRATION
-- 
-- Creates comprehensive backup infrastructure for encrypted audio data
-- with HIPAA-compliant retention policies and cross-region replication support.

-- Create backup metadata table
CREATE TABLE IF NOT EXISTS public.audio_backup_metadata (
    backup_id TEXT PRIMARY KEY,
    message_id TEXT NOT NULL,
    backup_type TEXT NOT NULL CHECK (backup_type IN ('full', 'incremental')),
    location TEXT NOT NULL CHECK (location IN ('local', 'cloud', 'cross_region')),
    cloud_path TEXT,
    size BIGINT NOT NULL DEFAULT 0,
    checksum TEXT NOT NULL,
    encrypted BOOLEAN NOT NULL DEFAULT true,
    retention_expiry TIMESTAMPTZ NOT NULL,
    session_id TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_message_id ON public.audio_backup_metadata(message_id);
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_user_id ON public.audio_backup_metadata(user_id);
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_session_id ON public.audio_backup_metadata(session_id);
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_retention_expiry ON public.audio_backup_metadata(retention_expiry);
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_created_at ON public.audio_backup_metadata(created_at);
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_location ON public.audio_backup_metadata(location);
CREATE INDEX IF NOT EXISTS idx_audio_backup_metadata_backup_type ON public.audio_backup_metadata(backup_type);

-- Create backup verification logs table
CREATE TABLE IF NOT EXISTS public.backup_verification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backup_id TEXT NOT NULL REFERENCES public.audio_backup_metadata(backup_id) ON DELETE CASCADE,
    verification_timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    verification_result BOOLEAN NOT NULL,
    checksum_match BOOLEAN NOT NULL DEFAULT false,
    size_match BOOLEAN NOT NULL DEFAULT false,
    encryption_valid BOOLEAN NOT NULL DEFAULT false,
    metadata_valid BOOLEAN NOT NULL DEFAULT false,
    errors TEXT[],
    warnings TEXT[],
    verification_duration_ms INTEGER,
    performed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for verification logs
CREATE INDEX IF NOT EXISTS idx_backup_verification_logs_backup_id ON public.backup_verification_logs(backup_id);
CREATE INDEX IF NOT EXISTS idx_backup_verification_logs_timestamp ON public.backup_verification_logs(verification_timestamp);
CREATE INDEX IF NOT EXISTS idx_backup_verification_logs_result ON public.backup_verification_logs(verification_result);

-- Create backup recovery logs table
CREATE TABLE IF NOT EXISTS public.backup_recovery_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id TEXT NOT NULL,
    backup_id TEXT REFERENCES public.audio_backup_metadata(backup_id) ON DELETE SET NULL,
    recovery_timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    recovery_successful BOOLEAN NOT NULL,
    recovery_source TEXT NOT NULL CHECK (recovery_source IN ('local_backup', 'cloud_backup', 'cross_region_backup')),
    recovery_duration_ms INTEGER,
    integrity_verified BOOLEAN NOT NULL DEFAULT false,
    error_message TEXT,
    initiated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    session_id TEXT
);

-- Create indexes for recovery logs
CREATE INDEX IF NOT EXISTS idx_backup_recovery_logs_message_id ON public.backup_recovery_logs(message_id);
CREATE INDEX IF NOT EXISTS idx_backup_recovery_logs_timestamp ON public.backup_recovery_logs(recovery_timestamp);
CREATE INDEX IF NOT EXISTS idx_backup_recovery_logs_successful ON public.backup_recovery_logs(recovery_successful);
CREATE INDEX IF NOT EXISTS idx_backup_recovery_logs_source ON public.backup_recovery_logs(recovery_source);

-- Create backup retention policies table
CREATE TABLE IF NOT EXISTS public.backup_retention_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    consultation_type TEXT NOT NULL UNIQUE,
    retention_days INTEGER NOT NULL,
    archive_after_days INTEGER NOT NULL,
    secure_delete_required BOOLEAN NOT NULL DEFAULT true,
    audit_log_required BOOLEAN NOT NULL DEFAULT true,
    cross_region_replication BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Insert default retention policies
INSERT INTO public.backup_retention_policies (
    consultation_type, 
    retention_days, 
    archive_after_days, 
    secure_delete_required, 
    audit_log_required,
    cross_region_replication
) VALUES 
    ('emergency', 2555, 365, true, true, true),      -- 7 years for emergency records
    ('general', 1825, 365, true, true, false),       -- 5 years for general consultations
    ('mental_health', 2555, 365, true, true, true),  -- 7 years for mental health records
    ('pediatric', 6570, 365, true, true, true)       -- 18 years for pediatric records
ON CONFLICT (consultation_type) DO NOTHING;

-- Create backup schedule tracking table
CREATE TABLE IF NOT EXISTS public.backup_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT,
    last_incremental_backup TIMESTAMPTZ,
    last_full_backup TIMESTAMPTZ,
    next_incremental_backup TIMESTAMPTZ,
    next_full_backup TIMESTAMPTZ,
    backup_count INTEGER DEFAULT 0,
    failed_backup_count INTEGER DEFAULT 0,
    last_backup_error TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for backup schedules
CREATE INDEX IF NOT EXISTS idx_backup_schedules_user_id ON public.backup_schedules(user_id);
CREATE INDEX IF NOT EXISTS idx_backup_schedules_session_id ON public.backup_schedules(session_id);
CREATE INDEX IF NOT EXISTS idx_backup_schedules_next_incremental ON public.backup_schedules(next_incremental_backup);
CREATE INDEX IF NOT EXISTS idx_backup_schedules_next_full ON public.backup_schedules(next_full_backup);

-- Create backup statistics view
CREATE OR REPLACE VIEW public.backup_statistics AS
SELECT 
    u.id as user_id,
    u.email,
    COUNT(abm.backup_id) as total_backups,
    COUNT(CASE WHEN abm.backup_type = 'full' THEN 1 END) as full_backups,
    COUNT(CASE WHEN abm.backup_type = 'incremental' THEN 1 END) as incremental_backups,
    COUNT(CASE WHEN abm.location = 'local' THEN 1 END) as local_backups,
    COUNT(CASE WHEN abm.location = 'cloud' THEN 1 END) as cloud_backups,
    COUNT(CASE WHEN abm.location = 'cross_region' THEN 1 END) as cross_region_backups,
    SUM(abm.size) as total_backup_size,
    MAX(abm.created_at) as last_backup_time,
    COUNT(CASE WHEN bvl.verification_result = true THEN 1 END) as verified_backups,
    COUNT(CASE WHEN bvl.verification_result = false THEN 1 END) as failed_verifications
FROM auth.users u
LEFT JOIN public.audio_backup_metadata abm ON u.id = abm.user_id
LEFT JOIN public.backup_verification_logs bvl ON abm.backup_id = bvl.backup_id
GROUP BY u.id, u.email;

-- Create function to automatically update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_audio_backup_metadata_updated_at 
    BEFORE UPDATE ON public.audio_backup_metadata 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_backup_retention_policies_updated_at 
    BEFORE UPDATE ON public.backup_retention_policies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_backup_schedules_updated_at 
    BEFORE UPDATE ON public.backup_schedules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean up expired backups
CREATE OR REPLACE FUNCTION cleanup_expired_backups()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    backup_record RECORD;
BEGIN
    -- Get expired backups
    FOR backup_record IN 
        SELECT backup_id, cloud_path 
        FROM public.audio_backup_metadata 
        WHERE retention_expiry < CURRENT_TIMESTAMP
    LOOP
        -- Log the deletion
        INSERT INTO public.audit_logs (
            event_type,
            resource_type,
            resource_id,
            action,
            details
        ) VALUES (
            'backup_deleted',
            'audio_backup',
            backup_record.backup_id,
            'automated_cleanup',
            jsonb_build_object(
                'reason', 'retention_policy_expired',
                'cloud_path', backup_record.cloud_path,
                'deleted_at', CURRENT_TIMESTAMP
            )
        );

        -- Delete the backup metadata (cloud storage cleanup handled by application)
        DELETE FROM public.audio_backup_metadata 
        WHERE backup_id = backup_record.backup_id;
        
        deleted_count := deleted_count + 1;
    END LOOP;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to verify backup integrity
CREATE OR REPLACE FUNCTION verify_backup_integrity(
    p_backup_id TEXT,
    p_current_checksum TEXT,
    p_current_size BIGINT,
    p_encryption_valid BOOLEAN,
    p_metadata_valid BOOLEAN
)
RETURNS BOOLEAN AS $$
DECLARE
    backup_record RECORD;
    verification_result BOOLEAN := false;
    checksum_match BOOLEAN := false;
    size_match BOOLEAN := false;
BEGIN
    -- Get backup metadata
    SELECT * INTO backup_record 
    FROM public.audio_backup_metadata 
    WHERE backup_id = p_backup_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Backup not found: %', p_backup_id;
    END IF;

    -- Perform integrity checks
    checksum_match := (backup_record.checksum = p_current_checksum);
    size_match := (ABS(backup_record.size - p_current_size) < 100); -- Allow small variance
    
    verification_result := checksum_match AND size_match AND p_encryption_valid AND p_metadata_valid;

    -- Log verification result
    INSERT INTO public.backup_verification_logs (
        backup_id,
        verification_result,
        checksum_match,
        size_match,
        encryption_valid,
        metadata_valid
    ) VALUES (
        p_backup_id,
        verification_result,
        checksum_match,
        size_match,
        p_encryption_valid,
        p_metadata_valid
    );

    RETURN verification_result;
END;
$$ LANGUAGE plpgsql;

-- Create RLS policies for backup tables

-- Enable RLS on backup tables
ALTER TABLE public.audio_backup_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_verification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_recovery_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_schedules ENABLE ROW LEVEL SECURITY;

-- Backup metadata policies
CREATE POLICY "Users can view their own backup metadata" ON public.audio_backup_metadata
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own backup metadata" ON public.audio_backup_metadata
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own backup metadata" ON public.audio_backup_metadata
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own backup metadata" ON public.audio_backup_metadata
    FOR DELETE USING (auth.uid() = user_id);

-- Backup verification logs policies
CREATE POLICY "Users can view verification logs for their backups" ON public.backup_verification_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.audio_backup_metadata abm 
            WHERE abm.backup_id = backup_verification_logs.backup_id 
            AND abm.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert verification logs" ON public.backup_verification_logs
    FOR INSERT WITH CHECK (true);

-- Backup recovery logs policies
CREATE POLICY "Users can view their own recovery logs" ON public.backup_recovery_logs
    FOR SELECT USING (auth.uid() = initiated_by);

CREATE POLICY "System can insert recovery logs" ON public.backup_recovery_logs
    FOR INSERT WITH CHECK (true);

-- Backup schedules policies
CREATE POLICY "Users can view their own backup schedules" ON public.backup_schedules
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own backup schedules" ON public.backup_schedules
    FOR ALL USING (auth.uid() = user_id);

-- Retention policies are read-only for users
CREATE POLICY "Users can view retention policies" ON public.backup_retention_policies
    FOR SELECT USING (true);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.audio_backup_metadata TO authenticated;
GRANT SELECT, INSERT ON public.backup_verification_logs TO authenticated;
GRANT SELECT, INSERT ON public.backup_recovery_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.backup_schedules TO authenticated;
GRANT SELECT ON public.backup_retention_policies TO authenticated;
GRANT SELECT ON public.backup_statistics TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION cleanup_expired_backups() TO service_role;
GRANT EXECUTE ON FUNCTION verify_backup_integrity(TEXT, TEXT, BIGINT, BOOLEAN, BOOLEAN) TO authenticated;

-- Create storage bucket for backup files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'voice-message-backups',
    'voice-message-backups',
    false,
    104857600, -- 100MB limit
    ARRAY['application/json']::text[]
) ON CONFLICT (id) DO NOTHING;

-- Create storage policies for backup bucket
CREATE POLICY "Users can upload their own backup files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'voice-message-backups' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own backup files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'voice-message-backups' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own backup files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'voice-message-backups' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Add comments for documentation
COMMENT ON TABLE public.audio_backup_metadata IS 'Metadata for encrypted audio message backups with HIPAA-compliant retention';
COMMENT ON TABLE public.backup_verification_logs IS 'Logs of backup integrity verification attempts';
COMMENT ON TABLE public.backup_recovery_logs IS 'Logs of backup recovery operations for audit trail';
COMMENT ON TABLE public.backup_retention_policies IS 'Configurable retention policies for different consultation types';
COMMENT ON TABLE public.backup_schedules IS 'Tracking of backup schedules per user/session';
COMMENT ON VIEW public.backup_statistics IS 'Aggregated backup statistics per user for monitoring';
COMMENT ON FUNCTION cleanup_expired_backups() IS 'Automated cleanup of expired backups based on retention policies';
COMMENT ON FUNCTION verify_backup_integrity(TEXT, TEXT, BIGINT, BOOLEAN, BOOLEAN) IS 'Verify backup integrity and log results';
