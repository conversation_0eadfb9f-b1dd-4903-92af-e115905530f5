/**
 * CIRCUIT BREAKER MONITORING DASHBOARD
 * 
 * Real-time monitoring dashboard for circuit breaker status across all services.
 * Provides visibility into system health and failure patterns.
 * 
 * FEATURES:
 * - Real-time circuit breaker status monitoring
 * - Service health visualization
 * - Failure rate tracking
 * - Manual circuit breaker controls
 * - Performance metrics display
 * - Emergency override capabilities
 */

import React, { useState, useEffect } from 'react';
import { circuitBreakerService } from '../services/CircuitBreakerService';
import { supabaseCircuitBreaker } from '../utils/supabaseCircuitBreaker';

const CircuitBreakerDashboard = () => {
  const [metrics, setMetrics] = useState([]);
  const [statusSummary, setStatusSummary] = useState({
    total: 0,
    closed: 0,
    open: 0,
    halfOpen: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Update metrics every 5 seconds
  useEffect(() => {
    const updateMetrics = () => {
      try {
        const allMetrics = circuitBreakerService.getAllMetrics();
        const supabaseMetrics = supabaseCircuitBreaker.getMetrics();
        
        // Combine all metrics
        const combinedMetrics = [...allMetrics, supabaseMetrics];
        
        setMetrics(combinedMetrics);
        setStatusSummary(circuitBreakerService.getStatusSummary());
        setLastUpdate(new Date());
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to update circuit breaker metrics:', error);
      }
    };

    // Initial update
    updateMetrics();

    // Set up interval
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  const getStateColor = (state) => {
    switch (state) {
      case 'closed': return 'text-green-600 bg-green-100';
      case 'open': return 'text-red-600 bg-red-100';
      case 'half-open': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStateIcon = (state) => {
    switch (state) {
      case 'closed': return '✅';
      case 'open': return '❌';
      case 'half-open': return '⚠️';
      default: return '❓';
    }
  };

  const handleReset = (serviceName) => {
    try {
      if (serviceName === 'supabase') {
        supabaseCircuitBreaker.reset();
      } else {
        const circuitBreaker = circuitBreakerService.getCircuitBreaker(serviceName);
        circuitBreaker.reset();
      }
      console.log(`Circuit breaker reset for ${serviceName}`);
    } catch (error) {
      console.error(`Failed to reset circuit breaker for ${serviceName}:`, error);
    }
  };

  const handleForceState = (serviceName, state) => {
    try {
      if (serviceName === 'supabase') {
        supabaseCircuitBreaker.forceState(state);
      } else {
        const circuitBreaker = circuitBreakerService.getCircuitBreaker(serviceName);
        circuitBreaker.forceState(state);
      }
      console.log(`Circuit breaker for ${serviceName} forced to ${state}`);
    } catch (error) {
      console.error(`Failed to force circuit breaker state for ${serviceName}:`, error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Circuit Breaker Dashboard</h2>
        <div className="text-sm text-gray-500">
          Last updated: {lastUpdate.toLocaleTimeString()}
        </div>
      </div>

      {/* Status Summary */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{statusSummary.total}</div>
          <div className="text-sm text-blue-800">Total Services</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{statusSummary.closed}</div>
          <div className="text-sm text-green-800">Healthy (Closed)</div>
        </div>
        <div className="bg-red-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{statusSummary.open}</div>
          <div className="text-sm text-red-800">Failed (Open)</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">{statusSummary.halfOpen}</div>
          <div className="text-sm text-yellow-800">Recovering</div>
        </div>
      </div>

      {/* Service Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800">Service Details</h3>
        
        {metrics.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No circuit breaker metrics available
          </div>
        ) : (
          <div className="grid gap-4">
            {metrics.map((metric) => (
              <div key={metric.name} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getStateIcon(metric.state)}</span>
                    <div>
                      <h4 className="font-semibold text-gray-800">{metric.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStateColor(metric.state)}`}>
                        {metric.state.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleReset(metric.name)}
                      className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                    >
                      Reset
                    </button>
                    <select
                      onChange={(e) => handleForceState(metric.name, e.target.value)}
                      className="px-2 py-1 text-xs border rounded"
                      defaultValue=""
                    >
                      <option value="" disabled>Force State</option>
                      <option value="closed">Closed</option>
                      <option value="open">Open</option>
                      <option value="half-open">Half-Open</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Success Rate</div>
                    <div className="font-semibold text-green-600">
                      {metric.successRate.toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">Failure Rate</div>
                    <div className="font-semibold text-red-600">
                      {metric.failureRate.toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">Avg Response</div>
                    <div className="font-semibold text-blue-600">
                      {metric.averageResponseTime.toFixed(0)}ms
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">Total Requests</div>
                    <div className="font-semibold text-gray-800">
                      {metric.totalRequests}
                    </div>
                  </div>
                </div>

                {metric.state === 'open' && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                    <div className="text-red-800 text-sm">
                      ⚠️ Circuit breaker is open. Service calls are being blocked to prevent cascading failures.
                    </div>
                  </div>
                )}

                {metric.state === 'half-open' && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <div className="text-yellow-800 text-sm">
                      🔄 Circuit breaker is in recovery mode. Limited requests are being allowed to test service health.
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Emergency Controls */}
      <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Emergency Controls</h3>
        <div className="flex space-x-4">
          <button
            onClick={() => circuitBreakerService.resetAll()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reset All Circuit Breakers
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Refresh Dashboard
          </button>
        </div>
        <div className="text-sm text-red-700 mt-2">
          ⚠️ Use emergency controls only when necessary. Resetting circuit breakers during active failures may cause system instability.
        </div>
      </div>
    </div>
  );
};

export default CircuitBreakerDashboard;
