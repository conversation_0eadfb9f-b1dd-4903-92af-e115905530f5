import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CustomReportBuilder = ({ onGenerateReport }) => {
  const [reportConfig, setReportConfig] = useState({
    name: '',
    dateRange: { start: '', end: '' },
    metrics: [],
    segments: [],
    filters: {},
    format: 'pdf',
    schedule: 'none'
  });

  const availableMetrics = [
    { id: 'consultations', label: 'Total Consultations', category: 'Usage' },
    { id: 'users', label: 'Active Users', category: 'Users' },
    { id: 'satisfaction', label: 'User Satisfaction', category: 'Quality' },
    { id: 'response_time', label: 'Response Time', category: 'Performance' },
    { id: 'completion_rate', label: 'Completion Rate', category: 'Quality' },
    { id: 'specialties', label: 'Medical Specialties', category: 'Medical' },
    { id: 'demographics', label: 'User Demographics', category: 'Users' },
    { id: 'retention', label: 'User Retention', category: 'Users' }
  ];

  const availableSegments = [
    { id: 'age_group', label: 'Age Group' },
    { id: 'specialty', label: 'Medical Specialty' },
    { id: 'subscription', label: 'Subscription Type' },
    { id: 'location', label: 'Geographic Location' },
    { id: 'device', label: 'Device Type' },
    { id: 'time_of_day', label: 'Time of Day' }
  ];

  const scheduleOptions = [
    { value: 'none', label: 'One-time Report' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' }
  ];

  const formatOptions = [
    { value: 'pdf', label: 'PDF', icon: 'FileText' },
    { value: 'excel', label: 'Excel', icon: 'Table' },
    { value: 'csv', label: 'CSV', icon: 'Download' },
    { value: 'json', label: 'JSON', icon: 'Code' }
  ];

  const handleMetricToggle = (metricId) => {
    setReportConfig(prev => ({
      ...prev,
      metrics: prev.metrics.includes(metricId)
        ? prev.metrics.filter(id => id !== metricId)
        : [...prev.metrics, metricId]
    }));
  };

  const handleSegmentToggle = (segmentId) => {
    setReportConfig(prev => ({
      ...prev,
      segments: prev.segments.includes(segmentId)
        ? prev.segments.filter(id => id !== segmentId)
        : [...prev.segments, segmentId]
    }));
  };

  const handleGenerateReport = () => {
    onGenerateReport?.(reportConfig);
  };

  const getMetricsByCategory = () => {
    const categories = {};
    availableMetrics.forEach(metric => {
      if (!categories[metric.category]) {
        categories[metric.category] = [];
      }
      categories[metric.category].push(metric);
    });
    return categories;
  };

  const isConfigValid = () => {
    return reportConfig.name.trim() && 
           reportConfig.metrics.length > 0 && 
           reportConfig.dateRange.start && 
           reportConfig.dateRange.end;
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Custom Report Builder
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Create tailored analytics reports for stakeholders
            </p>
          </div>
          <Button
            variant="primary"
            onClick={handleGenerateReport}
            disabled={!isConfigValid()}
            iconName="FileBarChart"
          >
            Generate Report
          </Button>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Report Name */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            Report Name
          </label>
          <input
            type="text"
            value={reportConfig.name}
            onChange={(e) => setReportConfig(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter report name..."
            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            Date Range
          </label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-text-secondary mb-1">Start Date</label>
              <input
                type="date"
                value={reportConfig.dateRange.start}
                onChange={(e) => setReportConfig(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, start: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-xs text-text-secondary mb-1">End Date</label>
              <input
                type="date"
                value={reportConfig.dateRange.end}
                onChange={(e) => setReportConfig(prev => ({
                  ...prev,
                  dateRange: { ...prev.dateRange, end: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>

        {/* Metrics Selection */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-3">
            Select Metrics
          </label>
          <div className="space-y-4">
            {Object.entries(getMetricsByCategory()).map(([category, metrics]) => (
              <div key={category}>
                <h4 className="text-xs font-medium text-text-secondary uppercase tracking-wider mb-2">
                  {category}
                </h4>
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
                  {metrics.map((metric) => (
                    <label
                      key={metric.id}
                      className="flex items-center p-3 border border-border rounded-lg cursor-pointer hover:bg-secondary-50 transition-fast"
                    >
                      <input
                        type="checkbox"
                        checked={reportConfig.metrics.includes(metric.id)}
                        onChange={() => handleMetricToggle(metric.id)}
                        className="rounded border-border text-primary-600 focus:ring-primary-500 mr-3"
                      />
                      <span className="text-sm text-text-primary">{metric.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Segmentation */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-3">
            Segmentation (Optional)
          </label>
          <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
            {availableSegments.map((segment) => (
              <label
                key={segment.id}
                className="flex items-center p-3 border border-border rounded-lg cursor-pointer hover:bg-secondary-50 transition-fast"
              >
                <input
                  type="checkbox"
                  checked={reportConfig.segments.includes(segment.id)}
                  onChange={() => handleSegmentToggle(segment.id)}
                  className="rounded border-border text-primary-600 focus:ring-primary-500 mr-3"
                />
                <span className="text-sm text-text-primary">{segment.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Export Format & Schedule */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Export Format
            </label>
            <div className="grid grid-cols-2 gap-2">
              {formatOptions.map((format) => (
                <label
                  key={format.value}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-fast ${
                    reportConfig.format === format.value
                      ? 'border-primary-500 bg-primary-50' :'border-border hover:bg-secondary-50'
                  }`}
                >
                  <input
                    type="radio"
                    name="format"
                    value={format.value}
                    checked={reportConfig.format === format.value}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, format: e.target.value }))}
                    className="sr-only"
                  />
                  <Icon name={format.icon} size={16} className="mr-2" />
                  <span className="text-sm">{format.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Schedule
            </label>
            <select
              value={reportConfig.schedule}
              onChange={(e) => setReportConfig(prev => ({ ...prev, schedule: e.target.value }))}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {scheduleOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {reportConfig.schedule !== 'none' && (
              <p className="text-xs text-text-secondary mt-2">
                Report will be automatically generated and delivered via email
              </p>
            )}
          </div>
        </div>

        {/* Report Summary */}
        <div className="bg-secondary-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-text-primary mb-2">Report Summary</h4>
          <div className="space-y-1 text-sm text-text-secondary">
            <p><strong>Name:</strong> {reportConfig.name || 'Untitled Report'}</p>
            <p><strong>Metrics:</strong> {reportConfig.metrics.length} selected</p>
            <p><strong>Segments:</strong> {reportConfig.segments.length} selected</p>
            <p><strong>Format:</strong> {formatOptions.find(f => f.value === reportConfig.format)?.label}</p>
            <p><strong>Schedule:</strong> {scheduleOptions.find(s => s.value === reportConfig.schedule)?.label}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomReportBuilder;