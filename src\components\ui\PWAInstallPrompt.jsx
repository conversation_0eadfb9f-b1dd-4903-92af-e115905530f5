import React, { useState, useEffect } from 'react';
import { X, Download, Smartphone } from 'lucide-react';
import pwaService from '../../utils/pwaService';

const PWAInstallPrompt = () => {
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    const unsubscribe = pwaService.subscribe((event, data) => {
      switch (event) {
        case 'installable':
          setShowPrompt(true);
          break;
        case 'installed':
          setIsInstalled(true);
          setShowPrompt(false);
          break;
      }
    });

    // Check if already installable
    if (pwaService.canInstall()) {
      setShowPrompt(true);
    }

    return unsubscribe;
  }, []);

  const handleInstall = async () => {
    const installed = await pwaService.showInstallPrompt();
    if (installed) {
      setShowPrompt(false);
      setIsInstalled(true);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    // Store dismissal in localStorage to not show again for a while
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // Check if user previously dismissed the prompt recently
  useEffect(() => {
    const dismissed = localStorage.getItem('pwa-install-dismissed');
    if (dismissed) {
      const dismissedTime = parseInt(dismissed);
      const daysSinceDismissal = (Date.now() - dismissedTime) / (1000 * 60 * 60 * 24);
      
      // Don't show again for 7 days after dismissal
      if (daysSinceDismissal < 7) {
        setShowPrompt(false);
      }
    }
  }, []);

  if (!showPrompt || isInstalled) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-2xl">
      <div className="max-w-4xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="bg-white bg-opacity-20 p-2 rounded-lg">
            <Smartphone className="w-6 h-6" />
          </div>
          
          <div>
            <h3 className="font-semibold text-lg">Install VoiceHealth AI</h3>
            <p className="text-sm text-blue-100">
              Get the full app experience with offline capabilities and faster access
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={handleInstall}
            className="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Install</span>
          </button>
          
          <button
            onClick={handleDismiss}
            className="text-white hover:text-blue-200 transition-colors p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Features list */}
      <div className="max-w-4xl mx-auto mt-3 flex flex-wrap gap-4 text-xs text-blue-100">
        <div className="flex items-center space-x-1">
          <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
          <span>Works offline</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
          <span>Faster loading</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
          <span>Push notifications</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
          <span>Home screen access</span>
        </div>
      </div>
    </div>
  );
};

export default PWAInstallPrompt;