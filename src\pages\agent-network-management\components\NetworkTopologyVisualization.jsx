import React from 'react';
import Icon from '../../../components/AppIcon';

const NetworkTopologyVisualization = ({ agents = [], networkStatus = 'healthy' }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'border-success-500 bg-success-100';
      case 'busy': return 'border-warning-500 bg-warning-100';
      case 'offline': return 'border-error-500 bg-error-100';
      default: return 'border-text-secondary bg-surface';
    }
  };

  const getNetworkStatusColor = () => {
    switch (networkStatus) {
      case 'healthy': return 'text-success-500';
      case 'emergency': return 'text-error-500';
      case 'failover': return 'text-warning-500';
      default: return 'text-text-secondary';
    }
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal p-6">
      {/* Network Status Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${networkStatus === 'healthy' ? 'bg-success-100' : 
                         networkStatus === 'emergency' ? 'bg-error-100' : 'bg-warning-100'}`}>
            <Icon 
              name="Network" 
              size={20} 
              className={getNetworkStatusColor()} 
            />
          </div>
          <div>
            <h3 className="font-semibold text-text-primary">Network Topology</h3>
            <p className="text-sm text-text-secondary">
              {agents?.length || 0} active agents with referral pathways
            </p>
          </div>
        </div>
        
        <div className="hidden lg:flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full border-2 border-success-500 bg-success-100"></div>
            <span className="text-text-secondary">Active</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full border-2 border-warning-500 bg-warning-100"></div>
            <span className="text-text-secondary">High Load</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full border-2 border-error-500 bg-error-100"></div>
            <span className="text-text-secondary">Offline</span>
          </div>
        </div>
      </div>

      {/* Desktop Topology View */}
      <div className="hidden lg:block">
        <div className="relative">
          {/* Central Hub */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-20 h-20 rounded-full bg-primary-100 border-2 border-primary-500 flex items-center justify-center">
              <Icon name="Network" size={32} className="text-primary-500" />
            </div>
            <p className="text-center text-sm font-medium text-text-primary mt-2">
              AI Network Hub
            </p>
          </div>

          {/* Agent Nodes positioned in a circle */}
          <div className="w-full h-96 relative">
            {agents?.map((agent, index) => {
              const angle = (360 / agents.length) * index;
              const radius = 140;
              const x = Math.cos((angle - 90) * Math.PI / 180) * radius;
              const y = Math.sin((angle - 90) * Math.PI / 180) * radius;
              
              return (
                <div
                  key={agent?.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2"
                  style={{
                    left: `calc(50% + ${x}px)`,
                    top: `calc(50% + ${y}px)`
                  }}
                >
                  {/* Connection Line */}
                  <svg
                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                    style={{
                      width: `${radius + 40}px`,
                      height: `${radius + 40}px`,
                      transform: `translate(-50%, -50%) rotate(${angle + 90}deg)`
                    }}
                  >
                    <line
                      x1={radius + 20}
                      y1={radius + 20}
                      x2={20}
                      y2={radius + 20}
                      stroke="#e5e7eb"
                      strokeWidth="2"
                      strokeDasharray="4 4"
                    />
                  </svg>

                  {/* Agent Node */}
                  <div className={`w-16 h-16 rounded-full border-2 ${getStatusColor(agent?.status)} 
                                  flex items-center justify-center cursor-pointer hover:scale-110 transition-transform`}>
                    <span className="text-2xl">{agent?.avatar}</span>
                  </div>
                  
                  {/* Agent Label */}
                  <div className="text-center mt-2 w-20">
                    <p className="text-xs font-medium text-text-primary truncate">
                      {agent?.shortName}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {agent?.currentLoad}% load
                    </p>
                  </div>

                  {/* Load Indicator */}
                  {agent?.currentLoad > 75 && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-warning-500 rounded-full 
                                   flex items-center justify-center animate-pulse">
                      <Icon name="AlertTriangle" size={10} className="text-white" />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Mobile Topology View */}
      <div className="lg:hidden">
        <div className="space-y-4">
          {/* Central Hub */}
          <div className="text-center py-4">
            <div className="w-16 h-16 rounded-full bg-primary-100 border-2 border-primary-500 flex items-center justify-center mx-auto mb-2">
              <Icon name="Network" size={24} className="text-primary-500" />
            </div>
            <p className="text-sm font-medium text-text-primary">AI Network Hub</p>
          </div>

          {/* Agent Connections */}
          <div className="space-y-3">
            {agents?.map((agent) => (
              <div key={agent?.id} className="flex items-center gap-3 p-3 bg-background rounded-lg">
                <div className={`w-12 h-12 rounded-full border-2 ${getStatusColor(agent?.status)} 
                               flex items-center justify-center`}>
                  <span className="text-lg">{agent?.avatar}</span>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-text-primary truncate">{agent?.shortName}</p>
                    {agent?.currentLoad > 75 && (
                      <Icon name="AlertTriangle" size={14} className="text-warning-500" />
                    )}
                  </div>
                  <p className="text-sm text-text-secondary">{agent?.currentLoad}% load</p>
                </div>

                <div className="text-right">
                  <p className="text-sm font-medium text-text-primary">{agent?.consultationsToday}</p>
                  <p className="text-xs text-text-secondary">consultations</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Referral Pathways Legend */}
      <div className="mt-6 pt-4 border-t border-border">
        <h4 className="text-sm font-medium text-text-primary mb-3">Common Referral Pathways</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-text-secondary">
          <div className="flex items-center gap-2">
            <Icon name="ArrowRight" size={14} />
            <span>GP → Mental Health (anxiety cases)</span>
          </div>
          <div className="flex items-center gap-2">
            <Icon name="ArrowRight" size={14} />
            <span>GP → Dermatologist (skin conditions)</span>
          </div>
          <div className="flex items-center gap-2">
            <Icon name="ArrowRight" size={14} />
            <span>Paediatrician → Dietician (child nutrition)</span>
          </div>
          <div className="flex items-center gap-2">
            <Icon name="ArrowRight" size={14} />
            <span>GP → Paediatrician (child health)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkTopologyVisualization;