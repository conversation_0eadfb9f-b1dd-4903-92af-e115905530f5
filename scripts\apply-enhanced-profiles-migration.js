/**
 * Apply Enhanced User Profiles Migration
 * 
 * This script applies the enhanced user profiles migration to add missing
 * demographic fields for improved context quality in medical consultations.
 * 
 * Usage: node scripts/apply-enhanced-profiles-migration.js
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - SUPABASE_URL or VITE_SUPABASE_URL');
  console.error('   - VITE_SUPABASE_ANON_KEY');
  console.error('\n💡 For this migration, please apply the SQL manually in Supabase Dashboard:');
  console.error('   1. Go to your Supabase project dashboard');
  console.error('   2. Navigate to SQL Editor');
  console.error('   3. Copy and paste the contents of supabase/migrations/20241223000000_enhanced_user_profiles.sql');
  console.error('   4. Execute the migration');
  process.exit(1);
}

// Initialize Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function applyEnhancedProfilesMigration() {
  console.log('🚀 Enhanced User Profiles Migration Guide');
  console.log('==========================================\n');

  try {
    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'supabase/migrations/20241223000000_enhanced_user_profiles.sql');

    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded successfully');

    console.log('\n📋 MANUAL MIGRATION REQUIRED');
    console.log('============================');
    console.log('Due to permission limitations, please apply this migration manually:');
    console.log('\n1. 🌐 Go to your Supabase project dashboard:');
    console.log('   https://supabase.com/dashboard/project/vbjxfrfwdbebrwdqaqne');
    console.log('\n2. 📝 Navigate to SQL Editor');
    console.log('\n3. 📋 Copy the migration SQL from:');
    console.log(`   ${migrationPath}`);
    console.log('\n4. ▶️ Execute the migration in the SQL Editor');
    console.log('\n5. ✅ Verify the migration completed successfully');

    console.log('\n📄 Migration Preview:');
    console.log('=====================');
    console.log(migrationSQL.substring(0, 500) + '...\n');

    // Try to verify if migration might already be applied
    console.log('🔍 Checking current schema...');
    
    // Verify the new columns exist
    console.log('🔍 Verifying new columns...');
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'user_profiles')
      .eq('table_schema', 'public')
      .in('column_name', ['country', 'city', 'occupation', 'insurance_status', 'emergency_contact']);
    
    if (columnsError) {
      console.warn('⚠️ Could not verify columns:', columnsError.message);
    } else {
      const addedColumns = columns.map(col => col.column_name);
      console.log('✅ Verified new columns:', addedColumns);
      
      if (addedColumns.length === 5) {
        console.log('🎉 All expected columns added successfully!');
      } else {
        console.warn('⚠️ Some columns may be missing. Expected 5, found:', addedColumns.length);
      }
    }
    
    // Test the new functions
    console.log('🧪 Testing new functions...');
    
    // Test profile completion calculation
    const { data: demoUser, error: demoError } = await supabase
      .from('user_profiles')
      .select('id, profile_completion_percentage')
      .eq('email', '<EMAIL>')
      .single();
    
    if (demoError) {
      console.warn('⚠️ Could not find demo user for testing:', demoError.message);
    } else {
      console.log('✅ Demo user profile completion:', demoUser.profile_completion_percentage + '%');
    }
    
    // Test geographic context function
    if (demoUser) {
      const { data: geoContext, error: geoError } = await supabase
        .rpc('get_user_geographic_context', { user_id: demoUser.id });
      
      if (geoError) {
        console.warn('⚠️ Geographic context function test failed:', geoError.message);
      } else {
        console.log('✅ Geographic context function working:', {
          country: geoContext[0]?.user_country,
          city: geoContext[0]?.user_city,
          hasRegionalData: !!geoContext[0]?.regional_data
        });
      }
    }
    
    console.log('\n🎉 Enhanced User Profiles Migration completed successfully!');
    console.log('\n📊 Summary of changes:');
    console.log('   ✅ Added 5 new demographic fields to user_profiles');
    console.log('   ✅ Created indexes for performance optimization');
    console.log('   ✅ Updated RLS policies for new fields');
    console.log('   ✅ Added profile completion calculation function');
    console.log('   ✅ Added geographic context retrieval function');
    console.log('   ✅ Added automatic profile completion tracking');
    console.log('   ✅ Added HIPAA-compliant audit logging');
    console.log('   ✅ Updated demo user with sample data');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Check your Supabase connection');
    console.error('   2. Verify you have admin privileges');
    console.error('   3. Ensure the migration file exists');
    console.error('   4. Check for any conflicting schema changes');
    process.exit(1);
  }
}

// Run the migration
applyEnhancedProfilesMigration();
