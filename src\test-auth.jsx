import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';

function TestComponent() {
  const { user, loading } = useAuth();
  
  return (
    <div>
      <h1>Auth Test</h1>
      <p>Loading: {loading ? 'Yes' : 'No'}</p>
      <p>User: {user ? user.email : 'Not logged in'}</p>
    </div>
  );
}

function TestApp() {
  return (
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
}

export default TestApp;
