-- Enhanced User Profiles Migration (Fixed Version)
-- Adds missing demographic fields to improve context quality for medical consultations
-- Date: 2024-12-23
-- Purpose: Complete user profile schema for enhanced patient context
-- Fixed: Removed unsupported "IF NOT EXISTS" from CREATE POLICY statements

-- Add missing demographic fields to user_profiles table
ALTER TABLE public.user_profiles
ADD COLUMN IF NOT EXISTS country TEXT,
ADD COLUMN IF NOT EXISTS city TEXT,
ADD COLUMN IF NOT EXISTS occupation TEXT,
ADD COLUMN IF NOT EXISTS insurance_status TEXT CHECK (insurance_status IN ('insured', 'uninsured', 'government', 'private', 'unknown')),
ADD COLUMN IF NOT EXISTS emergency_contact JSONB DEFAULT '{}';

-- Add indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_user_profiles_country ON public.user_profiles(country);
CREATE INDEX IF NOT EXISTS idx_user_profiles_city ON public.user_profiles(city);
CREATE INDEX IF NOT EXISTS idx_user_profiles_occupation ON public.user_profiles(occupation);
CREATE INDEX IF NOT EXISTS idx_user_profiles_insurance_status ON public.user_profiles(insurance_status);

-- Add comments for documentation
COMMENT ON COLUMN public.user_profiles.country IS 'ISO country code (e.g., US, NG, KE, ZA, GH) for regional health context';
COMMENT ON COLUMN public.user_profiles.city IS 'City name for local health context and emergency services';
COMMENT ON COLUMN public.user_profiles.occupation IS 'Patient occupation for occupational health considerations';
COMMENT ON COLUMN public.user_profiles.insurance_status IS 'Insurance coverage status for treatment recommendations';
COMMENT ON COLUMN public.user_profiles.emergency_contact IS 'Emergency contact information in JSON format: {"name": "...", "phone": "...", "relationship": "..."}';

-- Update RLS policies to include new fields
-- Users can view and update their own profile including new fields
DROP POLICY IF EXISTS "users_own_profile" ON public.user_profiles;
CREATE POLICY "users_own_profile" ON public.user_profiles FOR ALL
USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Admins can view all profiles including new demographic data
DROP POLICY IF EXISTS "admins_view_all_profiles" ON public.user_profiles;
CREATE POLICY "admins_view_all_profiles" ON public.user_profiles FOR SELECT
USING (public.is_admin());

-- Healthcare providers can view relevant demographic data for consultations
DROP POLICY IF EXISTS "providers_view_demographic_data" ON public.user_profiles;
CREATE POLICY "providers_view_demographic_data" ON public.user_profiles FOR SELECT
USING (
  public.is_provider() OR 
  public.is_admin()
  -- Note: Consultation-based access commented out as tables may not exist yet
  -- Uncomment when consultation system is fully implemented
  /*
  OR EXISTS (
    SELECT 1 FROM public.consultation_sessions cs
    WHERE cs.patient_id = user_profiles.id
    AND cs.status IN ('active', 'in_progress')
    AND (
      cs.assigned_provider_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM public.session_agent_participation sap
        WHERE sap.session_id = cs.id
        AND sap.agent_id IN (
          SELECT id FROM public.ai_agents 
          WHERE created_by = auth.uid()
        )
      )
    )
  )
  */
);

-- Create function to calculate enhanced profile completion percentage
CREATE OR REPLACE FUNCTION public.calculate_profile_completion(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  completion_score INTEGER := 0;
  total_fields INTEGER := 12; -- Total number of profile fields
BEGIN
  -- Check each field and add to completion score
  SELECT 
    CASE WHEN full_name IS NOT NULL AND full_name != '' THEN 1 ELSE 0 END +
    CASE WHEN email IS NOT NULL AND email != '' THEN 1 ELSE 0 END +
    CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 ELSE 0 END +
    CASE WHEN date_of_birth IS NOT NULL THEN 1 ELSE 0 END +
    CASE WHEN gender IS NOT NULL AND gender != '' THEN 1 ELSE 0 END +
    CASE WHEN preferred_language IS NOT NULL AND preferred_language != '' THEN 1 ELSE 0 END +
    CASE WHEN country IS NOT NULL AND country != '' THEN 1 ELSE 0 END +
    CASE WHEN city IS NOT NULL AND city != '' THEN 1 ELSE 0 END +
    CASE WHEN occupation IS NOT NULL AND occupation != '' THEN 1 ELSE 0 END +
    CASE WHEN insurance_status IS NOT NULL AND insurance_status != '' THEN 1 ELSE 0 END +
    CASE WHEN emergency_contact IS NOT NULL AND emergency_contact != '{}' THEN 1 ELSE 0 END +
    CASE WHEN avatar_url IS NOT NULL AND avatar_url != '' THEN 1 ELSE 0 END
  INTO completion_score
  FROM public.user_profiles
  WHERE id = user_id;
  
  -- Return percentage
  RETURN ROUND((completion_score::FLOAT / total_fields::FLOAT) * 100);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update profile completion percentage
CREATE OR REPLACE FUNCTION public.update_profile_completion()
RETURNS TRIGGER AS $$
BEGIN
  NEW.profile_completion_percentage := public.calculate_profile_completion(NEW.id);
  NEW.updated_at := CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists and create new one
DROP TRIGGER IF EXISTS trigger_update_profile_completion ON public.user_profiles;
CREATE TRIGGER trigger_update_profile_completion
  BEFORE INSERT OR UPDATE ON public.user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_profile_completion();

-- Create function to get user's geographic context for regional health data
CREATE OR REPLACE FUNCTION public.get_user_geographic_context(user_id UUID)
RETURNS TABLE (
  user_country TEXT,
  user_city TEXT,
  regional_data JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.country,
    up.city,
    COALESCE(
      row_to_json(rhd)::jsonb,
      '{}'::jsonb
    ) as regional_data
  FROM public.user_profiles up
  LEFT JOIN public.regional_health_data rhd ON rhd.country_code = up.country
  WHERE up.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.calculate_profile_completion(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_geographic_context(UUID) TO authenticated;

-- Add audit logging for profile updates
CREATE OR REPLACE FUNCTION public.log_profile_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Only log if audit_logs table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'audit_logs' AND table_schema = 'public') THEN
    INSERT INTO public.audit_logs (
      event_type,
      action,
      resource_type,
      resource_id,
      user_id,
      severity,
      description,
      metadata,
      integrity_hash
    ) VALUES (
      'profile_update',
      TG_OP,
      'user_profile',
      NEW.id::TEXT,
      auth.uid(),
      'low',
      'User profile demographic data updated',
      jsonb_build_object(
        'updated_fields', CASE 
          WHEN TG_OP = 'UPDATE' THEN (
            SELECT jsonb_object_agg(key, value)
            FROM jsonb_each(to_jsonb(NEW))
            WHERE key IN ('country', 'city', 'occupation', 'insurance_status', 'emergency_contact')
            AND to_jsonb(NEW) ->> key IS DISTINCT FROM to_jsonb(OLD) ->> key
          )
          ELSE to_jsonb(NEW)
        END,
        'completion_percentage', NEW.profile_completion_percentage
      ),
      encode(sha256(concat(NEW.id::TEXT, NEW.updated_at::TEXT, auth.uid()::TEXT)::bytea), 'hex')
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit trigger for profile updates (only if audit_logs table exists)
DROP TRIGGER IF EXISTS trigger_audit_profile_update ON public.user_profiles;
CREATE TRIGGER trigger_audit_profile_update
  AFTER INSERT OR UPDATE ON public.user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.log_profile_update();

-- Test the new functionality
DO $$
DECLARE
  test_user_id UUID;
  completion_percentage INTEGER;
BEGIN
  -- Find a test user (demo user or first available user)
  SELECT id INTO test_user_id
  FROM public.user_profiles
  WHERE email LIKE '%demo%' OR email LIKE '%test%'
  LIMIT 1;
  
  -- If no demo user, get any user
  IF test_user_id IS NULL THEN
    SELECT id INTO test_user_id
    FROM public.user_profiles
    LIMIT 1;
  END IF;
  
  -- Test profile completion calculation if we have a user
  IF test_user_id IS NOT NULL THEN
    SELECT public.calculate_profile_completion(test_user_id) INTO completion_percentage;
    RAISE NOTICE 'Test user profile completion: %', completion_percentage;
    
    -- Update test user with sample demographic data
    UPDATE public.user_profiles
    SET 
      country = 'GH',
      city = 'Accra',
      occupation = 'Software Engineer',
      insurance_status = 'private',
      emergency_contact = '{"name": "Jane Doe", "phone": "+233-20-123-4567", "relationship": "Spouse"}'::jsonb
    WHERE id = test_user_id;
    
    RAISE NOTICE 'Updated test user with enhanced demographic data';
  ELSE
    RAISE NOTICE 'No users found for testing';
  END IF;
END $$;

-- Refresh RLS policies
NOTIFY pgrst, 'reload schema';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Enhanced User Profiles Migration completed successfully!';
  RAISE NOTICE 'Added fields: country, city, occupation, insurance_status, emergency_contact';
  RAISE NOTICE 'Created functions: calculate_profile_completion, get_user_geographic_context';
  RAISE NOTICE 'Updated RLS policies and added audit logging';
END $$;
