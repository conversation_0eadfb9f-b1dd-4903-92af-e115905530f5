import React from 'react';
import { Info } from 'lucide-react';

const HelpSection = ({ selectedLanguage = 'en' }) => {
  const getLocalizedText = (key) => {
    const texts = {
      guidance: {
        en: 'Select 3-5 areas you\'re most interested in - you can always change these later.',
        tw: 'Yi 3-5 mmeae a w\'ani gye ho kɛse - wobɛtumi asesa eyinom akyiri yi.',
        yo: 'Yan awọn agbegbe 3-5 ti o nifẹ si julọ - o le yi awọn wọnyi pada nigbagbogbo.',
        sw: 'Chagua maeneo 3-5 unayopenda zaidi - unaweza kubadilisha haya baadaye.',
        af: 'Kies 3-5 areas waarin jy die meeste belangstel - jy kan dit altyd later verander.'
      },
      tip: {
        en: 'Choose based on your current health priorities and interests',
        tw: 'Yi gyina w\'akwahosan nneɛma a ɛho hia ne w\'ani a w\'ani gye ho so',
        yo: 'Yan ni ibamu pẹlu awọn pataki ilera rẹ lọwọlọwọ ati awọn ifẹ',
        sw: 'Chagua kulingana na vipaumbele vyako vya afya na mapendeleo',
        af: 'Kies gebaseer op jou huidige gesondheidsprioriteite en belangstellings'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="bg-primary-50 rounded-xl p-4 space-y-3">
      <div className="flex items-start gap-3">
        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
          <Info size={16} className="text-primary" />
        </div>
        <div className="space-y-2">
          <p className="text-sm text-text-primary font-medium">
            {getLocalizedText('guidance')}
          </p>
          <p className="text-xs text-text-secondary">
            {getLocalizedText('tip')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default HelpSection;