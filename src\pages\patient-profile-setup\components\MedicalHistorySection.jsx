import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';
import Button from '../../../components/ui/Button';

const MedicalHistorySection = ({ 
  data = {}, 
  onUpdate = () => {}, 
  isExpanded = false, 
  onToggle = () => {},
  errors = {}
}) => {
  const [newCondition, setNewCondition] = useState('');
  const [newMedication, setNewMedication] = useState('');
  const [newAllergy, setNewAllergy] = useState('');

  const handleInputChange = (field, value) => {
    onUpdate({ ...data, [field]: value });
  };

  const addCondition = () => {
    if (newCondition.trim()) {
      const conditions = data.medicalConditions || [];
      handleInputChange('medicalConditions', [...conditions, {
        id: Date.now(),
        name: newCondition.trim(),
        severity: 'moderate',
        diagnosedDate: '',
        status: 'active'
      }]);
      setNewCondition('');
    }
  };

  const removeCondition = (id) => {
    const conditions = data.medicalConditions || [];
    handleInputChange('medicalConditions', conditions.filter(c => c.id !== id));
  };

  const updateCondition = (id, field, value) => {
    const conditions = data.medicalConditions || [];
    handleInputChange('medicalConditions', conditions.map(c => 
      c.id === id ? { ...c, [field]: value } : c
    ));
  };

  const addMedication = () => {
    if (newMedication.trim()) {
      const medications = data.currentMedications || [];
      handleInputChange('currentMedications', [...medications, {
        id: Date.now(),
        name: newMedication.trim(),
        dosage: '',
        frequency: '',
        prescribedBy: '',
        startDate: ''
      }]);
      setNewMedication('');
    }
  };

  const removeMedication = (id) => {
    const medications = data.currentMedications || [];
    handleInputChange('currentMedications', medications.filter(m => m.id !== id));
  };

  const updateMedication = (id, field, value) => {
    const medications = data.currentMedications || [];
    handleInputChange('currentMedications', medications.map(m => 
      m.id === id ? { ...m, [field]: value } : m
    ));
  };

  const addAllergy = () => {
    if (newAllergy.trim()) {
      const allergies = data.allergies || [];
      handleInputChange('allergies', [...allergies, {
        id: Date.now(),
        name: newAllergy.trim(),
        severity: 'mild',
        reaction: '',
        type: 'medication'
      }]);
      setNewAllergy('');
    }
  };

  const removeAllergy = (id) => {
    const allergies = data.allergies || [];
    handleInputChange('allergies', allergies.filter(a => a.id !== id));
  };

  const updateAllergy = (id, field, value) => {
    const allergies = data.allergies || [];
    handleInputChange('allergies', allergies.map(a => 
      a.id === id ? { ...a, [field]: value } : a
    ));
  };

  const commonConditions = [
    "Diabetes", "Hypertension", "Asthma", "Heart Disease", "Arthritis",
    "Depression", "Anxiety", "High Cholesterol", "Migraine", "COPD"
  ];

  const commonMedications = [
    "Aspirin", "Metformin", "Lisinopril", "Atorvastatin", "Omeprazole",
    "Levothyroxine", "Amlodipine", "Metoprolol", "Losartan", "Gabapentin"
  ];

  const commonAllergies = [
    "Penicillin", "Sulfa drugs", "Aspirin", "Ibuprofen", "Codeine",
    "Peanuts", "Shellfish", "Latex", "Pollen", "Dust mites"
  ];

  return (
    <div className="bg-surface border border-border rounded-xl shadow-minimal overflow-hidden">
      {/* Section Header */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-6 hover:bg-secondary-50 transition-fast"
      >
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-error-50 rounded-lg flex items-center justify-center">
            <Icon name="Heart" size={20} color="var(--color-error)" />
          </div>
          <div className="text-left">
            <h3 className="font-semibold text-text-primary font-heading">
              Medical History
            </h3>
            <p className="text-sm text-text-secondary font-caption">
              Conditions, medications, and allergies
            </p>
          </div>
        </div>
        <Icon 
          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
          size={20} 
          color="var(--color-text-secondary)" 
        />
      </button>

      {/* Section Content */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-border">
          <div className="space-y-8">
            {/* Medical Conditions */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="FileText" size={16} />
                <span>Medical Conditions</span>
              </h4>
              
              {/* Add New Condition */}
              <div className="flex space-x-2 mb-4">
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="Type condition name..."
                    value={newCondition}
                    onChange={(e) => setNewCondition(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addCondition()}
                  />
                </div>
                <Button
                  variant="primary"
                  onClick={addCondition}
                  iconName="Plus"
                  disabled={!newCondition.trim()}
                >
                  Add
                </Button>
              </div>

              {/* Common Conditions */}
              <div className="mb-4">
                <p className="text-sm text-text-secondary mb-2">Common conditions:</p>
                <div className="flex flex-wrap gap-2">
                  {commonConditions.map((condition) => (
                    <button
                      key={condition}
                      onClick={() => setNewCondition(condition)}
                      className="px-3 py-1 text-sm bg-secondary-50 hover:bg-secondary-100 text-text-secondary hover:text-text-primary rounded-full transition-fast"
                    >
                      {condition}
                    </button>
                  ))}
                </div>
              </div>

              {/* Conditions List */}
              <div className="space-y-3">
                {(data.medicalConditions || []).map((condition) => (
                  <div key={condition.id} className="p-4 bg-secondary-50 rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <h5 className="font-medium text-text-primary">{condition.name}</h5>
                      <button
                        onClick={() => removeCondition(condition.id)}
                        className="p-1 hover:bg-error-50 rounded-lg transition-fast"
                      >
                        <Icon name="Trash2" size={16} color="var(--color-error)" />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Severity</label>
                        <select
                          value={condition.severity}
                          onChange={(e) => updateCondition(condition.id, 'severity', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                        >
                          <option value="mild">Mild</option>
                          <option value="moderate">Moderate</option>
                          <option value="severe">Severe</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Status</label>
                        <select
                          value={condition.status}
                          onChange={(e) => updateCondition(condition.id, 'status', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                        >
                          <option value="active">Active</option>
                          <option value="managed">Managed</option>
                          <option value="resolved">Resolved</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Diagnosed</label>
                        <Input
                          type="date"
                          value={condition.diagnosedDate}
                          onChange={(e) => updateCondition(condition.id, 'diagnosedDate', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Current Medications */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="Pill" size={16} />
                <span>Current Medications</span>
              </h4>
              
              {/* Add New Medication */}
              <div className="flex space-x-2 mb-4">
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="Type medication name..."
                    value={newMedication}
                    onChange={(e) => setNewMedication(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addMedication()}
                  />
                </div>
                <Button
                  variant="primary"
                  onClick={addMedication}
                  iconName="Plus"
                  disabled={!newMedication.trim()}
                >
                  Add
                </Button>
              </div>

              {/* Common Medications */}
              <div className="mb-4">
                <p className="text-sm text-text-secondary mb-2">Common medications:</p>
                <div className="flex flex-wrap gap-2">
                  {commonMedications.map((medication) => (
                    <button
                      key={medication}
                      onClick={() => setNewMedication(medication)}
                      className="px-3 py-1 text-sm bg-secondary-50 hover:bg-secondary-100 text-text-secondary hover:text-text-primary rounded-full transition-fast"
                    >
                      {medication}
                    </button>
                  ))}
                </div>
              </div>

              {/* Medications List */}
              <div className="space-y-3">
                {(data.currentMedications || []).map((medication) => (
                  <div key={medication.id} className="p-4 bg-secondary-50 rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <h5 className="font-medium text-text-primary">{medication.name}</h5>
                      <button
                        onClick={() => removeMedication(medication.id)}
                        className="p-1 hover:bg-error-50 rounded-lg transition-fast"
                      >
                        <Icon name="Trash2" size={16} color="var(--color-error)" />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Dosage</label>
                        <Input
                          type="text"
                          placeholder="e.g., 10mg"
                          value={medication.dosage}
                          onChange={(e) => updateMedication(medication.id, 'dosage', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Frequency</label>
                        <Input
                          type="text"
                          placeholder="e.g., Twice daily"
                          value={medication.frequency}
                          onChange={(e) => updateMedication(medication.id, 'frequency', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Prescribed By</label>
                        <Input
                          type="text"
                          placeholder="Doctor name"
                          value={medication.prescribedBy}
                          onChange={(e) => updateMedication(medication.id, 'prescribedBy', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Start Date</label>
                        <Input
                          type="date"
                          value={medication.startDate}
                          onChange={(e) => updateMedication(medication.id, 'startDate', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Allergies */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="AlertTriangle" size={16} />
                <span>Allergies & Reactions</span>
              </h4>
              
              {/* Add New Allergy */}
              <div className="flex space-x-2 mb-4">
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="Type allergy name..."
                    value={newAllergy}
                    onChange={(e) => setNewAllergy(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addAllergy()}
                  />
                </div>
                <Button
                  variant="primary"
                  onClick={addAllergy}
                  iconName="Plus"
                  disabled={!newAllergy.trim()}
                >
                  Add
                </Button>
              </div>

              {/* Common Allergies */}
              <div className="mb-4">
                <p className="text-sm text-text-secondary mb-2">Common allergies:</p>
                <div className="flex flex-wrap gap-2">
                  {commonAllergies.map((allergy) => (
                    <button
                      key={allergy}
                      onClick={() => setNewAllergy(allergy)}
                      className="px-3 py-1 text-sm bg-secondary-50 hover:bg-secondary-100 text-text-secondary hover:text-text-primary rounded-full transition-fast"
                    >
                      {allergy}
                    </button>
                  ))}
                </div>
              </div>

              {/* Allergies List */}
              <div className="space-y-3">
                {(data.allergies || []).map((allergy) => (
                  <div key={allergy.id} className="p-4 bg-error-50 rounded-lg border border-error-200">
                    <div className="flex items-start justify-between mb-3">
                      <h5 className="font-medium text-text-primary flex items-center space-x-2">
                        <Icon name="AlertTriangle" size={16} color="var(--color-error)" />
                        <span>{allergy.name}</span>
                      </h5>
                      <button
                        onClick={() => removeAllergy(allergy.id)}
                        className="p-1 hover:bg-error-100 rounded-lg transition-fast"
                      >
                        <Icon name="Trash2" size={16} color="var(--color-error)" />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Type</label>
                        <select
                          value={allergy.type}
                          onChange={(e) => updateAllergy(allergy.id, 'type', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                        >
                          <option value="medication">Medication</option>
                          <option value="food">Food</option>
                          <option value="environmental">Environmental</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Severity</label>
                        <select
                          value={allergy.severity}
                          onChange={(e) => updateAllergy(allergy.id, 'severity', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
                        >
                          <option value="mild">Mild</option>
                          <option value="moderate">Moderate</option>
                          <option value="severe">Severe</option>
                          <option value="life-threatening">Life-threatening</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-xs text-text-secondary mb-1">Reaction</label>
                        <Input
                          type="text"
                          placeholder="e.g., Rash, swelling"
                          value={allergy.reaction}
                          onChange={(e) => updateAllergy(allergy.id, 'reaction', e.target.value)}
                          className="text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MedicalHistorySection;