# VoiceHealth AI: Clinical & Cultural Enhancement Implementation Guide

## Pre-Implementation Checklist

Before beginning implementation, verify the following existing components are functioning:
- ✅ Supabase pgvector extension enabled
- ✅ VectorSearchService operational
- ✅ Manchester Triage System with Africa modifiers
- ✅ Emergency stop service with <2 second response time
- ✅ Multi-language support framework
- ✅ HIPAA-compliant encryption (AES-256-GCM)
- ✅ Agent orchestrator with provider fallback
- ✅ Cultural empathy mandate service

## Implementation Steps

### Step 1: Database Schema Extensions

#### 1.1 Create Clinical Evidence Tables
```sql
-- File: supabase/migrations/20241225000000_clinical_decision_support.sql

-- Extend medical documents with evidence levels
ALTER TABLE medical_documents 
ADD COLUMN IF NOT EXISTS evidence_level VARCHAR(1) CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
ADD COLUMN IF NOT EXISTS cultural_context TEXT[],
ADD COLUMN IF NOT EXISTS region_applicability TEXT[],
ADD COLUMN IF NOT EXISTS specialty VARCHAR(100),
ADD COLUMN IF NOT EXISTS traditional_medicine_refs TEXT[];

-- Clinical pathways table
CREATE TABLE IF NOT EXISTS clinical_pathways (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  condition VARCHAR(200) NOT NULL,
  region VARCHAR(10) NOT NULL,
  pathway_steps JSONB NOT NULL,
  evidence_level VARCHAR(1) NOT NULL CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
  cultural_adaptations JSONB,
  emergency_protocols JSONB,
  traditional_medicine_considerations JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- African pharmacology tables
CREATE TABLE IF NOT EXISTS african_pharmacogenetics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  population VARCHAR(50) NOT NULL,
  gene VARCHAR(20) NOT NULL,
  variant VARCHAR(100) NOT NULL,
  frequency DECIMAL(5,4) NOT NULL,
  metabolizer_status VARCHAR(20) NOT NULL,
  clinical_significance TEXT,
  evidence_level VARCHAR(1) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS drug_interactions_african (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  drug_a VARCHAR(200) NOT NULL,
  drug_b VARCHAR(200) NOT NULL,
  traditional_remedy VARCHAR(200),
  interaction_type VARCHAR(50) NOT NULL,
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('mild', 'moderate', 'severe', 'contraindicated')),
  population_specific BOOLEAN DEFAULT FALSE,
  genetic_variants TEXT[],
  evidence_level VARCHAR(1) NOT NULL,
  clinical_management TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Traditional medicine database
CREATE TABLE IF NOT EXISTS traditional_remedies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  local_names JSONB NOT NULL, -- {"twi": "name", "swahili": "name", etc.}
  regions TEXT[] NOT NULL,
  active_compounds TEXT[],
  traditional_uses TEXT[],
  scientific_evidence JSONB,
  safety_profile JSONB NOT NULL,
  drug_interactions JSONB,
  preparation_methods TEXT[],
  dosage_guidelines TEXT[],
  contraindications TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cultural profiles enhancement
CREATE TABLE IF NOT EXISTS enhanced_cultural_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  culture_code VARCHAR(10) NOT NULL,
  language_preference VARCHAR(10) NOT NULL,
  communication_style JSONB NOT NULL,
  religious_considerations JSONB,
  family_involvement_preferences JSONB,
  traditional_medicine_openness INTEGER CHECK (traditional_medicine_openness BETWEEN 1 AND 5),
  gender_sensitivity_level INTEGER CHECK (gender_sensitivity_level BETWEEN 1 AND 5),
  authority_respect_level INTEGER CHECK (authority_respect_level BETWEEN 1 AND 5),
  health_literacy_level VARCHAR(20) CHECK (health_literacy_level IN ('basic', 'intermediate', 'advanced')),
  preferred_explanation_style VARCHAR(20) CHECK (preferred_explanation_style IN ('technical', 'simplified', 'storytelling')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_clinical_pathways_condition_region ON clinical_pathways(condition, region);
CREATE INDEX IF NOT EXISTS idx_drug_interactions_drugs ON drug_interactions_african(drug_a, drug_b);
CREATE INDEX IF NOT EXISTS idx_traditional_remedies_regions ON traditional_remedies USING GIN(regions);
CREATE INDEX IF NOT EXISTS idx_medical_documents_evidence ON medical_documents(evidence_level);
CREATE INDEX IF NOT EXISTS idx_cultural_profiles_user ON enhanced_cultural_profiles(user_id);

-- RLS policies
ALTER TABLE clinical_pathways ENABLE ROW LEVEL SECURITY;
ALTER TABLE african_pharmacogenetics ENABLE ROW LEVEL SECURITY;
ALTER TABLE drug_interactions_african ENABLE ROW LEVEL SECURITY;
ALTER TABLE traditional_remedies ENABLE ROW LEVEL SECURITY;
ALTER TABLE enhanced_cultural_profiles ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (adjust based on your auth requirements)
CREATE POLICY "clinical_pathways_read" ON clinical_pathways FOR SELECT USING (true);
CREATE POLICY "african_pharmacogenetics_read" ON african_pharmacogenetics FOR SELECT USING (true);
CREATE POLICY "drug_interactions_read" ON drug_interactions_african FOR SELECT USING (true);
CREATE POLICY "traditional_remedies_read" ON traditional_remedies FOR SELECT USING (true);
CREATE POLICY "cultural_profiles_user_access" ON enhanced_cultural_profiles 
  FOR ALL USING (auth.uid() = user_id);
```

#### 1.2 Create Database Functions
```sql
-- File: supabase/migrations/20241225000001_clinical_functions.sql

-- Function to get clinical recommendations with cultural context
CREATE OR REPLACE FUNCTION get_clinical_recommendations(
  p_condition TEXT,
  p_region TEXT,
  p_cultural_context JSONB DEFAULT '{}'::JSONB,
  p_evidence_level TEXT[] DEFAULT ARRAY['A', 'B', 'C', 'D']
)
RETURNS TABLE (
  pathway_id UUID,
  condition TEXT,
  pathway_steps JSONB,
  evidence_level TEXT,
  cultural_adaptations JSONB,
  confidence_score DECIMAL
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cp.id,
    cp.condition,
    cp.pathway_steps,
    cp.evidence_level,
    cp.cultural_adaptations,
    CASE 
      WHEN cp.evidence_level = 'A' THEN 0.95
      WHEN cp.evidence_level = 'B' THEN 0.85
      WHEN cp.evidence_level = 'C' THEN 0.75
      ELSE 0.65
    END as confidence_score
  FROM clinical_pathways cp
  WHERE 
    cp.condition ILIKE '%' || p_condition || '%'
    AND cp.region = p_region
    AND cp.evidence_level = ANY(p_evidence_level)
  ORDER BY 
    CASE cp.evidence_level 
      WHEN 'A' THEN 1 
      WHEN 'B' THEN 2 
      WHEN 'C' THEN 3 
      ELSE 4 
    END,
    cp.updated_at DESC;
END;
$$;

-- Function to check drug interactions including traditional medicine
CREATE OR REPLACE FUNCTION check_comprehensive_drug_interactions(
  p_medications TEXT[],
  p_traditional_remedies TEXT[] DEFAULT ARRAY[]::TEXT[],
  p_population TEXT DEFAULT 'general'
)
RETURNS TABLE (
  interaction_type TEXT,
  severity TEXT,
  description TEXT,
  clinical_management TEXT,
  evidence_level TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  -- Modern drug interactions
  SELECT 
    dia.interaction_type,
    dia.severity,
    'Drug interaction between ' || dia.drug_a || ' and ' || dia.drug_b as description,
    dia.clinical_management,
    dia.evidence_level
  FROM drug_interactions_african dia
  WHERE 
    (dia.drug_a = ANY(p_medications) AND dia.drug_b = ANY(p_medications))
    AND (dia.population_specific = false OR p_population = ANY(ARRAY['West_African', 'East_African', 'Southern_African']))
  
  UNION ALL
  
  -- Traditional medicine interactions
  SELECT 
    dia.interaction_type,
    dia.severity,
    'Interaction between ' || dia.drug_a || ' and traditional remedy ' || dia.traditional_remedy as description,
    dia.clinical_management,
    dia.evidence_level
  FROM drug_interactions_african dia
  WHERE 
    dia.drug_a = ANY(p_medications)
    AND dia.traditional_remedy = ANY(p_traditional_remedies)
    AND dia.traditional_remedy IS NOT NULL;
END;
$$;
```

### Step 2: Enhanced Services Implementation

#### 2.1 Clinical Decision Support Service
```typescript
// File: src/services/ClinicalDecisionSupportService.ts

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { VectorSearchService } from './VectorSearchService';

export interface ClinicalEvidence {
  level: 'A' | 'B' | 'C' | 'D';
  source: string;
  confidence: number;
  applicability: string[];
  lastUpdated: Date;
}

export interface DiagnosticRecommendation {
  condition: string;
  probability: number;
  evidence: ClinicalEvidence[];
  differentialDiagnoses: string[];
  nextSteps: string[];
  culturalConsiderations: string[];
  traditionalMedicineRelevance?: string[];
}

export interface DrugInteractionResult {
  hasInteractions: boolean;
  interactions: DrugInteraction[];
  traditionalMedicineWarnings: TraditionalMedicineWarning[];
  dosageAdjustments: DosageAdjustment[];
  severity: 'mild' | 'moderate' | 'severe' | 'contraindicated';
}

export interface ClinicalPathway {
  id: string;
  condition: string;
  steps: ClinicalStep[];
  evidenceLevel: string;
  culturalAdaptations: CulturalAdaptation[];
  emergencyProtocols?: EmergencyProtocol[];
}

export class ClinicalDecisionSupportService {
  private supabase: SupabaseClient;
  private vectorSearch: VectorSearchService;
  private cache: Map<string, any> = new Map();

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for clinical decision support');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.vectorSearch = new VectorSearchService();
    console.log('✅ ClinicalDecisionSupportService initialized');
  }

  /**
   * Get evidence-based diagnostic recommendations
   */
  async getDiagnosticRecommendations(
    symptoms: string[],
    patientProfile: any,
    culturalContext: any
  ): Promise<DiagnosticRecommendation[]> {
    try {
      const cacheKey = `diagnostic_${symptoms.join('_')}_${patientProfile.region}`;
      
      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }

      // Use vector search for symptom-based recommendations
      const symptomQuery = symptoms.join(' ');
      const vectorResults = await this.vectorSearch.searchSimilarDocuments(
        symptomQuery,
        {
          limit: 10,
          threshold: 0.7,
          filters: {
            region_applicability: patientProfile.region,
            evidence_level: ['A', 'B', 'C']
          }
        }
      );

      // Get clinical pathways from database
      const { data: pathways, error } = await this.supabase
        .rpc('get_clinical_recommendations', {
          p_condition: symptomQuery,
          p_region: patientProfile.region,
          p_cultural_context: culturalContext,
          p_evidence_level: ['A', 'B', 'C', 'D']
        });

      if (error) {
        console.error('Error fetching clinical recommendations:', error);
        throw error;
      }

      const recommendations: DiagnosticRecommendation[] = pathways.map((pathway: any) => ({
        condition: pathway.condition,
        probability: pathway.confidence_score,
        evidence: [{
          level: pathway.evidence_level,
          source: 'Clinical Guidelines',
          confidence: pathway.confidence_score,
          applicability: [patientProfile.region],
          lastUpdated: new Date()
        }],
        differentialDiagnoses: this.extractDifferentialDiagnoses(vectorResults),
        nextSteps: pathway.pathway_steps?.steps || [],
        culturalConsiderations: pathway.cultural_adaptations?.considerations || [],
        traditionalMedicineRelevance: pathway.traditional_medicine_considerations?.relevant_remedies || []
      }));

      // Cache results for 5 minutes
      this.cache.set(cacheKey, recommendations);
      setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);

      return recommendations;
    } catch (error) {
      console.error('Error getting diagnostic recommendations:', error);
      throw error;
    }
  }

  /**
   * Check drug interactions including traditional medicine
   */
  async checkDrugInteractions(
    medications: string[],
    traditionalRemedies: string[] = [],
    patientProfile: any
  ): Promise<DrugInteractionResult> {
    try {
      const { data: interactions, error } = await this.supabase
        .rpc('check_comprehensive_drug_interactions', {
          p_medications: medications,
          p_traditional_remedies: traditionalRemedies,
          p_population: this.getPopulationFromRegion(patientProfile.region)
        });

      if (error) {
        console.error('Error checking drug interactions:', error);
        throw error;
      }

      const hasInteractions = interactions && interactions.length > 0;
      const maxSeverity = this.getMaxSeverity(interactions);

      return {
        hasInteractions,
        interactions: interactions || [],
        traditionalMedicineWarnings: this.extractTraditionalWarnings(interactions),
        dosageAdjustments: await this.getDosageAdjustments(medications, patientProfile),
        severity: maxSeverity
      };
    } catch (error) {
      console.error('Error checking drug interactions:', error);
      throw error;
    }
  }

  /**
   * Get clinical pathway for specific condition
   */
  async getClinicalPathway(
    condition: string,
    region: string,
    culturalFactors: any
  ): Promise<ClinicalPathway | null> {
    try {
      const { data: pathways, error } = await this.supabase
        .from('clinical_pathways')
        .select('*')
        .eq('condition', condition)
        .eq('region', region)
        .order('evidence_level')
        .limit(1);

      if (error || !pathways || pathways.length === 0) {
        return null;
      }

      const pathway = pathways[0];
      return {
        id: pathway.id,
        condition: pathway.condition,
        steps: pathway.pathway_steps?.steps || [],
        evidenceLevel: pathway.evidence_level,
        culturalAdaptations: pathway.cultural_adaptations?.adaptations || [],
        emergencyProtocols: pathway.emergency_protocols?.protocols || []
      };
    } catch (error) {
      console.error('Error getting clinical pathway:', error);
      return null;
    }
  }

  // Helper methods
  private extractDifferentialDiagnoses(vectorResults: any[]): string[] {
    return vectorResults
      .map(result => result.metadata?.condition)
      .filter(condition => condition)
      .slice(0, 5);
  }

  private getPopulationFromRegion(region: string): string {
    const regionMap: { [key: string]: string } = {
      'GH': 'West_African',
      'NG': 'West_African',
      'KE': 'East_African',
      'TZ': 'East_African',
      'ZA': 'Southern_African',
      'BW': 'Southern_African'
    };
    return regionMap[region] || 'general';
  }

  private getMaxSeverity(interactions: any[]): 'mild' | 'moderate' | 'severe' | 'contraindicated' {
    if (!interactions || interactions.length === 0) return 'mild';
    
    const severityOrder = ['mild', 'moderate', 'severe', 'contraindicated'];
    return interactions.reduce((max, interaction) => {
      const currentIndex = severityOrder.indexOf(interaction.severity);
      const maxIndex = severityOrder.indexOf(max);
      return currentIndex > maxIndex ? interaction.severity : max;
    }, 'mild');
  }

  private extractTraditionalWarnings(interactions: any[]): any[] {
    return interactions
      .filter(interaction => interaction.traditional_remedy)
      .map(interaction => ({
        remedy: interaction.traditional_remedy,
        warning: interaction.description,
        severity: interaction.severity,
        management: interaction.clinical_management
      }));
  }

  private async getDosageAdjustments(medications: string[], patientProfile: any): Promise<any[]> {
    try {
      const { data: adjustments, error } = await this.supabase
        .from('african_dosing_guidelines')
        .select('*')
        .in('medication', medications)
        .eq('population', this.getPopulationFromRegion(patientProfile.region));

      if (error) {
        console.error('Error getting dosage adjustments:', error);
        return [];
      }

      return adjustments || [];
    } catch (error) {
      console.error('Error getting dosage adjustments:', error);
      return [];
    }
  }
}

// Export singleton instance
export const clinicalDecisionSupportService = new ClinicalDecisionSupportService();
```

This implementation guide provides the foundation for integrating advanced clinical decision support and cultural enhancement features into VoiceHealth AI. The next steps would involve implementing the cultural adaptation service and integrating these services with the existing agent orchestrator.
