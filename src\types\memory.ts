/**
 * MEMORY SYSTEM TYPE DEFINITIONS
 * 
 * TypeScript interfaces for the persistent memory management system
 * that replaces the broken in-memory Map-based storage.
 */

export interface ConversationMessage {
  id?: string;
  session_id: string;
  speaker_type: 'user' | 'agent' | 'system';
  speaker_id?: string | null;
  speaker_name: string;
  content: string;
  timestamp?: string;
  confidence_score?: number;
  sequence_number: number;
  metadata?: Record<string, any>;
}

export interface ConversationContext {
  session_id: string;
  messages: ConversationMessage[];
  participant_count: number;
  active_agents: string[];
  last_activity: string;
  total_messages: number;
  session_metadata?: SessionMetadata;
}

export interface SessionMetadata {
  patient_id?: string;
  primary_agent_id?: string;
  session_title?: string;
  is_emergency?: boolean;
  key_symptoms?: string[];
  current_phase?: string;
  status?: 'scheduled' | 'active' | 'paused' | 'completed' | 'cancelled';
  started_at?: string;
  ended_at?: string;
  duration_seconds?: number;
  progress_percentage?: number;
}

export interface MemoryManagerOptions {
  supabaseUrl?: string;
  supabaseKey?: string;
  tableName?: string;
  sessionTableName?: string;
  enableEncryption?: boolean;
  retentionDays?: number;
}

export interface MemoryHealthStatus {
  healthy: boolean;
  details: string;
  lastChecked?: string;
  connectionStatus?: 'connected' | 'disconnected' | 'error';
  messageCount?: number;
  sessionCount?: number;
}

export interface ConversationSummary {
  session_id: string;
  total_messages: number;
  participants: string[];
  key_topics: string[];
  last_activity: string;
  duration_minutes?: number;
  emergency_flags?: string[];
}

// Legacy types for backward compatibility during migration
export interface ConversationMemory {
  sessionId: string;
  messages: ConversationMessage[];
  lastUpdate: string;
  tokenCount?: number;
}

// Agent-specific memory types
export interface AgentMemoryContext {
  agent_id: string;
  session_id: string;
  conversation_history: ConversationMessage[];
  agent_specific_context?: Record<string, any>;
  handoff_context?: HandoffContext;
}

export interface HandoffContext {
  from_agent_id: string;
  to_agent_id: string;
  handoff_reason: string;
  context_summary: string;
  confidence_score?: number;
  timestamp: string;
  patient_consent?: boolean;
}

// Emergency memory types
export interface EmergencyMemoryContext {
  session_id: string;
  emergency_type: string;
  triggered_at: string;
  response_time_ms: number;
  emergency_agent_id?: string;
  escalation_level: 'low' | 'medium' | 'high' | 'critical';
  emergency_contacts_notified?: boolean;
}

// HIPAA compliance types
export interface HIPAAMemoryAudit {
  session_id: string;
  access_type: 'read' | 'write' | 'delete' | 'export';
  accessed_by: string;
  access_timestamp: string;
  data_classification: 'public' | 'internal' | 'confidential' | 'restricted';
  retention_policy_applied: boolean;
  encryption_status: 'encrypted' | 'unencrypted' | 'in_transit';
}

export interface MemoryRetentionPolicy {
  session_type: 'consultation' | 'emergency' | 'administrative';
  retention_days: number;
  auto_delete_enabled: boolean;
  backup_required: boolean;
  patient_consent_required: boolean;
}

// Performance monitoring types
export interface MemoryPerformanceMetrics {
  operation_type: 'read' | 'write' | 'delete' | 'search';
  execution_time_ms: number;
  data_size_bytes: number;
  cache_hit_rate?: number;
  error_count: number;
  timestamp: string;
}

// Search and retrieval types
export interface MemorySearchQuery {
  session_id?: string;
  speaker_type?: 'user' | 'agent' | 'system';
  speaker_id?: string;
  content_keywords?: string[];
  date_range?: {
    start: string;
    end: string;
  };
  confidence_threshold?: number;
  limit?: number;
  offset?: number;
}

export interface MemorySearchResult {
  messages: ConversationMessage[];
  total_count: number;
  search_time_ms: number;
  relevance_scores?: number[];
}

// Export all types for easy importing
export type {
  ConversationMessage as Message,
  ConversationContext as Context,
  SessionMetadata as Session,
  MemoryManagerOptions as Options,
  MemoryHealthStatus as HealthStatus
};
