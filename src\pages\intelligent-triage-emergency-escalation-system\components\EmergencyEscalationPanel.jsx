import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import triageService from '../../../utils/triageService';

const EmergencyEscalationPanel = ({ 
  emergencyAlerts, 
  currentAssessment, 
  onEscalationTrigger, 
  loading, 
  userProfile 
}) => {
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [escalationHistory, setEscalationHistory] = useState([]);
  const [emergencyContacts, setEmergencyContacts] = useState({});
  const [autoEscalationEnabled, setAutoEscalationEnabled] = useState(true);

  useEffect(() => {
    // Load emergency contacts based on user's country
    const contacts = triageService.getEmergencyContacts(userProfile?.country);
    setEmergencyContacts(contacts);
    
    // Load escalation history from local storage
    const history = JSON.parse(localStorage.getItem('escalationHistory') || '[]');
    setEscalationHistory(history);
  }, [userProfile]);

  useEffect(() => {
    // Auto-select the most critical alert
    if (emergencyAlerts.length > 0 && !selectedAlert) {
      const criticalAlert = emergencyAlerts.find(alert => alert.urgencyLevel === 'critical');
      setSelectedAlert(criticalAlert || emergencyAlerts[0]);
    }
  }, [emergencyAlerts, selectedAlert]);

  const handleEmergencyCall = (phoneNumber, contactType) => {
    // Log the emergency call
    const callRecord = {
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      contactType,
      phoneNumber,
      alertId: selectedAlert?.id,
      action: 'emergency_call_initiated'
    };
    
    const updatedHistory = [callRecord, ...escalationHistory];
    setEscalationHistory(updatedHistory);
    localStorage.setItem('escalationHistory', JSON.stringify(updatedHistory));
    
    // Initiate the call
    window.location.href = `tel:${phoneNumber}`;
  };

  const handleEscalationTrigger = async (alert) => {
    if (onEscalationTrigger) {
      await onEscalationTrigger(alert);
      
      // Log the escalation
      const escalationRecord = {
        id: Math.random().toString(36).substr(2, 9),
        timestamp: new Date().toISOString(),
        alertId: alert.id,
        action: 'escalation_triggered',
        urgencyLevel: alert.urgencyLevel
      };
      
      const updatedHistory = [escalationRecord, ...escalationHistory];
      setEscalationHistory(updatedHistory);
      localStorage.setItem('escalationHistory', JSON.stringify(updatedHistory));
    }
  };

  const getUrgencyColor = (level) => {
    const colors = {
      critical: 'red',
      urgent: 'orange',
      semi_urgent: 'yellow',
      routine: 'green',
      self_care: 'blue'
    };
    return colors[level] || 'gray';
  };

  const getTimeSinceAssessment = (timestamp) => {
    const now = new Date();
    const assessmentTime = new Date(timestamp);
    const diffMinutes = Math.floor((now - assessmentTime) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  return (
    <div className="space-y-6">
      
      {/* Panel Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">
            Emergency Escalation Panel
          </h3>
          <p className="text-text-secondary">
            Immediate human intervention triggers and healthcare provider notifications
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoEscalation"
              checked={autoEscalationEnabled}
              onChange={(e) => setAutoEscalationEnabled(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="autoEscalation" className="text-sm text-text-secondary">
              Auto-escalation
            </label>
          </div>
          
          <Button
            variant="danger"
            onClick={() => handleEmergencyCall(emergencyContacts.emergency, 'emergency')}
            iconName="Phone"
            iconPosition="left"
            size="sm"
          >
            Call Emergency
          </Button>
        </div>
      </div>

      {/* Active Emergency Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Alert List */}
        <div className="bg-surface rounded-lg border border-border p-4">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="AlertTriangle" size={18} className="mr-2 text-red-600" />
            Active Emergency Alerts ({emergencyAlerts.length})
          </h4>
          
          {emergencyAlerts.length === 0 ? (
            <div className="text-center py-8">
              <Icon name="CheckCircle" size={48} className="text-green-500 mx-auto mb-4" />
              <p className="text-text-secondary">No emergency alerts at this time</p>
            </div>
          ) : (
            <div className="space-y-3">
              {emergencyAlerts.map((alert, index) => {
                const urgencyColor = getUrgencyColor(alert.urgencyLevel);
                const isSelected = selectedAlert?.id === alert.id;
                
                return (
                  <div
                    key={alert.id}
                    onClick={() => setSelectedAlert(alert)}
                    className={`
                      p-4 rounded-lg border-2 cursor-pointer transition-all duration-200
                      ${isSelected 
                        ? `border-${urgencyColor}-500 bg-${urgencyColor}-50` 
                        : 'border-border bg-background hover:bg-surface-hover'
                      }
                    `}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full bg-${urgencyColor}-500 mr-3 ${
                          alert.urgencyLevel === 'critical' ? 'animate-pulse' : ''
                        }`} />
                        <span className="font-medium text-text-primary">
                          Patient Assessment #{index + 1}
                        </span>
                      </div>
                      <span className="text-xs text-text-secondary">
                        {getTimeSinceAssessment(alert.timestamp)}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-text-secondary">Urgency:</span>
                        <span className={`ml-2 font-medium text-${urgencyColor}-800`}>
                          {alert.urgencyLevel?.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <span className="text-text-secondary">Score:</span>
                        <span className="ml-2 font-medium text-text-primary">
                          {alert.urgencyScore}/10
                        </span>
                      </div>
                    </div>
                    
                    {alert.redFlags?.length > 0 && (
                      <div className="mt-2 flex items-center">
                        <Icon name="Alert" size={14} className="text-red-600 mr-1" />
                        <span className="text-xs text-red-600">
                          {alert.redFlags.length} red flag{alert.redFlags.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Alert Details & Actions */}
        <div className="bg-surface rounded-lg border border-border p-4">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="FileText" size={18} className="mr-2" />
            Alert Details & Actions
          </h4>
          
          {selectedAlert ? (
            <div className="space-y-4">
              
              {/* Alert Summary */}
              <div className={`bg-${getUrgencyColor(selectedAlert.urgencyLevel)}-50 border border-${getUrgencyColor(selectedAlert.urgencyLevel)}-200 rounded-lg p-4`}>
                <div className="flex items-center justify-between mb-2">
                  <h5 className={`font-semibold text-${getUrgencyColor(selectedAlert.urgencyLevel)}-800`}>
                    {selectedAlert.urgencyLevel?.replace('_', ' ').toUpperCase()} - Score: {selectedAlert.urgencyScore}/10
                  </h5>
                  <span className={`text-xs px-2 py-1 rounded bg-${getUrgencyColor(selectedAlert.urgencyLevel)}-200 text-${getUrgencyColor(selectedAlert.urgencyLevel)}-800`}>
                    {selectedAlert.timeFrame}
                  </span>
                </div>
                
                {selectedAlert.redFlags?.length > 0 && (
                  <div className="mb-3">
                    <p className={`text-sm font-medium text-${getUrgencyColor(selectedAlert.urgencyLevel)}-800 mb-1`}>
                      Critical Symptoms:
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {selectedAlert.redFlags.map((flag, index) => (
                        <span key={index} className={`text-xs px-2 py-1 rounded bg-red-200 text-red-800`}>
                          {flag.replace('_', ' ')}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedAlert.detectedConditions?.length > 0 && (
                  <div>
                    <p className={`text-sm font-medium text-${getUrgencyColor(selectedAlert.urgencyLevel)}-800 mb-1`}>
                      Possible Conditions:
                    </p>
                    <div className="space-y-1">
                      {selectedAlert.detectedConditions.map((condition, index) => (
                        <div key={index} className="text-xs text-text-secondary">
                          • {condition.disease?.replace('_', ' ')} ({condition.confidence} confidence)
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Escalation Actions */}
              <div className="space-y-3">
                <h6 className="font-medium text-text-primary">Escalation Actions</h6>
                
                {selectedAlert.urgencyLevel === 'critical' && (
                  <Button
                    variant="danger"
                    fullWidth
                    onClick={() => handleEscalationTrigger(selectedAlert)}
                    loading={loading}
                    iconName="AlertTriangle"
                    iconPosition="left"
                  >
                    Trigger Immediate Emergency Response
                  </Button>
                )}
                
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleEmergencyCall(emergencyContacts.emergency, 'emergency')}
                    iconName="Phone"
                    iconPosition="left"
                    size="sm"
                  >
                    Emergency Services ({emergencyContacts.emergency})
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => handleEmergencyCall(emergencyContacts.ambulance, 'ambulance')}
                    iconName="Truck"
                    iconPosition="left"
                    size="sm"
                  >
                    Ambulance ({emergencyContacts.ambulance})
                  </Button>
                </div>
              </div>
              
              {/* Recommended Actions */}
              {selectedAlert.recommendedProtocols?.length > 0 && (
                <div>
                  <h6 className="font-medium text-text-primary mb-2">Recommended Protocols</h6>
                  <div className="space-y-2">
                    {selectedAlert.recommendedProtocols.map((protocol, index) => (
                      <div key={index} className="bg-blue-50 border border-blue-200 rounded p-3">
                        <div className="font-medium text-blue-900 text-sm capitalize mb-1">
                          {protocol.name?.replace('_', ' ')}
                        </div>
                        <p className="text-xs text-blue-700 mb-2">{protocol.instructions}</p>
                        <div className="text-xs text-blue-600">
                          ⏱️ Time limit: {protocol.timeLimit} minutes
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
            </div>
          ) : (
            <div className="text-center py-8">
              <Icon name="FileText" size={48} className="text-gray-400 mx-auto mb-4" />
              <p className="text-text-secondary">Select an alert to view details</p>
            </div>
          )}
        </div>
      </div>

      {/* Emergency Contacts Quick Access */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <h4 className="font-semibold text-text-primary mb-4 flex items-center">
          <Icon name="Phone" size={18} className="mr-2" />
          Emergency Contacts - {userProfile?.country || 'Ghana'}
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          {Object.entries(emergencyContacts).map(([type, number]) => (
            <button
              key={type}
              onClick={() => handleEmergencyCall(number, type)}
              className="bg-red-50 hover:bg-red-100 border border-red-200 rounded-lg p-3 text-center transition-all duration-200"
            >
              <div className="bg-red-600 text-white rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-2">
                <Icon name="Phone" size={16} />
              </div>
              <div className="text-sm font-medium text-red-800 capitalize mb-1">
                {type.replace('_', ' ')}
              </div>
              <div className="text-lg font-mono text-red-700">{number}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Recent Escalation History */}
      {escalationHistory.length > 0 && (
        <div className="bg-surface rounded-lg border border-border p-4">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="History" size={18} className="mr-2" />
            Recent Escalations
          </h4>
          
          <div className="space-y-2">
            {escalationHistory.slice(0, 5).map((record) => (
              <div key={record.id} className="flex items-center justify-between p-3 bg-surface-secondary rounded-lg">
                <div className="flex items-center">
                  <Icon 
                    name={record.action === 'emergency_call_initiated' ? 'Phone' : 'AlertTriangle'} 
                    size={16} 
                    className="mr-3 text-text-secondary" 
                  />
                  <div>
                    <span className="text-sm font-medium text-text-primary">
                      {record.action.replace('_', ' ').toUpperCase()}
                    </span>
                    {record.contactType && (
                      <span className="text-xs text-text-secondary ml-2">
                        ({record.contactType})
                      </span>
                    )}
                  </div>
                </div>
                <span className="text-xs text-text-secondary">
                  {getTimeSinceAssessment(record.timestamp)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

    </div>
  );
};

export default EmergencyEscalationPanel;