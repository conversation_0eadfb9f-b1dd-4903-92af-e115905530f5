import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const OfflineCapabilitiesSection = ({ 
  offlineContent = {},
  storageUsed = 0,
  storageLimit = 100,
  onDownloadContent = () => {},
  onRemoveContent = () => {},
  className = ''
}) => {
  const [selectedContent, setSelectedContent] = useState(new Set());

  const contentItems = [
    {
      id: 'agent-profiles',
      name: 'Agent Profiles',
      description: 'AI agent personalities and medical specializations',
      size: 25,
      icon: 'Users',
      isDownloaded: offlineContent?.agentProfiles || false
    },
    {
      id: 'medical-knowledge',
      name: 'Medical Knowledge Base',
      description: 'Essential medical references and protocols',
      size: 45,
      icon: 'BookOpen',
      isDownloaded: offlineContent?.medicalKnowledge || false
    },
    {
      id: 'emergency-protocols',
      name: 'Emergency Protocols',
      description: 'Critical emergency consultation procedures',
      size: 15,
      icon: 'AlertTriangle',
      isDownloaded: offlineContent?.emergencyProtocols || false,
      priority: 'high'
    },
    {
      id: 'consultation-templates',
      name: 'Consultation Templates',
      description: 'Standard consultation flows and questions',
      size: 20,
      icon: 'FileText',
      isDownloaded: offlineContent?.consultationTemplates || false
    }
  ];

  const handleToggleContent = (contentId) => {
    const item = contentItems.find(item => item.id === contentId);
    if (item?.isDownloaded) {
      onRemoveContent(contentId);
    } else {
      onDownloadContent(contentId);
    }
  };

  const getStoragePercentage = () => (storageUsed / storageLimit) * 100;

  const getStorageColor = () => {
    const percentage = getStoragePercentage();
    if (percentage >= 90) return 'bg-error-500';
    if (percentage >= 70) return 'bg-warning-500';
    return 'bg-success-500';
  };

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
            <Icon name="Download" size={20} color="var(--color-primary-500)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Offline Content
            </h3>
            <p className="text-sm text-text-secondary">
              Download content for offline access
            </p>
          </div>
        </div>
      </div>

      {/* Storage Usage */}
      <div className="mb-6 p-4 bg-background rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Icon name="HardDrive" size={16} color="var(--color-text-secondary)" />
            <span className="text-sm font-medium text-text-primary">Storage Usage</span>
          </div>
          <span className="text-sm text-text-secondary">
            {storageUsed}MB / {storageLimit}MB
          </span>
        </div>
        <div className="w-full bg-secondary-100 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${getStorageColor()}`}
            style={{ width: `${getStoragePercentage()}%` }}
          ></div>
        </div>
      </div>

      {/* Content Items */}
      <div className="space-y-4">
        {contentItems.map((item) => (
          <div key={item.id} className="border border-border rounded-lg p-4 hover:border-border-active transition-fast">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 flex-1">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  item.priority === 'high' ? 'bg-error-50' : 'bg-secondary-50'
                }`}>
                  <Icon 
                    name={item.icon} 
                    size={18} 
                    color={item.priority === 'high' ? 'var(--color-error-500)' : 'var(--color-text-secondary)'} 
                  />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-medium text-text-primary">{item.name}</h4>
                    {item.priority === 'high' && (
                      <span className="px-2 py-1 bg-error-50 text-error-600 text-xs rounded-full">
                        Essential
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-text-secondary mb-2">{item.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-text-muted">
                    <span>{item.size}MB</span>
                    {item.isDownloaded && (
                      <div className="flex items-center space-x-1">
                        <Icon name="CheckCircle" size={12} color="var(--color-success-500)" />
                        <span className="text-success-600">Downloaded</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <button
                onClick={() => handleToggleContent(item.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-fast ${
                  item.isDownloaded
                    ? 'bg-error-50 text-error-600 hover:bg-error-100' :'bg-primary-50 text-primary-600 hover:bg-primary-100'
                }`}
              >
                {item.isDownloaded ? 'Remove' : 'Download'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Storage Optimization */}
      <div className="mt-6 p-4 bg-warning-50 rounded-lg">
        <div className="flex items-start space-x-3">
          <Icon name="Info" size={16} color="var(--color-warning-600)" />
          <div>
            <h4 className="text-sm font-medium text-warning-800 mb-1">Storage Optimization</h4>
            <p className="text-xs text-warning-700">
              Emergency protocols are essential for offline consultations. 
              Consider removing other content if storage is limited.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfflineCapabilitiesSection;