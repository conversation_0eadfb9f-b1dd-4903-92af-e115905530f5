import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const AgentProfileCard = ({ 
  agent, 
  onVoicePreview, 
  onUpdateAgent, 
  isPreviewPlaying = false,
  className = '' 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localAgent, setLocalAgent] = useState(agent);

  const handleVoicePreview = () => {
    onVoicePreview(agent.id, localAgent.voiceProfile);
  };

  const handleUpdateField = (field, value) => {
    const updatedAgent = { ...localAgent, [field]: value };
    setLocalAgent(updatedAgent);
    onUpdateAgent(updatedAgent);
  };

  const handlePersonalityChange = (trait, value) => {
    const updatedPersonality = { ...localAgent.personality, [trait]: value };
    handleUpdateField('personality', updatedPersonality);
  };

  const handleExpertiseToggle = (area) => {
    const updatedExpertise = localAgent.expertiseFocus.includes(area)
      ? localAgent.expertiseFocus.filter(item => item !== area)
      : [...localAgent.expertiseFocus, area];
    handleUpdateField('expertiseFocus', updatedExpertise);
  };

  const getAgentTypeIcon = () => {
    switch (agent.type) {
      case 'gp': return 'Stethoscope';
      case 'cardiologist': return 'Heart';
      case 'nutritionist': return 'Apple';
      default: return 'User';
    }
  };

  const getAgentTypeColor = () => {
    switch (agent.type) {
      case 'gp': return 'primary';
      case 'cardiologist': return 'error';
      case 'nutritionist': return 'success';
      default: return 'secondary';
    }
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal hover:shadow-elevated transition-smooth ${className}`}>
      {/* Agent Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start space-x-4">
          <div className="relative">
            <div className="w-16 h-16 rounded-full overflow-hidden bg-secondary-100">
              <Image
                src={localAgent.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${agent.id}`}
                alt={`${localAgent.name} avatar`}
                className="w-full h-full object-cover"
              />
            </div>
            <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center ${
              getAgentTypeColor() === 'primary' ? 'bg-primary-500' :
              getAgentTypeColor() === 'error' ? 'bg-error-500' :
              getAgentTypeColor() === 'success'? 'bg-success-500' : 'bg-secondary-500'
            }`}>
              <Icon name={getAgentTypeIcon()} size={12} color="white" />
            </div>
          </div>
          
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-text-primary font-heading">
                  {localAgent.name}
                </h3>
                <p className="text-sm text-text-secondary font-caption">
                  {localAgent.specialty}
                </p>
              </div>
              
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-2 hover:bg-secondary-50 rounded-lg transition-fast"
                title={isExpanded ? "Show less" : "Show more"}
              >
                <Icon 
                  name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                  size={20} 
                  color="var(--color-text-secondary)"
                />
              </button>
            </div>
            
            <div className="flex items-center space-x-4 mt-2">
              <div className="flex items-center space-x-2">
                <Icon name="Volume2" size={14} color="var(--color-text-secondary)" />
                <span className="text-sm text-text-secondary">
                  {localAgent.voiceProfile.gender} • {localAgent.voiceProfile.accent}
                </span>
              </div>
              
              <Button
                variant="ghost"
                size="xs"
                onClick={handleVoicePreview}
                loading={isPreviewPlaying}
                iconName={isPreviewPlaying ? "Pause" : "Play"}
                iconPosition="left"
              >
                Preview
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Expandable Content */}
      <div className={`transition-all duration-300 overflow-hidden ${
        isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-0 opacity-0'
      }`}>
        <div className="px-6 pb-6 space-y-6">
          {/* Voice Profile Settings */}
          <div>
            <h4 className="font-medium text-text-primary mb-3">Voice Profile</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Gender Selection */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Gender
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {['Male', 'Female'].map((gender) => (
                    <button
                      key={gender}
                      onClick={() => handleUpdateField('voiceProfile', { 
                        ...localAgent.voiceProfile, 
                        gender: gender.toLowerCase() 
                      })}
                      className={`p-3 rounded-lg border transition-fast text-sm font-medium ${
                        localAgent.voiceProfile.gender === gender.toLowerCase()
                          ? 'border-primary-500 bg-primary-50 text-primary-600' :'border-border bg-surface text-text-secondary hover:bg-secondary-50'
                      }`}
                    >
                      <Icon 
                        name={gender === 'Male' ? 'User' : 'UserCheck'} 
                        size={16} 
                        className="mx-auto mb-1"
                      />
                      {gender}
                    </button>
                  ))}
                </div>
              </div>

              {/* Nationality/Accent */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Accent
                </label>
                <select
                  value={localAgent.voiceProfile.accent}
                  onChange={(e) => handleUpdateField('voiceProfile', { 
                    ...localAgent.voiceProfile, 
                    accent: e.target.value 
                  })}
                  className="w-full p-3 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="american">American English</option>
                  <option value="british">British English</option>
                  <option value="australian">Australian English</option>
                  <option value="canadian">Canadian English</option>
                  <option value="indian">Indian English</option>
                </select>
              </div>
            </div>
          </div>

          {/* Personality Traits */}
          <div>
            <h4 className="font-medium text-text-primary mb-3">Personality Traits</h4>
            <div className="space-y-4">
              {Object.entries(localAgent.personality).map(([trait, value]) => (
                <div key={trait}>
                  <div className="flex justify-between items-center mb-2">
                    <label className="text-sm font-medium text-text-primary capitalize">
                      {trait.replace(/([A-Z])/g, ' $1').trim()}
                    </label>
                    <span className="text-sm text-text-secondary font-data">
                      {Math.round(value * 100)}%
                    </span>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={value}
                      onChange={(e) => handlePersonalityChange(trait, parseFloat(e.target.value))}
                      className="w-full h-2 bg-secondary-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-text-muted mt-1">
                      <span>
                        {trait === 'formality' ? 'Casual' : 
                         trait === 'detailLevel' ? 'Concise' : 
                         trait === 'empathy' ? 'Clinical' : 'Low'}
                      </span>
                      <span>
                        {trait === 'formality' ? 'Formal' : 
                         trait === 'detailLevel' ? 'Detailed' : 
                         trait === 'empathy' ? 'Empathetic' : 'High'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Expertise Focus */}
          <div>
            <h4 className="font-medium text-text-primary mb-3">Expertise Focus</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {agent.availableExpertise.map((area) => (
                <label
                  key={area}
                  className="flex items-center space-x-3 p-3 rounded-lg border border-border hover:bg-secondary-50 transition-fast cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={localAgent.expertiseFocus.includes(area)}
                    onChange={() => handleExpertiseToggle(area)}
                    className="w-4 h-4 text-primary-600 bg-surface border-border rounded focus:ring-primary-500 focus:ring-2"
                  />
                  <span className="text-sm text-text-primary">{area}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Sample Conversation */}
          <div>
            <h4 className="font-medium text-text-primary mb-3">Sample Response</h4>
            <div className="p-4 bg-secondary-50 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-primary-500 flex items-center justify-center flex-shrink-0">
                  <Icon name={getAgentTypeIcon()} size={14} color="white" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-text-primary font-caption leading-relaxed">
                    {agent.sampleResponse}
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={handleVoicePreview}
                      loading={isPreviewPlaying}
                      iconName={isPreviewPlaying ? "Pause" : "Play"}
                      iconPosition="left"
                    >
                      {isPreviewPlaying ? 'Playing...' : 'Listen'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-6 py-4 border-t border-border bg-secondary-50">
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocalAgent(agent)}
            iconName="RotateCcw"
            iconPosition="left"
            className="flex-1"
          >
            Reset
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={handleVoicePreview}
            loading={isPreviewPlaying}
            iconName="Play"
            iconPosition="left"
            className="flex-1"
          >
            Test Voice
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AgentProfileCard;