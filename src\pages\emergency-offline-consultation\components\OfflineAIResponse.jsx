import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const OfflineAIResponse = ({ symptomAssessment, selectedProtocol, onRecordConsultation }) => {
  const [aiResponse, setAiResponse] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [consultationNotes, setConsultationNotes] = useState('');

  useEffect(() => {
    if (symptomAssessment || selectedProtocol) {
      generateOfflineResponse();
    }
  }, [symptomAssessment, selectedProtocol]);

  const generateOfflineResponse = () => {
    setIsProcessing(true);
    
    // Simulate AI processing time
    setTimeout(() => {
      const response = generateCachedResponse();
      setAiResponse(response);
      setIsProcessing(false);
    }, 2000);
  };

  const generateCachedResponse = () => {
    // This would typically use cached medical knowledge base
    let assessment = {
      riskLevel: 'moderate',
      recommendations: [],
      immediateActions: [],
      followUpNeeded: true,
      confidenceLevel: 85
    };

    if (selectedProtocol) {
      assessment = {
        riskLevel: selectedProtocol.severity === 'critical' ? 'high' : 'moderate',
        recommendations: [
          'Follow the emergency protocol steps carefully',
          'Call emergency services if condition worsens',
          'Monitor vital signs continuously'
        ],
        immediateActions: selectedProtocol.steps.slice(0, 3),
        followUpNeeded: true,
        confidenceLevel: 95,
        protocolBased: true
      };
    } else if (symptomAssessment) {
      const hasHighRiskSymptoms = symptomAssessment.symptoms?.some(s => 
        ['chest_pain', 'difficulty_breathing'].includes(s)
      );
      
      if (hasHighRiskSymptoms || symptomAssessment.severity === 'severe') {
        assessment.riskLevel = 'high';
        assessment.recommendations = [
          'Seek immediate medical attention',
          'Call emergency services (911)',
          'Do not drive yourself to the hospital'
        ];
        assessment.immediateActions = [
          'Call 911 immediately',
          'Remain calm and still',
          'Have someone stay with you'
        ];
      } else {
        assessment.recommendations = [
          'Monitor symptoms closely',
          'Rest and stay hydrated',
          'Contact healthcare provider if symptoms worsen'
        ];
        assessment.immediateActions = [
          'Rest in a comfortable position',
          'Note symptom changes',
          'Prepare to seek medical care if needed'
        ];
      }
    }

    return assessment;
  };

  const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'moderate': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleRecordConsultation = () => {
    const consultationRecord = {
      id: `offline_${Date.now()}`,
      timestamp: new Date(),
      type: 'emergency_offline',
      symptomAssessment,
      selectedProtocol,
      aiResponse,
      notes: consultationNotes,
      offline: true
    };
    
    // Store in local storage for later sync
    const existingRecords = JSON.parse(localStorage.getItem('offlineConsultations') || '[]');
    existingRecords.push(consultationRecord);
    localStorage.setItem('offlineConsultations', JSON.stringify(existingRecords));
    
    onRecordConsultation?.(consultationRecord);
  };

  if (!symptomAssessment && !selectedProtocol) {
    return (
      <div className="bg-surface rounded-xl shadow-minimal border border-border p-6">
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon name="Bot" size={32} className="text-primary" />
          </div>
          <h3 className="text-lg font-semibold text-text-primary mb-2">Offline AI Assistant Ready</h3>
          <p className="text-text-secondary">
            Complete symptom assessment or select an emergency protocol to get AI-powered guidance.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-surface rounded-xl shadow-minimal border border-border p-6">
      <div className="flex items-center mb-4">
        <Icon name="Bot" size={24} className="text-primary mr-3" />
        <h3 className="text-xl font-semibold text-text-primary">Offline AI Assessment</h3>
        <div className="ml-auto flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
          <span className="text-sm text-text-secondary">Offline Mode</span>
        </div>
      </div>

      {isProcessing ? (
        <div className="text-center py-8">
          <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <Icon name="Brain" size={24} className="text-primary" />
          </div>
          <p className="text-text-secondary">Analyzing symptoms using cached medical protocols...</p>
        </div>
      ) : aiResponse && (
        <div className="space-y-6">
          {/* Risk Assessment */}
          <div className={`p-4 rounded-lg border ${getRiskColor(aiResponse.riskLevel)}`}>
            <div className="flex items-center mb-2">
              <Icon name="AlertTriangle" size={20} className="mr-2" />
              <h4 className="font-semibold">Risk Level: {aiResponse.riskLevel.toUpperCase()}</h4>
            </div>
            <div className="flex items-center">
              <span className="text-sm">Confidence: {aiResponse.confidenceLevel}%</span>
              {aiResponse.protocolBased && (
                <span className="ml-4 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  Protocol-Based
                </span>
              )}
            </div>
          </div>

          {/* Immediate Actions */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-semibold text-red-800 mb-3 flex items-center">
              <Icon name="Zap" size={18} className="mr-2" />
              Immediate Actions Required
            </h4>
            <ol className="space-y-2">
              {aiResponse.immediateActions?.map((action, index) => (
                <li key={index} className="text-sm text-red-700 flex items-start">
                  <span className="bg-red-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    {index + 1}
                  </span>
                  {action}
                </li>
              ))}
            </ol>
          </div>

          {/* Recommendations */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-800 mb-3 flex items-center">
              <Icon name="CheckSquare" size={18} className="mr-2" />
              Additional Recommendations
            </h4>
            <ul className="space-y-2">
              {aiResponse.recommendations?.map((recommendation, index) => (
                <li key={index} className="text-sm text-blue-700 flex items-start">
                  <Icon name="Check" size={16} className="mr-2 mt-0.5 text-blue-600 flex-shrink-0" />
                  {recommendation}
                </li>
              ))}
            </ul>
          </div>

          {/* Consultation Notes */}
          <div className="border border-border rounded-lg p-4">
            <h4 className="font-semibold text-text-primary mb-3 flex items-center">
              <Icon name="FileText" size={18} className="mr-2" />
              Consultation Notes
            </h4>
            <textarea
              value={consultationNotes}
              onChange={(e) => setConsultationNotes(e.target.value)}
              placeholder="Add notes about the consultation for later review..."
              className="w-full h-24 p-3 border border-border rounded-lg resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Follow-up Alert */}
          {aiResponse.followUpNeeded && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <Icon name="Clock" size={20} className="text-yellow-600 mr-3 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-yellow-800 mb-1">Follow-up Required</h4>
                  <p className="text-sm text-yellow-700">
                    Schedule a follow-up consultation with a healthcare provider when internet connectivity is restored.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Record Consultation */}
          <div className="pt-4 border-t border-border">
            <Button
              onClick={handleRecordConsultation}
              iconName="Save"
              iconPosition="left"
              fullWidth
            >
              Record Offline Consultation
            </Button>
            <p className="text-xs text-text-secondary mt-2 text-center">
              This consultation will be saved locally and synced when online.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OfflineAIResponse;