import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import AutomaticAssessmentEngine from './components/AutomaticAssessmentEngine';
import EmergencyEscalationPanel from './components/EmergencyEscalationPanel';
import TriageAlgorithmConfiguration from './components/TriageAlgorithmConfiguration';
import EmergencyContactIntegration from './components/EmergencyContactIntegration';
import RealTimeMonitoring from './components/RealTimeMonitoring';
import { useAuth } from '../../contexts/SimpleAuthContext';
import triageService from '../../utils/triageService';

const IntelligentTriageEmergencyEscalationSystem = () => {
  const navigate = useNavigate();
  const { user, userProfile } = useAuth();
  
  const [activeTab, setActiveTab] = useState('assessment');
  const [currentAssessment, setCurrentAssessment] = useState(null);
  const [triageQueue, setTriageQueue] = useState([]);
  const [emergencyAlerts, setEmergencyAlerts] = useState([]);
  const [systemConfig, setSystemConfig] = useState({
    redFlagThreshold: 8,
    autoEscalationEnabled: true,
    culturalAdaptation: true,
    regionalSettings: userProfile?.country || 'ghana'
  });
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  const tabs = [
    { id: 'assessment', name: 'Assessment Engine', icon: 'Stethoscope', color: 'blue' },
    { id: 'escalation', name: 'Emergency Escalation', icon: 'AlertTriangle', color: 'red' },
    { id: 'configuration', name: 'Algorithm Config', icon: 'Settings', color: 'gray' },
    { id: 'contacts', name: 'Emergency Contacts', icon: 'Phone', color: 'green' },
    { id: 'monitoring', name: 'Real-time Monitor', icon: 'Activity', color: 'purple' }
  ];

  const urgencyStats = {
    critical: triageQueue.filter(item => item.urgencyLevel === 'critical').length,
    urgent: triageQueue.filter(item => item.urgencyLevel === 'urgent').length,
    routine: triageQueue.filter(item => item.urgencyLevel === 'routine').length,
    total: triageQueue.length
  };

  useEffect(() => {
    if (user) {
      loadTriageQueue();
      loadEmergencyAlerts();
      
      // Set up auto-refresh for real-time monitoring
      const interval = setInterval(() => {
        loadTriageQueue();
        loadEmergencyAlerts();
        setLastUpdate(new Date());
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [user]);

  useEffect(() => {
    // Auto-switch to escalation tab when critical assessment is detected
    if (currentAssessment?.urgencyLevel === 'critical' || currentAssessment?.redFlags?.length > 0) {
      setActiveTab('escalation');
    }
  }, [currentAssessment]);

  const loadTriageQueue = async () => {
    try {
      const result = await triageService.getTriageQueue(user?.id);
      if (result.success) {
        setTriageQueue(result.data);
      }
    } catch (error) {
      console.error('Failed to load triage queue:', error);
    }
  };

  const loadEmergencyAlerts = async () => {
    try {
      // Load active emergency alerts
      const alerts = triageQueue.filter(item => 
        item.urgencyLevel === 'critical' || 
        item.redFlags?.length > 0
      ).slice(0, 5);
      
      setEmergencyAlerts(alerts);
    } catch (error) {
      console.error('Failed to load emergency alerts:', error);
    }
  };

  const handleAssessmentComplete = (assessment) => {
    setCurrentAssessment(assessment);
    
    // Add to queue
    setTriageQueue(prev => [assessment, ...prev]);
    
    // Check for emergency escalation
    if (assessment.requiresEmergencyEscalation) {
      setEmergencyAlerts(prev => [assessment, ...prev]);
      
      // Auto-escalate if enabled
      if (systemConfig.autoEscalationEnabled) {
        handleEmergencyEscalation(assessment);
      }
    }
  };

  const handleEmergencyEscalation = async (assessment) => {
    setLoading(true);
    try {
      const result = await triageService.initiateEmergencyEscalation(assessment, {
        country: userProfile?.country,
        location: userProfile?.location
      });

      if (result.success) {
        if (result.action === 'emergency_call') {
          const shouldCall = window.confirm(
            `Critical condition detected! Should we call emergency services (${result.phoneNumber}) now?`
          );
          
          if (shouldCall) {
            window.location.href = `tel:${result.phoneNumber}`;
          }
        }
        
        // Show success notification
        alert(`Emergency escalation initiated: ${result.message}`);
      } else {
        alert(`Escalation failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Emergency escalation error:', error);
      alert('Failed to initiate emergency escalation. Please call emergency services directly.');
    } finally {
      setLoading(false);
    }
  };

  const handleConfigurationUpdate = (newConfig) => {
    setSystemConfig(newConfig);
    localStorage.setItem('triageSystemConfig', JSON.stringify(newConfig));
  };

  const handleManualRefresh = () => {
    loadTriageQueue();
    loadEmergencyAlerts();
    setLastUpdate(new Date());
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <Icon name="UserX" size={48} className="text-text-secondary mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-text-primary mb-2">Authentication Required</h2>
            <p className="text-text-secondary mb-4">Please sign in to access the triage system.</p>
            <Button onClick={() => navigate('/authentication-demo-access')}>
              Sign In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-4 lg:mb-0">
              <h1 className="text-3xl font-bold text-text-primary font-heading mb-2">
                Intelligent Triage & Emergency Escalation System
              </h1>
              <p className="text-text-secondary">
                AI-powered medical triage with automatic urgency assessment and emergency protocols
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
              <Button
                variant="outline"
                onClick={handleManualRefresh}
                iconName="RefreshCw"
                iconPosition="left"
                size="sm"
                disabled={loading}
              >
                Refresh
              </Button>
              <Button
                variant="danger"
                onClick={() => window.location.href = 'tel:999'}
                iconName="Phone"
                iconPosition="left"
                size="sm"
              >
                Emergency Call
              </Button>
            </div>
          </div>
        </div>

        {/* Real-time Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="bg-red-600 text-white rounded-full p-3 mr-4">
                <Icon name="AlertTriangle" size={24} />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-red-800">{urgencyStats.critical}</h3>
                <p className="text-red-700">Critical Cases</p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="bg-orange-600 text-white rounded-full p-3 mr-4">
                <Icon name="Clock" size={24} />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-orange-800">{urgencyStats.urgent}</h3>
                <p className="text-orange-700">Urgent Cases</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="bg-green-600 text-white rounded-full p-3 mr-4">
                <Icon name="CheckCircle" size={24} />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-green-800">{urgencyStats.routine}</h3>
                <p className="text-green-700">Routine Cases</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="bg-blue-600 text-white rounded-full p-3 mr-4">
                <Icon name="Users" size={24} />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-blue-800">{urgencyStats.total}</h3>
                <p className="text-blue-700">Total Queue</p>
              </div>
            </div>
          </div>
        </div>

        {/* Emergency Alerts Banner */}
        {emergencyAlerts.length > 0 && (
          <div className="bg-red-600 text-white rounded-lg p-4 mb-8 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Icon name="AlertTriangle" size={24} className="mr-3" />
                <div>
                  <h3 className="font-semibold text-lg">Emergency Alerts Active</h3>
                  <p className="text-red-100">
                    {emergencyAlerts.length} critical case{emergencyAlerts.length !== 1 ? 's' : ''} requiring immediate attention
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={() => setActiveTab('escalation')}
                className="border-white text-white hover:bg-white hover:text-red-600"
                size="sm"
              >
                View Alerts
              </Button>
            </div>
          </div>
        )}

        {/* Main Triage Interface */}
        <div className="bg-surface rounded-xl shadow-minimal border border-border">
          
          {/* Tab Navigation */}
          <div className="border-b border-border">
            <nav className="flex overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex-shrink-0 py-4 px-6 text-center transition-all duration-200 min-w-max
                    ${activeTab === tab.id
                      ? `border-b-2 border-${tab.color}-500 text-${tab.color}-600 bg-${tab.color}-50`
                      : 'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                    }
                  `}
                >
                  <div className="flex items-center justify-center mb-1">
                    <Icon name={tab.icon} size={18} className="mr-2" />
                    <span className="font-medium">{tab.name}</span>
                  </div>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'assessment' && (
              <AutomaticAssessmentEngine
                onAssessmentComplete={handleAssessmentComplete}
                currentAssessment={currentAssessment}
                systemConfig={systemConfig}
                userProfile={userProfile}
              />
            )}

            {activeTab === 'escalation' && (
              <EmergencyEscalationPanel
                emergencyAlerts={emergencyAlerts}
                currentAssessment={currentAssessment}
                onEscalationTrigger={handleEmergencyEscalation}
                loading={loading}
                userProfile={userProfile}
              />
            )}

            {activeTab === 'configuration' && (
              <TriageAlgorithmConfiguration
                systemConfig={systemConfig}
                onConfigUpdate={handleConfigurationUpdate}
                userProfile={userProfile}
              />
            )}

            {activeTab === 'contacts' && (
              <EmergencyContactIntegration
                userProfile={userProfile}
                systemConfig={systemConfig}
              />
            )}

            {activeTab === 'monitoring' && (
              <RealTimeMonitoring
                triageQueue={triageQueue}
                emergencyAlerts={emergencyAlerts}
                lastUpdate={lastUpdate}
                onRefresh={handleManualRefresh}
              />
            )}
          </div>

        </div>

        {/* System Information */}
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
          
          {/* Cultural Adaptation Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <Icon name="Globe" size={24} className="text-blue-600 mr-4 mt-0.5" />
              <div>
                <h4 className="text-lg font-semibold text-blue-800 mb-2">
                  Cultural Adaptation for {userProfile?.country || 'Ghana'}
                </h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Regional disease patterns integrated (Malaria, TB, Typhoid)</li>
                  <li>• Cost-sensitive treatment recommendations</li>
                  <li>• Family involvement considerations</li>
                  <li>• Traditional medicine respect protocols</li>
                  <li>• Local emergency service integration</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Last Update Info */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <div className="flex items-start">
              <Icon name="Clock" size={24} className="text-gray-600 mr-4 mt-0.5" />
              <div>
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  System Status
                </h4>
                <div className="text-sm text-gray-700 space-y-1">
                  <p>Last Update: {lastUpdate.toLocaleTimeString()}</p>
                  <p>Auto-refresh: Every 30 seconds</p>
                  <p>Emergency Escalation: {systemConfig.autoEscalationEnabled ? 'Enabled' : 'Disabled'}</p>
                  <p>Cultural Adaptation: {systemConfig.culturalAdaptation ? 'Active' : 'Inactive'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

      </main>
    </div>
  );
};

export default IntelligentTriageEmergencyEscalationSystem;