/**
 * CONTEXT TRUNCATION TESTS
 * 
 * Unit tests for the context truncation service to validate
 * intelligent truncation while preserving critical medical information.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { contextTruncationService } from '../../services/ContextTruncationService';

describe('Context Truncation Service', () => {
  beforeEach(() => {
    // Reset any state if needed
  });

  describe('Basic Truncation Functionality', () => {
    it('should not truncate context within token limits', async () => {
      const smallContext = {
        currentSymptoms: 'Mild headache',
        patientProfile: { age: 30, gender: 'female' }
      };

      const result = await contextTruncationService.truncateContext(smallContext, {
        maxTokens: 8000
      });

      expect(result.truncationStrategy).toBe('no_truncation_needed');
      expect(result.compressionRatio).toBe(1.0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should truncate large context while preserving priorities', async () => {
      const largeContext = {
        emergencyFlags: ['chest_pain', 'difficulty_breathing'],
        currentSymptoms: 'Severe chest pain radiating to left arm, shortness of breath, sweating',
        medicalHistory: Array(100).fill('Previous condition').join(', '),
        conversationHistory: Array(50).fill({ message: 'Long conversation history item' }),
        regionalContext: { country: 'Ghana', commonConditions: Array(20).fill('condition') }
      };

      const result = await contextTruncationService.truncateContext(largeContext, {
        maxTokens: 1000
      });

      expect(result.truncationStrategy).not.toBe('no_truncation_needed');
      expect(result.compressionRatio).toBeLessThan(1.0);
      expect(result.truncatedTokenCount).toBeLessThanOrEqual(1000);
      
      // Emergency context should be preserved
      const emergencySegment = result.preservedSegments.find(s => s.type === 'emergency_context');
      expect(emergencySegment).toBeDefined();
      expect(emergencySegment?.emergencyFlag).toBe(true);
    });
  });

  describe('Medical Information Prioritization', () => {
    it('should prioritize emergency context above all else', async () => {
      const contextWithEmergency = {
        emergencyFlags: ['cardiac_arrest', 'unconscious'],
        currentSymptoms: 'Patient is unconscious and not breathing',
        conversationHistory: Array(100).fill({ message: 'Very long conversation history' }),
        regionalContext: { country: 'Kenya', data: 'Large regional data set' }
      };

      const result = await contextTruncationService.truncateContext(contextWithEmergency, {
        maxTokens: 500,
        preserveEmergencyContext: true
      });

      // Emergency context should always be preserved
      const emergencySegment = result.preservedSegments.find(s => s.type === 'emergency_context');
      expect(emergencySegment).toBeDefined();
      expect(emergencySegment?.priority).toBeGreaterThan(9);

      // Lower priority items should be removed first
      const conversationSegment = result.removedSegments.find(s => s.type === 'conversation_history');
      expect(conversationSegment).toBeDefined();
    });

    it('should preserve current symptoms over conversation history', async () => {
      const context = {
        currentSymptoms: 'Severe abdominal pain, nausea, vomiting',
        conversationHistory: Array(50).fill({ message: 'Long conversation' }),
        regionalContext: { country: 'Nigeria', data: 'Regional data' }
      };

      const result = await contextTruncationService.truncateContext(context, {
        maxTokens: 300
      });

      const symptomsSegment = result.preservedSegments.find(s => s.type === 'current_symptoms');
      const conversationSegment = result.preservedSegments.find(s => s.type === 'conversation_history');

      expect(symptomsSegment).toBeDefined();
      expect(conversationSegment).toBeUndefined();
    });
  });

  describe('Compression Strategies', () => {
    it('should compress important segments when needed', async () => {
      const contextWithLargeMedicalHistory = {
        medicalHistory: Array(200).fill('Detailed medical history item with lots of information').join(', '),
        currentSymptoms: 'Current symptoms'
      };

      const result = await contextTruncationService.truncateContext(contextWithLargeMedicalHistory, {
        maxTokens: 500,
        compressionRatio: 0.5
      });

      expect(result.truncationStrategy).toContain('compression');
      
      const medicalHistorySegment = result.preservedSegments.find(s => s.type === 'medical_history');
      if (medicalHistorySegment) {
        expect(medicalHistorySegment.content).toContain('[COMPRESSED');
      }
    });
  });

  describe('Quality Assessment', () => {
    it('should calculate quality score based on medical relevance', async () => {
      const medicalContext = {
        emergencyFlags: ['severe_allergic_reaction'],
        currentSymptoms: 'Severe allergic reaction with swelling',
        medicalHistory: 'History of allergies to penicillin',
        conversationHistory: Array(20).fill({ message: 'Chat message' })
      };

      const result = await contextTruncationService.truncateContext(medicalContext, {
        maxTokens: 300
      });

      // Quality should be high when medical information is preserved
      expect(result.qualityScore).toBeGreaterThan(0.7);
    });

    it('should generate warnings for low quality truncation', async () => {
      const context = {
        medicalHistory: Array(100).fill('Important medical information').join(', '),
        currentSymptoms: 'Critical symptoms that need attention'
      };

      const result = await contextTruncationService.truncateContext(context, {
        maxTokens: 50, // Very restrictive limit
        qualityThreshold: 0.8
      });

      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.qualityScore).toBeLessThan(0.8);
    });
  });

  describe('Emergency Context Handling', () => {
    it('should preserve emergency context even with very low token limits', async () => {
      const emergencyContext = {
        emergencyFlags: ['cardiac_arrest', 'cpr_needed'],
        currentSymptoms: 'Patient in cardiac arrest',
        conversationHistory: Array(100).fill({ message: 'Long history' }),
        regionalContext: { country: 'South Africa', data: 'Large dataset' }
      };

      const result = await contextTruncationService.truncateContext(emergencyContext, {
        maxTokens: 100, // Very low limit
        preserveEmergencyContext: true
      });

      const emergencySegment = result.preservedSegments.find(s => s.type === 'emergency_context');
      expect(emergencySegment).toBeDefined();
      expect(emergencySegment?.emergencyFlag).toBe(true);
    });
  });

  describe('Token Estimation', () => {
    it('should estimate token counts reasonably accurately', async () => {
      const testText = 'This is a test message with approximately twenty words to test token estimation accuracy.';
      
      // Rough estimation: 1 token ≈ 4 characters
      const expectedTokens = Math.ceil(testText.length / 4);
      
      const context = { currentSymptoms: testText };
      const result = await contextTruncationService.truncateContext(context, {
        maxTokens: 10000 // High limit to avoid truncation
      });

      const segment = result.preservedSegments.find(s => s.type === 'current_symptoms');
      expect(segment?.tokenCount).toBeCloseTo(expectedTokens, 5);
    });
  });

  describe('Context Segment Types', () => {
    it('should correctly categorize different context types', async () => {
      const comprehensiveContext = {
        emergencyFlags: ['emergency'],
        currentSymptoms: 'symptoms',
        medicalHistory: 'history',
        steeringGuidance: 'guidance',
        patientProfile: { name: 'test' },
        conversationHistory: [{ message: 'chat' }],
        emotionalContext: { sentiment: 'positive' },
        regionalContext: { country: 'Ghana' }
      };

      const result = await contextTruncationService.truncateContext(comprehensiveContext, {
        maxTokens: 10000 // High limit to preserve all
      });

      const segmentTypes = result.preservedSegments.map(s => s.type);
      
      expect(segmentTypes).toContain('emergency_context');
      expect(segmentTypes).toContain('current_symptoms');
      expect(segmentTypes).toContain('medical_history');
      expect(segmentTypes).toContain('steering_guidance');
      expect(segmentTypes).toContain('patient_profile');
      expect(segmentTypes).toContain('conversation_history');
      expect(segmentTypes).toContain('emotional_context');
      expect(segmentTypes).toContain('regional_context');
    });
  });

  describe('Performance and Statistics', () => {
    it('should provide truncation statistics', () => {
      const stats = contextTruncationService.getTruncationStatistics();
      
      expect(stats).toHaveProperty('averageCompressionRatio');
      expect(stats).toHaveProperty('averageQualityScore');
      expect(stats).toHaveProperty('totalTruncations');
      
      expect(typeof stats.averageCompressionRatio).toBe('number');
      expect(typeof stats.averageQualityScore).toBe('number');
      expect(typeof stats.totalTruncations).toBe('number');
    });
  });
});
