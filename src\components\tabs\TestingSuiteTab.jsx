/**
 * Testing Suite Tab - Run and manage PWA tests
 */

import React, { useState } from 'react';
import Button from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Wifi, WifiOff, RefreshCw, Play, TestTube, CheckCircle, XCircle } from 'lucide-react';

export const TestingSuiteTab = ({ testSuite, testResults, isRunningTests, onRunTests, networkSimulator }) => {
  const [networkState, setNetworkState] = useState('online');

  const handleNetworkSimulation = (state) => {
    setNetworkState(state);
    switch (state) {
      case 'offline':
        networkSimulator.goOffline();
        break;
      case 'online':
        networkSimulator.goOnline();
        break;
      case 'intermittent':
        networkSimulator.simulateIntermittent(3000);
        break;
      default:
        networkSimulator.reset();
    }
  };

  return (
    <div className="space-y-6">
      {/* Network Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="h-5 w-5" />
            Network Simulation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3 mb-4">
            <Button
              onClick={() => handleNetworkSimulation('online')}
              variant={networkState === 'online' ? 'primary' : 'secondary'}
              size="sm"
              iconName="Wifi"
            >
              Online
            </Button>
            <Button
              onClick={() => handleNetworkSimulation('offline')}
              variant={networkState === 'offline' ? 'primary' : 'secondary'}
              size="sm"
              iconName="WifiOff"
            >
              Offline
            </Button>
            <Button
              onClick={() => handleNetworkSimulation('intermittent')}
              variant={networkState === 'intermittent' ? 'primary' : 'secondary'}
              size="sm"
              iconName="RefreshCw"
            >
              Intermittent
            </Button>
          </div>
          <Badge variant={networkState === 'online' ? 'success' : networkState === 'offline' ? 'error' : 'warning'}>
            Current: {networkState}
          </Badge>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Test Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button
              onClick={onRunTests}
              loading={isRunningTests}
              disabled={isRunningTests}
              iconName="Play"
            >
              {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            <Button
              onClick={() => testSuite?.cleanup?.()}
              variant="secondary"
              size="sm"
            >
              Reset Environment
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          {testResults && testResults.length > 0 ? (
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {result.passed ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <div>
                      <div className="font-medium">{result.name}</div>
                      {result.error && (
                        <div className="text-sm text-red-600">{result.error}</div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.duration && (
                      <span className="text-sm text-gray-500">{result.duration}ms</span>
                    )}
                    <Badge variant={result.passed ? 'success' : 'error'}>
                      {result.passed ? 'Pass' : 'Fail'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">
              {isRunningTests ? 'Running tests...' : 'No test results available. Click "Run All Tests" to start.'}
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
