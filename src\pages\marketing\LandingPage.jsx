import React from "react";
import { Link } from "react-router-dom";
import Navbar from "../../components/Navbar";
import Footer from "../../components/Footer";

const features = [
  {
    title: "AI-Powered Voice Consultations",
    desc: "Speak naturally with our AI specialists and get instant health insights."
  },
  {
    title: "Intelligent Triage",
    desc: "Automatic urgency scoring and escalation to human clinicians when needed."
  },
  {
    title: "Secure & Compliant",
    desc: "HIPAA-friendly infrastructure, end-to-end encryption and audit trails."
  }
];

const LandingPage = () => {
  return (
    <>
      <Navbar />
      <main className="bg-white">
        <section className="max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl sm:text-5xl font-extrabold text-text-primary mb-6">
            Your AI-Powered Voice Clinic
          </h1>
          <p className="text-lg text-text-secondary max-w-3xl mx-auto mb-8">
            VoiceHealth AI lets anyone start a secure voice consultation, get AI triage and connect with specialists in minutes.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/authentication-demo-access"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              Try the Demo
            </Link>
            <Link
              to="/pricing"
              className="inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100"
            >
              View Pricing
            </Link>
          </div>
        </section>

        <section className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((f) => (
              <div key={f.title} className="p-6 bg-white rounded-lg shadow">
                <h3 className="text-xl font-semibold text-text-primary mb-2">{f.title}</h3>
                <p className="text-text-secondary">{f.desc}</p>
              </div>
            ))}
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default LandingPage;
