import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CountryGrid from './components/CountryGrid';
import LocationDetection from './components/LocationDetection';
import NavigationControls from './components/NavigationControls';
import HelpSection from './components/HelpSection';
import ProgressIndicator from '../welcome-language-selection/components/ProgressIndicator';

const CountryRegionalSelection = () => {
  const navigate = useNavigate();
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [detectedCountry, setDetectedCountry] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');

  useEffect(() => {
    // Get language from localStorage
    const language = localStorage.getItem('selectedLanguage') || 'en';
    setSelectedLanguage(language);

    // Auto-detect location (mock implementation)
    detectUserLocation();
  }, []);

  const detectUserLocation = async () => {
    // Mock location detection - in real app, use geolocation API
    try {
      // Simulate detection delay
      setTimeout(() => {
        const mockDetectedCountry = 'ghana'; // Default detection
        setDetectedCountry(mockDetectedCountry);
      }, 1000);
    } catch (error) {
      console.log('Location detection failed:', error);
    }
  };

  const handleCountrySelect = (countryCode) => {
    setSelectedCountry(countryCode);
  };

  const handleNext = () => {
    if (!selectedCountry) return;
    
    setIsLoading(true);
    
    // Store selected country
    localStorage.setItem('selectedCountry', selectedCountry);
    
    // Navigate to next step
    setTimeout(() => {
      navigate('/health-interests-priorities');
    }, 300);
  };

  const handleBack = () => {
    navigate('/welcome-language-selection');
  };

  const getLocalizedText = (key) => {
    const texts = {
      title: {
        en: 'Select Your Country',
        tw: 'Yi wo ɔman',
        yo: 'Yan Orilẹ-ede Rẹ',
        sw: 'Chagua Nchi Yako',
        af: 'Kies Jou Land'
      },
      subtitle: {
        en: 'This helps us provide localized healthcare content and cultural considerations',
        tw: 'Eyi boa yɛn ma yɛde local akwahosan nsɛm ne amammere nsusuwii',
        yo: 'Eyi ran wa lọwọ lati pese akoonu ilera agbegbe ati awọn akiyesi aṣa',
        sw: 'Hii inatusaidia kutoa maudhui ya afya ya kijiografija na mazingatio ya kitamaduni',
        af: 'Dit help ons om gelokaliseerde gesondheidsinhoud en kulturele oorwegings te verskaf'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Progress Indicator */}
      <div className="pt-8 px-4">
        <ProgressIndicator currentStep={2} totalSteps={5} />
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center space-y-4 mb-8">
          <h1 className="text-3xl font-bold text-text-primary font-heading">
            {getLocalizedText('title')}
          </h1>
          <p className="text-text-secondary leading-relaxed">
            {getLocalizedText('subtitle')}
          </p>
        </div>

        {/* Location Detection */}
        <LocationDetection 
          detectedCountry={detectedCountry}
          onSelect={handleCountrySelect}
          selectedLanguage={selectedLanguage}
        />

        {/* Country Grid */}
        <CountryGrid
          selectedCountry={selectedCountry}
          onCountrySelect={handleCountrySelect}
          selectedLanguage={selectedLanguage}
        />

        {/* Help Section */}
        <HelpSection selectedLanguage={selectedLanguage} />

        {/* Navigation Controls */}
        <NavigationControls
          onBack={handleBack}
          onNext={handleNext}
          isNextDisabled={!selectedCountry}
          isLoading={isLoading}
          selectedLanguage={selectedLanguage}
        />
      </div>
    </div>
  );
};

export default CountryRegionalSelection;