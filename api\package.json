{"name": "voicehealth-secure-api", "version": "1.0.0", "description": "Secure backend API for VoiceHealth AI - handles sensitive operations", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "security-audit": "npm audit && snyk test"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "form-data": "^4.0.0", "helmet": "^6.1.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.40.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "snyk": "^1.1161.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}, "keywords": ["healthcare", "api", "security", "hipaa", "payments", "backend"], "author": "VoiceHealth AI Team", "license": "PROPRIETARY", "repository": {"type": "git", "url": "private"}}