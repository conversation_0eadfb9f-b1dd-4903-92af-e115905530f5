import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SecurityControls = ({
  encryptionEnabled = true,
  localProcessing = false,
  dataRetentionDays = 30,
  sessionToken = null,
  onEncryptionToggle = () => {},
  onLocalProcessingToggle = () => {},
  onDataRetentionChange = () => {},
  onSessionCleanup = () => {},
  isVisible = false,
  onToggleVisibility = () => {},
  className = ''
}) => {
  const [showTokenDetails, setShowTokenDetails] = useState(false);
  const [showDataPolicy, setShowDataPolicy] = useState(false);

  const formatToken = (token) => {
    if (!token) return 'Not generated';
    return `${token.slice(0, 8)}...${token.slice(-8)}`;
  };

  const getEncryptionStatus = () => {
    return encryptionEnabled ? 'End-to-End Encrypted' : 'Not Encrypted';
  };

  const getDataRetentionText = () => {
    if (dataRetentionDays === 0) return 'No retention (Immediate deletion)';
    if (dataRetentionDays === 1) return '1 day';
    if (dataRetentionDays < 30) return `${dataRetentionDays} days`;
    if (dataRetentionDays === 30) return '1 month (HIPAA compliant)';
    if (dataRetentionDays === 90) return '3 months';
    if (dataRetentionDays === 365) return '1 year';
    return `${dataRetentionDays} days`;
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <Icon name="Shield" size={20} color="var(--color-text-primary)" />
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              Security & Privacy
            </h3>
            <p className="text-sm text-text-secondary">
              HIPAA-compliant data protection
            </p>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleVisibility}
          iconName={isVisible ? "ChevronUp" : "ChevronDown"}
        >
          {isVisible ? 'Hide' : 'Show'}
        </Button>
      </div>

      {isVisible && (
        <>
          {/* Main Security Status */}
          <div className="p-4 space-y-4">
            {/* Encryption Status */}
            <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Icon 
                  name={encryptionEnabled ? "Lock" : "Unlock"} 
                  size={18} 
                  className={encryptionEnabled ? "text-success-600" : "text-error-600"} 
                />
                <div>
                  <div className="text-sm font-medium text-text-primary">
                    Data Encryption
                  </div>
                  <div className={`text-xs ${encryptionEnabled ? 'text-success-600' : 'text-error-600'}`}>
                    {getEncryptionStatus()}
                  </div>
                </div>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={encryptionEnabled}
                  onChange={(e) => onEncryptionToggle(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-success-500"></div>
              </label>
            </div>

            {/* Local Processing */}
            <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Icon 
                  name={localProcessing ? "Cpu" : "Cloud"} 
                  size={18} 
                  className={localProcessing ? "text-primary-600" : "text-secondary-600"} 
                />
                <div>
                  <div className="text-sm font-medium text-text-primary">
                    Local Processing
                  </div>
                  <div className="text-xs text-text-secondary">
                    {localProcessing ? 'Process audio locally' : 'Process in cloud'}
                  </div>
                </div>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localProcessing}
                  onChange={(e) => onLocalProcessingToggle(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
              </label>
            </div>

            {/* Data Retention */}
            <div className="p-3 bg-secondary-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <Icon name="Clock" size={18} className="text-text-secondary" />
                <div>
                  <div className="text-sm font-medium text-text-primary">
                    Data Retention Policy
                  </div>
                  <div className="text-xs text-text-secondary">
                    {getDataRetentionText()}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="365"
                  step="1"
                  value={dataRetentionDays}
                  onChange={(e) => onDataRetentionChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-secondary-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-text-muted">
                  <span>Immediate</span>
                  <span>30 days</span>
                  <span>1 year</span>
                </div>
              </div>
            </div>

            {/* Session Token */}
            {sessionToken && (
              <div className="p-3 bg-secondary-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <Icon name="Key" size={18} className="text-text-secondary" />
                    <div>
                      <div className="text-sm font-medium text-text-primary">
                        Session Token
                      </div>
                      <div className="text-xs text-text-secondary">
                        Secure session identifier
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTokenDetails(!showTokenDetails)}
                    iconName={showTokenDetails ? "EyeOff" : "Eye"}
                  />
                </div>
                
                <div className="bg-surface border rounded p-2 font-mono text-xs">
                  {showTokenDetails ? sessionToken : formatToken(sessionToken)}
                </div>
              </div>
            )}
          </div>

          {/* Security Actions */}
          <div className="border-t border-border p-4">
            <div className="space-y-3">
              <Button
                variant="outline"
                onClick={onSessionCleanup}
                iconName="Trash2"
                iconPosition="left"
                size="sm"
                className="w-full"
              >
                Clear Session Data
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => setShowDataPolicy(!showDataPolicy)}
                iconName="FileText"
                iconPosition="left"
                size="sm"
                className="w-full"
              >
                View Data Policy
              </Button>
            </div>
          </div>

          {/* Data Policy Details */}
          {showDataPolicy && (
            <div className="border-t border-border p-4 bg-blue-50">
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-blue-800">
                  HIPAA-Compliant Data Protection
                </h4>
                
                <div className="text-xs text-blue-700 space-y-2">
                  <div className="flex items-start space-x-2">
                    <Icon name="CheckCircle" size={12} className="text-blue-600 mt-0.5" />
                    <span>All audio data is encrypted during transmission using AES-256</span>
                  </div>
                  
                  <div className="flex items-start space-x-2">
                    <Icon name="CheckCircle" size={12} className="text-blue-600 mt-0.5" />
                    <span>Session recordings are stored with enterprise-grade security</span>
                  </div>
                  
                  <div className="flex items-start space-x-2">
                    <Icon name="CheckCircle" size={12} className="text-blue-600 mt-0.5" />
                    <span>Data retention follows your selected policy (default: 30 days)</span>
                  </div>
                  
                  <div className="flex items-start space-x-2">
                    <Icon name="CheckCircle" size={12} className="text-blue-600 mt-0.5" />
                    <span>Access logs are maintained for audit compliance</span>
                  </div>
                  
                  <div className="flex items-start space-x-2">
                    <Icon name="CheckCircle" size={12} className="text-blue-600 mt-0.5" />
                    <span>You can request data deletion at any time</span>
                  </div>
                </div>
                
                <div className="pt-2 border-t border-blue-200">
                  <p className="text-xs text-blue-600">
                    For questions about data handling, contact our privacy <NAME_EMAIL>
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Security Status Indicators */}
          <div className="border-t border-border p-4 bg-green-50">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Shield" size={16} className="text-success-600" />
              <span className="text-sm font-medium text-success-800">
                Security Status: Active
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center space-x-1">
                <Icon name="Check" size={12} className="text-success-600" />
                <span className="text-success-700">SSL/TLS Secured</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Icon name="Check" size={12} className="text-success-600" />
                <span className="text-success-700">HIPAA Compliant</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Icon name="Check" size={12} className="text-success-600" />
                <span className="text-success-700">SOC 2 Certified</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Icon name="Check" size={12} className="text-success-600" />
                <span className="text-success-700">Audit Ready</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SecurityControls;