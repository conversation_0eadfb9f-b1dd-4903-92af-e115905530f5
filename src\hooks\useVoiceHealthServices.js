/**
 * Unified hook for VoiceHealth AI core services
 * Integrates Speech Engine, AI Orchestrator, and Triage System
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import SpeechEngine from '../services/speechEngine';
import AIOrchestrator from '../services/aiOrchestrator';
import ManchesterTriageSystem from '../services/manchesterTriage';
import healthCheck from '../services/healthCheck';

export const useVoiceHealthServices = (options = {}) => {
  const [services, setServices] = useState({
    speech: null,
    ai: null,
    triage: null,
    health: null
  });

  const [status, setStatus] = useState({
    initialized: false,
    loading: true,
    error: null,
    healthStatus: 'unknown'
  });

  const [session, setSession] = useState({
    id: null,
    messages: [],
    transcript: '',
    triageResult: null,
    aiResponse: null
  });

  const servicesRef = useRef();

  /**
   * Initialize all core services
   */
  const initializeServices = useCallback(async () => {
    try {
      setStatus(prev => ({ ...prev, loading: true, error: null }));

      // Initialize services
      const speechEngine = new SpeechEngine({
        quality: options.speechQuality || 'high',
        enableVAD: options.enableVAD !== false,
        enableNoiseReduction: options.enableNoiseReduction !== false,
        ...options.speechOptions
      });

      const aiOrchestrator = new AIOrchestrator();
      const triageSystem = new ManchesterTriageSystem();
      
      // Set up speech engine event listeners
      speechEngine.on('transcript', handleTranscript);
      speechEngine.on('error', handleSpeechError);
      speechEngine.on('connectionStatus', handleConnectionStatus);

      const serviceInstances = {
        speech: speechEngine,
        ai: aiOrchestrator,
        triage: triageSystem,
        health: healthCheck
      };

      servicesRef.current = serviceInstances;
      setServices(serviceInstances);

      // Initialize session
      const sessionId = crypto.randomUUID();
      setSession(prev => ({ ...prev, id: sessionId }));

      // Perform health check
      const healthResult = await healthCheck.performHealthCheck();
      setStatus({
        initialized: true,
        loading: false,
        error: null,
        healthStatus: healthResult.status
      });

      return serviceInstances;

    } catch (error) {
      console.error('Failed to initialize VoiceHealth services:', error);
      setStatus({
        initialized: false,
        loading: false,
        error: error.message,
        healthStatus: 'unhealthy'
      });
      throw error;
    }
  }, [options]);

  /**
   * Handle speech transcript updates
   */
  const handleTranscript = useCallback((transcript) => {
    setSession(prev => ({
      ...prev,
      transcript: transcript.text || transcript
    }));
  }, []);

  /**
   * Handle speech engine errors
   */
  const handleSpeechError = useCallback((error) => {
    console.error('Speech engine error:', error);
    setStatus(prev => ({ ...prev, error: error.message }));
  }, []);

  /**
   * Handle connection status changes
   */
  const handleConnectionStatus = useCallback((status) => {
    console.log('Speech engine connection status:', status);
  }, []);

  /**
   * Start voice consultation session
   */
  const startConsultation = useCallback(async (patientData = {}) => {
    if (!services.speech || !status.initialized) {
      throw new Error('Services not initialized');
    }

    try {
      // Start speech recognition
      await services.speech.startRecording();
      
      // Initialize session with patient data
      setSession(prev => ({
        ...prev,
        messages: [{
          role: 'system',
          content: 'You are a medical AI assistant. Help assess the patient\'s condition and provide appropriate guidance.',
          timestamp: new Date().toISOString()
        }],
        patientData
      }));

      return session.id;

    } catch (error) {
      console.error('Failed to start consultation:', error);
      throw error;
    }
  }, [services, status, session.id]);

  /**
   * Process patient input (voice or text)
   */
  const processPatientInput = useCallback(async (input, type = 'text') => {
    if (!services.ai || !services.triage) {
      throw new Error('Services not available');
    }

    try {
      const userMessage = {
        role: 'user',
        content: input,
        type,
        timestamp: new Date().toISOString()
      };

      const updatedMessages = [...session.messages, userMessage];
      setSession(prev => ({ ...prev, messages: updatedMessages }));

      // Perform triage assessment
      const triageResult = await services.triage.performTriage({
        symptoms: input,
        region: session.patientData?.region || 'ghana',
        vitals: session.patientData?.vitals || {},
        ...session.patientData
      });

      // Get AI response
      const aiResponse = await services.ai.processConversation(
        session.id,
        updatedMessages,
        {
          priority: triageResult.triageOutcome.level <= 2 ? 'urgent' : 'balanced',
          context: { triage: triageResult }
        }
      );

      const assistantMessage = {
        role: 'assistant',
        content: aiResponse.content,
        confidence: aiResponse.confidence,
        provider: aiResponse.provider,
        timestamp: new Date().toISOString()
      };

      setSession(prev => ({
        ...prev,
        messages: [...updatedMessages, assistantMessage],
        triageResult,
        aiResponse
      }));

      return {
        aiResponse: assistantMessage,
        triageResult,
        escalationRequired: triageResult.escalationRequired
      };

    } catch (error) {
      console.error('Failed to process patient input:', error);
      throw error;
    }
  }, [services, session]);

  /**
   * Stop voice recording
   */
  const stopRecording = useCallback(async () => {
    if (services.speech) {
      await services.speech.stopRecording();
    }
  }, [services.speech]);

  /**
   * Get current health status
   */
  const getHealthStatus = useCallback(async () => {
    if (services.health) {
      const healthResult = await services.health.performHealthCheck();
      setStatus(prev => ({ ...prev, healthStatus: healthResult.status }));
      return healthResult;
    }
    return null;
  }, [services.health]);

  /**
   * Cleanup services
   */
  const cleanup = useCallback(() => {
    if (servicesRef.current?.speech) {
      servicesRef.current.speech.removeAllListeners();
      servicesRef.current.speech.disconnect();
    }
  }, []);

  // Initialize services on mount
  useEffect(() => {
    initializeServices();
    return cleanup;
  }, [initializeServices, cleanup]);

  return {
    // Service instances
    services,
    
    // Status
    status,
    
    // Session data
    session,
    
    // Methods
    startConsultation,
    processPatientInput,
    stopRecording,
    getHealthStatus,
    cleanup,
    
    // Direct service access
    speechEngine: services.speech,
    aiOrchestrator: services.ai,
    triageSystem: services.triage,
    healthCheck: services.health
  };
};

export default useVoiceHealthServices;
