name: VoiceHealth AI - Production Deployment

on:
  push:
    branches: [main]
    tags: ['v*']
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # =====================================================
  # SECURITY AND COMPLIANCE VALIDATION
  # =====================================================
  security-audit:
    name: Security Audit & Compliance Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: |
          npm audit --audit-level=high
          npm run security:audit

      - name: HIPAA compliance check
        run: npm run compliance:hipaa

      - name: Regional compliance check
        run: npm run compliance:regional

      - name: Vulnerability scanning
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: 'security-audit-results.sarif'

      - name: Upload security results
        uses: actions/upload-artifact@v4
        with:
          name: security-audit-results
          path: security-audit-results.sarif

  # =====================================================
  # COMPREHENSIVE TESTING SUITE
  # =====================================================
  test-suite:
    name: Comprehensive Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-type: [unit, integration, e2e, performance, cultural]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test environment
        run: |
          cp .env.test .env
          npm run db:setup:test

      - name: Run ${{ matrix.test-type }} tests
        run: |
          case "${{ matrix.test-type }}" in
            "unit")
              npm run test:unit -- --coverage
              ;;
            "integration")
              npm run test:integration
              ;;
            "e2e")
              npm run test:e2e
              ;;
            "performance")
              npm run test:performance
              ;;
            "cultural")
              npm run test:cultural
              ;;
          esac

      - name: Validate emergency response times
        if: matrix.test-type == 'performance'
        run: |
          npm run test:emergency-performance
          if [ $? -ne 0 ]; then
            echo "❌ Emergency response time validation failed"
            exit 1
          fi

      - name: Upload test results
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.test-type }}
          path: |
            coverage/
            test-results/
            performance-results/

  # =====================================================
  # BUILD AND CONTAINERIZATION
  # =====================================================
  build:
    name: Build and Containerize
    runs-on: ubuntu-latest
    needs: [security-audit, test-suite]
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: |
          npm run build
          npm run build:production

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ steps.meta.outputs.tags }}
          format: spdx-json
          output-file: sbom.spdx.json

      - name: Upload SBOM
        uses: actions/upload-artifact@v4
        with:
          name: sbom
          path: sbom.spdx.json

  # =====================================================
  # STAGING DEPLOYMENT
  # =====================================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.voicehealth.ai
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region eu-west-1 --name voicehealth-staging

      - name: Deploy to staging
        run: |
          # Update deployment with new image
          kubectl set image deployment/voicehealth-ai \
            voicehealth-ai=${{ needs.build.outputs.image-tag }} \
            -n staging

          # Wait for rollout to complete
          kubectl rollout status deployment/voicehealth-ai -n staging --timeout=600s

      - name: Run staging health checks
        run: |
          # Wait for pods to be ready
          kubectl wait --for=condition=ready pod -l app=voicehealth-ai -n staging --timeout=300s

          # Run health checks
          kubectl exec -n staging deployment/voicehealth-ai -- npm run health:check

          # Validate emergency response times
          kubectl exec -n staging deployment/voicehealth-ai -- npm run test:emergency-performance

      - name: Run staging smoke tests
        run: |
          # Run comprehensive smoke tests
          kubectl exec -n staging deployment/voicehealth-ai -- npm run test:smoke

          # Test cultural adaptations
          kubectl exec -n staging deployment/voicehealth-ai -- npm run test:cultural:smoke

          # Test regional configurations
          kubectl exec -n staging deployment/voicehealth-ai -- npm run test:regional:smoke

  # =====================================================
  # PRODUCTION DEPLOYMENT
  # =====================================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: https://voicehealth.ai
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: eu-west-1

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region eu-west-1 --name voicehealth-production

      - name: Pre-deployment security scan
        run: |
          # Scan the image for vulnerabilities
          docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
            aquasec/trivy image ${{ needs.build.outputs.image-tag }}

      - name: Blue-Green Deployment
        run: |
          # Create new deployment (green)
          kubectl apply -f k8s/production/deployment-green.yaml
          
          # Update green deployment with new image
          kubectl set image deployment/voicehealth-ai-green \
            voicehealth-ai=${{ needs.build.outputs.image-tag }} \
            -n production

          # Wait for green deployment to be ready
          kubectl rollout status deployment/voicehealth-ai-green -n production --timeout=600s

      - name: Production health validation
        run: |
          # Wait for pods to be ready
          kubectl wait --for=condition=ready pod -l app=voicehealth-ai-green -n production --timeout=300s

          # Run comprehensive health checks
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run health:check:comprehensive

          # Validate emergency response times (critical)
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run test:emergency-performance:production

          # Validate HIPAA compliance
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run compliance:hipaa:validate

          # Test all regional configurations
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run test:regional:all

      - name: Canary traffic routing
        run: |
          # Route 5% of traffic to green deployment
          kubectl patch service voicehealth-ai -n production -p '{"spec":{"selector":{"version":"green","weight":"5"}}}'
          
          # Monitor for 5 minutes
          sleep 300
          
          # Check error rates and performance
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run monitoring:canary:check

      - name: Full traffic switch
        run: |
          # Route all traffic to green deployment
          kubectl patch service voicehealth-ai -n production -p '{"spec":{"selector":{"app":"voicehealth-ai-green"}}}'
          
          # Update ingress to point to green service
          kubectl patch ingress voicehealth-ai -n production -p '{"spec":{"rules":[{"host":"voicehealth.ai","http":{"paths":[{"path":"/","pathType":"Prefix","backend":{"service":{"name":"voicehealth-ai-green","port":{"number":3000}}}}]}}]}}'

      - name: Post-deployment validation
        run: |
          # Wait for traffic to stabilize
          sleep 120
          
          # Run full production validation suite
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run test:production:full

          # Validate all emergency protocols
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run test:emergency:all

          # Check cultural adaptation effectiveness
          kubectl exec -n production deployment/voicehealth-ai-green -- npm run test:cultural:production

      - name: Cleanup old deployment
        run: |
          # Scale down blue deployment
          kubectl scale deployment voicehealth-ai-blue --replicas=0 -n production
          
          # Keep blue deployment for potential rollback
          echo "Blue deployment scaled to 0 but preserved for rollback"

      - name: Update monitoring and alerting
        run: |
          # Update monitoring dashboards
          kubectl apply -f k8s/monitoring/production-dashboards.yaml
          
          # Update alert rules for production
          kubectl apply -f k8s/monitoring/production-alerts.yaml

  # =====================================================
  # POST-DEPLOYMENT VALIDATION
  # =====================================================
  post-deployment-validation:
    name: Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup monitoring tools
        run: |
          # Install monitoring and testing tools
          npm install -g artillery newman

      - name: Load testing
        run: |
          # Run load tests against production
          artillery run tests/load/production-load-test.yml

      - name: API testing
        run: |
          # Run comprehensive API tests
          newman run tests/api/production-api-tests.json \
            --environment tests/api/production-environment.json

      - name: Cultural adaptation testing
        run: |
          # Test cultural adaptations for all regions
          for country in GH KE NG ZA ET; do
            echo "Testing cultural adaptations for $country"
            newman run tests/cultural/cultural-adaptation-$country.json
          done

      - name: Emergency protocol validation
        run: |
          # Validate emergency protocols in production
          newman run tests/emergency/emergency-protocols.json \
            --environment tests/emergency/production-environment.json

      - name: Performance monitoring setup
        run: |
          # Setup continuous performance monitoring
          curl -X POST "https://api.voicehealth.ai/monitoring/setup" \
            -H "Authorization: Bearer ${{ secrets.MONITORING_API_KEY }}" \
            -d '{"environment":"production","monitoring_level":"comprehensive"}'

      - name: Notification
        if: success()
        run: |
          # Send success notification
          curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{
              "text": "🚀 VoiceHealth AI Production Deployment Successful",
              "attachments": [{
                "color": "good",
                "fields": [
                  {"title": "Version", "value": "${{ github.ref_name }}", "short": true},
                  {"title": "Environment", "value": "Production", "short": true},
                  {"title": "Emergency Compliance", "value": "✅ Validated", "short": true},
                  {"title": "Cultural Adaptations", "value": "✅ All Regions", "short": true}
                ]
              }]
            }'

      - name: Failure notification
        if: failure()
        run: |
          # Send failure notification
          curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{
              "text": "❌ VoiceHealth AI Production Deployment Failed",
              "attachments": [{
                "color": "danger",
                "fields": [
                  {"title": "Version", "value": "${{ github.ref_name }}", "short": true},
                  {"title": "Environment", "value": "Production", "short": true},
                  {"title": "Status", "value": "Deployment Failed", "short": true},
                  {"title": "Action Required", "value": "Immediate Investigation", "short": true}
                ]
              }]
            }'

# =====================================================
# ROLLBACK WORKFLOW
# =====================================================
  rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    environment:
      name: production
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: eu-west-1

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region eu-west-1 --name voicehealth-production

      - name: Emergency rollback
        run: |
          # Switch traffic back to blue deployment
          kubectl patch service voicehealth-ai -n production -p '{"spec":{"selector":{"app":"voicehealth-ai-blue"}}}'
          
          # Scale up blue deployment
          kubectl scale deployment voicehealth-ai-blue --replicas=5 -n production
          
          # Wait for blue deployment to be ready
          kubectl rollout status deployment/voicehealth-ai-blue -n production --timeout=300s

      - name: Validate rollback
        run: |
          # Validate emergency response times after rollback
          kubectl exec -n production deployment/voicehealth-ai-blue -- npm run test:emergency-performance
          
          # Run health checks
          kubectl exec -n production deployment/voicehealth-ai-blue -- npm run health:check

      - name: Rollback notification
        run: |
          curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{
              "text": "🔄 VoiceHealth AI Emergency Rollback Completed",
              "attachments": [{
                "color": "warning",
                "fields": [
                  {"title": "Action", "value": "Emergency Rollback", "short": true},
                  {"title": "Environment", "value": "Production", "short": true},
                  {"title": "Status", "value": "✅ Rollback Successful", "short": true},
                  {"title": "Next Steps", "value": "Investigate deployment issue", "short": true}
                ]
              }]
            }'
