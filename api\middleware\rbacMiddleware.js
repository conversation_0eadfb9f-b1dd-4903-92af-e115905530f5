/**
 * RBAC MIDDLEWARE FOR BACKEND API
 * 
 * This middleware enforces role-based access control on all API endpoints,
 * ensuring that only authorized users can access medical data and operations.
 * 
 * FEATURES:
 * - Endpoint-level permission checking
 * - Role-based access control
 * - Emergency access protocol support
 * - Comprehensive audit logging
 * - Rate limiting integration
 * - Context-aware authorization
 * 
 * SECURITY REQUIREMENTS:
 * - All medical data endpoints must be protected
 * - Emergency access must be logged and justified
 * - Failed authorization attempts must be tracked
 * - Rate limiting for unauthorized access attempts
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Role definitions with permissions
const ROLE_DEFINITIONS = {
  patient: {
    role: 'patient',
    permissions: [
      { resource: 'medical_conditions', action: 'create', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medical_conditions', action: 'read', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medical_conditions', action: 'update', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medical_conditions', action: 'delete', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'create', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'read', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'update', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'medications', action: 'delete', conditions: [{ field: 'user_id', operator: 'equals', value: 'self' }] },
      { resource: 'consultations', action: 'create', conditions: [{ field: 'patient_id', operator: 'equals', value: 'self' }] },
      { resource: 'consultations', action: 'read', conditions: [{ field: 'patient_id', operator: 'equals', value: 'self' }] },
      { resource: 'consultations', action: 'emergency_stop', conditions: [{ field: 'patient_id', operator: 'equals', value: 'self' }] },
      { resource: 'audio_consultations', action: 'emergency_stop', conditions: [{ field: 'patient_id', operator: 'equals', value: 'self' }] }
    ],
    emergency_override: false
  },
  healthcare_provider: {
    role: 'healthcare_provider',
    permissions: [
      { resource: 'medical_conditions', action: 'read' },
      { resource: 'medical_conditions', action: 'create' },
      { resource: 'medical_conditions', action: 'update' },
      { resource: 'medications', action: 'read' },
      { resource: 'medications', action: 'create' },
      { resource: 'medications', action: 'update' },
      { resource: 'consultations', action: 'create' },
      { resource: 'consultations', action: 'read' },
      { resource: 'consultations', action: 'update' },
      { resource: 'patient_profiles', action: 'read' },
      { resource: 'patient_profiles', action: 'update' }
    ],
    inherits_from: ['patient'],
    emergency_override: true
  },
  admin: {
    role: 'admin',
    permissions: [
      { resource: 'medical_conditions', action: 'create' },
      { resource: 'medical_conditions', action: 'read' },
      { resource: 'medical_conditions', action: 'update' },
      { resource: 'medical_conditions', action: 'delete' },
      { resource: 'medications', action: 'create' },
      { resource: 'medications', action: 'read' },
      { resource: 'medications', action: 'update' },
      { resource: 'medications', action: 'delete' },
      { resource: 'consultations', action: 'create' },
      { resource: 'consultations', action: 'read' },
      { resource: 'consultations', action: 'update' },
      { resource: 'consultations', action: 'delete' },
      { resource: 'patient_profiles', action: 'create' },
      { resource: 'patient_profiles', action: 'read' },
      { resource: 'patient_profiles', action: 'update' },
      { resource: 'patient_profiles', action: 'delete' },
      { resource: 'audit_logs', action: 'read' },
      { resource: 'system_settings', action: 'read' },
      { resource: 'system_settings', action: 'update' },
      { resource: 'user_management', action: 'create' },
      { resource: 'user_management', action: 'read' },
      { resource: 'user_management', action: 'update' },
      { resource: 'user_management', action: 'delete' }
    ],
    inherits_from: ['healthcare_provider'],
    emergency_override: true
  },
  emergency_responder: {
    role: 'emergency_responder',
    permissions: [
      { resource: 'medical_conditions', action: 'read' },
      { resource: 'medications', action: 'read' },
      { resource: 'consultations', action: 'read' },
      { resource: 'consultations', action: 'create' },
      { resource: 'consultations', action: 'emergency_stop' },
      { resource: 'patient_profiles', action: 'read' },
      { resource: 'emergency_protocols', action: 'emergency_access' },
      { resource: 'audio_consultations', action: 'emergency_stop' }
    ],
    emergency_override: true
  }
};

// Endpoint to resource mapping
const ENDPOINT_RESOURCES = {
  '/api/medical-conditions': 'medical_conditions',
  '/api/medications': 'medications',
  '/api/consultations': 'consultations',
  '/api/patient-profiles': 'patient_profiles',
  '/api/audit-logs': 'audit_logs',
  '/api/users': 'user_management',
  '/api/settings': 'system_settings',
  '/api/speech-to-text': 'audio_consultations',
  '/api/text-to-speech': 'audio_consultations',
  '/api/ai-chat': 'audio_consultations',
  '/api/emergency-stop': 'emergency_protocols'
};

// HTTP method to action mapping
const METHOD_ACTIONS = {
  'GET': 'read',
  'POST': 'create',
  'PUT': 'update',
  'PATCH': 'update',
  'DELETE': 'delete'
};

/**
 * Get user permissions based on role and inheritance
 */
function getUserPermissions(userRole) {
  const roleDefinition = ROLE_DEFINITIONS[userRole];
  if (!roleDefinition) {
    console.warn(`Unknown role: ${userRole}, defaulting to patient permissions`);
    return ROLE_DEFINITIONS.patient.permissions;
  }

  let allPermissions = [...roleDefinition.permissions];

  // Add inherited permissions
  if (roleDefinition.inherits_from) {
    for (const inheritedRole of roleDefinition.inherits_from) {
      const inheritedDefinition = ROLE_DEFINITIONS[inheritedRole];
      if (inheritedDefinition) {
        allPermissions = [...allPermissions, ...inheritedDefinition.permissions];
      }
    }
  }

  // Remove duplicates
  const uniquePermissions = allPermissions.filter((permission, index, self) =>
    index === self.findIndex(p => 
      p.resource === permission.resource && p.action === permission.action
    )
  );

  return uniquePermissions;
}

/**
 * Evaluate permission conditions
 */
function evaluatePermissionConditions(permission, user, context) {
  if (!permission.conditions || permission.conditions.length === 0) {
    return true;
  }

  return permission.conditions.every(condition => {
    switch (condition.field) {
      case 'user_id':
        if (condition.operator === 'equals' && condition.value === 'self') {
          return context.userId === user.id || context.resourceOwnerId === user.id;
        }
        return evaluateCondition(context.userId, condition.operator, condition.value);

      case 'patient_id':
        if (condition.operator === 'equals' && condition.value === 'self') {
          return context.patientId === user.id;
        }
        return evaluateCondition(context.patientId, condition.operator, condition.value);

      default:
        console.warn(`Unknown condition field: ${condition.field}`);
        return false;
    }
  });
}

/**
 * Evaluate a single condition
 */
function evaluateCondition(fieldValue, operator, expectedValue) {
  switch (operator) {
    case 'equals':
      return fieldValue === expectedValue;
    case 'not_equals':
      return fieldValue !== expectedValue;
    case 'in':
      return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
    case 'not_in':
      return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue);
    default:
      console.warn(`Unknown condition operator: ${operator}`);
      return false;
  }
}

/**
 * Check if user has permission for resource and action
 */
function hasPermission(user, resource, action, context = {}) {
  const userRole = user.user_metadata?.role || 'patient';
  const permissions = getUserPermissions(userRole);

  // Check for emergency override
  if (context.emergencyOverride) {
    const roleDefinition = ROLE_DEFINITIONS[userRole];
    if (roleDefinition?.emergency_override) {
      return true;
    }
  }

  // Find matching permission
  const matchingPermission = permissions.find(p => 
    p.resource === resource && p.action === action
  );

  if (!matchingPermission) {
    return false;
  }

  // Evaluate conditions
  return evaluatePermissionConditions(matchingPermission, user, context);
}

/**
 * Log authorization attempt
 */
async function logAuthorizationAttempt(user, resource, action, granted, reason, context) {
  try {
    await supabase
      .from('audit_logs')
      .insert({
        id: crypto.randomUUID(),
        event_type: 'authorization_check',
        user_id: user?.id,
        resource_type: 'api_endpoint',
        resource_id: `${resource}_${action}`,
        action: 'authorization_check',
        success: granted,
        metadata: {
          user_role: user?.user_metadata?.role,
          resource,
          action,
          reason,
          context,
          ip_address: context.ip_address,
          user_agent: context.user_agent
        },
        timestamp: new Date().toISOString()
      });
  } catch (error) {
    console.error('Failed to log authorization attempt:', error);
  }
}

/**
 * RBAC Middleware
 */
const rbacMiddleware = (options = {}) => {
  const {
    skipPaths = ['/api/health', '/api/auth'],
    emergencyBypass = false
  } = options;

  return async (req, res, next) => {
    try {
      // Skip RBAC for certain paths
      if (skipPaths.some(path => req.path.startsWith(path))) {
        return next();
      }

      // Get user from previous authentication middleware
      const user = req.user;
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // Determine resource and action from request
      const resource = ENDPOINT_RESOURCES[req.path] || 'unknown';
      const action = METHOD_ACTIONS[req.method] || 'unknown';

      if (resource === 'unknown' || action === 'unknown') {
        console.warn(`Unknown endpoint mapping: ${req.method} ${req.path}`);
        return res.status(403).json({
          success: false,
          error: 'Endpoint not configured for RBAC'
        });
      }

      // Build context from request
      const context = {
        userId: req.params.userId || req.body.user_id || req.query.user_id,
        patientId: req.params.patientId || req.body.patient_id || req.query.patient_id,
        resourceOwnerId: req.params.id || req.body.id,
        emergencyOverride: req.headers['x-emergency-override'] === 'true' && emergencyBypass,
        ip_address: req.ip || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      };

      // Check permission
      const hasAccess = hasPermission(user, resource, action, context);

      // Log authorization attempt
      await logAuthorizationAttempt(
        user,
        resource,
        action,
        hasAccess,
        hasAccess ? 'granted' : 'denied',
        context
      );

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          details: {
            required_resource: resource,
            required_action: action,
            user_role: user.user_metadata?.role || 'patient'
          }
        });
      }

      // Add authorization context to request for use in handlers
      req.authContext = {
        user,
        resource,
        action,
        context,
        emergencyOverride: context.emergencyOverride
      };

      next();
    } catch (error) {
      console.error('RBAC middleware error:', error);
      
      // Log error
      if (req.user) {
        await logAuthorizationAttempt(
          req.user,
          'unknown',
          'unknown',
          false,
          `middleware_error: ${error.message}`,
          { ip_address: req.ip, user_agent: req.headers['user-agent'] }
        );
      }

      res.status(500).json({
        success: false,
        error: 'Authorization check failed'
      });
    }
  };
};

module.exports = {
  rbacMiddleware,
  hasPermission,
  getUserPermissions,
  ROLE_DEFINITIONS
};
