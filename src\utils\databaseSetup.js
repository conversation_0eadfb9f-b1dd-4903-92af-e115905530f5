import { supabase } from './supabaseClient';
import migrationRunner from './migrationRunner';

/**
 * Database Setup Utility for VoiceHealth AI
 * Provides functions to verify and setup database tables
 */

class DatabaseSetup {
  constructor() {
    this.requiredTables = [
      'user_profiles',
      'consultation_sessions', 
      'subscription_plans',
      'user_subscriptions',
      'payment_transactions',
      'user_payment_methods',
      'payment_webhooks'
    ];
  }

  /**
   * Enhanced error handling for database operations
   */
  async handleDatabaseError(error, context = '') {
    const errorInfo = {
      code: error.code,
      message: error.message,
      details: error.details,
      hint: error.hint,
      context
    };

    console.error('Database Setup Error:', errorInfo);

    // Handle specific PostgreSQL error codes
    if (error.code === '42P01') { // relation does not exist
      return {
        type: 'missing_table',
        message: 'Database table does not exist. Migration required.',
        needsMigration: true,
        autoFixAvailable: true,
        error: errorInfo
      };
    }

    if (error.code === '42501') { // insufficient privilege
      return {
        type: 'permission_error',
        message: 'Insufficient database privileges. Check your Supabase project settings.',
        needsPermissionFix: true,
        autoFixAvailable: false,
        error: errorInfo
      };
    }

    if (error.code === 'PGRST204') { // PostgREST table not found
      return {
        type: 'missing_table',
        message: 'Table not found in PostgREST schema. Migration required.',
        needsMigration: true,
        autoFixAvailable: true,
        error: errorInfo
      };
    }

    return {
      type: 'unknown_error',
      message: error.message || 'Unknown database error occurred',
      autoFixAvailable: true,
      error: errorInfo
    };
  }

  /**
   * Check if all required tables exist with enhanced error handling
   */
  async verifyDatabaseSetup() {
    try {
      const results = [];
      const errors = [];

      for (const table of this.requiredTables) {
        try {
          const exists = await this.checkTableExists(table);
          results.push(exists);
        } catch (error) {
          const handledError = await this.handleDatabaseError(error, `verifyDatabaseSetup:${table}`);
          errors.push({
            table,
            error: handledError
          });
          results.push(false);
        }
      }

      const missingTables = this.requiredTables.filter((table, index) => !results[index]);
      
      return {
        success: missingTables.length === 0,
        missingTables,
        errors,
        status: missingTables.length === 0 
          ? 'Database setup complete' 
          : `Missing tables: ${missingTables.join(', ')}`,
        needsMigration: missingTables.length > 0,
        autoFixAvailable: missingTables.length > 0
      };
    } catch (error) {
      const handledError = await this.handleDatabaseError(error, 'verifyDatabaseSetup');
      return {
        success: false,
        error: handledError.message,
        status: 'Database verification failed',
        errorDetails: handledError
      };
    }
  }

  /**
   * Enhanced table existence check with better error handling
   */
  async checkTableExists(tableName) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      // If no error, table exists
      if (!error) return true;

      // Handle specific error cases
      if (error.code === '42P01' || error.message?.includes('does not exist')) {
        return false;
      }

      if (error.code === 'PGRST204') {
        return false;
      }

      // For permission errors, assume table exists but is inaccessible
      if (error.code === '42501') {
        console.warn(`Table ${tableName} exists but has permission issues:`, error);
        return true;
      }

      // For other errors, log and return false to trigger migration
      console.warn(`Unknown error checking table ${tableName}:`, error);
      return false;
    } catch (error) {
      console.error(`Error checking table ${tableName}:`, error);
      return false;
    }
  }

  /**
   * Enhanced subscription plans verification
   */
  async verifySubscriptionPlans() {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('id, name, is_active')
        .eq('is_active', true);

      if (error) {
        const handledError = await this.handleDatabaseError(error, 'verifySubscriptionPlans');
        return {
          success: false,
          error: handledError.message,
          tableExists: false,
          errorDetails: handledError
        };
      }

      return {
        success: true,
        tableExists: true,
        planCount: data?.length || 0,
        hasPlans: (data?.length || 0) > 0,
        plans: data || []
      };
    } catch (error) {
      const handledError = await this.handleDatabaseError(error, 'verifySubscriptionPlans');
      return {
        success: false,
        error: handledError.message,
        tableExists: false,
        errorDetails: handledError
      };
    }
  }

  /**
   * Enhanced automatic database setup with multiple strategies
   */
  async autoFixDatabaseSetup() {
    try {
      console.log('🔧 Starting automatic database setup...');
      
      // Check current status
      const verification = await this.verifyDatabaseSetup();
      
      if (verification.success) {
        return {
          success: true,
          message: 'Database setup is already complete',
          action: 'none'
        };
      }

      console.log('📋 Missing tables detected:', verification.missingTables);

      // Strategy 1: Quick fix for subscription_plans only
      if (verification.missingTables.length === 1 && verification.missingTables[0] === 'subscription_plans') {
        console.log('🚀 Applying quick fix for subscription_plans table...');
        const result = await migrationRunner.quickFixSubscriptionPlans();
        
        if (result.success) {
          console.log('✅ Quick fix applied successfully');
          return {
            success: true,
            message: 'subscription_plans table created and populated',
            action: 'quick_fix',
            details: result
          };
        } else {
          console.warn('❌ Quick fix failed, trying full migration:', result.error);
        }
      }

      // Strategy 2: Run full migration
      console.log('🚀 Running full database migration...');
      const migrationResult = await migrationRunner.executeAllMigrations();
      
      if (migrationResult.success) {
        // Wait briefly for database to sync
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Verify the fix worked
        const postVerification = await this.verifyDatabaseSetup();
        
        return {
          success: postVerification.success,
          message: postVerification.success 
            ? 'Database setup completed successfully' 
            : 'Migration completed but some issues remain',
          action: 'full_migration',
          migrationResults: migrationResult.results,
          remainingIssues: postVerification.missingTables,
          details: migrationResult
        };
      }

      // Strategy 3: Emergency fallback - create essential tables
      if (!migrationResult.success && verification.missingTables.includes('subscription_plans')) {
        console.log('🆘 Attempting emergency fallback for subscription_plans...');
        const fallbackResult = await this.emergencyCreateSubscriptionPlans();
        
        if (fallbackResult.success) {
          return {
            success: true,
            message: 'Emergency fallback successful - subscription_plans created',
            action: 'emergency_fallback',
            details: fallbackResult
          };
        }
      }

      return {
        success: false,
        error: 'All migration strategies failed',
        migrationResults: migrationResult.results || [],
        lastAttempt: 'full_migration'
      };
    } catch (error) {
      console.error('❌ Auto-fix failed:', error);
      const handledError = await this.handleDatabaseError(error, 'autoFixDatabaseSetup');
      return {
        success: false,
        error: handledError.message,
        errorDetails: handledError
      };
    }
  }

  /**
   * Emergency fallback to create subscription_plans table
   */
  async emergencyCreateSubscriptionPlans() {
    try {
      console.log('🆘 Running emergency subscription_plans creation...');
      
      // Use raw SQL approach with multiple attempts
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.subscription_plans (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          description TEXT,
          price_ngn DECIMAL(10,2) NOT NULL,
          price_usd DECIMAL(10,2) NOT NULL,
          duration_days INTEGER NOT NULL,
          consultation_credits INTEGER DEFAULT 0,
          features JSONB DEFAULT '[]'::jsonb,
          is_active BOOLEAN DEFAULT true,
          is_popular BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
      `;

      // Try to create table
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql_text: createTableSQL
      });

      if (!createError) {
        // Insert default data
        const { error: insertError } = await supabase
          .from('subscription_plans')
          .insert([
            {
              name: 'Basic Plan',
              description: 'Perfect for occasional consultations',
              price_ngn: 5000.00,
              price_usd: 12.00,
              duration_days: 30,
              consultation_credits: 3,
              features: ['3 consultations per month', 'Basic AI agents', 'Text transcripts', 'Email support'],
              is_popular: false
            },
            {
              name: 'Premium Plan',
              description: 'Most popular for regular health monitoring',
              price_ngn: 12000.00,
              price_usd: 28.00,
              duration_days: 30,
              consultation_credits: 10,
              features: ['10 consultations per month', 'All AI specialists', 'Audio recordings', 'Priority support', 'Health insights'],
              is_popular: true
            },
            {
              name: 'Professional Plan',
              description: 'Unlimited access for comprehensive care',
              price_ngn: 25000.00,
              price_usd: 60.00,
              duration_days: 30,
              consultation_credits: 0,
              features: ['Unlimited consultations', 'All premium features', 'Personal health dashboard', '24/7 priority support', 'Advanced analytics'],
              is_popular: false
            }
          ]);

        if (!insertError) {
          return {
            success: true,
            message: 'Emergency subscription_plans table created and populated'
          };
        } else {
          console.warn('Table created but data insertion failed:', insertError);
          return {
            success: true,
            message: 'Table created but may be empty',
            warning: insertError.message
          };
        }
      }

      return {
        success: false,
        error: createError?.message || 'Failed to create table'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get database setup status for debugging
   */
  async getSetupStatus() {
    const verification = await this.verifyDatabaseSetup();
    const planStatus = await this.verifySubscriptionPlans();
    const migrationStatus = await migrationRunner.checkMigrationStatus();

    return {
      databaseSetup: verification,
      subscriptionPlans: planStatus,
      migrationStatus: migrationStatus.success ? migrationStatus : { error: migrationStatus.error },
      timestamp: new Date().toISOString(),
      recommendations: this.getRecommendations(verification, planStatus)
    };
  }

  /**
   * Enhanced setup recommendations
   */
  getRecommendations(dbStatus, planStatus) {
    const recommendations = [];

    if (!dbStatus.success) {
      recommendations.push({
        type: 'critical',
        message: 'Database tables are missing or inaccessible',
        action: 'Use autoFixDatabaseSetup() to run automatic migration',
        autoFixAvailable: dbStatus.autoFixAvailable,
        details: dbStatus.errors || []
      });
    }

    if (dbStatus.success && !planStatus.hasPlans) {
      recommendations.push({
        type: 'warning',
        message: 'Subscription plans table is empty',
        action: 'Re-run migration INSERT statements for default plans',
        autoFixAvailable: true
      });
    }

    if (planStatus.tableExists && planStatus.hasPlans) {
      recommendations.push({
        type: 'success',
        message: 'Database setup is complete and functional',
        action: 'No action required'
      });
    }

    // Add specific error-based recommendations
    if (dbStatus.errors?.length > 0) {
      for (const error of dbStatus.errors) {
        if (error.error.type === 'permission_error') {
          recommendations.push({
            type: 'critical',
            message: `Permission issue with table: ${error.table}`,
            action: 'Check RLS policies and database permissions in Supabase dashboard',
            autoFixAvailable: false
          });
        }
      }
    }

    return recommendations;
  }

  /**
   * Enhanced development helper with better error reporting
   */
  async logSetupStatus() {
    const status = await this.getSetupStatus();
    
    console.group('🔧 Database Setup Status');
    console.log('📊 Overall Status:', status.databaseSetup.success ? '✅ Ready' : '❌ Needs Setup');
    console.log('📋 Missing Tables:', status.databaseSetup.missingTables || 'None');
    console.log('💰 Subscription Plans:', status.subscriptionPlans.hasPlans ? '✅ Populated' : '❌ Empty');
    console.log('📈 Plan Count:', status.subscriptionPlans.planCount);
    
    // Log errors if any
    if (status.databaseSetup.errors?.length > 0) {
      console.log('❌ Errors Detected:');
      status.databaseSetup.errors.forEach(err => {
        console.log(`  - ${err.table}: ${err.error.message}`);
      });
    }
    
    if (status.recommendations.length > 0) {
      console.log('💡 Recommendations:');
      status.recommendations.forEach(rec => {
        const icon = rec.type === 'critical' ? '🚨' : rec.type === 'warning' ? '⚠️' : '✅';
        console.log(`  ${icon} ${rec.message}`);
        console.log(`     Action: ${rec.action}`);
        if (rec.autoFixAvailable) {
          console.log(`     🔧 Auto-fix available: Call autoFixDatabaseSetup()`);
        }
      });
    }
    
    console.groupEnd();
    return status;
  }

  /**
   * Enhanced diagnostic for subscription plans
   */
  async diagnoseSubscriptionPlans() {
    try {
      console.log('🔍 Diagnosing subscription_plans table...');
      
      const tableExists = await this.checkTableExists('subscription_plans');
      console.log('📋 Table exists:', tableExists);
      
      if (!tableExists) {
        return {
          tableExists: false,
          hasData: false,
          recommendation: 'Run migration to create subscription_plans table',
          autoFixAvailable: true
        };
      }

      const { data, error, count } = await supabase
        .from('subscription_plans')
        .select('*', { count: 'exact' })
        .eq('is_active', true);

      if (error) {
        console.error('❌ Error querying subscription_plans:', error);
        const handledError = await this.handleDatabaseError(error, 'diagnoseSubscriptionPlans');
        return {
          tableExists: true,
          hasData: false,
          error: handledError.message,
          errorDetails: handledError,
          recommendation: 'Check table structure and permissions',
          autoFixAvailable: handledError.autoFixAvailable
        };
      }

      console.log('📊 Active plans found:', count);
      console.log('📋 Plan data:', data);

      return {
        tableExists: true,
        hasData: count > 0,
        planCount: count,
        plans: data,
        recommendation: count > 0 ? 'Table is properly setup' : 'Insert default subscription plans',
        autoFixAvailable: count === 0
      };
    } catch (error) {
      console.error('🚨 Diagnostic failed:', error);
      const handledError = await this.handleDatabaseError(error, 'diagnoseSubscriptionPlans');
      return {
        tableExists: false,
        hasData: false,
        error: handledError.message,
        errorDetails: handledError,
        recommendation: 'Check database connection and permissions',
        autoFixAvailable: handledError.autoFixAvailable
      };
    }
  }
}

// Export singleton instance
const databaseSetup = new DatabaseSetup();
export default databaseSetup;

// Named exports for individual functions
export const {
  verifyDatabaseSetup,
  checkTableExists,
  verifySubscriptionPlans,
  getSetupStatus,
  logSetupStatus,
  autoFixDatabaseSetup,
  diagnoseSubscriptionPlans
} = databaseSetup;