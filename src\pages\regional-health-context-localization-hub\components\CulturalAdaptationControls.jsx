import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CulturalAdaptationControls = ({ country, onSettingsChange }) => {
  const [settings, setSettings] = useState({
    traditionalMedicineIntegration: 85,
    religiousConsiderations: 90,
    genderSensitivity: 95,
    ageRespectProtocols: 88,
    familyInvolvementLevel: 82,
    privacyPreferences: 77,
    communicationStyle: 'respectful',
    holisticApproach: true,
    communityHealthFocus: true,
    spiritualWellbeingIntegration: true
  });

  const [activeTab, setActiveTab] = useState('integration');

  const tabs = [
    { id: 'integration', label: 'Traditional Integration', icon: 'Leaf' },
    { id: 'communication', label: 'Communication Style', icon: 'MessageCircle' },
    { id: 'privacy', label: 'Privacy & Consent', icon: 'Shield' },
    { id: 'family', label: 'Family Dynamics', icon: 'Users' }
  ];

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);
  };

  const culturalConsiderations = {
    ghana: {
      respectElders: 'Always address elderly patients with appropriate titles and respect',
      familyDecisions: 'Family consultation may be required for major health decisions',
      traditionalBeliefs: 'Acknowledge traditional healing practices and spiritual beliefs',
      genderSensitivity: 'Be mindful of gender preferences for healthcare providers',
      communicationStyle: 'Use indirect communication style, avoid confrontational language'
    },
    nigeria: {
      tribalDiversity: 'Recognize diverse tribal customs and practices across regions',
      religiousFactors: 'Consider Islamic, Christian, and traditional religious practices',
      hierarchicalRespect: 'Maintain respect for age and social hierarchy',
      genderRoles: 'Understand traditional gender roles and expectations',
      languageNuances: 'Use appropriate local language expressions and idioms'
    },
    kenya: {
      communityOriented: 'Health decisions often involve community and family input',
      tribalDiversity: 'Respect diverse tribal customs and traditional practices',
      spiritualHealth: 'Integrate spiritual and mental wellbeing considerations',
      respectForAge: 'Show special reverence for elderly community members',
      genderSensitivity: 'Navigate traditional gender roles with cultural awareness'
    },
    'south-africa': {
      culturalDiversity: 'Navigate complex multicultural healthcare environment',
      languagePreferences: 'Offer consultations in preferred local languages',
      traditionalHealing: 'Respect traditional healing practices and sangoma consultations',
      apartheistory: 'Be sensitive to historical healthcare inequalities',
      ubuntu: 'Embrace Ubuntu philosophy - interconnectedness and community care'
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'integration':
        return (
          <div className="space-y-6">
            {/* Traditional Medicine Integration */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-text-primary">Traditional Medicine Integration</h4>
                  <p className="text-sm text-text-secondary">
                    How much should AI agents acknowledge and integrate traditional healing practices?
                  </p>
                </div>
                <span className="font-semibold text-primary-600">{settings.traditionalMedicineIntegration}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.traditionalMedicineIntegration}
                onChange={(e) => handleSettingChange('traditionalMedicineIntegration', parseInt(e.target.value))}
                className="w-full h-2 bg-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-text-secondary mt-2">
                <span>Never mention</span>
                <span>Acknowledge respectfully</span>
                <span>Fully integrate</span>
              </div>
            </div>

            {/* Religious Considerations */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-text-primary">Religious Considerations</h4>
                  <p className="text-sm text-text-secondary">
                    Integration of religious beliefs and practices in healthcare guidance
                  </p>
                </div>
                <span className="font-semibold text-success-600">{settings.religiousConsiderations}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.religiousConsiderations}
                onChange={(e) => handleSettingChange('religiousConsiderations', parseInt(e.target.value))}
                className="w-full h-2 bg-border rounded-lg appearance-none cursor-pointer slider"
              />
            </div>

            {/* Holistic Approach Toggle */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                {
                  key: 'holisticApproach',
                  label: 'Holistic Health Approach',
                  description: 'Consider mental, spiritual, and social wellbeing',
                  icon: 'Heart'
                },
                {
                  key: 'communityHealthFocus',
                  label: 'Community Health Focus',
                  description: 'Emphasize community and family health impact',
                  icon: 'Users'
                },
                {
                  key: 'spiritualWellbeingIntegration',
                  label: 'Spiritual Wellbeing',
                  description: 'Include spiritual health in recommendations',
                  icon: 'Star'
                }
              ].map((option) => (
                <div key={option.key} className="p-4 bg-surface border border-border rounded-lg">
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      <input
                        type="checkbox"
                        checked={settings[option.key]}
                        onChange={(e) => handleSettingChange(option.key, e.target.checked)}
                        className="w-4 h-4 text-primary-600 bg-background border-border rounded focus:ring-primary-500"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Icon name={option.icon} size={16} className="text-primary-500" />
                        <h5 className="font-medium text-text-primary">{option.label}</h5>
                      </div>
                      <p className="text-sm text-text-secondary">{option.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Traditional Practices Recognition */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Traditional Practices Recognition</h4>
              <div className="space-y-3">
                {country?.traditionalMedicine?.map((practice, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-success-50 border border-success-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Icon name="Leaf" size={16} className="text-success-600" />
                      <span className="text-success-800 font-medium">{practice}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-success-600">Recognized</span>
                      <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'communication':
        return (
          <div className="space-y-6">
            {/* Communication Style Selection */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Preferred Communication Style</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  {
                    id: 'respectful',
                    label: 'Respectful & Formal',
                    description: 'Traditional respectful approach with formal language',
                    icon: 'Award'
                  },
                  {
                    id: 'friendly',
                    label: 'Warm & Friendly',
                    description: 'Approachable and warm communication style',
                    icon: 'Smile'
                  },
                  {
                    id: 'direct',
                    label: 'Direct & Efficient',
                    description: 'Clear, direct communication for quick consultation',
                    icon: 'Zap'
                  }
                ].map((style) => (
                  <button
                    key={style.id}
                    onClick={() => handleSettingChange('communicationStyle', style.id)}
                    className={`p-4 border rounded-lg text-left transition-all ${
                      settings.communicationStyle === style.id
                        ? 'border-primary-500 bg-primary-50' :'border-border bg-surface hover:border-primary-200'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Icon name={style.icon} size={20} className={
                        settings.communicationStyle === style.id ? 'text-primary-600' : 'text-text-secondary'
                      } />
                      <h5 className={`font-medium ${
                        settings.communicationStyle === style.id ? 'text-primary-800' : 'text-text-primary'
                      }`}>
                        {style.label}
                      </h5>
                    </div>
                    <p className={`text-sm ${
                      settings.communicationStyle === style.id ? 'text-primary-700' : 'text-text-secondary'
                    }`}>
                      {style.description}
                    </p>
                  </button>
                ))}
              </div>
            </div>

            {/* Cultural Communication Guidelines */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Cultural Communication Guidelines</h4>
              <div className="space-y-3">
                {Object.entries(culturalConsiderations[country?.id] || {}).map(([key, guideline]) => (
                  <div key={key} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-start gap-3">
                      <Icon name="MessageSquare" size={16} className="text-info-500 mt-0.5" />
                      <div>
                        <h5 className="font-medium text-text-primary capitalize mb-1">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </h5>
                        <p className="text-text-secondary">{guideline}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Language Sensitivity */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Language Sensitivity Settings</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {country?.primaryLanguages?.map((language, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-text-primary">{language}</h5>
                      <span className="text-xs text-success-600 bg-success-100 px-2 py-1 rounded-full">
                        Active
                      </span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-text-secondary">Cultural Phrases</span>
                        <span className="text-text-primary">92%</span>
                      </div>
                      <div className="w-full bg-border rounded-full h-2">
                        <div className="bg-primary-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'privacy':
        return (
          <div className="space-y-6">
            {/* Privacy Preferences */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-text-primary">Privacy Sensitivity Level</h4>
                  <p className="text-sm text-text-secondary">
                    How much privacy and discretion should be maintained in consultations?
                  </p>
                </div>
                <span className="font-semibold text-accent-600">{settings.privacyPreferences}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.privacyPreferences}
                onChange={(e) => handleSettingChange('privacyPreferences', parseInt(e.target.value))}
                className="w-full h-2 bg-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-text-secondary mt-2">
                <span>Open discussion</span>
                <span>Moderate discretion</span>
                <span>High privacy</span>
              </div>
            </div>

            {/* Consent Requirements */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Consent & Authorization</h4>
              <div className="space-y-4">
                {[
                  {
                    title: 'Family Member Consultation',
                    description: 'Allow family members to participate in health discussions',
                    required: country?.id === 'ghana' || country?.id === 'nigeria'
                  },
                  {
                    title: 'Traditional Healer Referral',
                    description: 'Permission to suggest traditional healing consultations',
                    required: true
                  },
                  {
                    title: 'Community Health Worker Contact',
                    description: 'Allow contact with local community health workers',
                    required: false
                  },
                  {
                    title: 'Emergency Contact Authorization',
                    description: 'Contact family members in case of emergency situations',
                    required: true
                  }
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 bg-surface border border-border rounded-lg">
                    <input
                      type="checkbox"
                      defaultChecked={item.required}
                      className="w-4 h-4 text-primary-600 bg-background border-border rounded focus:ring-primary-500 mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h5 className="font-medium text-text-primary">{item.title}</h5>
                        {item.required && (
                          <span className="text-xs text-error-600 bg-error-100 px-2 py-0.5 rounded-full">
                            Required
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-text-secondary mt-1">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Data Sharing Preferences */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Health Data Sharing</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  {
                    category: 'Local Healthcare Providers',
                    items: ['Primary care clinics', 'Specialists', 'Emergency services'],
                    icon: 'Building'
                  },
                  {
                    category: 'Government Health Services',
                    items: ['Ministry of Health', 'Disease surveillance', 'Public health programs'],
                    icon: 'Flag'
                  },
                  {
                    category: 'Community Resources',
                    items: ['Community health workers', 'Traditional healers', 'Support groups'],
                    icon: 'Users'
                  },
                  {
                    category: 'Research & Analytics',
                    items: ['Anonymous health research', 'Population health studies', 'AI improvement'],
                    icon: 'BarChart3'
                  }
                ].map((category, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center gap-2 mb-3">
                      <Icon name={category.icon} size={16} className="text-primary-500" />
                      <h5 className="font-medium text-text-primary">{category.category}</h5>
                    </div>
                    <div className="space-y-2">
                      {category.items.map((item, itemIndex) => (
                        <label key={itemIndex} className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            defaultChecked={itemIndex < 2}
                            className="w-3 h-3 text-primary-600 bg-background border-border rounded focus:ring-primary-500"
                          />
                          <span className="text-sm text-text-secondary">{item}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'family':
        return (
          <div className="space-y-6">
            {/* Family Involvement Level */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-text-primary">Family Involvement Level</h4>
                  <p className="text-sm text-text-secondary">
                    How much should family members be involved in health decisions?
                  </p>
                </div>
                <span className="font-semibold text-info-600">{settings.familyInvolvementLevel}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.familyInvolvementLevel}
                onChange={(e) => handleSettingChange('familyInvolvementLevel', parseInt(e.target.value))}
                className="w-full h-2 bg-border rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-text-secondary mt-2">
                <span>Individual focus</span>
                <span>Family consultation</span>
                <span>Community decision</span>
              </div>
            </div>

            {/* Age Respect Protocols */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-text-primary">Age Respect Protocols</h4>
                  <p className="text-sm text-text-secondary">
                    Level of formality and respect for elderly patients and family members
                  </p>
                </div>
                <span className="font-semibold text-warning-600">{settings.ageRespectProtocols}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.ageRespectProtocols}
                onChange={(e) => handleSettingChange('ageRespectProtocols', parseInt(e.target.value))}
                className="w-full h-2 bg-border rounded-lg appearance-none cursor-pointer slider"
              />
            </div>

            {/* Gender Sensitivity */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-medium text-text-primary">Gender Sensitivity</h4>
                  <p className="text-sm text-text-secondary">
                    Awareness of gender roles and preferences in healthcare interactions
                  </p>
                </div>
                <span className="font-semibold text-success-600">{settings.genderSensitivity}%</span>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.genderSensitivity}
                onChange={(e) => handleSettingChange('genderSensitivity', parseInt(e.target.value))}
                className="w-full h-2 bg-border rounded-lg appearance-none cursor-pointer slider"
              />
            </div>

            {/* Family Dynamics Guidelines */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Family Dynamics Guidelines</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  {
                    title: 'Elder Decision Authority',
                    description: 'Respect for elderly family members in health decisions',
                    level: 'High',
                    color: 'success'
                  },
                  {
                    title: 'Spouse Consultation',
                    description: 'Include spouse in major health discussions',
                    level: 'Moderate',
                    color: 'info'
                  },
                  {
                    title: 'Children\'s Health Authority',
                    description: 'Family involvement in children\'s healthcare',
                    level: 'High',
                    color: 'success'
                  },
                  {
                    title: 'Gender-Specific Preferences',
                    description: 'Same-gender provider preferences for sensitive topics',
                    level: 'High',
                    color: 'success'
                  }
                ].map((guideline, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-medium text-text-primary">{guideline.title}</h5>
                      <span className={`text-xs px-2 py-1 rounded-full bg-${guideline.color}-100 text-${guideline.color}-700`}>
                        {guideline.level}
                      </span>
                    </div>
                    <p className="text-sm text-text-secondary">{guideline.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Community Integration */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Community Integration</h4>
              <div className="space-y-3">
                {[
                  'Acknowledge community health workers and their role',
                  'Respect traditional authority figures in health matters',
                  'Consider community health practices and beliefs',
                  'Include community support systems in care recommendations'
                ].map((practice, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-primary-50 border border-primary-200 rounded-lg">
                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                    <span className="text-primary-800">{practice}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-2xl">{country?.flag}</span>
          <div>
            <h2 className="text-xl font-semibold text-text-primary font-heading">
              Cultural Adaptation Controls - {country?.name}
            </h2>
            <p className="text-text-secondary">
              Configure cultural sensitivity and adaptation settings for respectful healthcare delivery
            </p>
          </div>
        </div>
        
        <Button
          variant="primary"
          onClick={() => onSettingsChange?.(settings)}
          iconName="Save"
        >
          Save Configuration
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-border">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-border'
              }`}
            >
              <Icon name={tab.icon} size={16} />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="bg-surface border border-border rounded-lg p-6">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default CulturalAdaptationControls;