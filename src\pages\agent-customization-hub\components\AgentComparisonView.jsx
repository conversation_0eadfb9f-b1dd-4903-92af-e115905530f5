import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import AgentProfileCard from './AgentProfileCard';

const AgentComparisonView = ({ 
  agents = [], 
  onVoicePreview = () => {},
  onUpdateAgent = () => {},
  isPreviewPlaying = false,
  className = '' 
}) => {
  const [selectedAgents, setSelectedAgents] = useState([]);
  const [comparisonMode, setComparisonMode] = useState(false);

  const handleAgentSelect = (agentId) => {
    if (selectedAgents.includes(agentId)) {
      setSelectedAgents(selectedAgents.filter(id => id !== agentId));
    } else if (selectedAgents.length < 2) {
      setSelectedAgents([...selectedAgents, agentId]);
    }
  };

  const toggleComparisonMode = () => {
    setComparisonMode(!comparisonMode);
    if (!comparisonMode) {
      setSelectedAgents([]);
    }
  };

  const getComparisonData = () => {
    if (selectedAgents.length !== 2) return null;
    
    const agent1 = agents.find(a => a.id === selectedAgents[0]);
    const agent2 = agents.find(a => a.id === selectedAgents[1]);
    
    return { agent1, agent2 };
  };

  const renderComparisonTable = () => {
    const comparison = getComparisonData();
    if (!comparison) return null;

    const { agent1, agent2 } = comparison;

    const comparisonFields = [
      { key: 'specialty', label: 'Specialty' },
      { key: 'voiceProfile.gender', label: 'Voice Gender' },
      { key: 'voiceProfile.accent', label: 'Accent' },
      { key: 'personality.formality', label: 'Formality', format: (val) => `${Math.round(val * 100)}%` },
      { key: 'personality.empathy', label: 'Empathy', format: (val) => `${Math.round(val * 100)}%` },
      { key: 'personality.detailLevel', label: 'Detail Level', format: (val) => `${Math.round(val * 100)}%` },
      { key: 'expertiseFocus', label: 'Expertise Areas', format: (val) => val.join(', ') }
    ];

    const getValue = (obj, path) => {
      return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    return (
      <div className="bg-surface border border-border rounded-xl overflow-hidden">
        <div className="p-4 bg-secondary-50 border-b border-border">
          <h3 className="font-semibold text-text-primary font-heading">
            Agent Comparison
          </h3>
          <p className="text-sm text-text-secondary font-caption">
            Side-by-side comparison of selected agents
          </p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left p-4 font-medium text-text-primary">Attribute</th>
                <th className="text-left p-4 font-medium text-text-primary">{agent1.name}</th>
                <th className="text-left p-4 font-medium text-text-primary">{agent2.name}</th>
              </tr>
            </thead>
            <tbody>
              {comparisonFields.map((field, index) => {
                const value1 = getValue(agent1, field.key);
                const value2 = getValue(agent2, field.key);
                const formattedValue1 = field.format ? field.format(value1) : value1;
                const formattedValue2 = field.format ? field.format(value2) : value2;
                
                return (
                  <tr key={field.key} className={index % 2 === 0 ? 'bg-secondary-50' : 'bg-surface'}>
                    <td className="p-4 font-medium text-text-primary">{field.label}</td>
                    <td className="p-4 text-text-secondary">{formattedValue1}</td>
                    <td className="p-4 text-text-secondary">{formattedValue2}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        
        <div className="p-4 bg-secondary-50 border-t border-border">
          <div className="flex space-x-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() => onVoicePreview(agent1.id, agent1.voiceProfile)}
              iconName="Play"
              iconPosition="left"
              className="flex-1"
            >
              Preview {agent1.name}
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={() => onVoicePreview(agent2.id, agent2.voiceProfile)}
              iconName="Play"
              iconPosition="left"
              className="flex-1"
            >
              Preview {agent2.name}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Comparison Mode Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-text-primary font-heading">
            Agent Comparison
          </h2>
          <p className="text-sm text-text-secondary font-caption">
            Compare agents side-by-side to make informed customization decisions
          </p>
        </div>
        
        <Button
          variant={comparisonMode ? "primary" : "outline"}
          onClick={toggleComparisonMode}
          iconName={comparisonMode ? "Eye" : "EyeOff"}
          iconPosition="left"
        >
          {comparisonMode ? 'Exit Comparison' : 'Compare Agents'}
        </Button>
      </div>

      {/* Agent Selection (when in comparison mode) */}
      {comparisonMode && (
        <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Icon name="Info" size={16} color="var(--color-primary)" />
            <span className="text-sm font-medium text-primary-600">
              Select 2 agents to compare ({selectedAgents.length}/2 selected)
            </span>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {agents.map((agent) => (
              <button
                key={agent.id}
                onClick={() => handleAgentSelect(agent.id)}
                disabled={!selectedAgents.includes(agent.id) && selectedAgents.length >= 2}
                className={`p-3 rounded-lg border transition-fast text-left ${
                  selectedAgents.includes(agent.id)
                    ? 'border-primary-500 bg-primary-100 text-primary-700'
                    : selectedAgents.length >= 2
                    ? 'border-border bg-secondary-50 text-text-muted cursor-not-allowed' :'border-border bg-surface text-text-primary hover:bg-secondary-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    agent.type === 'gp' ? 'bg-primary-500' :
                    agent.type === 'cardiologist' ? 'bg-error-500' :
                    agent.type === 'nutritionist'? 'bg-success-500' : 'bg-secondary-500'
                  }`}>
                    <Icon 
                      name={
                        agent.type === 'gp' ? 'Stethoscope' :
                        agent.type === 'cardiologist' ? 'Heart' :
                        agent.type === 'nutritionist'? 'Apple' : 'User'
                      } 
                      size={14} 
                      color="white" 
                    />
                  </div>
                  <div>
                    <div className="font-medium">{agent.name}</div>
                    <div className="text-xs opacity-75">{agent.specialty}</div>
                  </div>
                  {selectedAgents.includes(agent.id) && (
                    <Icon name="Check" size={16} color="var(--color-primary)" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Comparison Table */}
      {comparisonMode && selectedAgents.length === 2 && renderComparisonTable()}

      {/* Agent Cards Grid */}
      {!comparisonMode && (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {agents.map((agent) => (
            <AgentProfileCard
              key={agent.id}
              agent={agent}
              onVoicePreview={onVoicePreview}
              onUpdateAgent={onUpdateAgent}
              isPreviewPlaying={isPreviewPlaying}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AgentComparisonView;