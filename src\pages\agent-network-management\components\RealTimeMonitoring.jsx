import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RealTimeMonitoring = ({ agents = [], networkMetrics = {} }) => {
  const [activeConsultations, setActiveConsultations] = useState([]);
  const [selectedConsultation, setSelectedConsultation] = useState(null);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds

  // Mock active consultations data
  const mockConsultations = [
    {
      id: 'cons_001',
      patientId: 'P-2024-001',
      agentId: 'mental-health',
      agentName: 'Mental Health Specialist',
      priority: 'high',
      startTime: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      status: 'active',
      topic: 'Anxiety management',
      patientAge: 28,
      queuePosition: null,
      estimatedDuration: 25
    },
    {
      id: 'cons_002',
      patientId: 'P-2024-002',
      agentId: 'general-practitioner',
      agentName: 'General Practitioner',
      priority: 'medium',
      startTime: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
      status: 'active',
      topic: 'Common cold symptoms',
      patientAge: 34,
      queuePosition: null,
      estimatedDuration: 15
    },
    {
      id: 'cons_003',
      patientId: 'P-2024-003',
      agentId: 'paediatrician',
      agentName: 'Paediatrician',
      priority: 'urgent',
      startTime: null,
      status: 'queued',
      topic: 'Fever in 3-year-old',
      patientAge: 3,
      queuePosition: 1,
      estimatedWait: 5
    },
    {
      id: 'cons_004',
      patientId: 'P-2024-004',
      agentId: 'dermatologist',
      agentName: 'Dermatologist',
      priority: 'low',
      startTime: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
      status: 'active',
      topic: 'Acne treatment follow-up',
      patientAge: 22,
      queuePosition: null,
      estimatedDuration: 10
    },
    {
      id: 'cons_005',
      patientId: 'P-2024-005',
      agentId: 'dietician',
      agentName: 'Dietician',
      priority: 'medium',
      startTime: null,
      status: 'queued',
      topic: 'Weight management plan',
      patientAge: 45,
      queuePosition: 2,
      estimatedWait: 12
    }
  ];

  useEffect(() => {
    // Simulate real-time updates
    setActiveConsultations(mockConsultations);
    
    const interval = setInterval(() => {
      // Update consultation times and status
      setActiveConsultations(prev => prev.map(consultation => ({
        ...consultation,
        // Simulate progression for active consultations
        ...(consultation.status === 'active' && consultation.startTime && {
          duration: Math.floor((Date.now() - consultation.startTime.getTime()) / (1000 * 60))
        })
      })));
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-error-600 bg-error-100';
      case 'high': return 'text-warning-600 bg-warning-100';
      case 'medium': return 'text-primary-600 bg-primary-100';
      case 'low': return 'text-text-secondary bg-surface';
      default: return 'text-text-secondary bg-surface';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-success-600 bg-success-100';
      case 'queued': return 'text-warning-600 bg-warning-100';
      case 'paused': return 'text-text-secondary bg-surface';
      default: return 'text-text-secondary bg-surface';
    }
  };

  const formatDuration = (startTime) => {
    if (!startTime) return '00:00';
    const duration = Math.floor((Date.now() - startTime.getTime()) / (1000 * 60));
    const minutes = duration % 60;
    const hours = Math.floor(duration / 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  const getAgentConsultationCount = (agentId) => {
    return activeConsultations.filter(c => c.agentId === agentId && c.status === 'active').length;
  };

  const getAgentQueueCount = (agentId) => {
    return activeConsultations.filter(c => c.agentId === agentId && c.status === 'queued').length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-text-primary font-heading">
            Real-Time Monitoring
          </h2>
          <p className="text-text-secondary mt-1">
            Active consultations and queue management across all specialties
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-text-secondary">
            <Icon name="RefreshCw" size={16} />
            <span>Auto-refresh every {refreshInterval / 1000}s</span>
          </div>
          <Button variant="outline" size="sm" iconName="Download">
            Export Report
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Agent Status Overview */}
        <div className="lg:col-span-1">
          <div className="bg-surface rounded-lg border border-border p-6">
            <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
              <Icon name="Activity" size={20} className="text-primary-500" />
              Agent Status
            </h3>
            
            <div className="space-y-3">
              {agents?.map((agent) => {
                const activeCount = getAgentConsultationCount(agent.id);
                const queueCount = getAgentQueueCount(agent.id);
                
                return (
                  <div key={agent.id} className="p-3 bg-background rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{agent.avatar}</span>
                        <span className="font-medium text-text-primary text-sm">
                          {agent.shortName}
                        </span>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${
                        agent.status === 'active' ? 'bg-success-500' :
                        agent.status === 'busy' ? 'bg-warning-500' : 'bg-error-500'
                      }`}></div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-bold text-text-primary">{activeCount}</div>
                        <div className="text-text-secondary">Active</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-text-primary">{queueCount}</div>
                        <div className="text-text-secondary">Queued</div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Active Consultations */}
        <div className="lg:col-span-2">
          <div className="bg-surface rounded-lg border border-border p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text-primary flex items-center gap-2">
                <Icon name="MessageCircle" size={20} className="text-primary-500" />
                Active Consultations ({activeConsultations.filter(c => c.status === 'active').length})
              </h3>
              
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" iconName="Filter">
                  Filter
                </Button>
                <Button variant="ghost" size="sm" iconName="RefreshCw">
                  Refresh
                </Button>
              </div>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {activeConsultations.map((consultation) => (
                <div
                  key={consultation.id}
                  className={`p-4 border border-border rounded-lg cursor-pointer transition-all hover:shadow-small ${
                    selectedConsultation?.id === consultation.id ? 'ring-2 ring-primary-500' : ''
                  }`}
                  onClick={() => setSelectedConsultation(consultation)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-text-primary">
                          {consultation.patientId}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(consultation.priority)}`}>
                          {consultation.priority}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(consultation.status)}`}>
                          {consultation.status}
                        </span>
                      </div>
                      
                      <p className="text-sm text-text-secondary mb-1">
                        {consultation.topic}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-text-secondary">
                        <span>Age: {consultation.patientAge}</span>
                        <span>Agent: {consultation.agentName}</span>
                        {consultation.status === 'active' && consultation.startTime && (
                          <span>Duration: {formatDuration(consultation.startTime)}</span>
                        )}
                        {consultation.status === 'queued' && (
                          <span>Queue Position: #{consultation.queuePosition}</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex flex-col items-end gap-2">
                      {consultation.priority === 'urgent' && (
                        <Icon name="AlertTriangle" size={16} className="text-error-500" />
                      )}
                      
                      <div className="flex gap-1">
                        <Button variant="ghost" size="sm" iconName="Eye" />
                        <Button variant="ghost" size="sm" iconName="MessageSquare" />
                      </div>
                    </div>
                  </div>
                  
                  {/* Progress bar for active consultations */}
                  {consultation.status === 'active' && consultation.estimatedDuration && consultation.startTime && (
                    <div className="mt-3">
                      <div className="flex justify-between text-xs text-text-secondary mb-1">
                        <span>Progress</span>
                        <span>
                          {Math.min(100, Math.floor(((Date.now() - consultation.startTime.getTime()) / (1000 * 60)) / consultation.estimatedDuration * 100))}%
                        </span>
                      </div>
                      <div className="w-full bg-background rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full transition-all"
                          style={{ 
                            width: `${Math.min(100, Math.floor(((Date.now() - consultation.startTime.getTime()) / (1000 * 60)) / consultation.estimatedDuration * 100))}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Queue Management */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
          <Icon name="Users" size={20} className="text-primary-500" />
          Queue Management & Priority Handling
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Queue Stats */}
          <div className="text-center p-4 bg-background rounded-lg">
            <div className="text-2xl font-bold text-text-primary mb-1">
              {activeConsultations.filter(c => c.status === 'queued').length}
            </div>
            <div className="text-sm text-text-secondary">Total in Queue</div>
          </div>
          
          <div className="text-center p-4 bg-background rounded-lg">
            <div className="text-2xl font-bold text-warning-600 mb-1">
              {activeConsultations.filter(c => c.priority === 'urgent').length}
            </div>
            <div className="text-sm text-text-secondary">Urgent Cases</div>
          </div>
          
          <div className="text-center p-4 bg-background rounded-lg">
            <div className="text-2xl font-bold text-success-600 mb-1">
              {Math.round(activeConsultations.filter(c => c.estimatedWait).reduce((acc, c) => acc + (c.estimatedWait || 0), 0) / activeConsultations.filter(c => c.estimatedWait).length) || 0}
            </div>
            <div className="text-sm text-text-secondary">Avg Wait (min)</div>
          </div>
          
          <div className="text-center p-4 bg-background rounded-lg">
            <div className="text-2xl font-bold text-primary-600 mb-1">
              {activeConsultations.filter(c => c.status === 'active').length}
            </div>
            <div className="text-sm text-text-secondary">Active Sessions</div>
          </div>
        </div>
        
        {/* Queue Actions */}
        <div className="flex flex-wrap gap-3 mt-4">
          <Button variant="outline" size="sm" iconName="ArrowUp">
            Prioritize Urgent
          </Button>
          <Button variant="outline" size="sm" iconName="Users">
            Redistribute Queue
          </Button>
          <Button variant="outline" size="sm" iconName="Clock">
            Adjust Wait Times
          </Button>
          <Button variant="primary" size="sm" iconName="Zap">
            Emergency Routing
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RealTimeMonitoring;