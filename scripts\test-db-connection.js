/**
 * Test Database Connection
 * Simple script to verify Supabase connection and check if audit_logs table exists
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://vbjxfrfwdbebrwdqaqne.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZianhmcmZ3ZGJlYnJ3ZHFhcW5lIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNDY4MjAsImV4cCI6MjA2NjYyMjgyMH0.HXFPe8ve1WcW3R-ZvlwQC7u-7NN1EUydR83yHbJ_Z48';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  console.log(`📡 URL: ${SUPABASE_URL}`);
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('audit_logs')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      if (error.message.includes('does not exist')) {
        console.log('❌ Audit logs table does not exist');
        console.log('📋 Migration is required');
        return false;
      } else {
        console.error('❌ Database connection error:', error.message);
        return false;
      }
    }
    
    console.log('✅ Database connection successful');
    console.log(`📊 Audit logs table exists with ${data || 0} records`);
    return true;
    
  } catch (error) {
    console.error('💥 Connection test failed:', error.message);
    return false;
  }
}

async function createAuditLogsTable() {
  console.log('🔧 Attempting to create audit_logs table...');
  
  try {
    // Since we can't execute DDL with anon key, we'll provide instructions
    console.log('📋 Manual migration required:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Execute the following SQL:');
    console.log('\n' + '='.repeat(80));
    console.log(`
-- HIPAA-Compliant Audit Logs Table
CREATE TABLE IF NOT EXISTS audit_logs (
    id TEXT PRIMARY KEY,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    event_type TEXT NOT NULL,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    user_id UUID,
    user_email TEXT,
    user_role TEXT,
    session_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    severity TEXT CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT,
    metadata JSONB DEFAULT '{}',
    compliance_flags JSONB DEFAULT '{}',
    integrity_hash TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);

-- Enable Row Level Security (RLS)
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for authenticated users
CREATE POLICY "audit_logs_authenticated_access" ON audit_logs
    FOR SELECT USING (auth.role() = 'authenticated');

-- Insert initial system log
INSERT INTO audit_logs (
    id,
    timestamp,
    event_type,
    action,
    user_id,
    user_email,
    description,
    metadata,
    compliance_flags,
    integrity_hash
) VALUES (
    'system_init_' || extract(epoch from now())::text,
    NOW(),
    'system_initialization',
    'audit_system_created',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    'HIPAA-compliant audit logging system initialized',
    '{"version": "1.0", "compliance": "HIPAA", "retention": "7_years"}',
    '{"hipaa_relevant": true, "system_event": true, "retention_required": true}',
    'system_init_hash_' || extract(epoch from now())::text
);
    `);
    console.log('='.repeat(80) + '\n');
    
    return false;
    
  } catch (error) {
    console.error('❌ Table creation guidance failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🏥 VoiceHealth AI - Database Connection Test');
  console.log('🔒 Checking HIPAA-compliant audit logging setup...\n');
  
  const connected = await testConnection();
  
  if (!connected) {
    console.log('\n📋 Setting up audit logs table...');
    await createAuditLogsTable();
  } else {
    console.log('\n✅ Database is ready for audit logging');
  }
  
  console.log('\n🎯 Next steps:');
  console.log('1. If table creation was needed, execute the SQL in Supabase dashboard');
  console.log('2. Run: npm run test:security');
  console.log('3. Update frontend environment variables');
  console.log('4. Deploy secure backend API');
}

main().catch(console.error);
