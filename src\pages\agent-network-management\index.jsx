import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import NetworkTopologyVisualization from './components/NetworkTopologyVisualization';
import SpecialtyAgentPanel from './components/SpecialtyAgentPanel';
import AgentConfigurationSection from './components/AgentConfigurationSection';
import NetworkOrchestrationControls from './components/NetworkOrchestrationControls';
import RealTimeMonitoring from './components/RealTimeMonitoring';

const AgentNetworkManagement = () => {
  const navigate = useNavigate();
  const [activeView, setActiveView] = useState('overview');
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [networkStatus, setNetworkStatus] = useState('healthy');
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for the five health specialties
  const healthSpecialties = [
    {
      id: 'general-practitioner',
      name: 'General Practitioner',
      shortName: 'GP',
      description: 'First point of contact, handles common illnesses, advice and basic triage, and refers to other specialists',
      avatar: '👨‍⚕️',
      status: 'active',
      confidenceScore: 94,
      consultationsToday: 47,
      averageResponseTime: '2.3s',
      expertise: ['Common illnesses', 'Basic triage', 'Referrals', 'Preventive care'],
      performance: {
        accuracy: 96,
        patientSatisfaction: 4.7,
        consultationVolume: 1247,
        responseTime: 2.3
      },
      currentLoad: 68,
      queueLength: 3
    },
    {
      id: 'dietician',
      name: 'Dietician',
      shortName: 'Diet',
      description: 'Guidance on healthy eating, weight management, and chronic conditions like diabetes',
      avatar: '🥗',
      status: 'active',
      confidenceScore: 91,
      consultationsToday: 32,
      averageResponseTime: '1.8s',
      expertise: ['Nutrition counseling', 'Weight management', 'Diabetes care', 'Meal planning'],
      performance: {
        accuracy: 93,
        patientSatisfaction: 4.8,
        consultationVolume: 892,
        responseTime: 1.8
      },
      currentLoad: 45,
      queueLength: 1
    },
    {
      id: 'mental-health',
      name: 'Mental Health Specialist',
      shortName: 'MH',
      description: 'Deals with stress, anxiety, depression, sleep issues - high demand in digital health space',
      avatar: '🧠',
      status: 'active',
      confidenceScore: 89,
      consultationsToday: 63,
      averageResponseTime: '3.1s',
      expertise: ['Anxiety management', 'Depression support', 'Sleep therapy', 'Stress reduction'],
      performance: {
        accuracy: 91,
        patientSatisfaction: 4.9,
        consultationVolume: 1563,
        responseTime: 3.1
      },
      currentLoad: 82,
      queueLength: 7
    },
    {
      id: 'paediatrician',
      name: 'Paediatrician',
      shortName: 'Ped',
      description: 'Focuses on children\'s health: common childhood illnesses, growth, and vaccinations',
      avatar: '👶',
      status: 'active',
      confidenceScore: 92,
      consultationsToday: 28,
      averageResponseTime: '2.7s',
      expertise: ['Child development', 'Vaccinations', 'Childhood illnesses', 'Growth monitoring'],
      performance: {
        accuracy: 95,
        patientSatisfaction: 4.6,
        consultationVolume: 674,
        responseTime: 2.7
      },
      currentLoad: 34,
      queueLength: 2
    },
    {
      id: 'dermatologist',
      name: 'Dermatologist',
      shortName: 'Derm',
      description: 'Handles skin-related issues: acne, rashes, infections, pigmentations, cosmetic concerns',
      avatar: '🔬',
      status: 'active',
      confidenceScore: 88,
      consultationsToday: 41,
      averageResponseTime: '2.1s',
      expertise: ['Acne treatment', 'Skin conditions', 'Cosmetic dermatology', 'Skin cancer screening'],
      performance: {
        accuracy: 90,
        patientSatisfaction: 4.5,
        consultationVolume: 956,
        responseTime: 2.1
      },
      currentLoad: 59,
      queueLength: 4
    }
  ];

  const networkMetrics = {
    totalActiveAgents: 5,
    totalConsultationsToday: 211,
    averageNetworkResponseTime: '2.4s',
    networkUptime: '99.7%',
    emergencyOverrideActive: false,
    loadBalancingStatus: 'optimal'
  };

  useEffect(() => {
    // Simulate loading
    const loadNetworkData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsLoading(false);
    };

    loadNetworkData();
  }, []);

  const handleAgentSelect = (agentId) => {
    const agent = healthSpecialties.find(a => a.id === agentId);
    setSelectedAgent(agent);
    setActiveView('agent-config');
  };

  const handleEmergencyOverride = () => {
    console.log('Emergency override activated');
    setNetworkStatus('emergency');
  };

  const handleNetworkFailover = () => {
    console.log('Network failover initiated');
    setNetworkStatus('failover');
  };

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
              <Icon name="Network" size={24} color="var(--primary-500)" />
            </div>
            <p className="text-text-secondary">Loading agent network...</p>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'agent-config':
        return (
          <AgentConfigurationSection
            agent={selectedAgent}
            onBack={() => setActiveView('overview')}
            onSave={(updatedAgent) => {
              console.log('Agent configuration saved:', updatedAgent);
              setActiveView('overview');
            }}
          />
        );

      case 'network-controls':
        return (
          <NetworkOrchestrationControls
            networkMetrics={networkMetrics}
            agents={healthSpecialties}
            onEmergencyOverride={handleEmergencyOverride}
            onNetworkFailover={handleNetworkFailover}
          />
        );

      case 'monitoring':
        return (
          <RealTimeMonitoring
            agents={healthSpecialties}
            networkMetrics={networkMetrics}
          />
        );

      default:
        return (
          <div className="space-y-8">
            {/* Network Topology Visualization */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Agent Network Topology
              </h2>
              <NetworkTopologyVisualization
                agents={healthSpecialties}
                networkStatus={networkStatus}
              />
            </div>

            {/* Specialty Agent Panels */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Health Specialty Agents
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {healthSpecialties.map((agent) => (
                  <SpecialtyAgentPanel
                    key={agent.id}
                    agent={agent}
                    onConfigure={() => handleAgentSelect(agent.id)}
                  />
                ))}
              </div>
            </div>

            {/* Quick Network Actions */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Network Operations
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { 
                    id: 'network-controls', 
                    label: 'Network Controls', 
                    icon: 'Settings', 
                    description: 'Load balancing & routing',
                    color: 'text-primary-500',
                    bg: 'bg-primary-100'
                  },
                  { 
                    id: 'monitoring', 
                    label: 'Real-time Monitoring', 
                    icon: 'Activity', 
                    description: 'Active consultations & queues',
                    color: 'text-success-500',
                    bg: 'bg-success-100'
                  },
                  { 
                    id: 'training', 
                    label: 'Agent Training', 
                    icon: 'GraduationCap', 
                    description: 'Continuous learning modules',
                    color: 'text-accent-500',
                    bg: 'bg-accent-100'
                  },
                  { 
                    id: 'emergency', 
                    label: 'Emergency Override', 
                    icon: 'AlertTriangle', 
                    description: 'Emergency controls & failover',
                    color: 'text-error-500',
                    bg: 'bg-error-100'
                  }
                ].map((action) => (
                  <button
                    key={action.id}
                    onClick={() => {
                      if (action.id === 'emergency') {
                        handleEmergencyOverride();
                      } else {
                        setActiveView(action.id);
                      }
                    }}
                    className="bg-surface border border-border rounded-lg p-4 hover:shadow-small transition-all text-left"
                  >
                    <div className={`p-3 rounded-lg ${action.bg} w-fit mb-3`}>
                      <Icon name={action.icon} size={24} className={action.color} />
                    </div>
                    <h3 className="font-medium text-text-primary mb-1">{action.label}</h3>
                    <p className="text-sm text-text-secondary">{action.description}</p>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-3xl font-bold text-text-primary font-heading">
              Agent Network Management
            </h1>
            <p className="text-text-secondary mt-2">
              Comprehensive management of the five-specialty AI agent network for VoiceHealth AI
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex items-center gap-2 px-3 py-2 bg-surface rounded-lg border border-border">
              <div className={`w-2 h-2 rounded-full ${
                networkStatus === 'healthy' ? 'bg-success-500' :
                networkStatus === 'emergency' ? 'bg-error-500' :
                networkStatus === 'failover' ? 'bg-warning-500' : 'bg-text-secondary'
              }`}></div>
              <span className="text-sm font-medium text-text-primary">
                Network {networkStatus === 'healthy' ? 'Healthy' : 
                        networkStatus === 'emergency' ? 'Emergency Mode' :
                        networkStatus === 'failover' ? 'Failover Active' : 'Status'}
              </span>
            </div>
            
            <Button
              variant="outline"
              onClick={() => navigate('/voice-consultation-interface')}
              iconName="MessageCircle"
            >
              Start Consultation
            </Button>
            
            <Button
              variant="primary"
              onClick={() => setActiveView('monitoring')}
              iconName="Activity"
            >
              Live Monitoring
            </Button>
          </div>
        </div>

        {/* Main Content */}
        {renderMainContent()}
      </main>
    </div>
  );
};

export default AgentNetworkManagement;