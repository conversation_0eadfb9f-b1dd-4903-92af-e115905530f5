import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AudioPlaybackControls = ({
  audioSessions = [],
  currentSessionId = null,
  isPlaying = false,
  onPlaySession = () => {},
  onPauseSession = () => {},
  onSeekTo = () => {},
  onSpeedChange = () => {},
  showWaveform = true,
  enableDownload = true,
  className = ''
}) => {
  const [currentAudio, setCurrentAudio] = useState(null);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [selectedSession, setSelectedSession] = useState(null);
  const [isBuffering, setIsBuffering] = useState(false);
  const [showPlaylist, setShowPlaylist] = useState(false);

  const audioRef = useRef(null);
  const waveformCanvasRef = useRef(null);
  const animationFrameRef = useRef(null);

  useEffect(() => {
    if (selectedSession && audioRef.current) {
      const audio = audioRef.current;
      
      audio.onloadedmetadata = () => {
        setDuration(audio.duration);
        setIsBuffering(false);
      };

      audio.ontimeupdate = () => {
        setPlaybackPosition(audio.currentTime);
        if (showWaveform) {
          drawWaveform();
        }
      };

      audio.onended = () => {
        setPlaybackPosition(0);
        onPauseSession(selectedSession.id);
      };

      audio.onwaiting = () => setIsBuffering(true);
      audio.oncanplay = () => setIsBuffering(false);
    }
  }, [selectedSession, showWaveform]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
      audioRef.current.playbackRate = playbackSpeed;
    }
  }, [volume, playbackSpeed]);

  const handlePlayPause = async (session) => {
    if (!session) return;

    try {
      if (selectedSession?.id === session.id && isPlaying) {
        // Pause current session
        if (audioRef.current) {
          audioRef.current.pause();
        }
        onPauseSession(session.id);
      } else {
        // Play new session or resume
        setIsBuffering(true);
        
        if (selectedSession?.id !== session.id) {
          // Load new audio session
          setSelectedSession(session);
          
          // In a real implementation, you would load the audio from the session
          // For now, we'll create a mock audio element
          if (session.audioBlob) {
            const audioUrl = URL.createObjectURL(session.audioBlob);
            if (audioRef.current) {
              audioRef.current.src = audioUrl;
            }
          } else if (session.audioUrl) {
            if (audioRef.current) {
              audioRef.current.src = session.audioUrl;
            }
          }
        }

        if (audioRef.current) {
          await audioRef.current.play();
        }
        onPlaySession(session.id);
      }
    } catch (error) {
      console.error('Playback error:', error);
      setIsBuffering(false);
      alert('Failed to play audio. Please try again.');
    }
  };

  const handleSeek = (event) => {
    if (!audioRef.current || !selectedSession) return;

    const canvas = waveformCanvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const clickPosition = x / rect.width;
      const newTime = clickPosition * duration;
      
      audioRef.current.currentTime = newTime;
      setPlaybackPosition(newTime);
      onSeekTo(selectedSession.id, newTime);
    }
  };

  const handleSpeedChange = (newSpeed) => {
    setPlaybackSpeed(newSpeed);
    onSpeedChange(selectedSession?.id, newSpeed);
  };

  const handleDownload = (session) => {
    if (session.audioBlob) {
      const url = URL.createObjectURL(session.audioBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${session.title || 'audio-session'}_${session.date || 'unknown'}.webm`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (session.audioUrl) {
      window.open(session.audioUrl, '_blank');
    }
  };

  const drawWaveform = () => {
    const canvas = waveformCanvasRef.current;
    const audio = audioRef.current;
    
    if (!canvas || !audio || !selectedSession) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw background
    ctx.fillStyle = '#f1f5f9';
    ctx.fillRect(0, 0, width, height);

    // Mock waveform data (in real implementation, you'd analyze the audio)
    const segments = 100;
    const segmentWidth = width / segments;
    const progress = duration > 0 ? playbackPosition / duration : 0;

    for (let i = 0; i < segments; i++) {
      const x = i * segmentWidth;
      const amplitude = Math.random() * 0.8 + 0.2; // Mock amplitude
      const segmentHeight = amplitude * height * 0.8;
      const y = (height - segmentHeight) / 2;

      // Color based on playback position
      const segmentProgress = i / segments;
      ctx.fillStyle = segmentProgress <= progress ? '#3b82f6' : '#cbd5e1';
      
      ctx.fillRect(x, y, segmentWidth - 1, segmentHeight);
    }

    // Draw playback cursor
    const cursorX = progress * width;
    ctx.strokeStyle = '#ef4444';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(cursorX, 0);
    ctx.lineTo(cursorX, height);
    ctx.stroke();
  };

  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch (error) {
      return 'Unknown date';
    }
  };

  if (audioSessions.length === 0) {
    return (
      <div className={`bg-surface border border-border rounded-xl p-6 ${className}`}>
        <div className="text-center">
          <Icon name="Volume2" size={48} className="mx-auto mb-4 text-secondary-400" />
          <p className="text-text-secondary">No audio sessions available</p>
          <p className="text-sm text-text-muted mt-1">
            Audio sessions will appear here after you record voice messages
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <Icon name="Volume2" size={20} color="var(--color-text-primary)" />
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              Audio Playback Controls
            </h3>
            <p className="text-sm text-text-secondary">
              {selectedSession ? selectedSession.title : `${audioSessions.length} audio sessions`}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowPlaylist(!showPlaylist)}
            iconName="List"
          />
        </div>
      </div>

      {/* Main Player */}
      {selectedSession && (
        <div className="p-4 border-b border-border">
          {/* Session Info */}
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="font-medium text-text-primary">
                {selectedSession.title}
              </h4>
              <p className="text-sm text-text-secondary">
                {formatDate(selectedSession.date)} • {formatTime(selectedSession.duration)}
              </p>
            </div>
            
            {enableDownload && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDownload(selectedSession)}
                iconName="Download"
              />
            )}
          </div>

          {/* Waveform */}
          {showWaveform && (
            <div className="mb-4">
              <canvas
                ref={waveformCanvasRef}
                width={400}
                height={80}
                className="w-full h-20 bg-secondary-50 rounded-lg cursor-pointer"
                onClick={handleSeek}
              />
            </div>
          )}

          {/* Progress Bar (fallback if no waveform) */}
          {!showWaveform && (
            <div className="mb-4">
              <div 
                className="w-full bg-secondary-200 rounded-full h-2 cursor-pointer"
                onClick={handleSeek}
              >
                <div 
                  className="bg-primary-500 h-2 rounded-full transition-all duration-200"
                  style={{ 
                    width: duration > 0 ? `${(playbackPosition / duration) * 100}%` : '0%' 
                  }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-text-secondary mt-1">
                <span>{formatTime(playbackPosition)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>
          )}

          {/* Controls */}
          <div className="flex items-center justify-center space-x-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (audioRef.current) {
                  audioRef.current.currentTime = Math.max(0, audioRef.current.currentTime - 10);
                }
              }}
              iconName="SkipBack"
            />

            <Button
              onClick={() => handlePlayPause(selectedSession)}
              disabled={isBuffering}
              className="w-12 h-12 rounded-full"
              iconName={isBuffering ? "Loader" : (isPlaying ? "Pause" : "Play")}
              iconProps={{ className: isBuffering ? "animate-spin" : "" }}
            />

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (audioRef.current) {
                  audioRef.current.currentTime = Math.min(duration, audioRef.current.currentTime + 10);
                }
              }}
              iconName="SkipForward"
            />
          </div>

          {/* Additional Controls */}
          <div className="flex items-center justify-between">
            {/* Volume */}
            <div className="flex items-center space-x-2">
              <Icon name="Volume2" size={16} color="var(--color-text-secondary)" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(parseFloat(e.target.value))}
                className="w-20"
              />
            </div>

            {/* Speed Control */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-text-secondary">Speed:</span>
              <select
                value={playbackSpeed}
                onChange={(e) => handleSpeedChange(parseFloat(e.target.value))}
                className="text-sm border border-border rounded px-2 py-1 bg-surface"
              >
                <option value={0.5}>0.5x</option>
                <option value={0.75}>0.75x</option>
                <option value={1}>1x</option>
                <option value={1.25}>1.25x</option>
                <option value={1.5}>1.5x</option>
                <option value={2}>2x</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Playlist */}
      {showPlaylist && (
        <div className="max-h-64 overflow-y-auto">
          {audioSessions.map((session, index) => (
            <div
              key={session.id || index}
              className={`flex items-center justify-between p-3 border-b border-border last:border-b-0 hover:bg-secondary-50 cursor-pointer ${
                selectedSession?.id === session.id ? 'bg-primary-50' : ''
              }`}
              onClick={() => handlePlayPause(session)}
            >
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  iconName={
                    selectedSession?.id === session.id && isPlaying 
                      ? "Pause" :"Play"
                  }
                  className="flex-shrink-0"
                />
                
                <div className="min-w-0">
                  <p className="font-medium text-text-primary truncate">
                    {session.title || `Session ${index + 1}`}
                  </p>
                  <div className="flex items-center space-x-2 text-sm text-text-secondary">
                    <span>{formatTime(session.duration)}</span>
                    <span>•</span>
                    <span>{formatDate(session.date)}</span>
                    {session.hasTranscript && (
                      <>
                        <span>•</span>
                        <Icon name="FileText" size={12} />
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className={`px-2 py-1 text-xs rounded-full ${
                  session.quality === 'high' ? 'bg-success-100 text-success-800' :
                  session.quality === 'medium'? 'bg-warning-100 text-warning-800' : 'bg-secondary-100 text-secondary-800'
                }`}>
                  {session.quality || 'medium'}
                </div>
                
                {enableDownload && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(session);
                    }}
                    iconName="Download"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* No session selected */}
      {!selectedSession && !showPlaylist && (
        <div className="p-6 text-center">
          <Icon name="Play" size={48} className="mx-auto mb-4 text-secondary-400" />
          <p className="text-text-secondary mb-2">Select an audio session to play</p>
          <Button
            onClick={() => setShowPlaylist(true)}
            iconName="List"
            iconPosition="left"
            size="sm"
          >
            View All Sessions
          </Button>
        </div>
      )}

      {/* Hidden Audio Element */}
      <audio
        ref={audioRef}
        preload="metadata"
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default AudioPlaybackControls;