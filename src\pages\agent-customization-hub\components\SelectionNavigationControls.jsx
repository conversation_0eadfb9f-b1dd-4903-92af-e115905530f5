import React from 'react';
import Icon from '../../../components/AppIcon';

const SelectionNavigationControls = ({ 
  onBack, 
  onNext, 
  isNextDisabled, 
  isLoading, 
  selectedLanguage,
  selectedCount = 0,
  minSelections = 1
}) => {
  const getLocalizedText = (key) => {
    const texts = {
      back: {
        en: 'Back',
        tw: 'San kɔ',
        yo: 'Pada',
        sw: 'Nyuma',
        af: 'Terug'
      },
      next: {
        en: 'Continue',
        tw: 'Kɔ so',
        yo: 'Tẹsiwaju',
        sw: 'Endelea',
        af: 'Gaan voort'
      },
      selectSpecialist: {
        en: `Please select at least ${minSelections} specialist${minSelections > 1 ? 's' : ''} to continue`,
        tw: `Yɛsrɛ yi okunini ${minSelections} na toa so`,
        yo: `Jọwọ yan amoye ${minSelections} kan ni o kere ju lati tẹsiwaju`,
        sw: `Tafadhali chagua angalau mtaalamu ${minSelections} ili kuendelea`,
        af: `<PERSON><PERSON> asseblief ten minste ${minSelections} spesialis${minSelections > 1 ? 'se' : ''} om voort te gaan`
      },
      selectedCount: {
        en: `${selectedCount} specialist${selectedCount !== 1 ? 's' : ''} selected`,
        tw: `Wɔayi okunini ${selectedCount}`,
        yo: `A ti yan amoye ${selectedCount}`,
        sw: `Wataalamu ${selectedCount} wamechaguliwa`,
        af: `${selectedCount} spesialis${selectedCount !== 1 ? 'se' : ''} gekies`
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="space-y-4">
      {/* Selection Status */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-2 mb-2">
          <div className={`w-3 h-3 rounded-full ${
            selectedCount >= minSelections ? 'bg-success-500' : 'bg-secondary-300'
          }`} />
          <span className={`text-sm font-medium ${
            selectedCount >= minSelections ? 'text-success-600' : 'text-text-secondary'
          }`}>
            {getLocalizedText('selectedCount')}
          </span>
        </div>
        
        {isNextDisabled && (
          <p className="text-sm text-text-muted">
            {getLocalizedText('selectSpecialist')}
          </p>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex space-x-4">
        {/* Back Button */}
        <button
          onClick={onBack}
          className="flex-1 bg-surface border border-border text-text-primary font-medium py-3 px-6 rounded-xl hover:bg-secondary-50 hover:border-secondary-300 transition-all duration-200 flex items-center justify-center space-x-2 min-h-14"
        >
          <Icon name="ArrowLeft" size={18} color="var(--color-text-primary)" />
          <span>{getLocalizedText('back')}</span>
        </button>

        {/* Next Button */}
        <button
          onClick={onNext}
          disabled={isNextDisabled || isLoading}
          className={`flex-1 font-medium py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-14 ${
            isNextDisabled || isLoading
              ? 'bg-secondary-200 text-secondary-400 cursor-not-allowed' :'bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white shadow-elevated hover:shadow-floating transform hover:scale-[1.02] active:scale-[0.98]'
          }`}
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span>Loading...</span>
            </>
          ) : (
            <>
              <span>{getLocalizedText('next')}</span>
              <Icon name="ArrowRight" size={18} color="currentColor" />
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default SelectionNavigationControls;