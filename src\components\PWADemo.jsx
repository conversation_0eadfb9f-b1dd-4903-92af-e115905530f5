/**
 * PWA Demo Component - Testing Offline Features
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/Card';
import Button from './ui/Button';
import { Badge } from './ui/Badge';
import { 
  Wifi, WifiOff, Download, RotateCcw, Mic, MicOff, Database,
  Activity, AlertCircle, CheckCircle, Clock, RefreshCw
} from 'lucide-react';
import usePWA from '../hooks/usePWA';
import { useSpeechSocket } from '../hooks/useSpeechSocket';
import OfflineIndicator from './OfflineIndicator';

const PWADemo = () => {
  const [demoSessionId] = useState(`demo-${Date.now()}`);
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  
  // PWA hook
  const pwa = usePWA();
  
  // Speech hook with offline persistence
  const speech = useSpeechSocket({
    sessionId: demoSessionId,
    autoInit: true,
    compressionEnabled: true,
    autoSync: true
  });

  const [stats, setStats] = useState({
    cacheStatus: null,
    syncStats: null
  });

  // Update stats periodically
  useEffect(() => {
    const updateStats = async () => {
      try {
        const [cacheStatus, syncStats] = await Promise.all([
          pwa.getCacheStatus?.(),
          pwa.getSyncStats?.()
        ]);
        setStats({ cacheStatus, syncStats });
      } catch (error) {
        console.error('Failed to update stats:', error);
      }
    };

    updateStats();
    const interval = setInterval(updateStats, 10000);
    return () => clearInterval(interval);
  }, [pwa]);

  const runOfflineTest = async () => {
    setIsRunningTests(true);
    const results = [];
    
    try {
      // Test 1: Service Worker Ready
      results.push({
        name: 'Service Worker',
        status: pwa.swReady ? 'pass' : 'fail',
        details: pwa.swReady ? 'SW registered and ready' : 'SW not ready'
      });

      // Test 2: Network Detection
      results.push({
        name: 'Network Detection',
        status: 'pass',
        details: `Currently ${pwa.isOnline ? 'online' : 'offline'}`
      });

      // Test 3: Voice Persistence
      if (speech.persistenceStatus?.storedChunks !== undefined) {
        results.push({
          name: 'Voice Persistence',
          status: 'pass',
          details: `${speech.persistenceStatus.storedChunks} chunks stored`
        });
      } else {
        results.push({
          name: 'Voice Persistence',
          status: 'fail',
          details: 'Persistence not available'
        });
      }

      // Test 4: Background Sync
      if (pwa.syncInProgress !== undefined) {
        results.push({
          name: 'Background Sync',
          status: 'pass',
          details: `Sync status: ${pwa.syncStatus || 'ready'}`
        });
      } else {
        results.push({
          name: 'Background Sync',
          status: 'fail',
          details: 'Background sync not available'
        });
      }

      setTestResults(results);
    } catch (error) {
      results.push({
        name: 'Test Suite',
        status: 'fail',
        details: error.message
      });
      setTestResults(results);
    } finally {
      setIsRunningTests(false);
    }
  };

  const simulateOfflineMode = () => {
    // Dispatch offline event to simulate network loss
    window.dispatchEvent(new Event('offline'));
    console.log('📡 Simulated offline mode');
  };

  const simulateOnlineMode = () => {
    // Dispatch online event to simulate network restoration
    window.dispatchEvent(new Event('online'));
    console.log('📡 Simulated online mode');
  };

  const formatBytes = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">PWA Demo & Testing</h1>
        <OfflineIndicator />
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Connection Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              {pwa.isOnline ? 
                <Wifi className="w-8 h-8 text-green-500 mx-auto mb-2" /> : 
                <WifiOff className="w-8 h-8 text-red-500 mx-auto mb-2" />
              }
              <Badge variant={pwa.isOnline ? 'default' : 'destructive'}>
                {pwa.isOnline ? 'Online' : 'Offline'}
              </Badge>
            </div>

            <div>
              <Activity className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <Badge variant={pwa.swReady ? 'default' : 'secondary'}>
                SW {pwa.swReady ? 'Ready' : 'Loading'}
              </Badge>
            </div>

            <div>
              {speech.isRecording ? 
                <Mic className="w-8 h-8 text-red-500 mx-auto mb-2" /> : 
                <MicOff className="w-8 h-8 text-gray-500 mx-auto mb-2" />
              }
              <Badge variant={speech.isConnected ? 'default' : 'outline'}>
                Voice {speech.isConnected ? 'Connected' : 'Disconnected'}
              </Badge>
            </div>

            <div>
              <Sync className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <Badge variant={pwa.syncStatus === 'syncing' ? 'secondary' : 'outline'}>
                {pwa.syncStatus || 'Ready'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={runOfflineTest}
              disabled={isRunningTests}
              className="flex items-center gap-2"
            >
              {isRunningTests ? 
                <RefreshCw className="w-4 h-4 animate-spin" /> : 
                <CheckCircle className="w-4 h-4" />
              }
              Run PWA Tests
            </Button>

            <Button
              onClick={simulateOfflineMode}
              variant="outline"
              className="flex items-center gap-2"
            >
              <WifiOff className="w-4 h-4" />
              Simulate Offline
            </Button>

            <Button
              onClick={simulateOnlineMode}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Wifi className="w-4 h-4" />
              Simulate Online
            </Button>

            <Button
              onClick={pwa.forceSync}
              disabled={!pwa.isOnline}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Sync className="w-4 h-4" />
              Force Sync
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Test Results:</h4>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                    {result.status === 'pass' ? 
                      <CheckCircle className="w-4 h-4 text-green-500" /> : 
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    }
                    <span className="font-medium">{result.name}:</span>
                    <span className="text-sm text-gray-600">{result.details}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Voice Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="w-5 h-5" />
            Voice Recording Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={speech.isRecording ? speech.stopRecording : speech.startRecording}
              variant={speech.isRecording ? 'destructive' : 'default'}
              className="flex items-center gap-2"
            >
              {speech.isRecording ? 
                <MicOff className="w-4 h-4" /> : 
                <Mic className="w-4 h-4" />
              }
              {speech.isRecording ? 'Stop Recording' : 'Start Recording'}
            </Button>

            <Button
              onClick={speech.syncPendingChunks}
              variant="outline"
              disabled={!pwa.isOnline}
              className="flex items-center gap-2"
            >
              <Sync className="w-4 h-4" />
              Sync Voice Data
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Stored Chunks:</span>
              <div className="text-lg font-bold text-blue-600">
                {speech.persistenceStatus?.storedChunks || 0}
              </div>
            </div>
            <div>
              <span className="font-medium">Pending Sync:</span>
              <div className="text-lg font-bold text-orange-600">
                {speech.persistenceStatus?.pendingSyncChunks || 0}
              </div>
            </div>
            <div>
              <span className="font-medium">Storage Used:</span>
              <div className="text-lg font-bold text-purple-600">
                {formatBytes(speech.persistenceStatus?.totalStorageUsed || 0)}
              </div>
            </div>
            <div>
              <span className="font-medium">Transcripts:</span>
              <div className="text-lg font-bold text-green-600">
                {speech.transcripts?.length || 0}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* PWA Installation */}
      {pwa.canInstall && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              App Installation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Install VoiceHealth AI</p>
                <p className="text-sm text-gray-600">
                  Install for better offline experience
                </p>
              </div>
              <Button onClick={pwa.installApp} className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                Install App
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PWADemo;
