import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';


const SpecialistPromptEditor = ({ specialists, selectedSpecialist, onSpecialistChange }) => {
  const [currentSpecialist, setCurrentSpecialist] = useState(selectedSpecialist || 'general-practitioner');
  const [promptData, setPromptData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('system-prompt');

  const mockPromptData = {
    'general-practitioner': {
      systemPrompt: `You are a skilled General Practitioner AI assistant providing comprehensive primary healthcare consultations for patients in Africa. Your expertise covers:

MEDICAL EXPERTISE:
- Primary care medicine with 15+ years experience
- Africa-specific diseases (malaria, TB, sickle cell, tropical diseases)
- Preventive care and health promotion
- Chronic disease management in resource-limited settings

CULTURAL COMPETENCY:
- Deep understanding of African healthcare contexts
- Respectful of traditional medicine practices
- Sensitive to economic constraints and resource limitations
- Culturally appropriate communication styles

CONSULTATION APPROACH:
1. Be<PERSON> with warm, respectful greeting acknowledging cultural context
2. Conduct systematic symptom assessment with Africa-specific considerations
3. Provide differential diagnoses considering regional disease patterns
4. Offer practical, affordable treatment recommendations
5. Include prevention advice and lifestyle modifications
6. Respect traditional healing practices while ensuring safety

SAFETY PROTOCOLS:
- Immediate escalation for red flag symptoms
- Clear referral pathways for emergency conditions
- Drug interaction checks including traditional medicines
- Contraindication awareness for pregnancy, children, elderly

Remember: You are providing medical guidance, not replacing in-person medical care. Always recommend professional consultation for serious conditions.`,
      contextualPrompts: {
        patientLocation: 'Adapt recommendations based on {{location}} - consider local disease prevalence, available medications, and healthcare infrastructure.',
        economicStatus: 'Tailor advice to {{economic_level}} - prioritize cost-effective treatments and generic medications when appropriate.',
        culturalBackground: 'Respect {{cultural_background}} traditions while providing evidence-based medical advice.',
        ageGroup: 'Customize communication and treatment for {{age_group}} - use age-appropriate language and consider developmental factors.'
      },
      regionalAdaptations: {
        ghana: 'High malaria prevalence, focus on vector control, emphasize sickle cell screening',
        nigeria: 'Lassa fever awareness, meningitis belt considerations, high TB burden',
        kenya: 'Rift Valley fever, highland malaria, HIV co-infections common',
        'south-africa': 'High HIV prevalence, tuberculosis co-infection, cardiovascular disease rising'
      },
      redFlagTriggers: [
        'chest pain + shortness of breath + diaphoresis',
        'severe headache + neck stiffness + fever',
        'abdominal pain + vomiting + dehydration',
        'altered consciousness + fever + recent travel'
      ],
      responseTemplates: {
        greeting: 'Good {{time_of_day}}! I\'m Dr. {{name}}, your AI General Practitioner. I\'m here to help with your health concerns today.',
        symptomInquiry: 'Can you tell me more about {{symptom}}? When did it start, and how has it changed over time?',
        culturalSensitivity: 'I understand this may be a sensitive topic in {{culture}}. Please know that our conversation is confidential.',
        economicConsideration: 'I\'ll recommend treatments that are both effective and affordable in {{location}}.',
        redFlagEscalation: 'Based on your symptoms, I recommend seeking immediate medical attention. This could be a serious condition requiring urgent care.'
      }
    },
    'dietician': {
      systemPrompt: `You are an expert Dietician AI assistant specializing in nutrition counseling for African populations. Your expertise includes:

NUTRITIONAL EXPERTISE:
- Clinical nutrition and dietary planning
- Traditional African foods and their nutritional value
- Malnutrition management in resource-limited settings
- Chronic disease nutrition therapy (diabetes, hypertension)
- Maternal and child nutrition

CULTURAL FOOD COMPETENCY:
- Deep knowledge of African cuisine and food traditions
- Understanding of food security challenges
- Seasonal food availability and agricultural patterns
- Economic constraints on food choices
- Religious and cultural food restrictions

CONSULTATION APPROACH:
1. Assess current dietary patterns with cultural sensitivity
2. Understand food access and economic limitations
3. Provide practical, affordable nutrition recommendations
4. Incorporate traditional foods in meal planning
5. Address cultural beliefs about food and health
6. Emphasize locally available, nutrient-dense foods

SPECIALIZED AREAS:
- Malnutrition treatment and prevention
- Diabetes and hypertension dietary management
- Pregnancy and lactation nutrition
- Child feeding practices and growth monitoring
- Food safety in tropical climates`,
      contextualPrompts: {
        nutritionalStatus: 'Assess {{nutritional_status}} and provide appropriate dietary interventions.',
        foodSecurity: 'Consider {{food_security_level}} when making dietary recommendations.',
        culturalDiet: 'Incorporate {{traditional_foods}} into nutrition plans while meeting nutritional needs.',
        medicalCondition: 'Adapt dietary advice for {{medical_condition}} management.'
      },
      regionalAdaptations: {
        ghana: 'Emphasis on cassava, plantain, palm oil - address vitamin A deficiency',
        nigeria: 'Focus on yam, millet, sorghum - iron deficiency common',
        kenya: 'Maize and beans staples - emphasize protein quality',
        'south-africa': 'Maize meal, meat availability - address overnutrition and undernutrition'
      }
    }
  };

  useEffect(() => {
    const loadPromptData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));
      setPromptData(mockPromptData);
      setIsLoading(false);
    };

    loadPromptData();
  }, []);

  useEffect(() => {
    setCurrentSpecialist(selectedSpecialist || 'general-practitioner');
  }, [selectedSpecialist]);

  const handleSpecialistChange = (specialistId) => {
    setCurrentSpecialist(specialistId);
    onSpecialistChange?.(specialistId);
  };

  const currentPrompt = promptData[currentSpecialist] || {};
  const specialist = specialists?.find(s => s.id === currentSpecialist);

  const tabs = [
    { id: 'system-prompt', label: 'System Prompt', icon: 'FileText' },
    { id: 'contextual', label: 'Contextual Prompts', icon: 'Brain' },
    { id: 'regional', label: 'Regional Adaptations', icon: 'MapPin' },
    { id: 'red-flags', label: 'Red Flag Triggers', icon: 'AlertTriangle' },
    { id: 'templates', label: 'Response Templates', icon: 'MessageSquare' }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading specialist prompts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Specialist Selection */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Select Specialist</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {specialists?.map((spec) => (
            <button
              key={spec.id}
              onClick={() => handleSpecialistChange(spec.id)}
              className={`p-3 rounded-lg border text-left transition-all ${
                currentSpecialist === spec.id
                  ? 'border-primary-500 bg-primary-50' :'border-border hover:border-primary-300 hover:bg-surface-hover'
              }`}
            >
              <div className="flex items-center mb-2">
                <Icon name={spec.icon} size={20} className="text-primary-500 mr-2" />
                <h4 className="font-medium text-text-primary">{spec.name}</h4>
              </div>
              <p className="text-sm text-text-secondary mb-2">{spec.description}</p>
              <div className="flex items-center justify-between text-xs text-text-secondary">
                <span>{spec.promptCount} prompts</span>
                <span>{spec.culturalVariations} cultural adaptations</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Specialist Info */}
      {specialist && (
        <div className="bg-surface rounded-lg border border-border p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Icon name={specialist.icon} size={24} className="text-primary-500 mr-3" />
              <div>
                <h2 className="text-xl font-semibold text-text-primary">{specialist.name}</h2>
                <p className="text-text-secondary">{specialist.description}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-text-secondary">Last updated</p>
              <p className="text-sm font-medium text-text-primary">{specialist.lastUpdated}</p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-border mb-6">
            <div className="flex space-x-1 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 py-3 text-sm font-medium whitespace-nowrap transition-colors ${
                    activeTab === tab.id
                      ? 'border-b-2 border-primary-500 text-primary-600' :'text-text-secondary hover:text-text-primary'
                  }`}
                >
                  <Icon name={tab.icon} size={16} className="mr-2" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === 'system-prompt' && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-text-primary">System Prompt</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" iconName="Copy">
                      Copy
                    </Button>
                    <Button variant="outline" size="sm" iconName="TestTube">
                      Test
                    </Button>
                    <Button variant="primary" size="sm" iconName="Save">
                      Save Changes
                    </Button>
                  </div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4 border">
                  <textarea
                    value={currentPrompt.systemPrompt || ''}
                    onChange={() => {}}
                    className="w-full h-96 p-4 text-sm font-mono bg-white border border-border rounded resize-none focus:outline-none focus:border-primary-500"
                    placeholder="Enter system prompt for this specialist..."
                  />
                </div>
                <div className="mt-4 flex items-center text-sm text-text-secondary">
                  <Icon name="Info" size={16} className="mr-2" />
                  <span>Character count: {currentPrompt.systemPrompt?.length || 0} | Recommended: 1000-2000 characters</span>
                </div>
              </div>
            )}

            {activeTab === 'contextual' && (
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-4">Contextual Prompts</h3>
                <div className="space-y-4">
                  {Object.entries(currentPrompt.contextualPrompts || {}).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-text-primary capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </h4>
                        <Button variant="ghost" size="sm" iconName="Edit">
                          Edit
                        </Button>
                      </div>
                      <p className="text-sm text-text-secondary">{value}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'regional' && (
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-4">Regional Adaptations</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(currentPrompt.regionalAdaptations || {}).map(([region, adaptation]) => (
                    <div key={region} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center mb-2">
                        <Icon name="MapPin" size={16} className="text-primary-500 mr-2" />
                        <h4 className="font-medium text-text-primary capitalize">{region.replace('-', ' ')}</h4>
                      </div>
                      <p className="text-sm text-text-secondary">{adaptation}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'red-flags' && (
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-4">Red Flag Triggers</h3>
                <div className="space-y-3">
                  {currentPrompt.redFlagTriggers?.map((trigger, index) => (
                    <div key={index} className="flex items-center justify-between bg-error-50 border border-error-200 rounded-lg p-3">
                      <div className="flex items-center">
                        <Icon name="AlertTriangle" size={16} className="text-error-500 mr-2" />
                        <span className="text-sm font-medium text-error-700">{trigger}</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" iconName="Edit">
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" iconName="Trash2">
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" iconName="Plus">
                    Add Red Flag Trigger
                  </Button>
                </div>
              </div>
            )}

            {activeTab === 'templates' && (
              <div>
                <h3 className="text-lg font-semibold text-text-primary mb-4">Response Templates</h3>
                <div className="space-y-4">
                  {Object.entries(currentPrompt.responseTemplates || {}).map(([templateType, template]) => (
                    <div key={templateType} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-text-primary capitalize">
                          {templateType.replace(/([A-Z])/g, ' $1').trim()}
                        </h4>
                        <Button variant="ghost" size="sm" iconName="Edit">
                          Edit
                        </Button>
                      </div>
                      <p className="text-sm text-text-secondary font-mono bg-white p-2 rounded border">
                        {template}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SpecialistPromptEditor;