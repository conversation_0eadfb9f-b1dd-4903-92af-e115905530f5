/**
 * CACHE ANALYTICS AND MONITORING SERVICE
 * 
 * This service provides comprehensive cache analytics and monitoring with:
 * - Real-time performance metrics and monitoring
 * - Medical data access pattern analysis
 * - Emergency data usage tracking
 * - HIPAA-compliant analytics and reporting
 * - Cache optimization recommendations
 * - Predictive cache warming strategies
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency data access must be monitored in real-time
 * - Medical data patterns must be analyzed for optimization
 * - Performance degradation must trigger alerts
 * - Analytics must maintain patient privacy
 * - Monitoring must not impact system performance
 */

import type {
  CacheMetrics,
  CachePerformanceMetrics,
  CacheAnalyticsEvent,
  CacheHealthCheck,
  CacheAlert,
  MedicalDataPriority,
  CacheDataType
} from '../types/cache';

import auditLogger from './auditLogger';

interface AnalyticsConfig {
  readonly enabled: boolean;
  readonly metricsInterval: number;
  readonly retentionPeriod: number;
  readonly alertThresholds: {
    readonly hitRate: number;
    readonly responseTime: number;
    readonly errorRate: number;
    readonly memoryUsage: number;
  };
  readonly emergencyDataMonitoring: boolean;
  readonly performanceTracking: boolean;
  readonly privacyCompliant: boolean;
}

interface CacheUsagePattern {
  readonly dataType: CacheDataType;
  readonly accessFrequency: number;
  readonly averageSize: number;
  readonly peakUsageHours: number[];
  readonly userRoles: string[];
  readonly emergencyAccess: boolean;
  readonly predictedGrowth: number;
}

interface PerformanceBaseline {
  readonly hitRate: number;
  readonly averageResponseTime: number;
  readonly memoryEfficiency: number;
  readonly errorRate: number;
  readonly emergencyDataAccessTime: number;
  readonly lastUpdated: number;
}

class CacheAnalyticsService {
  private readonly config: AnalyticsConfig;
  private readonly metricsHistory: Map<number, CacheMetrics>;
  private readonly eventLog: CacheAnalyticsEvent[];
  private readonly usagePatterns: Map<CacheDataType, CacheUsagePattern>;
  private readonly alerts: CacheAlert[];
  private readonly performanceBaseline: PerformanceBaseline;
  private monitoringActive: boolean;
  private metricsInterval: NodeJS.Timeout | null;

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = {
      enabled: true,
      metricsInterval: 60000, // 1 minute
      retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 days
      alertThresholds: {
        hitRate: 0.8, // 80% minimum hit rate
        responseTime: 100, // 100ms maximum response time
        errorRate: 0.05, // 5% maximum error rate
        memoryUsage: 0.9 // 90% maximum memory usage
      },
      emergencyDataMonitoring: true,
      performanceTracking: true,
      privacyCompliant: true,
      ...config
    };

    this.metricsHistory = new Map();
    this.eventLog = [];
    this.usagePatterns = new Map();
    this.alerts = [];
    this.performanceBaseline = this.initializeBaseline();
    this.monitoringActive = false;
    this.metricsInterval = null;

    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Start real-time cache monitoring
   */
  startMonitoring(): void {
    if (this.monitoringActive) return;

    this.monitoringActive = true;
    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, this.config.metricsInterval);

    auditLogger.logSecurityEvent(
      'cache_monitoring_started',
      'low',
      {
        metrics_interval: this.config.metricsInterval,
        emergency_monitoring: this.config.emergencyDataMonitoring,
        privacy_compliant: this.config.privacyCompliant
      }
    );
  }

  /**
   * Stop cache monitoring
   */
  stopMonitoring(): void {
    if (!this.monitoringActive) return;

    this.monitoringActive = false;
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }

    auditLogger.logSecurityEvent(
      'cache_monitoring_stopped',
      'low',
      { reason: 'manual_stop' }
    );
  }

  /**
   * Record cache analytics event
   */
  recordEvent(event: Omit<CacheAnalyticsEvent, 'timestamp'>): void {
    const fullEvent: CacheAnalyticsEvent = {
      ...event,
      timestamp: Date.now()
    };

    this.eventLog.push(fullEvent);
    this.updateUsagePatterns(fullEvent);
    this.checkAlertConditions(fullEvent);

    // Maintain event log size
    if (this.eventLog.length > 10000) {
      this.eventLog.splice(0, this.eventLog.length - 10000);
    }

    // Log emergency data access immediately
    if (fullEvent.isEmergencyData && this.config.emergencyDataMonitoring) {
      auditLogger.logMedicalDataAccess(
        'emergency_cache_access',
        fullEvent.dataType || 'unknown',
        fullEvent.key,
        {
          event_type: fullEvent.eventType,
          priority: fullEvent.priority,
          duration: fullEvent.duration,
          real_time_monitoring: true
        }
      );
    }
  }

  /**
   * Get current cache metrics
   */
  getCurrentMetrics(): CacheMetrics {
    const now = Date.now();
    const recentEvents = this.getRecentEvents(60000); // Last minute

    const hitEvents = recentEvents.filter(e => e.eventType === 'hit');
    const missEvents = recentEvents.filter(e => e.eventType === 'miss');
    const totalRequests = hitEvents.length + missEvents.length;

    const hitRate = totalRequests > 0 ? hitEvents.length / totalRequests : 0;
    const missRate = totalRequests > 0 ? missEvents.length / totalRequests : 0;

    const emergencyDataCount = recentEvents.filter(e => e.isEmergencyData).length;
    const encryptedEntries = recentEvents.filter(e => e.metadata?.encrypted).length;

    const averageAccessTime = this.calculateAverageAccessTime(recentEvents);
    const memoryUsage = this.estimateMemoryUsage(recentEvents);

    const priorityDistribution = this.calculatePriorityDistribution(recentEvents);
    const dataTypeDistribution = this.calculateDataTypeDistribution(recentEvents);

    return {
      hitRate,
      missRate,
      evictionRate: this.calculateEvictionRate(recentEvents),
      averageAccessTime,
      memoryUsage,
      emergencyDataCount,
      totalEntries: this.eventLog.length,
      encryptedEntries,
      compressedEntries: recentEvents.filter(e => e.metadata?.compressed).length,
      priorityDistribution,
      dataTypeDistribution,
      performanceMetrics: this.calculatePerformanceMetrics(recentEvents)
    };
  }

  /**
   * Get cache health check
   */
  getCacheHealth(): CacheHealthCheck {
    const metrics = this.getCurrentMetrics();
    const alerts = this.getActiveAlerts();
    
    const overallHealth = this.determineOverallHealth(metrics, alerts);
    const recommendations = this.generateRecommendations(metrics, alerts);

    return {
      timestamp: Date.now(),
      overallHealth,
      memoryUsage: metrics.memoryUsage,
      hitRate: metrics.hitRate,
      errorRate: this.calculateErrorRate(),
      emergencyDataIntegrity: this.checkEmergencyDataIntegrity(),
      encryptionStatus: this.checkEncryptionStatus(),
      performanceScore: this.calculatePerformanceScore(metrics),
      recommendations,
      alerts
    };
  }

  /**
   * Get usage patterns for optimization
   */
  getUsagePatterns(): Map<CacheDataType, CacheUsagePattern> {
    return new Map(this.usagePatterns);
  }

  /**
   * Get cache warming recommendations
   */
  getCacheWarmingRecommendations(): {
    emergencyData: string[];
    frequentlyAccessed: string[];
    predictiveLoad: string[];
    userSpecific: Map<string, string[]>;
  } {
    const emergencyData = this.getEmergencyDataRecommendations();
    const frequentlyAccessed = this.getFrequentlyAccessedRecommendations();
    const predictiveLoad = this.getPredictiveLoadRecommendations();
    const userSpecific = this.getUserSpecificRecommendations();

    return {
      emergencyData,
      frequentlyAccessed,
      predictiveLoad,
      userSpecific
    };
  }

  /**
   * Get performance optimization suggestions
   */
  getOptimizationSuggestions(): {
    cacheSize: string[];
    evictionPolicy: string[];
    compressionSettings: string[];
    encryptionOptimization: string[];
    emergencyDataHandling: string[];
  } {
    const metrics = this.getCurrentMetrics();
    const suggestions = {
      cacheSize: [],
      evictionPolicy: [],
      compressionSettings: [],
      encryptionOptimization: [],
      emergencyDataHandling: []
    };

    // Cache size suggestions
    if (metrics.memoryUsage > 0.9) {
      suggestions.cacheSize.push('Consider increasing cache size limit');
      suggestions.cacheSize.push('Implement more aggressive cleanup policies');
    }

    // Hit rate optimization
    if (metrics.hitRate < 0.8) {
      suggestions.evictionPolicy.push('Review eviction policy - consider LFU over LRU');
      suggestions.evictionPolicy.push('Increase TTL for frequently accessed medical data');
    }

    // Compression optimization
    const compressionRatio = metrics.performanceMetrics.compressionRatio;
    if (compressionRatio < 0.3) {
      suggestions.compressionSettings.push('Enable compression for large medical records');
      suggestions.compressionSettings.push('Adjust compression level for better ratio');
    }

    // Emergency data handling
    if (metrics.emergencyDataCount > 0) {
      suggestions.emergencyDataHandling.push('Ensure emergency data is never evicted');
      suggestions.emergencyDataHandling.push('Consider dedicated emergency cache partition');
    }

    return suggestions;
  }

  /**
   * Export analytics data for reporting
   */
  exportAnalyticsData(options: {
    startDate?: Date;
    endDate?: Date;
    includePersonalData?: boolean;
    format?: 'json' | 'csv';
  } = {}): any {
    const {
      startDate = new Date(Date.now() - this.config.retentionPeriod),
      endDate = new Date(),
      includePersonalData = false,
      format = 'json'
    } = options;

    const filteredEvents = this.eventLog.filter(event => 
      event.timestamp >= startDate.getTime() && 
      event.timestamp <= endDate.getTime()
    );

    // Remove personal data if not included
    const sanitizedEvents = includePersonalData ? 
      filteredEvents : 
      filteredEvents.map(event => ({
        ...event,
        patientId: undefined,
        key: this.hashKey(event.key)
      }));

    const analyticsData = {
      summary: {
        totalEvents: sanitizedEvents.length,
        dateRange: { startDate, endDate },
        emergencyDataAccess: sanitizedEvents.filter(e => e.isEmergencyData).length,
        averageResponseTime: this.calculateAverageAccessTime(sanitizedEvents),
        hitRate: this.calculateHitRate(sanitizedEvents)
      },
      events: sanitizedEvents,
      usagePatterns: Array.from(this.usagePatterns.entries()),
      performanceMetrics: this.getCurrentMetrics().performanceMetrics,
      alerts: this.alerts.filter(alert => 
        alert.timestamp >= startDate.getTime() && 
        alert.timestamp <= endDate.getTime()
      )
    };

    if (format === 'csv') {
      return this.convertToCSV(analyticsData);
    }

    return analyticsData;
  }

  // Private helper methods
  private collectMetrics(): void {
    const metrics = this.getCurrentMetrics();
    const timestamp = Date.now();
    
    this.metricsHistory.set(timestamp, metrics);
    
    // Clean up old metrics
    const cutoff = timestamp - this.config.retentionPeriod;
    for (const [ts] of this.metricsHistory) {
      if (ts < cutoff) {
        this.metricsHistory.delete(ts);
      }
    }

    // Check for performance degradation
    this.checkPerformanceDegradation(metrics);
  }

  private updateUsagePatterns(event: CacheAnalyticsEvent): void {
    if (!event.dataType) return;

    const existing = this.usagePatterns.get(event.dataType) || {
      dataType: event.dataType,
      accessFrequency: 0,
      averageSize: 0,
      peakUsageHours: [],
      userRoles: [],
      emergencyAccess: false,
      predictedGrowth: 0
    };

    const updated: CacheUsagePattern = {
      ...existing,
      accessFrequency: existing.accessFrequency + 1,
      averageSize: event.size ? 
        (existing.averageSize + event.size) / 2 : 
        existing.averageSize,
      emergencyAccess: existing.emergencyAccess || !!event.isEmergencyData
    };

    this.usagePatterns.set(event.dataType, updated);
  }

  private checkAlertConditions(event: CacheAnalyticsEvent): void {
    // Check for emergency data access alerts
    if (event.isEmergencyData && event.eventType === 'miss') {
      this.createAlert(
        'emergency-data-cache-miss',
        'critical',
        'Emergency data cache miss detected',
        'performance',
        [event.key],
        'Ensure emergency data is properly cached',
        true
      );
    }

    // Check for slow response times
    if (event.duration && event.duration > this.config.alertThresholds.responseTime) {
      this.createAlert(
        'slow-cache-response',
        'warning',
        `Slow cache response time: ${event.duration}ms`,
        'performance',
        [event.key],
        'Investigate cache performance issues'
      );
    }

    // Check for encryption errors
    if (event.eventType === 'error' && event.metadata?.encrypted) {
      this.createAlert(
        'encryption-error',
        'error',
        'Cache encryption/decryption error',
        'security',
        [event.key],
        'Check encryption service status',
        event.isEmergencyData
      );
    }
  }

  private createAlert(
    id: string,
    severity: 'info' | 'warning' | 'error' | 'critical',
    message: string,
    category: 'performance' | 'security' | 'integrity' | 'capacity',
    affectedKeys?: string[],
    recommendedAction?: string,
    emergencyDataAffected?: boolean
  ): void {
    const alert: CacheAlert = {
      id: `${id}-${Date.now()}`,
      severity,
      message,
      timestamp: Date.now(),
      category,
      affectedKeys,
      recommendedAction,
      emergencyDataAffected
    };

    this.alerts.push(alert);

    // Log critical alerts immediately
    if (severity === 'critical' || emergencyDataAffected) {
      auditLogger.logSecurityEvent(
        'critical_cache_alert',
        'critical',
        {
          alert_id: alert.id,
          message: alert.message,
          category: alert.category,
          emergency_data_affected: emergencyDataAffected
        }
      );
    }

    // Maintain alerts list size
    if (this.alerts.length > 1000) {
      this.alerts.splice(0, this.alerts.length - 1000);
    }
  }

  private getRecentEvents(timeWindow: number): CacheAnalyticsEvent[] {
    const cutoff = Date.now() - timeWindow;
    return this.eventLog.filter(event => event.timestamp >= cutoff);
  }

  private calculateAverageAccessTime(events: CacheAnalyticsEvent[]): number {
    const timedEvents = events.filter(e => e.duration !== undefined);
    if (timedEvents.length === 0) return 0;
    
    const totalTime = timedEvents.reduce((sum, e) => sum + (e.duration || 0), 0);
    return totalTime / timedEvents.length;
  }

  private estimateMemoryUsage(events: CacheAnalyticsEvent[]): number {
    const totalSize = events.reduce((sum, e) => sum + (e.size || 0), 0);
    return totalSize / (100 * 1024 * 1024); // Convert to percentage of 100MB
  }

  private calculatePriorityDistribution(events: CacheAnalyticsEvent[]): Record<MedicalDataPriority, number> {
    const distribution: Record<MedicalDataPriority, number> = {
      emergency: 0,
      critical: 0,
      high: 0,
      normal: 0,
      low: 0
    };

    events.forEach(event => {
      if (event.priority) {
        distribution[event.priority]++;
      }
    });

    return distribution;
  }

  private calculateDataTypeDistribution(events: CacheAnalyticsEvent[]): Record<CacheDataType, number> {
    const distribution: Record<CacheDataType, number> = {
      medical_condition: 0,
      medication: 0,
      symptom: 0,
      medical_notes: 0,
      patient_data: 0,
      provider_notes: 0,
      emergency_data: 0,
      user_preferences: 0,
      system_config: 0,
      static_content: 0
    };

    events.forEach(event => {
      if (event.dataType) {
        distribution[event.dataType]++;
      }
    });

    return distribution;
  }

  private calculatePerformanceMetrics(events: CacheAnalyticsEvent[]): CachePerformanceMetrics {
    const setEvents = events.filter(e => e.eventType === 'set');
    const getEvents = events.filter(e => e.eventType === 'hit' || e.eventType === 'miss');
    const cleanupEvents = events.filter(e => e.eventType === 'cleanup');

    return {
      averageSetTime: this.calculateAverageAccessTime(setEvents),
      averageGetTime: this.calculateAverageAccessTime(getEvents),
      averageCleanupTime: this.calculateAverageAccessTime(cleanupEvents),
      cacheEfficiency: this.calculateHitRate(events),
      memoryEfficiency: 0.85, // Placeholder
      compressionRatio: 0.4, // Placeholder
      encryptionOverhead: 15 // Placeholder - 15ms average overhead
    };
  }

  private calculateEvictionRate(events: CacheAnalyticsEvent[]): number {
    const evictEvents = events.filter(e => e.eventType === 'evict');
    const totalEvents = events.length;
    return totalEvents > 0 ? evictEvents.length / totalEvents : 0;
  }

  private calculateHitRate(events: CacheAnalyticsEvent[]): number {
    const hitEvents = events.filter(e => e.eventType === 'hit');
    const missEvents = events.filter(e => e.eventType === 'miss');
    const totalRequests = hitEvents.length + missEvents.length;
    return totalRequests > 0 ? hitEvents.length / totalRequests : 0;
  }

  private calculateErrorRate(): number {
    const recentEvents = this.getRecentEvents(300000); // Last 5 minutes
    const errorEvents = recentEvents.filter(e => e.eventType === 'error');
    return recentEvents.length > 0 ? errorEvents.length / recentEvents.length : 0;
  }

  private checkEmergencyDataIntegrity(): boolean {
    const emergencyEvents = this.getRecentEvents(3600000) // Last hour
      .filter(e => e.isEmergencyData);
    
    const errorEvents = emergencyEvents.filter(e => e.eventType === 'error');
    return errorEvents.length === 0;
  }

  private checkEncryptionStatus(): boolean {
    const encryptedEvents = this.getRecentEvents(3600000) // Last hour
      .filter(e => e.metadata?.encrypted);
    
    const encryptionErrors = encryptedEvents.filter(e => e.eventType === 'error');
    return encryptionErrors.length === 0;
  }

  private calculatePerformanceScore(metrics: CacheMetrics): number {
    const hitRateScore = metrics.hitRate * 40; // 40% weight
    const responseTimeScore = Math.max(0, 100 - metrics.averageAccessTime) * 0.3; // 30% weight
    const memoryScore = Math.max(0, 100 - metrics.memoryUsage * 100) * 0.2; // 20% weight
    const errorScore = Math.max(0, 100 - this.calculateErrorRate() * 1000) * 0.1; // 10% weight

    return Math.min(100, hitRateScore + responseTimeScore + memoryScore + errorScore);
  }

  private determineOverallHealth(metrics: CacheMetrics, alerts: CacheAlert[]): 'healthy' | 'warning' | 'critical' {
    const criticalAlerts = alerts.filter(a => a.severity === 'critical');
    const emergencyDataIssues = alerts.filter(a => a.emergencyDataAffected);

    if (criticalAlerts.length > 0 || emergencyDataIssues.length > 0) {
      return 'critical';
    }

    if (metrics.hitRate < 0.7 || metrics.memoryUsage > 0.9 || this.calculateErrorRate() > 0.1) {
      return 'warning';
    }

    return 'healthy';
  }

  private generateRecommendations(metrics: CacheMetrics, alerts: CacheAlert[]): string[] {
    const recommendations: string[] = [];

    if (metrics.hitRate < 0.8) {
      recommendations.push('Consider increasing cache size or adjusting eviction policy');
    }

    if (metrics.memoryUsage > 0.9) {
      recommendations.push('Implement more aggressive cache cleanup');
    }

    if (alerts.some(a => a.emergencyDataAffected)) {
      recommendations.push('Review emergency data caching strategy');
    }

    if (this.calculateErrorRate() > 0.05) {
      recommendations.push('Investigate and resolve cache errors');
    }

    return recommendations;
  }

  private getActiveAlerts(): CacheAlert[] {
    const oneHourAgo = Date.now() - 3600000;
    return this.alerts.filter(alert => alert.timestamp >= oneHourAgo);
  }

  private getEmergencyDataRecommendations(): string[] {
    // Analyze emergency data access patterns
    return ['emergency-contacts', 'critical-medications', 'allergy-information'];
  }

  private getFrequentlyAccessedRecommendations(): string[] {
    // Analyze frequently accessed data
    return ['user-preferences', 'recent-conditions', 'active-medications'];
  }

  private getPredictiveLoadRecommendations(): string[] {
    // Use ML to predict what data will be needed
    return ['upcoming-appointments', 'seasonal-conditions'];
  }

  private getUserSpecificRecommendations(): Map<string, string[]> {
    // Generate user-specific recommendations
    return new Map();
  }

  private checkPerformanceDegradation(metrics: CacheMetrics): void {
    const baseline = this.performanceBaseline;
    
    if (metrics.hitRate < baseline.hitRate * 0.8) {
      this.createAlert(
        'performance-degradation',
        'warning',
        'Cache hit rate has degraded significantly',
        'performance',
        undefined,
        'Review cache configuration and usage patterns'
      );
    }

    if (metrics.averageAccessTime > baseline.averageResponseTime * 2) {
      this.createAlert(
        'response-time-degradation',
        'warning',
        'Cache response time has increased significantly',
        'performance',
        undefined,
        'Investigate cache performance bottlenecks'
      );
    }
  }

  private initializeBaseline(): PerformanceBaseline {
    return {
      hitRate: 0.85,
      averageResponseTime: 50,
      memoryEfficiency: 0.8,
      errorRate: 0.01,
      emergencyDataAccessTime: 10,
      lastUpdated: Date.now()
    };
  }

  private hashKey(key: string): string {
    // Simple hash for privacy
    return btoa(key).substring(0, 8);
  }

  private convertToCSV(data: any): string {
    // Simple CSV conversion
    return JSON.stringify(data); // Placeholder
  }
}

export default new CacheAnalyticsService();
