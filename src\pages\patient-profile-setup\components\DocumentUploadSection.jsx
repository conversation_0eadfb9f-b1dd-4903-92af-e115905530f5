import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Image from '../../../components/AppImage';

const DocumentUploadSection = ({ 
  documents = [], 
  onDocumentsUpdate = () => {},
  maxFiles = 5,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  className = ''
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const acceptedTypes = {
    'application/pdf': '.pdf',
    'image/jpeg': '.jpg,.jpeg',
    'image/png': '.png',
    'image/gif': '.gif',
    'application/msword': '.doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx'
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = async (fileList) => {
    const files = Array.from(fileList);
    
    // Check file count limit
    if (documents.length + files.length > maxFiles) {
      alert(`Maximum ${maxFiles} files allowed`);
      return;
    }

    setUploading(true);
    
    try {
      const newDocuments = [];
      
      for (const file of files) {
        // Check file size
        if (file.size > maxFileSize) {
          alert(`File ${file.name} is too large. Maximum size is ${maxFileSize / (1024 * 1024)}MB`);
          continue;
        }

        // Check file type
        if (!Object.keys(acceptedTypes).includes(file.type)) {
          alert(`File type ${file.type} is not supported`);
          continue;
        }

        // Create document object
        const document = {
          id: Date.now() + Math.random(),
          name: file.name,
          type: file.type,
          size: file.size,
          uploadDate: new Date().toISOString(),
          category: getDocumentCategory(file.name, file.type),
          file: file,
          preview: null
        };

        // Generate preview for images
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (e) => {
            document.preview = e.target.result;
          };
          reader.readAsDataURL(file);
        }

        newDocuments.push(document);
      }

      onDocumentsUpdate([...documents, ...newDocuments]);
    } catch (error) {
      console.error('Error uploading files:', error);
      alert('Error uploading files. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const getDocumentCategory = (fileName, fileType) => {
    const name = fileName.toLowerCase();
    
    if (name.includes('lab') || name.includes('test') || name.includes('result')) {
      return 'lab-results';
    }
    if (name.includes('prescription') || name.includes('rx')) {
      return 'prescription';
    }
    if (name.includes('report') || name.includes('scan') || name.includes('xray') || name.includes('mri')) {
      return 'medical-report';
    }
    if (name.includes('insurance') || name.includes('card')) {
      return 'insurance';
    }
    if (name.includes('id') || name.includes('license')) {
      return 'identification';
    }
    
    return 'other';
  };

  const removeDocument = (id) => {
    onDocumentsUpdate(documents.filter(doc => doc.id !== id));
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'lab-results':
        return 'TestTube';
      case 'prescription':
        return 'Pill';
      case 'medical-report':
        return 'FileText';
      case 'insurance':
        return 'CreditCard';
      case 'identification':
        return 'IdCard';
      default:
        return 'File';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'lab-results':
        return 'primary';
      case 'prescription':
        return 'success';
      case 'medical-report':
        return 'warning';
      case 'insurance':
        return 'accent';
      case 'identification':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="font-semibold text-text-primary font-heading flex items-center space-x-2">
            <Icon name="Upload" size={20} />
            <span>Medical Documents</span>
          </h3>
          <p className="text-sm text-text-secondary font-caption">
            Upload medical records, prescriptions, and test results
          </p>
        </div>
        
        <div className="text-right">
          <div className="text-sm text-text-secondary">
            {documents.length} of {maxFiles} files
          </div>
        </div>
      </div>

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all ${
          dragActive 
            ? 'border-primary-500 bg-primary-50' :'border-secondary-300 hover:border-primary-400 hover:bg-primary-50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple
          accept={Object.values(acceptedTypes).join(',')}
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={uploading || documents.length >= maxFiles}
        />
        
        <div className="space-y-4">
          <div className="w-16 h-16 mx-auto bg-primary-100 rounded-full flex items-center justify-center">
            <Icon 
              name={uploading ? "Loader" : "Upload"} 
              size={24} 
              color="var(--color-primary)"
              className={uploading ? "animate-spin" : ""}
            />
          </div>
          
          <div>
            <h4 className="font-medium text-text-primary mb-2">
              {uploading ? 'Uploading...' : 'Drop files here or click to browse'}
            </h4>
            <p className="text-sm text-text-secondary">
              Supports PDF, DOC, DOCX, JPG, PNG, GIF up to {maxFileSize / (1024 * 1024)}MB each
            </p>
          </div>
          
          <Button
            variant="primary"
            iconName="Upload"
            iconPosition="left"
            disabled={uploading || documents.length >= maxFiles}
            loading={uploading}
          >
            {uploading ? 'Uploading...' : 'Choose Files'}
          </Button>
        </div>
      </div>

      {/* Document Categories */}
      <div className="mt-6">
        <h4 className="font-medium text-text-primary mb-3">Document Categories</h4>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
          {[
            { key: 'lab-results', label: 'Lab Results' },
            { key: 'prescription', label: 'Prescriptions' },
            { key: 'medical-report', label: 'Medical Reports' },
            { key: 'insurance', label: 'Insurance' },
            { key: 'identification', label: 'ID Documents' }
          ].map((category) => (
            <div
              key={category.key}
              className="p-3 bg-secondary-50 rounded-lg text-center"
            >
              <Icon 
                name={getCategoryIcon(category.key)} 
                size={20} 
                color={`var(--color-${getCategoryColor(category.key)})`}
                className="mx-auto mb-2"
              />
              <span className="text-xs text-text-secondary">{category.label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Uploaded Documents */}
      {documents.length > 0 && (
        <div className="mt-6">
          <h4 className="font-medium text-text-primary mb-4">Uploaded Documents</h4>
          <div className="space-y-3">
            {documents.map((document) => (
              <div
                key={document.id}
                className="flex items-center space-x-4 p-4 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-fast"
              >
                {/* Document Preview/Icon */}
                <div className="flex-shrink-0">
                  {document.preview ? (
                    <div className="w-12 h-12 rounded-lg overflow-hidden border border-border">
                      <Image
                        src={document.preview}
                        alt={document.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center bg-${getCategoryColor(document.category)}-50`}>
                      <Icon 
                        name={getCategoryIcon(document.category)} 
                        size={20} 
                        color={`var(--color-${getCategoryColor(document.category)})`}
                      />
                    </div>
                  )}
                </div>

                {/* Document Info */}
                <div className="flex-1 min-w-0">
                  <h5 className="font-medium text-text-primary truncate">
                    {document.name}
                  </h5>
                  <div className="flex items-center space-x-4 text-sm text-text-secondary">
                    <span>{formatFileSize(document.size)}</span>
                    <span className={`px-2 py-1 rounded-full text-xs bg-${getCategoryColor(document.category)}-50 text-${getCategoryColor(document.category)}-600`}>
                      {document.category.replace('-', ' ')}
                    </span>
                    <span>{new Date(document.uploadDate).toLocaleDateString()}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    className="p-2 hover:bg-secondary-200 rounded-lg transition-fast"
                    title="Download"
                  >
                    <Icon name="Download" size={16} color="var(--color-text-secondary)" />
                  </button>
                  <button
                    onClick={() => removeDocument(document.id)}
                    className="p-2 hover:bg-error-50 rounded-lg transition-fast"
                    title="Remove"
                  >
                    <Icon name="Trash2" size={16} color="var(--color-error)" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="mt-6 p-4 bg-accent-50 rounded-lg border border-accent-200">
        <h4 className="font-medium text-accent-600 mb-2 flex items-center space-x-2">
          <Icon name="Info" size={16} />
          <span>Upload Guidelines</span>
        </h4>
        <ul className="space-y-1 text-sm text-accent-600">
          <li className="flex items-center space-x-2">
            <Icon name="Check" size={12} />
            <span>Ensure documents are clear and readable</span>
          </li>
          <li className="flex items-center space-x-2">
            <Icon name="Check" size={12} />
            <span>Remove any personal information you don't want to share</span>
          </li>
          <li className="flex items-center space-x-2">
            <Icon name="Check" size={12} />
            <span>All documents are encrypted and stored securely</span>
          </li>
          <li className="flex items-center space-x-2">
            <Icon name="Check" size={12} />
            <span>You can remove documents at any time</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default DocumentUploadSection;