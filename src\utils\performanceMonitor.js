/**
 * Performance Monitoring Utilities
 * Monitor bundle size, loading times, and PWA performance
 */

/**
 * Bundle Size Monitor
 */
export class BundleSizeMonitor {
  static async getBundleInfo() {
    if (!('performance' in window) || !window.performance.getEntriesByType) {
      return null;
    }

    const navigationEntries = window.performance.getEntriesByType('navigation');
    const resourceEntries = window.performance.getEntriesByType('resource');
    
    const bundleInfo = {
      totalResources: resourceEntries.length,
      totalTransferSize: 0,
      totalEncodedSize: 0,
      jsFiles: [],
      cssFiles: [],
      imageFiles: [],
      fontFiles: [],
      otherFiles: []
    };

    resourceEntries.forEach(entry => {
      bundleInfo.totalTransferSize += entry.transferSize || 0;
      bundleInfo.totalEncodedSize += entry.encodedBodySize || 0;

      const fileInfo = {
        name: entry.name.split('/').pop(),
        url: entry.name,
        transferSize: entry.transferSize || 0,
        encodedSize: entry.encodedBodySize || 0,
        loadTime: entry.responseEnd - entry.responseStart,
        type: this.getFileType(entry.name)
      };

      switch (fileInfo.type) {
        case 'js':
          bundleInfo.jsFiles.push(fileInfo);
          break;
        case 'css':
          bundleInfo.cssFiles.push(fileInfo);
          break;
        case 'image':
          bundleInfo.imageFiles.push(fileInfo);
          break;
        case 'font':
          bundleInfo.fontFiles.push(fileInfo);
          break;
        default:
          bundleInfo.otherFiles.push(fileInfo);
      }
    });

    // Sort files by size (largest first)
    const sortBySize = (a, b) => b.transferSize - a.transferSize;
    bundleInfo.jsFiles.sort(sortBySize);
    bundleInfo.cssFiles.sort(sortBySize);
    bundleInfo.imageFiles.sort(sortBySize);
    bundleInfo.fontFiles.sort(sortBySize);
    bundleInfo.otherFiles.sort(sortBySize);

    return bundleInfo;
  }

  static getFileType(url) {
    const extension = url.split('.').pop().toLowerCase();
    
    if (['js', 'jsx', 'ts', 'tsx'].includes(extension)) return 'js';
    if (['css', 'scss', 'sass'].includes(extension)) return 'css';
    if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
    if (['woff', 'woff2', 'ttf', 'otf'].includes(extension)) return 'font';
    
    return 'other';
  }

  static formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static logBundleReport() {
    this.getBundleInfo().then(info => {
      if (!info) {
        console.log('📦 Bundle Size: Performance API not available');
        return;
      }

      console.group('📦 Bundle Size Report');
      console.log(`Total Resources: ${info.totalResources}`);
      console.log(`Total Transfer Size: ${this.formatBytes(info.totalTransferSize)}`);
      console.log(`Total Encoded Size: ${this.formatBytes(info.totalEncodedSize)}`);
      
      if (info.jsFiles.length > 0) {
        console.group('JavaScript Files');
        info.jsFiles.slice(0, 5).forEach(file => {
          console.log(`${file.name}: ${this.formatBytes(file.transferSize)}`);
        });
        if (info.jsFiles.length > 5) {
          console.log(`... and ${info.jsFiles.length - 5} more`);
        }
        console.groupEnd();
      }

      if (info.cssFiles.length > 0) {
        console.group('CSS Files');
        info.cssFiles.slice(0, 3).forEach(file => {
          console.log(`${file.name}: ${this.formatBytes(file.transferSize)}`);
        });
        console.groupEnd();
      }

      console.groupEnd();
    });
  }
}

/**
 * Loading Performance Monitor
 */
export class LoadingPerformanceMonitor {
  static getLoadingMetrics() {
    if (!('performance' in window)) {
      return null;
    }

    const navigation = performance.getEntriesByType('navigation')[0];
    if (!navigation) return null;

    return {
      // Core Web Vitals approximation
      loadTime: navigation.loadEventEnd - navigation.fetchStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
      
      // Network timing
      dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcpConnection: navigation.connectEnd - navigation.connectStart,
      serverResponse: navigation.responseEnd - navigation.requestStart,
      domProcessing: navigation.domComplete - navigation.responseEnd,
      
      // Resource loading
      totalResources: performance.getEntriesByType('resource').length,
      
      // Navigation type
      navigationType: this.getNavigationType(navigation.type),
      
      // Memory usage (if available)
      memoryUsage: this.getMemoryUsage()
    };
  }

  static getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fpEntry = paintEntries.find(entry => entry.name === 'first-paint');
    return fpEntry ? fpEntry.startTime : null;
  }

  static getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcpEntry ? fcpEntry.startTime : null;
  }

  static getNavigationType(type) {
    const types = {
      0: 'navigate',
      1: 'reload',
      2: 'back_forward',
      255: 'reserved'
    };
    return types[type] || 'unknown';
  }

  static getMemoryUsage() {
    if ('memory' in performance) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  static logPerformanceReport() {
    const metrics = this.getLoadingMetrics();
    if (!metrics) {
      console.log('⚡ Performance: Metrics not available');
      return;
    }

    console.group('⚡ Loading Performance Report');
    console.log(`Page Load Time: ${metrics.loadTime.toFixed(2)}ms`);
    console.log(`DOM Content Loaded: ${metrics.domContentLoaded.toFixed(2)}ms`);
    
    if (metrics.firstPaint) {
      console.log(`First Paint: ${metrics.firstPaint.toFixed(2)}ms`);
    }
    
    if (metrics.firstContentfulPaint) {
      console.log(`First Contentful Paint: ${metrics.firstContentfulPaint.toFixed(2)}ms`);
    }

    console.group('Network Timing');
    console.log(`DNS Lookup: ${metrics.dnsLookup.toFixed(2)}ms`);
    console.log(`TCP Connection: ${metrics.tcpConnection.toFixed(2)}ms`);
    console.log(`Server Response: ${metrics.serverResponse.toFixed(2)}ms`);
    console.log(`DOM Processing: ${metrics.domProcessing.toFixed(2)}ms`);
    console.groupEnd();

    if (metrics.memoryUsage) {
      console.group('Memory Usage');
      console.log(`Used: ${BundleSizeMonitor.formatBytes(metrics.memoryUsage.used)}`);
      console.log(`Total: ${BundleSizeMonitor.formatBytes(metrics.memoryUsage.total)}`);
      console.log(`Limit: ${BundleSizeMonitor.formatBytes(metrics.memoryUsage.limit)}`);
      console.groupEnd();
    }

    console.groupEnd();
  }
}

/**
 * PWA Performance Monitor
 */
export class PWAPerformanceMonitor {
  constructor() {
    this.metrics = {
      serviceWorkerInstallTime: null,
      cacheHitRate: 0,
      backgroundSyncEvents: 0,
      offlineEvents: 0,
      voiceChunksSynced: 0,
      avgSyncTime: 0
    };
    this.startTime = Date.now();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Service Worker events
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', this.handleSWMessage.bind(this));
    }

    // Network events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Background sync events
    window.addEventListener('background-sync-complete', this.handleBackgroundSync.bind(this));
    window.addEventListener('voice-chunk-synced', this.handleVoiceSync.bind(this));
  }

  handleSWMessage(event) {
    const { type, data } = event.data;
    
    switch (type) {
      case 'SW_INSTALLED':
        this.metrics.serviceWorkerInstallTime = Date.now() - this.startTime;
        break;
      case 'CACHE_HIT':
        this.updateCacheHitRate(true);
        break;
      case 'CACHE_MISS':
        this.updateCacheHitRate(false);
        break;
    }
  }

  handleOnline() {
    console.log('📡 Network: Back online');
    // Track how long we were offline
    if (this.offlineStartTime) {
      const offlineDuration = Date.now() - this.offlineStartTime;
      console.log(`📡 Network: Was offline for ${offlineDuration}ms`);
      this.offlineStartTime = null;
    }
  }

  handleOffline() {
    this.metrics.offlineEvents++;
    this.offlineStartTime = Date.now();
    console.log('📡 Network: Gone offline');
  }

  handleBackgroundSync(event) {
    this.metrics.backgroundSyncEvents++;
    if (event.detail?.duration) {
      this.updateAvgSyncTime(event.detail.duration);
    }
  }

  handleVoiceSync(event) {
    this.metrics.voiceChunksSynced++;
    if (event.detail?.duration) {
      this.updateAvgSyncTime(event.detail.duration);
    }
  }

  updateCacheHitRate(isHit) {
    // Simple moving average for cache hit rate
    const weight = 0.1;
    this.metrics.cacheHitRate = this.metrics.cacheHitRate * (1 - weight) + (isHit ? 1 : 0) * weight;
  }

  updateAvgSyncTime(duration) {
    // Simple moving average for sync time
    const weight = 0.1;
    this.metrics.avgSyncTime = this.metrics.avgSyncTime * (1 - weight) + duration * weight;
  }

  getMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
      cacheHitRatePercent: (this.metrics.cacheHitRate * 100).toFixed(1)
    };
  }

  logPWAReport() {
    const metrics = this.getMetrics();
    
    console.group('🚀 PWA Performance Report');
    console.log(`Uptime: ${(metrics.uptime / 1000 / 60).toFixed(1)} minutes`);
    
    if (metrics.serviceWorkerInstallTime) {
      console.log(`SW Install Time: ${metrics.serviceWorkerInstallTime}ms`);
    }
    
    console.log(`Cache Hit Rate: ${metrics.cacheHitRatePercent}%`);
    console.log(`Background Sync Events: ${metrics.backgroundSyncEvents}`);
    console.log(`Offline Events: ${metrics.offlineEvents}`);
    console.log(`Voice Chunks Synced: ${metrics.voiceChunksSynced}`);
    
    if (metrics.avgSyncTime > 0) {
      console.log(`Average Sync Time: ${metrics.avgSyncTime.toFixed(2)}ms`);
    }
    
    console.groupEnd();
  }

  // Method to be called by components when they sync
  recordVoiceSync(duration) {
    this.handleVoiceSync({ detail: { duration } });
  }

  recordBackgroundSync(duration) {
    this.handleBackgroundSync({ detail: { duration } });
  }
}

/**
 * Comprehensive Performance Dashboard
 */
export class PerformanceDashboard {
  constructor() {
    this.pwaMonitor = new PWAPerformanceMonitor();
    this.startMonitoring();
  }

  startMonitoring() {
    // Log initial reports after page load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => this.logAllReports(), 1000);
      });
    } else {
      setTimeout(() => this.logAllReports(), 1000);
    }

    // Periodic reporting
    setInterval(() => {
      this.logAllReports();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  logAllReports() {
    console.group('📊 Performance Dashboard - Full Report');
    
    LoadingPerformanceMonitor.logPerformanceReport();
    BundleSizeMonitor.logBundleReport();
    this.pwaMonitor.logPWAReport();
    
    console.groupEnd();
  }

  getFullReport() {
    return {
      loading: LoadingPerformanceMonitor.getLoadingMetrics(),
      bundle: BundleSizeMonitor.getBundleInfo(),
      pwa: this.pwaMonitor.getMetrics(),
      timestamp: new Date().toISOString()
    };
  }

  // Export data for analytics
  async exportReport() {
    const report = await this.getFullReport();
    
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `voicehealth-performance-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// Global performance monitor instance
let performanceDashboard = null;

export const initPerformanceMonitoring = () => {
  if (!performanceDashboard && typeof window !== 'undefined') {
    performanceDashboard = new PerformanceDashboard();
  }
  return performanceDashboard;
};

export const getPerformanceMonitor = () => {
  return performanceDashboard;
};

export default {
  BundleSizeMonitor,
  LoadingPerformanceMonitor,
  PWAPerformanceMonitor,
  PerformanceDashboard,
  initPerformanceMonitoring,
  getPerformanceMonitor
};
