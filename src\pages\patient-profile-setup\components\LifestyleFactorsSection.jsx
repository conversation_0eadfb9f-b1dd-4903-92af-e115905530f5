import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';


const LifestyleFactorsSection = ({ 
  data = {}, 
  onUpdate = () => {}, 
  isExpanded = false, 
  onToggle = () => {},
  errors = {}
}) => {
  const handleInputChange = (field, value) => {
    onUpdate({ ...data, [field]: value });
  };

  const handleArrayToggle = (field, value) => {
    const currentArray = data[field] || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    handleInputChange(field, newArray);
  };

  const dietaryOptions = [
    { value: 'vegetarian', label: 'Vegetarian', icon: 'Leaf' },
    { value: 'vegan', label: 'Vegan', icon: 'Sprout' },
    { value: 'keto', label: 'Ketogenic', icon: 'Zap' },
    { value: 'paleo', label: 'Paleo', icon: 'Mountain' },
    { value: 'mediterranean', label: 'Mediterranean', icon: 'Fish' },
    { value: 'gluten_free', label: 'Gluten-Free', icon: 'Shield' },
    { value: 'dairy_free', label: 'Dairy-Free', icon: 'Milk' },
    { value: 'low_sodium', label: 'Low Sodium', icon: 'Droplets' }
  ];

  const exerciseTypes = [
    { value: 'cardio', label: 'Cardio', icon: 'Heart' },
    { value: 'strength', label: 'Strength Training', icon: 'Dumbbell' },
    { value: 'yoga', label: 'Yoga', icon: 'User' },
    { value: 'swimming', label: 'Swimming', icon: 'Waves' },
    { value: 'cycling', label: 'Cycling', icon: 'Bike' },
    { value: 'running', label: 'Running', icon: 'Zap' },
    { value: 'walking', label: 'Walking', icon: 'MapPin' },
    { value: 'sports', label: 'Sports', icon: 'Trophy' }
  ];

  const sleepQualityOptions = [
    { value: 'excellent', label: 'Excellent', color: 'success' },
    { value: 'good', label: 'Good', color: 'primary' },
    { value: 'fair', label: 'Fair', color: 'warning' },
    { value: 'poor', label: 'Poor', color: 'error' }
  ];

  const stressLevels = [
    { value: 'low', label: 'Low', color: 'success' },
    { value: 'moderate', label: 'Moderate', color: 'warning' },
    { value: 'high', label: 'High', color: 'error' },
    { value: 'very_high', label: 'Very High', color: 'error' }
  ];

  return (
    <div className="bg-surface border border-border rounded-xl shadow-minimal overflow-hidden">
      {/* Section Header */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-6 hover:bg-secondary-50 transition-fast"
      >
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-success-50 rounded-lg flex items-center justify-center">
            <Icon name="Activity" size={20} color="var(--color-success)" />
          </div>
          <div className="text-left">
            <h3 className="font-semibold text-text-primary font-heading">
              Lifestyle Factors
            </h3>
            <p className="text-sm text-text-secondary font-caption">
              Diet, exercise, sleep, and habits
            </p>
          </div>
        </div>
        <Icon 
          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
          size={20} 
          color="var(--color-text-secondary)" 
        />
      </button>

      {/* Section Content */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-border">
          <div className="space-y-8">
            {/* Dietary Preferences */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="Apple" size={16} />
                <span>Dietary Preferences</span>
              </h4>
              
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 mb-4">
                {dietaryOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleArrayToggle('dietaryPreferences', option.value)}
                    className={`p-3 rounded-lg border-2 transition-fast text-left ${
                      (data.dietaryPreferences || []).includes(option.value)
                        ? 'border-primary-500 bg-primary-50 text-primary-600' :'border-border hover:border-primary-200 hover:bg-primary-50'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Icon 
                        name={option.icon} 
                        size={16} 
                        color={(data.dietaryPreferences || []).includes(option.value) ? 'var(--color-primary)' : 'var(--color-text-secondary)'}
                      />
                      <span className="text-sm font-medium">{option.label}</span>
                    </div>
                  </button>
                ))}
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Additional Dietary Notes
                </label>
                <textarea
                  placeholder="Any specific dietary restrictions, food allergies, or preferences..."
                  value={data.dietaryNotes || ''}
                  onChange={(e) => handleInputChange('dietaryNotes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary resize-none"
                />
              </div>
            </div>

            {/* Exercise & Physical Activity */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="Dumbbell" size={16} />
                <span>Exercise & Physical Activity</span>
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Exercise Frequency (per week)
                  </label>
                  <select
                    value={data.exerciseFrequency || ''}
                    onChange={(e) => handleInputChange('exerciseFrequency', e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  >
                    <option value="">Select frequency</option>
                    <option value="none">None</option>
                    <option value="1-2">1-2 times</option>
                    <option value="3-4">3-4 times</option>
                    <option value="5-6">5-6 times</option>
                    <option value="daily">Daily</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Exercise Duration (per session)
                  </label>
                  <select
                    value={data.exerciseDuration || ''}
                    onChange={(e) => handleInputChange('exerciseDuration', e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  >
                    <option value="">Select duration</option>
                    <option value="15-30">15-30 minutes</option>
                    <option value="30-45">30-45 minutes</option>
                    <option value="45-60">45-60 minutes</option>
                    <option value="60+">60+ minutes</option>
                  </select>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-text-primary mb-3">
                  Types of Exercise
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                  {exerciseTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => handleArrayToggle('exerciseTypes', type.value)}
                      className={`p-3 rounded-lg border-2 transition-fast text-left ${
                        (data.exerciseTypes || []).includes(type.value)
                          ? 'border-success-500 bg-success-50 text-success-600' :'border-border hover:border-success-200 hover:bg-success-50'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <Icon 
                          name={type.icon} 
                          size={16} 
                          color={(data.exerciseTypes || []).includes(type.value) ? 'var(--color-success)' : 'var(--color-text-secondary)'}
                        />
                        <span className="text-sm font-medium">{type.label}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Sleep Patterns */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="Moon" size={16} />
                <span>Sleep Patterns</span>
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Average Sleep Hours
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="12"
                    step="0.5"
                    placeholder="7.5"
                    value={data.sleepHours || ''}
                    onChange={(e) => handleInputChange('sleepHours', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Bedtime
                  </label>
                  <Input
                    type="time"
                    value={data.bedtime || ''}
                    onChange={(e) => handleInputChange('bedtime', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Wake Time
                  </label>
                  <Input
                    type="time"
                    value={data.wakeTime || ''}
                    onChange={(e) => handleInputChange('wakeTime', e.target.value)}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-3">
                  Sleep Quality
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {sleepQualityOptions.map((quality) => (
                    <button
                      key={quality.value}
                      onClick={() => handleInputChange('sleepQuality', quality.value)}
                      className={`p-3 rounded-lg border-2 transition-fast ${
                        data.sleepQuality === quality.value
                          ? `border-${quality.color}-500 bg-${quality.color}-50 text-${quality.color}-600`
                          : 'border-border hover:border-primary-200 hover:bg-primary-50'
                      }`}
                    >
                      <span className="text-sm font-medium">{quality.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Stress & Mental Health */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="Brain" size={16} />
                <span>Stress & Mental Health</span>
              </h4>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-text-primary mb-3">
                  Current Stress Level
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                  {stressLevels.map((level) => (
                    <button
                      key={level.value}
                      onClick={() => handleInputChange('stressLevel', level.value)}
                      className={`p-3 rounded-lg border-2 transition-fast ${
                        data.stressLevel === level.value
                          ? `border-${level.color}-500 bg-${level.color}-50 text-${level.color}-600`
                          : 'border-border hover:border-primary-200 hover:bg-primary-50'
                      }`}
                    >
                      <span className="text-sm font-medium">{level.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Stress Management Techniques
                </label>
                <textarea
                  placeholder="How do you currently manage stress? (e.g., meditation, exercise, hobbies)"
                  value={data.stressManagement || ''}
                  onChange={(e) => handleInputChange('stressManagement', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary resize-none"
                />
              </div>
            </div>

            {/* Habits */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="Coffee" size={16} />
                <span>Habits & Substances</span>
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Smoking Status
                  </label>
                  <select
                    value={data.smokingStatus || ''}
                    onChange={(e) => handleInputChange('smokingStatus', e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  >
                    <option value="">Select status</option>
                    <option value="never">Never smoked</option>
                    <option value="former">Former smoker</option>
                    <option value="current">Current smoker</option>
                    <option value="occasional">Occasional smoker</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Alcohol Consumption
                  </label>
                  <select
                    value={data.alcoholConsumption || ''}
                    onChange={(e) => handleInputChange('alcoholConsumption', e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  >
                    <option value="">Select frequency</option>
                    <option value="never">Never</option>
                    <option value="rarely">Rarely</option>
                    <option value="occasionally">Occasionally</option>
                    <option value="regularly">Regularly</option>
                    <option value="daily">Daily</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Caffeine Intake (cups/day)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="20"
                    placeholder="2"
                    value={data.caffeineIntake || ''}
                    onChange={(e) => handleInputChange('caffeineIntake', e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Water Intake (glasses/day)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="20"
                    placeholder="8"
                    value={data.waterIntake || ''}
                    onChange={(e) => handleInputChange('waterIntake', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Additional Notes */}
            <div>
              <h4 className="font-medium text-text-primary mb-4 flex items-center space-x-2">
                <Icon name="FileText" size={16} />
                <span>Additional Information</span>
              </h4>
              
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Other Lifestyle Factors
                </label>
                <textarea
                  placeholder="Any other lifestyle factors, habits, or information that might be relevant for your health consultations..."
                  value={data.additionalNotes || ''}
                  onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary resize-none"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LifestyleFactorsSection;