import React, { useState } from 'react';
import { Globe, ChevronDown, Check } from 'lucide-react';

const LanguageConfigurationSection = ({ isExpanded, onToggle, settings, onSettingsChange }) => {
  const [selectedLanguage, setSelectedLanguage] = useState(settings?.language || 'en-US');
  const [selectedRegion, setSelectedRegion] = useState(settings?.region || 'US');

  const languages = [
    { code: 'en-US', name: 'English (US)', regions: ['US', 'CA', 'AU', 'UK'] },
    { code: 'es-ES', name: 'Spanish (Spain)', regions: ['ES', 'MX', 'AR', 'CO'] },
    { code: 'fr-FR', name: 'French (France)', regions: ['FR', 'CA', 'CH', 'BE'] },
    { code: 'de-DE', name: 'German (Germany)', regions: ['DE', 'AT', 'CH'] },
    { code: 'it-IT', name: 'Italian (Italy)', regions: ['IT', 'CH'] },
    { code: 'pt-BR', name: 'Portuguese (Brazil)', regions: ['BR', 'PT'] },
    { code: 'zh-CN', name: 'Chinese (Simplified)', regions: ['CN', 'SG'] },
    { code: 'ja-JP', name: 'Japanese (Japan)', regions: ['JP'] },
    { code: 'ko-KR', name: 'Korean (Korea)', regions: ['KR'] },
    { code: 'ar-SA', name: 'Arabic (Saudi Arabia)', regions: ['SA', 'AE', 'EG'] }
  ];

  const medicalTerminologyOptions = [
    { id: 'standard', name: 'Standard Medical Terms', description: 'Professional medical terminology' },
    { id: 'simplified', name: 'Simplified Language', description: 'Easy-to-understand explanations' },
    { id: 'cultural', name: 'Culturally Adapted', description: 'Culture-specific medical context' }
  ];

  const handleLanguageChange = (langCode) => {
    setSelectedLanguage(langCode);
    const langData = languages.find(lang => lang.code === langCode);
    if (langData && langData.regions.length > 0) {
      setSelectedRegion(langData.regions[0]);
    }
    onSettingsChange?.({
      ...settings,
      language: langCode,
      region: langData?.regions[0] || 'US'
    });
  };

  const handleRegionChange = (region) => {
    setSelectedRegion(region);
    onSettingsChange?.({
      ...settings,
      region: region
    });
  };

  const handleTerminologyChange = (terminology) => {
    onSettingsChange?.({
      ...settings,
      medicalTerminology: terminology
    });
  };

  const selectedLangData = languages.find(lang => lang.code === selectedLanguage);

  return (
    <div className="bg-surface border border-border rounded-xl overflow-hidden">
      <button
        onClick={onToggle}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-secondary-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary-50 rounded-lg">
            <Globe className="w-5 h-5 text-primary-600" />
          </div>
          <div className="text-left">
            <h3 className="text-lg font-semibold text-text-primary">Language Configuration</h3>
            <p className="text-sm text-text-muted">Interface language and regional settings</p>
          </div>
        </div>
        <ChevronDown className={`w-5 h-5 text-text-muted transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
      </button>

      {isExpanded && (
        <div className="px-6 pb-6 space-y-6">
          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Primary Language
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  className={`p-3 rounded-lg border text-left transition-all ${
                    selectedLanguage === language.code
                      ? 'border-primary-500 bg-primary-50 text-primary-700' :'border-border hover:border-border-active hover:bg-secondary-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{language.name}</span>
                    {selectedLanguage === language.code && (
                      <Check className="w-4 h-4 text-primary-600" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Region Selection */}
          {selectedLangData && selectedLangData.regions.length > 1 && (
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Regional Variant
              </label>
              <div className="flex flex-wrap gap-2">
                {selectedLangData.regions.map((region) => (
                  <button
                    key={region}
                    onClick={() => handleRegionChange(region)}
                    className={`px-4 py-2 rounded-lg border text-sm font-medium transition-all ${
                      selectedRegion === region
                        ? 'border-primary-500 bg-primary-500 text-white' :'border-border hover:border-border-active hover:bg-secondary-50'
                    }`}
                  >
                    {region}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Medical Terminology Preference */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Medical Terminology Preference
            </label>
            <div className="space-y-3">
              {medicalTerminologyOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleTerminologyChange(option.id)}
                  className={`w-full p-4 rounded-lg border text-left transition-all ${
                    settings?.medicalTerminology === option.id
                      ? 'border-primary-500 bg-primary-50' :'border-border hover:border-border-active hover:bg-secondary-50'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium text-text-primary">{option.name}</h4>
                      <p className="text-sm text-text-muted mt-1">{option.description}</p>
                    </div>
                    {settings?.medicalTerminology === option.id && (
                      <Check className="w-5 h-5 text-primary-600 mt-0.5" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Cultural Context Adaptation */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="text-sm font-medium text-text-primary">
                Cultural Context Adaptation
              </label>
              <button
                onClick={() => onSettingsChange?.({
                  ...settings,
                  culturalAdaptation: !settings?.culturalAdaptation
                })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings?.culturalAdaptation ? 'bg-primary-600' : 'bg-secondary-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings?.culturalAdaptation ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            <p className="text-sm text-text-muted">
              Adapt health guidance and medical explanations to cultural context and local healthcare practices
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageConfigurationSection;