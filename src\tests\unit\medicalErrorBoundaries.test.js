/**
 * COMPREHENSIVE UNIT TESTS FOR MEDICAL ERROR BOUNDARIES
 * 
 * This test suite provides comprehensive coverage for medical error boundaries
 * with focus on:
 * - Medical data preservation during errors
 * - Emergency access during system failures
 * - Error recovery mechanisms
 * - Patient safety prioritization
 * - Audit logging for error events
 * - Performance under error conditions
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Medical data must never be lost during errors
 * - Emergency access must remain available
 * - Error recovery must prioritize patient safety
 * - All errors must be logged for audit
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MedicalErrorBoundary } from '../../components/errorBoundaries/MedicalErrorBoundary';
import { EncryptionErrorBoundary } from '../../components/errorBoundaries/EncryptionErrorBoundary';
import { NetworkErrorBoundary } from '../../components/errorBoundaries/NetworkErrorBoundary';
import { MedicalErrorProvider } from '../../components/errorBoundaries/MedicalErrorProvider';

// Mock dependencies
vi.mock('../../utils/auditLogger', () => ({
  default: global.testHelpers.createMockAuditLogger()
}));

vi.mock('../../utils/offlineHealthRecordsService', () => ({
  default: {
    preserveMedicalData: vi.fn(() => Promise.resolve({ success: true })),
    getMedicalData: vi.fn(() => Promise.resolve({ success: true, data: [] })),
    clearPreservedData: vi.fn(() => Promise.resolve({ success: true }))
  }
}));

vi.mock('../../utils/encryptionService', () => ({
  default: global.testHelpers.createMockEncryptionService()
}));

describe('Medical Error Boundaries', () => {
  let mockAuditLogger;
  let mockOfflineService;
  let mockEncryptionService;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAuditLogger = global.testHelpers.createMockAuditLogger();
    
    // Setup console error mock to prevent test noise
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('MedicalErrorBoundary', () => {
    const ThrowError = ({ shouldThrow = false, errorType = 'general' }) => {
      if (shouldThrow) {
        const error = new Error('Test error');
        error.type = errorType;
        throw error;
      }
      return <div>Normal Component</div>;
    };

    it('should catch and handle general component errors', () => {
      // Arrange & Act
      render(
        <MedicalErrorBoundary
          componentName="TestComponent"
          medicalContext={{ patientId: global.mockUser.id }}
          onError={vi.fn()}
          preserveDataOnError={true}
        >
          <ThrowError shouldThrow={true} />
        </MedicalErrorBoundary>
      );

      // Assert
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/medical data has been preserved/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /emergency access/i })).toBeInTheDocument();
    });

    it('should preserve medical data when errors occur', async () => {
      // Arrange
      const onError = vi.fn();
      const medicalContext = {
        patientId: global.mockUser.id,
        currentData: global.mockMedicalData.condition
      };

      // Act
      render(
        <MedicalErrorBoundary
          componentName="MedicalDataComponent"
          medicalContext={medicalContext}
          onError={onError}
          preserveDataOnError={true}
        >
          <ThrowError shouldThrow={true} />
        </MedicalErrorBoundary>
      );

      // Assert
      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith(
          expect.any(Error),
          expect.objectContaining({
            componentName: 'MedicalDataComponent',
            medicalContext,
            dataPreserved: true
          })
        );
      });
    });

    it('should provide emergency access during errors', async () => {
      // Arrange
      render(
        <MedicalErrorBoundary
          componentName="TestComponent"
          medicalContext={{ patientId: global.mockUser.id }}
          enableEmergencyMode={true}
        >
          <ThrowError shouldThrow={true} />
        </MedicalErrorBoundary>
      );

      // Act
      const emergencyButton = screen.getByRole('button', { name: /emergency access/i });
      fireEvent.click(emergencyButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/emergency mode activated/i)).toBeInTheDocument();
        expect(screen.getByText(/basic medical functions available/i)).toBeInTheDocument();
      });
    });

    it('should log errors for audit purposes', async () => {
      // Arrange
      const error = new Error('Medical component error');
      const medicalContext = { patientId: global.mockUser.id };

      // Act
      render(
        <MedicalErrorBoundary
          componentName="MedicalComponent"
          medicalContext={medicalContext}
          onError={vi.fn()}
        >
          <ThrowError shouldThrow={true} />
        </MedicalErrorBoundary>
      );

      // Assert
      await waitFor(() => {
        expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
          'component_error',
          'medium',
          expect.objectContaining({
            component_name: 'MedicalComponent',
            error_message: expect.any(String),
            medical_context: medicalContext,
            data_preservation_attempted: true
          })
        );
      });
    });

    it('should handle critical medical errors with high priority', async () => {
      // Arrange
      render(
        <MedicalErrorBoundary
          componentName="CriticalMedicalComponent"
          medicalContext={{ 
            patientId: global.mockUser.id,
            isEmergency: true 
          }}
          onError={vi.fn()}
        >
          <ThrowError shouldThrow={true} errorType="critical_medical" />
        </MedicalErrorBoundary>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/critical medical error/i)).toBeInTheDocument();
        expect(screen.getByText(/emergency protocols activated/i)).toBeInTheDocument();
        expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
          'critical_medical_error',
          'critical',
          expect.objectContaining({
            immediate_attention_required: true,
            emergency_protocols_activated: true
          })
        );
      });
    });

    it('should provide error recovery options', async () => {
      // Arrange
      const onRecovery = vi.fn();

      render(
        <MedicalErrorBoundary
          componentName="TestComponent"
          onRecovery={onRecovery}
        >
          <ThrowError shouldThrow={true} />
        </MedicalErrorBoundary>
      );

      // Act
      const retryButton = screen.getByRole('button', { name: /retry/i });
      fireEvent.click(retryButton);

      // Assert
      await waitFor(() => {
        expect(onRecovery).toHaveBeenCalledWith(
          expect.objectContaining({
            recoveryMethod: 'retry',
            timestamp: expect.any(String)
          })
        );
      });
    });
  });

  describe('EncryptionErrorBoundary', () => {
    const EncryptionError = ({ shouldThrow = false }) => {
      if (shouldThrow) {
        const error = new Error('Encryption failed');
        error.type = 'encryption_error';
        throw error;
      }
      return <div>Encrypted Component</div>;
    };

    it('should handle encryption failures gracefully', () => {
      // Arrange & Act
      render(
        <EncryptionErrorBoundary
          onEncryptionError={vi.fn()}
          allowUnencryptedFallback={true}
        >
          <EncryptionError shouldThrow={true} />
        </EncryptionErrorBoundary>
      );

      // Assert
      expect(screen.getByText(/encryption error occurred/i)).toBeInTheDocument();
      expect(screen.getByText(/fallback mode activated/i)).toBeInTheDocument();
    });

    it('should provide emergency data export during encryption failures', async () => {
      // Arrange
      render(
        <EncryptionErrorBoundary
          onEncryptionError={vi.fn()}
          emergencyExportEnabled={true}
        >
          <EncryptionError shouldThrow={true} />
        </EncryptionErrorBoundary>
      );

      // Act
      const exportButton = screen.getByRole('button', { name: /emergency export/i });
      fireEvent.click(exportButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/emergency export initiated/i)).toBeInTheDocument();
        expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
          'emergency_data_export',
          'high',
          expect.objectContaining({
            reason: 'encryption_failure',
            export_method: 'emergency'
          })
        );
      });
    });

    it('should prevent unencrypted fallback when not allowed', () => {
      // Arrange & Act
      render(
        <EncryptionErrorBoundary
          onEncryptionError={vi.fn()}
          allowUnencryptedFallback={false}
        >
          <EncryptionError shouldThrow={true} />
        </EncryptionErrorBoundary>
      );

      // Assert
      expect(screen.getByText(/encryption required/i)).toBeInTheDocument();
      expect(screen.getByText(/system locked for security/i)).toBeInTheDocument();
      expect(screen.queryByText(/fallback mode/i)).not.toBeInTheDocument();
    });
  });

  describe('NetworkErrorBoundary', () => {
    const NetworkError = ({ shouldThrow = false }) => {
      if (shouldThrow) {
        const error = new Error('Network connection failed');
        error.type = 'network_error';
        throw error;
      }
      return <div>Network Component</div>;
    };

    it('should handle network errors with offline mode', () => {
      // Arrange & Act
      render(
        <NetworkErrorBoundary
          onNetworkError={vi.fn()}
          enableOfflineMode={true}
        >
          <NetworkError shouldThrow={true} />
        </NetworkErrorBoundary>
      );

      // Assert
      expect(screen.getByText(/network error/i)).toBeInTheDocument();
      expect(screen.getByText(/offline mode activated/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /retry connection/i })).toBeInTheDocument();
    });

    it('should prioritize critical operations during network errors', async () => {
      // Arrange
      render(
        <NetworkErrorBoundary
          onNetworkError={vi.fn()}
          criticalOperation={true}
          enableOfflineMode={true}
        >
          <NetworkError shouldThrow={true} />
        </NetworkErrorBoundary>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/critical operation detected/i)).toBeInTheDocument();
        expect(screen.getByText(/emergency sync enabled/i)).toBeInTheDocument();
        expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
          'network_error_critical_operation',
          'high',
          expect.objectContaining({
            critical_operation: true,
            offline_mode_activated: true
          })
        );
      });
    });

    it('should attempt automatic reconnection', async () => {
      // Arrange
      const onNetworkError = vi.fn();
      
      render(
        <NetworkErrorBoundary
          onNetworkError={onNetworkError}
          enableOfflineMode={true}
        >
          <NetworkError shouldThrow={true} />
        </NetworkErrorBoundary>
      );

      // Act - Wait for automatic retry
      await waitFor(() => {
        expect(screen.getByText(/attempting reconnection/i)).toBeInTheDocument();
      }, { timeout: 5000 });

      // Assert
      expect(onNetworkError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          reconnection_attempted: true,
          offline_mode_active: true
        })
      );
    });
  });

  describe('MedicalErrorProvider Integration', () => {
    const MultiLayerError = ({ errorType }) => {
      const error = new Error(`${errorType} error`);
      error.type = errorType;
      throw error;
    };

    it('should handle layered error boundaries correctly', async () => {
      // Arrange & Act
      render(
        <MedicalErrorProvider
          emergencyContact="+**********"
          enableOfflineMode={true}
          enableEncryptionFallback={true}
        >
          <MultiLayerError errorType="network" />
        </MedicalErrorProvider>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
        expect(screen.getByText(/offline mode activated/i)).toBeInTheDocument();
      });
    });

    it('should escalate critical errors appropriately', async () => {
      // Arrange
      const onCriticalError = vi.fn();

      render(
        <MedicalErrorProvider
          onCriticalError={onCriticalError}
          emergencyContact="+**********"
        >
          <MultiLayerError errorType="critical_medical" />
        </MedicalErrorProvider>
      );

      // Assert
      await waitFor(() => {
        expect(onCriticalError).toHaveBeenCalledWith(
          expect.any(Error),
          'critical_medical_error'
        );
        expect(screen.getByText(/emergency contact notified/i)).toBeInTheDocument();
      });
    });

    it('should maintain error state across boundary layers', async () => {
      // Arrange
      render(
        <MedicalErrorProvider>
          <MultiLayerError errorType="encryption" />
        </MedicalErrorProvider>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/encryption error/i)).toBeInTheDocument();
        expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
          'layered_error_boundary_activation',
          'medium',
          expect.objectContaining({
            error_type: 'encryption',
            boundary_layer: 'encryption',
            medical_data_preserved: true
          })
        );
      });
    });
  });

  describe('Performance Under Error Conditions', () => {
    it('should handle multiple concurrent errors efficiently', async () => {
      // Arrange
      const ErrorComponent = ({ id }) => {
        throw new Error(`Error ${id}`);
      };

      const startTime = performance.now();

      // Act
      const errorBoundaries = Array(10).fill().map((_, i) => (
        <MedicalErrorBoundary key={i} componentName={`Component${i}`}>
          <ErrorComponent id={i} />
        </MedicalErrorBoundary>
      ));

      render(<div>{errorBoundaries}</div>);

      const endTime = performance.now();

      // Assert
      expect(endTime - startTime).toBeLessThan(1000); // Should handle within 1 second
      expect(screen.getAllByText(/something went wrong/i)).toHaveLength(10);
    });

    it('should maintain system responsiveness during error recovery', async () => {
      // Arrange
      const onRecovery = vi.fn();

      render(
        <MedicalErrorBoundary
          componentName="TestComponent"
          onRecovery={onRecovery}
        >
          <div>Test Component</div>
        </MedicalErrorBoundary>
      );

      // Simulate error and recovery
      const startTime = performance.now();
      
      // Act - Multiple recovery attempts
      for (let i = 0; i < 5; i++) {
        const retryButton = screen.queryByRole('button', { name: /retry/i });
        if (retryButton) {
          fireEvent.click(retryButton);
          await waitFor(() => {}, { timeout: 100 });
        }
      }

      const endTime = performance.now();

      // Assert
      expect(endTime - startTime).toBeLessThan(2000); // Should remain responsive
    });
  });
});
