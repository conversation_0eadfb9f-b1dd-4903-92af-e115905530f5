import React from 'react';
import Icon from '../../../components/AppIcon';

const ProfileProgressIndicator = ({ 
  currentStep = 1, 
  totalSteps = 3, 
  completionPercentage = 0,
  sectionProgress = {},
  className = ''
}) => {
  const steps = [
    {
      id: 1,
      title: 'Personal Info',
      description: 'Basic details',
      icon: 'User',
      key: 'personalInfo'
    },
    {
      id: 2,
      title: 'Medical History',
      description: 'Health records',
      icon: 'Heart',
      key: 'medicalHistory'
    },
    {
      id: 3,
      title: 'Lifestyle',
      description: 'Habits & preferences',
      icon: 'Activity',
      key: 'lifestyle'
    }
  ];

  const getStepStatus = (step) => {
    const progress = sectionProgress[step.key] || 0;
    if (progress >= 100) return 'completed';
    if (progress > 0) return 'in-progress';
    if (step.id === currentStep) return 'current';
    return 'pending';
  };

  const getStepColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'current':
        return 'primary';
      default:
        return 'secondary';
    }
  };

  const getCompletionMessage = () => {
    if (completionPercentage >= 100) {
      return 'Profile complete! Ready for consultations.';
    }
    if (completionPercentage >= 75) {
      return 'Almost done! Just a few more details.';
    }
    if (completionPercentage >= 50) {
      return 'Great progress! Keep going.';
    }
    if (completionPercentage >= 25) {
      return 'Good start! Continue building your profile.';
    }
    return 'Let\'s get started with your health profile.';
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-text-primary font-heading">
            Profile Setup
          </h2>
          <p className="text-sm text-text-secondary font-caption">
            {getCompletionMessage()}
          </p>
        </div>
        
        <div className="text-right">
          <div className="text-2xl font-bold text-primary-600 font-data">
            {Math.round(completionPercentage)}%
          </div>
          <div className="text-xs text-text-secondary">Complete</div>
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-text-secondary mb-2">
          <span>Overall Progress</span>
          <span>{Math.round(completionPercentage)}% of 100%</span>
        </div>
        <div className="w-full bg-secondary-100 rounded-full h-3">
          <div 
            className="bg-primary-500 h-3 rounded-full transition-all duration-500 relative overflow-hidden"
            style={{ width: `${completionPercentage}%` }}
          >
            {completionPercentage > 0 && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
            )}
          </div>
        </div>
      </div>

      {/* Steps */}
      <div className="space-y-4">
        {steps.map((step, index) => {
          const status = getStepStatus(step);
          const color = getStepColor(status);
          const progress = sectionProgress[step.key] || 0;
          
          return (
            <div key={step.id} className="relative">
              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="absolute left-5 top-12 w-0.5 h-8 bg-secondary-200"></div>
              )}
              
              <div className="flex items-start space-x-4">
                {/* Step Icon */}
                <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all ${
                  status === 'completed' ? 'bg-success-500 border-success-500' :
                  status === 'in-progress' ? 'bg-warning-50 border-warning-500' :
                  status === 'current'? 'bg-primary-50 border-primary-500' : 'bg-secondary-50 border-secondary-300'
                }`}>
                  {status === 'completed' ? (
                    <Icon name="Check" size={16} color="white" />
                  ) : (
                    <Icon 
                      name={step.icon} 
                      size={16} 
                      color={`var(--color-${color})`}
                    />
                  )}
                </div>
                
                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className={`font-medium ${
                      status === 'current' ? 'text-primary-600' :
                      status === 'completed'? 'text-success-600' : 'text-text-primary'
                    }`}>
                      {step.title}
                    </h3>
                    
                    {/* Status Badge */}
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      status === 'completed' ? 'bg-success-50 text-success-600' :
                      status === 'in-progress' ? 'bg-warning-50 text-warning-600' :
                      status === 'current'? 'bg-primary-50 text-primary-600' : 'bg-secondary-50 text-secondary-600'
                    }`}>
                      {status === 'completed' ? 'Complete' :
                       status === 'in-progress' ? `${Math.round(progress)}%` :
                       status === 'current'? 'Current' : 'Pending'}
                    </span>
                  </div>
                  
                  <p className="text-sm text-text-secondary mb-2">
                    {step.description}
                  </p>
                  
                  {/* Section Progress Bar */}
                  {(status === 'in-progress' || status === 'completed') && (
                    <div className="w-full bg-secondary-100 rounded-full h-1.5">
                      <div 
                        className={`h-1.5 rounded-full transition-all duration-300 ${
                          status === 'completed' ? 'bg-success-500' : 'bg-warning-500'
                        }`}
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Stats */}
      <div className="mt-6 pt-6 border-t border-border">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-success-600 font-data">
              {Object.values(sectionProgress).filter(p => p >= 100).length}
            </div>
            <div className="text-xs text-text-secondary">Completed</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-warning-600 font-data">
              {Object.values(sectionProgress).filter(p => p > 0 && p < 100).length}
            </div>
            <div className="text-xs text-text-secondary">In Progress</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-secondary-600 font-data">
              {Object.values(sectionProgress).filter(p => p === 0).length}
            </div>
            <div className="text-xs text-text-secondary">Remaining</div>
          </div>
        </div>
      </div>

      {/* Completion Benefits */}
      {completionPercentage < 100 && (
        <div className="mt-6 p-4 bg-primary-50 rounded-lg border border-primary-200">
          <h4 className="font-medium text-primary-600 mb-2 flex items-center space-x-2">
            <Icon name="Star" size={16} />
            <span>Complete Your Profile</span>
          </h4>
          <ul className="space-y-1 text-sm text-primary-600">
            <li className="flex items-center space-x-2">
              <Icon name="Check" size={12} />
              <span>Get personalized health insights</span>
            </li>
            <li className="flex items-center space-x-2">
              <Icon name="Check" size={12} />
              <span>Receive tailored AI recommendations</span>
            </li>
            <li className="flex items-center space-x-2">
              <Icon name="Check" size={12} />
              <span>Enable advanced consultation features</span>
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default ProfileProgressIndicator;