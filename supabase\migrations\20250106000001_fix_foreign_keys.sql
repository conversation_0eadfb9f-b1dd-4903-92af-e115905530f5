-- Critical Foreign Key Fixes - Phase 1 Remediation
-- VoiceHealth AI - Gap Analysis Remediation
-- Created: 2025-01-06

-- =====================================================
-- ADD MISSING FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Fix 1: Medical terminology translations verified_by reference
-- Add foreign key constraint to link verified_by to auth.users
ALTER TABLE medical_terminology_translations 
ADD CONSTRAINT fk_medical_translations_verified_by 
FOREIGN KEY (verified_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Fix 2: Cultural focus groups facilitator reference
-- Add facilitator_id column and foreign key constraint
ALTER TABLE cultural_focus_groups 
ADD COLUMN facilitator_id UUID,
ADD CONSTRAINT fk_focus_groups_facilitator 
FOREIGN KEY (facilitator_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Update existing facilitator text field to reference user ID where possible
COMMENT ON COLUMN cultural_focus_groups.facilitator IS 'Deprecated: Use facilitator_id instead';
COMMENT ON COLUMN cultural_focus_groups.facilitator_id IS 'References auth.users(id) for the session facilitator';

-- Fix 3: Regional deployments created_by reference
-- Add created_by column and foreign key constraint
ALTER TABLE regional_deployments 
ADD COLUMN created_by UUID,
ADD CONSTRAINT fk_regional_deployments_created_by 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Fix 4: Performance metrics component reference
-- Add component_id column to link to system health checks
ALTER TABLE performance_metrics 
ADD COLUMN component_id UUID,
ADD CONSTRAINT fk_performance_metrics_component 
FOREIGN KEY (component_id) REFERENCES system_health_checks(id) ON DELETE CASCADE;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_performance_metrics_component_id 
ON performance_metrics(component_id);

-- Fix 5: System health checks region reference
-- Add region_deployment_id to link to regional deployments
ALTER TABLE system_health_checks 
ADD COLUMN region_deployment_id UUID,
ADD CONSTRAINT fk_health_checks_region 
FOREIGN KEY (region_deployment_id) REFERENCES regional_deployments(id) ON DELETE SET NULL;

-- Fix 6: Deployment configurations created_by reference
-- Add proper foreign key constraint for created_by
ALTER TABLE deployment_configurations 
ADD CONSTRAINT fk_deployment_configs_created_by 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Fix 7: Monitoring dashboards created_by reference
-- Add proper foreign key constraint for created_by
ALTER TABLE monitoring_dashboards 
ADD CONSTRAINT fk_dashboards_created_by 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Fix 8: Alert configurations created_by reference
-- Add proper foreign key constraint for created_by
ALTER TABLE alert_configurations 
ADD CONSTRAINT fk_alerts_created_by 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Fix 9: Incident management assigned_to reference
-- Add proper foreign key constraint for assigned_to
ALTER TABLE incident_management 
ADD CONSTRAINT fk_incidents_assigned_to 
FOREIGN KEY (assigned_to) REFERENCES auth.users(id) ON DELETE SET NULL;

-- =====================================================
-- ADD MISSING RELATIONSHIPS TO EXISTING TABLES
-- =====================================================

-- Fix 10: Link performance metrics to clinical notes
-- Add clinical_note_id to track performance of clinical documentation
ALTER TABLE performance_metrics 
ADD COLUMN clinical_note_id UUID,
ADD CONSTRAINT fk_performance_metrics_clinical_note 
FOREIGN KEY (clinical_note_id) REFERENCES clinical_notes(id) ON DELETE CASCADE;

-- Fix 11: Link cultural focus groups to cultural adaptations
-- Add cultural_adaptation_id to track which adaptations were validated
ALTER TABLE cultural_focus_groups 
ADD COLUMN cultural_adaptation_id UUID;

-- Note: We'll add the foreign key after ensuring cultural_adaptations table exists
-- This will be handled in a separate migration if needed

-- Fix 12: Link regional deployments to consultation sessions
-- Add consultation_session_id to track regional usage
ALTER TABLE regional_deployments 
ADD COLUMN last_consultation_session_id UUID;

-- Note: Foreign key will be added when consultation_sessions table is confirmed

-- =====================================================
-- UPDATE EXISTING DATA TO MAINTAIN INTEGRITY
-- =====================================================

-- Update performance_metrics to link existing component names to health check IDs
UPDATE performance_metrics 
SET component_id = (
    SELECT id FROM system_health_checks 
    WHERE system_health_checks.component = performance_metrics.component 
    LIMIT 1
)
WHERE component_id IS NULL AND component IS NOT NULL;

-- Update system_health_checks to link to regional deployments where possible
UPDATE system_health_checks 
SET region_deployment_id = (
    SELECT id FROM regional_deployments 
    WHERE regional_deployments.country = system_health_checks.region 
    LIMIT 1
)
WHERE region_deployment_id IS NULL AND region IS NOT NULL;

-- =====================================================
-- ADD VALIDATION CONSTRAINTS
-- =====================================================

-- Ensure medical terminology translations have valid confidence scores
ALTER TABLE medical_terminology_translations 
ADD CONSTRAINT chk_confidence_score_range 
CHECK (confidence_score IS NULL OR (confidence_score >= 0 AND confidence_score <= 1));

-- Ensure cultural focus groups have valid satisfaction scores
ALTER TABLE cultural_focus_groups 
ADD CONSTRAINT chk_satisfaction_range 
CHECK (overall_satisfaction IS NULL OR (overall_satisfaction >= 0 AND overall_satisfaction <= 5));

ALTER TABLE cultural_focus_groups 
ADD CONSTRAINT chk_cultural_appropriateness_range 
CHECK (cultural_appropriateness IS NULL OR (cultural_appropriateness >= 0 AND cultural_appropriateness <= 5));

-- Ensure performance metrics have positive values where appropriate
ALTER TABLE performance_metrics 
ADD CONSTRAINT chk_positive_metric_value 
CHECK (metric_value >= 0);

-- Ensure system health checks have valid response times
ALTER TABLE system_health_checks 
ADD CONSTRAINT chk_positive_response_time 
CHECK (response_time IS NULL OR response_time >= 0);

-- Ensure system health checks have valid error rates
ALTER TABLE system_health_checks 
ADD CONSTRAINT chk_error_rate_range 
CHECK (error_rate IS NULL OR (error_rate >= 0 AND error_rate <= 100));

-- =====================================================
-- UPDATE ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Update policies to account for new foreign key relationships

-- Medical terminology translations - allow users to see their own verifications
CREATE POLICY "users_see_own_verifications" ON medical_terminology_translations 
    FOR SELECT USING (verified_by = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'cultural_expert'));

-- Cultural focus groups - allow facilitators to manage their own sessions
CREATE POLICY "facilitators_manage_own_sessions" ON cultural_focus_groups 
    FOR ALL USING (facilitator_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'researcher'));

-- Regional deployments - allow creators to manage their deployments
CREATE POLICY "creators_manage_deployments" ON regional_deployments 
    FOR ALL USING (created_by = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'system'));

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for new foreign key columns
CREATE INDEX IF NOT EXISTS idx_medical_translations_verified_by 
ON medical_terminology_translations(verified_by);

CREATE INDEX IF NOT EXISTS idx_focus_groups_facilitator_id 
ON cultural_focus_groups(facilitator_id);

CREATE INDEX IF NOT EXISTS idx_regional_deployments_created_by 
ON regional_deployments(created_by);

CREATE INDEX IF NOT EXISTS idx_health_checks_region_deployment 
ON system_health_checks(region_deployment_id);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_clinical_note 
ON performance_metrics(clinical_note_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_performance_metrics_component_timestamp 
ON performance_metrics(component_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_health_checks_status_component 
ON system_health_checks(status, component);

-- =====================================================
-- ADD HELPFUL COMMENTS
-- =====================================================

COMMENT ON CONSTRAINT fk_medical_translations_verified_by ON medical_terminology_translations 
IS 'Links medical terminology verifications to the user who verified them';

COMMENT ON CONSTRAINT fk_focus_groups_facilitator ON cultural_focus_groups 
IS 'Links cultural focus group sessions to their facilitator user';

COMMENT ON CONSTRAINT fk_regional_deployments_created_by ON regional_deployments 
IS 'Links regional deployments to the user who created them';

COMMENT ON CONSTRAINT fk_performance_metrics_component ON performance_metrics 
IS 'Links performance metrics to specific system components';

COMMENT ON CONSTRAINT fk_health_checks_region ON system_health_checks 
IS 'Links system health checks to their regional deployment';

-- =====================================================
-- VALIDATION QUERIES
-- =====================================================

-- Verify foreign key constraints are working
DO $$
BEGIN
    -- Test medical terminology translations constraint
    BEGIN
        INSERT INTO medical_terminology_translations (english_term, translated_term, target_language, category, verified_by)
        VALUES ('test_constraint', 'test', 'en', 'symptom', 'invalid-user-id');
        RAISE EXCEPTION 'Foreign key constraint failed - should not allow invalid user ID';
    EXCEPTION
        WHEN foreign_key_violation THEN
            RAISE NOTICE 'Foreign key constraint working correctly for medical_terminology_translations';
    END;
    
    -- Add more constraint tests as needed
    RAISE NOTICE 'Foreign key constraint validation completed';
END $$;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log successful completion
INSERT INTO migration_log (migration_name, completed_at, description) 
VALUES (
    '20250106000001_fix_foreign_keys', 
    NOW(), 
    'Added missing foreign key constraints and relationships between Phase 1 tables and existing tables'
) ON CONFLICT (migration_name) DO UPDATE SET 
    completed_at = NOW(),
    description = EXCLUDED.description;

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

SELECT 'Foreign key constraints migration completed successfully' as status;
