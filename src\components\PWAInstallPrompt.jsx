/**
 * PWA Installation Prompt Component
 * Handles app installation prompts and user onboarding
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import Button from './ui/Button';
import { Badge } from './ui/badge';
import { 
  Download, 
  X, 
  Smartphone, 
  Wifi, 
  Zap, 
  Shield,
  CheckCircle,
  ArrowRight,
  Star
} from 'lucide-react';
import use<PERSON><PERSON> from '../hooks/usePWA';

const PWAInstallPrompt = ({ 
  showOnLoad = false, 
  autoPromptDelay = 30000, // 30 seconds
  onInstallSuccess,
  onPromptDismiss 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showFeatures, setShowFeatures] = useState(false);
  const [installStep, setInstallStep] = useState('prompt'); // prompt, installing, success, error
  const [hasBeenDismissed, setHasBeenDismissed] = useState(false);
  
  const pwa = usePWA();

  // Check if user has previously dismissed the prompt
  useEffect(() => {
    const dismissed = localStorage.getItem('pwa-install-dismissed');
    const dismissedTime = dismissed ? parseInt(dismissed) : 0;
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    setHasBeenDismissed(dismissedTime > oneWeekAgo);
  }, []);

  // Auto-show prompt after delay
  useEffect(() => {
    if (!showOnLoad || !pwa.canInstall || hasBeenDismissed) return;

    const timer = setTimeout(() => {
      setIsVisible(true);
    }, autoPromptDelay);

    return () => clearTimeout(timer);
  }, [showOnLoad, pwa.canInstall, hasBeenDismissed, autoPromptDelay]);

  // Show prompt when install becomes available
  useEffect(() => {
    if (pwa.canInstall && showOnLoad && !hasBeenDismissed) {
      setIsVisible(true);
    }
  }, [pwa.canInstall, showOnLoad, hasBeenDismissed]);

  const handleInstall = async () => {
    setInstallStep('installing');
    
    try {
      const result = await pwa.installApp();
      
      if (result) {
        setInstallStep('success');
        onInstallSuccess?.();
        
        // Hide prompt after success
        setTimeout(() => {
          setIsVisible(false);
        }, 3000);
      } else {
        setInstallStep('error');
      }
    } catch (error) {
      console.error('Installation failed:', error);
      setInstallStep('error');
    }
  };

  const handleDismiss = (permanent = false) => {
    setIsVisible(false);
    
    if (permanent) {
      localStorage.setItem('pwa-install-dismissed', Date.now().toString());
    }
    
    onPromptDismiss?.();
  };

  const features = [
    {
      icon: <Wifi className="w-5 h-5 text-blue-500" />,
      title: 'Works Offline',
      description: 'Continue using VoiceHealth AI even without internet connection'
    },
    {
      icon: <Zap className="w-5 h-5 text-yellow-500" />,
      title: 'Lightning Fast',
      description: 'Instant loading and smooth performance like a native app'
    },
    {
      icon: <Shield className="w-5 h-5 text-green-500" />,
      title: 'Secure & Private',
      description: 'Your health data stays protected with end-to-end encryption'
    },
    {
      icon: <Smartphone className="w-5 h-5 text-purple-500" />,
      title: 'Mobile Optimized',
      description: 'Perfect experience on phones, tablets, and desktop'
    }
  ];

  if (!pwa.canInstall || !isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="relative">
          <button
            onClick={() => handleDismiss(false)}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Download className="w-8 h-8 text-blue-600" />
            </div>
            
            <CardTitle className="text-xl mb-2">
              Install VoiceHealth AI
            </CardTitle>
            
            <p className="text-gray-600 text-sm">
              Get the full app experience with offline capabilities and faster performance
            </p>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Installation Status */}
          {installStep === 'installing' && (
            <div className="text-center py-4">
              <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Installing app...</p>
            </div>
          )}

          {installStep === 'success' && (
            <div className="text-center py-4">
              <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-2" />
              <p className="font-medium text-green-700">Successfully Installed!</p>
              <p className="text-sm text-gray-600">You can now access VoiceHealth AI from your home screen</p>
            </div>
          )}

          {installStep === 'error' && (
            <div className="text-center py-4">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <X className="w-6 h-6 text-red-500" />
              </div>
              <p className="font-medium text-red-700">Installation Failed</p>
              <p className="text-sm text-gray-600">Please try again or install manually</p>
            </div>
          )}

          {/* Features */}
          {installStep === 'prompt' && (
            <>
              {!showFeatures ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">App Benefits:</span>
                    <button
                      onClick={() => setShowFeatures(true)}
                      className="text-blue-600 hover:text-blue-700 flex items-center gap-1"
                    >
                      View all <ArrowRight className="w-3 h-3" />
                    </button>
                  </div>
                  
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <Wifi className="w-6 h-6 text-blue-500" />
                    <div>
                      <p className="font-medium text-sm">Works Offline</p>
                      <p className="text-xs text-gray-600">Continue consultations without internet</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Why Install VoiceHealth AI?</span>
                    <button
                      onClick={() => setShowFeatures(false)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3 p-2">
                      {feature.icon}
                      <div>
                        <p className="font-medium text-sm">{feature.title}</p>
                        <p className="text-xs text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}

          {/* Action Buttons */}
          {installStep === 'prompt' && (
            <div className="space-y-2 pt-4">
              <Button
                onClick={handleInstall}
                className="w-full flex items-center justify-center gap-2"
              >
                <Download className="w-4 h-4" />
                Install App
              </Button>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => handleDismiss(false)}
                  variant="outline"
                  className="flex-1 text-sm"
                >
                  Maybe Later
                </Button>
                
                <Button
                  onClick={() => handleDismiss(true)}
                  variant="ghost"
                  className="flex-1 text-sm text-gray-500"
                >
                  Don't Ask Again
                </Button>
              </div>
            </div>
          )}

          {installStep === 'error' && (
            <div className="space-y-2 pt-4">
              <Button
                onClick={handleInstall}
                className="w-full"
              >
                Try Again
              </Button>
              
              <Button
                onClick={() => handleDismiss(false)}
                variant="outline"
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          )}

          {/* App Rating */}
          {installStep === 'prompt' && (
            <div className="text-center pt-2 border-t">
              <div className="flex items-center justify-center gap-1 mb-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <p className="text-xs text-gray-500">
                Rated 4.9/5 by healthcare professionals
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Floating Install Button - Persistent but unobtrusive
 */
export const PWAInstallButton = ({ className = '' }) => {
  const pwa = usePWA();
  const [isInstalling, setIsInstalling] = useState(false);

  const handleInstall = async () => {
    setIsInstalling(true);
    
    try {
      await pwa.installApp();
    } catch (error) {
      console.error('Installation failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  if (!pwa.canInstall) {
    return null;
  }

  return (
    <Button
      onClick={handleInstall}
      disabled={isInstalling}
      className={`fixed bottom-4 right-4 rounded-full shadow-lg z-40 ${className}`}
      size="sm"
    >
      {isInstalling ? (
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      ) : (
        <Download className="w-4 h-4" />
      )}
      <span className="ml-2">Install App</span>
    </Button>
  );
};

/**
 * PWA Features Showcase - Educational component
 */
export const PWAFeaturesShowcase = ({ onInstall }) => {
  const pwa = usePWA();

  const offlineFeatures = [
    'Voice consultations continue offline',
    'Medical history always accessible',
    'Secure local data storage',
    'Automatic sync when back online'
  ];

  const performanceFeatures = [
    'Instant app loading',
    'Native app-like experience',
    'Smooth animations and transitions',
    'Optimized for mobile and desktop'
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="w-6 h-6" />
          Enhanced App Experience
        </CardTitle>
        <p className="text-gray-600">
          Install VoiceHealth AI for the complete healthcare experience
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Wifi className="w-5 h-5 text-blue-500" />
              Offline Capabilities
            </h3>
            <ul className="space-y-2">
              {offlineFeatures.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              Performance Benefits
            </h3>
            <ul className="space-y-2">
              {performanceFeatures.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {pwa.canInstall && (
          <div className="flex justify-center pt-4 border-t">
            <Button
              onClick={onInstall || pwa.installApp}
              className="flex items-center gap-2"
              size="lg"
            >
              <Download className="w-5 h-5" />
              Install VoiceHealth AI
            </Button>
          </div>
        )}

        {!pwa.canInstall && pwa.isInstallable && (
          <div className="text-center py-4 bg-green-50 rounded-lg">
            <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="font-medium text-green-700">App Already Installed!</p>
            <p className="text-sm text-green-600">
              Look for VoiceHealth AI on your home screen
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PWAInstallPrompt;
