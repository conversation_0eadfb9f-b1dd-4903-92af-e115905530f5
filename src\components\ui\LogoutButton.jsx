import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const LogoutButton = ({ 
  variant = 'outline', 
  size = 'md', 
  showIcon = true, 
  showText = true,
  className = '',
  onLogoutSuccess,
  onLogoutError
}) => {
  const { signOut, user } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const navigate = useNavigate();

  const handleLogout = async () => {
    if (isLoggingOut || !user) return;

    try {
      setIsLoggingOut(true);
      
      const result = await signOut();
      
      if (result?.success) {
        // Clear any cached data
        localStorage.removeItem('demo-session');
        sessionStorage.clear();
        
        // Call success callback if provided
        if (onLogoutSuccess) {
          onLogoutSuccess();
        }
        
        // Navigate to authentication page
        navigate('/authentication-demo-access', { replace: true });
        
        // Show success message
        console.log('Logged out successfully');
        
        // Force page reload to clear any remaining state
        setTimeout(() => {
          window.location.reload();
        }, 100);
      } else {
        const errorMsg = result?.error || 'Logout failed. Please try again.';
        console.error('Logout failed:', errorMsg);
        
        if (onLogoutError) {
          onLogoutError(errorMsg);
        } else {
          alert(errorMsg);
        }
      }
    } catch (error) {
      const errorMsg = 'An error occurred during logout. Please try again.';
      console.error('Logout error:', error);
      
      if (onLogoutError) {
        onLogoutError(errorMsg);
      } else {
        alert(errorMsg);
      }
    } finally {
      setIsLoggingOut(false);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleLogout}
      disabled={isLoggingOut}
      className={`${className} ${isLoggingOut ? 'cursor-not-allowed' : ''}`}
      aria-label="Sign out"
    >
      {showIcon && (
        <Icon 
          name={isLoggingOut ? "Loader2" : "LogOut"} 
          size={size === 'sm' ? 14 : size === 'lg' ? 20 : 16} 
          className={`${isLoggingOut ? 'animate-spin' : ''} ${showText ? 'mr-2' : ''}`} 
        />
      )}
      {showText && (
        <span>{isLoggingOut ? 'Signing out...' : 'Sign Out'}</span>
      )}
    </Button>
  );
};

export default LogoutButton;
