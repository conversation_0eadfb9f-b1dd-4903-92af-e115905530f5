import React from 'react';
import Icon from '../../../components/AppIcon';

const KPIOverviewCards = ({ kpis = {} }) => {
  const defaultKPIs = {
    totalUsers: { value: '12,543', change: '+8.2%', trend: 'up' },
    activeConsultations: { value: '234', change: '+12.5%', trend: 'up' },
    systemUptime: { value: '99.9%', change: '+0.1%', trend: 'up' },
    errorRate: { value: '0.02%', change: '-0.05%', trend: 'down' },
    avgResponseTime: { value: '145ms', change: '-15ms', trend: 'down' },
    dailyConsultations: { value: '1,847', change: '+23.1%', trend: 'up' },
    ...kpis
  };

  const cards = [
    {
      id: 'users',
      title: 'Total Users',
      value: defaultKPIs.totalUsers?.value,
      change: defaultKPIs.totalUsers?.change,
      trend: defaultKPIs.totalUsers?.trend,
      icon: 'Users',
      iconColor: 'text-primary-500',
      iconBg: 'bg-primary-100'
    },
    {
      id: 'consultations',
      title: 'Active Consultations',
      value: defaultKPIs.activeConsultations?.value,
      change: defaultKPIs.activeConsultations?.change,
      trend: defaultKPIs.activeConsultations?.trend,
      icon: 'Activity',
      iconColor: 'text-success-500',
      iconBg: 'bg-success-100'
    },
    {
      id: 'uptime',
      title: 'System Uptime',
      value: defaultKPIs.systemUptime?.value,
      change: defaultKPIs.systemUptime?.change,
      trend: defaultKPIs.systemUptime?.trend,
      icon: 'Shield',
      iconColor: 'text-success-500',
      iconBg: 'bg-success-100'
    },
    {
      id: 'errors',
      title: 'Error Rate',
      value: defaultKPIs.errorRate?.value,
      change: defaultKPIs.errorRate?.change,
      trend: defaultKPIs.errorRate?.trend,
      icon: 'AlertTriangle',
      iconColor: 'text-warning-500',
      iconBg: 'bg-warning-100'
    },
    {
      id: 'response',
      title: 'Avg Response Time',
      value: defaultKPIs.avgResponseTime?.value,
      change: defaultKPIs.avgResponseTime?.change,
      trend: defaultKPIs.avgResponseTime?.trend,
      icon: 'Zap',
      iconColor: 'text-primary-500',
      iconBg: 'bg-primary-100'
    },
    {
      id: 'daily',
      title: 'Daily Consultations',
      value: defaultKPIs.dailyConsultations?.value,
      change: defaultKPIs.dailyConsultations?.change,
      trend: defaultKPIs.dailyConsultations?.trend,
      icon: 'TrendingUp',
      iconColor: 'text-success-500',
      iconBg: 'bg-success-100'
    }
  ];

  const getTrendColor = (trend, change) => {
    if (!change) return 'text-text-secondary';
    
    const isPositive = change.startsWith('+');
    const isNegative = change.startsWith('-');
    
    // For error rates, down trend is good (green), up trend is bad (red)
    if (trend === 'down') {
      return 'text-success-600';
    } else if (trend === 'up') {
      return 'text-success-600';
    }
    
    return 'text-text-secondary';
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return 'TrendingUp';
      case 'down': return 'TrendingDown';
      default: return 'Minus';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cards.map((card) => (
        <div
          key={card.id}
          className="bg-surface rounded-lg border border-border shadow-minimal p-6 hover:shadow-small transition-all duration-200"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-text-secondary mb-2">
                {card.title}
              </p>
              <p className="text-2xl font-bold text-text-primary mb-2">
                {card.value}
              </p>
              {card.change && (
                <div className="flex items-center">
                  <Icon
                    name={getTrendIcon(card.trend)}
                    size={16}
                    className={`mr-1 ${getTrendColor(card.trend, card.change)}`}
                  />
                  <span className={`text-sm font-medium ${getTrendColor(card.trend, card.change)}`}>
                    {card.change}
                  </span>
                  <span className="text-text-secondary text-sm ml-1">
                    vs last month
                  </span>
                </div>
              )}
            </div>
            <div className={`p-3 rounded-lg ${card.iconBg}`}>
              <Icon
                name={card.icon}
                size={24}
                className={card.iconColor}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default KPIOverviewCards;