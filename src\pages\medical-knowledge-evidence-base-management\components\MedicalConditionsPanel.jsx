import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';


const MedicalConditionsPanel = ({ searchQuery }) => {
  const [conditions, setConditions] = useState([]);
  const [selectedCondition, setSelectedCondition] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filterCategory, setFilterCategory] = useState('all');

  const mockConditions = [
    {
      id: 'hypertension_001',
      name: 'Essential Hypertension',
      category: 'cardiovascular',
      icd10: 'I10',
      symptoms: ['headache', 'dizziness', 'chest pain', 'fatigue'],
      redFlags: ['stroke symptoms', 'heart failure signs', 'retinal changes'],
      diagnosticCriteria: [
        'Systolic BP ≥140 mmHg or Diastolic BP ≥90 mmHg',
        'Persistent elevation on multiple readings',
        'No identifiable secondary cause'
      ],
      treatmentProtocol: 'WHO/ISH 2020 Guidelines',
      prevalence: 'High',
      africanConsiderations: 'Higher salt sensitivity, genetic predisposition',
      confidence: 0.95,
      lastUpdated: '2024-01-10'
    },
    {
      id: 'diabetes_001',
      name: 'Type 2 Diabetes Mellitus',
      category: 'endocrine',
      icd10: 'E11',
      symptoms: ['polyuria', 'polydipsia', 'weight loss', 'fatigue', 'blurred vision'],
      redFlags: ['ketoacidosis symptoms', 'severe hypoglycemia', 'diabetic coma'],
      diagnosticCriteria: [
        'Fasting glucose ≥7.0 mmol/L (126 mg/dL)',
        'Random glucose ≥11.1 mmol/L (200 mg/dL)',
        'HbA1c ≥48 mmol/mol (6.5%)'
      ],
      treatmentProtocol: 'ADA/EASD 2022 Consensus',
      prevalence: 'Increasing',
      africanConsiderations: 'Rapid urbanization impact, genetic variants',
      confidence: 0.97,
      lastUpdated: '2024-01-12'
    },
    {
      id: 'pneumonia_001',
      name: 'Community-Acquired Pneumonia',
      category: 'respiratory',
      icd10: 'J44.0',
      symptoms: ['cough', 'fever', 'chest pain', 'dyspnea', 'sputum production'],
      redFlags: ['respiratory failure', 'septic shock', 'confusion in elderly'],
      diagnosticCriteria: [
        'Clinical symptoms with chest X-ray changes',
        'Elevated inflammatory markers',
        'Positive sputum culture if available'
      ],
      treatmentProtocol: 'WHO Pneumonia Guidelines 2023',
      prevalence: 'High',
      africanConsiderations: 'TB co-infection risk, HIV co-morbidity',
      confidence: 0.93,
      lastUpdated: '2024-01-08'
    }
  ];

  const categories = [
    { id: 'all', label: 'All Categories', count: mockConditions.length },
    { id: 'cardiovascular', label: 'Cardiovascular', count: 1 },
    { id: 'endocrine', label: 'Endocrine', count: 1 },
    { id: 'respiratory', label: 'Respiratory', count: 1 },
    { id: 'infectious', label: 'Infectious', count: 0 },
    { id: 'neurological', label: 'Neurological', count: 0 }
  ];

  useEffect(() => {
    const loadConditions = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));
      setConditions(mockConditions);
      setIsLoading(false);
    };

    loadConditions();
  }, []);

  const filteredConditions = conditions.filter(condition => {
    const matchesCategory = filterCategory === 'all' || condition.category === filterCategory;
    const matchesSearch = !searchQuery || 
      condition.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      condition.symptoms?.some(symptom => symptom.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const handleConditionSelect = (condition) => {
    setSelectedCondition(selectedCondition?.id === condition.id ? null : condition);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading medical conditions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setFilterCategory(category.id)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                filterCategory === category.id
                  ? 'bg-primary-500 text-white' :'bg-primary-50 text-primary-700 hover:bg-primary-100'
              }`}
            >
              {category.label}
              {category.count > 0 && (
                <span className="ml-2 text-xs opacity-75">({category.count})</span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Conditions List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredConditions.map((condition) => (
          <div
            key={condition.id}
            className={`bg-surface rounded-lg border transition-all cursor-pointer ${
              selectedCondition?.id === condition.id
                ? 'border-primary-500 shadow-medium'
                : 'border-border hover:border-primary-300 hover:shadow-small'
            }`}
            onClick={() => handleConditionSelect(condition)}
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-text-primary mb-1">
                    {condition.name}
                  </h3>
                  <div className="flex items-center space-x-3 text-sm text-text-secondary">
                    <span className="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs font-medium">
                      {condition.category}
                    </span>
                    <span>ICD-10: {condition.icd10}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    condition.confidence > 0.95 ? 'bg-success-500' :
                    condition.confidence > 0.9 ? 'bg-warning-500' : 'bg-error-500'
                  }`}></div>
                  <span className="text-xs text-text-secondary">
                    {Math.round(condition.confidence * 100)}%
                  </span>
                </div>
              </div>

              {/* Symptoms */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-text-primary mb-2">Common Symptoms</h4>
                <div className="flex flex-wrap gap-1">
                  {condition.symptoms?.slice(0, 4).map((symptom, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >
                      {symptom}
                    </span>
                  ))}
                  {condition.symptoms?.length > 4 && (
                    <span className="text-xs text-text-secondary">
                      +{condition.symptoms.length - 4} more
                    </span>
                  )}
                </div>
              </div>

              {/* Red Flags */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Icon name="AlertTriangle" size={16} className="text-error-500 mr-2" />
                  <h4 className="text-sm font-medium text-text-primary">Red Flags</h4>
                </div>
                <div className="flex flex-wrap gap-1">
                  {condition.redFlags?.slice(0, 2).map((flag, index) => (
                    <span
                      key={index}
                      className="bg-error-50 text-error-700 px-2 py-1 rounded text-xs"
                    >
                      {flag}
                    </span>
                  ))}
                  {condition.redFlags?.length > 2 && (
                    <span className="text-xs text-text-secondary">
                      +{condition.redFlags.length - 2} more
                    </span>
                  )}
                </div>
              </div>

              {/* Expandable Details */}
              {selectedCondition?.id === condition.id && (
                <div className="mt-6 pt-6 border-t border-border space-y-4">
                  {/* Diagnostic Criteria */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-2">Diagnostic Criteria</h4>
                    <ul className="space-y-1">
                      {condition.diagnosticCriteria?.map((criteria, index) => (
                        <li key={index} className="text-sm text-text-secondary flex items-start">
                          <Icon name="CheckCircle" size={16} className="text-success-500 mr-2 mt-0.5 flex-shrink-0" />
                          {criteria}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Treatment Protocol */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-2">Treatment Protocol</h4>
                    <p className="text-sm text-text-secondary">{condition.treatmentProtocol}</p>
                  </div>

                  {/* African Considerations */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-2">African Context</h4>
                    <p className="text-sm text-text-secondary">{condition.africanConsiderations}</p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" iconName="Edit">
                      Edit Condition
                    </Button>
                    <Button variant="outline" size="sm" iconName="BookOpen">
                      View Guidelines
                    </Button>
                    <Button variant="outline" size="sm" iconName="AlertTriangle">
                      Red Flag Config
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredConditions.length === 0 && (
        <div className="text-center py-12">
          <Icon name="Search" size={48} className="text-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">No conditions found</h3>
          <p className="text-text-secondary">
            Try adjusting your search or filter criteria
          </p>
        </div>
      )}

      {/* Add New Condition Button */}
      <div className="flex justify-center pt-6">
        <Button variant="primary" iconName="Plus">
          Add New Medical Condition
        </Button>
      </div>
    </div>
  );
};

export default MedicalConditionsPanel;