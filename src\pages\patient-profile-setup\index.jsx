import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/ui/Header';
import PersonalInfoSection from './components/PersonalInfoSection';
import MedicalHistorySection from './components/MedicalHistorySection';
import LifestyleFactorsSection from './components/LifestyleFactorsSection';
import ProfileProgressIndicator from './components/ProfileProgressIndicator';
import DocumentUploadSection from './components/DocumentUploadSection';
import ProfileCompletionPrompt from '../../components/ui/ProfileCompletionPrompt';
import Button from '../../components/ui/Button';
import Icon from '../../components/AppIcon';
import { useProfileCompletionWithUpdates } from '../../hooks/useProfileCompletion';

const PatientProfileSetup = () => {
  const navigate = useNavigate();

  // Profile completion tracking
  const {
    completionData,
    validationResult,
    isLoading: isCompletionLoading,
    error: completionError,
    refreshCompletion,
    validateForConsultation,
    updateAfterProfileChange,
    completionPercentage,
    criticalFieldsMissing,
    nextPriorityField
  } = useProfileCompletionWithUpdates();

  // Form data state
  const [profileData, setProfileData] = useState({
    personalInfo: {},
    medicalHistory: {},
    lifestyle: {},
    documents: []
  });

  // UI state
  const [expandedSections, setExpandedSections] = useState({
    personalInfo: true,
    medicalHistory: false,
    lifestyle: false
  });

  const [errors, setErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [isDraft, setIsDraft] = useState(false);
  const [showCompletionPrompt, setShowCompletionPrompt] = useState(true);

  // Legacy progress calculation (for backward compatibility)
  const [sectionProgress, setSectionProgress] = useState({
    personalInfo: 0,
    medicalHistory: 0,
    lifestyle: 0
  });

  const [overallProgress, setOverallProgress] = useState(0);

  // Mock user data for demonstration
  useEffect(() => {
    // Simulate loading existing profile data
    const mockData = {
      personalInfo: {
        fullName: "Sarah Johnson",
        email: "<EMAIL>",
        phoneNumber: "+****************",
        dateOfBirth: "1985-03-15",
        gender: "female",
        country: "US",
        city: "New York",
        occupation: "Software Engineer",
        insuranceStatus: "private",
        emergencyContactName: "John Johnson",
        emergencyContactPhone: "+****************",
        emergencyContactRelationship: "spouse"
      },
      medicalHistory: {
        medicalConditions: [
          {
            id: 1,
            name: "Hypertension",
            severity: "moderate",
            status: "managed",
            diagnosedDate: "2020-01-15"
          }
        ],
        currentMedications: [
          {
            id: 1,
            name: "Lisinopril",
            dosage: "10mg",
            frequency: "Once daily",
            prescribedBy: "Dr. Smith",
            startDate: "2020-01-20"
          }
        ],
        allergies: []
      },
      lifestyle: {
        exerciseFrequency: "3-4",
        sleepHours: "7",
        stressLevel: "moderate"
      },
      documents: []
    };

    setProfileData(mockData);
    calculateProgress(mockData);
  }, []);

  // Calculate section and overall progress
  const calculateProgress = (data) => {
    // Enhanced personal info fields including new demographic data
    const personalInfoFields = [
      'fullName', 'email', 'phoneNumber', 'dateOfBirth', 'gender',
      'country', 'city', 'occupation', 'insuranceStatus'
    ];
    const emergencyContactFields = ['emergencyContactName', 'emergencyContactPhone'];

    // Calculate personal info progress (core fields + demographic + emergency contact)
    const coreFieldsProgress = personalInfoFields.reduce((acc, field) => {
      return acc + (data.personalInfo[field] ? (80 / personalInfoFields.length) : 0);
    }, 0);

    const emergencyContactProgress = emergencyContactFields.reduce((acc, field) => {
      return acc + (data.personalInfo[field] ? 10 : 0);
    }, 0);

    const personalInfoProgress = coreFieldsProgress + emergencyContactProgress;

    const medicalHistoryProgress = 
      (data.medicalHistory.medicalConditions?.length > 0 ? 33 : 0) +
      (data.medicalHistory.currentMedications?.length > 0 ? 33 : 0) +
      (data.medicalHistory.allergies?.length >= 0 ? 34 : 0);

    const lifestyleFields = ['exerciseFrequency', 'sleepHours', 'stressLevel'];
    const lifestyleProgress = lifestyleFields.reduce((acc, field) => {
      return acc + (data.lifestyle[field] ? 33 : 0);
    }, 0);

    const newSectionProgress = {
      personalInfo: Math.min(personalInfoProgress, 100),
      medicalHistory: Math.min(medicalHistoryProgress, 100),
      lifestyle: Math.min(lifestyleProgress, 100)
    };

    const newOverallProgress = Math.round(
      (newSectionProgress.personalInfo + newSectionProgress.medicalHistory + newSectionProgress.lifestyle) / 3
    );

    setSectionProgress(newSectionProgress);
    setOverallProgress(newOverallProgress);
  };

  // Section handlers
  const handleSectionToggle = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleSectionUpdate = (section, data) => {
    const newProfileData = {
      ...profileData,
      [section]: data
    };
    setProfileData(newProfileData);
    calculateProgress(newProfileData);

    // Trigger enhanced profile completion update
    updateAfterProfileChange(section);

    // Clear errors for updated section
    if (errors[section]) {
      setErrors(prev => ({
        ...prev,
        [section]: {}
      }));
    }
  };

  // Validation
  const validateSection = (section, data) => {
    const sectionErrors = {};

    if (section === 'personalInfo') {
      if (!data.fullName?.trim()) sectionErrors.fullName = 'Full name is required';
      if (!data.email?.trim()) sectionErrors.email = 'Email is required';
      if (!data.phoneNumber?.trim()) sectionErrors.phoneNumber = 'Phone number is required';
      if (!data.dateOfBirth) sectionErrors.dateOfBirth = 'Date of birth is required';
      if (!data.gender) sectionErrors.gender = 'Gender is required';
      
      if (data.email && !/\S+@\S+\.\S+/.test(data.email)) {
        sectionErrors.email = 'Please enter a valid email address';
      }
    }

    return sectionErrors;
  };

  const validateAllSections = () => {
    const allErrors = {};
    
    Object.keys(profileData).forEach(section => {
      if (section !== 'documents') {
        const sectionErrors = validateSection(section, profileData[section]);
        if (Object.keys(sectionErrors).length > 0) {
          allErrors[section] = sectionErrors;
        }
      }
    });

    setErrors(allErrors);
    return Object.keys(allErrors).length === 0;
  };

  // Save handlers
  const handleSaveDraft = async () => {
    setIsSaving(true);
    setIsDraft(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Draft saved:', profileData);
      
      // Show success message
      alert('Profile draft saved successfully!');
    } catch (error) {
      console.error('Error saving draft:', error);
      alert('Error saving draft. Please try again.');
    } finally {
      setIsSaving(false);
      setIsDraft(false);
    }
  };

  const handleSaveAndContinue = async () => {
    if (!validateAllSections()) {
      // Expand sections with errors
      Object.keys(errors).forEach(section => {
        setExpandedSections(prev => ({
          ...prev,
          [section]: true
        }));
      });
      return;
    }

    setIsSaving(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Profile saved:', profileData);
      
      // Navigate to next step or dashboard
      navigate('/voice-consultation-interface');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Error saving profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCompleteProfile = () => {
    // Expand all sections for completion
    setExpandedSections({
      personalInfo: true,
      medicalHistory: true,
      lifestyle: true
    });
    setShowCompletionPrompt(false);
  };

  const getMissingFields = () => {
    const missing = [];
    
    if (sectionProgress.personalInfo < 100) {
      missing.push({ key: 'personal_info', label: 'Personal Information' });
    }
    if (sectionProgress.medicalHistory < 50) {
      missing.push({ key: 'medical_history', label: 'Medical History' });
    }
    if (sectionProgress.lifestyle < 50) {
      missing.push({ key: 'lifestyle', label: 'Lifestyle Information' });
    }
    
    return missing;
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center">
                <Icon name="User" size={24} color="white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-text-primary font-heading">
                  Patient Profile Setup
                </h1>
                <p className="text-text-secondary font-caption">
                  Create your comprehensive health profile for personalized AI consultations
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Personal Information Section */}
              <PersonalInfoSection
                data={profileData.personalInfo}
                onUpdate={(data) => handleSectionUpdate('personalInfo', data)}
                isExpanded={expandedSections.personalInfo}
                onToggle={() => handleSectionToggle('personalInfo')}
                errors={errors.personalInfo || {}}
              />

              {/* Medical History Section */}
              <MedicalHistorySection
                data={profileData.medicalHistory}
                onUpdate={(data) => handleSectionUpdate('medicalHistory', data)}
                isExpanded={expandedSections.medicalHistory}
                onToggle={() => handleSectionToggle('medicalHistory')}
                errors={errors.medicalHistory || {}}
              />

              {/* Lifestyle Factors Section */}
              <LifestyleFactorsSection
                data={profileData.lifestyle}
                onUpdate={(data) => handleSectionUpdate('lifestyle', data)}
                isExpanded={expandedSections.lifestyle}
                onToggle={() => handleSectionToggle('lifestyle')}
                errors={errors.lifestyle || {}}
              />

              {/* Document Upload Section */}
              <DocumentUploadSection
                documents={profileData.documents}
                onDocumentsUpdate={(documents) => handleSectionUpdate('documents', documents)}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Progress Indicator */}
              <ProfileProgressIndicator
                currentStep={1}
                totalSteps={3}
                completionPercentage={completionData?.completionPercentage || overallProgress}
                sectionProgress={completionData?.sectionProgress || sectionProgress}
                isLoading={isCompletionLoading}
                criticalFieldsMissing={criticalFieldsMissing}
                nextPriorityField={nextPriorityField}
              />

              {/* Quick Actions */}
              <div className="bg-surface border border-border rounded-xl shadow-minimal p-6">
                <h3 className="font-semibold text-text-primary font-heading mb-4">
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    fullWidth
                    iconName="Eye"
                    iconPosition="left"
                    onClick={() => navigate('/session-dashboard-history')}
                  >
                    View Previous Sessions
                  </Button>
                  <Button
                    variant="outline"
                    fullWidth
                    iconName="Settings"
                    iconPosition="left"
                    onClick={() => navigate('/agent-customization-hub')}
                  >
                    Customize AI Agents
                  </Button>
                  <Button
                    variant="outline"
                    fullWidth
                    iconName="HelpCircle"
                    iconPosition="left"
                  >
                    Get Help
                  </Button>
                </div>
              </div>

              {/* Privacy Notice */}
              <div className="bg-accent-50 border border-accent-200 rounded-xl p-6">
                <h4 className="font-medium text-accent-600 mb-3 flex items-center space-x-2">
                  <Icon name="Shield" size={16} />
                  <span>Privacy & Security</span>
                </h4>
                <ul className="space-y-2 text-sm text-accent-600">
                  <li className="flex items-start space-x-2">
                    <Icon name="Lock" size={12} className="mt-0.5 flex-shrink-0" />
                    <span>All data is encrypted and stored securely</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Icon name="UserCheck" size={12} className="mt-0.5 flex-shrink-0" />
                    <span>Only you and your chosen healthcare providers can access your information</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Icon name="Trash2" size={12} className="mt-0.5 flex-shrink-0" />
                    <span>You can delete your data at any time</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Sticky Bottom Actions */}
          <div className="fixed bottom-0 left-0 right-0 bg-surface border-t border-border shadow-elevated p-4 lg:relative lg:bg-transparent lg:border-0 lg:shadow-none lg:p-0 lg:mt-8">
            <div className="max-w-7xl mx-auto flex flex-col sm:flex-row gap-3 sm:justify-end">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                loading={isSaving && isDraft}
                disabled={isSaving}
                iconName="Save"
                iconPosition="left"
                className="sm:w-auto"
              >
                Save Draft
              </Button>
              <Button
                variant="primary"
                onClick={handleSaveAndContinue}
                loading={isSaving && !isDraft}
                disabled={isSaving}
                iconName="ArrowRight"
                iconPosition="right"
                className="sm:w-auto"
              >
                {overallProgress >= 100 ? 'Complete Setup' : 'Save & Continue'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Completion Prompt */}
      {showCompletionPrompt && (completionData?.completionPercentage || overallProgress) < 100 && (
        <ProfileCompletionPrompt
          profileCompletion={completionData?.completionPercentage || overallProgress}
          missingFields={completionData?.missingFields || getMissingFields()}
          onCompleteProfile={handleCompleteProfile}
          onDismiss={() => setShowCompletionPrompt(false)}
          isVisible={true}
          criticalFieldsMissing={criticalFieldsMissing}
          nextPriorityField={nextPriorityField}
        />
      )}
    </div>
  );
};

export default PatientProfileSetup;