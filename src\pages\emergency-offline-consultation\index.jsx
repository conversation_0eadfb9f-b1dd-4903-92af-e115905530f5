import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import OfflineModeIndicator from './components/OfflineModeIndicator';
import OfflineSymptomChecker from './components/OfflineSymptomChecker';
import EmergencyProtocols from './components/EmergencyProtocols';
import OfflineAIResponse from './components/OfflineAIResponse';
import VitalSignsLogger from './components/VitalSignsLogger';
import { useAuth } from '../../contexts/AuthContext';

const EmergencyOfflineConsultation = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [lastSyncTime, setLastSyncTime] = useState(new Date(Date.now() - 3600000)); // 1 hour ago
  const [activeTab, setActiveTab] = useState('symptoms');
  const [symptomAssessment, setSymptomAssessment] = useState(null);
  const [selectedProtocol, setSelectedProtocol] = useState(null);
  const [consultationRecords, setConsultationRecords] = useState([]);

  // Emergency override state
  const [emergencyOverride, setEmergencyOverride] = useState(false);
  const [emergencyReason, setEmergencyReason] = useState(null);
  const [emergencyToken, setEmergencyToken] = useState(null);

  const emergencyContacts = [
    { name: 'Emergency', number: '911' },
    { name: 'Poison Control', number: '1-800-222-1222' },
    { name: 'Local Hospital', number: '(555) 123-4567' }
  ];

  const tabs = [
    { id: 'symptoms', name: 'Symptom Checker', icon: 'Stethoscope' },
    { id: 'protocols', name: 'Emergency Protocols', icon: 'FileText' },
    { id: 'vitals', name: 'Vital Signs', icon: 'Activity' },
    { id: 'assessment', name: 'AI Assessment', icon: 'Bot' }
  ];

  useEffect(() => {
    // Monitor network connectivity
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Emergency override detection for offline consultation
  useEffect(() => {
    const detectEmergencyOverride = async () => {
      try {
        // Check URL parameters for emergency override
        const urlParams = new URLSearchParams(window.location.search);
        const emergencyParam = urlParams.get('emergency');
        const emergencyReasonParam = urlParams.get('reason');

        if (emergencyParam === 'true') {
          console.log('🚨 Emergency override detected in offline consultation');

          // Import emergency authentication service
          const { default: emergencyAuthService } = await import('../../services/emergencyAuthService');

          // Create emergency session
          const sessionId = `offline-emergency-${Date.now()}`;
          const reason = emergencyReasonParam || 'offline_emergency_consultation';

          const emergencySession = await emergencyAuthService.createEmergencySession(
            'emergency_user',
            sessionId,
            reason,
            'offline_consultation'
          );

          if (emergencySession) {
            setEmergencyOverride(true);
            setEmergencyReason(reason);
            setEmergencyToken(emergencySession.emergencyToken);

            console.log(`✅ Emergency override activated for offline consultation: ${reason}`);

            // Auto-switch to emergency protocols tab
            setActiveTab('protocols');

            // Cache emergency state for offline access
            localStorage.setItem('emergency_offline_session', JSON.stringify({
              isEmergencyActive: true,
              emergencyReason: reason,
              emergencyToken: emergencySession.emergencyToken,
              sessionId,
              timestamp: Date.now()
            }));
          }
        }

        // Check for cached emergency state (important for offline mode)
        const cachedEmergencyState = localStorage.getItem('emergency_offline_session');
        if (cachedEmergencyState && !emergencyOverride) {
          const emergencyData = JSON.parse(cachedEmergencyState);
          if (emergencyData.isEmergencyActive) {
            setEmergencyOverride(true);
            setEmergencyReason(emergencyData.emergencyReason);
            setEmergencyToken(emergencyData.emergencyToken);

            console.log('🚨 Restored offline emergency session from cache');
            setActiveTab('protocols');
          }
        }
      } catch (error) {
        console.error('Failed to detect emergency override in offline consultation:', error);
        // In offline mode, still allow emergency protocols to be accessed
        if (isOffline) {
          console.log('⚠️ Offline mode: Emergency protocols available without authentication');
        }
      }
    };

    detectEmergencyOverride();
  }, [isOffline]);

  useEffect(() => {
    // Load cached consultation records
    const cachedRecords = JSON.parse(localStorage.getItem('offlineConsultations') || '[]');
    setConsultationRecords(cachedRecords);
  }, []);

  useEffect(() => {
    // Auto-navigate to assessment when symptom assessment is completed
    if (symptomAssessment) {
      setActiveTab('assessment');
    }
  }, [symptomAssessment]);

  const handleSymptomAssessment = (assessment) => {
    setSymptomAssessment(assessment);
    
    // Check if emergency protocol should be suggested
    const hasHighRiskSymptoms = assessment.symptoms?.some(s => 
      ['chest_pain', 'difficulty_breathing'].includes(s)
    );
    
    if (hasHighRiskSymptoms || assessment.severity === 'severe') {
      // Show emergency alert
      const shouldCallEmergency = window.confirm(
        'Your symptoms indicate a potential emergency. Would you like to call emergency services now?'
      );
      
      if (shouldCallEmergency) {
        window.location.href = 'tel:911';
      }
    }
  };

  const handleProtocolSelect = (protocol) => {
    setSelectedProtocol(protocol);
    setActiveTab('assessment');
  };

  const handleConsultationRecord = (record) => {
    setConsultationRecords(prev => [...prev, record]);
  };

  const handleVitalSignsUpdate = (vitals) => {
    console.log('Vital signs recorded:', vitals);
    // Show success notification
    alert('Vital signs recorded successfully. They will be synced when connectivity is restored.');
  };

  const handleEmergencyCall = (number) => {
    window.location.href = `tel:${number}`;
  };

  const handlePrintProtocols = () => {
    // Create printable version of emergency protocols
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Emergency Medical Protocols</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .protocol { margin-bottom: 30px; page-break-inside: avoid; }
            .protocol h3 { color: #dc2626; }
            .steps { margin-left: 20px; }
            .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 10px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <h1>Emergency Medical Protocols - Offline Reference</h1>
          <p>Generated: ${new Date().toLocaleString()}</p>
          <hr />
          <!-- Protocol content would be inserted here -->
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <Icon name="UserX" size={48} className="text-text-secondary mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-text-primary mb-2">Authentication Required</h2>
            <p className="text-text-secondary mb-4">Please sign in to access emergency consultation features.</p>
            <Button onClick={() => navigate('/authentication-demo-access')}>
              Sign In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Emergency Override Indicator */}
      {emergencyOverride && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 bg-error-600 text-white px-6 py-3 rounded-lg shadow-lg border-2 border-error-700">
          <div className="flex items-center space-x-3">
            <Icon name="AlertTriangle" size={20} color="white" />
            <div>
              <div className="font-bold text-sm">EMERGENCY OFFLINE MODE ACTIVE</div>
              <div className="text-xs opacity-90">
                Reason: {emergencyReason?.replace(/_/g, ' ').toUpperCase()} |
                Authentication bypassed | Offline protocols enabled
              </div>
            </div>
          </div>
        </div>
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Offline Mode Indicator */}
        <OfflineModeIndicator
          isOffline={isOffline}
          lastSyncTime={lastSyncTime}
          emergencyContacts={emergencyContacts}
          emergencyMode={emergencyOverride}
        />

        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-text-primary font-heading mb-2">
                Emergency Offline Consultation
              </h1>
              <p className="text-text-secondary">
                Critical healthcare guidance available without internet connectivity
              </p>
            </div>
            
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handlePrintProtocols}
                iconName="Printer"
                iconPosition="left"
                size="sm"
              >
                Print Protocols
              </Button>
              <Button
                variant="danger"
                onClick={() => handleEmergencyCall('911')}
                iconName="Phone"
                iconPosition="left"
                size="sm"
              >
                Call 911
              </Button>
            </div>
          </div>
        </div>

        {/* Emergency Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          {emergencyContacts.map((contact, index) => (
            <button
              key={index}
              onClick={() => handleEmergencyCall(contact.number)}
              className="bg-red-50 hover:bg-red-100 border border-red-200 rounded-lg p-4 text-left transition-all duration-200"
            >
              <div className="flex items-center">
                <div className="bg-red-600 text-white rounded-full p-2 mr-3">
                  <Icon name="Phone" size={20} />
                </div>
                <div>
                  <h3 className="font-semibold text-red-800">{contact.name}</h3>
                  <p className="text-red-700 text-lg font-mono">{contact.number}</p>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Tab Navigation */}
        <div className="bg-surface rounded-xl shadow-minimal border border-border mb-8">
          <div className="border-b border-border">
            <nav className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 py-4 px-6 text-center transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-b-2 border-primary text-primary bg-primary-50' :'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                  }`}
                >
                  <div className="flex items-center justify-center mb-1">
                    <Icon name={tab.icon} size={18} className="mr-2" />
                    <span className="font-medium">{tab.name}</span>
                  </div>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Tab Content */}
            {activeTab === 'symptoms' && (
              <OfflineSymptomChecker onSymptomAssessment={handleSymptomAssessment} />
            )}

            {activeTab === 'protocols' && (
              <EmergencyProtocols
                selectedProtocol={selectedProtocol?.id}
                onProtocolSelect={handleProtocolSelect}
              />
            )}

            {activeTab === 'vitals' && (
              <VitalSignsLogger onVitalSignsUpdate={handleVitalSignsUpdate} />
            )}

            {activeTab === 'assessment' && (
              <OfflineAIResponse
                symptomAssessment={symptomAssessment}
                selectedProtocol={selectedProtocol}
                onRecordConsultation={handleConsultationRecord}
              />
            )}
          </div>
        </div>

        {/* Consultation History */}
        {consultationRecords.length > 0 && (
          <div className="bg-surface rounded-xl shadow-minimal border border-border p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-text-primary flex items-center">
                <Icon name="History" size={24} className="mr-3" />
                Offline Consultation History
              </h3>
              <span className="text-sm text-text-secondary">
                {consultationRecords.length} record{consultationRecords.length !== 1 ? 's' : ''} pending sync
              </span>
            </div>
            
            <div className="space-y-3">
              {consultationRecords.slice(-3).map((record, index) => (
                <div key={record.id} className="bg-surface-secondary border border-border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-sm font-medium text-text-primary">
                        {record.type === 'emergency_offline' ? 'Emergency Assessment' : 'Consultation'}
                      </span>
                      <p className="text-xs text-text-secondary mt-1">
                        {record.timestamp?.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex items-center">
                      <Icon name="Wifi" size={16} className="text-red-500 mr-2" />
                      <span className="text-xs text-red-600">Offline</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Help & Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <Icon name="Info" size={24} className="text-blue-600 mr-4 mt-0.5" />
            <div>
              <h4 className="text-lg font-semibold text-blue-800 mb-2">
                How to Use Offline Emergency Consultation
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Use the Symptom Checker to assess your condition using cached medical protocols</li>
                <li>• Access Emergency Protocols for step-by-step guidance on critical situations</li>
                <li>• Log vital signs for professional review when connectivity is restored</li>
                <li>• All data is stored locally and will sync automatically when online</li>
                <li>• For life-threatening emergencies, always call 911 immediately</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default EmergencyOfflineConsultation;