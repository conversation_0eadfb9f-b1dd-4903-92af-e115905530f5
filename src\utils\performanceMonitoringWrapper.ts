/**
 * PERFORMANCE MONITORING WRAPPER UTILITY
 * 
 * Provides decorators and utilities to automatically add performance monitoring
 * to service methods across all phases of VoiceHealth AI implementation.
 * 
 * Features:
 * - Method execution time tracking
 * - Success/failure rate monitoring
 * - Automatic metric recording
 * - Error handling and logging
 * - Emergency protocol timing validation
 * - Cultural adaptation performance tracking
 */

import { performanceValidationService } from '../services/PerformanceValidationService';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface PerformanceMetricOptions {
  operation: string;
  category?: 'api' | 'database' | 'authentication' | 'emergency' | 'cultural' | 'clinical';
  target?: number; // Target response time in milliseconds
  emergencyOperation?: boolean; // If true, applies emergency timing validation
  culturalOperation?: boolean; // If true, tracks cultural adaptation metrics
  includeMetadata?: boolean; // If true, includes additional metadata
}

export interface MethodMetadata {
  serviceName: string;
  methodName: string;
  parameters?: any[];
  result?: any;
  error?: Error;
  startTime: number;
  endTime: number;
  responseTime: number;
  success: boolean;
}

// =====================================================
// PERFORMANCE MONITORING DECORATOR
// =====================================================

/**
 * Decorator to add performance monitoring to service methods
 */
export function MonitorPerformance(options: PerformanceMetricOptions) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const serviceName = target.constructor.name;
      const operationName = options.operation || `${serviceName}.${propertyName}`;

      let result: any;
      let error: Error | undefined;
      let success = false;

      try {
        result = await method.apply(this, args);
        success = true;
        return result;
      } catch (err) {
        error = err as Error;
        success = false;
        throw err;
      } finally {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // Record performance metric
        await recordPerformanceMetric({
          serviceName,
          methodName: propertyName,
          parameters: options.includeMetadata ? args : undefined,
          result: options.includeMetadata ? result : undefined,
          error,
          startTime,
          endTime,
          responseTime,
          success
        }, options);

        // Validate emergency timing if applicable
        if (options.emergencyOperation && responseTime > 2000) {
          console.warn(`🚨 Emergency operation ${operationName} exceeded 2 second limit: ${responseTime}ms`);
        }

        // Log performance for debugging
        if (process.env.NODE_ENV === 'development') {
          console.log(`📊 ${operationName}: ${responseTime}ms (${success ? 'success' : 'failed'})`);
        }
      }
    };

    return descriptor;
  };
}

/**
 * Function wrapper for adding performance monitoring to existing methods
 */
export function wrapWithPerformanceMonitoring<T extends (...args: any[]) => any>(
  originalMethod: T,
  options: PerformanceMetricOptions,
  serviceName: string,
  methodName: string
): T {
  return (async function (...args: any[]) {
    const startTime = Date.now();
    const operationName = options.operation || `${serviceName}.${methodName}`;

    let result: any;
    let error: Error | undefined;
    let success = false;

    try {
      result = await originalMethod.apply(this, args);
      success = true;
      return result;
    } catch (err) {
      error = err as Error;
      success = false;
      throw err;
    } finally {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Record performance metric
      await recordPerformanceMetric({
        serviceName,
        methodName,
        parameters: options.includeMetadata ? args : undefined,
        result: options.includeMetadata ? result : undefined,
        error,
        startTime,
        endTime,
        responseTime,
        success
      }, options);

      // Validate emergency timing if applicable
      if (options.emergencyOperation && responseTime > 2000) {
        console.warn(`🚨 Emergency operation ${operationName} exceeded 2 second limit: ${responseTime}ms`);
      }
    }
  }) as T;
}

// =====================================================
// METRIC RECORDING FUNCTIONS
// =====================================================

/**
 * Record performance metric with comprehensive metadata
 */
async function recordPerformanceMetric(
  metadata: MethodMetadata,
  options: PerformanceMetricOptions
): Promise<void> {
  try {
    const metricData = {
      operation: options.operation || `${metadata.serviceName}.${metadata.methodName}`,
      responseTime: metadata.responseTime,
      success: metadata.success,
      timestamp: new Date(metadata.startTime),
      category: options.category || 'api',
      target: options.target,
      metadata: {
        serviceName: metadata.serviceName,
        methodName: metadata.methodName,
        startTime: metadata.startTime,
        endTime: metadata.endTime,
        ...(metadata.error && { 
          error: {
            name: metadata.error.name,
            message: metadata.error.message,
            stack: process.env.NODE_ENV === 'development' ? metadata.error.stack : undefined
          }
        }),
        ...(options.emergencyOperation && { emergencyOperation: true }),
        ...(options.culturalOperation && { culturalOperation: true }),
        ...(options.includeMetadata && metadata.parameters && { 
          parameterCount: metadata.parameters.length 
        }),
        ...(options.includeMetadata && metadata.result && { 
          resultSize: JSON.stringify(metadata.result).length 
        })
      }
    };

    // Record the metric
    performanceValidationService.recordMetric(metricData);

    // Additional logging for emergency operations
    if (options.emergencyOperation) {
      performanceValidationService.recordMetric({
        ...metricData,
        operation: 'emergency_protocol_timing',
        category: 'emergency',
        target: 2000
      });
    }

    // Additional logging for cultural operations
    if (options.culturalOperation && metadata.result) {
      const culturalScore = extractCulturalScore(metadata.result);
      if (culturalScore !== null) {
        performanceValidationService.recordMetric({
          ...metricData,
          operation: 'cultural_adaptation_effectiveness',
          category: 'cultural',
          target: 90, // Target cultural score
          metadata: {
            ...metricData.metadata,
            culturalScore
          }
        });
      }
    }

  } catch (error) {
    console.error('❌ Error recording performance metric:', error);
  }
}

/**
 * Extract cultural score from service result
 */
function extractCulturalScore(result: any): number | null {
  try {
    if (result && typeof result === 'object') {
      // Check for cultural validation result
      if (result.overallScore && typeof result.overallScore === 'number') {
        return result.overallScore;
      }
      
      // Check for nested cultural validation
      if (result.validation && result.validation.overallScore) {
        return result.validation.overallScore;
      }
      
      // Check for cultural adaptations in clinical documentation
      if (result.documentation && result.documentation.qualityMetrics) {
        return result.documentation.qualityMetrics.culturalSensitivity || null;
      }
      
      // Check for cultural considerations in risk assessment
      if (result.riskAssessment && result.riskAssessment.assessmentMetadata) {
        return result.riskAssessment.assessmentMetadata.culturalAdaptationScore || null;
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

// =====================================================
// BATCH PERFORMANCE MONITORING
// =====================================================

/**
 * Apply performance monitoring to multiple methods of a service
 */
export function applyPerformanceMonitoringToService(
  service: any,
  serviceName: string,
  methodConfigs: { [methodName: string]: PerformanceMetricOptions }
): void {
  Object.entries(methodConfigs).forEach(([methodName, options]) => {
    if (typeof service[methodName] === 'function') {
      const originalMethod = service[methodName].bind(service);
      service[methodName] = wrapWithPerformanceMonitoring(
        originalMethod,
        options,
        serviceName,
        methodName
      );
    }
  });
}

// =====================================================
// PREDEFINED MONITORING CONFIGURATIONS
// =====================================================

/**
 * Standard monitoring configurations for different service types
 */
export const MonitoringConfigurations = {
  // Clinical Documentation Service
  clinicalDocumentation: {
    generateVoiceToNote: {
      operation: 'clinical_documentation_generation',
      category: 'clinical' as const,
      target: 5000,
      culturalOperation: true,
      includeMetadata: true
    },
    assessNoteQuality: {
      operation: 'clinical_note_quality_assessment',
      category: 'clinical' as const,
      target: 2000,
      includeMetadata: true
    }
  },

  // Advanced Risk Stratification Service
  riskStratification: {
    performRiskAssessment: {
      operation: 'risk_stratification',
      category: 'clinical' as const,
      target: 3000,
      culturalOperation: true,
      includeMetadata: true
    }
  },

  // Cultural Validation Service
  culturalValidation: {
    validateCulturalContent: {
      operation: 'cultural_validation',
      category: 'cultural' as const,
      target: 1000,
      culturalOperation: true,
      includeMetadata: true
    }
  },

  // Authentication Service
  authentication: {
    authenticate: {
      operation: 'authentication',
      category: 'authentication' as const,
      target: 500,
      includeMetadata: false // Don't log sensitive auth data
    },
    authenticateEmergency: {
      operation: 'emergency_authentication',
      category: 'emergency' as const,
      target: 50,
      emergencyOperation: true,
      includeMetadata: false
    }
  },

  // Encryption Service
  encryption: {
    encryptPHI: {
      operation: 'phi_encryption',
      category: 'api' as const,
      target: 1000,
      includeMetadata: true
    },
    decryptPHI: {
      operation: 'phi_decryption',
      category: 'api' as const,
      target: 1000,
      includeMetadata: true
    }
  },

  // AI Orchestrator
  aiOrchestrator: {
    generateClinicalDocumentation: {
      operation: 'orchestrator_clinical_documentation',
      category: 'api' as const,
      target: 8000,
      culturalOperation: true,
      includeMetadata: true
    },
    performRiskStratification: {
      operation: 'orchestrator_risk_stratification',
      category: 'api' as const,
      target: 5000,
      culturalOperation: true,
      includeMetadata: true
    },
    validateCulturalContent: {
      operation: 'orchestrator_cultural_validation',
      category: 'cultural' as const,
      target: 2000,
      culturalOperation: true,
      includeMetadata: true
    }
  }
};

// =====================================================
// EMERGENCY PROTOCOL MONITORING
// =====================================================

/**
 * Special monitoring for emergency protocols with strict timing requirements
 */
export function monitorEmergencyProtocol<T extends (...args: any[]) => any>(
  originalMethod: T,
  protocolName: string
): T {
  return wrapWithPerformanceMonitoring(
    originalMethod,
    {
      operation: `emergency_protocol_${protocolName}`,
      category: 'emergency',
      target: 2000,
      emergencyOperation: true,
      includeMetadata: true
    },
    'EmergencyProtocol',
    protocolName
  );
}

// =====================================================
// CULTURAL ADAPTATION MONITORING
// =====================================================

/**
 * Special monitoring for cultural adaptation operations
 */
export function monitorCulturalAdaptation<T extends (...args: any[]) => any>(
  originalMethod: T,
  adaptationName: string
): T {
  return wrapWithPerformanceMonitoring(
    originalMethod,
    {
      operation: `cultural_adaptation_${adaptationName}`,
      category: 'cultural',
      target: 1500,
      culturalOperation: true,
      includeMetadata: true
    },
    'CulturalAdaptation',
    adaptationName
  );
}

export default {
  MonitorPerformance,
  wrapWithPerformanceMonitoring,
  applyPerformanceMonitoringToService,
  MonitoringConfigurations,
  monitorEmergencyProtocol,
  monitorCulturalAdaptation
};
