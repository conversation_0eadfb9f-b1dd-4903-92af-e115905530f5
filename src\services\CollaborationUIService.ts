/**
 * COLLABORATION UI SERVICE
 * 
 * Connects the existing collaboration UI components to the real backend APIs.
 * Replaces simulated collaboration data with actual multi-agent system data.
 * 
 * FEATURES:
 * - Real-time agent status and presence updates
 * - Live collaboration session management
 * - Actual inter-agent dialogue streaming
 * - Real collaboration progress tracking
 * - Backend-driven session control
 * - HIPAA-compliant UI data handling
 */

import { EventEmitter } from 'events';
import { agentOrchestrator } from './AgentOrchestrator';
import { agentRegistry } from './AgentRegistry';
import { agentCommunicationProtocol, type AgentMessage, type CollaborationSession } from './AgentCommunicationProtocol';
import { multiAgentCollaborationEngine, type MultiAgentCase } from './MultiAgentCollaborationEngine';
import { realTimeAgentCommunication, type AgentPresence, type RealTimeMessage } from './RealTimeAgentCommunication';

export interface UIAgentData {
  id: string;
  name: string;
  role: string;
  specialization: string;
  status: 'online' | 'busy' | 'away' | 'offline' | 'speaking' | 'listening';
  confidence: number;
  avatar: string;
  isActive: boolean;
  currentSessions: string[];
  lastActivity: string;
}

export interface UICollaborationData {
  id: string;
  sessionId: string;
  phase: 'initializing' | 'assessment' | 'collaboration' | 'synthesis' | 'completed';
  progress: number;
  participatingAgents: UIAgentData[];
  leadAgent: UIAgentData | null;
  startedAt: string;
  estimatedTimeRemaining: number;
  currentTopic: string;
  keyFindings: string[];
}

export interface UIDialogueMessage {
  id: string;
  fromAgent: UIAgentData;
  toAgent?: UIAgentData;
  content: string;
  timestamp: string;
  type: 'consultation' | 'expertise_share' | 'diagnosis' | 'treatment' | 'system';
  confidence?: number;
  isHighlighted?: boolean;
}

export interface UISessionControl {
  sessionId: string;
  status: 'active' | 'paused' | 'completed' | 'error';
  canPause: boolean;
  canResume: boolean;
  canInjectQuestion: boolean;
  canRedirectTopic: boolean;
  canEndSession: boolean;
  userInterventionEnabled: boolean;
}

export class CollaborationUIService extends EventEmitter {
  private activeCollaborations: Map<string, UICollaborationData> = new Map();
  private agentDataCache: Map<string, UIAgentData> = new Map();
  private dialogueHistory: Map<string, UIDialogueMessage[]> = new Map();
  private sessionControls: Map<string, UISessionControl> = new Map();

  constructor() {
    super();
    console.log('🎨 Initializing Collaboration UI Service...');
    this.setupEventHandlers();
    this.startDataSync();
  }

  /**
   * Get current agent data for UI display
   */
  getUIAgentData(): UIAgentData[] {
    try {
      const allAgents = agentRegistry.getAllAgents();
      const agentData: UIAgentData[] = [];

      allAgents.forEach(agent => {
        const presence = realTimeAgentCommunication.getAgentPresence(agent.id);
        const metrics = agent.getPerformanceMetrics();

        const uiAgent: UIAgentData = {
          id: agent.id,
          name: agent.name,
          role: agent.role,
          specialization: this.getSpecializationDisplay(agent.role),
          status: presence?.status || 'offline',
          confidence: metrics.averageConfidence || 0.8,
          avatar: this.getAgentAvatar(agent.role),
          isActive: agent.isActive,
          currentSessions: presence?.currentSessions || [],
          lastActivity: presence?.lastSeen || new Date().toISOString()
        };

        agentData.push(uiAgent);
        this.agentDataCache.set(agent.id, uiAgent);
      });

      return agentData;

    } catch (error) {
      console.error('❌ Failed to get UI agent data:', error);
      return [];
    }
  }

  /**
   * Get collaboration data for UI display
   */
  getUICollaborationData(sessionId: string): UICollaborationData | null {
    try {
      // Check if we have cached collaboration data
      const cached = this.activeCollaborations.get(sessionId);
      if (cached) {
        return cached;
      }

      // Get active cases from collaboration engine
      const activeCases = multiAgentCollaborationEngine.getActiveCases();
      const sessionCase = activeCases.find(case_ => case_.sessionId === sessionId);

      if (!sessionCase) {
        return null;
      }

      // Convert to UI format
      const uiCollaboration = this.convertCaseToUIData(sessionCase);
      this.activeCollaborations.set(sessionId, uiCollaboration);

      return uiCollaboration;

    } catch (error) {
      console.error('❌ Failed to get UI collaboration data:', error);
      return null;
    }
  }

  /**
   * Get dialogue messages for UI display
   */
  getUIDialogueMessages(sessionId: string): UIDialogueMessage[] {
    try {
      // Check cache first
      const cached = this.dialogueHistory.get(sessionId);
      if (cached) {
        return cached;
      }

      // Get agent messages from communication protocol
      const agentMessages = agentCommunicationProtocol.getSessionMessageHistory(sessionId);
      const uiMessages = agentMessages.map(msg => this.convertMessageToUIFormat(msg));

      this.dialogueHistory.set(sessionId, uiMessages);
      return uiMessages;

    } catch (error) {
      console.error('❌ Failed to get UI dialogue messages:', error);
      return [];
    }
  }

  /**
   * Get session control data for UI
   */
  getUISessionControl(sessionId: string): UISessionControl {
    try {
      // Check cache first
      const cached = this.sessionControls.get(sessionId);
      if (cached) {
        return cached;
      }

      // Get current agent for session
      const currentAgent = agentOrchestrator.getCurrentAgent(sessionId);
      const collaborationData = this.getUICollaborationData(sessionId);

      const sessionControl: UISessionControl = {
        sessionId,
        status: collaborationData?.phase === 'completed' ? 'completed' : 'active',
        canPause: true,
        canResume: true,
        canInjectQuestion: true,
        canRedirectTopic: true,
        canEndSession: true,
        userInterventionEnabled: true
      };

      this.sessionControls.set(sessionId, sessionControl);
      return sessionControl;

    } catch (error) {
      console.error('❌ Failed to get UI session control:', error);
      return {
        sessionId,
        status: 'error',
        canPause: false,
        canResume: false,
        canInjectQuestion: false,
        canRedirectTopic: false,
        canEndSession: true,
        userInterventionEnabled: false
      };
    }
  }

  /**
   * Start a new collaboration session
   */
  async startCollaboration(
    sessionId: string,
    patientContext: any,
    requestingAgentId: string
  ): Promise<string | null> {
    try {
      console.log(`🚀 Starting UI collaboration for session: ${sessionId}`);

      // Initiate collaboration through backend
      const collaborationId = await multiAgentCollaborationEngine.initiateCollaboration(
        sessionId,
        patientContext,
        requestingAgentId
      );

      if (collaborationId) {
        // Register for real-time updates
        realTimeAgentCommunication.registerAgent(requestingAgentId, 'UI Client');
        
        // Emit UI event
        this.emit('collaboration_started', {
          collaborationId,
          sessionId,
          requestingAgentId
        });

        console.log(`✅ UI collaboration started: ${collaborationId}`);
      }

      return collaborationId;

    } catch (error) {
      console.error('❌ Failed to start UI collaboration:', error);
      return null;
    }
  }

  /**
   * Send user question to collaboration
   */
  async injectUserQuestion(sessionId: string, question: string): Promise<boolean> {
    try {
      console.log(`💬 Injecting user question into session: ${sessionId}`);

      // Send through agent orchestrator
      const response = await agentOrchestrator.processRequest({
        sessionId,
        userMessage: question,
        urgencyLevel: 'medium'
      });

      if (response) {
        // Update UI with new message
        this.addUIDialogueMessage(sessionId, {
          id: `user_${Date.now()}`,
          fromAgent: {
            id: 'user',
            name: 'Patient',
            role: 'user',
            specialization: 'Patient',
            status: 'online',
            confidence: 1.0,
            avatar: 'user',
            isActive: true,
            currentSessions: [sessionId],
            lastActivity: new Date().toISOString()
          },
          content: question,
          timestamp: new Date().toISOString(),
          type: 'consultation'
        });

        return true;
      }

      return false;

    } catch (error) {
      console.error('❌ Failed to inject user question:', error);
      return false;
    }
  }

  /**
   * Pause collaboration session
   */
  async pauseCollaboration(sessionId: string): Promise<boolean> {
    try {
      console.log(`⏸️ Pausing collaboration for session: ${sessionId}`);

      // Update session control
      const sessionControl = this.getUISessionControl(sessionId);
      sessionControl.status = 'paused';
      this.sessionControls.set(sessionId, sessionControl);

      // Emit UI event
      this.emit('collaboration_paused', { sessionId });

      return true;

    } catch (error) {
      console.error('❌ Failed to pause collaboration:', error);
      return false;
    }
  }

  /**
   * Resume collaboration session
   */
  async resumeCollaboration(sessionId: string): Promise<boolean> {
    try {
      console.log(`▶️ Resuming collaboration for session: ${sessionId}`);

      // Update session control
      const sessionControl = this.getUISessionControl(sessionId);
      sessionControl.status = 'active';
      this.sessionControls.set(sessionId, sessionControl);

      // Emit UI event
      this.emit('collaboration_resumed', { sessionId });

      return true;

    } catch (error) {
      console.error('❌ Failed to resume collaboration:', error);
      return false;
    }
  }

  /**
   * Convert backend case to UI format
   */
  private convertCaseToUIData(case_: MultiAgentCase): UICollaborationData {
    const participatingAgents = case_.assignedAgents
      .map(agentId => this.agentDataCache.get(agentId))
      .filter((agent): agent is UIAgentData => agent !== undefined);

    const leadAgent = this.agentDataCache.get(case_.leadAgent) || null;

    return {
      id: case_.id,
      sessionId: case_.sessionId,
      phase: this.mapCaseStatusToPhase(case_.status),
      progress: this.calculateProgress(case_),
      participatingAgents,
      leadAgent,
      startedAt: case_.startedAt,
      estimatedTimeRemaining: this.estimateTimeRemaining(case_),
      currentTopic: case_.patientContext.chiefComplaint,
      keyFindings: this.extractKeyFindings(case_)
    };
  }

  /**
   * Convert agent message to UI format
   */
  private convertMessageToUIFormat(message: AgentMessage): UIDialogueMessage {
    const fromAgent = this.agentDataCache.get(message.fromAgentId);
    const toAgent = message.toAgentId ? this.agentDataCache.get(message.toAgentId) : undefined;

    return {
      id: message.id,
      fromAgent: fromAgent || this.createFallbackAgentData(message.fromAgentId),
      toAgent,
      content: message.content,
      timestamp: message.timestamp,
      type: this.mapMessageTypeToUIType(message.messageType),
      confidence: message.metadata?.confidenceScore
    };
  }

  /**
   * Add dialogue message to UI cache
   */
  private addUIDialogueMessage(sessionId: string, message: UIDialogueMessage): void {
    const messages = this.dialogueHistory.get(sessionId) || [];
    messages.push(message);
    this.dialogueHistory.set(sessionId, messages);

    // Emit UI event
    this.emit('dialogue_message_added', { sessionId, message });
  }

  /**
   * Setup event handlers for real-time updates
   */
  private setupEventHandlers(): void {
    // Listen for agent communication events
    agentCommunicationProtocol.on('message', (message: AgentMessage) => {
      const uiMessage = this.convertMessageToUIFormat(message);
      this.addUIDialogueMessage(message.sessionId, uiMessage);
    });

    // Listen for collaboration engine events
    multiAgentCollaborationEngine.on('collaboration_initiated', (case_: MultiAgentCase) => {
      const uiCollaboration = this.convertCaseToUIData(case_);
      this.activeCollaborations.set(case_.sessionId, uiCollaboration);
      this.emit('collaboration_data_updated', { sessionId: case_.sessionId, data: uiCollaboration });
    });

    multiAgentCollaborationEngine.on('consensus_reached', (case_: MultiAgentCase) => {
      const uiCollaboration = this.convertCaseToUIData(case_);
      this.activeCollaborations.set(case_.sessionId, uiCollaboration);
      this.emit('collaboration_completed', { sessionId: case_.sessionId, data: uiCollaboration });
    });

    // Listen for real-time communication events
    realTimeAgentCommunication.on('agent_status_change', (data: any) => {
      this.refreshAgentData();
      this.emit('agent_status_updated', data);
    });
  }

  /**
   * Start periodic data synchronization
   */
  private startDataSync(): void {
    // Refresh agent data every 10 seconds
    setInterval(() => {
      this.refreshAgentData();
    }, 10000);

    // Refresh collaboration data every 5 seconds
    setInterval(() => {
      this.refreshCollaborationData();
    }, 5000);
  }

  /**
   * Refresh agent data cache
   */
  private refreshAgentData(): void {
    try {
      const agentData = this.getUIAgentData();
      this.emit('agent_data_updated', agentData);
    } catch (error) {
      console.error('❌ Failed to refresh agent data:', error);
    }
  }

  /**
   * Refresh collaboration data cache
   */
  private refreshCollaborationData(): void {
    try {
      for (const [sessionId] of this.activeCollaborations.entries()) {
        const data = this.getUICollaborationData(sessionId);
        if (data) {
          this.emit('collaboration_data_updated', { sessionId, data });
        }
      }
    } catch (error) {
      console.error('❌ Failed to refresh collaboration data:', error);
    }
  }

  /**
   * Helper methods for data conversion
   */
  private getSpecializationDisplay(role: string): string {
    const specializations: Record<string, string> = {
      'general_practitioner': 'General Medicine',
      'cardiologist': 'Cardiology',
      'nutritionist': 'Nutrition & Dietetics',
      'psychiatrist': 'Mental Health',
      'emergency': 'Emergency Medicine',
      'triage': 'Triage & Assessment'
    };
    return specializations[role] || role.replace('_', ' ');
  }

  private getAgentAvatar(role: string): string {
    const avatars: Record<string, string> = {
      'general_practitioner': 'stethoscope',
      'cardiologist': 'heart',
      'nutritionist': 'apple',
      'psychiatrist': 'brain',
      'emergency': 'zap',
      'triage': 'clipboard'
    };
    return avatars[role] || 'user-md';
  }

  private mapCaseStatusToPhase(status: string): UICollaborationData['phase'] {
    const mapping: Record<string, UICollaborationData['phase']> = {
      'initiated': 'initializing',
      'in_progress': 'collaboration',
      'consensus_building': 'synthesis',
      'completed': 'completed',
      'escalated': 'completed'
    };
    return mapping[status] || 'assessment';
  }

  private mapMessageTypeToUIType(messageType: string): UIDialogueMessage['type'] {
    const mapping: Record<string, UIDialogueMessage['type']> = {
      'consultation_request': 'consultation',
      'consultation_response': 'consultation',
      'expertise_request': 'expertise_share',
      'expertise_share': 'expertise_share',
      'diagnosis_collaboration': 'diagnosis',
      'treatment_suggestion': 'treatment'
    };
    return mapping[messageType] || 'system';
  }

  private calculateProgress(case_: MultiAgentCase): number {
    const statusProgress: Record<string, number> = {
      'initiated': 10,
      'in_progress': 50,
      'consensus_building': 80,
      'completed': 100,
      'escalated': 100
    };
    return statusProgress[case_.status] || 0;
  }

  private estimateTimeRemaining(case_: MultiAgentCase): number {
    const elapsed = Date.now() - new Date(case_.startedAt).getTime();
    const progress = this.calculateProgress(case_);
    
    if (progress >= 100) return 0;
    if (progress <= 0) return 300; // 5 minutes default
    
    const totalEstimated = elapsed / (progress / 100);
    return Math.max(0, totalEstimated - elapsed) / 1000; // Return in seconds
  }

  private extractKeyFindings(case_: MultiAgentCase): string[] {
    return [
      case_.patientContext.chiefComplaint,
      `Complexity: ${case_.complexity}`,
      `Specialties involved: ${case_.requiredSpecialties.join(', ')}`
    ];
  }

  private createFallbackAgentData(agentId: string): UIAgentData {
    return {
      id: agentId,
      name: 'Unknown Agent',
      role: 'unknown',
      specialization: 'Unknown',
      status: 'offline',
      confidence: 0.5,
      avatar: 'user',
      isActive: false,
      currentSessions: [],
      lastActivity: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const collaborationUIService = new CollaborationUIService();
export default collaborationUIService;
