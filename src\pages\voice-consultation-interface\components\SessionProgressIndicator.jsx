import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const SessionProgressIndicator = ({ 
  currentPhase = 'preparation',
  progress = 0,
  estimatedTimeRemaining = 0,
  sessionDuration = 0,
  phases = [],
  className = ''
}) => {
  const [timeDisplay, setTimeDisplay] = useState('');

  const defaultPhases = [
    { id: 'preparation', name: 'Preparation', icon: 'Settings', duration: 2 },
    { id: 'initial_assessment', name: 'Initial Assessment', icon: 'Clipboard', duration: 5 },
    { id: 'consultation', name: 'Consultation', icon: 'MessageCircle', duration: 15 },
    { id: 'agent_discussion', name: 'Agent Discussion', icon: 'Users', duration: 3 },
    { id: 'recommendations', name: 'Recommendations', icon: 'FileText', duration: 5 },
    { id: 'summary', name: 'Summary', icon: 'CheckCircle', duration: 2 }
  ];

  const sessionPhases = phases.length > 0 ? phases : defaultPhases;

  useEffect(() => {
    const formatTime = (minutes) => {
      if (minutes < 1) return '< 1 min';
      if (minutes < 60) return `${Math.round(minutes)} min`;
      const hours = Math.floor(minutes / 60);
      const mins = Math.round(minutes % 60);
      return `${hours}h ${mins}m`;
    };

    setTimeDisplay(formatTime(estimatedTimeRemaining));
  }, [estimatedTimeRemaining]);

  const getCurrentPhaseIndex = () => {
    return sessionPhases.findIndex(phase => phase.id === currentPhase);
  };

  const getPhaseStatus = (phaseIndex) => {
    const currentIndex = getCurrentPhaseIndex();
    if (phaseIndex < currentIndex) return 'completed';
    if (phaseIndex === currentIndex) return 'active';
    return 'pending';
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 shadow-minimal ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="font-semibold text-text-primary font-heading">
            Session Progress
          </h3>
          <p className="text-sm text-text-secondary font-caption">
            {sessionPhases.find(p => p.id === currentPhase)?.name || 'In Progress'}
          </p>
        </div>
        
        <div className="text-right">
          <div className="text-lg font-semibold text-text-primary font-data">
            {Math.round(progress)}%
          </div>
          <div className="text-xs text-text-secondary">Complete</div>
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-secondary-100 rounded-full h-3">
          <div 
            className="bg-primary-500 h-3 rounded-full transition-all duration-500 relative"
            style={{ width: `${progress}%` }}
          >
            <div className="absolute right-0 top-0 w-3 h-3 bg-primary-600 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Phase Timeline */}
      <div className="space-y-4">
        {sessionPhases.map((phase, index) => {
          const status = getPhaseStatus(index);
          return (
            <div 
              key={phase.id}
              className={`flex items-center space-x-4 p-3 rounded-lg transition-all duration-300 ${
                status === 'active' ? 'bg-primary-50 border border-primary-200' :
                status === 'completed'? 'bg-success-50 border border-success-200' : 'bg-secondary-50 border border-secondary-200'
              }`}
            >
              {/* Phase Icon */}
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                status === 'active' ? 'bg-primary-500 animate-breathe' :
                status === 'completed'? 'bg-success-500' : 'bg-secondary-400'
              }`}>
                <Icon 
                  name={status === 'completed' ? 'Check' : phase.icon}
                  size={20}
                  color="white"
                />
              </div>

              {/* Phase Info */}
              <div className="flex-1">
                <h4 className={`font-medium ${
                  status === 'active' ? 'text-primary-700' :
                  status === 'completed'? 'text-success-700' : 'text-text-secondary'
                }`}>
                  {phase.name}
                </h4>
                <p className="text-xs text-text-secondary">
                  {status === 'completed' ? 'Completed' :
                   status === 'active' ? 'In progress...' :
                   `Est. ${phase.duration} min`}
                </p>
              </div>

              {/* Phase Status */}
              <div className="text-right">
                {status === 'active' && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-primary-600 font-medium">
                      Active
                    </span>
                  </div>
                )}
                {status === 'completed' && (
                  <Icon name="CheckCircle" size={16} color="var(--color-success)" />
                )}
                {status === 'pending' && (
                  <Icon name="Clock" size={16} color="var(--color-text-muted)" />
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Time Information */}
      <div className="mt-6 pt-4 border-t border-border">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {formatDuration(sessionDuration)}
            </div>
            <div className="text-xs text-text-secondary">Elapsed</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {timeDisplay}
            </div>
            <div className="text-xs text-text-secondary">Remaining</div>
          </div>
        </div>
      </div>

      {/* Current Phase Details */}
      {currentPhase && (
        <div className="mt-4 p-3 bg-primary-50 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Info" size={14} color="var(--color-primary)" />
            <span className="text-sm font-medium text-primary-700">
              Current Phase
            </span>
          </div>
          <p className="text-sm text-primary-600">
            {currentPhase === 'preparation' && 'Setting up your consultation environment and preparing AI agents.'}
            {currentPhase === 'initial_assessment' && 'Gathering initial information about your health concerns.'}
            {currentPhase === 'consultation' && 'Active consultation with AI medical agents.'}
            {currentPhase === 'agent_discussion' && 'AI agents are discussing your case privately to provide the best recommendations.'}
            {currentPhase === 'recommendations' && 'Preparing personalized health recommendations based on your consultation.'}
            {currentPhase === 'summary' && 'Generating comprehensive session summary and next steps.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default SessionProgressIndicator;