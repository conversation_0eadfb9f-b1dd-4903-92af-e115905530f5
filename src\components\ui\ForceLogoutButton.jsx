import React from 'react';
import { useAuth } from '../../contexts/SimpleAuthContext';

const ForceLogoutButton = () => {
  const { signOut } = useAuth();

  const handleForceLogout = async () => {
    try {
      console.log('Force logout initiated...');
      
      // Clear all storage
      localStorage.clear();
      sessionStorage.clear();
      
      // Sign out from Supabase
      await signOut();
      
      // Force reload to clear all state
      window.location.href = '/authentication-demo-access';
      
    } catch (error) {
      console.error('Force logout error:', error);
      // Even if there's an error, force reload
      window.location.href = '/authentication-demo-access';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50">
      <button
        onClick={handleForceLogout}
        className="px-4 py-2 bg-red-600 text-white rounded-lg shadow-lg hover:bg-red-700 transition-colors font-medium text-sm"
        title="Force logout for demo testing"
      >
        🚨 Force Logout
      </button>
    </div>
  );
};

export default ForceLogoutButton;
