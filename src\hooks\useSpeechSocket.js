/**
 * React hook for WebSocket-based speech recognition
 * Replaces fetch-poll with real-time streaming with offline persistence
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import SpeechEngine from '../services/speechEngine';
import useVoicePersistence from './useVoicePersistence';
import usePWA from './usePWA';

export const useSpeechSocket = (config = {}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcripts, setTranscripts] = useState([]);
  const [connectionQuality, setConnectionQuality] = useState('disconnected'); // green/yellow/red
  const [error, setError] = useState(null);
  const [status, setStatus] = useState({});
  const [offlineMode, setOfflineMode] = useState(false);
  
  const speechEngineRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioStreamRef = useRef(null);
  const chunkBuffer = useRef([]);
  
  // PWA and voice persistence hooks
  const { isOnline, syncStatus, hasPendingSync } = usePWA();
  const voicePersistence = useVoicePersistence(config.sessionId, {
    maxChunkSize: config.maxChunkSize || 64 * 1024,
    autoSync: config.autoSync !== false,
    compressionEnabled: config.compressionEnabled !== false
  });

  /**
   * Initialize speech engine
   */
  const initialize = useCallback(async () => {
    try {
      if (!speechEngineRef.current) {
        speechEngineRef.current = new SpeechEngine();
        
        // Set up event listeners
        speechEngineRef.current.on('connected', () => {
          setIsConnected(true);
          setConnectionQuality('green');
          setOfflineMode(false);
          setError(null);
          
          // Sync any pending voice chunks
          syncPendingChunks();
        });

        speechEngineRef.current.on('disconnected', () => {
          setIsConnected(false);
          setConnectionQuality('red');
          setOfflineMode(!isOnline);
        });

        speechEngineRef.current.on('transcript', (transcript) => {
          setTranscripts(prev => [...prev, transcript]);
        });

        speechEngineRef.current.on('voiceActivity', (vad) => {
          // Update UI based on voice activity
          if (config.onVoiceActivity) {
            config.onVoiceActivity(vad);
          }
        });

        speechEngineRef.current.on('error', (errorData) => {
          setError(errorData);
          if (errorData.type === 'websocket') {
            setConnectionQuality('yellow'); // Degraded connection
          }
        });

        speechEngineRef.current.on('status', (statusData) => {
          setStatus(statusData);
        });
      }

      await speechEngineRef.current.initialize(config);
    } catch (error) {
      setError({ type: 'initialization', error });
      console.error('Speech socket initialization failed:', error);
    }
  }, [config]);

  /**
   * Start recording audio
   */
  const startRecording = useCallback(async () => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 44100,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      audioStreamRef.current = stream;

      // Create MediaRecorder for audio capture
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      const audioChunks = [];

      mediaRecorderRef.current.ondataavailable = async (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
          
          // Process audio chunk
          const audioBuffer = await event.data.arrayBuffer();
          
          // Store chunk for offline persistence
          await storeVoiceChunk(audioBuffer, {
            chunkIndex: chunkBuffer.current.length,
            duration: 100, // 100ms chunks
            quality: isConnected ? 'real-time' : 'offline'
          });
          
          if (speechEngineRef.current && isConnected) {
            await speechEngineRef.current.processAudioChunk(audioBuffer);
          } else if (offlineMode) {
            // Buffer chunk for offline processing
            chunkBuffer.current.push({
              data: audioBuffer,
              timestamp: Date.now(),
              processed: false
            });
          }
        }
      };

      mediaRecorderRef.current.start(100); // Capture every 100ms
      setIsRecording(true);

    } catch (error) {
      setError({ type: 'recording', error });
      console.error('Failed to start recording:', error);
    }
  }, []);

  /**
   * Stop recording audio
   */
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }

    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop());
      audioStreamRef.current = null;
    }
  }, [isRecording]);

  /**
   * Clear transcripts
   */
  const clearTranscripts = useCallback(() => {
    setTranscripts([]);
  }, []);

  /**
   * Change speech engine quality
   */
  const setQuality = useCallback((quality) => {
    if (speechEngineRef.current) {
      speechEngineRef.current.setQuality(quality);
    }
  }, []);

  /**
   * Toggle voice activity detection
   */
  const toggleVAD = useCallback((enabled) => {
    if (speechEngineRef.current) {
      speechEngineRef.current.toggleVAD(enabled);
    }
  }, []);

  /**
   * Get current engine status
   */
  const getEngineStatus = useCallback(() => {
    return speechEngineRef.current ? speechEngineRef.current.getStatus() : {};
  }, []);

  /**
   * Store voice chunk with persistence
   */
  const storeVoiceChunk = useCallback(async (audioBuffer, metadata = {}) => {
    try {
      const chunkId = await voicePersistence.storeVoiceChunk(audioBuffer, metadata);
      return chunkId;
    } catch (error) {
      console.error('Failed to store voice chunk:', error);
      setError({ type: 'storage', error });
    }
  }, [voicePersistence]);

  /**
   * Sync pending voice chunks
   */
  const syncPendingChunks = useCallback(async () => {
    if (!isOnline || !config.sessionId) return;

    try {
      const result = await voicePersistence.forcSync();
      console.log(`Synced ${result.success}/${result.total} voice chunks`);
      
      // Process buffered offline chunks
      if (chunkBuffer.current.length > 0) {
        const bufferedChunks = chunkBuffer.current.splice(0);
        
        for (const chunk of bufferedChunks) {
          if (!chunk.processed && speechEngineRef.current) {
            try {
              await speechEngineRef.current.processAudioChunk(chunk.data);
              chunk.processed = true;
            } catch (error) {
              console.error('Failed to process buffered chunk:', error);
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to sync pending chunks:', error);
      setError({ type: 'sync', error });
    }
  }, [isOnline, config.sessionId, voicePersistence]);

  /**
   * Get voice persistence status
   */
  const getPersistenceStatus = useCallback(() => {
    return voicePersistence.persistenceStatus;
  }, [voicePersistence]);

  /**
   * Clear stored voice data
   */
  const clearVoiceData = useCallback(async () => {
    try {
      await voicePersistence.clearStoredChunks();
      chunkBuffer.current = [];
      console.log('Voice data cleared');
    } catch (error) {
      console.error('Failed to clear voice data:', error);
      setError({ type: 'cleanup', error });
    }
  }, [voicePersistence]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      stopRecording();
      if (speechEngineRef.current) {
        speechEngineRef.current.disconnect();
      }
    };
  }, [stopRecording]);

  /**
   * Auto-initialize on mount
   */
  useEffect(() => {
    if (config.autoInit !== false) {
      initialize();
    }
  }, [initialize, config.autoInit]);

  return {
    // Connection state
    isConnected,
    isRecording,
    connectionQuality,
    error,
    status,
    offlineMode,
    
    // Transcription results
    transcripts,
    
    // Control methods
    initialize,
    startRecording,
    stopRecording,
    clearTranscripts,
    setQuality,
    toggleVAD,
    getEngineStatus,
    
    // Offline & persistence methods
    syncPendingChunks,
    getPersistenceStatus,
    clearVoiceData,
    
    // PWA status
    isOnline,
    syncStatus,
    hasPendingSync,
    
    // Voice persistence status
    persistenceStatus: voicePersistence.persistenceStatus,
    
    // Raw engine access for advanced use
    speechEngine: speechEngineRef.current
  };
};
