/**
 * ENHANCED PATIENT CONTEXT SERVICE
 * 
 * Loads comprehensive patient context for agent conversations including:
 * - Complete patient profile with demographics
 * - Medical history, conditions, medications
 * - Regional health context based on location
 * - Cultural and socioeconomic factors
 * - Emergency contact information
 * 
 * This service bridges the gap between existing context infrastructure
 * and actual agent conversation flow.
 */

import { EventEmitter } from 'events';
import { supabase } from '../utils/supabaseClient';
import type { ComprehensivePatientProfile } from './PatientContextAggregator';

export interface EnhancedPatientContext {
  patientProfile: PatientProfile;
  medicalHistory: MedicalHistory;
  regionalContext: RegionalHealthContext;
  conversationContext: ConversationContext;
  emergencyContext: EmergencyContext;
  culturalContext: CulturalContext;
  lastUpdated: string;
  cacheExpiry: string;
}

export interface PatientProfile {
  id: string;
  fullName: string;
  age: number;
  dateOfBirth: string;
  gender: string;
  country: string;
  city: string;
  stateProvince: string;
  occupation: string;
  insuranceStatus: string;
  healthcareAccessLevel: string;
  culturalBackground: string;
  preferredLanguage: string;
  profileCompletionPercentage: number;
  emergencyContact: EmergencyContact;
}

export interface EmergencyContact {
  name?: string;
  phone?: string;
  relationship?: string;
  email?: string;
}

export interface MedicalHistory {
  conditions: MedicalCondition[];
  medications: Medication[];
  allergies: string[];
  chronicConditions: string[];
  activeConcerns: string[];
  riskFactors: string[];
  lastUpdated: string;
}

export interface MedicalCondition {
  id: string;
  conditionName: string;
  diagnosedDate: string;
  isCurrent: boolean;
  severity: string;
  notes: string;
}

export interface Medication {
  id: string;
  medicationName: string;
  dosage: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  prescribedBy: string;
  notes: string;
}

export interface RegionalHealthContext {
  countryCode: string;
  countryName: string;
  region: string;
  city: string;
  commonConditions: string[];
  endemicDiseases: string[];
  currentSeason: string;
  seasonalRisks: string[];
  healthcareAccessLevel: string;
  traditionalMedicine: string[];
  emergencyContacts: EmergencyContact[];
  culturalConsiderations: string[];
  economicFactors: {
    currency: string;
    healthcareCostConcerns: string;
    insurancePenetration: string;
    outOfPocketExpenses: string;
  };
}

export interface EmergencyContact {
  name: string;
  phone: string;
  type: string;
  location?: string;
}

export interface ConversationContext {
  sessionId: string;
  currentPhase: string;
  previousSessions: number;
  lastConsultationDate?: string;
  preferredConsultationStyle: string;
  communicationPreferences: string[];
}

export interface EmergencyContext {
  hasEmergencyContact: boolean;
  emergencyContactInfo?: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalAlerts: string[];
  criticalAllergies: string[];
  emergencyMedications: string[];
}

export interface CulturalContext {
  primaryLanguage: string;
  culturalBackground: string;
  religiousConsiderations: string[];
  familyInvolvementLevel: string;
  traditionalMedicineUse: boolean;
  communicationStyle: string;
  healthBeliefs: string[];
}

class EnhancedPatientContextService extends EventEmitter {
  private contextCache: Map<string, EnhancedPatientContext> = new Map();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  constructor() {
    super();
    console.log('🏥 Enhanced Patient Context Service initialized');
  }

  /**
   * Load comprehensive patient context for a session
   */
  async loadPatientContext(userId: string, sessionId: string): Promise<EnhancedPatientContext> {
    try {
      console.log(`👤 Loading comprehensive patient context for user: ${userId}`);

      // Check cache first
      const cacheKey = `${userId}-${sessionId}`;
      const cachedContext = this.contextCache.get(cacheKey);
      
      if (cachedContext && new Date(cachedContext.cacheExpiry) > new Date()) {
        console.log('✅ Using cached patient context');
        return cachedContext;
      }

      // Load all context components in parallel
      const [
        patientProfile,
        medicalHistory,
        regionalContext,
        conversationContext,
        emergencyContext,
        culturalContext
      ] = await Promise.all([
        this.loadPatientProfile(userId),
        this.loadMedicalHistory(userId),
        this.loadRegionalContext(userId),
        this.loadConversationContext(userId, sessionId),
        this.loadEmergencyContext(userId),
        this.loadCulturalContext(userId)
      ]);

      const enhancedContext: EnhancedPatientContext = {
        patientProfile,
        medicalHistory,
        regionalContext,
        conversationContext,
        emergencyContext,
        culturalContext,
        lastUpdated: new Date().toISOString(),
        cacheExpiry: new Date(Date.now() + this.CACHE_DURATION).toISOString()
      };

      // Cache the context
      this.contextCache.set(cacheKey, enhancedContext);

      console.log('✅ Patient context loaded successfully');
      this.emit('contextLoaded', { userId, sessionId, context: enhancedContext });

      return enhancedContext;

    } catch (error) {
      console.error('❌ Failed to load patient context:', error);
      throw new Error(`Failed to load patient context: ${error.message}`);
    }
  }

  /**
   * Load patient profile with demographics
   */
  private async loadPatientProfile(userId: string): Promise<PatientProfile> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      throw new Error(`Failed to load patient profile: ${error.message}`);
    }

    const age = data.date_of_birth ? 
      Math.floor((new Date().getTime() - new Date(data.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 
      null;

    // Parse emergency contact from JSONB field
    const emergencyContact: EmergencyContact = {};
    if (data.emergency_contact && typeof data.emergency_contact === 'object') {
      emergencyContact.name = data.emergency_contact.name || '';
      emergencyContact.phone = data.emergency_contact.phone || '';
      emergencyContact.relationship = data.emergency_contact.relationship || '';
      emergencyContact.email = data.emergency_contact.email || '';
    }

    return {
      id: data.id,
      fullName: data.full_name || 'Unknown',
      age: age || 0,
      dateOfBirth: data.date_of_birth || '',
      gender: data.gender || 'not_specified',
      country: data.country || 'Unknown',
      city: data.city || 'Unknown',
      stateProvince: data.state_province || '',
      occupation: data.occupation || 'Not specified',
      insuranceStatus: data.insurance_status || 'Unknown',
      healthcareAccessLevel: data.healthcare_access_level || 'good',
      culturalBackground: data.cultural_background || '',
      preferredLanguage: data.preferred_language || 'English',
      profileCompletionPercentage: data.profile_completion_percentage || 0,
      emergencyContact
    };
  }

  /**
   * Load medical history including conditions and medications
   */
  private async loadMedicalHistory(userId: string): Promise<MedicalHistory> {
    // Load conditions
    const { data: conditions, error: conditionsError } = await supabase
      .from('medical_conditions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (conditionsError) {
      console.warn('Failed to load medical conditions:', conditionsError);
    }

    // Load medications
    const { data: medications, error: medicationsError } = await supabase
      .from('medications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (medicationsError) {
      console.warn('Failed to load medications:', medicationsError);
    }

    const medicalConditions: MedicalCondition[] = (conditions || []).map(condition => ({
      id: condition.id,
      conditionName: condition.condition_name,
      diagnosedDate: condition.diagnosed_date || '',
      isCurrent: condition.is_current || false,
      severity: condition.severity || 'unknown',
      notes: condition.notes || ''
    }));

    const medicationList: Medication[] = (medications || []).map(med => ({
      id: med.id,
      medicationName: med.medication_name,
      dosage: med.dosage || '',
      frequency: med.frequency || '',
      startDate: med.start_date || '',
      endDate: med.end_date,
      isCurrent: med.is_current || false,
      prescribedBy: med.prescribed_by || '',
      notes: med.notes || ''
    }));

    const chronicConditions = medicalConditions
      .filter(c => c.isCurrent && ['chronic', 'ongoing', 'permanent'].includes(c.severity.toLowerCase()))
      .map(c => c.conditionName);

    const activeConcerns = medicalConditions
      .filter(c => c.isCurrent)
      .map(c => c.conditionName);

    return {
      conditions: medicalConditions,
      medications: medicationList,
      allergies: [], // TODO: Add allergies table
      chronicConditions,
      activeConcerns,
      riskFactors: [], // TODO: Calculate risk factors
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Load regional health context based on user location
   */
  private async loadRegionalContext(userId: string): Promise<RegionalHealthContext> {
    try {
      // Use the new geographic context function from our enhanced migration
      const { data, error } = await supabase
        .rpc('get_user_geographic_context', { user_id: userId });

      if (error || !data || data.length === 0) {
        console.warn('Failed to load geographic context, trying fallback method:', error);
        return await this.loadRegionalContextFallback(userId);
      }

      const geoData = data[0];
      const regionalData = geoData.regional_data || {};

      // Determine current season based on location and date
      const currentSeason = this.getCurrentSeason(geoData.user_country);
      const seasonalPatterns = regionalData.seasonal_patterns || {};
      const currentSeasonData = seasonalPatterns[currentSeason] || {};

      return {
        countryCode: geoData.user_country || 'Unknown',
        countryName: regionalData.country_name || 'Unknown',
        region: regionalData.region || this.getRegionFromCountry(geoData.user_country),
        city: geoData.user_city || 'Unknown',
        commonConditions: regionalData.common_conditions || [],
        endemicDiseases: regionalData.endemic_diseases || [],
        currentSeason,
        seasonalRisks: currentSeasonData.common_conditions || [],
        healthcareAccessLevel: regionalData.healthcare_access_level || 'good',
        traditionalMedicine: regionalData.traditional_medicine || [],
        emergencyContacts: this.parseEmergencyContacts(regionalData.emergency_contacts),
        culturalConsiderations: regionalData.cultural_considerations || [],
        economicFactors: this.getEconomicFactors(geoData.user_country, regionalData.economic_factors)
      };

    } catch (error) {
      console.error('Error loading regional context:', error);
      return this.getDefaultRegionalContext();
    }
  }

  /**
   * Fallback method for regional context loading
   */
  private async loadRegionalContextFallback(userId: string): Promise<RegionalHealthContext> {
    const { data, error } = await supabase
      .rpc('get_user_regional_context', { user_id: userId });

    if (error || !data || data.length === 0) {
      console.warn('Fallback regional context also failed, using default:', error);
      return this.getDefaultRegionalContext();
    }

    const regionalData = data[0];
    const seasonalPatterns = regionalData.seasonal_patterns || {};
    const currentSeasonData = seasonalPatterns[regionalData.current_season] || {};

    return {
      countryCode: regionalData.country_code,
      countryName: regionalData.country_name,
      region: regionalData.region || 'Unknown',
      city: 'Unknown',
      commonConditions: regionalData.common_conditions || [],
      endemicDiseases: regionalData.endemic_diseases || [],
      currentSeason: regionalData.current_season,
      seasonalRisks: currentSeasonData.common_conditions || [],
      healthcareAccessLevel: regionalData.healthcare_access_level,
      traditionalMedicine: regionalData.traditional_medicine || [],
      emergencyContacts: this.parseEmergencyContacts(regionalData.emergency_contacts),
      culturalConsiderations: regionalData.cultural_considerations || [],
      economicFactors: {
        currency: 'USD',
        healthcareCostConcerns: 'medium',
        insurancePenetration: 'medium',
        outOfPocketExpenses: 'medium'
      }
    };
  }
  }

  /**
   * Parse emergency contacts from JSONB data
   */
  private parseEmergencyContacts(emergencyContactsData: any): EmergencyContact[] {
    if (!emergencyContactsData || !emergencyContactsData.hospitals) {
      return [];
    }

    return emergencyContactsData.hospitals.map((hospital: any) => ({
      name: hospital.name,
      phone: hospital.phone,
      type: 'hospital',
      location: hospital.location
    }));
  }

  /**
   * Get default regional context (Ghana)
   */
  private getDefaultRegionalContext(): RegionalHealthContext {
    return {
      countryCode: 'GH',
      countryName: 'Ghana',
      region: 'West Africa',
      city: 'Accra',
      commonConditions: ['Malaria', 'Hypertension', 'Diabetes'],
      endemicDiseases: ['Malaria', 'Yellow fever'],
      currentSeason: 'dry_season',
      seasonalRisks: ['Respiratory infections', 'Dehydration'],
      healthcareAccessLevel: 'limited',
      traditionalMedicine: ['Traditional herbal medicine'],
      emergencyContacts: [],
      culturalConsiderations: ['Family involvement in healthcare'],
      economicFactors: {
        currency: 'GHS',
        healthcareCostConcerns: 'high',
        insurancePenetration: 'low',
        outOfPocketExpenses: 'high'
      }
    };
  }

  /**
   * Load conversation context
   */
  private async loadConversationContext(userId: string, sessionId: string): Promise<ConversationContext> {
    // TODO: Implement conversation history loading
    return {
      sessionId,
      currentPhase: 'greeting_and_introduction',
      previousSessions: 0,
      preferredConsultationStyle: 'comprehensive',
      communicationPreferences: ['clear_explanations', 'step_by_step']
    };
  }

  /**
   * Load emergency context
   */
  private async loadEmergencyContext(userId: string): Promise<EmergencyContext> {
    try {
      // Load patient profile to get emergency contact
      const patientProfile = await this.loadPatientProfile(userId);

      // Load medical history for emergency medications and allergies
      const medicalHistory = await this.loadMedicalHistory(userId);

      // Check if emergency contact exists and is complete
      const hasEmergencyContact = !!(
        patientProfile.emergencyContact.name &&
        patientProfile.emergencyContact.phone
      );

      // Extract critical allergies (assuming we have allergy data)
      const criticalAllergies = medicalHistory.allergies.filter(allergy =>
        allergy.toLowerCase().includes('severe') ||
        allergy.toLowerCase().includes('anaphylaxis')
      );

      // Extract emergency medications (current medications that are critical)
      const emergencyMedications = medicalHistory.medications
        .filter(med => med.isCurrent)
        .filter(med =>
          med.medicationName.toLowerCase().includes('insulin') ||
          med.medicationName.toLowerCase().includes('epinephrine') ||
          med.medicationName.toLowerCase().includes('nitroglycerin') ||
          med.medicationName.toLowerCase().includes('albuterol')
        )
        .map(med => med.medicationName);

      return {
        hasEmergencyContact,
        emergencyContactInfo: hasEmergencyContact ? patientProfile.emergencyContact : undefined,
        medicalAlerts: [], // TODO: Implement medical alerts system
        criticalAllergies,
        emergencyMedications
      };

    } catch (error) {
      console.error('Error loading emergency context:', error);
      return {
        hasEmergencyContact: false,
        medicalAlerts: [],
        criticalAllergies: [],
        emergencyMedications: []
      };
    }
  }

  /**
   * Load cultural context
   */
  private async loadCulturalContext(userId: string): Promise<CulturalContext> {
    // TODO: Enhance with cultural data
    return {
      primaryLanguage: 'English',
      culturalBackground: '',
      religiousConsiderations: [],
      familyInvolvementLevel: 'moderate',
      traditionalMedicineUse: false,
      communicationStyle: 'direct',
      healthBeliefs: []
    };
  }

  /**
   * Clear context cache for a user
   */
  clearUserCache(userId: string): void {
    const keysToDelete = Array.from(this.contextCache.keys())
      .filter(key => key.startsWith(userId));
    
    keysToDelete.forEach(key => this.contextCache.delete(key));
    console.log(`🗑️ Cleared context cache for user: ${userId}`);
  }

  /**
   * Get current season based on country and date
   */
  private getCurrentSeason(countryCode: string): string {
    const now = new Date();
    const month = now.getMonth() + 1; // 1-12

    // Determine hemisphere based on country
    const southernHemisphere = ['ZA', 'AU', 'NZ', 'AR', 'CL', 'BR'];
    const isSouthern = southernHemisphere.includes(countryCode);

    if (isSouthern) {
      // Southern hemisphere seasons are opposite
      if (month >= 12 || month <= 2) return 'summer';
      if (month >= 3 && month <= 5) return 'autumn';
      if (month >= 6 && month <= 8) return 'winter';
      return 'spring';
    } else {
      // Northern hemisphere
      if (month >= 12 || month <= 2) return 'winter';
      if (month >= 3 && month <= 5) return 'spring';
      if (month >= 6 && month <= 8) return 'summer';
      return 'autumn';
    }
  }

  /**
   * Get region from country code
   */
  private getRegionFromCountry(countryCode: string): string {
    const regionMap: Record<string, string> = {
      'NG': 'West Africa',
      'GH': 'West Africa',
      'KE': 'East Africa',
      'ZA': 'Southern Africa',
      'EG': 'North Africa',
      'US': 'North America',
      'CA': 'North America',
      'GB': 'Europe',
      'DE': 'Europe',
      'FR': 'Europe',
      'IN': 'South Asia',
      'CN': 'East Asia',
      'JP': 'East Asia',
      'AU': 'Oceania',
      'BR': 'South America'
    };

    return regionMap[countryCode] || 'Unknown Region';
  }

  /**
   * Get economic factors based on country and regional data
   */
  private getEconomicFactors(countryCode: string, regionalEconomicData?: any): any {
    // Default economic factors by country
    const defaultFactors: Record<string, any> = {
      'NG': {
        currency: 'NGN',
        healthcareCostConcerns: 'high',
        insurancePenetration: 'low',
        outOfPocketExpenses: 'very_high'
      },
      'GH': {
        currency: 'GHS',
        healthcareCostConcerns: 'high',
        insurancePenetration: 'medium',
        outOfPocketExpenses: 'high'
      },
      'KE': {
        currency: 'KES',
        healthcareCostConcerns: 'high',
        insurancePenetration: 'low',
        outOfPocketExpenses: 'high'
      },
      'ZA': {
        currency: 'ZAR',
        healthcareCostConcerns: 'medium',
        insurancePenetration: 'medium',
        outOfPocketExpenses: 'medium'
      },
      'US': {
        currency: 'USD',
        healthcareCostConcerns: 'very_high',
        insurancePenetration: 'high',
        outOfPocketExpenses: 'high'
      }
    };

    const defaultForCountry = defaultFactors[countryCode] || {
      currency: 'USD',
      healthcareCostConcerns: 'medium',
      insurancePenetration: 'medium',
      outOfPocketExpenses: 'medium'
    };

    // Merge with regional data if available
    return { ...defaultForCountry, ...regionalEconomicData };
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.contextCache.size,
      keys: Array.from(this.contextCache.keys())
    };
  }
}

// Export singleton instance
export const enhancedPatientContextService = new EnhancedPatientContextService();
