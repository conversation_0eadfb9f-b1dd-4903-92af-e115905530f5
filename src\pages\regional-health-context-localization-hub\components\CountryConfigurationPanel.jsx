import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CountryConfigurationPanel = ({ country, localizationData, onUpdate }) => {
  const [activeSection, setActiveSection] = useState('diseases');
  const [isEditing, setIsEditing] = useState(false);

  const sections = [
    { id: 'diseases', label: 'Disease Patterns', icon: 'Activity' },
    { id: 'medicines', label: 'Local Medicines', icon: 'Pill' },
    { id: 'terminology', label: 'Medical Terminology', icon: 'Book' },
    { id: 'protocols', label: 'Health Protocols', icon: 'FileText' }
  ];

  const [diseaseData, setDiseaseData] = useState({
    endemic: country?.commonConditions?.slice(0, 4) || [],
    seasonal: country?.seasonalPatterns || {},
    emerging: ['COVID-19 variants', 'Monkeypox', 'Dengue fever outbreaks'],
    prevalenceRates: {
      'Malaria': '45%',
      'Hypertension': '28%',
      'Diabetes': '12%',
      'HIV/AIDS': country?.id === 'south-africa' ? '18%' : '8%'
    }
  });

  const [medicineData, setMedicineData] = useState({
    traditional: country?.traditionalMedicine || [],
    availability: {
      'Artemether-lumefantrine': 'Widely Available',
      'Amoxicillin': 'Available',
      'Paracetamol': 'Widely Available',
      'Iron supplements': 'Limited',
      'Insulin': country?.id === 'south-africa' ? 'Available' : 'Limited'
    },
    costs: {
      'Basic consultation': country?.currency === 'ZAR' ? 'R150-300' : 
                          country?.currency === 'NGN' ? '₦2000-5000' :
                          country?.currency === 'KES' ? 'KSh500-1500' : 'GH₵20-50',
      'Prescription drugs': 'Varies significantly',
      'Emergency care': 'Often subsidized'
    }
  });

  const [terminologyData, setTerminologyData] = useState({
    medicalTerms: {
      'High blood pressure': country?.primaryLanguages?.includes('Swahili') ? 'Shinikizo la damu' :
                            country?.primaryLanguages?.includes('Yoruba') ? 'Eje giga' :
                            country?.primaryLanguages?.includes('Twi') ? 'Mogya mmoroso' : 'High BP',
      'Diabetes': country?.primaryLanguages?.includes('Swahili') ? 'Kisukari' :
                 country?.primaryLanguages?.includes('Yoruba') ? 'Àtọgbẹ́' :
                 country?.primaryLanguages?.includes('Twi') ? 'Asisi yadeε' : 'Sugar sickness',
      'Malaria': country?.primaryLanguages?.includes('Swahili') ? 'Malaria' :
                country?.primaryLanguages?.includes('Yoruba') ? 'Ìba' :
                country?.primaryLanguages?.includes('Twi') ? 'Atiridii' : 'Fever sickness'
    },
    symptoms: {
      'Headache': country?.primaryLanguages?.includes('Swahili') ? 'Maumivu ya kichwa' :
                 country?.primaryLanguages?.includes('Yoruba') ? 'Orí fífọ́' : 'Head pain',
      'Fever': country?.primaryLanguages?.includes('Swahili') ? 'Homa' :
              country?.primaryLanguages?.includes('Yoruba') ? 'Ìbà' : 'Body heat',
      'Cough': country?.primaryLanguages?.includes('Swahili') ? 'Kikohozi' :
              country?.primaryLanguages?.includes('Yoruba') ? 'Íko' : 'Cough'
    }
  });

  const [protocolData, setProtocolData] = useState({
    emergencyNumbers: {
      'Emergency Services': country?.id === 'ghana' ? '193/999' :
                          country?.id === 'nigeria' ? '199/112' :
                          country?.id === 'kenya' ? '999/112' : '10177',
      'Ambulance': country?.id === 'ghana' ? '193' :
                  country?.id === 'nigeria' ? '199' :
                  country?.id === 'kenya' ? '999' : '10177',
      'Poison Control': country?.id === 'south-africa' ? '0861 555 777' : 'Contact nearest hospital'
    },
    referralProtocols: {
      'Primary to Secondary': 'Referral letter required',
      'Emergency cases': 'Direct admission allowed',
      'Specialist consultation': 'GP referral preferred'
    },
    insuranceRequirements: {
      'Public healthcare': country?.healthSystemType,
      'Documentation': 'ID/Passport + Insurance card',
      'Payment': 'Co-payment may apply'
    }
  });

  const renderSection = () => {
    switch (activeSection) {
      case 'diseases':
        return (
          <div className="space-y-6">
            {/* Endemic Diseases */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Endemic Diseases</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {diseaseData.endemic.map((disease, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-surface border border-border rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-error-500 rounded-full"></div>
                      <span className="text-text-primary">{disease}</span>
                    </div>
                    <span className="text-sm text-text-secondary">
                      {diseaseData.prevalenceRates[disease] || 'N/A'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Seasonal Patterns */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Seasonal Disease Patterns</h4>
              <div className="space-y-3">
                {Object.entries(diseaseData.seasonal).map(([season, diseases]) => (
                  <div key={season} className="p-4 bg-surface border border-border rounded-lg">
                    <h5 className="font-medium text-text-primary mb-2">{season}</h5>
                    <div className="flex flex-wrap gap-2">
                      {diseases.map((disease, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-warning-100 text-warning-700 text-xs rounded-full"
                        >
                          {disease}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Emerging Threats */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Emerging Health Threats</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {diseaseData.emerging.map((threat, index) => (
                  <div key={index} className="p-3 bg-warning-50 border border-warning-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Icon name="AlertTriangle" size={16} className="text-warning-600" />
                      <span className="text-sm font-medium text-warning-700">{threat}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'medicines':
        return (
          <div className="space-y-6">
            {/* Traditional Medicine */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Traditional Medicine Practices</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {medicineData.traditional.map((practice, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Icon name="Leaf" size={16} className="text-success-500" />
                      <span className="font-medium text-text-primary">{practice}</span>
                    </div>
                    <p className="text-sm text-text-secondary">
                      Recognized traditional healing practice in {country?.name}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Medicine Availability */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Medicine Availability</h4>
              <div className="space-y-3">
                {Object.entries(medicineData.availability).map(([medicine, status]) => (
                  <div key={medicine} className="flex items-center justify-between p-3 bg-surface border border-border rounded-lg">
                    <span className="text-text-primary">{medicine}</span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      status === 'Widely Available' ? 'bg-success-100 text-success-700' :
                      status === 'Available'? 'bg-info-100 text-info-700' : 'bg-warning-100 text-warning-700'
                    }`}>
                      {status}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Cost Information */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Healthcare Costs</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(medicineData.costs).map(([service, cost]) => (
                  <div key={service} className="p-4 bg-surface border border-border rounded-lg">
                    <h5 className="font-medium text-text-primary mb-1">{service}</h5>
                    <p className="text-lg font-semibold text-primary-600">{cost}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'terminology':
        return (
          <div className="space-y-6">
            {/* Medical Terms Translation */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Medical Terms in Local Languages</h4>
              <div className="space-y-3">
                {Object.entries(terminologyData.medicalTerms).map(([english, local]) => (
                  <div key={english} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-text-secondary mb-1">English</p>
                        <p className="font-medium text-text-primary">{english}</p>
                      </div>
                      <div>
                        <p className="text-sm text-text-secondary mb-1">Local Translation</p>
                        <p className="font-medium text-text-primary">{local}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Common Symptoms */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Common Symptoms Translation</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(terminologyData.symptoms).map(([english, local]) => (
                  <div key={english} className="p-3 bg-surface border border-border rounded-lg">
                    <p className="text-sm text-text-secondary">{english}</p>
                    <p className="font-medium text-text-primary">{local}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Language Coverage */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Language Coverage</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {country?.primaryLanguages?.map((language, index) => (
                  <div key={index} className="p-3 bg-primary-50 border border-primary-200 rounded-lg text-center">
                    <p className="font-medium text-primary-700">{language}</p>
                    <div className="mt-2">
                      <div className="w-full bg-primary-200 rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full"
                          style={{ width: `${85 + Math.random() * 15}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-primary-600 mt-1">
                        {Math.floor(85 + Math.random() * 15)}% Coverage
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'protocols':
        return (
          <div className="space-y-6">
            {/* Emergency Protocols */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Emergency Contact Numbers</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(protocolData.emergencyNumbers).map(([service, number]) => (
                  <div key={service} className="p-4 bg-error-50 border border-error-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Icon name="Phone" size={16} className="text-error-600" />
                      <h5 className="font-medium text-error-800">{service}</h5>
                    </div>
                    <p className="text-xl font-bold text-error-700">{number}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Referral Protocols */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Referral Protocols</h4>
              <div className="space-y-3">
                {Object.entries(protocolData.referralProtocols).map(([type, protocol]) => (
                  <div key={type} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-start gap-3">
                      <Icon name="ArrowRight" size={16} className="text-primary-500 mt-0.5" />
                      <div>
                        <h5 className="font-medium text-text-primary">{type}</h5>
                        <p className="text-text-secondary mt-1">{protocol}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Insurance Requirements */}
            <div>
              <h4 className="font-medium text-text-primary mb-3">Insurance & Payment Requirements</h4>
              <div className="space-y-3">
                {Object.entries(protocolData.insuranceRequirements).map(([requirement, details]) => (
                  <div key={requirement} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-start gap-3">
                      <Icon name="CreditCard" size={16} className="text-success-500 mt-0.5" />
                      <div>
                        <h5 className="font-medium text-text-primary">{requirement}</h5>
                        <p className="text-text-secondary mt-1">{details}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-2xl">{country?.flag}</span>
          <div>
            <h2 className="text-xl font-semibold text-text-primary font-heading">
              Medical Context Configuration - {country?.name}
            </h2>
            <p className="text-text-secondary">
              Configure medical knowledge and protocols for local healthcare delivery
            </p>
          </div>
        </div>
        
        <div className="flex gap-3">
          <Button
            variant={isEditing ? "primary" : "outline"}
            onClick={() => setIsEditing(!isEditing)}
            iconName={isEditing ? "Save" : "Edit"}
          >
            {isEditing ? "Save Changes" : "Edit Configuration"}
          </Button>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="border-b border-border">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 ${
                activeSection === section.id
                  ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-border'
              }`}
            >
              <Icon name={section.icon} size={16} />
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="bg-surface border border-border rounded-lg p-6">
        {renderSection()}
      </div>
    </div>
  );
};

export default CountryConfigurationPanel;