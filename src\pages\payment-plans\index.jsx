import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2, AlertCircle, Database, RefreshCw } from 'lucide-react';
import Header from '../../components/ui/Header';
import Button from '../../components/ui/Button';
import PaymentModal from '../../components/ui/PaymentModal';
import PaymentHistory from '../../components/ui/PaymentHistory';
import DatabaseSetupPrompt from '../../components/ui/DatabaseSetupPrompt';
import DatabaseErrorBoundary from '../../components/ui/DatabaseErrorBoundary';
import { usePayment } from '../../contexts/PaymentContext';
import { useAuth } from '../../contexts/SimpleAuthContext';
import PlanCard from './components/PlanCard';
import databaseSetup from '../../utils/databaseSetup';

const PaymentPlans = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { 
    subscriptionPlans, 
    subscription, 
    paymentHistory, 
    hasActiveSubscription, 
    getRemainingCredits,
    loading,
    subscribeUser,
    isProcessingPayment,
    paymentError,
    loadPaymentData,
    databaseError,
    isDatabaseSetupRequired,
    autoFixDatabase,
    getDemoPlans
  } = usePayment();

  const [selectedPlan, setSelectedPlan] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState('NGN');
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [showPaymentHistory, setShowPaymentHistory] = useState(false);
  const [showDatabaseSetup, setShowDatabaseSetup] = useState(false);
  const [isDatabaseSetupComplete, setIsDatabaseSetupComplete] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/authentication-demo-access');
    }
  }, [user, navigate]);

  // Check database setup status on mount
  useEffect(() => {
    checkDatabaseSetup();
  }, []);

  // Show database setup when required
  useEffect(() => {
    if (isDatabaseSetupRequired && databaseError) {
      setShowDatabaseSetup(true);
    }
  }, [isDatabaseSetupRequired, databaseError]);

  const checkDatabaseSetup = async () => {
    try {
      const status = await databaseSetup.getSetupStatus();
      const isComplete = status.databaseSetup.success && status.subscriptionPlans.hasPlans;
      setIsDatabaseSetupComplete(isComplete);
      
      // Show setup prompt if database is not properly configured
      if (!isComplete && (paymentError?.includes('does not exist') || databaseError)) {
        setShowDatabaseSetup(true);
      }
    } catch (error) {
      console.error('Failed to check database setup:', error);
    }
  };

  const handleDatabaseSetupComplete = async (result) => {
    if (result.success) {
      setIsDatabaseSetupComplete(true);
      setShowDatabaseSetup(false);
      
      // Reload payment data to get real subscription plans
      await loadPaymentData();
      
      // Force refresh of subscription plans
      window.location.reload();
    }
  };

  const handleDatabaseErrorRetry = async () => {
    await loadPaymentData();
  };

  const handleSelectPlan = (plan) => {
    // Don't allow demo plan selection
    if (plan.is_demo) {
      setShowDatabaseSetup(true);
      return;
    }
    
    setSelectedPlan(plan);
    setIsPaymentModalOpen(true);
  };

  const handlePaymentSuccess = () => {
    setIsPaymentModalOpen(false);
    setSelectedPlan(null);
    // Show success message or redirect
    setTimeout(() => {
      navigate('/payment-success');
    }, 1000);
  };

  const formatPrice = (priceNgn, priceUsd, currency) => {
    const price = currency === 'USD' ? priceUsd : priceNgn;
    const formatter = new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
    return formatter.format(price);
  };

  const getCurrentPlanFeatures = () => {
    if (!subscription?.subscription_plans) return [];
    return subscription.subscription_plans.features || [];
  };

  const getRemainingDays = () => {
    if (!subscription?.expires_at) return 0;
    const now = new Date();
    const expiryDate = new Date(subscription.expires_at);
    const diffTime = expiryDate - now;
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  };

  if (!user) {
    return null;
  }

  // Show database error boundary if there's a database issue
  if (databaseError && databaseError.type === 'missing_table') {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Choose Your Health Plan
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get unlimited access to AI-powered health consultations with our flexible subscription plans
            </p>
          </div>

          <DatabaseErrorBoundary
            error={databaseError.error}
            onRetry={handleDatabaseErrorRetry}
            onSetupComplete={handleDatabaseSetupComplete}
            showAutoFix={true}
            className="mb-8 bg-white"
          />

          {/* Show demo plans while database is being set up */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <div className="flex items-start">
              <Database size={20} className="text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-800 mb-1">
                  Demo Mode
                </h3>
                <p className="text-sm text-blue-700">
                  Showing demo plans while database is being configured. Complete the setup above to access real subscription plans.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {getDemoPlans().map((plan) => (
              <PlanCard
                key={plan.id}
                plan={plan}
                currency={selectedCurrency}
                isPopular={plan.is_popular}
                isCurrentPlan={false}
                onSelect={() => handleSelectPlan(plan)}
                disabled
                isDemoMode={true}
              />
            ))}
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose Your Health Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get unlimited access to AI-powered health consultations with our flexible subscription plans
          </p>
        </div>

        {/* Currency Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-200">
            <button
              onClick={() => setSelectedCurrency('NGN')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedCurrency === 'NGN' ?'bg-blue-600 text-white' :'text-gray-600 hover:text-gray-900'
              }`}
            >
              NGN (₦)
            </button>
            <button
              onClick={() => setSelectedCurrency('USD')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedCurrency === 'USD' ?'bg-blue-600 text-white' :'text-gray-600 hover:text-gray-900'
              }`}
            >
              USD ($)
            </button>
          </div>
        </div>

        {/* Database Setup Warning */}
        {(databaseError || !isDatabaseSetupComplete) && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-8">
            <div className="flex items-start">
              <Database size={20} className="text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-amber-800 mb-1">
                  Database Setup Required
                </h3>
                <p className="text-sm text-amber-700 mb-3">
                  {databaseError?.message || 'The subscription plans table is not found in your Supabase database. Demo plans are being shown.'}
                </p>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    onClick={() => setShowDatabaseSetup(true)}
                    className="bg-amber-600 hover:bg-amber-700"
                  >
                    <Database size={14} className="mr-1" />
                    Setup Database
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={checkDatabaseSetup}
                  >
                    <RefreshCw size={14} className="mr-1" />
                    Check Status
                  </Button>
                  {databaseError?.autoFixAvailable && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={autoFixDatabase}
                      className="border-blue-600 text-blue-600 hover:bg-blue-50"
                    >
                      Auto Fix
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* No Active Subscription Warning */}
        {!hasActiveSubscription() && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-8">
            <div className="flex items-start">
              <AlertCircle size={20} className="text-orange-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-orange-800 mb-1">
                  No Active Subscription
                </h3>
                <p className="text-sm text-orange-700">
                  Subscribe to a plan to start your AI-powered health consultations.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center py-12">
            <Loader2 size={32} className="animate-spin text-blue-600" />
          </div>
        )}

        {/* Subscription Plans */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {subscriptionPlans?.map((plan) => (
              <PlanCard
                key={plan.id}
                plan={plan}
                currency={selectedCurrency}
                isPopular={plan.is_popular}
                isCurrentPlan={subscription?.plan_id === plan.id}
                onSelect={() => handleSelectPlan(plan)}
                disabled={isProcessingPayment}
              />
            ))}
          </div>
        )}

        {/* Payment History Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Payment History
            </h2>
            <Button
              variant={showPaymentHistory ? 'secondary' : 'outline'}
              onClick={() => setShowPaymentHistory(!showPaymentHistory)}
            >
              {showPaymentHistory ? 'Hide' : 'Show'} History
            </Button>
          </div>

          {showPaymentHistory && (
            <PaymentHistory
              transactions={paymentHistory}
              onDownloadReceipt={(transactionId) => {
                console.log('Download receipt for:', transactionId);
                // Implement receipt download logic
              }}
            />
          )}
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Frequently Asked Questions
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                How do consultations work?
              </h3>
              <p className="text-gray-600 text-sm">
                Our AI agents provide real-time voice consultations, analyzing your health concerns and providing personalized recommendations.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Can I cancel anytime?
              </h3>
              <p className="text-gray-600 text-sm">
                Yes, you can cancel your subscription at any time. You'll continue to have access until your current billing period ends.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600 text-sm">
                We accept all major credit/debit cards, bank transfers, USSD, and mobile money payments through Paystack.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Is my data secure?
              </h3>
              <p className="text-gray-600 text-sm">
                Yes, all your health data is encrypted and stored securely. We comply with international healthcare data protection standards.
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => {
          setIsPaymentModalOpen(false);
          setSelectedPlan(null);
        }}
        paymentType="subscription"
        planId={selectedPlan?.id}
        amount={selectedCurrency === 'USD' ? selectedPlan?.price_usd : selectedPlan?.price_ngn}
        currency={selectedCurrency}
        planDetails={selectedPlan}
      />

      {/* Database Setup Prompt */}
      {showDatabaseSetup && (
        <DatabaseSetupPrompt
          onSetupComplete={handleDatabaseSetupComplete}
          onClose={() => setShowDatabaseSetup(false)}
        />
      )}
    </div>
  );
};

export default PaymentPlans;