/**
 * Service Worker Management
 * Handles SW registration, updates, and communication
 */

class ServiceWorkerManager {
  constructor() {
    this.registration = null;
    this.updateAvailable = false;
    this.isOnline = navigator.onLine;
    this.eventTarget = new EventTarget();
    
    // Bind methods
    this.handleMessage = this.handleMessage.bind(this);
    this.handleStateChange = this.handleStateChange.bind(this);
    this.handleNetworkChange = this.handleNetworkChange.bind(this);
  }

  /**
   * Initialize service worker
   */
  async init() {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Worker not supported');
      return false;
    }

    try {
      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('Service Worker registered:', this.registration.scope);

      // Set up event listeners
      this.setupEventListeners();

      // Check for updates
      this.checkForUpdates();

      // Set up periodic update checks
      this.scheduleUpdateChecks();

      return true;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return false;
    }
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    if (!this.registration) return;

    // Listen for service worker state changes
    this.registration.addEventListener('updatefound', this.handleStateChange);
    
    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', this.handleMessage);
    
    // Listen for network changes
    window.addEventListener('online', this.handleNetworkChange);
    window.addEventListener('offline', this.handleNetworkChange);

    // Listen for app focus changes
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkForUpdates();
      }
    });
  }

  /**
   * Handle messages from service worker
   */
  handleMessage(event) {
    const { type, data } = event.data;

    switch (type) {
      case 'SW_UPDATE_AVAILABLE':
        this.updateAvailable = true;
        this.emit('update-available', data);
        break;

      case 'SW_UPDATED':
        this.emit('updated', data);
        break;

      case 'CACHE_STATUS':
        this.emit('cache-status', data);
        break;

      case 'SYNC_STATUS':
        this.emit('sync-status', data);
        break;

      case 'BACKGROUND_SYNC_SUCCESS':
        this.emit('sync-success', data);
        break;

      case 'BACKGROUND_SYNC_FAILED':
        this.emit('sync-failed', data);
        break;

      default:
        console.log('Unknown SW message:', type, data);
    }
  }

  /**
   * Handle service worker state changes
   */
  handleStateChange() {
    if (!this.registration.installing) return;

    const newWorker = this.registration.installing;
    
    newWorker.addEventListener('statechange', () => {
      switch (newWorker.state) {
        case 'installed':
          if (navigator.serviceWorker.controller) {
            // New service worker installed, update available
            this.updateAvailable = true;
            this.emit('update-available', {
              worker: newWorker,
              registration: this.registration
            });
          } else {
            // First time install
            this.emit('installed', {
              worker: newWorker,
              registration: this.registration
            });
          }
          break;

        case 'activated':
          this.emit('activated', {
            worker: newWorker,
            registration: this.registration
          });
          break;

        case 'redundant':
          this.emit('redundant', {
            worker: newWorker,
            registration: this.registration
          });
          break;
      }
    });
  }

  /**
   * Handle network changes
   */
  handleNetworkChange() {
    const wasOnline = this.isOnline;
    this.isOnline = navigator.onLine;

    if (wasOnline !== this.isOnline) {
      this.emit('network-change', { isOnline: this.isOnline });
      
      if (this.isOnline) {
        // Trigger background sync when coming back online
        this.triggerBackgroundSync();
      }
    }
  }

  /**
   * Check for service worker updates
   */
  async checkForUpdates() {
    if (!this.registration) return;

    try {
      await this.registration.update();
    } catch (error) {
      console.error('Failed to check for SW updates:', error);
    }
  }

  /**
   * Schedule periodic update checks
   */
  scheduleUpdateChecks() {
    // Check for updates every hour
    setInterval(() => {
      if (!document.hidden) {
        this.checkForUpdates();
      }
    }, 60 * 60 * 1000);
  }

  /**
   * Apply pending update
   */
  async applyUpdate() {
    if (!this.registration?.waiting) {
      throw new Error('No update available');
    }

    // Send skip waiting message to service worker
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });

    // Wait for activation
    return new Promise((resolve) => {
      const handleControllerChange = () => {
        navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
        resolve();
        
        // Reload page to use new service worker
        window.location.reload();
      };

      navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
    });
  }

  /**
   * Send message to service worker
   */
  async sendMessage(type, data = {}) {
    if (!this.registration?.active) {
      throw new Error('No active service worker');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        if (event.data.error) {
          reject(new Error(event.data.error));
        } else {
          resolve(event.data);
        }
      };

      this.registration.active.postMessage(
        { type, data },
        [messageChannel.port2]
      );
    });
  }

  /**
   * Get cache status
   */
  async getCacheStatus() {
    try {
      return await this.sendMessage('GET_CACHE_STATUS');
    } catch (error) {
      console.error('Failed to get cache status:', error);
      return null;
    }
  }

  /**
   * Clear all caches
   */
  async clearCaches() {
    try {
      await this.sendMessage('CLEAR_CACHE');
      this.emit('cache-cleared');
    } catch (error) {
      console.error('Failed to clear caches:', error);
      throw error;
    }
  }

  /**
   * Cache voice chunk
   */
  async cacheVoiceChunk(chunkData) {
    try {
      await this.sendMessage('CACHE_VOICE_CHUNK', chunkData);
    } catch (error) {
      console.error('Failed to cache voice chunk:', error);
      throw error;
    }
  }

  /**
   * Trigger background sync
   */
  async triggerBackgroundSync(tags = []) {
    if (!this.registration?.sync) {
      console.warn('Background sync not supported');
      return;
    }

    const defaultTags = [
      'messages-sync',
      'voice-chunks-sync',
      'triage-results-sync',
      'consultations-sync',
      'analytics-sync',
      'error-logs-sync'
    ];

    const syncTags = tags.length > 0 ? tags : defaultTags;

    try {
      for (const tag of syncTags) {
        await this.registration.sync.register(tag);
      }
      console.log('Background sync registered for tags:', syncTags);
    } catch (error) {
      console.error('Failed to register background sync:', error);
    }
  }

  /**
   * Force sync specific tag
   */
  async forceSync(tag) {
    try {
      await this.sendMessage('FORCE_SYNC', { tag });
    } catch (error) {
      console.error(`Failed to force sync ${tag}:`, error);
      throw error;
    }
  }

  /**
   * Get sync status
   */
  async getSyncStatus() {
    try {
      return await this.sendMessage('GET_SYNC_STATUS');
    } catch (error) {
      console.error('Failed to get sync status:', error);
      return null;
    }
  }

  /**
   * Check if service worker is ready
   */
  isReady() {
    return this.registration && this.registration.active;
  }

  /**
   * Check if update is available
   */
  hasUpdateAvailable() {
    return this.updateAvailable && this.registration?.waiting;
  }

  /**
   * Get current service worker state
   */
  getState() {
    if (!this.registration) return 'not-registered';
    if (this.registration.installing) return 'installing';
    if (this.registration.waiting) return 'waiting';
    if (this.registration.active) return 'active';
    return 'unknown';
  }

  /**
   * Event emitter methods
   */
  emit(eventType, data) {
    this.eventTarget.dispatchEvent(new CustomEvent(eventType, { detail: data }));
  }

  on(eventType, callback) {
    this.eventTarget.addEventListener(eventType, callback);
  }

  off(eventType, callback) {
    this.eventTarget.removeEventListener(eventType, callback);
  }

  /**
   * Unregister service worker (for development/testing)
   */
  async unregister() {
    if (!this.registration) return false;

    try {
      const result = await this.registration.unregister();
      console.log('Service Worker unregistered:', result);
      this.registration = null;
      return result;
    } catch (error) {
      console.error('Failed to unregister service worker:', error);
      return false;
    }
  }
}

// Create singleton instance
const swManager = new ServiceWorkerManager();

export default swManager;
