/**
 * Emergency Scenario Testing Framework
 * Comprehensive test suite for emergency scenarios that bypasses authentication entirely
 * Tests emergency protocols across all audio consultation interfaces
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import emergencyStopService from '../services/emergencyStopService';
import emergencyAuthService from '../services/emergencyAuthService';

// Mock emergency contexts for testing
interface MockEmergencyContext {
  sessionId: string;
  userId: string;
  reason: string;
  isOffline: boolean;
  networkDegraded: boolean;
  authenticationFailed: boolean;
}

// Mock audio components for testing
class MockMediaRecorder {
  state: 'inactive' | 'recording' | 'paused' = 'inactive';
  private listeners: Map<string, Function[]> = new Map();

  start() {
    this.state = 'recording';
  }

  stop() {
    this.state = 'inactive';
    this.dispatchEvent('stop');
  }

  addEventListener(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  private dispatchEvent(event: string) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback());
  }
}

class MockMediaStream {
  private tracks: MockMediaStreamTrack[] = [];
  private listeners: Map<string, Function[]> = new Map();

  constructor() {
    this.tracks = [new MockMediaStreamTrack()];
  }

  getTracks() {
    return this.tracks;
  }

  addEventListener(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }
}

class MockMediaStreamTrack {
  readyState: 'live' | 'ended' = 'live';

  stop() {
    this.readyState = 'ended';
  }
}

class MockAudioContext {
  state: 'suspended' | 'running' | 'closed' = 'running';
  private listeners: Map<string, Function[]> = new Map();

  async suspend() {
    this.state = 'suspended';
    this.dispatchEvent('statechange');
  }

  async close() {
    this.state = 'closed';
    this.dispatchEvent('statechange');
  }

  addEventListener(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  private dispatchEvent(event: string) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback());
  }
}

describe('Emergency Scenario Testing Framework', () => {
  let mockEmergencyContext: MockEmergencyContext;
  let mockMediaRecorder: MockMediaRecorder;
  let mockMediaStream: MockMediaStream;
  let mockAudioContext: MockAudioContext;

  beforeEach(() => {
    // Reset emergency services
    vi.clearAllMocks();
    
    // Create mock emergency context
    mockEmergencyContext = {
      sessionId: 'emergency-test-session-' + Date.now(),
      userId: 'emergency-test-user',
      reason: 'test_emergency',
      isOffline: false,
      networkDegraded: false,
      authenticationFailed: false
    };

    // Create mock audio components
    mockMediaRecorder = new MockMediaRecorder();
    mockMediaStream = new MockMediaStream();
    mockAudioContext = new MockAudioContext();

    // Mock performance.now for consistent timing
    vi.spyOn(performance, 'now').mockReturnValue(0);
  });

  afterEach(() => {
    // Clean up emergency states
    emergencyStopService.clearEmergencyState?.(mockEmergencyContext.sessionId);
    vi.restoreAllMocks();
  });

  describe('Emergency Authentication Bypass', () => {
    it('should bypass authentication entirely for emergency scenarios', async () => {
      const startTime = performance.now();

      // Create emergency session without authentication
      const emergencySession = await emergencyAuthService.createEmergencySession(
        mockEmergencyContext.userId,
        mockEmergencyContext.sessionId,
        'medical_emergency',
        'emergency_test_system'
      );

      const responseTime = performance.now() - startTime;

      expect(emergencySession).toBeDefined();
      expect(emergencySession.sessionId).toBe(mockEmergencyContext.sessionId);
      expect(emergencySession.isActive).toBe(true);
      expect(responseTime).toBeLessThan(100); // Should be nearly instantaneous
    });

    it('should validate emergency authentication context immediately', async () => {
      const startTime = performance.now();

      const emergencyAuthContext = {
        emergencyOverride: true,
        emergencyToken: 'test-emergency-token',
        reason: 'medical_emergency',
        sessionId: mockEmergencyContext.sessionId,
        bypassAuthentication: true
      };

      const isValid = await emergencyAuthService.validateEmergencyAuth(emergencyAuthContext);
      const responseTime = performance.now() - startTime;

      expect(isValid).toBe(true);
      expect(responseTime).toBeLessThan(50); // Should be extremely fast
    });

    it('should provide pre-cached emergency tokens', () => {
      const stats = emergencyAuthService.getEmergencyAuthStats();
      
      expect(stats.availableTokens).toBeGreaterThan(0);
      expect(emergencyAuthService.isEmergencyAuthAvailable()).toBe(true);
    });
  });

  describe('Emergency Stop Response Time', () => {
    it('should trigger emergency stop in < 2 seconds', async () => {
      // Register mock audio components
      emergencyStopService.registerMediaRecorder(mockMediaRecorder as any);
      emergencyStopService.registerMediaStream(mockMediaStream as any);
      emergencyStopService.registerAudioContext(mockAudioContext as any);

      // Start recording
      mockMediaRecorder.start();

      const startTime = performance.now();
      
      const result = await emergencyStopService.triggerEmergencyStop(
        'medical_emergency',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      expect(result.success).toBe(true);
      expect(result.responseTime).toBeLessThan(2000); // < 2 second requirement
      expect(mockMediaRecorder.state).toBe('inactive');
      expect(mockAudioContext.state).toBe('suspended');
    });

    it('should terminate audio components immediately', async () => {
      // Register and start audio components
      emergencyStopService.registerMediaRecorder(mockMediaRecorder as any);
      emergencyStopService.registerMediaStream(mockMediaStream as any);
      emergencyStopService.registerAudioContext(mockAudioContext as any);

      mockMediaRecorder.start();

      const result = await emergencyStopService.triggerEmergencyStop(
        'user_initiated',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      expect(result.success).toBe(true);
      expect(mockMediaRecorder.state).toBe('inactive');
      expect(mockMediaStream.getTracks()[0].readyState).toBe('ended');
      expect(mockAudioContext.state).toBe('suspended');
    });

    it('should work without network dependencies', async () => {
      // Simulate offline condition
      mockEmergencyContext.isOffline = true;

      const result = await emergencyStopService.triggerEmergencyStop(
        'network_failure_emergency',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      expect(result.success).toBe(true);
      expect(result.responseTime).toBeLessThan(2000);
    });
  });

  describe('Emergency Callbacks and State Management', () => {
    it('should execute emergency callbacks immediately', async () => {
      let callbackExecuted = false;
      const emergencyCallback = () => {
        callbackExecuted = true;
      };

      emergencyStopService.registerEmergencyCallback(emergencyCallback);

      await emergencyStopService.triggerEmergencyStop(
        'callback_test',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      expect(callbackExecuted).toBe(true);
    });

    it('should cache emergency state locally', async () => {
      await emergencyStopService.triggerEmergencyStop(
        'local_cache_test',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      const cachedState = emergencyStopService.getEmergencyStateFromCache?.(mockEmergencyContext.sessionId);
      expect(cachedState).toBeDefined();
      expect(cachedState?.isEmergencyActive).toBe(true);
    });

    it('should maintain emergency state across page reloads', async () => {
      await emergencyStopService.triggerEmergencyStop(
        'page_reload_test',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      // Simulate page reload by checking localStorage
      const cachedState = localStorage.getItem(`emergency_${mockEmergencyContext.sessionId}`);
      expect(cachedState).toBeDefined();
      
      const parsedState = JSON.parse(cachedState!);
      expect(parsedState.isEmergencyActive).toBe(true);
    });
  });

  describe('Offline and Degraded Network Scenarios', () => {
    it('should function in offline PWA mode', async () => {
      // Mock offline condition
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false
      });

      const result = await emergencyStopService.triggerEmergencyStop(
        'offline_emergency',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      expect(result.success).toBe(true);
      expect(result.responseTime).toBeLessThan(2000);
    });

    it('should handle degraded network conditions', async () => {
      // Simulate slow network by adding delay to async operations
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = ((callback: Function, delay: number) => {
        if (delay === 0) {
          // Immediate callbacks should still be immediate
          return originalSetTimeout(callback, 0);
        }
        return originalSetTimeout(callback, delay * 10); // Simulate slow network
      }) as any;

      const result = await emergencyStopService.triggerEmergencyStop(
        'degraded_network_emergency',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      expect(result.success).toBe(true);
      expect(result.responseTime).toBeLessThan(2000); // Should still meet requirement

      global.setTimeout = originalSetTimeout;
    });
  });

  describe('HIPAA Compliance in Emergency Scenarios', () => {
    it('should maintain audit logging during emergencies', async () => {
      const auditLogSpy = vi.fn();
      
      // Mock audit logger
      vi.doMock('../utils/auditLogger', () => ({
        default: {
          logEmergencyAccess: auditLogSpy
        }
      }));

      await emergencyStopService.triggerEmergencyStop(
        'hipaa_compliance_test',
        mockEmergencyContext.sessionId,
        mockEmergencyContext.userId
      );

      // Allow time for async logging
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(auditLogSpy).toHaveBeenCalled();
    });

    it('should log emergency authentication bypass events', async () => {
      const emergencySession = await emergencyAuthService.createEmergencySession(
        mockEmergencyContext.userId,
        mockEmergencyContext.sessionId,
        'hipaa_audit_test',
        'test_system'
      );

      expect(emergencySession).toBeDefined();
      // Audit logging is tested through the service's internal logging mechanism
    });
  });

  describe('Performance and Reliability', () => {
    it('should handle multiple concurrent emergency stops', async () => {
      const concurrentStops = Array.from({ length: 5 }, (_, i) => 
        emergencyStopService.triggerEmergencyStop(
          `concurrent_test_${i}`,
          `session_${i}`,
          `user_${i}`
        )
      );

      const results = await Promise.all(concurrentStops);

      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.responseTime).toBeLessThan(2000);
      });
    });

    it('should maintain performance under stress', async () => {
      const stressTests = Array.from({ length: 20 }, (_, i) => 
        emergencyStopService.triggerEmergencyStop(
          `stress_test_${i}`,
          `stress_session_${i}`,
          `stress_user_${i}`
        )
      );

      const startTime = performance.now();
      const results = await Promise.all(stressTests);
      const totalTime = performance.now() - startTime;

      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.responseTime).toBeLessThan(2000);
      });

      // Total time for 20 concurrent emergency stops should be reasonable
      expect(totalTime).toBeLessThan(5000);
    });
  });

  describe('Emergency Statistics and Monitoring', () => {
    it('should provide emergency statistics', () => {
      const stats = emergencyStopService.getEmergencyStats?.();

      expect(stats).toBeDefined();
      expect(typeof stats?.activeRecorders).toBe('number');
      expect(typeof stats?.activeStreams).toBe('number');
      expect(typeof stats?.activeContexts).toBe('number');
    });

    it('should track emergency authentication statistics', () => {
      const authStats = emergencyAuthService.getEmergencyAuthStats();

      expect(authStats).toBeDefined();
      expect(typeof authStats.activeSessions).toBe('number');
      expect(typeof authStats.availableTokens).toBe('number');
      expect(typeof authStats.totalSessions).toBe('number');
    });
  });

  describe('Interface-Specific Emergency Scenarios', () => {
    describe('Enhanced Voice Consultation Interface', () => {
      it('should handle emergency stop during active recording', async () => {
        // Simulate active recording state
        mockMediaRecorder.start();
        emergencyStopService.registerMediaRecorder(mockMediaRecorder as any);

        const result = await emergencyStopService.triggerEmergencyStop(
          'enhanced_voice_emergency',
          mockEmergencyContext.sessionId,
          mockEmergencyContext.userId
        );

        expect(result.success).toBe(true);
        expect(result.responseTime).toBeLessThan(2000);
        expect(mockMediaRecorder.state).toBe('inactive');
      });

      it('should handle emergency stop during AI processing', async () => {
        const result = await emergencyStopService.triggerEmergencyStop(
          'ai_processing_emergency',
          mockEmergencyContext.sessionId,
          mockEmergencyContext.userId
        );

        expect(result.success).toBe(true);
        expect(result.protocolsTriggered).toContain('immediate_termination');
      });
    });

    describe('Real-time Multi-agent Collaboration', () => {
      it('should handle emergency stop during multi-agent session', async () => {
        // Simulate multi-agent collaboration
        const multiAgentContext = {
          ...mockEmergencyContext,
          sessionId: 'multi-agent-session-' + Date.now(),
          reason: 'multi_agent_emergency'
        };

        const result = await emergencyStopService.triggerEmergencyStop(
          'multi_agent_collaboration_emergency',
          multiAgentContext.sessionId,
          multiAgentContext.userId
        );

        expect(result.success).toBe(true);
        expect(result.responseTime).toBeLessThan(2000);
      });
    });

    describe('PWA Offline Mode', () => {
      it('should handle emergency stop in offline PWA mode', async () => {
        // Mock PWA offline state
        Object.defineProperty(navigator, 'onLine', {
          writable: true,
          value: false
        });

        const result = await emergencyStopService.triggerEmergencyStop(
          'pwa_offline_emergency',
          mockEmergencyContext.sessionId,
          mockEmergencyContext.userId
        );

        expect(result.success).toBe(true);
        expect(result.responseTime).toBeLessThan(2000);
      });

      it('should sync emergency events when back online', async () => {
        // Simulate offline emergency
        Object.defineProperty(navigator, 'onLine', {
          writable: true,
          value: false
        });

        await emergencyStopService.triggerEmergencyStop(
          'offline_sync_test',
          mockEmergencyContext.sessionId,
          mockEmergencyContext.userId
        );

        // Simulate coming back online
        Object.defineProperty(navigator, 'onLine', {
          writable: true,
          value: true
        });

        // Check that emergency state is preserved for sync
        const cachedState = emergencyStopService.getEmergencyStateFromCache?.(mockEmergencyContext.sessionId);
        expect(cachedState).toBeDefined();
        expect(cachedState?.isEmergencyActive).toBe(true);
      });
    });
  });
});
