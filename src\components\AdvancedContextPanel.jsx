/**
 * ADVANCED CONTEXT PANEL
 * 
 * React component that displays rich contextual insights, patient profiling,
 * predictive analytics, and real-time collaboration data from the advanced
 * context system to enhance user experience and clinical decision making.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from './ui/Tabs';
import { 
  Brain, 
  Heart, 
  TrendingUp, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Target,
  Activity,
  Shield
} from 'lucide-react';

const AdvancedContextPanel = ({ 
  unifiedContext, 
  contextualInsights = [], 
  riskAssessment, 
  recommendations = [],
  performanceMetrics,
  patientProfile,
  isVisible = true 
}) => {
  const [activeTab, setActiveTab] = useState('insights');
  const [contextHealth, setContextHealth] = useState('good');

  useEffect(() => {
    if (performanceMetrics) {
      const overallScore = performanceMetrics.overallPerformance || 0.8;
      if (overallScore >= 0.8) setContextHealth('excellent');
      else if (overallScore >= 0.6) setContextHealth('good');
      else if (overallScore >= 0.4) setContextHealth('fair');
      else setContextHealth('poor');
    }
  }, [performanceMetrics]);

  if (!isVisible || !unifiedContext) {
    return null;
  }

  const getRiskLevelColor = (level) => {
    switch (level) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'moderate': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getContextHealthColor = (health) => {
    switch (health) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatConfidence = (confidence) => {
    return Math.round((confidence || 0) * 100);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Brain className="h-5 w-5" />
            Advanced Context Intelligence
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getContextHealthColor(contextHealth)}>
              Context Health: {contextHealth.toUpperCase()}
            </Badge>
            {performanceMetrics && (
              <Badge variant="secondary">
                Performance: {formatConfidence(performanceMetrics.overallPerformance)}%
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="insights" className="flex items-center gap-1">
              <Brain className="h-4 w-4" />
              Insights
            </TabsTrigger>
            <TabsTrigger value="risk" className="flex items-center gap-1">
              <AlertTriangle className="h-4 w-4" />
              Risk
            </TabsTrigger>
            <TabsTrigger value="patient" className="flex items-center gap-1">
              <Heart className="h-4 w-4" />
              Patient
            </TabsTrigger>
            <TabsTrigger value="predictions" className="flex items-center gap-1">
              <TrendingUp className="h-4 w-4" />
              Predictions
            </TabsTrigger>
            <TabsTrigger value="collaboration" className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              Collaboration
            </TabsTrigger>
          </TabsList>

          <TabsContent value="insights" className="mt-4 space-y-4">
            <div className="grid gap-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Target className="h-5 w-5" />
                Key Contextual Insights
              </h3>
              
              {contextualInsights.length > 0 ? (
                <div className="space-y-3">
                  {contextualInsights.slice(0, 5).map((insight, index) => (
                    <Alert key={index} className="border-blue-200 bg-blue-50">
                      <CheckCircle className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800">
                        <strong>Insight {index + 1}:</strong> {insight}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              ) : (
                <Alert className="border-gray-200">
                  <AlertDescription>
                    Contextual insights will appear here as the conversation develops.
                  </AlertDescription>
                </Alert>
              )}

              {unifiedContext.synthesizedContext?.confidenceMetrics && (
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-600">Overall Confidence</label>
                    <Progress 
                      value={formatConfidence(unifiedContext.synthesizedContext.confidenceMetrics.overallConfidence)} 
                      className="h-2"
                    />
                    <span className="text-xs text-gray-500">
                      {formatConfidence(unifiedContext.synthesizedContext.confidenceMetrics.overallConfidence)}%
                    </span>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-600">Data Quality</label>
                    <Progress 
                      value={formatConfidence(unifiedContext.synthesizedContext.confidenceMetrics.dataQuality)} 
                      className="h-2"
                    />
                    <span className="text-xs text-gray-500">
                      {formatConfidence(unifiedContext.synthesizedContext.confidenceMetrics.dataQuality)}%
                    </span>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="risk" className="mt-4 space-y-4">
            <div className="grid gap-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Risk Assessment
              </h3>
              
              {riskAssessment ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className={`w-4 h-4 rounded-full ${getRiskLevelColor(riskAssessment.overallRiskLevel)}`}></div>
                      <span className="font-medium">Overall Risk Level:</span>
                      <Badge variant="outline" className="capitalize">
                        {riskAssessment.overallRiskLevel}
                      </Badge>
                    </div>
                    {riskAssessment.riskScore && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Risk Score:</span>
                        <span className="font-bold text-lg">{riskAssessment.riskScore}/100</span>
                      </div>
                    )}
                  </div>

                  {riskAssessment.riskFactors && riskAssessment.riskFactors.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Primary Risk Factors:</h4>
                      <div className="grid gap-2">
                        {riskAssessment.riskFactors.slice(0, 3).map((factor, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-orange-50 rounded border border-orange-200">
                            <span className="text-sm">{factor.factor}</span>
                            <Badge variant="secondary" className="text-xs">
                              {factor.category}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <Alert className="border-gray-200">
                  <AlertDescription>
                    Risk assessment data will be displayed here based on patient context.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="patient" className="mt-4 space-y-4">
            <div className="grid gap-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Patient Profile
              </h3>
              
              {patientProfile ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-600">Patient ID</label>
                      <p className="text-sm bg-gray-50 p-2 rounded">{patientProfile.patientId}</p>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-600">Profile Completeness</label>
                      <Progress 
                        value={formatConfidence(patientProfile.dataQuality?.completeness || 0.7)} 
                        className="h-2"
                      />
                    </div>
                  </div>

                  {patientProfile.medicalHistory && (
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Medical History Summary:</h4>
                      <div className="grid gap-2">
                        {patientProfile.medicalHistory.chronicConditions?.slice(0, 3).map((condition, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-blue-50 rounded border border-blue-200">
                            <span className="text-sm">{condition.condition}</span>
                            <Badge variant="outline" className="text-xs">
                              {condition.severity}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <Alert className="border-gray-200">
                  <AlertDescription>
                    Patient profile information will be built as the conversation progresses.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="predictions" className="mt-4 space-y-4">
            <div className="grid gap-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Predictive Insights
              </h3>
              
              {recommendations.length > 0 ? (
                <div className="space-y-3">
                  {recommendations.slice(0, 4).map((rec, index) => (
                    <Alert key={index} className="border-green-200 bg-green-50">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-green-800">
                        <strong>Recommendation {index + 1}:</strong> {rec.recommendation || rec}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              ) : (
                <Alert className="border-gray-200">
                  <AlertDescription>
                    Predictive insights and recommendations will appear based on conversation analysis.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="collaboration" className="mt-4 space-y-4">
            <div className="grid gap-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Agent Collaboration
              </h3>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded border border-blue-200">
                  <Activity className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold text-blue-800">
                    {unifiedContext.participantProfiles?.length || 1}
                  </div>
                  <div className="text-sm text-blue-600">Active Agents</div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded border border-green-200">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold text-green-800">
                    {formatConfidence(performanceMetrics?.efficiencyGain || 0.75)}%
                  </div>
                  <div className="text-sm text-green-600">Efficiency Gain</div>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded border border-purple-200">
                  <Clock className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                  <div className="text-2xl font-bold text-purple-800">
                    {Math.round((performanceMetrics?.averageResponseTime || 1500) / 1000)}s
                  </div>
                  <div className="text-sm text-purple-600">Avg Response</div>
                </div>
              </div>

              <Alert className="border-blue-200 bg-blue-50">
                <Users className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  <strong>Multi-Agent System Active:</strong> Advanced context intelligence is enhancing agent collaboration and decision-making in real-time.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AdvancedContextPanel;
