import { supabase } from './supabaseClient';
import pwaService from './pwaService';


// Medical conditions database with Africa-specific diseases
const MEDICAL_CONDITIONS_DB = {
  // Common symptoms and their urgency weights
  symptoms: {
    'chest_pain': { weight: 9, category: 'cardiovascular', redFlag: true },
    'difficulty_breathing': { weight: 9, category: 'respiratory', redFlag: true },
    'severe_headache': { weight: 8, category: 'neurological', redFlag: true },
    'high_fever': { weight: 7, category: 'infectious', redFlag: false },
    'persistent_cough': { weight: 6, category: 'respiratory', redFlag: false },
    'abdominal_pain': { weight: 6, category: 'gastrointestinal', redFlag: false },
    'fatigue': { weight: 4, category: 'general', redFlag: false },
    'nausea_vomiting': { weight: 5, category: 'gastrointestinal', redFlag: false },
    'dizziness': { weight: 5, category: 'neurological', redFlag: false },
    'skin_rash': { weight: 3, category: 'dermatological', redFlag: false },
    'joint_pain': { weight: 4, category: 'musculoskeletal', redFlag: false },
    'loss_of_consciousness': { weight: 10, category: 'neurological', redFlag: true },
    'severe_bleeding': { weight: 10, category: 'trauma', redFlag: true },
    'inability_to_speak': { weight: 9, category: 'neurological', redFlag: true },
    'severe_dehydration': { weight: 8, category: 'general', redFlag: true }
  },

  // Africa-specific disease patterns by country
  regionalDiseases: {
    ghana: {
      common: ['malaria', 'typhoid', 'hepatitis_b', 'tuberculosis', 'hypertension'],
      endemic: ['malaria', 'schistosomiasis', 'onchocerciasis'],
      seasonal: ['meningitis', 'cholera']
    },
    nigeria: {
      common: ['malaria', 'tuberculosis', 'hepatitis_b', 'typhoid', 'hiv'],
      endemic: ['malaria', 'lassa_fever', 'yellow_fever'],
      seasonal: ['meningitis', 'cholera']
    },
    kenya: {
      common: ['malaria', 'tuberculosis', 'hiv', 'typhoid', 'dengue'],
      endemic: ['malaria', 'rift_valley_fever', 'chikungunya'],
      seasonal: ['cholera', 'dengue']
    },
    south_africa: {
      common: ['hiv', 'tuberculosis', 'hypertension', 'diabetes', 'malaria'],
      endemic: ['hiv', 'tuberculosis'],
      seasonal: ['influenza', 'gastroenteritis']
    }
  },

  // Red flag combinations that require immediate attention
  redFlagCombinations: [
    ['chest_pain', 'difficulty_breathing'],
    ['severe_headache', 'high_fever'],
    ['loss_of_consciousness', 'severe_bleeding'],
    ['difficulty_breathing', 'high_fever'],
    ['chest_pain', 'dizziness']
  ],

  // Emergency protocols
  emergencyProtocols: {
    cardiovascular_emergency: {
      symptoms: ['chest_pain', 'difficulty_breathing', 'dizziness'],
      action: 'immediate_hospital',
      timeLimit: 30, // minutes
      instructions: 'Call emergency services immediately. Do not drive yourself.'
    },
    respiratory_emergency: {
      symptoms: ['difficulty_breathing', 'inability_to_speak'],
      action: 'immediate_hospital',
      timeLimit: 15,
      instructions: 'Call emergency services. Sit upright, loosen clothing.'
    },
    neurological_emergency: {
      symptoms: ['loss_of_consciousness', 'severe_headache', 'inability_to_speak'],
      action: 'immediate_hospital',
      timeLimit: 20,
      instructions: 'Call emergency services. Do not give food or water.'
    },
    infectious_disease: {
      symptoms: ['high_fever', 'persistent_cough', 'fatigue'],
      action: 'urgent_care',
      timeLimit: 240,
      instructions: 'Seek medical attention within 4 hours. Isolate if possible.'
    }
  }
};

// WHO/CDC-aligned evidence-based guidelines
const EVIDENCE_BASED_GUIDELINES = {
  urgencyClassification: {
    critical: { score: 9-10, timeFrame: 'immediate', color: 'red' },
    urgent: { score: 7-8, timeFrame: '1-2 hours', color: 'orange' },
    semi_urgent: { score: 5-6, timeFrame: '4-6 hours', color: 'yellow' },
    routine: { score: 3-4, timeFrame: '24-48 hours', color: 'green' },
    self_care: { score: 1-2, timeFrame: 'monitor', color: 'blue' }
  },

  culturalConsiderations: {
    family_involvement: true,
    traditional_medicine_respect: true,
    cost_sensitivity: true,
    language_barriers: true,
    religious_considerations: true
  }
};

const triageService = {
  // Automatic urgency assessment
  assessUrgency: async (symptoms, patientProfile = {}) => {
    try {
      let totalScore = 0;
      let redFlags = [];
      let detectedConditions = [];
      let recommendedProtocols = [];

      // Calculate symptom scores
      symptoms.forEach(symptom => {
        const symptomData = MEDICAL_CONDITIONS_DB.symptoms[symptom];
        if (symptomData) {
          totalScore += symptomData.weight;
          
          if (symptomData.redFlag) {
            redFlags.push(symptom);
          }
        }
      });

      // Check for red flag combinations
      MEDICAL_CONDITIONS_DB.redFlagCombinations.forEach(combination => {
        const hasAllSymptoms = combination.every(symptom => symptoms.includes(symptom));
        if (hasAllSymptoms) {
          totalScore += 2; // Bonus score for dangerous combinations
          redFlags = [...new Set([...redFlags, ...combination])];
        }
      });

      // Regional disease pattern analysis
      const userCountry = patientProfile.country?.toLowerCase() || 'ghana';
      const regionalData = MEDICAL_CONDITIONS_DB.regionalDiseases[userCountry];
      
      if (regionalData) {
        // Check for endemic disease symptoms
        const suspectedDiseases = this.checkRegionalDiseases(symptoms, regionalData);
        detectedConditions = suspectedDiseases;
        
        // Adjust score based on regional prevalence
        if (suspectedDiseases.length > 0) {
          totalScore += 1;
        }
      }

      // Determine urgency level
      let urgencyLevel = 'routine';
      for (const [level, criteria] of Object.entries(EVIDENCE_BASED_GUIDELINES.urgencyClassification)) {
        if (typeof criteria.score === 'string') {
          const [min, max] = criteria.score.split('-').map(Number);
          if (totalScore >= min && totalScore <= max) {
            urgencyLevel = level;
            break;
          }
        }
      }

      // Check for matching emergency protocols
      Object.entries(MEDICAL_CONDITIONS_DB.emergencyProtocols).forEach(([protocolName, protocol]) => {
        const matchingSymptoms = protocol.symptoms.filter(s => symptoms.includes(s));
        if (matchingSymptoms.length >= 2) {
          recommendedProtocols.push({
            name: protocolName,
            ...protocol,
            matchingSymptoms
          });
        }
      });

      const assessment = {
        urgencyScore: totalScore,
        urgencyLevel,
        redFlags,
        detectedConditions,
        recommendedProtocols,
        timeFrame: EVIDENCE_BASED_GUIDELINES.urgencyClassification[urgencyLevel]?.timeFrame,
        color: EVIDENCE_BASED_GUIDELINES.urgencyClassification[urgencyLevel]?.color,
        requiresEmergencyEscalation: urgencyLevel === 'critical' || redFlags.length > 0,
        culturalConsiderations: EVIDENCE_BASED_GUIDELINES.culturalConsiderations,
        timestamp: new Date().toISOString(),
        patientId: patientProfile.id
      };

      // Store assessment offline for later sync
      await this.storeTriageAssessment(assessment);

      return {
        success: true,
        data: assessment
      };

    } catch (error) {
      console.error('Triage assessment error:', error);
      return {
        success: false,
        error: 'Failed to assess urgency. Please try again.'
      };
    }
  },

  // Check for regional disease patterns
  checkRegionalDiseases: (symptoms, regionalData) => {
    const suspectedDiseases = [];
    
    // Malaria symptoms check
    if (symptoms.includes('high_fever') && symptoms.includes('fatigue')) {
      if (regionalData.common.includes('malaria')) {
        suspectedDiseases.push({
          disease: 'malaria',
          confidence: 'high',
          recommendation: 'Rapid diagnostic test needed'
        });
      }
    }

    // Tuberculosis symptoms check
    if (symptoms.includes('persistent_cough') && symptoms.includes('fatigue')) {
      if (regionalData.common.includes('tuberculosis')) {
        suspectedDiseases.push({
          disease: 'tuberculosis',
          confidence: 'medium',
          recommendation: 'Chest X-ray and sputum test recommended'
        });
      }
    }

    // Typhoid fever check
    if (symptoms.includes('high_fever') && symptoms.includes('abdominal_pain')) {
      if (regionalData.common.includes('typhoid')) {
        suspectedDiseases.push({
          disease: 'typhoid',
          confidence: 'medium',
          recommendation: 'Blood culture test needed'
        });
      }
    }

    return suspectedDiseases;
  },

  // Red flag detection
  detectRedFlags: (symptoms) => {
    const redFlags = [];
    
    symptoms.forEach(symptom => {
      const symptomData = MEDICAL_CONDITIONS_DB.symptoms[symptom];
      if (symptomData?.redFlag) {
        redFlags.push({
          symptom,
          severity: 'high',
          action: 'immediate_medical_attention',
          category: symptomData.category
        });
      }
    });

    // Check combinations
    MEDICAL_CONDITIONS_DB.redFlagCombinations.forEach(combination => {
      const hasAllSymptoms = combination.every(symptom => symptoms.includes(symptom));
      if (hasAllSymptoms) {
        redFlags.push({
          combination,
          severity: 'critical',
          action: 'emergency_services',
          message: 'Dangerous combination of symptoms detected'
        });
      }
    });

    return redFlags;
  },

  // Emergency escalation
  initiateEmergencyEscalation: async (assessment, contactInfo = {}) => {
    try {
      const escalation = {
        id: Math.random().toString(36).substr(2, 9),
        assessmentId: assessment.id,
        patientId: assessment.patientId,
        urgencyLevel: assessment.urgencyLevel,
        redFlags: assessment.redFlags,
        escalationType: assessment.urgencyLevel === 'critical' ? 'emergency_call' : 'urgent_referral',
        contactInfo,
        status: 'initiated',
        timestamp: new Date().toISOString(),
        location: contactInfo.location || null,
        emergencyContacts: this.getEmergencyContacts(contactInfo.country)
      };

      // Store escalation record
      await this.storeEscalationRecord(escalation);

      // Auto-trigger emergency services for critical cases
      if (assessment.urgencyLevel === 'critical') {
        return {
          success: true,
          data: escalation,
          action: 'emergency_call',
          message: 'Emergency services should be contacted immediately',
          phoneNumber: this.getEmergencyNumber(contactInfo.country)
        };
      }

      return {
        success: true,
        data: escalation,
        action: 'urgent_referral',
        message: 'Urgent medical attention required'
      };

    } catch (error) {
      console.error('Emergency escalation error:', error);
      return {
        success: false,
        error: 'Failed to initiate emergency escalation'
      };
    }
  },

  // Get emergency contacts by country
  getEmergencyContacts: (country) => {
    const contacts = {
      ghana: {
        emergency: '999',
        ambulance: '193',
        police: '999',
        fire: '192'
      },
      nigeria: {
        emergency: '199',
        ambulance: '199',
        police: '199',
        fire: '199'
      },
      kenya: {
        emergency: '999',
        ambulance: '999',
        police: '999',
        fire: '999'
      },
      south_africa: {
        emergency: '10177',
        ambulance: '10177',
        police: '10111',
        fire: '10177'
      }
    };

    return contacts[country?.toLowerCase()] || contacts.ghana;
  },

  // Get primary emergency number
  getEmergencyNumber: (country) => {
    const contacts = this.getEmergencyContacts(country);
    return contacts.emergency;
  },

  // Store triage assessment
  storeTriageAssessment: async (assessment) => {
    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('triage_assessments')
          .insert(assessment)
          .select()
          .single();

        if (error) throw error;
        
        // Also store offline as backup
        localStorage.setItem('lastTriageAssessment', JSON.stringify(data));
        return { success: true, data };
      } else {
        // Store offline only
        const offlineAssessments = JSON.parse(localStorage.getItem('offlineTriageAssessments') || '[]');
        offlineAssessments.push(assessment);
        localStorage.setItem('offlineTriageAssessments', JSON.stringify(offlineAssessments));
        localStorage.setItem('lastTriageAssessment', JSON.stringify(assessment));
        
        return { success: true, data: assessment, offline: true };
      }
    } catch (error) {
      console.error('Store assessment error:', error);
      // Fallback to local storage
      localStorage.setItem('lastTriageAssessment', JSON.stringify(assessment));
      return { success: true, data: assessment, offline: true };
    }
  },

  // Store escalation record
  storeEscalationRecord: async (escalation) => {
    try {
      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('emergency_escalations')
          .insert(escalation)
          .select()
          .single();

        if (error) throw error;
        return { success: true, data };
      } else {
        // Store offline
        const offlineEscalations = JSON.parse(localStorage.getItem('offlineEscalations') || '[]');
        offlineEscalations.push(escalation);
        localStorage.setItem('offlineEscalations', JSON.stringify(offlineEscalations));
        
        return { success: true, data: escalation, offline: true };
      }
    } catch (error) {
      console.error('Store escalation error:', error);
      return { success: false, error: 'Failed to store escalation record' };
    }
  },

  // Get consultation queue with priority
  getTriageQueue: async (userId) => {
    try {
      const queue = [];

      if (pwaService.isAppOnline()) {
        const { data, error } = await supabase
          .from('triage_assessments')
          .select('*')
          .order('urgencyScore', { ascending: false })
          .limit(50);

        if (!error && data) {
          queue.push(...data);
        }
      }

      // Add offline assessments
      const offlineAssessments = JSON.parse(localStorage.getItem('offlineTriageAssessments') || '[]');
      queue.push(...offlineAssessments);

      // Sort by urgency score
      queue.sort((a, b) => b.urgencyScore - a.urgencyScore);

      return {
        success: true,
        data: queue.slice(0, 20) // Return top 20
      };

    } catch (error) {
      console.error('Get triage queue error:', error);
      return {
        success: false,
        error: 'Failed to load triage queue'
      };
    }
  },

  // Cultural adaptation for recommendations
  adaptRecommendationsCulturally: (recommendations, patientProfile) => {
    const adaptedRecommendations = [...recommendations];
    
    // Add family involvement consideration
    if (EVIDENCE_BASED_GUIDELINES.culturalConsiderations.family_involvement) {
      adaptedRecommendations.unshift({
        type: 'cultural',
        message: 'Consider involving family members in medical decisions',
        priority: 'medium'
      });
    }

    // Add cost-sensitive alternatives
    if (EVIDENCE_BASED_GUIDELINES.culturalConsiderations.cost_sensitivity) {
      adaptedRecommendations.forEach(rec => {
        if (rec.cost === 'high') {
          rec.alternatives = ['Visit government health center', 'Seek community health worker'];
        }
      });
    }

    // Add traditional medicine respect
    if (EVIDENCE_BASED_GUIDELINES.culturalConsiderations.traditional_medicine_respect) {
      adaptedRecommendations.push({
        type: 'cultural',
        message: 'Traditional remedies may complement but should not replace urgent medical care',
        priority: 'medium'
      });
    }

    return adaptedRecommendations;
  }
};

export default triageService;