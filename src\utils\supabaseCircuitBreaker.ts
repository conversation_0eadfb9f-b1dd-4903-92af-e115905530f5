/**
 * <PERSON><PERSON><PERSON>AS<PERSON> CIRCUIT BREAKER WRAPPER
 * 
 * Provides circuit breaker protection for Supabase database operations
 * to prevent cascading failures and ensure graceful degradation.
 * 
 * FEATURES:
 * - Circuit breaker protection for all database operations
 * - Emergency bypass for critical medical operations
 * - Fallback mechanisms for read operations
 * - Performance monitoring and metrics
 * - HIPAA-compliant error handling
 */

import { supabase } from './supabaseClient';
import { circuitBreakerService, CircuitBreakerOpenError } from '../services/CircuitBreakerService';
import { auditLogger } from './auditLogger';

export interface SupabaseOperation<T> {
  operation: () => Promise<T>;
  operationType: 'read' | 'write' | 'rpc';
  tableName?: string;
  emergencyContext?: boolean;
  fallbackData?: T;
}

export class SupabaseCircuitBreaker {
  private circuitBreaker = circuitBreakerService.getCircuitBreaker('supabase', {
    failureThreshold: 5,
    recoveryTimeoutMs: 30000, // 30 seconds
    emergencyBypass: true,
    fallbackEnabled: true
  });

  constructor() {
    console.log('🔧 Supabase Circuit Breaker initialized');
    this.setupFallbacks();
  }

  /**
   * Execute Supabase operation with circuit breaker protection
   */
  async execute<T>(operation: SupabaseOperation<T>): Promise<T> {
    try {
      return await this.circuitBreaker.execute(async () => {
        const startTime = Date.now();
        
        try {
          const result = await operation.operation();
          
          // Log successful operation
          const duration = Date.now() - startTime;
          console.log(`✅ Supabase ${operation.operationType} operation completed in ${duration}ms`);
          
          return result;
          
        } catch (error) {
          // Log failed operation
          const duration = Date.now() - startTime;
          console.error(`❌ Supabase ${operation.operationType} operation failed after ${duration}ms:`, error);
          
          // Audit log for HIPAA compliance
          await auditLogger.logDatabaseError({
            operation: operation.operationType,
            tableName: operation.tableName,
            error: error instanceof Error ? error.message : 'Unknown error',
            duration,
            timestamp: new Date().toISOString()
          });
          
          throw error;
        }
      }, operation.emergencyContext);
      
    } catch (error) {
      // Handle circuit breaker open state
      if (error instanceof CircuitBreakerOpenError) {
        console.warn(`⚠️ Supabase circuit breaker is open, attempting fallback for ${operation.operationType}`);
        
        // Try fallback for read operations
        if (operation.operationType === 'read' && operation.fallbackData !== undefined) {
          console.log(`🔄 Using fallback data for ${operation.operationType} operation`);
          return operation.fallbackData;
        }
        
        // For write operations or when no fallback is available
        if (operation.emergencyContext) {
          throw new Error('Emergency operation failed: Database circuit breaker is open and no fallback available');
        } else {
          throw new Error('Database temporarily unavailable. Please try again later.');
        }
      }
      
      throw error;
    }
  }

  /**
   * Setup fallback mechanisms
   */
  private setupFallbacks(): void {
    this.circuitBreaker.setFallback(async () => {
      console.log('🔄 Supabase fallback mechanism triggered');
      
      // Return empty result for fallback
      // In a real implementation, this could use a cache or backup database
      return null;
    });
  }

  /**
   * Wrapper methods for common Supabase operations
   */

  /**
   * Select operation with circuit breaker protection
   */
  async select<T>(
    tableName: string,
    query: any,
    fallbackData?: T[],
    emergencyContext?: boolean
  ): Promise<T[]> {
    return this.execute({
      operation: async () => {
        const { data, error } = await query;
        if (error) throw error;
        return data || [];
      },
      operationType: 'read',
      tableName,
      emergencyContext,
      fallbackData: fallbackData || []
    });
  }

  /**
   * Insert operation with circuit breaker protection
   */
  async insert<T>(
    tableName: string,
    data: any,
    emergencyContext?: boolean
  ): Promise<T[]> {
    return this.execute({
      operation: async () => {
        const { data: result, error } = await supabase
          .from(tableName)
          .insert(data)
          .select();
        if (error) throw error;
        return result || [];
      },
      operationType: 'write',
      tableName,
      emergencyContext
    });
  }

  /**
   * Update operation with circuit breaker protection
   */
  async update<T>(
    tableName: string,
    updateData: any,
    filter: any,
    emergencyContext?: boolean
  ): Promise<T[]> {
    return this.execute({
      operation: async () => {
        const { data, error } = await supabase
          .from(tableName)
          .update(updateData)
          .match(filter)
          .select();
        if (error) throw error;
        return data || [];
      },
      operationType: 'write',
      tableName,
      emergencyContext
    });
  }

  /**
   * Delete operation with circuit breaker protection
   */
  async delete<T>(
    tableName: string,
    filter: any,
    emergencyContext?: boolean
  ): Promise<T[]> {
    return this.execute({
      operation: async () => {
        const { data, error } = await supabase
          .from(tableName)
          .delete()
          .match(filter)
          .select();
        if (error) throw error;
        return data || [];
      },
      operationType: 'write',
      tableName,
      emergencyContext
    });
  }

  /**
   * RPC operation with circuit breaker protection
   */
  async rpc<T>(
    functionName: string,
    params?: any,
    fallbackData?: T,
    emergencyContext?: boolean
  ): Promise<T> {
    return this.execute({
      operation: async () => {
        const { data, error } = await supabase.rpc(functionName, params);
        if (error) throw error;
        return data;
      },
      operationType: 'rpc',
      tableName: `rpc:${functionName}`,
      emergencyContext,
      fallbackData
    });
  }

  /**
   * Storage operation with circuit breaker protection
   */
  async storageUpload(
    bucket: string,
    path: string,
    file: File | Blob,
    emergencyContext?: boolean
  ): Promise<any> {
    return this.execute({
      operation: async () => {
        const { data, error } = await supabase.storage
          .from(bucket)
          .upload(path, file);
        if (error) throw error;
        return data;
      },
      operationType: 'write',
      tableName: `storage:${bucket}`,
      emergencyContext
    });
  }

  /**
   * Storage download with circuit breaker protection
   */
  async storageDownload(
    bucket: string,
    path: string,
    emergencyContext?: boolean
  ): Promise<Blob> {
    return this.execute({
      operation: async () => {
        const { data, error } = await supabase.storage
          .from(bucket)
          .download(path);
        if (error) throw error;
        return data;
      },
      operationType: 'read',
      tableName: `storage:${bucket}`,
      emergencyContext
    });
  }

  /**
   * Get circuit breaker metrics
   */
  getMetrics() {
    return this.circuitBreaker.getMetrics();
  }

  /**
   * Reset circuit breaker (for testing/recovery)
   */
  reset(): void {
    this.circuitBreaker.reset();
    console.log('🔄 Supabase circuit breaker reset');
  }

  /**
   * Force circuit breaker state (for testing/emergency)
   */
  forceState(state: 'closed' | 'open' | 'half-open'): void {
    this.circuitBreaker.forceState(state);
  }
}

// Export singleton instance
export const supabaseCircuitBreaker = new SupabaseCircuitBreaker();
export default supabaseCircuitBreaker;
