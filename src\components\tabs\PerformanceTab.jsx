/**
 * Performance Tab - Monitor and analyze PWA performance metrics
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import Button from '../ui/Button';
import { BarChart3, TrendingUp, Download, Clock, Zap, RefreshCw } from 'lucide-react';

export const PerformanceTab = ({ performanceData }) => {
  const [metrics, setMetrics] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (performanceData) {
      setMetrics(performanceData);
    }
  }, [performanceData]);

  const refreshMetrics = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const formatBytes = (bytes) => {
    if (!bytes) return 'N/A';
    return `${(bytes / 1024).toFixed(1)}KB`;
  };

  const formatTime = (ms) => {
    if (!ms) return 'N/A';
    return `${Math.round(ms)}ms`;
  };

  const getPerformanceScore = (loadTime) => {
    if (!loadTime) return 'unknown';
    if (loadTime < 1000) return 'excellent';
    if (loadTime < 2500) return 'good';
    if (loadTime < 4000) return 'fair';
    return 'poor';
  };

  const getScoreColor = (score) => {
    switch (score) {
      case 'excellent': return 'success';
      case 'good': return 'info';
      case 'fair': return 'warning';
      case 'poor': return 'error';
      default: return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      {/* Performance Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Performance Score
            </div>
            <Button
              onClick={refreshMetrics}
              loading={isRefreshing}
              variant="secondary"
              size="sm"
              iconName="RefreshCw"
            >
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {metrics?.loadTime ? (
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">
                <Badge 
                  variant={getScoreColor(getPerformanceScore(metrics.loadTime))}
                  className="text-2xl px-4 py-2"
                >
                  {getPerformanceScore(metrics.loadTime).toUpperCase()}
                </Badge>
              </div>
              <p className="text-gray-600">
                Based on load time of {formatTime(metrics.loadTime)}
              </p>
            </div>
          ) : (
            <p className="text-gray-500 text-center">Performance data not available</p>
          )}
        </CardContent>
      </Card>

      {/* Core Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Download className="h-8 w-8 text-blue-500" />
              <div>
                <div className="text-sm text-gray-600">Bundle Size</div>
                <div className="text-xl font-bold">
                  {formatBytes(metrics?.bundleSize)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Clock className="h-8 w-8 text-green-500" />
              <div>
                <div className="text-sm text-gray-600">Load Time</div>
                <div className="text-xl font-bold">
                  {formatTime(metrics?.loadTime)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-purple-500" />
              <div>
                <div className="text-sm text-gray-600">Cache Hit Rate</div>
                <div className="text-xl font-bold">
                  {metrics?.cacheHitRate ? `${Math.round(metrics.cacheHitRate * 100)}%` : 'N/A'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Zap className="h-8 w-8 text-orange-500" />
              <div>
                <div className="text-sm text-gray-600">Sync Events</div>
                <div className="text-xl font-bold">
                  {metrics?.syncEvents || 0}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          {metrics ? (
            <div className="space-y-4">
              {/* Bundle Analysis */}
              {metrics.bundleInfo && (
                <div>
                  <h4 className="font-medium mb-2">Bundle Analysis</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Total Resources:</span>
                      <div className="font-mono">{metrics.bundleInfo.totalResources}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Transfer Size:</span>
                      <div className="font-mono">{formatBytes(metrics.bundleInfo.totalTransferSize)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">JS Files:</span>
                      <div className="font-mono">{metrics.bundleInfo.jsFiles?.length || 0}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">CSS Files:</span>
                      <div className="font-mono">{metrics.bundleInfo.cssFiles?.length || 0}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* PWA Metrics */}
              {metrics.pwaMetrics && (
                <div>
                  <h4 className="font-medium mb-2">PWA Specific Metrics</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">SW Install Time:</span>
                      <div className="font-mono">{formatTime(metrics.pwaMetrics.serviceWorkerInstallTime)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Background Syncs:</span>
                      <div className="font-mono">{metrics.pwaMetrics.backgroundSyncEvents || 0}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Voice Chunks Synced:</span>
                      <div className="font-mono">{metrics.pwaMetrics.voiceChunksSynced || 0}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Recommendations */}
              <div>
                <h4 className="font-medium mb-2">Optimization Recommendations</h4>
                <div className="space-y-2 text-sm">
                  {metrics.loadTime > 3000 && (
                    <div className="p-2 bg-yellow-50 border border-yellow-200 rounded">
                      <span className="text-yellow-800">⚠️ Consider code splitting to reduce initial bundle size</span>
                    </div>
                  )}
                  {metrics.cacheHitRate < 0.8 && (
                    <div className="p-2 bg-blue-50 border border-blue-200 rounded">
                      <span className="text-blue-800">💡 Improve caching strategy for better performance</span>
                    </div>
                  )}
                  {(!metrics.pwaMetrics?.serviceWorkerInstallTime || metrics.pwaMetrics.serviceWorkerInstallTime > 2000) && (
                    <div className="p-2 bg-red-50 border border-red-200 rounded">
                      <span className="text-red-800">🔧 Service Worker installation is slow or failed</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">
              No performance data available. Metrics will be collected as you use the app.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
