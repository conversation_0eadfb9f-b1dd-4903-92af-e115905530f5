/**
 * COMPREHENSIVE UNIT TESTS FOR AUDIT LOGGER
 * 
 * This test suite provides comprehensive coverage for the audit logger
 * with focus on:
 * - HIPAA-compliant audit logging
 * - Tamper-proof log storage
 * - Medical data access tracking
 * - Emergency access logging
 * - Security event monitoring
 * - Performance optimization
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - All medical data access must be logged
 * - Audit logs must be tamper-proof
 * - Emergency access must be tracked
 * - Security events must be monitored
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import auditLogger from '../../utils/auditLogger';

// Mock Supabase client
vi.mock('../../utils/supabaseClient', () => ({
  supabase: global.testHelpers.createMockSupabaseClient()
}));

describe('AuditLogger', () => {
  let mockSupabase;

  beforeEach(() => {
    vi.clearAllMocks();
    mockSupabase = global.testHelpers.createMockSupabaseClient();
    
    // Mock successful audit log insertion
    mockSupabase.from().insert().mockResolvedValue({
      data: [global.mockAuditLog],
      error: null
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Medical Data Access Logging', () => {
    describe('logMedicalDataAccess', () => {
      it('should log successful medical data access', async () => {
        // Arrange
        const action = 'read';
        const resourceType = 'medical_condition';
        const resourceId = global.mockMedicalData.condition.id;
        const metadata = {
          operation: 'get_user_conditions',
          user_id: global.mockUser.id,
          source: 'online'
        };

        // Act
        const result = await auditLogger.logMedicalDataAccess(
          action, 
          resourceType, 
          resourceId, 
          metadata
        );

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from).toHaveBeenCalledWith('audit_logs');
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            action,
            resource_type: resourceType,
            resource_id: resourceId,
            success: true,
            metadata: expect.objectContaining(metadata),
            timestamp: expect.any(String),
            ip_address: expect.any(String),
            user_agent: expect.any(String)
          })
        );
      });

      it('should log failed medical data access attempts', async () => {
        // Arrange
        const action = 'read';
        const resourceType = 'medical_condition';
        const resourceId = global.mockMedicalData.condition.id;
        const metadata = {
          operation: 'get_user_conditions',
          error: 'Access denied',
          success: false
        };

        // Act
        const result = await auditLogger.logMedicalDataAccess(
          action, 
          resourceType, 
          resourceId, 
          metadata
        );

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            success: false,
            metadata: expect.objectContaining({
              error: 'Access denied'
            })
          })
        );
      });

      it('should include user context in audit logs', async () => {
        // Arrange
        const action = 'update';
        const resourceType = 'medication';
        const resourceId = global.mockMedicalData.medication.id;
        const metadata = {
          user_id: global.mockUser.id,
          user_role: 'patient'
        };

        // Act
        await auditLogger.logMedicalDataAccess(action, resourceType, resourceId, metadata);

        // Assert
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            user_id: global.mockUser.id,
            metadata: expect.objectContaining({
              user_role: 'patient'
            })
          })
        );
      });

      it('should handle audit logging failures gracefully', async () => {
        // Arrange
        mockSupabase.from().insert.mockRejectedValue(new Error('Database error'));
        const action = 'read';
        const resourceType = 'medical_condition';
        const resourceId = global.mockMedicalData.condition.id;

        // Act
        const result = await auditLogger.logMedicalDataAccess(
          action, 
          resourceType, 
          resourceId, 
          {}
        );

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('Database error');
      });
    });

    describe('logConditionAccess', () => {
      it('should log condition-specific access with medical context', async () => {
        // Arrange
        const action = 'create';
        const conditionId = global.mockMedicalData.condition.id;
        const success = true;
        const metadata = {
          condition_name: 'Hypertension',
          severity: 'moderate',
          is_emergency: false
        };

        // Act
        const result = await auditLogger.logConditionAccess(action, conditionId, success, metadata);

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            action,
            resource_type: 'medical_condition',
            resource_id: conditionId,
            success,
            metadata: expect.objectContaining({
              condition_name: 'Hypertension',
              medical_context: true
            })
          })
        );
      });

      it('should flag emergency condition access', async () => {
        // Arrange
        const action = 'read';
        const emergencyConditionId = global.mockEmergencyData.condition.id;
        const metadata = {
          condition_name: 'Chest Pain',
          is_emergency: true,
          severity: 'critical'
        };

        // Act
        await auditLogger.logConditionAccess(action, emergencyConditionId, true, metadata);

        // Assert
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              is_emergency: true,
              priority: 'critical',
              alert_required: true
            })
          })
        );
      });
    });

    describe('logMedicationAccess', () => {
      it('should log medication access with dosage information', async () => {
        // Arrange
        const action = 'update';
        const medicationId = global.mockMedicalData.medication.id;
        const metadata = {
          medication_name: 'Lisinopril',
          dosage: '10mg',
          frequency: 'once daily'
        };

        // Act
        const result = await auditLogger.logMedicationAccess(action, medicationId, true, metadata);

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            resource_type: 'medication',
            metadata: expect.objectContaining({
              medication_name: 'Lisinopril',
              dosage_info: true
            })
          })
        );
      });

      it('should flag controlled substance access', async () => {
        // Arrange
        const action = 'read';
        const medicationId = 'controlled-medication-id';
        const metadata = {
          medication_name: 'Oxycodone',
          is_controlled_substance: true,
          dea_schedule: 'II'
        };

        // Act
        await auditLogger.logMedicationAccess(action, medicationId, true, metadata);

        // Assert
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              controlled_substance: true,
              requires_monitoring: true,
              dea_schedule: 'II'
            })
          })
        );
      });
    });
  });

  describe('Emergency Access Logging', () => {
    describe('logEmergencyAccess', () => {
      it('should log emergency access requests with justification', async () => {
        // Arrange
        const userId = global.mockProvider.id;
        const patientId = global.mockUser.id;
        const justification = 'Patient experiencing chest pain, need immediate access to medical history';
        const metadata = {
          emergency_type: 'cardiac_event',
          location: 'Emergency Department',
          urgency: 'critical'
        };

        // Act
        const result = await auditLogger.logEmergencyAccess(
          userId, 
          patientId, 
          justification, 
          metadata
        );

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            action: 'emergency_access',
            resource_type: 'patient_data',
            user_id: userId,
            metadata: expect.objectContaining({
              patient_id: patientId,
              justification,
              emergency_type: 'cardiac_event',
              requires_review: true,
              alert_level: 'high'
            })
          })
        );
      });

      it('should log emergency access approval/denial', async () => {
        // Arrange
        const requestId = 'emergency-request-id-12345';
        const approved = true;
        const reviewerId = global.mockAdmin.id;
        const metadata = {
          review_time: new Date().toISOString(),
          review_notes: 'Approved due to critical patient condition'
        };

        // Act
        const result = await auditLogger.logEmergencyAccessReview(
          requestId, 
          approved, 
          reviewerId, 
          metadata
        );

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            action: 'emergency_access_review',
            metadata: expect.objectContaining({
              request_id: requestId,
              approved,
              reviewer_id: reviewerId,
              compliance_check: true
            })
          })
        );
      });
    });
  });

  describe('Security Event Logging', () => {
    describe('logSecurityEvent', () => {
      it('should log authentication failures', async () => {
        // Arrange
        const eventType = 'authentication_failure';
        const severity = 'medium';
        const metadata = {
          attempted_email: '<EMAIL>',
          failure_reason: 'invalid_credentials',
          attempt_count: 3
        };

        // Act
        const result = await auditLogger.logSecurityEvent(eventType, severity, metadata);

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            action: 'security_event',
            resource_type: 'authentication',
            metadata: expect.objectContaining({
              event_type: eventType,
              severity,
              security_alert: true
            })
          })
        );
      });

      it('should log suspicious data access patterns', async () => {
        // Arrange
        const eventType = 'suspicious_access_pattern';
        const severity = 'high';
        const metadata = {
          user_id: 'suspicious-user-id',
          access_count: 50,
          time_window: '5 minutes',
          resources_accessed: ['patient_data', 'medical_records']
        };

        // Act
        const result = await auditLogger.logSecurityEvent(eventType, severity, metadata);

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              requires_investigation: true,
              alert_level: 'high',
              automated_response: 'rate_limit_applied'
            })
          })
        );
      });

      it('should log data breach attempts', async () => {
        // Arrange
        const eventType = 'data_breach_attempt';
        const severity = 'critical';
        const metadata = {
          attack_type: 'sql_injection',
          blocked: true,
          attack_vector: 'medical_search_endpoint'
        };

        // Act
        const result = await auditLogger.logSecurityEvent(eventType, severity, metadata);

        // Assert
        expect(result.success).toBe(true);
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              immediate_alert: true,
              law_enforcement_notification: true,
              system_lockdown: false
            })
          })
        );
      });
    });
  });

  describe('Audit Log Integrity', () => {
    it('should generate tamper-proof log entries', async () => {
      // Arrange
      const action = 'read';
      const resourceType = 'medical_condition';
      const resourceId = global.mockMedicalData.condition.id;

      // Act
      await auditLogger.logMedicalDataAccess(action, resourceType, resourceId, {});

      // Assert
      expect(mockSupabase.from().insert).toHaveBeenCalledWith(
        expect.objectContaining({
          integrity_hash: expect.any(String),
          sequence_number: expect.any(Number),
          previous_hash: expect.any(String)
        })
      );
    });

    it('should detect log tampering attempts', async () => {
      // Arrange
      const logId = 'audit-log-id-12345';
      const tamperedData = {
        action: 'delete', // Changed from original 'read'
        timestamp: '2024-01-01T00:00:00.000Z'
      };

      // Act
      const result = await auditLogger.verifyLogIntegrity(logId, tamperedData);

      // Assert
      expect(result.valid).toBe(false);
      expect(result.tampering_detected).toBe(true);
      expect(result.alert_generated).toBe(true);
    });

    it('should maintain audit log chain integrity', async () => {
      // Arrange
      const logEntries = [
        { action: 'read', resource_type: 'condition' },
        { action: 'update', resource_type: 'medication' },
        { action: 'create', resource_type: 'symptom' }
      ];

      // Act
      for (const entry of logEntries) {
        await auditLogger.logMedicalDataAccess(
          entry.action, 
          entry.resource_type, 
          'test-id', 
          {}
        );
      }

      // Assert
      const calls = mockSupabase.from().insert.mock.calls;
      expect(calls.length).toBe(3);
      
      // Verify chain integrity
      for (let i = 1; i < calls.length; i++) {
        const currentEntry = calls[i][0];
        const previousEntry = calls[i - 1][0];
        expect(currentEntry.previous_hash).toBe(previousEntry.integrity_hash);
      }
    });
  });

  describe('Performance and Compliance', () => {
    it('should log audit entries within acceptable time limits', async () => {
      // Arrange
      const startTime = performance.now();
      const action = 'read';
      const resourceType = 'medical_condition';
      const resourceId = global.mockMedicalData.condition.id;

      // Act
      await auditLogger.logMedicalDataAccess(action, resourceType, resourceId, {});
      const endTime = performance.now();

      // Assert
      expect(endTime - startTime).toBeLessThan(500); // Should complete within 500ms
    });

    it('should handle high-volume audit logging', async () => {
      // Arrange
      const logPromises = Array(100).fill().map((_, i) => 
        auditLogger.logMedicalDataAccess(
          'read', 
          'medical_condition', 
          `condition-${i}`, 
          { batch_test: true }
        )
      );

      // Act
      const results = await Promise.all(logPromises);

      // Assert
      expect(results.length).toBe(100);
      expect(results.every(result => result.success)).toBe(true);
    });

    it('should comply with HIPAA audit requirements', async () => {
      // Arrange
      const action = 'read';
      const resourceType = 'medical_condition';
      const resourceId = global.mockMedicalData.condition.id;
      const metadata = {
        user_id: global.mockUser.id,
        patient_id: global.mockUser.id
      };

      // Act
      await auditLogger.logMedicalDataAccess(action, resourceType, resourceId, metadata);

      // Assert
      expect(mockSupabase.from().insert).toHaveBeenCalledWith(
        expect.objectContaining({
          // HIPAA required fields
          timestamp: expect.any(String),
          user_id: expect.any(String),
          action: expect.any(String),
          resource_type: expect.any(String),
          resource_id: expect.any(String),
          success: expect.any(Boolean),
          ip_address: expect.any(String),
          user_agent: expect.any(String),
          // Additional compliance fields
          hipaa_compliant: true,
          retention_period: expect.any(String),
          access_purpose: expect.any(String)
        })
      );
    });
  });
});
