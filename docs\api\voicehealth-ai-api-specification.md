# VoiceHealth AI - API Specification

## Overview

The VoiceHealth AI API provides comprehensive healthcare AI services with cultural adaptation, emergency protocols, and HIPAA-compliant data handling for African healthcare markets.

**Base URL**: `https://api.voicehealth.ai`  
**Version**: `v1`  
**Authentication**: JWT <PERSON>er Token  
**Content-Type**: `application/json`

## Authentication

### POST /auth/login
Authenticate user and obtain access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "mfaCode": "123456",
  "emergencyOverride": false,
  "clientInfo": {
    "userAgent": "VoiceHealth App/1.0",
    "ipAddress": "*************",
    "deviceId": "device-123"
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "role": "provider",
    "profile": {
      "firstName": "<PERSON>. <PERSON>",
      "lastName": "Johnson",
      "country": "GH",
      "language": "en",
      "timezone": "GMT"
    },
    "permissions": [
      {
        "resource": "patient_data",
        "actions": ["read", "write"]
      }
    ]
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_here",
  "expiresIn": 28800
}
```

**Emergency Authentication (< 50ms response time):**
```json
{
  "email": "<EMAIL>",
  "password": "emergency",
  "emergencyOverride": true
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

## Clinical Documentation

### POST /clinical/voice-to-note
Convert voice transcription to structured clinical note.

**Headers:**
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "audioTranscription": "Patient is a 45-year-old female presenting with headache and fever for 3 days...",
  "patientId": "patient-123",
  "providerId": "provider-456",
  "noteType": "soap",
  "culturalContext": {
    "cultureCode": "akan",
    "languagePreference": "en",
    "familyInvolvementLevel": "high",
    "traditionalMedicineOpenness": 4,
    "country": "GH"
  },
  "timestamp": "2025-01-06T10:30:00Z"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "documentation": {
    "clinicalNote": {
      "chiefComplaint": "Headache and fever for 3 days",
      "historyOfPresentIllness": "Patient reports severe headache...",
      "pastMedicalHistory": [
        {
          "condition": "Hypertension",
          "diagnosedDate": "2020-01-01T00:00:00Z",
          "status": "active"
        }
      ],
      "physicalExamination": {
        "vitalSigns": {
          "bloodPressure": { "systolic": 150, "diastolic": 90 },
          "temperature": 38.5,
          "heartRate": 85
        }
      },
      "assessment": {
        "primaryDiagnosis": {
          "condition": "Tension headache",
          "confidence": "probable",
          "evidenceLevel": "C"
        }
      },
      "plan": {
        "medications": [
          {
            "medication": "Ibuprofen",
            "dosage": "400mg",
            "frequency": "every 6 hours as needed"
          }
        ]
      }
    },
    "culturalAdaptations": [
      {
        "aspect": "family_involvement",
        "adaptation": "Include family in treatment decisions",
        "rationale": "High family involvement preference in Akan culture"
      }
    ],
    "suggestedCodes": {
      "icd10": [
        {
          "code": "G44.2",
          "description": "Tension-type headache",
          "confidence": 0.85
        }
      ],
      "cpt": [
        {
          "code": "99213",
          "description": "Office visit, established patient",
          "confidence": 0.90
        }
      ]
    },
    "qualityMetrics": {
      "completeness": 92,
      "accuracy": 88,
      "clarity": 90,
      "culturalSensitivity": 95,
      "complianceScore": 94
    }
  },
  "timestamp": "2025-01-06T10:30:15Z"
}
```

### POST /clinical/assess-quality
Assess quality of clinical documentation.

**Request Body:**
```json
{
  "clinicalNote": { /* Clinical note object */ },
  "noteType": "soap",
  "culturalContext": { /* Cultural context object */ }
}
```

## Risk Stratification

### POST /risk/assess
Perform advanced risk stratification with cultural factors.

**Request Body:**
```json
{
  "patientId": "patient-123",
  "demographics": {
    "age": 45,
    "gender": "female",
    "country": "GH",
    "ethnicity": "akan"
  },
  "medicalHistory": ["hypertension", "diabetes"],
  "currentSymptoms": ["headache", "fever", "nausea"],
  "behavioralFactors": {
    "smokingStatus": "never",
    "physicalActivity": "moderate",
    "dietQuality": "good",
    "adherenceToMedications": "good"
  },
  "socioeconomicFactors": {
    "income": "middle",
    "education": "secondary",
    "healthcareAccess": "moderate"
  },
  "culturalFactors": {
    "cultureCode": "akan",
    "traditionalMedicineOpenness": 4,
    "familyInvolvementLevel": "high"
  },
  "environmentalFactors": ["urban_environment"]
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "riskAssessment": {
    "overallRiskScore": 65,
    "riskCategory": "moderate",
    "conditionSpecificRisks": [
      {
        "condition": "hypertension",
        "riskScore": 45,
        "riskFactors": ["elevated_blood_pressure", "medication_adherence"],
        "culturalConsiderations": ["traditional_medicine_interactions"]
      }
    ],
    "regionalRiskFactors": [
      {
        "factor": "healthcare_access",
        "impact": "moderate",
        "description": "Healthcare access in Greater Accra region"
      }
    ],
    "modifiableRiskFactors": [
      {
        "factor": "blood_pressure_control",
        "currentStatus": "suboptimal",
        "improvementPotential": "high",
        "culturalBarriers": ["traditional_medicine_preferences"]
      }
    ],
    "nonModifiableRiskFactors": [
      {
        "factor": "age",
        "impact": "medium",
        "description": "Age-related cardiovascular risk"
      }
    ],
    "predictiveAnalytics": {
      "hospitalizationRisk": {
        "probability": 0.15,
        "timeframe": "6_months",
        "confidence": 0.82,
        "culturalFactors": ["family_support_reduces_risk"]
      },
      "diseaseProgression": {
        "likelihood": "moderate",
        "timeframe": "12_months",
        "factors": ["uncontrolled_hypertension"],
        "culturalMitigators": ["strong_family_support"]
      }
    },
    "culturalConsiderations": [
      {
        "factor": "traditional_medicine_integration",
        "recommendation": "Discuss traditional remedies and ensure compatibility",
        "impact": "moderate",
        "implementation": "Include traditional medicine history in assessment"
      }
    ],
    "recommendations": [
      {
        "category": "immediate",
        "action": "Blood pressure monitoring",
        "priority": "high",
        "culturalAdaptation": "Discuss with family members"
      }
    ],
    "urgentActions": [
      {
        "action": "Emergency department evaluation if symptoms worsen",
        "timeframe": "immediate",
        "culturalNote": "Family should accompany patient"
      }
    ],
    "assessmentMetadata": {
      "assessmentDate": "2025-01-06T10:35:00Z",
      "assessmentVersion": "3.0",
      "dataCompleteness": 85,
      "confidenceLevel": 0.8,
      "culturalAdaptationScore": 92,
      "processingTime": 2100
    }
  },
  "timestamp": "2025-01-06T10:35:00Z"
}
```

## Cultural Validation

### POST /cultural/validate
Validate cultural appropriateness of medical content.

**Request Body:**
```json
{
  "content": "Please take your medication as prescribed and follow up with your family doctor.",
  "contentType": "medical_advice",
  "targetCulture": "akan",
  "language": "en",
  "context": {
    "cultureCode": "akan",
    "country": "GH",
    "ethnicGroup": "akan",
    "religiousContext": ["christian", "traditional"],
    "familyStructure": "extended"
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "validation": {
    "overallScore": 88,
    "culturalSensitivity": {
      "score": 92,
      "issues": [],
      "recommendations": [
        "Consider mentioning family involvement in care decisions"
      ]
    },
    "languageAppropriateness": {
      "score": 95,
      "issues": [],
      "improvements": [
        {
          "aspect": "clarity",
          "suggestion": "Use simpler medical terminology",
          "priority": "medium"
        }
      ]
    },
    "culturalAccuracy": {
      "score": 85,
      "inaccuracies": [],
      "corrections": []
    },
    "readingLevel": {
      "level": "grade_8",
      "appropriate": true,
      "targetLevel": "grade_6_to_10"
    },
    "biasDetection": {
      "biasDetected": false,
      "biasTypes": [],
      "confidence": 0.95
    },
    "culturalReferences": [
      {
        "reference": "family doctor",
        "accuracy": "high",
        "culturalRelevance": "appropriate"
      }
    ],
    "recommendations": [
      {
        "aspect": "family_involvement",
        "suggestion": "Add: 'Discuss with your family about the treatment plan'",
        "priority": "medium",
        "impact": "positive"
      }
    ],
    "flaggedContent": [],
    "validationStatus": "approved",
    "processingTime": 800
  },
  "timestamp": "2025-01-06T10:40:00Z"
}
```

## Emergency Protocols

### POST /emergency/activate
Activate emergency protocol (< 2 second response time).

**Request Body:**
```json
{
  "emergencyType": "medical_emergency",
  "patientId": "patient-123",
  "location": {
    "country": "GH",
    "region": "Greater Accra",
    "coordinates": {
      "latitude": 5.6037,
      "longitude": -0.1870
    }
  },
  "severity": "critical",
  "symptoms": ["chest_pain", "difficulty_breathing"],
  "culturalContext": {
    "cultureCode": "akan",
    "familyContactRequired": true,
    "traditionalHealerConsultation": false
  }
}
```

**Response (200 OK - < 2000ms):**
```json
{
  "success": true,
  "emergencyResponse": {
    "responseId": "emergency-123",
    "activatedAt": "2025-01-06T10:45:00Z",
    "estimatedResponseTime": 1850,
    "protocols": [
      {
        "protocol": "immediate_medical_attention",
        "actions": [
          "Contact emergency services",
          "Notify family members",
          "Prepare for hospital transport"
        ],
        "culturalAdaptations": [
          "Include family in emergency decisions",
          "Respect cultural preferences for care"
        ]
      }
    ],
    "notifications": {
      "emergencyServices": {
        "contacted": true,
        "estimatedArrival": "15 minutes"
      },
      "familyContacts": {
        "notified": true,
        "contacts": ["family-contact-1", "family-contact-2"]
      }
    },
    "followUpActions": [
      "Monitor patient status",
      "Coordinate with emergency responders",
      "Document emergency response"
    ]
  },
  "responseTime": 1850
}
```

## Health Monitoring

### GET /health
System health check endpoint.

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-06T10:50:00Z",
  "version": "1.0.0",
  "environment": "production",
  "components": {
    "aiOrchestrator": {
      "status": "healthy",
      "responseTime": 150,
      "lastCheck": "2025-01-06T10:49:45Z"
    },
    "authentication": {
      "status": "healthy",
      "responseTime": 45,
      "lastCheck": "2025-01-06T10:49:45Z"
    },
    "database": {
      "status": "healthy",
      "responseTime": 120,
      "lastCheck": "2025-01-06T10:49:45Z"
    },
    "encryption": {
      "status": "healthy",
      "keyRotationStatus": "current",
      "lastCheck": "2025-01-06T10:49:45Z"
    }
  },
  "performance": {
    "emergencyResponseTime": 1.8,
    "authenticationTime": 0.045,
    "apiResponseTime": 0.8
  },
  "compliance": {
    "hipaa": "compliant",
    "regional": "compliant",
    "lastAudit": "2025-01-01T00:00:00Z"
  }
}
```

### GET /health/emergency
Emergency protocol health check (< 50ms response time).

**Response (200 OK - < 50ms):**
```json
{
  "status": "ready",
  "responseTime": 35,
  "emergencyProtocols": "active",
  "lastValidation": "2025-01-06T10:45:00Z"
}
```

## Regional Configuration

### GET /config/regional/{country}
Get regional configuration for specific country.

**Parameters:**
- `country`: Country code (GH, KE, NG, ZA, ET)

**Response (200 OK):**
```json
{
  "countryCode": "GH",
  "countryName": "Ghana",
  "languages": [
    {
      "code": "en",
      "name": "English",
      "primary": true,
      "supportLevel": "full"
    },
    {
      "code": "tw",
      "name": "Twi",
      "primary": false,
      "supportLevel": "full"
    }
  ],
  "healthcareSystem": {
    "emergencyNumber": "193",
    "responseTime": 45,
    "traditionalMedicine": {
      "recognition": "informal",
      "integration": "parallel"
    }
  },
  "cultural": {
    "primaryCultures": ["akan", "mole-dagbon", "ewe"],
    "communicationStyles": ["indirect", "respectful", "hierarchical"],
    "familyStructures": ["extended", "nuclear", "community"]
  },
  "compliance": {
    "dataProtectionLaw": "Data Protection Act 2012",
    "requiredApprovals": [
      {
        "authority": "Ghana Health Service",
        "approvalType": "Digital Health Platform Registration"
      }
    ]
  }
}
```

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "patientId",
      "issue": "Required field missing"
    },
    "timestamp": "2025-01-06T10:55:00Z",
    "requestId": "req-123"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AUTHENTICATION_REQUIRED` | 401 | Valid authentication token required |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `RESOURCE_NOT_FOUND` | 404 | Requested resource not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Rate limit exceeded |
| `EMERGENCY_PROTOCOL_TIMEOUT` | 500 | Emergency response time exceeded |
| `CULTURAL_VALIDATION_FAILED` | 422 | Cultural appropriateness validation failed |
| `COMPLIANCE_VIOLATION` | 422 | Request violates compliance requirements |

## Rate Limiting

- **Standard endpoints**: 1000 requests per minute
- **Emergency endpoints**: No rate limiting (emergency bypass)
- **Authentication endpoints**: 100 requests per minute
- **Cultural validation**: 500 requests per minute

Rate limit headers:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1641484800
```

## Security Headers

All responses include security headers:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

## Compliance and Audit

### HIPAA Compliance
- All PHI data is encrypted using AES-256-GCM
- Audit logs are maintained for all data access
- Business Associate Agreements required for third-party integrations

### Regional Compliance
- Data localization requirements met for each region
- Regional data protection laws compliance validated
- Cultural sensitivity requirements enforced

### Audit Logging
All API requests are logged with:
- Request timestamp and duration
- User identification and permissions
- Data accessed or modified
- Cultural adaptations applied
- Emergency protocol activations

---

**API Version**: 1.0.0  
**Last Updated**: 2025-01-06  
**Support**: <EMAIL>  
**Documentation**: https://docs.voicehealth.ai
