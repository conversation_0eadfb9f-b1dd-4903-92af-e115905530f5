import React from 'react';
import Icon from '../../../components/AppIcon';

const WelcomeHeader = ({ selectedLanguage }) => {
  const getLocalizedText = (key) => {
    const texts = {
      title: {
        en: 'Welcome to Your Personal Health Assistant',
        tw: '<PERSON>k<PERSON><PERSON> wɔ wo ankasa akwahosan boafoɔ ho',
        yo: 'Kaabo si <PERSON>o Ilera Ti Ara Ẹ',
        sw: '<PERSON><PERSON><PERSON> kwenye Msaidizi Wako wa Afya',
        af: 'Welkom by jou <PERSON>soonlike Gesondheidsassistent'
      },
      subtitle: {
        en: 'Experience AI-powered healthcare consultation with multi-specialist collaboration',
        tw: 'Nya AI-powered akwahosan nhwehwɛmu ne multi-specialist adwumayɛkuo',
        yo: 'Ni iriri iwadi ilera ti AI-powered pẹlu ifowosowopo awọn amoye-pupọ',
        sw: 'Pata uzoefu wa ushauri wa huduma za afya za AI pamoja na ushirikiano wa wataalamu wengi',
        af: 'Ervaar AI-aangedrewe gesondheidsorgkonsultasie met multi-spesialis samewerking'
      },
      features: {
        voice: {
          en: 'Voice First',
          tw: '<PERSON><PERSON> <PERSON>',
          yo: 'Oh<PERSON><PERSON>kọk<PERSON>',
          sw: 'Sauti Kwanza',
          af: 'Stem Eerste'
        },
        ai: {
          en: 'AI Powered',
          tw: 'AI Tumidi',
          yo: 'AI Ti Agbara',
          sw: 'AI Imeongozwa',
          af: 'AI Aangedryf'
        },
        secure: {
          en: 'Secure',
          tw: 'Ahobammɔ',
          yo: 'Aabo',
          sw: 'Salama',
          af: 'Veilig'
        }
      }
    };

    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="space-y-8">
      {/* Logo */}
      <div className="flex justify-center">
        <div className="relative">
          <div className="w-20 h-20 bg-primary-500 rounded-3xl flex items-center justify-center shadow-elevated">
            <Icon name="Activity" size={40} color="white" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-success-500 rounded-full flex items-center justify-center shadow-minimal">
            <Icon name="Mic" size={16} color="white" />
          </div>
          {/* AI Indicator */}
          <div className="absolute -bottom-1 -left-1 w-6 h-6 bg-accent-500 rounded-full flex items-center justify-center">
            <Icon name="Brain" size={12} color="white" />
          </div>
        </div>
      </div>

      {/* Brand Name */}
      <div className="space-y-3">
        <h1 className="text-4xl font-bold text-text-primary font-heading text-center">
          VoiceHealth AI
        </h1>
        <div className="w-16 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full mx-auto"></div>
      </div>

      {/* Welcome Message */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold text-text-primary text-center leading-tight">
          {getLocalizedText('title')}
        </h2>
        <p className="text-text-secondary text-center leading-relaxed px-4">
          {getLocalizedText('subtitle')}
        </p>
      </div>

      {/* Healthcare Features */}
      <div className="grid grid-cols-3 gap-6 max-w-sm mx-auto pt-4">
        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-primary-50 rounded-xl flex items-center justify-center mx-auto">
            <Icon name="Mic" size={24} color="var(--color-primary)" />
          </div>
          <p className="text-sm font-medium text-text-secondary">
            {getLocalizedText('features').voice}
          </p>
        </div>
        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-success-50 rounded-xl flex items-center justify-center mx-auto">
            <Icon name="Brain" size={24} color="var(--color-success)" />
          </div>
          <p className="text-sm font-medium text-text-secondary">
            {getLocalizedText('features').ai}
          </p>
        </div>
        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-warning-50 rounded-xl flex items-center justify-center mx-auto">
            <Icon name="Shield" size={24} color="var(--color-warning)" />
          </div>
          <p className="text-sm font-medium text-text-secondary">
            {getLocalizedText('features').secure}
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeHeader;