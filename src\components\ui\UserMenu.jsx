import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../contexts/SimpleAuthContext';
import { useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const UserMenu = ({ isMobile = false }) => {
  const { user, userProfile, signOut } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const menuRef = useRef(null);
  
  // Safely get navigate function
  let navigate;
  try {
    navigate = useNavigate();
  } catch (error) {
    // Fallback navigation function if useNavigate fails
    navigate = (path) => {
      window.location.href = path;
    };
  }

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    if (isLoggingOut) return;

    try {
      setIsLoggingOut(true);
      setIsOpen(false);
      
      const result = await signOut();
      
      if (result?.success) {
        // Clear any cached data
        localStorage.removeItem('demo-session');
        sessionStorage.clear();
        
        // Navigate to authentication page
        navigate('/authentication-demo-access', { replace: true });
        
        // Show success message
        console.log('Logged out successfully');
        
        // Force page reload to clear any remaining state
        setTimeout(() => {
          window.location.reload();
        }, 100);
      } else {
        console.error('Logout failed:', result?.error);
        alert('Logout failed. Please try again.');
      }
    } catch (error) {
      console.error('Logout error:', error);
      alert('An error occurred during logout. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getRoleDisplayName = (role) => {
    switch (role) {
      case 'patient':
        return 'Patient';
      case 'provider':
        return 'Healthcare Provider';
      case 'admin':
        return 'Administrator';
      default:
        return 'User';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'patient':
        return 'User';
      case 'provider':
        return 'Stethoscope';
      case 'admin':
        return 'Shield';
      default:
        return 'User';
    }
  };

  const getUserDisplayName = () => {
    if (userProfile?.full_name) {
      return userProfile.full_name;
    }
    if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'User';
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'patient':
        return 'Patient';
      case 'provider':
        return 'Provider';
      case 'admin':
        return 'Admin';
      default:
        return 'User';
    }
  };

  const isDemoAccount = user.email?.includes('<EMAIL>');

  if (!user) {
    return null;
  }

  // Mobile version - simplified layout
  if (isMobile) {
    return (
      <div className="w-full">
        {/* Mobile User Info */}
        <div className="flex items-center space-x-3 px-3 py-2 mb-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-semibold ${
            userProfile?.role === 'admin' ? 'bg-gradient-to-br from-purple-400 to-purple-600' :
            userProfile?.role === 'provider' ? 'bg-gradient-to-br from-green-400 to-green-600' :
            'bg-gradient-to-br from-primary-400 to-primary-600'
          }`}>
            {userProfile?.full_name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-text-primary">
              {userProfile?.full_name || 'User'}
            </div>
            <div className="text-xs text-text-secondary flex items-center space-x-2">
              <span>{getRoleLabel(userProfile?.role)}</span>
              {isDemoAccount && (
                <span className="px-2 py-0.5 bg-warning-100 text-warning-700 text-xs rounded-full font-medium">
                  Demo
                </span>
              )}
            </div>
          </div>
        </div>
        
        {/* Mobile Logout Button */}
        <Button
          onClick={handleLogout}
          disabled={isLoggingOut}
          variant="outline"
          className="w-full justify-center"
          iconName={isLoggingOut ? "Loader2" : "LogOut"}
          iconSize={16}
        >
          {isLoggingOut ? 'Logging out...' : 'Logout'}
        </Button>
        
        {isDemoAccount && (
          <div className="mt-3 p-3 bg-info-50 border border-info-200 rounded-lg">
            <p className="text-xs text-info-700 text-center">
              💡 <strong>Demo Account:</strong> Logout to test other demo accounts
            </p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative" ref={menuRef}>
      {/* User Avatar Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold ${
          userProfile?.role === 'admin' ? 'bg-gradient-to-br from-purple-400 to-purple-600' :
          userProfile?.role === 'provider' ? 'bg-gradient-to-br from-green-400 to-green-600' :
          'bg-gradient-to-br from-primary-400 to-primary-600'
        }`}>
          {userProfile?.full_name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
        </div>
        <div className="hidden sm:block text-left">
          <div className="text-sm font-medium text-text-primary">
            {userProfile?.full_name || 'User'}
          </div>
          <div className="text-xs text-text-secondary">
            {getRoleLabel(userProfile?.role)}
          </div>
        </div>
        <Icon name="ChevronDown" size={16} className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center text-white font-medium">
                <Icon name={getRoleIcon(userProfile?.role)} size={20} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 dark:text-white truncate">
                  {getUserDisplayName()}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {user.email}
                </div>
                <div className="text-xs text-primary-600 dark:text-primary-400 font-medium">
                  {getRoleDisplayName(userProfile?.role)}
                </div>
              </div>
            </div>
          </div>

          {/* Demo Account Badge */}
          {user.email?.includes('<EMAIL>') && (
            <div className="px-4 py-2 bg-amber-50 dark:bg-amber-900/20 border-l-4 border-amber-400">
              <div className="flex items-center space-x-2">
                <Icon name="TestTube" size={16} className="text-amber-600 dark:text-amber-400" />
                <span className="text-xs font-medium text-amber-700 dark:text-amber-300">
                  Demo Account
                </span>
              </div>
            </div>
          )}

          {/* Menu Items */}
          <div className="py-2">
            <button
              onClick={() => {
                setIsOpen(false);
                navigate('/patient-profile-setup');
              }}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <Icon name="User" size={16} />
              <span>Profile Settings</span>
            </button>

            <button
              onClick={() => {
                setIsOpen(false);
                navigate('/session-dashboard-history');
              }}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <Icon name="History" size={16} />
              <span>Consultation History</span>
            </button>

            {userProfile?.role === 'admin' && (
              <button
                onClick={() => {
                  setIsOpen(false);
                  navigate('/pwa-testing');
                }}
                className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <Icon name="Settings" size={16} />
                <span>PWA Testing</span>
              </button>
            )}
          </div>

          {/* Logout Section */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Icon 
                name={isLoggingOut ? "Loader2" : "LogOut"} 
                size={16} 
                className={isLoggingOut ? "animate-spin" : ""} 
              />
              <span>{isLoggingOut ? 'Logging out...' : 'Sign Out'}</span>
            </button>
          </div>

          {/* Quick Account Switch for Demo */}
          {user.email?.includes('<EMAIL>') && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
              <div className="px-4 py-2">
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                  Switch Demo Account:
                </div>
                <div className="text-xs text-gray-400 dark:text-gray-500 space-y-1">
                  <div>• Patient: <EMAIL></div>
                  <div>• Provider: <EMAIL></div>
                  <div>• Admin: <EMAIL></div>
                  <div className="text-amber-600 dark:text-amber-400 font-medium mt-1">
                    Sign out to switch accounts
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UserMenu;
