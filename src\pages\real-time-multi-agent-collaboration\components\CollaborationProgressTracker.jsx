import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const CollaborationProgressTracker = ({ 
  currentPhase = 'initializing',
  phaseProgress = 0,
  estimatedTimeRemaining = 0,
  completedTasks = [],
  upcomingTasks = [],
  onPhaseComplete = () => {},
  className = ''
}) => {
  const [animatedProgress, setAnimatedProgress] = useState(0);

  const phases = [
    {
      id: 'initializing',
      name: 'Initialization',
      description: 'Agents connecting and sharing context',
      icon: 'Settings',
      color: 'secondary',
      estimatedDuration: 30
    },
    {
      id: 'assessment',
      name: 'Initial Assessment',
      description: 'Individual agent analysis',
      icon: 'Search',
      color: 'primary',
      estimatedDuration: 120
    },
    {
      id: 'collaboration',
      name: 'Active Collaboration',
      description: 'Inter-agent discussion and consensus building',
      icon: 'MessageSquare',
      color: 'success',
      estimatedDuration: 180
    },
    {
      id: 'synthesis',
      name: 'Recommendation Synthesis',
      description: 'Formulating comprehensive treatment plan',
      icon: 'FileText',
      color: 'warning',
      estimatedDuration: 90
    },
    {
      id: 'completed',
      name: 'Completed',
      description: 'Consultation complete with recommendations',
      icon: 'CheckCircle',
      color: 'success',
      estimatedDuration: 0
    }
  ];

  useEffect(() => {
    // Animate progress bar
    const timer = setTimeout(() => {
      setAnimatedProgress(phaseProgress);
    }, 100);
    return () => clearTimeout(timer);
  }, [phaseProgress]);

  const getCurrentPhaseIndex = () => {
    return phases.findIndex(phase => phase.id === currentPhase);
  };

  const getCurrentPhase = () => {
    return phases.find(phase => phase.id === currentPhase) || phases[0];
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    if (mins > 0) {
      return `${mins}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getOverallProgress = () => {
    const currentIndex = getCurrentPhaseIndex();
    const baseProgress = (currentIndex / (phases.length - 1)) * 100;
    const phaseContribution = (phaseProgress / 100) * (100 / (phases.length - 1));
    return Math.min(baseProgress + phaseContribution, 100);
  };

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="font-semibold text-text-primary font-heading">
            Collaboration Progress
          </h3>
          <p className="text-sm text-text-secondary font-caption">
            {getCurrentPhase().description}
          </p>
        </div>
        
        {estimatedTimeRemaining > 0 && (
          <div className="text-right">
            <div className="text-sm font-medium text-text-primary">
              {formatTime(estimatedTimeRemaining)}
            </div>
            <div className="text-xs text-text-secondary">
              remaining
            </div>
          </div>
        )}
      </div>

      {/* Overall Progress */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-text-secondary mb-2">
          <span>Overall Progress</span>
          <span>{Math.round(getOverallProgress())}%</span>
        </div>
        <div className="w-full bg-secondary-100 rounded-full h-3">
          <div 
            className="bg-gradient-to-r from-primary-500 to-success-500 h-3 rounded-full transition-all duration-1000 ease-out"
            style={{ width: `${getOverallProgress()}%` }}
          ></div>
        </div>
      </div>

      {/* Phase Timeline */}
      <div className="space-y-4 mb-6">
        {phases.map((phase, index) => {
          const isActive = phase.id === currentPhase;
          const isCompleted = getCurrentPhaseIndex() > index;
          const isUpcoming = getCurrentPhaseIndex() < index;

          return (
            <div key={phase.id} className="flex items-center space-x-4">
              {/* Phase Icon */}
              <div className={`relative flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                isCompleted 
                  ? 'bg-success-50 border-success-500' 
                  : isActive 
                    ? `bg-${phase.color}-50 border-${phase.color}-500 animate-pulse`
                    : 'bg-secondary-50 border-secondary-300'
              }`}>
                <Icon 
                  name={isCompleted ? 'Check' : phase.icon}
                  size={18}
                  color={
                    isCompleted 
                      ? 'var(--color-success)' 
                      : isActive 
                        ? `var(--color-${phase.color})`
                        : 'var(--color-text-secondary)'
                  }
                />
                
                {/* Connection Line */}
                {index < phases.length - 1 && (
                  <div className={`absolute top-10 left-1/2 w-0.5 h-6 transform -translate-x-1/2 ${
                    isCompleted ? 'bg-success-300' : 'bg-secondary-200'
                  }`}></div>
                )}
              </div>

              {/* Phase Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className={`font-medium ${
                    isActive ? 'text-text-primary' : 
                    isCompleted ? 'text-success-600': 'text-text-secondary'
                  }`}>
                    {phase.name}
                  </h4>
                  
                  {isActive && (
                    <div className="flex items-center space-x-2">
                      <div className="text-sm font-medium text-text-primary">
                        {Math.round(animatedProgress)}%
                      </div>
                      <div className="w-16 bg-secondary-100 rounded-full h-1.5">
                        <div 
                          className={`h-1.5 rounded-full transition-all duration-500 bg-${phase.color}-500`}
                          style={{ width: `${animatedProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  
                  {isCompleted && (
                    <Icon name="CheckCircle" size={16} color="var(--color-success)" />
                  )}
                </div>
                
                <p className={`text-sm mt-1 ${
                  isActive ? 'text-text-secondary' : 
                  isCompleted ? 'text-success-500': 'text-text-muted'
                }`}>
                  {phase.description}
                </p>

                {/* Phase Duration */}
                <div className="text-xs text-text-muted mt-1">
                  Est. {formatTime(phase.estimatedDuration)}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Current Tasks */}
      <div className="space-y-4">
        {/* Completed Tasks */}
        {completedTasks.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-success-600 mb-2 flex items-center space-x-2">
              <Icon name="CheckCircle" size={16} />
              <span>Completed Tasks</span>
            </h4>
            <div className="space-y-1">
              {completedTasks.slice(-3).map((task, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-success-600">
                  <Icon name="Check" size={12} />
                  <span>{task}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upcoming Tasks */}
        {upcomingTasks.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-2 flex items-center space-x-2">
              <Icon name="Clock" size={16} />
              <span>Next Steps</span>
            </h4>
            <div className="space-y-1">
              {upcomingTasks.slice(0, 3).map((task, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-text-secondary">
                  <div className="w-3 h-3 border border-text-secondary rounded-full"></div>
                  <span>{task}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Phase Completion Indicator */}
      {phaseProgress >= 100 && currentPhase !== 'completed' && (
        <div className="mt-4 p-3 bg-success-50 rounded-lg border border-success-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="CheckCircle" size={16} color="var(--color-success)" />
              <span className="text-sm font-medium text-success-600">
                {getCurrentPhase().name} Complete
              </span>
            </div>
            <button
              onClick={() => onPhaseComplete(currentPhase)}
              className="text-sm text-success-600 hover:text-success-700 font-medium"
            >
              Continue →
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollaborationProgressTracker;