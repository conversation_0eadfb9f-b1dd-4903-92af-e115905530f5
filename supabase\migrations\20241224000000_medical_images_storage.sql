-- Medical Images Storage Migration
-- Creates secure storage bucket and policies for medical image uploads
-- Date: 2024-12-24
-- Purpose: Enable multi-modal diagnostic capabilities with secure image storage

-- Create medical images storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'medical-images',
    'medical-images',
    true, -- Public for easy access during consultations
    10485760, -- 10MB limit per file
    ARRAY[
        'image/jpeg',
        'image/png', 
        'image/webp',
        'image/bmp',
        'image/tiff'
    ]::text[]
) ON CONFLICT (id) DO NOTHING;

-- Create storage policies for medical images bucket
CREATE POLICY "Users can upload their own medical images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'medical-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own medical images" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'medical-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own medical images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'medical-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Create medical images metadata table
CREATE TABLE IF NOT EXISTS public.medical_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    storage_path TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    upload_timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    analysis_status TEXT DEFAULT 'pending' CHECK (analysis_status IN ('pending', 'analyzing', 'completed', 'failed')),
    analysis_results JSONB DEFAULT '{}',
    agent_id TEXT, -- Which agent analyzed the image
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX idx_medical_images_session_id ON public.medical_images(session_id);
CREATE INDEX idx_medical_images_user_id ON public.medical_images(user_id);
CREATE INDEX idx_medical_images_upload_timestamp ON public.medical_images(upload_timestamp);
CREATE INDEX idx_medical_images_analysis_status ON public.medical_images(analysis_status);

-- Enable Row Level Security
ALTER TABLE public.medical_images ENABLE ROW LEVEL SECURITY;

-- RLS Policies for medical_images table
CREATE POLICY "Users can view their own medical images metadata" ON public.medical_images
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own medical images metadata" ON public.medical_images
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own medical images metadata" ON public.medical_images
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own medical images metadata" ON public.medical_images
    FOR DELETE USING (user_id = auth.uid());

-- Healthcare providers can view images for their sessions
CREATE POLICY "Healthcare providers can view session medical images" ON public.medical_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.consultation_sessions cs
            JOIN public.user_profiles up ON up.id = auth.uid()
            WHERE cs.id = medical_images.session_id
            AND up.role IN ('provider', 'admin')
        )
    );

-- Function to update analysis status
CREATE OR REPLACE FUNCTION update_medical_image_analysis(
    image_id UUID,
    new_status TEXT,
    analysis_data JSONB DEFAULT NULL,
    analyzing_agent TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.medical_images
    SET 
        analysis_status = new_status,
        analysis_results = COALESCE(analysis_data, analysis_results),
        agent_id = COALESCE(analyzing_agent, agent_id),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = image_id;
    
    RETURN FOUND;
END;
$$;

-- Function to get images for a session
CREATE OR REPLACE FUNCTION get_session_medical_images(session_uuid UUID)
RETURNS TABLE (
    id UUID,
    storage_path TEXT,
    original_filename TEXT,
    file_size BIGINT,
    mime_type TEXT,
    upload_timestamp TIMESTAMPTZ,
    analysis_status TEXT,
    analysis_results JSONB,
    agent_id TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mi.id,
        mi.storage_path,
        mi.original_filename,
        mi.file_size,
        mi.mime_type,
        mi.upload_timestamp,
        mi.analysis_status,
        mi.analysis_results,
        mi.agent_id
    FROM public.medical_images mi
    WHERE mi.session_id = session_uuid
    ORDER BY mi.upload_timestamp DESC;
END;
$$;

-- Function to clean up old medical images (for data retention compliance)
CREATE OR REPLACE FUNCTION cleanup_old_medical_images(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER := 0;
    image_record RECORD;
BEGIN
    -- Find images older than retention period
    FOR image_record IN
        SELECT id, storage_path
        FROM public.medical_images
        WHERE upload_timestamp < (CURRENT_TIMESTAMP - INTERVAL '1 day' * retention_days)
    LOOP
        -- Delete from storage
        PERFORM storage.delete_object('medical-images', image_record.storage_path);
        
        -- Delete metadata record
        DELETE FROM public.medical_images WHERE id = image_record.id;
        
        deleted_count := deleted_count + 1;
    END LOOP;
    
    RETURN deleted_count;
END;
$$;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_medical_images_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

CREATE TRIGGER trigger_update_medical_images_updated_at
    BEFORE UPDATE ON public.medical_images
    FOR EACH ROW
    EXECUTE FUNCTION update_medical_images_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.medical_images IS 'Metadata for medical images uploaded during consultations';
COMMENT ON COLUMN public.medical_images.session_id IS 'Reference to the consultation session';
COMMENT ON COLUMN public.medical_images.storage_path IS 'Path to the image file in Supabase Storage';
COMMENT ON COLUMN public.medical_images.analysis_status IS 'Status of AI analysis: pending, analyzing, completed, failed';
COMMENT ON COLUMN public.medical_images.analysis_results IS 'JSON results from AI image analysis';
COMMENT ON COLUMN public.medical_images.agent_id IS 'ID of the agent that analyzed the image';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.medical_images TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION update_medical_image_analysis(UUID, TEXT, JSONB, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_session_medical_images(UUID) TO authenticated;
