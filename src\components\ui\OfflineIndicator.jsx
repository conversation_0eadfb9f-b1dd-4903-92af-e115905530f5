import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi, Cloud, CloudOff, RefreshCw } from 'lucide-react';
import pwaService from '../../utils/pwaService';

const OfflineIndicator = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingCount, setPendingCount] = useState(0);
  const [syncing, setSyncing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const unsubscribe = pwaService.subscribe((event, data) => {
      switch (event) {
        case 'online':
          setIsOnline(true);
          break;
        case 'offline':
          setIsOnline(false);
          break;
        case 'pendingActionAdded':
          setPendingCount(data?.count || 0);
          break;
        case 'syncStarted':
          setSyncing(true);
          break;
        case 'syncCompleted':
          setSyncing(false);
          setPendingCount(data?.remaining || 0);
          break;
      }
    });

    // Initial setup
    setPendingCount(pwaService.getPendingActionsCount());

    return unsubscribe;
  }, []);

  const handleForceSync = async () => {
    if (isOnline && !syncing) {
      await pwaService.forceSync();
    }
  };

  if (isOnline && pendingCount === 0) {
    return null; // Don't show indicator when online with no pending actions
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <div
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg transition-all duration-300 cursor-pointer
          ${isOnline 
            ? 'bg-blue-500 text-white hover:bg-blue-600' :'bg-red-500 text-white hover:bg-red-600'
          }
        `}
        onClick={() => setShowDetails(!showDetails)}
      >
        {/* Connection Status Icon */}
        <div className="flex items-center space-x-1">
          {isOnline ? (
            <Wifi className="w-4 h-4" />
          ) : (
            <WifiOff className="w-4 h-4" />
          )}
          
          {/* Sync Status Icon */}
          {pendingCount > 0 && (
            <>
              {syncing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <CloudOff className="w-4 h-4" />
              )}
            </>
          )}
        </div>

        {/* Status Text */}
        <span className="text-sm font-medium">
          {isOnline ? (
            pendingCount > 0 ? (
              syncing ? 'Syncing...' : `${pendingCount} pending`
            ) : (
              'Online'
            )
          ) : (
            'Offline'
          )}
        </span>

        {/* Pending Count Badge */}
        {pendingCount > 0 && !syncing && (
          <div className="bg-white bg-opacity-20 rounded-full px-2 py-1 text-xs font-bold">
            {pendingCount}
          </div>
        )}
      </div>

      {/* Details Panel */}
      {showDetails && (
        <div className="mt-2 bg-white rounded-lg shadow-xl border p-4 min-w-64">
          <div className="space-y-3">
            {/* Connection Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {isOnline ? (
                  <Wifi className="w-4 h-4 text-green-500" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-500" />
                )}
                <span className="text-sm font-medium text-gray-700">
                  Connection
                </span>
              </div>
              <span className={`text-sm font-semibold ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
                {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>

            {/* Sync Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {syncing ? (
                  <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
                ) : pendingCount > 0 ? (
                  <CloudOff className="w-4 h-4 text-orange-500" />
                ) : (
                  <Cloud className="w-4 h-4 text-green-500" />
                )}
                <span className="text-sm font-medium text-gray-700">
                  Sync Status
                </span>
              </div>
              <span className={`text-sm font-semibold ${
                syncing ? 'text-blue-600' : 
                pendingCount > 0 ? 'text-orange-600' : 'text-green-600'
              }`}>
                {syncing ? 'Syncing' : 
                 pendingCount > 0 ? `${pendingCount} pending` : 'Synced'}
              </span>
            </div>

            {/* Actions */}
            <div className="pt-2 border-t">
              {isOnline && pendingCount > 0 && !syncing && (
                <button
                  onClick={handleForceSync}
                  className="w-full bg-blue-500 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 transition-colors"
                >
                  Sync Now
                </button>
              )}
              
              {!isOnline && (
                <div className="text-xs text-gray-500 text-center">
                  Changes will sync when connection is restored
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OfflineIndicator;