import React from "react";
import { Link } from "react-router-dom";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";

const AboutUs = () => {
  const teamMembers = [
    {
      name: "Dr. <PERSON>",
      role: "Chief Medical Officer",
      bio: "Board-certified physician with 15+ years in digital health and AI-powered diagnostics.",
      image: "/api/placeholder/150/150"
    },
    {
      name: "<PERSON>",
      role: "Chief Technology Officer", 
      bio: "Former Google AI researcher specializing in natural language processing and healthcare applications.",
      image: "/api/placeholder/150/150"
    },
    {
      name: "Dr. <PERSON><PERSON>",
      role: "Head of Clinical Research",
      bio: "Public health expert focused on healthcare accessibility in underserved communities across Africa.",
      image: "/api/placeholder/150/150"
    },
    {
      name: "<PERSON>",
      role: "Head of Engineering",
      bio: "Full-stack engineer with expertise in scalable healthcare platforms and HIPAA compliance.",
      image: "/api/placeholder/150/150"
    }
  ];

  const milestones = [
    {
      year: "2023",
      title: "Company Founded",
      description: "VoiceHealth AI was founded with a mission to democratize healthcare access across Africa through AI-powered voice technology."
    },
    {
      year: "2023",
      title: "MVP Launch",
      description: "Launched our first AI voice consultation platform with support for English, Swahili, and French."
    },
    {
      year: "2024",
      title: "Multi-Language Expansion",
      description: "Expanded to support 12+ African languages with culturally-adapted health guidance."
    },
    {
      year: "2024",
      title: "Emergency Integration",
      description: "Integrated emergency response systems across 15 African countries with <2 second response times."
    },
    {
      year: "2024",
      title: "HIPAA Compliance",
      description: "Achieved full HIPAA compliance and implemented AES-256 encryption for all health data."
    }
  ];

  return (
    <>
      <Navbar />
      <main className="bg-white">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
          <div className="max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl sm:text-5xl font-extrabold mb-6">
              About VoiceHealth AI
            </h1>
            <p className="text-xl max-w-3xl mx-auto mb-8 opacity-90">
              Democratizing healthcare access across Africa through AI-powered voice consultations, 
              intelligent triage, and culturally-sensitive medical guidance.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/authentication-demo-access"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50"
              >
                Try Our Platform
              </Link>
              <Link
                to="/contact"
                className="inline-flex items-center justify-center px-6 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-primary-600"
              >
                Contact Us
              </Link>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Our Mission</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                To make quality healthcare accessible to everyone, everywhere, by leveraging artificial intelligence 
                and voice technology to bridge the gap between patients and medical expertise.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Accessibility</h3>
                <p className="text-gray-600">
                  Breaking down barriers to healthcare access through voice-first technology that works in any language, anywhere.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Quality</h3>
                <p className="text-gray-600">
                  Delivering evidence-based health insights powered by advanced AI and validated by medical professionals.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Security</h3>
                <p className="text-gray-600">
                  Protecting patient privacy with HIPAA-compliant infrastructure, end-to-end encryption, and comprehensive audit trails.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-8">Our Story</h2>
              <div className="text-lg text-gray-700 space-y-6">
                <p>
                  VoiceHealth AI was born from a simple observation: millions of people across Africa lack access to 
                  timely, quality healthcare guidance. Whether due to geographic barriers, language differences, 
                  or resource constraints, too many health concerns go unaddressed until they become emergencies.
                </p>
                <p>
                  Our founders, a team of medical professionals, AI researchers, and healthcare advocates, 
                  recognized that voice technology could bridge this gap. By combining advanced natural language 
                  processing with medical expertise, we could create a platform that speaks every language, 
                  understands cultural context, and provides instant health guidance.
                </p>
                <p>
                  Today, VoiceHealth AI serves patients across 15+ African countries, providing AI-powered 
                  consultations in 12+ languages, with emergency response integration and culturally-adapted 
                  health recommendations. We're not just building technology – we're building bridges to better health.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Our Journey</h2>
              <p className="text-lg text-gray-600">
                Key milestones in our mission to democratize healthcare access
              </p>
            </div>
            
            <div className="relative">
              <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-200"></div>
              <div className="space-y-12">
                {milestones.map((milestone, index) => (
                  <div key={index} className={`relative flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                    <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <div className="bg-white p-6 rounded-lg shadow-md">
                        <div className="text-primary-600 font-bold text-lg mb-2">{milestone.year}</div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">{milestone.title}</h3>
                        <p className="text-gray-600">{milestone.description}</p>
                      </div>
                    </div>
                    <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Our Team</h2>
              <p className="text-lg text-gray-600">
                Meet the experts behind VoiceHealth AI's innovative healthcare solutions
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                    <svg className="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                    <p className="text-primary-600 font-medium mb-3">{member.role}</p>
                    <p className="text-gray-600 text-sm">{member.bio}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Our Values</h2>
              <p className="text-lg text-gray-600">
                The principles that guide everything we do
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Patient First</h3>
                <p className="text-gray-600">
                  Every decision we make prioritizes patient safety, privacy, and well-being above all else.
                </p>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Cultural Sensitivity</h3>
                <p className="text-gray-600">
                  We respect and adapt to diverse cultural contexts, traditional medicine practices, and local health beliefs.
                </p>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Innovation</h3>
                <p className="text-gray-600">
                  We continuously push the boundaries of AI and healthcare technology to solve real-world problems.
                </p>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Transparency</h3>
                <p className="text-gray-600">
                  We maintain open communication about our AI capabilities, limitations, and decision-making processes.
                </p>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Collaboration</h3>
                <p className="text-gray-600">
                  We work closely with healthcare providers, communities, and governments to improve health outcomes.
                </p>
              </div>
              
              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Equity</h3>
                <p className="text-gray-600">
                  We're committed to reducing healthcare disparities and ensuring equal access to quality care.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-primary-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-extrabold mb-4">Join Us in Transforming Healthcare</h2>
            <p className="text-xl mb-8 opacity-90">
              Experience the future of accessible, AI-powered healthcare today.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/authentication-demo-access"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50"
              >
                Start Your Consultation
              </Link>
              <Link
                to="/contact"
                className="inline-flex items-center justify-center px-6 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-primary-600"
              >
                Partner With Us
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default AboutUs;
