{"environment": "staging", "description": "Staging environment for VoiceHealth AI testing and validation", "version": "1.0.0", "lastUpdated": "2025-01-06T00:00:00Z", "infrastructure": {"cloudProvider": "AWS", "region": "eu-west-1", "availabilityZones": ["eu-west-1a", "eu-west-1b"], "vpc": {"cidr": "********/16", "subnets": {"public": ["********/24", "********/24"], "private": ["*********/24", "*********/24"], "database": ["*********/24", "*********/24"]}}, "scaling": {"autoScaling": true, "minInstances": 2, "maxInstances": 10, "targetCPUUtilization": 70}}, "application": {"baseUrl": "https://staging.voicehealth.ai", "apiBaseUrl": "https://api-staging.voicehealth.ai", "frontendUrl": "https://app-staging.voicehealth.ai", "adminUrl": "https://admin-staging.voicehealth.ai", "features": {"enableDebugMode": true, "enableTestData": true, "enableMockServices": true, "enablePerformanceMonitoring": true, "enableDetailedLogging": true, "enableCulturalValidation": true, "enableTraditionalMedicine": true, "enableEmergencyProtocols": true, "enableVoiceProcessing": true, "enableAIOrchestration": true}, "limits": {"maxConcurrentUsers": 1000, "maxRequestsPerMinute": 10000, "maxFileUploadSize": "25MB", "maxAudioDuration": 300, "maxSessionDuration": 3600}}, "database": {"type": "postgresql", "host": "staging-db.voicehealth.ai", "port": 5432, "name": "voicehealth_staging", "ssl": true, "connectionPool": {"min": 5, "max": 20, "idleTimeout": 30000}, "backup": {"enabled": true, "frequency": "daily", "retention": "7 days"}}, "redis": {"host": "staging-redis.voicehealth.ai", "port": 6379, "ssl": true, "maxConnections": 100, "keyPrefix": "voicehealth:staging:"}, "storage": {"provider": "aws_s3", "bucket": "voicehealth-staging-storage", "region": "eu-west-1", "encryption": "AES256", "versioning": true, "lifecycle": {"transitionToIA": 30, "transitionToGlacier": 90, "expiration": 365}}, "security": {"encryption": {"algorithm": "AES-256-GCM", "keyRotation": "monthly"}, "authentication": {"jwtSecret": "${STAGING_JWT_SECRET}", "jwtExpiration": "24h", "refreshTokenExpiration": "7d", "mfaEnabled": true}, "cors": {"allowedOrigins": ["https://app-staging.voicehealth.ai", "https://admin-staging.voicehealth.ai", "http://localhost:3000", "http://localhost:4028"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Requested-With"]}, "rateLimiting": {"enabled": true, "windowMs": 60000, "maxRequests": 1000, "emergencyBypass": true}}, "monitoring": {"enabled": true, "provider": "cloudwatch", "metrics": {"collectInterval": 30, "retentionPeriod": "30 days"}, "logging": {"level": "debug", "format": "json", "destination": "cloudwatch", "retention": "30 days"}, "alerts": {"enabled": true, "channels": ["email", "slack"], "thresholds": {"errorRate": 5, "responseTime": 2000, "cpuUsage": 80, "memoryUsage": 85}}, "healthChecks": {"enabled": true, "interval": 30, "timeout": 5000, "endpoints": ["/health", "/api/health", "/api/ai-health"]}}, "aiServices": {"providers": {"openai": {"enabled": true, "apiKey": "${STAGING_OPENAI_API_KEY}", "model": "gpt-4", "maxTokens": 1000, "temperature": 0.7}, "anthropic": {"enabled": true, "apiKey": "${STAGING_ANTHROPIC_API_KEY}", "model": "claude-3-sonnet-20240229", "maxTokens": 1000}, "elevenlabs": {"enabled": true, "apiKey": "${STAGING_ELEVENLABS_API_KEY}", "voiceId": "ErXwobaYiN019PkySvjV"}}, "fallback": {"enabled": true, "order": ["openai", "anthropic"], "timeout": 30000}}, "culturalServices": {"validation": {"enabled": true, "strictMode": false, "cacheResults": true, "cacheTTL": 3600}, "focusGroups": {"enabled": true, "mockData": true, "regions": ["GH", "KE", "NG", "ZA", "ET"]}, "traditionalMedicine": {"enabled": true, "safetyChecks": true, "integrationLevel": "advisory"}}, "emergencyServices": {"enabled": true, "responseTimeTarget": 2000, "escalationRules": {"critical": ["immediate_notification", "family_contact", "emergency_services"], "high": ["urgent_notification", "family_contact"], "medium": ["standard_notification"], "low": ["routine_notification"]}, "notifications": {"email": {"enabled": true, "provider": "ses", "fromAddress": "<EMAIL>"}, "sms": {"enabled": true, "provider": "twi<PERSON>", "fromNumber": "${STAGING_TWILIO_PHONE_NUMBER}"}}}, "testing": {"enabled": true, "testData": {"enabled": true, "autoGenerate": true, "seedData": true}, "mockServices": {"enabled": true, "aiProviders": false, "paymentGateway": true, "emergencyServices": true}, "performance": {"enabled": true, "loadTesting": true, "stressTesting": false}}, "compliance": {"hipaa": {"enabled": true, "auditLogging": true, "dataEncryption": true, "accessControls": true}, "gdpr": {"enabled": true, "dataRetention": "2 years", "rightToErasure": true, "consentManagement": true}, "regional": {"ghana": {"dataProtectionAct": true}, "kenya": {"dataProtectionAct": true}, "nigeria": {"ndpr": true}, "southAfrica": {"popia": true}, "ethiopia": {"dataProtection": true}}}, "deployment": {"strategy": "blue_green", "rollback": {"enabled": true, "automaticTriggers": ["high_error_rate", "performance_degradation"], "maxRollbackTime": 300}, "healthChecks": {"enabled": true, "warmupTime": 60, "healthyThreshold": 3, "unhealthyThreshold": 2}}, "environmentVariables": {"NODE_ENV": "staging", "LOG_LEVEL": "debug", "ENABLE_CORS": "true", "ENABLE_RATE_LIMITING": "true", "ENABLE_MONITORING": "true", "ENABLE_HEALTH_CHECKS": "true", "DATABASE_SSL": "true", "REDIS_SSL": "true", "S3_ENCRYPTION": "true"}}