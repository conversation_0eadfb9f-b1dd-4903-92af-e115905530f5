import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const SyncControlsPanel = ({
  syncSettings = {},
  onUpdateSettings = () => {},
  onResolveConflict = () => {},
  conflicts = [],
  className = ''
}) => {
  const [settings, setSettings] = useState({
    autoSync: syncSettings?.autoSync || true,
    syncFrequency: syncSettings?.syncFrequency || 'realtime',
    syncOnWifi: syncSettings?.syncOnWifi || true,
    backgroundSync: syncSettings?.backgroundSync || false,
    conflictResolution: syncSettings?.conflictResolution || 'manual',
    ...syncSettings
  });

  const [showConflicts, setShowConflicts] = useState(false);

  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onUpdateSettings(newSettings);
  };

  const syncFrequencyOptions = [
    { value: 'realtime', label: 'Real-time', description: 'Immediate synchronization' },
    { value: '5min', label: 'Every 5 minutes', description: 'Regular intervals' },
    { value: '30min', label: 'Every 30 minutes', description: 'Moderate frequency' },
    { value: 'manual', label: 'Manual only', description: 'User-initiated sync' }
  ];

  const conflictResolutionOptions = [
    { value: 'manual', label: 'Manual resolution', description: 'Review each conflict' },
    { value: 'server', label: 'Server wins', description: 'Always use server data' },
    { value: 'local', label: 'Local wins', description: 'Keep local changes' },
    { value: 'newest', label: 'Newest wins', description: 'Use most recent data' }
  ];

  return (
    <div className={`bg-surface rounded-xl shadow-elevated ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
              <Icon name="Settings" size={20} color="var(--color-primary-500)" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary font-heading">
                Sync Controls
              </h3>
              <p className="text-sm text-text-secondary">
                Configure synchronization preferences
              </p>
            </div>
          </div>

          {conflicts?.length > 0 && (
            <button
              onClick={() => setShowConflicts(!showConflicts)}
              className="flex items-center space-x-2 px-3 py-2 bg-warning-50 text-warning-600 rounded-lg hover:bg-warning-100 transition-fast"
            >
              <Icon name="AlertTriangle" size={16} />
              <span className="text-sm font-medium">{conflicts.length} Conflicts</span>
            </button>
          )}
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Auto Sync Toggle */}
        <div className="flex items-center justify-between p-4 bg-background rounded-lg">
          <div className="flex-1">
            <h4 className="font-medium text-text-primary mb-1">Automatic Synchronization</h4>
            <p className="text-sm text-text-secondary">
              Automatically sync data when connection is available
            </p>
          </div>
          <button
            onClick={() => handleSettingChange('autoSync', !settings.autoSync)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.autoSync ? 'bg-primary-500' : 'bg-secondary-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.autoSync ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Sync Frequency */}
        <div className="space-y-3">
          <h4 className="font-medium text-text-primary">Sync Frequency</h4>
          <div className="space-y-2">
            {syncFrequencyOptions.map((option) => (
              <label key={option.value} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="syncFrequency"
                  value={option.value}
                  checked={settings.syncFrequency === option.value}
                  onChange={(e) => handleSettingChange('syncFrequency', e.target.value)}
                  className="w-4 h-4 text-primary-500 focus:ring-primary-500 border-secondary-300"
                />
                <div>
                  <span className="text-sm font-medium text-text-primary">{option.label}</span>
                  <p className="text-xs text-text-secondary">{option.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Network Preferences */}
        <div className="space-y-4">
          <h4 className="font-medium text-text-primary">Network Preferences</h4>
          
          <div className="flex items-center justify-between p-3 bg-background rounded-lg">
            <div>
              <span className="text-sm font-medium text-text-primary">WiFi Only</span>
              <p className="text-xs text-text-secondary">Only sync over WiFi connections</p>
            </div>
            <button
              onClick={() => handleSettingChange('syncOnWifi', !settings.syncOnWifi)}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                settings.syncOnWifi ? 'bg-primary-500' : 'bg-secondary-200'
              }`}
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  settings.syncOnWifi ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between p-3 bg-background rounded-lg">
            <div>
              <span className="text-sm font-medium text-text-primary">Background Sync</span>
              <p className="text-xs text-text-secondary">Sync when app is in background</p>
            </div>
            <button
              onClick={() => handleSettingChange('backgroundSync', !settings.backgroundSync)}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                settings.backgroundSync ? 'bg-primary-500' : 'bg-secondary-200'
              }`}
            >
              <span
                className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                  settings.backgroundSync ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        {/* Conflict Resolution */}
        <div className="space-y-3">
          <h4 className="font-medium text-text-primary">Conflict Resolution</h4>
          <div className="space-y-2">
            {conflictResolutionOptions.map((option) => (
              <label key={option.value} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="conflictResolution"
                  value={option.value}
                  checked={settings.conflictResolution === option.value}
                  onChange={(e) => handleSettingChange('conflictResolution', e.target.value)}
                  className="w-4 h-4 text-primary-500 focus:ring-primary-500 border-secondary-300"
                />
                <div>
                  <span className="text-sm font-medium text-text-primary">{option.label}</span>
                  <p className="text-xs text-text-secondary">{option.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Conflicts Panel */}
      {showConflicts && conflicts?.length > 0 && (
        <div className="border-t border-border p-6">
          <h4 className="font-medium text-text-primary mb-4">Data Conflicts</h4>
          <div className="space-y-3">
            {conflicts.map((conflict, index) => (
              <div key={index} className="p-4 bg-warning-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-warning-800">{conflict.type}</span>
                  <span className="text-xs text-warning-600">{conflict.timestamp}</span>
                </div>
                <p className="text-sm text-warning-700 mb-3">{conflict.description}</p>
                <div className="flex space-x-2">
                  <button
                    onClick={() => onResolveConflict(conflict.id, 'local')}
                    className="px-3 py-1 bg-primary-50 text-primary-600 rounded text-xs hover:bg-primary-100"
                  >
                    Keep Local
                  </button>
                  <button
                    onClick={() => onResolveConflict(conflict.id, 'server')}
                    className="px-3 py-1 bg-secondary-50 text-secondary-600 rounded text-xs hover:bg-secondary-100"
                  >
                    Use Server
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SyncControlsPanel;