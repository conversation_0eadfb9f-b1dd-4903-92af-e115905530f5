/**
 * AGENT COMMUNICATION PROTOCOL
 * 
 * Enables real-time communication between AI agents for collaborative medical consultations.
 * Replaces the fake collaboration UI with actual inter-agent communication.
 * 
 * FEATURES:
 * - Inter-agent messaging and consultation requests
 * - Real-time collaboration on complex medical cases
 * - Agent expertise sharing and knowledge transfer
 * - Collaborative diagnosis and treatment planning
 * - HIPAA-compliant communication logging
 * - Emergency escalation protocols
 */

import { EventEmitter } from 'events';
import type { IAgent, AgentRole, AgentCapability } from '../agents/BaseAgent';
import { memoryManager } from './MemoryManager';
import { agentRegistry } from './AgentRegistry';

export interface AgentMessage {
  id: string;
  fromAgentId: string;
  toAgentId: string;
  sessionId: string;
  messageType: AgentMessageType;
  content: string;
  metadata?: AgentMessageMetadata;
  timestamp: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  requiresResponse: boolean;
  expiresAt?: string;
}

export type AgentMessageType = 
  | 'consultation_request'
  | 'consultation_response'
  | 'expertise_request'
  | 'expertise_share'
  | 'diagnosis_collaboration'
  | 'treatment_suggestion'
  | 'emergency_alert'
  | 'handoff_request'
  | 'status_update'
  | 'knowledge_share';

export interface AgentMessageMetadata {
  patientContext?: any;
  medicalData?: any;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  requiredCapabilities?: AgentCapability[];
  confidenceScore?: number;
  reasoning?: string;
  attachments?: AgentMessageAttachment[];
}

export interface AgentMessageAttachment {
  type: 'medical_data' | 'diagnostic_result' | 'treatment_plan' | 'reference_material';
  content: any;
  description: string;
}

export interface CollaborationSession {
  id: string;
  sessionId: string; // Patient session ID
  participatingAgents: string[]; // Agent IDs
  leadAgent: string; // Primary agent ID
  topic: string;
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  startedAt: string;
  endedAt?: string;
  messages: AgentMessage[];
  outcome?: CollaborationOutcome;
}

export interface CollaborationOutcome {
  consensus: boolean;
  primaryRecommendation: string;
  alternativeOptions: string[];
  confidenceLevel: number;
  followUpRequired: boolean;
  escalationNeeded: boolean;
}

export interface ConsultationRequest {
  requestingAgentId: string;
  targetAgentRole?: AgentRole;
  targetAgentId?: string;
  sessionId: string;
  consultationType: 'second_opinion' | 'expertise_needed' | 'complex_case' | 'emergency_support';
  patientContext: any;
  specificQuestion: string;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  timeoutMinutes: number;
}

export class AgentCommunicationProtocol extends EventEmitter {
  private activeCollaborations: Map<string, CollaborationSession> = new Map();
  private pendingMessages: Map<string, AgentMessage[]> = new Map(); // agentId -> messages
  private messageHistory: Map<string, AgentMessage[]> = new Map(); // sessionId -> messages

  constructor() {
    super();
    console.log('🔗 Initializing Agent Communication Protocol...');
    this.setupEventHandlers();
  }

  /**
   * Send a message from one agent to another
   */
  async sendMessage(message: Omit<AgentMessage, 'id' | 'timestamp'>): Promise<boolean> {
    try {
      const fullMessage: AgentMessage = {
        ...message,
        id: this.generateMessageId(),
        timestamp: new Date().toISOString()
      };

      console.log(`📨 Agent message: ${message.fromAgentId} → ${message.toAgentId} (${message.messageType})`);

      // Validate agents exist
      const fromAgent = agentRegistry.getAgent(message.fromAgentId);
      const toAgent = agentRegistry.getAgent(message.toAgentId);

      if (!fromAgent || !toAgent) {
        console.error('❌ Invalid agent IDs in message');
        return false;
      }

      // Store message in pending queue for target agent
      if (!this.pendingMessages.has(message.toAgentId)) {
        this.pendingMessages.set(message.toAgentId, []);
      }
      this.pendingMessages.get(message.toAgentId)!.push(fullMessage);

      // Store in session message history
      if (!this.messageHistory.has(message.sessionId)) {
        this.messageHistory.set(message.sessionId, []);
      }
      this.messageHistory.get(message.sessionId)!.push(fullMessage);

      // Log to persistent memory for HIPAA compliance
      await this.logMessageToMemory(fullMessage);

      // Emit event for real-time updates
      this.emit('message', fullMessage);

      // Handle high priority messages immediately
      if (message.priority === 'critical' || message.priority === 'high') {
        this.emit('urgent_message', fullMessage);
      }

      return true;

    } catch (error) {
      console.error('❌ Failed to send agent message:', error);
      return false;
    }
  }

  /**
   * Get pending messages for an agent
   */
  getPendingMessages(agentId: string): AgentMessage[] {
    return this.pendingMessages.get(agentId) || [];
  }

  /**
   * Mark messages as read by an agent
   */
  markMessagesAsRead(agentId: string, messageIds: string[]): void {
    const pending = this.pendingMessages.get(agentId) || [];
    const remaining = pending.filter(msg => !messageIds.includes(msg.id));
    this.pendingMessages.set(agentId, remaining);
  }

  /**
   * Request consultation from another agent
   */
  async requestConsultation(request: ConsultationRequest): Promise<string | null> {
    try {
      console.log(`🩺 Consultation request: ${request.consultationType} for session ${request.sessionId}`);

      // Find target agent
      let targetAgent: IAgent | null = null;
      
      if (request.targetAgentId) {
        targetAgent = agentRegistry.getAgent(request.targetAgentId);
      } else if (request.targetAgentRole) {
        const agents = agentRegistry.getAgentsByRole(request.targetAgentRole);
        targetAgent = agents.length > 0 ? agents[0] : null;
      }

      if (!targetAgent) {
        console.error('❌ No suitable agent found for consultation');
        return null;
      }

      // Create collaboration session
      const collaborationId = this.generateCollaborationId();
      const collaboration: CollaborationSession = {
        id: collaborationId,
        sessionId: request.sessionId,
        participatingAgents: [request.requestingAgentId, targetAgent.id],
        leadAgent: request.requestingAgentId,
        topic: `${request.consultationType}: ${request.specificQuestion}`,
        status: 'active',
        startedAt: new Date().toISOString(),
        messages: []
      };

      this.activeCollaborations.set(collaborationId, collaboration);

      // Send consultation request message
      const success = await this.sendMessage({
        fromAgentId: request.requestingAgentId,
        toAgentId: targetAgent.id,
        sessionId: request.sessionId,
        messageType: 'consultation_request',
        content: `Consultation Request: ${request.specificQuestion}`,
        metadata: {
          patientContext: request.patientContext,
          urgencyLevel: request.urgencyLevel,
          requiredCapabilities: targetAgent.capabilities
        },
        priority: request.urgencyLevel === 'critical' ? 'critical' : 'high',
        requiresResponse: true,
        expiresAt: new Date(Date.now() + request.timeoutMinutes * 60 * 1000).toISOString()
      });

      if (success) {
        console.log(`✅ Consultation request sent to ${targetAgent.name}`);
        return collaborationId;
      } else {
        this.activeCollaborations.delete(collaborationId);
        return null;
      }

    } catch (error) {
      console.error('❌ Failed to request consultation:', error);
      return null;
    }
  }

  /**
   * Respond to a consultation request
   */
  async respondToConsultation(
    collaborationId: string,
    respondingAgentId: string,
    response: string,
    confidence: number,
    additionalRecommendations?: string[]
  ): Promise<boolean> {
    try {
      const collaboration = this.activeCollaborations.get(collaborationId);
      if (!collaboration) {
        console.error('❌ Collaboration not found');
        return false;
      }

      // Send response message
      const success = await this.sendMessage({
        fromAgentId: respondingAgentId,
        toAgentId: collaboration.leadAgent,
        sessionId: collaboration.sessionId,
        messageType: 'consultation_response',
        content: response,
        metadata: {
          confidenceScore: confidence,
          reasoning: additionalRecommendations?.join('; ')
        },
        priority: 'high',
        requiresResponse: false
      });

      if (success) {
        // Update collaboration status
        collaboration.outcome = {
          consensus: confidence > 0.8,
          primaryRecommendation: response,
          alternativeOptions: additionalRecommendations || [],
          confidenceLevel: confidence,
          followUpRequired: confidence < 0.7,
          escalationNeeded: confidence < 0.5
        };

        console.log(`✅ Consultation response sent for collaboration ${collaborationId}`);
        this.emit('collaboration_update', collaboration);
      }

      return success;

    } catch (error) {
      console.error('❌ Failed to respond to consultation:', error);
      return false;
    }
  }

  /**
   * Get active collaboration sessions
   */
  getActiveCollaborations(): CollaborationSession[] {
    return Array.from(this.activeCollaborations.values());
  }

  /**
   * Get collaboration by ID
   */
  getCollaboration(collaborationId: string): CollaborationSession | null {
    return this.activeCollaborations.get(collaborationId) || null;
  }

  /**
   * Get message history for a session
   */
  getSessionMessageHistory(sessionId: string): AgentMessage[] {
    return this.messageHistory.get(sessionId) || [];
  }

  /**
   * Complete a collaboration session
   */
  async completeCollaboration(collaborationId: string, outcome: CollaborationOutcome): Promise<boolean> {
    try {
      const collaboration = this.activeCollaborations.get(collaborationId);
      if (!collaboration) {
        return false;
      }

      collaboration.status = 'completed';
      collaboration.endedAt = new Date().toISOString();
      collaboration.outcome = outcome;

      // Log completion to memory
      await memoryManager.saveMessage(
        collaboration.sessionId,
        'system',
        'communication_protocol',
        'Agent Communication System',
        `COLLABORATION COMPLETED: ${collaboration.topic}. Outcome: ${outcome.primaryRecommendation}`,
        0,
        {
          collaborationType: 'completion',
          collaborationId,
          participatingAgents: collaboration.participatingAgents,
          outcome,
          duration: Date.now() - new Date(collaboration.startedAt).getTime()
        }
      );

      this.emit('collaboration_completed', collaboration);
      console.log(`✅ Collaboration ${collaborationId} completed`);

      return true;

    } catch (error) {
      console.error('❌ Failed to complete collaboration:', error);
      return false;
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.on('urgent_message', (message: AgentMessage) => {
      console.log(`🚨 Urgent agent message: ${message.messageType} from ${message.fromAgentId}`);
      // Handle urgent messages (could trigger notifications, etc.)
    });

    this.on('collaboration_update', (collaboration: CollaborationSession) => {
      console.log(`🔄 Collaboration updated: ${collaboration.id} - ${collaboration.status}`);
    });
  }

  /**
   * Log message to persistent memory for HIPAA compliance
   */
  private async logMessageToMemory(message: AgentMessage): Promise<void> {
    try {
      await memoryManager.saveMessage(
        message.sessionId,
        'system',
        'agent_communication',
        'Agent Communication Protocol',
        `AGENT MESSAGE: ${message.fromAgentId} → ${message.toAgentId} (${message.messageType})`,
        0, // System messages get priority sequence
        {
          messageType: 'agent_communication',
          agentMessage: {
            id: message.id,
            fromAgentId: message.fromAgentId,
            toAgentId: message.toAgentId,
            messageType: message.messageType,
            priority: message.priority,
            requiresResponse: message.requiresResponse
          },
          timestamp: message.timestamp
        }
      );
    } catch (error) {
      console.error('❌ Failed to log agent message to memory:', error);
    }
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique collaboration ID
   */
  private generateCollaborationId(): string {
    return `collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup expired messages and collaborations
   */
  async cleanup(): Promise<void> {
    const now = new Date();

    // Clean up expired messages
    for (const [agentId, messages] of this.pendingMessages.entries()) {
      const validMessages = messages.filter(msg => {
        if (!msg.expiresAt) return true;
        return new Date(msg.expiresAt) > now;
      });
      this.pendingMessages.set(agentId, validMessages);
    }

    // Clean up old collaborations (keep for 24 hours)
    const cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    for (const [id, collaboration] of this.activeCollaborations.entries()) {
      if (collaboration.status === 'completed' && 
          collaboration.endedAt && 
          new Date(collaboration.endedAt) < cutoffTime) {
        this.activeCollaborations.delete(id);
      }
    }

    console.log('🧹 Agent communication cleanup completed');
  }
}

// Export singleton instance
export const agentCommunicationProtocol = new AgentCommunicationProtocol();
export default agentCommunicationProtocol;
