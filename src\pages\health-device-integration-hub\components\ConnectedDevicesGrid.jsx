import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const ConnectedDevicesGrid = ({ 
  devices = [],
  onConfigureDevice = () => {},
  onDisconnectDevice = () => {},
  onSyncDevice = () => {},
  className = ''
}) => {
  const [selectedDevice, setSelectedDevice] = useState(null);

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'fitness-tracker': return 'Watch';
      case 'blood-pressure': return 'Heart';
      case 'glucose-meter': return 'Droplet';
      case 'smart-scale': return 'Scale';
      case 'oximeter': return 'Circle';
      case 'thermometer': return 'Thermometer';
      default: return 'Smartphone';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-success-600 bg-success-50';
      case 'syncing': return 'text-primary-600 bg-primary-50';
      case 'error': return 'text-error-600 bg-error-50';
      case 'disconnected': return 'text-text-muted bg-secondary-50';
      default: return 'text-text-muted bg-secondary-50';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return 'CheckCircle';
      case 'syncing': return 'RefreshCw';
      case 'error': return 'AlertCircle';
      case 'disconnected': return 'XCircle';
      default: return 'Circle';
    }
  };

  const formatLastSync = (timestamp) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
            <Icon name="Smartphone" size={20} color="var(--color-primary-500)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Connected Devices
            </h3>
            <p className="text-sm text-text-secondary">
              {devices.length} devices connected
            </p>
          </div>
        </div>

        <button className="flex items-center space-x-2 px-4 py-2 bg-primary-50 text-primary-600 rounded-lg hover:bg-primary-100 transition-fast">
          <Icon name="Plus" size={16} />
          <span className="text-sm font-medium">Add Device</span>
        </button>
      </div>

      {/* Device Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {devices.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Icon name="Smartphone" size={48} color="var(--color-text-muted)" className="mx-auto mb-4" />
            <h4 className="text-lg font-medium text-text-primary mb-2">No devices connected</h4>
            <p className="text-text-secondary mb-4">Connect your health devices to start tracking your data</p>
            <button className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-fast">
              Connect Device
            </button>
          </div>
        ) : (
          devices.map((device) => (
            <div 
              key={device.id} 
              className="border border-border rounded-lg p-4 hover:border-border-active transition-fast cursor-pointer"
              onClick={() => setSelectedDevice(device)}
            >
              {/* Device Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-secondary-50 rounded-lg flex items-center justify-center">
                    <Icon 
                      name={getDeviceIcon(device.type)} 
                      size={18} 
                      color="var(--color-text-secondary)" 
                    />
                  </div>
                  <div>
                    <h4 className="font-medium text-text-primary">{device.name}</h4>
                    <p className="text-sm text-text-secondary">{device.model}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSyncDevice(device.id);
                    }}
                    className="p-2 text-primary-600 hover:bg-primary-50 rounded-lg transition-fast"
                    title="Sync device"
                  >
                    <Icon 
                      name={getStatusIcon(device.status)} 
                      size={16} 
                      className={device.status === 'syncing' ? 'animate-spin' : ''} 
                    />
                  </button>
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center justify-between mb-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                  {device.status}
                </span>
                <span className="text-xs text-text-muted">
                  Battery: {device.battery || 'N/A'}%
                </span>
              </div>

              {/* Metrics */}
              <div className="space-y-2 mb-4">
                {device.metrics?.map((metric, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-text-secondary">{metric.name}</span>
                    <span className="font-medium text-text-primary">
                      {metric.value} {metric.unit}
                    </span>
                  </div>
                ))}
              </div>

              {/* Last Sync */}
              <div className="flex items-center justify-between text-xs text-text-muted">
                <div className="flex items-center space-x-1">
                  <Icon name="Clock" size={12} />
                  <span>Last sync: {formatLastSync(device.lastSync)}</span>
                </div>
                <span>{device.dataPoints || 0} readings</span>
              </div>

              {/* Actions */}
              <div className="flex space-x-2 mt-4">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onConfigureDevice(device.id);
                  }}
                  className="flex-1 px-3 py-2 bg-secondary-50 text-text-secondary rounded-lg hover:bg-secondary-100 transition-fast text-sm"
                >
                  Configure
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDisconnectDevice(device.id);
                  }}
                  className="px-3 py-2 bg-error-50 text-error-600 rounded-lg hover:bg-error-100 transition-fast text-sm"
                >
                  Disconnect
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Device Detail Modal */}
      {selectedDevice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-surface rounded-xl p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-text-primary">
                {selectedDevice.name}
              </h3>
              <button
                onClick={() => setSelectedDevice(null)}
                className="p-2 text-text-secondary hover:text-text-primary hover:bg-secondary-50 rounded-lg"
              >
                <Icon name="X" size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-4 bg-background rounded-lg">
                <div className="w-12 h-12 bg-secondary-50 rounded-lg flex items-center justify-center">
                  <Icon 
                    name={getDeviceIcon(selectedDevice.type)} 
                    size={24} 
                    color="var(--color-text-secondary)" 
                  />
                </div>
                <div>
                  <h4 className="font-medium text-text-primary">{selectedDevice.model}</h4>
                  <p className="text-sm text-text-secondary">{selectedDevice.manufacturer}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-background rounded-lg">
                  <p className="text-sm text-text-secondary">Status</p>
                  <p className="font-medium text-text-primary capitalize">{selectedDevice.status}</p>
                </div>
                <div className="p-3 bg-background rounded-lg">
                  <p className="text-sm text-text-secondary">Battery</p>
                  <p className="font-medium text-text-primary">{selectedDevice.battery || 'N/A'}%</p>
                </div>
                <div className="p-3 bg-background rounded-lg">
                  <p className="text-sm text-text-secondary">Data Points</p>
                  <p className="font-medium text-text-primary">{selectedDevice.dataPoints || 0}</p>
                </div>
                <div className="p-3 bg-background rounded-lg">
                  <p className="text-sm text-text-secondary">Last Sync</p>
                  <p className="font-medium text-text-primary">{formatLastSync(selectedDevice.lastSync)}</p>
                </div>
              </div>

              {selectedDevice.metrics && (
                <div>
                  <h4 className="font-medium text-text-primary mb-3">Recent Readings</h4>
                  <div className="space-y-2">
                    {selectedDevice.metrics.map((metric, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-background rounded-lg">
                        <span className="text-text-secondary">{metric.name}</span>
                        <span className="font-medium text-text-primary">
                          {metric.value} {metric.unit}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => onConfigureDevice(selectedDevice.id)}
                  className="flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-fast"
                >
                  Configure
                </button>
                <button
                  onClick={() => onSyncDevice(selectedDevice.id)}
                  className="px-4 py-2 bg-secondary-50 text-text-secondary rounded-lg hover:bg-secondary-100 transition-fast"
                >
                  Sync Now
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConnectedDevicesGrid;