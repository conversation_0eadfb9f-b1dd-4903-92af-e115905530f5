import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AIEngineConfiguration = () => {
  const [selectedEngine, setSelectedEngine] = useState('gpt-4.1');
  const [engineConfig, setEngineConfig] = useState({
    'gpt-4.1': {
      status: 'active',
      load: 67,
      responseTime: 234,
      requestsPerMin: 45,
      tokensPerMin: 12500,
      maxTokens: 4096,
      temperature: 0.7,
      topP: 0.9
    },
    'claude-3.5': {
      status: 'standby',
      load: 23,
      responseTime: 189,
      requestsPerMin: 18,
      tokensPerMin: 8200,
      maxTokens: 8192,
      temperature: 0.8,
      topP: 0.95
    },
    'gemini-pro-1.5': {
      status: 'backup',
      load: 12,
      responseTime: 156,
      requestsPerMin: 8,
      tokensPerMin: 4800,
      maxTokens: 2048,
      temperature: 0.6,
      topP: 0.85
    }
  });

  const [loadBalancing, setLoadBalancing] = useState({
    enabled: true,
    strategy: 'round-robin',
    failoverTimeout: 30,
    healthCheckInterval: 60
  });

  const engines = [
    { id: 'gpt-4.1', name: 'GPT-4.1', icon: 'Brain', color: 'text-green-600' },
    { id: 'claude-3.5', name: 'Claude 3.5', icon: 'Zap', color: 'text-blue-600' },
    { id: 'gemini-pro-1.5', name: 'Gemini Pro 1.5', icon: 'Stars', color: 'text-purple-600' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-success-600 bg-success-100';
      case 'standby': return 'text-warning-600 bg-warning-100';
      case 'backup': return 'text-gray-600 bg-gray-100';
      case 'error': return 'text-error-600 bg-error-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getLoadColor = (load) => {
    if (load >= 80) return 'text-error-600';
    if (load >= 60) return 'text-warning-600';
    return 'text-success-600';
  };

  const handleEngineToggle = (engineId, newStatus) => {
    setEngineConfig(prev => ({
      ...prev,
      [engineId]: {
        ...prev[engineId],
        status: newStatus
      }
    }));
  };

  const handleParameterChange = (engineId, parameter, value) => {
    setEngineConfig(prev => ({
      ...prev,
      [engineId]: {
        ...prev[engineId],
        [parameter]: value
      }
    }));
  };

  const handleLoadBalancingChange = (setting, value) => {
    setLoadBalancing(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const currentEngine = engineConfig[selectedEngine];

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              AI Engine Configuration
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Manage GPT-4.1, Claude 3.5, and Gemini Pro 1.5 backend switching with load balancing
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              iconName="RotateCcw"
            >
              Reset
            </Button>
            <Button
              size="sm"
              variant="primary"
              iconName="Save"
            >
              Save Config
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Engine Selection */}
        <div>
          <h4 className="text-sm font-medium text-text-primary mb-3">AI Engine Status</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {engines.map((engine) => {
              const config = engineConfig[engine.id];
              return (
                <div
                  key={engine.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedEngine === engine.id
                      ? 'border-primary-500 bg-primary-50' :'border-border bg-surface hover:border-primary-300'
                  }`}
                  onClick={() => setSelectedEngine(engine.id)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <Icon name={engine.icon} size={20} className={`mr-2 ${engine.color}`} />
                      <span className="font-medium text-text-primary">{engine.name}</span>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(config.status)}`}>
                      {config.status}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Load</span>
                      <span className={`font-medium ${getLoadColor(config.load)}`}>{config.load}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div
                        className={`h-1 rounded-full transition-all duration-300 ${
                          config.load >= 80 ? 'bg-error-500' :
                          config.load >= 60 ? 'bg-warning-500' : 'bg-success-500'
                        }`}
                        style={{ width: `${config.load}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-text-secondary">
                      <span>Response: {config.responseTime}ms</span>
                      <span>{config.requestsPerMin}/min</span>
                    </div>
                  </div>

                  <div className="mt-3 flex gap-2">
                    {config.status !== 'active' && (
                      <Button
                        size="sm"
                        variant="outline"
                        iconName="Play"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEngineToggle(engine.id, 'active');
                        }}
                      >
                        Activate
                      </Button>
                    )}
                    {config.status === 'active' && (
                      <Button
                        size="sm"
                        variant="outline"
                        iconName="Pause"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEngineToggle(engine.id, 'standby');
                        }}
                      >
                        Standby
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Selected Engine Configuration */}
        <div className="border-t border-border pt-6">
          <h4 className="text-sm font-medium text-text-primary mb-4">
            Configure {engines.find(e => e.id === selectedEngine)?.name}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Performance Metrics */}
            <div className="bg-secondary-50 rounded-lg p-4">
              <h5 className="font-medium text-text-primary mb-3">Performance Metrics</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Tokens/Min</span>
                  <span className="font-medium text-text-primary">{currentEngine?.tokensPerMin?.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Avg Response Time</span>
                  <span className="font-medium text-text-primary">{currentEngine?.responseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Requests/Min</span>
                  <span className="font-medium text-text-primary">{currentEngine?.requestsPerMin}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-secondary">Current Load</span>
                  <span className={`font-medium ${getLoadColor(currentEngine?.load)}`}>{currentEngine?.load}%</span>
                </div>
              </div>
            </div>

            {/* Model Parameters */}
            <div className="bg-secondary-50 rounded-lg p-4">
              <h5 className="font-medium text-text-primary mb-3">Model Parameters</h5>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-text-secondary mb-1">Max Tokens</label>
                  <input
                    type="number"
                    value={currentEngine?.maxTokens}
                    onChange={(e) => handleParameterChange(selectedEngine, 'maxTokens', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-border rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-text-secondary mb-1">Temperature</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={currentEngine?.temperature}
                    onChange={(e) => handleParameterChange(selectedEngine, 'temperature', parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-text-secondary mt-1">
                    <span>0 (Conservative)</span>
                    <span>{currentEngine?.temperature}</span>
                    <span>1 (Creative)</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm text-text-secondary mb-1">Top P</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.05"
                    value={currentEngine?.topP}
                    onChange={(e) => handleParameterChange(selectedEngine, 'topP', parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-text-secondary mt-1">
                    <span>0</span>
                    <span>{currentEngine?.topP}</span>
                    <span>1</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Load Balancing Configuration */}
        <div className="border-t border-border pt-6">
          <h4 className="text-sm font-medium text-text-primary mb-4">Load Balancing & Failover</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-secondary-50 rounded-lg p-4">
              <h5 className="font-medium text-text-primary mb-3">Load Balancing</h5>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-text-secondary">Enable Load Balancing</span>
                  <button
                    onClick={() => handleLoadBalancingChange('enabled', !loadBalancing.enabled)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      loadBalancing.enabled ? 'bg-primary-500' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        loadBalancing.enabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                <div>
                  <label className="block text-sm text-text-secondary mb-1">Strategy</label>
                  <select
                    value={loadBalancing.strategy}
                    onChange={(e) => handleLoadBalancingChange('strategy', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md text-sm"
                  >
                    <option value="round-robin">Round Robin</option>
                    <option value="least-load">Least Load</option>
                    <option value="weighted">Weighted</option>
                    <option value="response-time">Best Response Time</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-secondary-50 rounded-lg p-4">
              <h5 className="font-medium text-text-primary mb-3">Failover Settings</h5>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm text-text-secondary mb-1">Failover Timeout (seconds)</label>
                  <input
                    type="number"
                    value={loadBalancing.failoverTimeout}
                    onChange={(e) => handleLoadBalancingChange('failoverTimeout', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-border rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-text-secondary mb-1">Health Check Interval (seconds)</label>
                  <input
                    type="number"
                    value={loadBalancing.healthCheckInterval}
                    onChange={(e) => handleLoadBalancingChange('healthCheckInterval', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-border rounded-md text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Emergency Controls */}
        <div className="border-t border-border pt-6">
          <h4 className="text-sm font-medium text-text-primary mb-4">Emergency Controls</h4>
          <div className="flex gap-4">
            <Button
              variant="outline"
              iconName="AlertTriangle"
              className="text-error-600 border-error-300 hover:bg-error-50"
            >
              Emergency Stop All
            </Button>
            <Button
              variant="outline"
              iconName="Wrench"
            >
              Maintenance Mode
            </Button>
            <Button
              variant="outline"
              iconName="RotateCcw"
            >
              Reset Configuration
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIEngineConfiguration;