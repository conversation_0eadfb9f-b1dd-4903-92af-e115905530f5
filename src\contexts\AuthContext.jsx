import React, { createContext, useContext, useEffect, useState } from "react";
import authService from "../utils/authService";

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(null);

  useEffect(() => {
    let isMounted = true;

    // Initialize auth state
    const initializeAuth = async () => {
      try {
        setLoading(true);
        setAuthError(null);

        const sessionResult = await authService.getSession();

        if (
          sessionResult?.success &&
          sessionResult?.data?.session?.user &&
          isMounted
        ) {
          const authUser = sessionResult.data.session.user;
          setUser(authUser);

          // Fetch user profile
          try {
            const profileResult = await authService.getUserProfile(authUser.id);
            if (profileResult?.success && profileResult?.data && isMounted) {
              setUserProfile(profileResult.data);
            }
          } catch (profileError) {
            console.warn('Failed to fetch user profile:', profileError);
          }
        } else {
          // No active session - set demo user for testing
          console.log('No active session, using demo user for testing');
          if (isMounted) {
            setUser({
              id: 'demo-user-123',
              email: '<EMAIL>',
              name: 'Demo User',
              role: 'patient'
            });
            setUserProfile({
              id: 'demo-profile-123',
              userId: 'demo-user-123',
              firstName: 'Demo',
              lastName: 'User',
              dateOfBirth: '1990-01-01',
              gender: 'other',
              emergencyContact: {
                name: 'Emergency Contact',
                phone: '+**********'
              }
            });
          }
        }
      } catch (error) {
        if (isMounted) {
          setAuthError("Failed to initialize authentication");
          console.log("Auth initialization error:", error);

          // Fallback to demo user on error
          setUser({
            id: 'demo-user-123',
            email: '<EMAIL>',
            name: 'Demo User',
            role: 'patient'
          });
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    let subscription;
    try {
      const {
        data: { subscription: authSubscription },
      } = authService.onAuthStateChange(async (event, session) => {
        if (!isMounted) return;

        setAuthError(null);

        if (event === "SIGNED_IN" && session?.user) {
          setUser(session.user);

          // Fetch user profile for signed in user
          authService.getUserProfile(session.user.id).then((profileResult) => {
            if (profileResult?.success && isMounted) {
              setUserProfile(profileResult.data);
            } else if (isMounted) {
              setAuthError(profileResult?.error || "Failed to load user profile");
            }
          });
        } else if (event === "SIGNED_OUT") {
          setUser(null);
          setUserProfile(null);
        } else if (event === "TOKEN_REFRESHED" && session?.user) {
          setUser(session.user);
        }
      });

      subscription = authSubscription;
    } catch (error) {
      console.warn('Auth state change listener setup failed:', error);
    }

    return () => {
      isMounted = false;
      subscription?.unsubscribe();
    };
  }, []);

  // Full authentication methods with Supabase integration
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setAuthError(null);

      const result = await authService.signIn(email, password);

      if (result.success) {
        setUser(result.data.user);

        // Fetch user profile
        const profileResult = await authService.getUserProfile(result.data.user.id);
        if (profileResult?.success) {
          setUserProfile(profileResult.data);
        }
      } else {
        setAuthError(result.error);
      }

      return result;
    } catch (error) {
      const errorMessage = "Sign in failed";
      setAuthError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email, password, userData) => {
    try {
      setLoading(true);
      setAuthError(null);

      const result = await authService.signUp(email, password, userData);

      if (result.success) {
        setUser(result.data.user);

        // Create user profile
        if (userData) {
          const profileResult = await authService.createUserProfile(result.data.user.id, userData);
          if (profileResult?.success) {
            setUserProfile(profileResult.data);
          }
        }
      } else {
        setAuthError(result.error);
      }

      return result;
    } catch (error) {
      const errorMessage = "Sign up failed";
      setAuthError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const result = await authService.signOut();

      if (result.success) {
        setUser(null);
        setUserProfile(null);
        setAuthError(null);
      }

      return result;
    } catch (error) {
      const errorMessage = "Sign out failed";
      setAuthError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      setAuthError(null);

      if (!user) {
        throw new Error("No authenticated user");
      }

      const result = await authService.updateUserProfile(user.id, profileData);

      if (result.success) {
        setUserProfile(result.data);
      } else {
        setAuthError(result.error);
      }

      return result;
    } catch (error) {
      const errorMessage = "Profile update failed";
      setAuthError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email) => {
    try {
      setLoading(true);
      setAuthError(null);

      const result = await authService.resetPassword(email);

      if (!result.success) {
        setAuthError(result.error);
      }

      return result;
    } catch (error) {
      const errorMessage = "Password reset failed";
      setAuthError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Additional authentication utilities
  const refreshSession = async () => {
    try {
      const result = await authService.refreshSession();
      if (result.success && result.data.user) {
        setUser(result.data.user);
      }
      return result;
    } catch (error) {
      console.error('Session refresh failed:', error);
      return { success: false, error: 'Session refresh failed' };
    }
  };

  const checkSession = async () => {
    try {
      const result = await authService.getSession();
      return result.success && result.data.session?.user;
    } catch (error) {
      console.error('Session check failed:', error);
      return false;
    }
  };

  const value = {
    // State
    user,
    userProfile,
    loading,
    authError,

    // Authentication methods
    signIn,
    signUp,
    signOut,
    updateProfile,
    resetPassword,
    refreshSession,
    checkSession,

    // Utility methods
    isAuthenticated: !!user,
    hasRole: (role) => user?.role === role,
    hasPermission: (permission) => {
      // Enhanced permission checking
      if (!user) return false;

      // Emergency responders have all permissions
      if (user.role === 'emergency_responder') return true;

      // Admin has most permissions
      if (user.role === 'admin') return true;

      // Healthcare providers have medical permissions
      if (user.role === 'healthcare_provider') {
        return ['read_medical_data', 'write_medical_data', 'consultation_access'].includes(permission);
      }

      // Patients have limited permissions
      if (user.role === 'patient') {
        return ['read_own_data', 'consultation_access', 'emergency_access'].includes(permission);
      }

      return false;
    },

    // Emergency access
    hasEmergencyAccess: () => {
      return user?.role === 'emergency_responder' || user?.role === 'healthcare_provider';
    },

    // Profile utilities
    getFullName: () => {
      if (userProfile?.firstName && userProfile?.lastName) {
        return `${userProfile.firstName} ${userProfile.lastName}`;
      }
      return user?.name || user?.email || 'User';
    },

    // Session utilities
    getSessionInfo: () => ({
      userId: user?.id,
      userRole: user?.role,
      isAuthenticated: !!user,
      profileComplete: !!(userProfile?.firstName && userProfile?.lastName),
      emergencyContact: userProfile?.emergencyContact
    })
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
