# VoiceHealth AI Database Setup Guide

## Prerequisites
1. Supabase CLI installed globally
2. Supabase project linked to your local environment
3. Environment variables properly configured

## Setup Instructions

### 1. Install Supabase CLI (if not already installed)
```bash
npm install -g supabase
```

### 2. Login to Supabase
```bash
supabase login
```

### 3. Link Your Project
```bash
supabase link --project-ref YOUR_PROJECT_REF
```
*Replace YOUR_PROJECT_REF with your actual Supabase project reference*

### 4. Run Migrations
```bash
# Run all pending migrations
supabase db push

# Or run migrations individually
supabase migration up
```

### 5. Verify Migration Success
```bash
# Check migration status
supabase migration list

# Connect to database and verify tables
supabase db reset --debug
```

## Alternative: Manual SQL Execution

If CLI method doesn't work, execute the migration manually:

### 1. Open Supabase Dashboard
- Go to your Supabase project dashboard
- Navigate to SQL Editor

### 2. Execute Migration Files
Copy and paste the contents of these files in order:

1. `supabase/migrations/20241216120000_auth_and_consultation_system.sql`
2. `supabase/migrations/20241217000000_payment_system.sql`

### 3. Verify Table Creation
Run this query to verify the tables were created:

```sql
-- Check if subscription_plans table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'subscription_plans';

-- Verify sample data
SELECT * FROM public.subscription_plans LIMIT 5;
```

## Environment Variables Verification

Ensure these variables are set in your `.env` file:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key
VITE_PAYSTACK_SECRET_KEY=your_paystack_secret_key
```

## Troubleshooting

### If migration fails:
1. Check Supabase connection
2. Verify project permissions
3. Ensure no conflicting tables exist
4. Review migration file syntax

### If tables exist but data is missing:
```sql
-- Check if default plans were inserted
SELECT COUNT(*) FROM public.subscription_plans;

-- If count is 0, re-run the INSERT statements from the migration
```

## Verification Steps

After successful migration, verify:

1. **Tables Created**: subscription_plans, user_subscriptions, payment_transactions, etc.
2. **Sample Data**: Default subscription plans should be populated
3. **RLS Policies**: Row Level Security policies should be active
4. **Functions**: Helper functions should be created

## Success Indicators

✅ No more "relation does not exist" errors
✅ Payment plans page loads without fallback warning
✅ Subscription plans display actual database data
✅ All database functions work correctly

For additional support, check the Supabase documentation or contact support.