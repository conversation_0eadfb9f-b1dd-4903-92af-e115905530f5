# VoiceHealth AI - Demo Account Credentials

This document contains the login credentials for testing the VoiceHealth AI application.

## Demo Accounts

### 🩺 Patient Account
- **Email**: `<EMAIL>`
- **Password**: `HealthDemo123`
- **Role**: Patient
- **Use Case**: Test patient consultation flows, booking appointments, viewing health records

### 👨‍⚕️ Provider Account
- **Email**: `<EMAIL>`
- **Password**: `ProviderDemo123`
- **Role**: Healthcare Provider
- **Use Case**: Test provider dashboard, patient management, consultation handling

### 🔧 Admin Account
- **Email**: `<EMAIL>`
- **Password**: `AdminDemo123`
- **Role**: Administrator
- **Use Case**: Test system administration, user management, platform analytics

## Testing the Accounts

### Quick Login Test
You can test all demo accounts at once by running:
```bash
npm run test-demo
```

### Individual Account Testing
1. Navigate to the login page
2. Use any of the credential pairs above
3. Each account will redirect to the appropriate dashboard based on role

## Account Management

### Reseed Demo Data
If you need to recreate the demo accounts:
```bash
npm run seed-demo
```

### Cleanup Demo Accounts
To remove all demo accounts:
```bash
npm run cleanup-demo
```

## Security Notes

⚠️ **Important**: These are test accounts only and should never be used in production environments.

- All demo passwords use simple, predictable patterns
- Demo accounts have elevated permissions for testing
- In production, implement proper password policies and MFA
- Demo accounts should be disabled/removed before production deployment

## Troubleshooting

### Account Not Working?
1. Verify the account exists by running `npm run test-demo`
2. Check that your Supabase configuration is correct
3. Ensure RLS policies allow demo account access
4. Re-run `npm run seed-demo` to recreate accounts if needed

### Login Fails?
1. Check browser console for authentication errors
2. Verify Supabase URL and anon key in environment variables
3. Ensure user_profiles table exists and has proper structure
4. Check that AuthContext is properly configured

---

*Last updated: December 2024*
*VoiceHealth AI Demo Environment*
