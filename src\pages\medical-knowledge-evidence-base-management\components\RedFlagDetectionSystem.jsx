import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RedFlagDetectionSystem = ({ searchQuery }) => {
  const [redFlagPatterns, setRedFlagPatterns] = useState([]);
  const [selectedPattern, setSelectedPattern] = useState(null);
  const [filterUrgency, setFilterUrgency] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  const urgencyLevels = [
    { id: 'all', label: 'All Levels', color: 'bg-gray-100 text-gray-700' },
    { id: 'emergency', label: 'Emergency', color: 'bg-error-100 text-error-700' },
    { id: 'urgent', label: 'Urgent', color: 'bg-warning-100 text-warning-700' },
    { id: 'routine', label: 'Routine', color: 'bg-primary-100 text-primary-700' }
  ];

  const mockRedFlagPatterns = [
    {
      id: 'chest_pain_emergency',
      name: 'Acute Chest Pain - Emergency',
      urgency: 'emergency',
      category: 'cardiovascular',
      triggers: [
        'Crushing chest pain',
        'Pain radiating to left arm/jaw',
        'Shortness of breath',
        'Diaphoresis',
        'Nausea/vomiting'
      ],
      combinations: [
        {
          pattern: 'Chest pain + Shortness of breath + Diaphoresis',
          confidence: 0.95,
          action: 'Immediate EMS activation'
        },
        {
          pattern: 'Chest pain + Left arm radiation + Age >50',
          confidence: 0.92,
          action: 'Emergency department referral'
        }
      ],
      escalationProtocol: {
        immediate: 'Call emergency services',
        within5min: 'Administer aspirin if no contraindications',
        within15min: 'Prepare for advanced life support',
        humanHandoff: 'Emergency physician consultation required'
      },
      culturalConsiderations: 'May present atypically in women and elderly',
      africaSpecific: 'Consider rheumatic heart disease in young adults',
      lastUpdated: '2024-01-15'
    },
    {
      id: 'fever_malaria_urgent',
      name: 'Fever Pattern - Malaria Alert',
      urgency: 'urgent',
      category: 'infectious',
      triggers: [
        'Fever >38.5°C',
        'Chills/rigors',
        'Headache',
        'Recent travel to endemic area',
        'No antimalarial prophylaxis'
      ],
      combinations: [
        {
          pattern: 'Fever + Chills + Travel history + No prophylaxis',
          confidence: 0.88,
          action: 'Urgent malaria testing'
        },
        {
          pattern: 'High fever + Confusion + Recent travel',
          confidence: 0.94,
          action: 'Immediate hospital referral'
        }
      ],
      escalationProtocol: {
        immediate: 'Obtain travel history',
        within30min: 'Rapid diagnostic test or microscopy',
        within2hours: 'Start antimalarial if positive',
        humanHandoff: 'Infectious disease specialist if severe'
      },
      culturalConsiderations: 'Traditional remedies may delay presentation',
      africaSpecific: 'High index of suspicion in endemic areas',
      lastUpdated: '2024-01-12'
    },
    {
      id: 'respiratory_tb_routine',
      name: 'Chronic Cough - TB Screening',
      urgency: 'routine',
      category: 'respiratory',
      triggers: [
        'Cough >2 weeks',
        'Weight loss',
        'Night sweats',
        'Fatigue',
        'Low-grade fever'
      ],
      combinations: [
        {
          pattern: 'Chronic cough + Weight loss + Night sweats',
          confidence: 0.75,
          action: 'TB screening required'
        },
        {
          pattern: 'Cough + Hemoptysis + Weight loss',
          confidence: 0.85,
          action: 'Urgent TB investigation'
        }
      ],
      escalationProtocol: {
        immediate: 'Isolate if suspected TB',
        within24hours: 'Sputum collection for microscopy',
        within7days: 'GeneXpert testing',
        humanHandoff: 'TB specialist if positive'
      },
      culturalConsiderations: 'Stigma may affect disclosure',
      africaSpecific: 'HIV co-infection screening essential',
      lastUpdated: '2024-01-10'
    },
    {
      id: 'headache_emergency',
      name: 'Severe Headache - Emergency',
      urgency: 'emergency',
      category: 'neurological',
      triggers: [
        'Sudden severe headache',
        'Worst headache of life',
        'Neck stiffness',
        'Photophobia',
        'Altered consciousness'
      ],
      combinations: [
        {
          pattern: 'Sudden severe headache + Neck stiffness + Photophobia',
          confidence: 0.96,
          action: 'Immediate hospital referral'
        },
        {
          pattern: 'Headache + Fever + Altered consciousness',
          confidence: 0.93,
          action: 'Emergency meningitis protocol'
        }
      ],
      escalationProtocol: {
        immediate: 'Call emergency services',
        within10min: 'IV access and vital signs',
        within30min: 'CT scan or lumbar puncture',
        humanHandoff: 'Neurologist consultation required'
      },
      culturalConsiderations: 'May attribute to spiritual causes',
      africaSpecific: 'Consider cerebral malaria in endemic areas',
      lastUpdated: '2024-01-08'
    }
  ];

  useEffect(() => {
    const loadRedFlagPatterns = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setRedFlagPatterns(mockRedFlagPatterns);
      setIsLoading(false);
    };

    loadRedFlagPatterns();
  }, []);

  const filteredPatterns = redFlagPatterns.filter(pattern => {
    const matchesUrgency = filterUrgency === 'all' || pattern.urgency === filterUrgency;
    const matchesSearch = !searchQuery || 
      pattern.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pattern.triggers?.some(trigger => trigger.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesUrgency && matchesSearch;
  });

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'emergency': return 'bg-error-100 text-error-700';
      case 'urgent': return 'bg-warning-100 text-warning-700';
      case 'routine': return 'bg-primary-100 text-primary-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getUrgencyIcon = (urgency) => {
    switch (urgency) {
      case 'emergency': return 'AlertTriangle';
      case 'urgent': return 'Clock';
      case 'routine': return 'CheckCircle';
      default: return 'Info';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-error-500 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading red flag detection patterns...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Urgency Filter */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <div className="flex flex-wrap gap-2">
          {urgencyLevels.map((level) => (
            <button
              key={level.id}
              onClick={() => setFilterUrgency(level.id)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                filterUrgency === level.id
                  ? level.color.replace('100', '500').replace('700', 'white')
                  : level.color + ' hover:opacity-80'
              }`}
            >
              {level.label}
            </button>
          ))}
        </div>
      </div>

      {/* Red Flag Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="AlertTriangle" size={24} className="text-error-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredPatterns.filter(p => p.urgency === 'emergency').length}
              </p>
              <p className="text-sm text-text-secondary">Emergency Patterns</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Clock" size={24} className="text-warning-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredPatterns.filter(p => p.urgency === 'urgent').length}
              </p>
              <p className="text-sm text-text-secondary">Urgent Patterns</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Target" size={24} className="text-success-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {Math.round(filteredPatterns.reduce((sum, p) => 
                  sum + Math.max(...(p.combinations?.map(c => c.confidence) || [0])), 0) / 
                  filteredPatterns.length * 100) || 0}%
              </p>
              <p className="text-sm text-text-secondary">Avg Confidence</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Users" size={24} className="text-primary-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredPatterns.reduce((sum, p) => sum + (p.combinations?.length || 0), 0)}
              </p>
              <p className="text-sm text-text-secondary">Total Combinations</p>
            </div>
          </div>
        </div>
      </div>

      {/* Red Flag Patterns List */}
      <div className="space-y-4">
        {filteredPatterns.map((pattern) => (
          <div
            key={pattern.id}
            className={`bg-surface rounded-lg border transition-all ${
              selectedPattern?.id === pattern.id
                ? 'border-primary-500 shadow-medium'
                : 'border-border hover:border-primary-300 hover:shadow-small'
            }`}
          >
            <div 
              className="p-6 cursor-pointer"
              onClick={() => setSelectedPattern(selectedPattern?.id === pattern.id ? null : pattern)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <Icon name={getUrgencyIcon(pattern.urgency)} size={20} className={`mr-2 ${
                      pattern.urgency === 'emergency' ? 'text-error-500' :
                      pattern.urgency === 'urgent' ? 'text-warning-500' : 'text-primary-500'
                    }`} />
                    <h3 className="text-lg font-semibold text-text-primary">
                      {pattern.name}
                    </h3>
                  </div>
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getUrgencyColor(pattern.urgency)}`}>
                      {pattern.urgency.charAt(0).toUpperCase() + pattern.urgency.slice(1)}
                    </span>
                    <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium">
                      {pattern.category}
                    </span>
                  </div>
                </div>
                <Icon 
                  name={selectedPattern?.id === pattern.id ? "ChevronUp" : "ChevronDown"} 
                  size={20} 
                  className="text-text-secondary" 
                />
              </div>

              {/* Trigger Symptoms */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-text-primary mb-2">Key Triggers</h4>
                <div className="flex flex-wrap gap-2">
                  {pattern.triggers?.map((trigger, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >
                      {trigger}
                    </span>
                  ))}
                </div>
              </div>

              {/* Expanded Details */}
              {selectedPattern?.id === pattern.id && (
                <div className="mt-6 pt-6 border-t border-border space-y-6">
                  {/* Symptom Combinations */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Critical Combinations</h4>
                    <div className="space-y-3">
                      {pattern.combinations?.map((combination, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <p className="font-medium text-text-primary text-sm">{combination.pattern}</p>
                              <p className="text-sm text-text-secondary mt-1">
                                <Icon name="Activity" size={16} className="inline mr-1" />
                                {combination.action}
                              </p>
                            </div>
                            <div className="text-right">
                              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                                combination.confidence > 0.9 ? 'bg-success-100 text-success-700' :
                                combination.confidence > 0.8 ? 'bg-warning-100 text-warning-700': 'bg-error-100 text-error-700'
                              }`}>
                                {Math.round(combination.confidence * 100)}% confidence
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Escalation Protocol */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Escalation Protocol</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(pattern.escalationProtocol || {}).map(([timeframe, action]) => (
                        <div key={timeframe} className="flex items-start">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 text-xs font-medium ${
                            timeframe === 'immediate' ? 'bg-error-100 text-error-700' : timeframe.includes('min') ? 'bg-warning-100 text-warning-700' :
                            'bg-primary-100 text-primary-700'
                          }`}>
                            <Icon name="Clock" size={12} />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-text-primary capitalize">
                              {timeframe.replace(/([A-Z])/g, ' $1').trim()}
                            </p>
                            <p className="text-sm text-text-secondary">{action}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Cultural & Regional Considerations */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Cultural Considerations</h4>
                      <p className="text-sm text-text-secondary">{pattern.culturalConsiderations}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Africa-Specific Notes</h4>
                      <p className="text-sm text-text-secondary">{pattern.africaSpecific}</p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" iconName="Edit">
                      Edit Pattern
                    </Button>
                    <Button variant="outline" size="sm" iconName="TestTube">
                      Test Algorithm
                    </Button>
                    <Button variant="outline" size="sm" iconName="BarChart3">
                      View Analytics
                    </Button>
                    <Button variant="outline" size="sm" iconName="Users">
                      Training Module
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredPatterns.length === 0 && (
        <div className="text-center py-12">
          <Icon name="AlertTriangle" size={48} className="text-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">No red flag patterns found</h3>
          <p className="text-text-secondary">
            Try selecting a different urgency level or adjusting your search
          </p>
        </div>
      )}

      {/* Add New Pattern Button */}
      <div className="flex justify-center pt-6">
        <Button variant="primary" iconName="Plus">
          Add New Red Flag Pattern
        </Button>
      </div>
    </div>
  );
};

export default RedFlagDetectionSystem;