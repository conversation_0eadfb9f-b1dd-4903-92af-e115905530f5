/**
 * Offline Status Indicator Component
 * Shows network status and sync progress for PWA
 */

import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Cloud, CloudOff, Loader2, CheckCircle, AlertTriangle } from 'lucide-react';
import backgroundSync from '../services/backgroundSync';

const OfflineIndicator = ({ className = '', showDetails = false }) => {
  const [networkStatus, setNetworkStatus] = useState({
    isOnline: navigator.onLine,
    syncStatus: 'idle',
    pendingItems: 0,
    failedItems: 0,
    lastSync: null
  });

  const [syncProgress, setSyncProgress] = useState({
    isVisible: false,
    current: 0,
    total: 0,
    status: 'idle'
  });

  useEffect(() => {
    // Network status listeners
    const handleOnline = () => {
      setNetworkStatus(prev => ({ ...prev, isOnline: true }));
      refreshSyncStatus();
    };

    const handleOffline = () => {
      setNetworkStatus(prev => ({ 
        ...prev, 
        isOnline: false, 
        syncStatus: 'offline' 
      }));
    };

    // Background sync event listeners
    const handleSyncEvent = (event) => {
      const { type, data } = event.detail;
      
      switch (type) {
        case 'sync-started':
          setSyncProgress({
            isVisible: true,
            current: 0,
            total: data.total || 0,
            status: 'syncing'
          });
          setNetworkStatus(prev => ({ ...prev, syncStatus: 'syncing' }));
          break;
          
        case 'sync-progress':
          setSyncProgress(prev => ({
            ...prev,
            current: data.completed || 0,
            total: data.total || prev.total
          }));
          break;
          
        case 'sync-completed':
          setSyncProgress({
            isVisible: false,
            current: 0,
            total: 0,
            status: 'completed'
          });
          setNetworkStatus(prev => ({ 
            ...prev, 
            syncStatus: 'completed',
            lastSync: new Date().toISOString()
          }));
          
          // Hide completed status after delay
          setTimeout(() => {
            setNetworkStatus(prev => ({ ...prev, syncStatus: 'idle' }));
          }, 3000);
          break;
          
        case 'sync-failed':
          setSyncProgress({
            isVisible: false,
            current: 0,
            total: 0,
            status: 'failed'
          });
          setNetworkStatus(prev => ({ ...prev, syncStatus: 'failed' }));
          break;
      }
    };

    // Service worker update listeners
    const handleSWUpdate = (event) => {
      const { type } = event.data;
      
      if (type === 'SW_UPDATE_AVAILABLE') {
        setNetworkStatus(prev => ({ ...prev, updateAvailable: true }));
      }
    };

    // Set up event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('background-sync', handleSyncEvent);
    
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleSWUpdate);
    }

    // Initial sync status check
    refreshSyncStatus();

    // Periodic sync status refresh
    const statusInterval = setInterval(refreshSyncStatus, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('background-sync', handleSyncEvent);
      
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleSWUpdate);
      }
      
      clearInterval(statusInterval);
    };
  }, []);

  const refreshSyncStatus = async () => {
    try {
      const status = await backgroundSync.getSyncStatus();
      setNetworkStatus(prev => ({
        ...prev,
        pendingItems: status.pendingItems,
        failedItems: status.failedItems,
        lastSync: status.lastSync,
        syncInProgress: status.syncInProgress
      }));
    } catch (error) {
      console.error('Failed to refresh sync status:', error);
    }
  };

  const handleForceSync = async () => {
    if (!networkStatus.isOnline) {
      return;
    }

    try {
      setNetworkStatus(prev => ({ ...prev, syncStatus: 'syncing' }));
      await backgroundSync.forcSync();
    } catch (error) {
      console.error('Force sync failed:', error);
      setNetworkStatus(prev => ({ ...prev, syncStatus: 'failed' }));
    }
  };

  const getStatusIcon = () => {
    if (!networkStatus.isOnline) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }

    switch (networkStatus.syncStatus) {
      case 'syncing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        if (networkStatus.pendingItems > 0) {
          return <CloudOff className="w-4 h-4 text-orange-500" />;
        }
        return <Cloud className="w-4 h-4 text-green-500" />;
    }
  };

  const getStatusText = () => {
    if (!networkStatus.isOnline) {
      return 'Offline';
    }

    switch (networkStatus.syncStatus) {
      case 'syncing':
        return 'Syncing...';
      case 'completed':
        return 'Synced';
      case 'failed':
        return 'Sync failed';
      default:
        if (networkStatus.pendingItems > 0) {
          return `${networkStatus.pendingItems} pending`;
        }
        return 'Online';
    }
  };

  const getStatusColor = () => {
    if (!networkStatus.isOnline) {
      return 'text-red-600 bg-red-50 border-red-200';
    }

    switch (networkStatus.syncStatus) {
      case 'syncing':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        if (networkStatus.pendingItems > 0) {
          return 'text-orange-600 bg-orange-50 border-orange-200';
        }
        return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const formatLastSync = (lastSync) => {
    if (!lastSync) return 'Never';
    
    const date = new Date(lastSync);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      {/* Main Status Indicator */}
      <div className={`
        inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border
        ${getStatusColor()}
      `}>
        {getStatusIcon()}
        <span>{getStatusText()}</span>
      </div>

      {/* Sync Progress Bar */}
      {syncProgress.isVisible && (
        <div className="flex items-center space-x-2">
          <div className="w-24 bg-gray-200 rounded-full h-1">
            <div 
              className="bg-blue-500 h-1 rounded-full transition-all duration-300"
              style={{ 
                width: syncProgress.total > 0 
                  ? `${(syncProgress.current / syncProgress.total) * 100}%` 
                  : '0%' 
              }}
            />
          </div>
          <span className="text-xs text-gray-500">
            {syncProgress.current}/{syncProgress.total}
          </span>
        </div>
      )}

      {/* Detailed Status (if enabled) */}
      {showDetails && (
        <div className="text-xs text-gray-500 space-y-1">
          {networkStatus.pendingItems > 0 && (
            <div>
              Pending: {networkStatus.pendingItems}
              {networkStatus.isOnline && (
                <button
                  onClick={handleForceSync}
                  className="ml-2 text-blue-500 hover:text-blue-700 underline"
                  disabled={networkStatus.syncStatus === 'syncing'}
                >
                  Sync now
                </button>
              )}
            </div>
          )}
          
          {networkStatus.failedItems > 0 && (
            <div className="text-red-500">
              Failed: {networkStatus.failedItems}
            </div>
          )}
          
          <div>
            Last sync: {formatLastSync(networkStatus.lastSync)}
          </div>
        </div>
      )}

      {/* Update Available Notification */}
      {networkStatus.updateAvailable && (
        <div className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded border border-blue-200">
          <button
            onClick={() => {
              if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then(registration => {
                  registration.waiting?.postMessage({ type: 'SKIP_WAITING' });
                });
              }
            }}
            className="underline hover:no-underline"
          >
            Update available
          </button>
        </div>
      )}
    </div>
  );
};

export default OfflineIndicator;
