import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';

const RealTimeTranscription = ({ 
  transcriptionData = [],
  isListening = false,
  isProcessing = false,
  onClearTranscription = () => {},
  className = ''
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const transcriptionRef = useRef(null);
  const endRef = useRef(null);

  useEffect(() => {
    if (isAutoScroll && endRef.current) {
      endRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [transcriptionData, isAutoScroll]);

  useEffect(() => {
    if (isListening) {
      setDisplayText('Listening...');
    } else if (isProcessing) {
      setDisplayText('Processing your input...');
    } else {
      setDisplayText('');
    }
  }, [isListening, isProcessing]);

  const handleScroll = () => {
    if (transcriptionRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = transcriptionRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setIsAutoScroll(isAtBottom);
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  };

  const getSpeakerIcon = (speaker) => {
    if (speaker === 'user') return 'User';
    if (speaker === 'system') return 'Bot';
    return 'Stethoscope';
  };

  const getSpeakerColor = (speaker) => {
    if (speaker === 'user') return 'primary';
    if (speaker === 'system') return 'secondary';
    return 'success';
  };

  return (
    <div className={`bg-surface border border-border rounded-xl ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <Icon name="MessageSquare" size={20} color="var(--color-text-primary)" />
          <h3 className="font-semibold text-text-primary font-heading">
            Live Transcription
          </h3>
          {(isListening || isProcessing) && (
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-success-600 font-medium">
                {isListening ? 'Recording' : 'Processing'}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Auto-scroll toggle */}
          <button
            onClick={() => setIsAutoScroll(!isAutoScroll)}
            className={`p-2 rounded-lg transition-fast ${
              isAutoScroll 
                ? 'bg-primary-50 text-primary-600' :'bg-secondary-50 text-text-secondary hover:bg-secondary-100'
            }`}
            title={isAutoScroll ? 'Disable auto-scroll' : 'Enable auto-scroll'}
          >
            <Icon name="ArrowDown" size={16} />
          </button>

          {/* Clear transcription */}
          <button
            onClick={onClearTranscription}
            className="p-2 rounded-lg bg-secondary-50 text-text-secondary hover:bg-secondary-100 transition-fast"
            title="Clear transcription"
            disabled={transcriptionData.length === 0}
          >
            <Icon name="Trash2" size={16} />
          </button>
        </div>
      </div>

      {/* Transcription Content */}
      <div 
        ref={transcriptionRef}
        onScroll={handleScroll}
        className="h-80 overflow-y-auto p-4 space-y-4"
      >
        {transcriptionData.length === 0 && !displayText ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Icon name="MessageCircle" size={48} color="var(--color-text-muted)" />
            <p className="text-text-muted font-caption mt-4">
              Start speaking to see live transcription
            </p>
            <p className="text-xs text-text-muted mt-2">
              Your conversation will appear here in real-time
            </p>
          </div>
        ) : (
          <>
            {/* Transcription Messages */}
            {transcriptionData.map((entry, index) => (
              <div 
                key={entry.id || index}
                className={`flex space-x-3 ${
                  entry.speaker === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}
              >
                {/* Speaker Avatar */}
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  entry.speaker === 'user' ?'bg-primary-50' 
                    : entry.speaker === 'system' ?'bg-secondary-50' :'bg-success-50'
                }`}>
                  <Icon 
                    name={getSpeakerIcon(entry.speaker)}
                    size={16}
                    color={`var(--color-${getSpeakerColor(entry.speaker)})`}
                  />
                </div>

                {/* Message Content */}
                <div className={`flex-1 max-w-xs lg:max-w-md ${
                  entry.speaker === 'user' ? 'text-right' : 'text-left'
                }`}>
                  <div className={`inline-block p-3 rounded-lg ${
                    entry.speaker === 'user' ?'bg-primary-500 text-white'
                      : entry.speaker === 'system' ?'bg-secondary-100 text-text-primary' :'bg-success-50 text-success-700'
                  }`}>
                    {/* Speaker Name */}
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`text-xs font-medium ${
                        entry.speaker === 'user' ? 'text-primary-100' : 'text-text-secondary'
                      }`}>
                        {entry.speakerName || 
                         (entry.speaker === 'user' ? 'You' : 
                          entry.speaker === 'system' ? 'System' : 'AI Agent')}
                      </span>
                      {entry.confidence && (
                        <span className={`text-xs ${
                          entry.speaker === 'user' ? 'text-primary-200' : 'text-text-muted'
                        }`}>
                          {Math.round(entry.confidence * 100)}%
                        </span>
                      )}
                    </div>

                    {/* Message Text */}
                    <p className="text-sm leading-relaxed">
                      {entry.text}
                    </p>

                    {/* Timestamp */}
                    <div className={`text-xs mt-1 ${
                      entry.speaker === 'user' ? 'text-primary-200' : 'text-text-muted'
                    }`}>
                      {formatTimestamp(entry.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Current Input Display */}
            {displayText && (
              <div className="flex space-x-3">
                <div className="w-8 h-8 rounded-full bg-warning-50 flex items-center justify-center flex-shrink-0">
                  <Icon name="Mic" size={16} color="var(--color-warning)" />
                </div>
                <div className="flex-1">
                  <div className="inline-block p-3 bg-warning-50 rounded-lg">
                    <p className="text-sm text-warning-700 animate-pulse">
                      {displayText}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div ref={endRef} />
          </>
        )}
      </div>

      {/* Footer Stats */}
      {transcriptionData.length > 0 && (
        <div className="px-4 py-3 border-t border-border bg-secondary-50">
          <div className="flex items-center justify-between text-xs text-text-secondary">
            <span>
              {transcriptionData.length} message{transcriptionData.length !== 1 ? 's' : ''}
            </span>
            <span>
              {transcriptionData.reduce((acc, entry) => acc + (entry.text?.length || 0), 0)} characters
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default RealTimeTranscription;