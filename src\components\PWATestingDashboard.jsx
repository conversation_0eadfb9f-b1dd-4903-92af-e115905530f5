/**
 * PWA Testing Dashboard - Comprehensive Testing Interface
 * Integrates all PWA testing components into a unified dashboard
 */

import React, { useState, useEffect, useCallback } from 'react';
import Button from './ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/Tabs';
import { 
  Activity, AlertCircle, CheckCircle, Clock, RefreshCw, 
  Wifi, WifiOff, Download, RotateCcw, Mic, Database, Play,
  BarChart3, Settings, TestTube, Zap, Monitor, Globe
} from 'lucide-react';

// Import testing utilities and components
import { 
  NetworkSimulator, 
  PWATestSuite, 
  runPWAHealthCheck,
  IndexedDBTestHelper,
  ServiceWorkerTestHelper 
} from '../utils/pwaTestUtils';
import { 
  BundleSizeMonitor, 
  LoadingPerformanceMonitor, 
  PWAPerformanceMonitor,
  getPerformanceMonitor 
} from '../utils/performanceMonitor';
import PWADemo from './PWADemo';
import PWAInstallPrompt from './PWAInstallPrompt';
import { OverviewTab } from './tabs/OverviewTab';
import { TestingSuiteTab } from './tabs/TestingSuiteTab';
import { PerformanceTab } from './tabs/PerformanceTab';
import { InstallationTab } from './tabs/InstallationTab';

// Import hooks
import usePWA from '../hooks/usePWA';
import { useSpeechSocket } from '../hooks/useSpeechSocket';

const PWATestingDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [performanceData, setPerformanceData] = useState(null);
  const [healthStatus, setHealthStatus] = useState(null);
  const [networkSimulator] = useState(() => new NetworkSimulator());
  const [testSuite] = useState(() => new PWATestSuite());

  // PWA and Speech hooks
  const pwa = usePWA();
  const speech = useSpeechSocket({ sessionId: `dashboard-${Date.now()}` });

  // System status state
  const [systemStatus, setSystemStatus] = useState({
    networkStatus: navigator.onLine ? 'online' : 'offline',
    serviceWorker: 'unknown',
    indexedDB: 'unknown',
    cacheStatus: 'unknown',
    syncStatus: 'unknown'
  });

  // Update system status periodically
  useEffect(() => {
    const updateSystemStatus = async () => {
      try {
        const [swStatus, dbHelper] = await Promise.all([
          ServiceWorkerTestHelper.getStatus(),
          new IndexedDBTestHelper()
        ]);

        setSystemStatus(prev => ({
          ...prev,
          networkStatus: navigator.onLine ? 'online' : 'offline',
          serviceWorker: swStatus.active ? 'active' : 'inactive',
          indexedDB: 'available',
          cacheStatus: swStatus.caches?.length > 0 ? 'populated' : 'empty'
        }));
      } catch (error) {
        console.error('Failed to update system status:', error);
      }
    };

    updateSystemStatus();
    const interval = setInterval(updateSystemStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  // Run health check
  const handleHealthCheck = useCallback(async () => {
    try {
      const health = await runPWAHealthCheck();
      setHealthStatus(health);
    } catch (error) {
      setHealthStatus({ error: error.message });
    }
  }, []);

  // Run automated tests
  const handleRunTests = useCallback(async () => {
    setIsRunningTests(true);
    try {
      const results = await testSuite.runAllTests(speech);
      setTestResults(results);
    } catch (error) {
      console.error('Test execution failed:', error);
      setTestResults([{ name: 'Test Suite', passed: false, error: error.message }]);
    } finally {
      setIsRunningTests(false);
    }
  }, [testSuite, speech]);

  // Update performance data
  useEffect(() => {
    const updatePerformanceData = async () => {
      try {
        const monitor = getPerformanceMonitor();
        if (monitor) {
          const data = await monitor.getFullReport();
          setPerformanceData(data);
        }
      } catch (error) {
        console.error('Failed to get performance data:', error);
      }
    };

    updatePerformanceData();
    const interval = setInterval(updatePerformanceData, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            PWA Testing Dashboard
          </h1>
          <p className="text-gray-600">
            Comprehensive testing suite for VoiceHealth AI PWA capabilities
          </p>
        </div>

        {/* Quick Actions */}
        <div className="mb-6 flex flex-wrap gap-4">
          <Button
            onClick={handleHealthCheck}
            iconName="Activity"
            variant="secondary"
            size="sm"
          >
            Health Check
          </Button>
          <Button
            onClick={handleRunTests}
            iconName="TestTube"
            loading={isRunningTests}
            disabled={isRunningTests}
            size="sm"
          >
            {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
          </Button>
          <Button
            onClick={() => networkSimulator.goOffline()}
            iconName="WifiOff"
            variant="warning"
            size="sm"
          >
            Simulate Offline
          </Button>
          <Button
            onClick={() => networkSimulator.goOnline()}
            iconName="Wifi"
            variant="success"
            size="sm"
          >
            Simulate Online
          </Button>
        </div>

        {/* System Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <StatusCard
            title="Network"
            status={systemStatus.networkStatus}
            icon={systemStatus.networkStatus === 'online' ? Wifi : WifiOff}
          />
          <StatusCard
            title="Service Worker"
            status={systemStatus.serviceWorker}
            icon={Settings}
          />
          <StatusCard
            title="IndexedDB"
            status={systemStatus.indexedDB}
            icon={Database}
          />
          <StatusCard
            title="Cache"
            status={systemStatus.cacheStatus}
            icon={Download}
          />
          <StatusCard
            title="Sync"
            status={systemStatus.syncStatus}
            icon={RotateCcw}
          />
        </div>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="testing">Testing Suite</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="demo">Live Demo</TabsTrigger>
            <TabsTrigger value="installation">Installation</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <OverviewTab 
              healthStatus={healthStatus}
              testResults={testResults}
              performanceData={performanceData}
            />
          </TabsContent>

          <TabsContent value="testing">
            <TestingSuiteTab 
              testSuite={testSuite}
              testResults={testResults}
              isRunningTests={isRunningTests}
              onRunTests={runTests}
              networkSimulator={networkSimulator}
            />
          </TabsContent>

          <TabsContent value="performance">
            <PerformanceTab 
              performanceData={performanceData}
            />
          </TabsContent>

          <TabsContent value="demo">
            <PWADemo />
          </TabsContent>

          <TabsContent value="installation">
            <InstallationTab />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// Status Card Component
const StatusCard = ({ title, status, icon: Icon }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'online':
      case 'active':
      case 'available':
      case 'populated':
        return 'success';
      case 'offline':
      case 'inactive':
      case 'empty':
        return 'error';
      default:
        return 'warning';
    }
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">{title}</p>
            <Badge variant={getStatusColor(status)} className="mt-1">
              {status}
            </Badge>
          </div>
          <Icon className="h-5 w-5 text-gray-400" />
        </div>
      </CardContent>
    </Card>
  );
};

export default PWATestingDashboard;
