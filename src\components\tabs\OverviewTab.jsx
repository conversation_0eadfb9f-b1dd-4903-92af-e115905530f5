/**
 * Overview Tab - System health and quick stats for PWA Testing Dashboard
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Activity, TestTube, BarChart3, Zap } from 'lucide-react';

export const OverviewTab = ({ healthStatus, testResults, performanceData }) => {
  const getHealthColor = (status) => {
    if (!status) return 'warning';
    return status.overall === 'healthy' ? 'success' : 'error';
  };

  const getTestStats = () => {
    if (!testResults || testResults.length === 0) return { total: 0, passed: 0, failed: 0 };
    const passed = testResults.filter(t => t.passed).length;
    return { total: testResults.length, passed, failed: testResults.length - passed };
  };

  const testStats = getTestStats();

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{testStats.total}</div>
            <div className="text-sm text-gray-600">Total Tests</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{testStats.passed}</div>
            <div className="text-sm text-gray-600">Passed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{testStats.failed}</div>
            <div className="text-sm text-gray-600">Failed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-lg font-semibold text-purple-600">
              {testStats.total > 0 ? Math.round((testStats.passed / testStats.total) * 100) : 0}%
            </div>
            <div className="text-sm text-gray-600">Success Rate</div>
          </CardContent>
        </Card>
      </div>

      {/* Health Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          {healthStatus ? (
            <div className="space-y-3">
              {Object.entries(healthStatus).map(([key, value]) => (
                <div key={key} className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                  <Badge variant={typeof value === 'boolean' ? (value ? 'success' : 'error') : 'info'}>
                    {typeof value === 'boolean' ? (value ? 'OK' : 'Failed') : String(value)}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">Run health check to see system status</p>
          )}
        </CardContent>
      </Card>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          {performanceData ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">
                  {performanceData.bundleSize ? `${Math.round(performanceData.bundleSize / 1024)}KB` : 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Bundle Size</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">
                  {performanceData.loadTime ? `${Math.round(performanceData.loadTime)}ms` : 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Load Time</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">
                  {performanceData.cacheHitRate ? `${Math.round(performanceData.cacheHitRate * 100)}%` : 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Cache Hit Rate</div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">Loading performance data...</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
