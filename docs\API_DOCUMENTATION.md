# VoiceHealth AI - API Documentation

## Overview

This document provides comprehensive API documentation for all newly implemented features and services in VoiceHealth AI, including integration guides and developer resources.

## Table of Contents

1. [Agent Orchestrator API](#agent-orchestrator-api)
2. [Circuit Breaker Service API](#circuit-breaker-service-api)
3. [Memory Cleanup Manager API](#memory-cleanup-manager-api)
4. [Real-Time Communication API](#real-time-communication-api)
5. [Context Truncation Service API](#context-truncation-service-api)
6. [Error Sanitization Service API](#error-sanitization-service-api)
7. [Performance Monitoring Service API](#performance-monitoring-service-api)
8. [Secure Storage Service API](#secure-storage-service-api)
9. [Integration Examples](#integration-examples)
10. [Error Handling](#error-handling)
11. [Security Considerations](#security-considerations)

---

## Agent Orchestrator API

### Overview
The Agent Orchestrator manages conversation flow, agent selection, and steering guidance with enhanced context assembly and performance monitoring.

### Core Methods

#### `processRequest(request: OrchestrationRequest): Promise<OrchestrationResponse>`

Processes user requests through the agent system with comprehensive context and monitoring.

**Parameters:**
```typescript
interface OrchestrationRequest {
  sessionId: string;
  userMessage: string;
  userId?: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  audioData?: Blob;
  patientContext?: PatientContext;
}
```

**Response:**
```typescript
interface OrchestrationResponse {
  response: string;
  agentId: string;
  metadata: {
    goalTracking?: GoalTrackingMetadata;
    steeringGuidance?: SteeringGuidance[];
    contextEnhancement?: ContextEnhancement;
    performanceMetrics?: PerformanceMetrics;
    emergencyFlags?: string[];
    truncationMetadata?: TruncationResult;
  };
}
```

**Example:**
```typescript
import { agentOrchestrator } from './services/AgentOrchestrator';

const response = await agentOrchestrator.processRequest({
  sessionId: 'session-123',
  userMessage: 'I have chest pain',
  userId: 'user-456',
  urgencyLevel: 'high'
});

console.log('Agent response:', response.response);
console.log('Emergency flags:', response.metadata.emergencyFlags);
```

#### `performHandoff(sessionId: string, targetAgentRole: AgentRole, reason: string): Promise<boolean>`

Performs agent handoff with proper context transfer and audit logging.

**Example:**
```typescript
const success = await agentOrchestrator.performHandoff(
  'session-123',
  'emergency_specialist',
  'Patient reported severe symptoms'
);
```

---

## Circuit Breaker Service API

### Overview
Provides resilient service calls with automatic failure detection and recovery mechanisms.

### Core Methods

#### `getCircuitBreaker(name: string, config?: CircuitBreakerConfig): CircuitBreaker`

Creates or retrieves a circuit breaker instance.

**Parameters:**
```typescript
interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeoutMs: number;
  monitoringPeriodMs: number;
  emergencyBypass: boolean;
}
```

**Example:**
```typescript
import { circuitBreakerService } from './services/CircuitBreakerService';

const aiServiceBreaker = circuitBreakerService.getCircuitBreaker('openai', {
  failureThreshold: 5,
  recoveryTimeoutMs: 30000,
  emergencyBypass: true
});

// Execute with circuit breaker protection
const result = await aiServiceBreaker.execute(async () => {
  return await openaiService.generateResponse(prompt);
});
```

#### `getAllMetrics(): CircuitBreakerMetrics[]`

Returns metrics for all circuit breakers.

**Example:**
```typescript
const metrics = circuitBreakerService.getAllMetrics();
metrics.forEach(metric => {
  console.log(`${metric.name}: ${metric.state} (${metric.successRate}% success)`);
});
```

---

## Memory Cleanup Manager API

### Overview
Manages memory cleanup for long-running services with HIPAA-compliant data retention.

### Core Methods

#### `scheduleCleanup(config: CleanupConfig): string`

Schedules automatic memory cleanup.

**Parameters:**
```typescript
interface CleanupConfig {
  serviceName: string;
  retentionHours: number;
  cleanupIntervalMs: number;
  emergencyRetentionHours: number;
  hipaaCompliant: boolean;
}
```

**Example:**
```typescript
import { memoryCleanupManager } from './services/MemoryCleanupManager';

const cleanupId = memoryCleanupManager.scheduleCleanup({
  serviceName: 'conversation-history',
  retentionHours: 24,
  cleanupIntervalMs: 3600000, // 1 hour
  emergencyRetentionHours: 72,
  hipaaCompliant: true
});
```

#### `performGlobalCleanup(): Promise<CleanupResult[]>`

Performs immediate cleanup across all services.

**Example:**
```typescript
const results = await memoryCleanupManager.performGlobalCleanup();
console.log(`Cleaned up ${results.length} services`);
```

---

## Real-Time Communication API

### Overview
Provides WebSocket-based real-time communication for goal tracking and steering guidance updates.

### Core Methods

#### `emit(event: string, data: any): void`

Emits real-time events to connected clients.

**Example:**
```typescript
import { realTimeAgentCommunication } from './services/RealTimeAgentCommunication';

// Emit goal progress update
realTimeAgentCommunication.emit('goal-progress-update', {
  sessionId: 'session-123',
  data: {
    goals: updatedGoals,
    progressPercentage: 75
  }
});
```

#### `on(event: string, callback: Function): void`

Registers event listeners for real-time updates.

**Example:**
```typescript
// Listen for goal progress updates
realTimeAgentCommunication.on('goal-progress-update', (data) => {
  if (data.sessionId === currentSessionId) {
    updateGoalDisplay(data.data.goals);
  }
});

// Listen for steering guidance
realTimeAgentCommunication.on('steering-guidance-update', (data) => {
  displaySteeringGuidance(data.data.steeringGuidance);
});
```

---

## Context Truncation Service API

### Overview
Intelligently truncates large context payloads while preserving critical medical information.

### Core Methods

#### `truncateContext(context: any, config?: TruncationConfig): Promise<TruncationResult>`

Truncates context with medical information prioritization.

**Parameters:**
```typescript
interface TruncationConfig {
  maxTokens: number;
  preserveEmergencyContext: boolean;
  preserveMedicalHistory: boolean;
  compressionRatio: number;
  qualityThreshold: number;
}
```

**Response:**
```typescript
interface TruncationResult {
  originalTokenCount: number;
  truncatedTokenCount: number;
  compressionRatio: number;
  preservedSegments: ContextSegment[];
  removedSegments: ContextSegment[];
  qualityScore: number;
  truncationStrategy: string;
  warnings: string[];
}
```

**Example:**
```typescript
import { contextTruncationService } from './services/ContextTruncationService';

const result = await contextTruncationService.truncateContext(largeContext, {
  maxTokens: 8000,
  preserveEmergencyContext: true,
  preserveMedicalHistory: true,
  compressionRatio: 0.7,
  qualityThreshold: 0.8
});

console.log(`Truncated from ${result.originalTokenCount} to ${result.truncatedTokenCount} tokens`);
console.log(`Quality score: ${result.qualityScore}`);
```

---

## Error Sanitization Service API

### Overview
Provides HIPAA-compliant error message sanitization to prevent sensitive data exposure.

### Core Methods

#### `sanitizeError(error: Error | string, context?: ErrorContext, emergencyBypass?: boolean): SanitizedError`

Sanitizes error messages for HIPAA compliance.

**Parameters:**
```typescript
interface ErrorContext {
  sessionId?: string;
  userId?: string;
  agentId?: string;
  operation?: string;
  component?: string;
}
```

**Response:**
```typescript
interface SanitizedError {
  id: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  userMessage: string;
  technicalMessage: string;
  timestamp: string;
  context?: ErrorContext;
  emergencyBypass?: boolean;
}
```

**Example:**
```typescript
import { errorSanitizationService } from './services/ErrorSanitizationService';

try {
  // Some operation that might fail
  await riskyOperation();
} catch (error) {
  const sanitizedError = errorSanitizationService.sanitizeError(error, {
    sessionId: 'session-123',
    userId: 'user-456',
    operation: 'processRequest',
    component: 'AgentOrchestrator'
  });

  console.error(`Error [${sanitizedError.id}]:`, sanitizedError.technicalMessage);
  // Show user-friendly message to client
  showUserError(sanitizedError.userMessage);
}
```

---

## Performance Monitoring Service API

### Overview
Comprehensive performance monitoring with real-time metrics collection and alerting.

### Core Methods

#### `measurePerformance<T>(feature: string, operation: string, operationFn: () => Promise<T>, metadata?: Record<string, any>): Promise<T>`

Measures and records performance of async operations.

**Example:**
```typescript
import { performanceMonitoringService } from './services/PerformanceMonitoringService';

const result = await performanceMonitoringService.measurePerformance(
  'ai_service',
  'generate_response',
  async () => {
    return await aiService.generateResponse(prompt);
  },
  { promptLength: prompt.length, model: 'gpt-4' },
  sessionId,
  userId
);
```

#### `recordMetric(feature: string, operation: string, duration: number, success: boolean, metadata?: Record<string, any>): void`

Records a performance metric manually.

**Example:**
```typescript
const startTime = Date.now();
try {
  await someOperation();
  const duration = Date.now() - startTime;
  performanceMonitoringService.recordMetric('custom_feature', 'operation', duration, true);
} catch (error) {
  const duration = Date.now() - startTime;
  performanceMonitoringService.recordMetric('custom_feature', 'operation', duration, false);
}
```

#### `onAlert(callback: (alert: PerformanceAlert) => void): void`

Registers callback for performance alerts.

**Example:**
```typescript
performanceMonitoringService.onAlert((alert) => {
  if (alert.level === 'emergency') {
    console.error(`🚨 EMERGENCY: ${alert.feature}.${alert.operation} took ${alert.currentValue}ms`);
    // Trigger emergency protocols
  }
});
```

---

## Secure Storage Service API

### Overview
HIPAA-compliant secure storage with encryption, access controls, and audit logging.

### Core Methods

#### `secureUpload(bucketName: string, filePath: string, fileData: File | Blob, userId: string, metadata?: Record<string, any>): Promise<SecureUploadResult>`

Securely uploads files with encryption and validation.

**Example:**
```typescript
import { secureStorageService } from './services/SecureStorageService';

const uploadResult = await secureStorageService.secureUpload(
  'medical-records',
  `patient-123/report-${Date.now()}.pdf`,
  fileBlob,
  'user-456',
  {
    documentType: 'lab_report',
    patientId: 'patient-123',
    providerId: 'provider-789'
  }
);

if (uploadResult.success) {
  console.log('File uploaded:', uploadResult.fileId);
  console.log('Encryption key:', uploadResult.encryptionKey);
}
```

#### `secureDownload(bucketName: string, filePath: string, userId: string, emergencyAccess?: boolean): Promise<SecureDownloadResult>`

Securely downloads files with decryption and access validation.

**Example:**
```typescript
const downloadResult = await secureStorageService.secureDownload(
  'medical-records',
  'patient-123/report-12345.pdf',
  'user-456',
  false // Not emergency access
);

if (downloadResult.success) {
  // Create download link
  const url = URL.createObjectURL(downloadResult.fileData);
  const link = document.createElement('a');
  link.href = url;
  link.download = 'medical-report.pdf';
  link.click();
}
```

#### `grantPermission(permission: StoragePermission): void`

Grants storage permissions to users.

**Example:**
```typescript
secureStorageService.grantPermission({
  userId: 'provider-789',
  role: 'provider',
  permissions: ['read', 'write'],
  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
});
```

---

## Integration Examples

### Complete Medical Consultation Flow

```typescript
import { agentOrchestrator } from './services/AgentOrchestrator';
import { secureStorageService } from './services/SecureStorageService';
import { performanceMonitoringService } from './services/PerformanceMonitoringService';

async function handleMedicalConsultation(sessionId: string, userMessage: string, userId: string, audioFile?: File) {
  try {
    // Upload audio file if provided
    let audioUploadResult;
    if (audioFile) {
      audioUploadResult = await secureStorageService.secureUpload(
        'audio-consultations',
        `${sessionId}/audio-${Date.now()}.webm`,
        audioFile,
        userId
      );
    }

    // Process consultation request
    const response = await agentOrchestrator.processRequest({
      sessionId,
      userMessage,
      userId,
      urgencyLevel: detectUrgency(userMessage),
      audioData: audioFile
    });

    // Handle emergency scenarios
    if (response.metadata.emergencyFlags?.length > 0) {
      await handleEmergencyProtocols(response.metadata.emergencyFlags, sessionId);
    }

    return {
      success: true,
      response: response.response,
      agentId: response.agentId,
      audioFileId: audioUploadResult?.fileId,
      emergencyFlags: response.metadata.emergencyFlags
    };

  } catch (error) {
    console.error('Consultation failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
```

### Real-Time Goal Tracking Setup

```typescript
import { realTimeAgentCommunication } from './services/RealTimeAgentCommunication';

function setupRealTimeGoalTracking(sessionId: string) {
  // Listen for goal progress updates
  realTimeAgentCommunication.on('goal-progress-update', (data) => {
    if (data.sessionId === sessionId) {
      updateGoalProgressUI(data.data.goals);
    }
  });

  // Listen for steering guidance
  realTimeAgentCommunication.on('steering-guidance-update', (data) => {
    if (data.sessionId === sessionId) {
      displaySteeringGuidance(data.data.steeringGuidance);
    }
  });

  // Listen for goal completion
  realTimeAgentCommunication.on('goal-completed', (data) => {
    if (data.sessionId === sessionId) {
      showGoalCompletionNotification(data);
    }
  });

  // Request initial goal progress
  realTimeAgentCommunication.emit('goal-progress-request', {
    sessionId,
    requestId: `goal-request-${Date.now()}`,
    timestamp: new Date().toISOString()
  });
}
```

---

## Error Handling

### Standard Error Response Format

All APIs return errors in a consistent format:

```typescript
interface APIError {
  success: false;
  error: string;
  errorId?: string;
  category?: 'technical' | 'user' | 'security' | 'medical';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
}
```

### Error Handling Best Practices

1. **Always use try-catch blocks** for async operations
2. **Check success flags** in API responses
3. **Log errors appropriately** using the error sanitization service
4. **Provide user-friendly messages** without exposing sensitive data
5. **Implement fallback mechanisms** for critical operations

```typescript
try {
  const result = await someAPICall();
  if (!result.success) {
    console.error('API call failed:', result.error);
    showUserMessage('Operation failed. Please try again.');
    return;
  }
  // Handle success
} catch (error) {
  const sanitizedError = errorSanitizationService.sanitizeError(error);
  console.error('Unexpected error:', sanitizedError.technicalMessage);
  showUserMessage(sanitizedError.userMessage);
}
```

---

## Security Considerations

### Authentication and Authorization

1. **Always validate user permissions** before API calls
2. **Use emergency bypass sparingly** and with proper audit logging
3. **Implement proper session management** with timeout handling
4. **Validate all input parameters** to prevent injection attacks

### Data Protection

1. **Enable encryption** for all sensitive data storage
2. **Use HTTPS** for all API communications
3. **Implement proper audit logging** for compliance
4. **Follow HIPAA guidelines** for medical data handling

### Performance and Monitoring

1. **Monitor API performance** using the performance monitoring service
2. **Implement circuit breakers** for external service calls
3. **Use context truncation** for large payloads
4. **Set up alerting** for performance degradation

---

## Support and Resources

- **GitHub Repository**: [VoiceHealth AI](https://github.com/voicehealth/ai)
- **Issue Tracking**: Use GitHub Issues for bug reports and feature requests
- **Documentation Updates**: Submit PRs for documentation improvements
- **Security Issues**: Report security vulnerabilities privately

For additional support, contact the development team or refer to the inline code documentation.
