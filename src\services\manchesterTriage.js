/**
 * Manchester Triage System Implementation
 * Clinical-grade triage with African context adaptation
 */

export const MANCHESTER_CATEGORIES = {
  IMMEDIATE: { level: 1, color: 'red', name: 'Immediate', timeTarget: 0 },
  VERY_URGENT: { level: 2, color: 'orange', name: 'Very Urgent', timeTarget: 10 },
  URGENT: { level: 3, color: 'yellow', name: 'Urgent', timeTarget: 60 },
  STANDARD: { level: 4, color: 'green', name: 'Standard', timeTarget: 120 },
  NON_URGENT: { level: 5, color: 'blue', name: 'Non-urgent', timeTarget: 240 }
};

export const TRIAGE_DISCRIMINATORS = {
  life_threat: {
    category: MANCHESTER_CATEGORIES.IMMEDIATE,
    keywords: ['cardiac arrest', 'respiratory arrest', 'unconscious', 'unresponsive'],
    vitalThresholds: { systolic: { max: 70 }, heartRate: { min: 140 } }
  },
  severe_pain: {
    category: MANCHESTER_CATEGORIES.VERY_URGENT,
    keywords: ['severe pain', 'excruciating', '10/10 pain'],
    vitalThresholds: { painScore: { min: 8 } }
  },
  chest_pain: {
    category: MANCHESTER_CATEGORIES.VERY_URGENT,
    keywords: ['chest pain', 'heart attack', 'crushing chest pain'],
    vitalThresholds: { systolic: { max: 90 } }
  },
  shortness_of_breath: {
    category: MANCHESTER_CATEGORIES.URGENT,
    keywords: ['shortness of breath', 'difficulty breathing', 'dyspnea'],
    vitalThresholds: { respiratoryRate: { min: 25 }, oxygenSat: { max: 94 } }
  }
};

export const AFRICA_MODIFIERS = {
  malaria_suspect: {
    keywords: ['fever', 'chills', 'sweats', 'headache', 'body aches'],
    regions: ['ghana', 'nigeria', 'kenya'],
    modifier: -1, // Increase urgency
    conditions: { temperature: { min: 37.5 } }
  },
  tuberculosis_suspect: {
    keywords: ['persistent cough', 'night sweats', 'weight loss'],
    regions: ['all'],
    modifier: -1,
    conditions: { duration: { min: 21 } }
  },
  maternal_risk: {
    keywords: ['pregnancy bleeding', 'severe headache pregnancy'],
    regions: ['all'],
    modifier: -2,
    conditions: { pregnant: true }
  }
};

class ManchesterTriageSystem {
  constructor() {
    this.version = '2024.1-Africa';
    this.auditTrail = [];
  }

  /**
   * Perform comprehensive triage assessment
   */
  async performTriage(patientData) {
    const assessment = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      version: this.version,
      patient: patientData,
      triageOutcome: null,
      confidence: 0,
      rationale: [],
      escalationRequired: false
    };

    try {
      // Step 1: Life threat assessment
      const lifeThreat = this.assessLifeThreats(patientData);
      if (lifeThreat.detected) {
        assessment.triageOutcome = lifeThreat.category;
        assessment.confidence = 0.95;
        assessment.rationale = lifeThreat.evidence;
        assessment.escalationRequired = true;
        return this.finalizeAssessment(assessment);
      }

      // Step 2: Apply discriminators
      const discriminatorResult = this.applyDiscriminators(patientData);
      
      // Step 3: Apply vital signs assessment
      const vitalSignsResult = this.assessVitalSigns(patientData, discriminatorResult);
      
      // Step 4: Apply Africa-specific modifiers
      const finalResult = this.applyAfricaModifiers(patientData, vitalSignsResult);

      assessment.triageOutcome = finalResult.category;
      assessment.confidence = finalResult.confidence;
      assessment.rationale = finalResult.rationale;
      assessment.escalationRequired = this.checkEscalationRequired(finalResult);

      return this.finalizeAssessment(assessment);

    } catch (error) {
      console.error('Triage assessment failed:', error);
      // Fail-safe: Default to URGENT for safety
      assessment.triageOutcome = MANCHESTER_CATEGORIES.URGENT;
      assessment.confidence = 0.1;
      assessment.rationale = ['System error - defaulted to URGENT for safety'];
      assessment.escalationRequired = true;
      return this.finalizeAssessment(assessment);
    }
  }

  /**
   * Assess for immediate life threats
   */
  assessLifeThreats(patientData) {
    const symptoms = (patientData.symptoms || '').toLowerCase();
    const vitals = patientData.vitals || {};
    
    const evidence = [];
    let detected = false;

    // Critical vital signs
    if (vitals.systolic && vitals.systolic < 70) {
      evidence.push('Severe hypotension (systolic < 70)');
      detected = true;
    }
    
    if (vitals.heartRate && vitals.heartRate > 140) {
      evidence.push('Severe tachycardia (HR > 140)');
      detected = true;
    }
    
    if (vitals.oxygenSat && vitals.oxygenSat < 85) {
      evidence.push('Critical oxygen saturation (< 85%)');
      detected = true;
    }

    // Critical symptoms
    const lifeThreatKeywords = [
      'cardiac arrest', 'respiratory arrest', 'unconscious', 'unresponsive',
      'not breathing', 'no pulse', 'collapsed'
    ];

    lifeThreatKeywords.forEach(keyword => {
      if (symptoms.includes(keyword)) {
        evidence.push(`Life-threatening symptom: ${keyword}`);
        detected = true;
      }
    });

    return {
      detected,
      category: MANCHESTER_CATEGORIES.IMMEDIATE,
      evidence
    };
  }

  /**
   * Apply clinical discriminators
   */
  applyDiscriminators(patientData) {
    const symptoms = (patientData.symptoms || '').toLowerCase();
    let highestCategory = MANCHESTER_CATEGORIES.NON_URGENT;
    const rationale = [];

    Object.entries(TRIAGE_DISCRIMINATORS).forEach(([key, discriminator]) => {
      const keywordMatch = discriminator.keywords.some(keyword => 
        symptoms.includes(keyword.toLowerCase())
      );

      if (keywordMatch) {
        if (discriminator.category.level < highestCategory.level) {
          highestCategory = discriminator.category;
        }
        rationale.push(`Discriminator matched: ${key}`);
      }
    });

    return {
      category: highestCategory,
      confidence: rationale.length > 0 ? 0.8 : 0.2,
      rationale
    };
  }

  /**
   * Assess vital signs against thresholds
   */
  assessVitalSigns(patientData, previousResult) {
    const vitals = patientData.vitals || {};
    const rationale = [...previousResult.rationale];
    let category = previousResult.category;

    // Temperature assessment
    if (vitals.temperature) {
      if (vitals.temperature > 39.5) {
        category = this.upgradeCategory(category, MANCHESTER_CATEGORIES.URGENT);
        rationale.push('High fever > 39.5°C');
      } else if (vitals.temperature < 35) {
        category = this.upgradeCategory(category, MANCHESTER_CATEGORIES.VERY_URGENT);
        rationale.push('Hypothermia < 35°C');
      }
    }

    // Blood pressure assessment
    if (vitals.systolic && vitals.diastolic) {
      if (vitals.systolic > 180 || vitals.diastolic > 110) {
        category = this.upgradeCategory(category, MANCHESTER_CATEGORIES.URGENT);
        rationale.push('Severe hypertension');
      }
    }

    return {
      category,
      confidence: Math.min(0.9, previousResult.confidence + 0.1),
      rationale
    };
  }

  /**
   * Apply Africa-specific clinical modifiers
   */
  applyAfricaModifiers(patientData, previousResult) {
    const symptoms = (patientData.symptoms || '').toLowerCase();
    const region = patientData.region || 'ghana';
    let category = previousResult.category;
    const rationale = [...previousResult.rationale];

    Object.entries(AFRICA_MODIFIERS).forEach(([key, modifier]) => {
      const keywordMatch = modifier.keywords.some(keyword => 
        symptoms.includes(keyword.toLowerCase())
      );

      const regionMatch = modifier.regions.includes('all') || 
                         modifier.regions.includes(region.toLowerCase());

      if (keywordMatch && regionMatch) {
        const newLevel = Math.max(1, category.level + modifier.modifier);
        const newCategory = Object.values(MANCHESTER_CATEGORIES)
          .find(cat => cat.level === newLevel);
        
        if (newCategory && newCategory.level < category.level) {
          category = newCategory;
          rationale.push(`Africa modifier applied: ${key}`);
        }
      }
    });

    return {
      category,
      confidence: previousResult.confidence,
      rationale
    };
  }

  /**
   * Check if escalation is required
   */
  checkEscalationRequired(result) {
    return result.category.level <= 2 || result.confidence < 0.7;
  }

  /**
   * Upgrade category to higher urgency
   */
  upgradeCategory(currentCategory, newCategory) {
    return newCategory.level < currentCategory.level ? newCategory : currentCategory;
  }

  /**
   * Finalize assessment and log
   */
  finalizeAssessment(assessment) {
    // Log to audit trail
    this.auditTrail.push({
      id: assessment.id,
      timestamp: assessment.timestamp,
      outcome: assessment.triageOutcome,
      confidence: assessment.confidence,
      escalation: assessment.escalationRequired
    });

    // Trigger escalation if required
    if (assessment.escalationRequired) {
      this.triggerEscalation(assessment);
    }

    return assessment;
  }

  /**
   * Trigger emergency escalation
   */
  async triggerEscalation(assessment) {
    try {
      // Send webhook notification
      if (import.meta.env.VITE_EMERGENCY_WEBHOOK_URL) {
        await fetch(import.meta.env.VITE_EMERGENCY_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'emergency_escalation',
            assessment: assessment,
            timestamp: new Date().toISOString()
          })
        });
      }
      
      console.warn('Emergency escalation triggered:', assessment.id);
    } catch (error) {
      console.error('Failed to trigger escalation:', error);
    }
  }

  /**
   * Get audit trail for compliance
   */
  getAuditTrail(limit = 50) {
    return this.auditTrail.slice(-limit);
  }
}

export default ManchesterTriageSystem;
