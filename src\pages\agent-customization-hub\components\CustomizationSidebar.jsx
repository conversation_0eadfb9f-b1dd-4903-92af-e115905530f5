import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CustomizationSidebar = ({ 
  selectedAgent = null,
  onSaveChanges = () => {},
  onResetToDefaults = () => {},
  onPresetLoad = () => {},
  hasUnsavedChanges = false,
  isSaving = false,
  className = ''
}) => {
  const [activePreset, setActivePreset] = useState(null);

  const presets = [
    {
      id: 'professional',
      name: 'Professional',
      description: 'Formal, detailed, clinical approach',
      icon: 'Briefcase',
      settings: {
        personality: {
          formality: 0.9,
          empathy: 0.6,
          detailLevel: 0.8
        }
      }
    },
    {
      id: 'friendly',
      name: 'Friendly',
      description: 'Warm, empathetic, conversational',
      icon: 'Heart',
      settings: {
        personality: {
          formality: 0.3,
          empathy: 0.9,
          detailLevel: 0.6
        }
      }
    },
    {
      id: 'concise',
      name: 'Concise',
      description: 'Brief, direct, to-the-point',
      icon: 'Zap',
      settings: {
        personality: {
          formality: 0.7,
          empathy: 0.5,
          detailLevel: 0.3
        }
      }
    },
    {
      id: 'detailed',
      name: 'Detailed',
      description: 'Comprehensive, thorough explanations',
      icon: 'FileText',
      settings: {
        personality: {
          formality: 0.6,
          empathy: 0.7,
          detailLevel: 0.9
        }
      }
    }
  ];

  const handlePresetSelect = (preset) => {
    setActivePreset(preset.id);
    onPresetLoad(preset.settings);
  };

  const handleSaveChanges = () => {
    onSaveChanges();
    setActivePreset(null);
  };

  const handleResetToDefaults = () => {
    onResetToDefaults();
    setActivePreset(null);
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h3 className="font-semibold text-text-primary font-heading">
          Quick Customization
        </h3>
        <p className="text-sm text-text-secondary font-caption">
          Apply preset configurations or save your changes
        </p>
      </div>

      {/* Presets */}
      <div className="p-4">
        <h4 className="font-medium text-text-primary mb-3">Personality Presets</h4>
        <div className="space-y-2">
          {presets.map((preset) => (
            <button
              key={preset.id}
              onClick={() => handlePresetSelect(preset)}
              className={`w-full p-3 rounded-lg border transition-fast text-left ${
                activePreset === preset.id
                  ? 'border-primary-500 bg-primary-50 text-primary-700' :'border-border bg-surface text-text-primary hover:bg-secondary-50'
              }`}
            >
              <div className="flex items-start space-x-3">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                  activePreset === preset.id ? 'bg-primary-500' : 'bg-secondary-100'
                }`}>
                  <Icon 
                    name={preset.icon} 
                    size={16} 
                    color={activePreset === preset.id ? 'white' : 'var(--color-text-secondary)'}
                  />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{preset.name}</div>
                  <div className="text-xs opacity-75 mt-1">{preset.description}</div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Agent Info */}
      {selectedAgent && (
        <div className="p-4 border-t border-border">
          <h4 className="font-medium text-text-primary mb-3">Current Agent</h4>
          <div className="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              selectedAgent.type === 'gp' ? 'bg-primary-500' :
              selectedAgent.type === 'cardiologist' ? 'bg-error-500' :
              selectedAgent.type === 'nutritionist'? 'bg-success-500' : 'bg-secondary-500'
            }`}>
              <Icon 
                name={
                  selectedAgent.type === 'gp' ? 'Stethoscope' :
                  selectedAgent.type === 'cardiologist' ? 'Heart' :
                  selectedAgent.type === 'nutritionist'? 'Apple' : 'User'
                } 
                size={16} 
                color="white" 
              />
            </div>
            <div>
              <div className="font-medium text-text-primary">{selectedAgent.name}</div>
              <div className="text-sm text-text-secondary">{selectedAgent.specialty}</div>
            </div>
          </div>
        </div>
      )}

      {/* Voice Profile Summary */}
      {selectedAgent && (
        <div className="p-4 border-t border-border">
          <h4 className="font-medium text-text-primary mb-3">Voice Profile</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-text-secondary">Gender:</span>
              <span className="text-text-primary font-medium capitalize">
                {selectedAgent.voiceProfile.gender}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-secondary">Accent:</span>
              <span className="text-text-primary font-medium">
                {selectedAgent.voiceProfile.accent}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Personality Summary */}
      {selectedAgent && (
        <div className="p-4 border-t border-border">
          <h4 className="font-medium text-text-primary mb-3">Personality Traits</h4>
          <div className="space-y-3">
            {Object.entries(selectedAgent.personality).map(([trait, value]) => (
              <div key={trait}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-text-secondary capitalize">
                    {trait.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                  <span className="text-text-primary font-data">
                    {Math.round(value * 100)}%
                  </span>
                </div>
                <div className="w-full bg-secondary-200 rounded-full h-1.5">
                  <div 
                    className="bg-primary-500 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${value * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="p-4 border-t border-border space-y-3">
        <Button
          variant="primary"
          onClick={handleSaveChanges}
          loading={isSaving}
          disabled={!hasUnsavedChanges}
          iconName="Save"
          iconPosition="left"
          fullWidth
        >
          {isSaving ? 'Saving Changes...' : 'Apply Changes'}
        </Button>
        
        <Button
          variant="outline"
          onClick={handleResetToDefaults}
          disabled={isSaving}
          iconName="RotateCcw"
          iconPosition="left"
          fullWidth
        >
          Reset to Defaults
        </Button>

        {hasUnsavedChanges && (
          <div className="flex items-center space-x-2 p-2 bg-warning-50 rounded-lg">
            <Icon name="AlertTriangle" size={14} color="var(--color-warning)" />
            <span className="text-xs text-warning-600">
              You have unsaved changes
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomizationSidebar;