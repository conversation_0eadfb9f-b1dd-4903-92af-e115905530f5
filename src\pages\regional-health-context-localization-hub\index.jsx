import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import RegionalMapVisualization from './components/RegionalMapVisualization';
import CountryConfigurationPanel from './components/CountryConfigurationPanel';
import CulturalAdaptationControls from './components/CulturalAdaptationControls';
import LocalHealthcareProviderIntegration from './components/LocalHealthcareProviderIntegration';
import RealTimeHealthUpdates from './components/RealTimeHealthUpdates';

const RegionalHealthContextLocalizationHub = () => {
  const navigate = useNavigate();
  const [selectedCountry, setSelectedCountry] = useState('ghana');
  const [activeTab, setActiveTab] = useState('overview');
  const [localizationData, setLocalizationData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [syncStatus, setSyncStatus] = useState('synced');

  // Target countries data for MVP
  const targetCountries = [
    {
      id: 'ghana',
      name: 'Ghana',
      code: 'GH',
      flag: '🇬🇭',
      population: '32.8M',
      healthSystemType: 'National Health Insurance Scheme',
      primaryLanguages: ['English', 'Twi', 'Ga', 'Ewe'],
      currency: 'GHS',
      timezone: 'GMT',
      coordinates: { lat: 7.9465, lng: -1.0232 },
      commonConditions: [
        'Malaria', 'Hypertension', 'Diabetes', 'Respiratory infections',
        'Diarrheal diseases', 'Anemia', 'HIV/AIDS', 'Tuberculosis'
      ],
      seasonalPatterns: {
        'Dry Season (Nov-Mar)': ['Respiratory infections', 'Meningitis', 'Skin conditions'],
        'Rainy Season (Apr-Oct)': ['Malaria', 'Cholera', 'Diarrheal diseases']
      },
      traditionalMedicine: [
        'Herbal medicine', 'Traditional bone setting', 'Spiritual healing',
        'Nutritional therapy with local foods'
      ],
      healthcareAccess: {
        urbanCoverage: '85%',
        ruralCoverage: '65%',
        primaryHealthCenters: 3500,
        hospitals: 350
      }
    },
    {
      id: 'nigeria',
      name: 'Nigeria',
      code: 'NG',
      flag: '🇳🇬',
      population: '218M',
      healthSystemType: 'National Health Insurance Authority',
      primaryLanguages: ['English', 'Hausa', 'Yoruba', 'Igbo'],
      currency: 'NGN',
      timezone: 'WAT',
      coordinates: { lat: 9.0820, lng: 8.6753 },
      commonConditions: [
        'Malaria', 'Tuberculosis', 'HIV/AIDS', 'Hypertension',
        'Diabetes', 'Maternal mortality', 'Respiratory infections', 'Typhoid'
      ],
      seasonalPatterns: {
        'Dry Season (Nov-Mar)': ['Meningitis', 'Respiratory infections', 'Lassa fever'],
        'Rainy Season (Apr-Oct)': ['Malaria', 'Cholera', 'Yellow fever']
      },
      traditionalMedicine: [
        'Hausa traditional medicine', 'Yoruba healing practices',
        'Igbo herbal treatments', 'Islamic medicine (Tibb)'
      ],
      healthcareAccess: {
        urbanCoverage: '75%',
        ruralCoverage: '45%',
        primaryHealthCenters: 30000,
        hospitals: 3500
      }
    },
    {
      id: 'kenya',
      name: 'Kenya',
      code: 'KE',
      flag: '🇰🇪',
      population: '54.9M',
      healthSystemType: 'Universal Health Coverage',
      primaryLanguages: ['English', 'Swahili', 'Kikuyu', 'Luo'],
      currency: 'KES',
      timezone: 'EAT',
      coordinates: { lat: -0.0236, lng: 37.9062 },
      commonConditions: [
        'Malaria', 'HIV/AIDS', 'Tuberculosis', 'Diarrheal diseases',
        'Respiratory infections', 'Malnutrition', 'Diabetes', 'Hypertension'
      ],
      seasonalPatterns: {
        'Long Dry Season (Jun-Oct)': ['Respiratory infections', 'Malnutrition'],
        'Short Rains (Nov-Dec)': ['Malaria', 'Rift Valley fever'],
        'Long Rains (Mar-May)': ['Cholera', 'Dengue fever']
      },
      traditionalMedicine: [
        'Kikuyu traditional healing', 'Luo herbal medicine',
        'Maasai traditional practices', 'Community health practices'
      ],
      healthcareAccess: {
        urbanCoverage: '80%',
        ruralCoverage: '60%',
        primaryHealthCenters: 5500,
        hospitals: 650
      }
    },
    {
      id: 'south-africa',
      name: 'South Africa',
      code: 'ZA',
      flag: '🇿🇦',
      population: '60.4M',
      healthSystemType: 'National Health Insurance',
      primaryLanguages: ['English', 'Afrikaans', 'Zulu', 'Xhosa', 'Sotho'],
      currency: 'ZAR',
      timezone: 'SAST',
      coordinates: { lat: -30.5595, lng: 22.9375 },
      commonConditions: [
        'HIV/AIDS', 'Tuberculosis', 'Hypertension', 'Diabetes',
        'Cardiovascular disease', 'Mental health disorders', 'Cancer', 'Stroke'
      ],
      seasonalPatterns: {
        'Summer (Oct-Mar)': ['Gastroenteritis', 'Dengue fever', 'Heat-related illness'],
        'Winter (Apr-Sep)': ['Respiratory infections', 'Influenza', 'Pneumonia']
      },
      traditionalMedicine: [
        'Zulu traditional healing', 'Xhosa herbal medicine',
        'African potato (Hypoxis)', 'Sangoma practices'
      ],
      healthcareAccess: {
        urbanCoverage: '90%',
        ruralCoverage: '70%',
        primaryHealthCenters: 4500,
        hospitals: 950
      }
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Regional Overview', icon: 'Globe' },
    { id: 'medical-context', label: 'Medical Context', icon: 'Stethoscope' },
    { id: 'cultural-adaptation', label: 'Cultural Adaptation', icon: 'Users' },
    { id: 'provider-integration', label: 'Provider Integration', icon: 'Building' },
    { id: 'real-time-updates', label: 'Real-time Updates', icon: 'RefreshCw' }
  ];

  useEffect(() => {
    // Simulate loading localization data
    const loadLocalizationData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock localization data
      const mockData = {
        ghana: {
          agentAdaptations: {
            'general-practitioner': { localTerminology: 95, culturalSensitivity: 92 },
            'dietician': { localTerminology: 88, culturalSensitivity: 94 },
            'mental-health': { localTerminology: 85, culturalSensitivity: 98 }
          },
          lastSyncTime: new Date().toISOString(),
          dataCompleteness: 87
        },
        nigeria: {
          agentAdaptations: {
            'general-practitioner': { localTerminology: 92, culturalSensitivity: 89 },
            'dietician': { localTerminology: 91, culturalSensitivity: 92 },
            'mental-health': { localTerminology: 88, culturalSensitivity: 95 }
          },
          lastSyncTime: new Date().toISOString(),
          dataCompleteness: 84
        }
      };
      
      setLocalizationData(mockData);
      setIsLoading(false);
    };

    loadLocalizationData();
  }, []);

  const handleCountrySelect = (countryId) => {
    setSelectedCountry(countryId);
    setActiveTab('overview');
  };

  const handleSyncData = async () => {
    setSyncStatus('syncing');
    await new Promise(resolve => setTimeout(resolve, 2000));
    setSyncStatus('synced');
  };

  const selectedCountryData = targetCountries.find(c => c.id === selectedCountry);

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
              <Icon name="Globe" size={24} color="var(--primary-500)" />
            </div>
            <p className="text-text-secondary">Loading regional health context...</p>
          </div>
        </div>
      );
    }

    switch (activeTab) {
      case 'medical-context':
        return (
          <CountryConfigurationPanel
            country={selectedCountryData}
            localizationData={localizationData?.[selectedCountry]}
            onUpdate={(updates) => {
              setLocalizationData(prev => ({
                ...prev,
                [selectedCountry]: { ...prev?.[selectedCountry], ...updates }
              }));
            }}
          />
        );

      case 'cultural-adaptation':
        return (
          <CulturalAdaptationControls
            country={selectedCountryData}
            onSettingsChange={(settings) => {
              console.log('Cultural adaptation settings updated:', settings);
            }}
          />
        );

      case 'provider-integration':
        return (
          <LocalHealthcareProviderIntegration
            country={selectedCountryData}
            onIntegrationUpdate={(integration) => {
              console.log('Provider integration updated:', integration);
            }}
          />
        );

      case 'real-time-updates':
        return (
          <RealTimeHealthUpdates
            country={selectedCountryData}
            onUpdateSync={handleSyncData}
            syncStatus={syncStatus}
          />
        );

      default:
        return (
          <div className="space-y-8">
            {/* Regional Map */}
            <div>
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-4">
                Target Countries Interactive Map
              </h3>
              <RegionalMapVisualization
                countries={targetCountries}
                selectedCountry={selectedCountry}
                onCountrySelect={handleCountrySelect}
              />
            </div>

            {/* Country Statistics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  label: 'Population',
                  value: selectedCountryData?.population,
                  icon: 'Users',
                  color: 'text-primary-500'
                },
                {
                  label: 'Health System',
                  value: selectedCountryData?.healthSystemType?.split(' ').slice(0, 2).join(' '),
                  icon: 'Building',
                  color: 'text-success-500'
                },
                {
                  label: 'Primary Languages',
                  value: `${selectedCountryData?.primaryLanguages?.length} Languages`,
                  icon: 'MessageSquare',
                  color: 'text-accent-500'
                },
                {
                  label: 'Data Completeness',
                  value: `${localizationData?.[selectedCountry]?.dataCompleteness || 0}%`,
                  icon: 'BarChart3',
                  color: 'text-info-500'
                }
              ].map((stat, index) => (
                <div key={index} className="bg-surface border border-border rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-text-secondary font-caption mb-1">
                        {stat.label}
                      </p>
                      <p className="text-xl font-semibold text-text-primary">
                        {stat.value}
                      </p>
                    </div>
                    <div className={`p-3 rounded-lg bg-opacity-10 ${stat.color.replace('500', '100')}`}>
                      <Icon name={stat.icon} size={24} className={stat.color} />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Common Health Conditions */}
            <div>
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-4">
                Common Health Conditions - {selectedCountryData?.name}
              </h3>
              <div className="bg-surface border border-border rounded-lg p-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {selectedCountryData?.commonConditions?.map((condition, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                      <span className="text-sm text-text-primary">{condition}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Agent Network Localization Status */}
            <div>
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-4">
                Multi-Agent Network Localization Status
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {['general-practitioner', 'dietician', 'mental-health'].map((agentId) => {
                  const agentData = localizationData?.[selectedCountry]?.agentAdaptations?.[agentId];
                  const agentNames = {
                    'general-practitioner': 'General Practitioner',
                    'dietician': 'Dietician',
                    'mental-health': 'Mental Health Specialist'
                  };
                  
                  return (
                    <div key={agentId} className="bg-surface border border-border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-text-primary">
                          {agentNames[agentId]}
                        </h4>
                        <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-text-secondary">Local Terminology</span>
                          <span className="text-sm font-medium text-text-primary">
                            {agentData?.localTerminology || 0}%
                          </span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2">
                          <div 
                            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${agentData?.localTerminology || 0}%` }}
                          ></div>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-text-secondary">Cultural Sensitivity</span>
                          <span className="text-sm font-medium text-text-primary">
                            {agentData?.culturalSensitivity || 0}%
                          </span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2">
                          <div 
                            className="bg-success-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${agentData?.culturalSensitivity || 0}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-3xl font-bold text-text-primary font-heading">
              Regional Health Context & Localization Hub
            </h1>
            <p className="text-text-secondary mt-2">
              Comprehensive localization management for culturally-aware healthcare consultations across Ghana, Nigeria, Kenya, and South Africa
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex items-center gap-2 px-3 py-2 bg-surface rounded-lg border border-border">
              <div className={`w-2 h-2 rounded-full ${
                syncStatus === 'synced' ? 'bg-success-500' :
                syncStatus === 'syncing' ? 'bg-warning-500 animate-pulse' : 'bg-error-500'
              }`}></div>
              <span className="text-sm font-medium text-text-primary">
                {syncStatus === 'synced' ? 'Data Synced' :
                 syncStatus === 'syncing' ? 'Syncing...' : 'Sync Error'}
              </span>
            </div>
            
            <Button
              variant="outline"
              onClick={() => navigate('/agent-network-management')}
              iconName="Network"
            >
              Agent Network
            </Button>
            
            <Button
              variant="primary"
              onClick={handleSyncData}
              iconName="RefreshCw"
              disabled={syncStatus === 'syncing'}
            >
              Sync Data
            </Button>
          </div>
        </div>

        {/* Country Selection Tabs */}
        <div className="mb-8">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {targetCountries.map((country) => (
                <button
                  key={country.id}
                  onClick={() => handleCountrySelect(country.id)}
                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    selectedCountry === country.id
                      ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-border'
                  }`}
                >
                  <span className="mr-2">{country.flag}</span>
                  {country.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Feature Tabs */}
        <div className="mb-8">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-border'
                  }`}
                >
                  <Icon name={tab.icon} size={16} />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        {renderTabContent()}
      </main>
    </div>
  );
};

export default RegionalHealthContextLocalizationHub;