import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/SimpleAuthContext';
import AuthenticationHeader from './components/AuthenticationHeader';
import AuthenticationForm from './components/AuthenticationForm';
import SocialAuthOptions from './components/SocialAuthOptions';
import DemoCredentialsInfo from './components/DemoCredentialsInfo';
import CreateAccountPrompt from './components/CreateAccountPrompt';

const AuthenticationDemoAccess = () => {
  const navigate = useNavigate();
  const { signIn, signUp, authError, user, loading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showDemoCredentials, setShowDemoCredentials] = useState(false);
  const [authSuccess, setAuthSuccess] = useState('');

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !loading) {
      const userRole = user?.user_metadata?.role;
      
      if (userRole === 'admin') {
        navigate('/admin-dashboard');
      } else if (userRole === 'provider') {
        navigate('/session-dashboard-history');
      } else {
        // Default for patients
        navigate('/patient-profile-setup');
      }
    }
  }, [user, loading, navigate]);

  // Mock credentials for validation (kept for demo purposes)
  const validCredentials = [
    { email: '<EMAIL>', password: 'HealthDemo123', role: 'patient' },
    { email: '<EMAIL>', password: 'ProviderDemo123', role: 'provider' },
    { email: '<EMAIL>', password: 'AdminDemo123', role: 'admin' }
  ];

  const handleLogin = async (formData) => {
    setIsLoading(true);
    setAuthSuccess('');

    try {
      const result = await signIn(formData.email, formData.password);

      if (result?.success) {
        setAuthSuccess('Login successful! Redirecting...');
        
        // Navigation is handled by useEffect when user state updates
        setTimeout(() => {
          const userRole = result.data?.user?.user_metadata?.role;
          if (userRole === 'admin') {
            navigate('/session-dashboard-history');
          } else {
            navigate('/patient-profile-setup');
          }
        }, 1000);
      }
    } catch (error) {
      console.log('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (email, password, userData) => {
    setIsLoading(true);
    setAuthSuccess('');

    try {
      const result = await signUp(email, password, userData);

      if (result?.success) {
        setAuthSuccess('Account created successfully! Please check your email to confirm your account.');
      }
    } catch (error) {
      console.log('Signup error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider) => {
    setIsLoading(true);
    
    try {
      // For demo purposes - simulate social login
      const demoData = {
        email: `demo.${provider}@voicehealth.ai`,
        password: 'SocialDemo123',
        full_name: `Demo ${provider.charAt(0).toUpperCase() + provider.slice(1)} User`,
        role: 'patient'
      };

      const result = await signUp(demoData.email, demoData.password, {
        full_name: demoData.full_name,
        role: demoData.role
      });

      if (result?.success) {
        setAuthSuccess(`${provider.charAt(0).toUpperCase() + provider.slice(1)} account created! Please check your email.`);
      }
    } catch (error) {
      console.log('Social login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoAccess = () => {
    setShowDemoCredentials(true);
  };

  const handleCreateAccount = () => {
    // For real implementation, would show signup form
    // For now, just show info about demo
    setAuthSuccess('Use the demo credentials below or create a real account with your email.');
    setShowDemoCredentials(true);
  };

  const closeDemoCredentials = () => {
    setShowDemoCredentials(false);
  };

  // Show loading state during initial auth check
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-muted">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <div className="flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto w-full space-y-8">
          {/* Header */}
          <AuthenticationHeader />

          {/* Authentication Form */}
          <div className="bg-surface shadow-elevated rounded-xl p-8 space-y-6">
            {/* Status Messages */}
            {authError && (
              <div className="bg-error-50 border border-error-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-error-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">!</span>
                  </div>
                  <p className="text-sm text-error-700">{authError}</p>
                </div>
              </div>
            )}

            {authSuccess && (
              <div className="bg-success-50 border border-success-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 bg-success-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <p className="text-sm text-success-700">{authSuccess}</p>
                </div>
              </div>
            )}

            <AuthenticationForm
              onLogin={handleLogin}
              onSignup={handleSignup}
              onDemoAccess={handleDemoAccess}
              isLoading={isLoading}
            />

            {/* Social Authentication */}
            <SocialAuthOptions
              onSocialLogin={handleSocialLogin}
              isLoading={isLoading}
            />
          </div>

          {/* Create Account Prompt */}
          <CreateAccountPrompt
            onCreateAccount={handleCreateAccount}
            isLoading={isLoading}
          />

          {/* Footer */}
          <div className="text-center space-y-4">
            <div className="text-xs text-text-muted">
              <p>© {new Date().getFullYear()} VoiceHealth AI. All rights reserved.</p>
              <div className="flex justify-center space-x-4 mt-2">
                <button className="hover:text-text-secondary transition-fast">
                  Privacy Policy
                </button>
                <span>•</span>
                <button className="hover:text-text-secondary transition-fast">
                  Terms of Service
                </button>
                <span>•</span>
                <button className="hover:text-text-secondary transition-fast">
                  Support
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Credentials Modal */}
      <DemoCredentialsInfo
        isVisible={showDemoCredentials}
        onClose={closeDemoCredentials}
        validCredentials={validCredentials}
      />
    </div>
  );
};

export default AuthenticationDemoAccess;