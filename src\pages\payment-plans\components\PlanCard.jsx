import React from 'react';
import { CheckCircle, Star, AlertTriangle } from 'lucide-react';
import Button from '../../../components/ui/Button';

const PlanCard = ({ 
  plan, 
  currency = 'NGN', 
  isPopular = false, 
  isCurrentPlan = false, 
  onSelect, 
  disabled = false,
  isDemoMode = false 
}) => {
  const formatPrice = (priceNgn, priceUsd, currency) => {
    const price = currency === 'USD' ? priceUsd : priceNgn;
    const formatter = new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
    return formatter.format(price);
  };

  const formatDuration = (days) => {
    if (days >= 365) {
      const years = Math.floor(days / 365);
      return `${years} year${years > 1 ? 's' : ''}`;
    } else if (days >= 30) {
      const months = Math.floor(days / 30);
      return `${months} month${months > 1 ? 's' : ''}`;
    } else {
      return `${days} day${days > 1 ? 's' : ''}`;
    }
  };

  const getCreditsText = (credits) => {
    if (credits === 0) return 'Unlimited consultations';
    return `${credits} consultation${credits > 1 ? 's' : ''}`;
  };

  return (
    <div className={`
      relative bg-white rounded-lg shadow-sm border-2 transition-all duration-200 hover:shadow-md
      ${isPopular ? 'border-blue-500' : 'border-gray-200'}
      ${isCurrentPlan ? 'ring-2 ring-green-500' : ''}
      ${isDemoMode ? 'opacity-75' : ''}
    `}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
            <Star size={12} className="mr-1" />
            Most Popular
          </div>
        </div>
      )}

      {/* Demo Mode Badge */}
      {isDemoMode && (
        <div className="absolute -top-3 right-4">
          <div className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            Demo
          </div>
        </div>
      )}

      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 left-4">
          <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
            <CheckCircle size={12} className="mr-1" />
            Current Plan
          </div>
        </div>
      )}

      <div className="p-6">
        {/* Plan Header */}
        <div className="text-center mb-6">
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {plan.name}
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {plan.description}
          </p>
          
          {/* Price */}
          <div className="mb-2">
            <span className="text-3xl font-bold text-gray-900">
              {formatPrice(plan.price_ngn, plan.price_usd, currency)}
            </span>
            <span className="text-gray-600 ml-1">
              / {formatDuration(plan.duration_days)}
            </span>
          </div>
          
          {/* Credits */}
          <p className="text-sm text-gray-600">
            {getCreditsText(plan.consultation_credits)}
          </p>
        </div>

        {/* Features */}
        <div className="mb-6">
          <h4 className="font-semibold text-gray-900 mb-3 text-sm">
            What's included:
          </h4>
          <ul className="space-y-2">
            {plan.features?.map((feature, index) => (
              <li key={index} className="flex items-start">
                <CheckCircle size={16} className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                <span className="text-sm text-gray-600">{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Demo Warning */}
        {isDemoMode && (
          <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
            <div className="flex items-start">
              <AlertTriangle size={16} className="text-orange-600 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <p className="text-xs text-orange-800 font-medium">Demo Plan</p>
                <p className="text-xs text-orange-700">
                  Complete database setup to access real subscription plans
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Button */}
        <Button
          onClick={onSelect}
          disabled={disabled || isCurrentPlan}
          variant={isPopular ? 'primary' : 'outline'}
          className={`w-full ${
            isCurrentPlan 
              ? 'bg-green-100 text-green-800 border-green-300 cursor-not-allowed' :''
          } ${
            isDemoMode 
              ? 'opacity-75' :''
          }`}
        >
          {isCurrentPlan 
            ? 'Current Plan' 
            : isDemoMode 
              ? 'Setup Required'
              : disabled 
                ? 'Processing...' :'Select Plan'
          }
        </Button>

        {/* Value Proposition */}
        {plan.consultation_credits > 0 && (
          <div className="mt-3 text-center">
            <p className="text-xs text-gray-500">
              {currency === 'USD' 
                ? `$${(plan.price_usd / plan.consultation_credits).toFixed(2)}` 
                : `₦${(plan.price_ngn / plan.consultation_credits).toFixed(0)}`
              } per consultation
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlanCard;