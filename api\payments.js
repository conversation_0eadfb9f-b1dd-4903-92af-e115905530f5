/**
 * SECURE BACKEND API FOR PAYSTACK OPERATIONS
 * This file should be deployed to a secure server environment
 * NEVER deploy this to client-side or expose secret keys
 */

const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
const router = express.Router();

// Environment variables (server-side only)
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Initialize Supabase client with service role key for server operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Middleware for authentication
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Authentication required' });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ success: false, error: 'Invalid authentication token' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ success: false, error: 'Authentication failed' });
  }
};

// Input validation middleware
const validatePaymentData = (req, res, next) => {
  const { email, amount, currency, reference } = req.body;

  if (!email || !amount || !reference) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields: email, amount, reference'
    });
  }

  if (typeof amount !== 'number' || amount <= 0) {
    return res.status(400).json({
      success: false,
      error: 'Amount must be a positive number'
    });
  }

  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid email format'
    });
  }

  next();
};

// Sanitize input data
const sanitizeInput = (data) => {
  const sanitized = {};
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = value.trim().replace(/[<>]/g, '');
    } else {
      sanitized[key] = value;
    }
  }
  return sanitized;
};

/**
 * Initialize Paystack payment - SECURE SERVER-SIDE ENDPOINT
 */
router.post('/initialize', authenticateUser, validatePaymentData, async (req, res) => {
  try {
    const sanitizedData = sanitizeInput(req.body);
    
    // Verify user owns the resources they're trying to pay for
    if (sanitizedData.metadata?.user_id && sanitizedData.metadata.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized: Cannot create payment for another user'
      });
    }

    const response = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: sanitizedData.email,
        amount: Math.round(sanitizedData.amount * 100), // Convert to kobo
        currency: sanitizedData.currency || 'NGN',
        reference: sanitizedData.reference,
        callback_url: sanitizedData.callback_url,
        metadata: {
          user_id: req.user.id, // Always use authenticated user ID
          subscription_id: sanitizedData.metadata?.subscription_id,
          consultation_session_id: sanitizedData.metadata?.consultation_session_id,
          ...sanitizedData.metadata
        },
        channels: sanitizedData.channels || ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
      })
    });

    const result = await response.json();
    
    if (!result.status) {
      return res.status(400).json({
        success: false,
        error: result.message || 'Failed to initialize payment'
      });
    }

    // Log payment initialization (without sensitive data)
    console.log('Payment initialized:', {
      reference: sanitizedData.reference,
      user_id: req.user.id,
      amount: sanitizedData.amount,
      currency: sanitizedData.currency,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      data: {
        authorization_url: result.data.authorization_url,
        access_code: result.data.access_code,
        reference: result.data.reference
      }
    });

  } catch (error) {
    console.error('Payment initialization error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during payment initialization'
    });
  }
});

/**
 * Verify Paystack payment - SECURE SERVER-SIDE ENDPOINT
 */
router.get('/verify/:reference', authenticateUser, async (req, res) => {
  try {
    const { reference } = req.params;
    
    if (!reference || !/^[a-zA-Z0-9_-]+$/.test(reference)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment reference format'
      });
    }

    const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      }
    });

    const result = await response.json();
    
    if (!result.status) {
      return res.status(400).json({
        success: false,
        error: result.message || 'Payment verification failed'
      });
    }

    const transactionData = result.data;
    
    // Verify the transaction belongs to the authenticated user
    if (transactionData.metadata?.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized: Cannot verify payment for another user'
      });
    }

    // Log payment verification (without sensitive data)
    console.log('Payment verified:', {
      reference,
      user_id: req.user.id,
      status: transactionData.status,
      amount: transactionData.amount / 100, // Convert from kobo
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      data: transactionData
    });

  } catch (error) {
    console.error('Payment verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during payment verification'
    });
  }
});

module.exports = router;
