/**
 * COMPREHENSIVE UNIT TESTS FOR ENCRYPTION SERVICE
 * 
 * This test suite provides comprehensive coverage for the encryption service
 * with focus on:
 * - AES-256-GCM encryption/decryption
 * - HIPAA compliance for medical data
 * - Key derivation and management
 * - Secure random generation
 * - Error handling and recovery
 * - Performance optimization
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - All medical data must be encrypted at rest
 * - Encryption keys must never be stored persistently
 * - Authentication tags must prevent tampering
 * - Key rotation must be tested thoroughly
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import encryptionService from '../../utils/encryptionService';

describe('EncryptionService', () => {
  let mockCrypto;
  let mockKey;
  let mockEncryptedData;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock crypto key
    mockKey = { type: 'secret', algorithm: { name: 'AES-GCM' } };
    
    // Setup mock encrypted data
    mockEncryptedData = new ArrayBuffer(64);
    
    // Setup crypto mocks
    mockCrypto = {
      getRandomValues: vi.fn((arr) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256);
        }
        return arr;
      }),
      subtle: {
        importKey: vi.fn(() => Promise.resolve(mockKey)),
        deriveKey: vi.fn(() => Promise.resolve(mockKey)),
        deriveBits: vi.fn(() => Promise.resolve(new ArrayBuffer(32))),
        encrypt: vi.fn(() => Promise.resolve(mockEncryptedData)),
        decrypt: vi.fn(() => Promise.resolve(new ArrayBuffer(32))),
        digest: vi.fn(() => Promise.resolve(new ArrayBuffer(32)))
      }
    };
    
    // Use proper crypto mocking technique
    vi.stubGlobal('crypto', mockCrypto);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Medical Data Encryption', () => {
    describe('encryptMedicalData', () => {
      it('should successfully encrypt medical data with AES-256-GCM', async () => {
        // Arrange
        const medicalData = global.mockMedicalData.condition;
        const sessionToken = 'test-session-token';

        // Act
        const result = await encryptionService.encryptMedicalData(medicalData, sessionToken);

        // Assert
        expect(result.success).toBe(true);
        expect(result.encrypted).toBeDefined();
        expect(result.salt).toBeDefined();
        expect(result.iv).toBeDefined();
        expect(result.tag).toBeDefined();
        expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'AES-GCM',
            iv: expect.any(Uint8Array),
            tagLength: 128
          }),
          mockKey,
          expect.any(Uint8Array)
        );
      });

      it('should generate unique salt and IV for each encryption', async () => {
        // Arrange
        const medicalData = global.mockMedicalData.condition;
        const sessionToken = 'test-session-token';

        // Act
        const result1 = await encryptionService.encryptMedicalData(medicalData, sessionToken);
        const result2 = await encryptionService.encryptMedicalData(medicalData, sessionToken);

        // Assert
        expect(result1.success).toBe(true);
        expect(result2.success).toBe(true);
        expect(result1.salt).not.toBe(result2.salt);
        expect(result1.iv).not.toBe(result2.iv);
      });

      it('should handle encryption of emergency medical data with priority', async () => {
        // Arrange
        const emergencyData = global.mockEmergencyData.condition;
        const sessionToken = 'test-session-token';

        // Act
        const result = await encryptionService.encryptMedicalData(
          emergencyData, 
          sessionToken, 
          { isEmergency: true }
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.priority).toBe('emergency');
        expect(result.encrypted).toBeDefined();
      });

      it('should validate session token before encryption', async () => {
        // Arrange
        const medicalData = global.mockMedicalData.condition;
        const invalidTokens = [null, undefined, '', 'short'];

        // Act & Assert
        for (const token of invalidTokens) {
          const result = await encryptionService.encryptMedicalData(medicalData, token);
          expect(result.success).toBe(false);
          expect(result.error).toContain('session token');
        }
      });

      it('should handle encryption errors gracefully', async () => {
        // Arrange
        const medicalData = global.mockMedicalData.condition;
        const sessionToken = 'test-session-token';
        mockCrypto.subtle.encrypt.mockRejectedValue(new Error('Encryption failed'));

        // Act
        const result = await encryptionService.encryptMedicalData(medicalData, sessionToken);

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('Encryption failed');
      });

      it('should encrypt large medical datasets efficiently', async () => {
        // Arrange
        const largeMedicalData = {
          conditions: Array(100).fill(global.mockMedicalData.condition),
          medications: Array(100).fill(global.mockMedicalData.medication),
          symptoms: Array(100).fill(global.mockMedicalData.symptom)
        };
        const sessionToken = 'test-session-token';
        const startTime = performance.now();

        // Act
        const result = await encryptionService.encryptMedicalData(largeMedicalData, sessionToken);
        const endTime = performance.now();

        // Assert
        expect(result.success).toBe(true);
        expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
      });
    });

    describe('decryptMedicalData', () => {
      it('should successfully decrypt medical data', async () => {
        // Arrange
        const encryptedData = global.mockEncryptionData;
        const sessionToken = 'test-session-token';
        
        // Mock successful decryption
        const originalData = JSON.stringify(global.mockMedicalData.condition);
        mockCrypto.subtle.decrypt.mockResolvedValue(
          new TextEncoder().encode(originalData).buffer
        );

        // Act
        const result = await encryptionService.decryptMedicalData(encryptedData, sessionToken);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toEqual(global.mockMedicalData.condition);
        expect(mockCrypto.subtle.decrypt).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'AES-GCM',
            iv: expect.any(Uint8Array),
            tagLength: 128
          }),
          mockKey,
          expect.any(ArrayBuffer)
        );
      });

      it('should detect tampering through authentication tag verification', async () => {
        // Arrange
        const tamperedData = {
          ...global.mockEncryptionData,
          tag: 'tampered-tag-base64'
        };
        const sessionToken = 'test-session-token';
        
        // Mock decryption failure due to tampering
        mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Authentication tag verification failed'));

        // Act
        const result = await encryptionService.decryptMedicalData(tamperedData, sessionToken);

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('tampering detected');
      });

      it('should handle decryption with wrong session token', async () => {
        // Arrange
        const encryptedData = global.mockEncryptionData;
        const wrongSessionToken = 'wrong-session-token';

        // Act
        const result = await encryptionService.decryptMedicalData(encryptedData, wrongSessionToken);

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('decryption failed');
      });

      it('should validate encrypted data structure before decryption', async () => {
        // Arrange
        const invalidEncryptedData = [
          null,
          undefined,
          {},
          { encrypted: 'data' }, // Missing required fields
          { salt: 'salt', iv: 'iv' } // Missing encrypted and tag
        ];
        const sessionToken = 'test-session-token';

        // Act & Assert
        for (const data of invalidEncryptedData) {
          const result = await encryptionService.decryptMedicalData(data, sessionToken);
          expect(result.success).toBe(false);
          expect(result.error).toContain('invalid');
        }
      });
    });
  });

  describe('Key Management', () => {
    describe('deriveEncryptionKey', () => {
      it('should derive encryption key from session token and salt', async () => {
        // Arrange
        const sessionToken = 'test-session-token';
        const salt = new Uint8Array(32);

        // Act
        const result = await encryptionService.deriveEncryptionKey(sessionToken, salt);

        // Assert
        expect(result).toBe(mockKey);
        expect(mockCrypto.subtle.importKey).toHaveBeenCalled();
        expect(mockCrypto.subtle.deriveKey).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'PBKDF2',
            salt: salt,
            iterations: expect.any(Number),
            hash: 'SHA-256'
          }),
          expect.any(Object),
          expect.objectContaining({
            name: 'AES-GCM',
            length: 256
          }),
          false,
          ['encrypt', 'decrypt']
        );
      });

      it('should use sufficient iterations for key derivation', async () => {
        // Arrange
        const sessionToken = 'test-session-token';
        const salt = new Uint8Array(32);

        // Act
        await encryptionService.deriveEncryptionKey(sessionToken, salt);

        // Assert
        expect(mockCrypto.subtle.deriveKey).toHaveBeenCalledWith(
          expect.objectContaining({
            iterations: expect.any(Number)
          }),
          expect.any(Object),
          expect.any(Object),
          false,
          ['encrypt', 'decrypt']
        );
        
        // Verify iterations are sufficient (at least 100,000)
        const call = mockCrypto.subtle.deriveKey.mock.calls[0];
        expect(call[0].iterations).toBeGreaterThanOrEqual(100000);
      });
    });

    describe('rotateEncryptionKey', () => {
      it('should rotate encryption key and re-encrypt data', async () => {
        // Arrange
        const oldSessionToken = 'old-session-token';
        const newSessionToken = 'new-session-token';
        const encryptedData = global.mockEncryptionData;

        // Mock successful decryption and re-encryption
        const originalData = JSON.stringify(global.mockMedicalData.condition);
        mockCrypto.subtle.decrypt.mockResolvedValue(
          new TextEncoder().encode(originalData).buffer
        );

        // Act
        const result = await encryptionService.rotateEncryptionKey(
          encryptedData, 
          oldSessionToken, 
          newSessionToken
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.encrypted).toBeDefined();
        expect(result.salt).not.toBe(encryptedData.salt); // New salt should be generated
        expect(mockCrypto.subtle.decrypt).toHaveBeenCalled();
        expect(mockCrypto.subtle.encrypt).toHaveBeenCalled();
      });

      it('should handle key rotation failures gracefully', async () => {
        // Arrange
        const oldSessionToken = 'old-session-token';
        const newSessionToken = 'new-session-token';
        const encryptedData = global.mockEncryptionData;
        
        mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));

        // Act
        const result = await encryptionService.rotateEncryptionKey(
          encryptedData, 
          oldSessionToken, 
          newSessionToken
        );

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('key rotation failed');
      });
    });
  });

  describe('Security and Compliance', () => {
    it('should never store encryption keys persistently', async () => {
      // Arrange
      const medicalData = global.mockMedicalData.condition;
      const sessionToken = 'test-session-token';
      const localStorageSpy = vi.spyOn(localStorage, 'setItem');
      const sessionStorageSpy = vi.spyOn(sessionStorage, 'setItem');

      // Act
      await encryptionService.encryptMedicalData(medicalData, sessionToken);

      // Assert
      expect(localStorageSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('key'),
        expect.anything()
      );
      expect(sessionStorageSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('key'),
        expect.anything()
      );
    });

    it('should use cryptographically secure random number generation', async () => {
      // Arrange
      const medicalData = global.mockMedicalData.condition;
      const sessionToken = 'test-session-token';

      // Act
      await encryptionService.encryptMedicalData(medicalData, sessionToken);

      // Assert
      expect(mockCrypto.getRandomValues).toHaveBeenCalled();
      
      // Verify it's called for both salt and IV generation
      expect(mockCrypto.getRandomValues).toHaveBeenCalledTimes(2);
    });

    it('should comply with HIPAA encryption requirements', async () => {
      // Arrange
      const medicalData = global.mockMedicalData.condition;
      const sessionToken = 'test-session-token';

      // Act
      const result = await encryptionService.encryptMedicalData(medicalData, sessionToken);

      // Assert
      expect(result.success).toBe(true);
      
      // Verify AES-256-GCM is used (HIPAA compliant)
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          tagLength: 128 // Authentication tag for integrity
        }),
        expect.objectContaining({
          algorithm: expect.objectContaining({
            name: 'AES-GCM'
          })
        }),
        expect.any(Uint8Array)
      );
    });

    it('should handle encryption service availability', async () => {
      // Arrange
      const medicalData = global.mockMedicalData.condition;
      const sessionToken = 'test-session-token';
      
      // Mock crypto API unavailable
      vi.stubGlobal('crypto', undefined);

      // Act
      const result = await encryptionService.encryptMedicalData(medicalData, sessionToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('encryption not available');
    });
  });

  describe('Performance and Optimization', () => {
    it('should encrypt data within acceptable time limits', async () => {
      // Arrange
      const medicalData = global.mockMedicalData.condition;
      const sessionToken = 'test-session-token';
      const startTime = performance.now();

      // Act
      const result = await encryptionService.encryptMedicalData(medicalData, sessionToken);
      const endTime = performance.now();

      // Assert
      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle concurrent encryption operations', async () => {
      // Arrange
      const medicalData = global.mockMedicalData.condition;
      const sessionToken = 'test-session-token';
      const concurrentOperations = Array(10).fill().map(() => 
        encryptionService.encryptMedicalData(medicalData, sessionToken)
      );

      // Act
      const results = await Promise.all(concurrentOperations);

      // Assert
      expect(results.length).toBe(10);
      expect(results.every(result => result.success)).toBe(true);
      
      // Verify each encryption has unique salt and IV
      const salts = results.map(result => result.salt);
      const ivs = results.map(result => result.iv);
      expect(new Set(salts).size).toBe(10); // All salts should be unique
      expect(new Set(ivs).size).toBe(10); // All IVs should be unique
    });
  });
});
