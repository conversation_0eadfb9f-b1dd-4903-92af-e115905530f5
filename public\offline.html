<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VoiceHealth AI - Offline Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .offline-container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 480px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            background: #f0f4ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: #3b82f6;
            font-size: 36px;
        }
        
        .offline-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }
        
        .offline-message {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.5;
        }
        
        .offline-features {
            background: #f9fafb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            text-align: left;
        }
        
        .offline-features h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: #4b5563;
        }
        
        .feature-list li:before {
            content: "✓";
            background: #10b981;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
        }
        
        .retry-button:hover {
            background: #2563eb;
        }
        
        .retry-button:active {
            background: #1d4ed8;
        }
        
        .emergency-access {
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }
        
        .emergency-button {
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .emergency-button:hover {
            background: #b91c1c;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 24px;
            }
            
            .offline-title {
                font-size: 20px;
            }
            
            .offline-message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">You're Currently Offline</h1>
        
        <p class="offline-message">
            Don't worry! VoiceHealth AI works offline too. You can still access your health records and use emergency consultation features.
        </p>
        
        <div class="offline-features">
            <h3>Available Offline Features</h3>
            <ul class="feature-list">
                <li>View cached medical records</li>
                <li>Access emergency consultation protocols</li>
                <li>Log vital signs and symptoms</li>
                <li>Use offline symptom checker</li>
                <li>Review previous consultation history</li>
            </ul>
        </div>
        
        <button class="retry-button" onclick="retryConnection()">
            Try Again
        </button>
        
        <div class="emergency-access">
            <p style="font-size: 14px; color: #6b7280; margin-bottom: 12px;">
                Need immediate medical assistance?
            </p>
            <a href="/emergency-offline-consultation" class="emergency-button">
                Emergency Mode
            </a>
        </div>
    </div>
    
    <script>
        function retryConnection() {
            const button = document.querySelector('.retry-button');
            button.textContent = 'Checking Connection...';
            button.disabled = true;
            
            // Check if we're back online
            if (navigator.onLine) {
                window.location.reload();
            } else {
                // Try to fetch a small resource to test connectivity
                fetch('/', { cache: 'no-cache' })
                    .then(() => {
                        window.location.reload();
                    })
                    .catch(() => {
                        button.textContent = 'Still Offline - Try Again';
                        button.disabled = false;
                        
                        // Show a temporary message
                        const message = document.createElement('div');
                        message.textContent = 'Still no connection. Please check your internet and try again.';
                        message.style.cssText = 'color: #dc2626; font-size: 14px; margin-top: 12px;';
                        button.parentNode.appendChild(message);
                        
                        setTimeout(() => {
                            if (message.parentNode) {
                                message.parentNode.removeChild(message);
                            }
                        }, 3000);
                    });
            }
        }
        
        // Listen for online events
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Check connection status periodically
        setInterval(() => {
            if (navigator.onLine) {
                fetch('/', { cache: 'no-cache' })
                    .then(() => {
                        window.location.reload();
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 30000); // Check every 30 seconds
    </script>
</body>
</html>