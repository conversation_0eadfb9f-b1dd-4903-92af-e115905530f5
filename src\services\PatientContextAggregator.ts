/**
 * PATIENT CONTEXT AGGREGATOR
 * 
 * Comprehensive patient profiling system that synthesizes medical history,
 * risk stratification, and contextual patient information to enhance
 * agent performance and provide personalized healthcare experiences.
 * 
 * FEATURES:
 * - Comprehensive patient profile construction from conversation history
 * - Medical history synthesis and timeline reconstruction
 * - Risk stratification and predictive health modeling
 * - Medication interaction and allergy tracking
 * - Social determinants of health integration
 * - Care coordination and provider communication
 * - Longitudinal health trend analysis
 * - Personalized care recommendations
 */

import { EventEmitter } from 'events';
import { contextualMemoryEngine, type ContextualMemory, type MedicalConcept } from './ContextualMemoryEngine';
import { memoryManager } from './MemoryManager';
import type { ConversationMessage } from '../types/memory';

export interface ComprehensivePatientProfile {
  patientId: string;
  demographics: PatientDemographics;
  medicalHistory: MedicalHistoryProfile;
  currentHealth: CurrentHealthStatus;
  riskProfile: RiskStratificationProfile;
  medicationProfile: MedicationProfile;
  socialContext: SocialDeterminantsProfile;
  careCoordination: CareCoordinationProfile;
  preferences: PatientPreferences;
  healthTrends: HealthTrendAnalysis;
  contextualInsights: PatientContextualInsights;
  lastUpdated: string;
  dataQuality: DataQualityMetrics;
}

export interface PatientDemographics {
  age?: number;
  ageRange?: string;
  gender?: string;
  ethnicity?: string;
  primaryLanguage?: string;
  location?: GeographicContext;
  occupation?: string;
  educationLevel?: string;
  insuranceStatus?: InsuranceInformation;
}

export interface GeographicContext {
  country?: string;
  region?: string;
  urbanRural?: 'urban' | 'suburban' | 'rural';
  healthcareAccess?: 'excellent' | 'good' | 'limited' | 'poor';
  environmentalFactors?: string[];
}

export interface InsuranceInformation {
  type?: 'private' | 'public' | 'uninsured' | 'unknown';
  coverage?: 'comprehensive' | 'basic' | 'limited';
  barriers?: string[];
}

export interface MedicalHistoryProfile {
  chronicConditions: ChronicCondition[];
  pastIllnesses: PastIllness[];
  surgicalHistory: SurgicalProcedure[];
  familyHistory: FamilyHistoryItem[];
  immunizationStatus: ImmunizationRecord[];
  allergyProfile: AllergyProfile;
  medicalTimeline: MedicalTimelineEvent[];
  diagnosticHistory: DiagnosticResult[];
}

export interface ChronicCondition {
  condition: string;
  icdCode?: string;
  diagnosisDate?: string;
  severity: 'mild' | 'moderate' | 'severe';
  controlStatus: 'well_controlled' | 'partially_controlled' | 'poorly_controlled' | 'unknown';
  complications?: string[];
  lastAssessment?: string;
  managementPlan?: string[];
}

export interface PastIllness {
  illness: string;
  date?: string;
  severity: 'mild' | 'moderate' | 'severe';
  complications?: string[];
  treatment?: string;
  resolved: boolean;
}

export interface SurgicalProcedure {
  procedure: string;
  date?: string;
  indication: string;
  complications?: string[];
  outcome: 'successful' | 'complicated' | 'failed' | 'unknown';
}

export interface FamilyHistoryItem {
  relationship: string;
  condition: string;
  ageOfOnset?: number;
  deceased?: boolean;
  causeOfDeath?: string;
}

export interface ImmunizationRecord {
  vaccine: string;
  date?: string;
  status: 'up_to_date' | 'overdue' | 'contraindicated' | 'unknown';
  nextDue?: string;
}

export interface AllergyProfile {
  drugAllergies: DrugAllergy[];
  foodAllergies: FoodAllergy[];
  environmentalAllergies: EnvironmentalAllergy[];
  allergyTesting?: AllergyTestResult[];
}

export interface DrugAllergy {
  medication: string;
  reaction: string;
  severity: 'mild' | 'moderate' | 'severe' | 'life_threatening';
  verified: boolean;
  crossReactivities?: string[];
}

export interface FoodAllergy {
  food: string;
  reaction: string;
  severity: 'mild' | 'moderate' | 'severe' | 'life_threatening';
  verified: boolean;
}

export interface EnvironmentalAllergy {
  allergen: string;
  reaction: string;
  seasonal?: boolean;
  severity: 'mild' | 'moderate' | 'severe';
}

export interface AllergyTestResult {
  allergen: string;
  testType: string;
  result: string;
  date: string;
}

export interface CurrentHealthStatus {
  chiefComplaints: ChiefComplaint[];
  currentSymptoms: CurrentSymptom[];
  vitalSigns: VitalSignsProfile;
  functionalStatus: FunctionalAssessment;
  mentalHealthStatus: MentalHealthAssessment;
  painAssessment: PainAssessment;
  sleepAssessment: SleepAssessment;
  nutritionalStatus: NutritionalAssessment;
}

export interface ChiefComplaint {
  complaint: string;
  duration: string;
  severity: number; // 1-10 scale
  associatedSymptoms: string[];
  aggravatingFactors?: string[];
  relievingFactors?: string[];
  previousOccurrence?: boolean;
}

export interface CurrentSymptom {
  symptom: string;
  onset: string;
  duration: string;
  severity: number;
  quality: string;
  location?: string;
  radiation?: string;
  timing: string;
  context: string;
  modifyingFactors: string[];
}

export interface VitalSignsProfile {
  bloodPressure?: BloodPressureReading[];
  heartRate?: number;
  temperature?: number;
  respiratoryRate?: number;
  oxygenSaturation?: number;
  weight?: number;
  height?: number;
  bmi?: number;
  lastMeasured?: string;
}

export interface BloodPressureReading {
  systolic: number;
  diastolic: number;
  date: string;
  position?: string;
  cuffSize?: string;
}

export interface RiskStratificationProfile {
  cardiovascularRisk: RiskAssessment;
  diabetesRisk: RiskAssessment;
  cancerRisk: RiskAssessment;
  fallRisk: RiskAssessment;
  medicationRisk: RiskAssessment;
  overallRiskScore: number;
  riskFactors: RiskFactor[];
  protectiveFactors: ProtectiveFactor[];
  riskMitigationStrategies: RiskMitigationStrategy[];
}

export interface RiskAssessment {
  riskLevel: 'low' | 'moderate' | 'high' | 'very_high';
  riskScore?: number;
  calculationMethod?: string;
  contributingFactors: string[];
  timeframe: string;
  lastAssessed: string;
  recommendations: string[];
}

export interface RiskFactor {
  factor: string;
  category: 'modifiable' | 'non_modifiable';
  impact: 'low' | 'moderate' | 'high';
  evidence: string;
  interventions?: string[];
}

export interface ProtectiveFactor {
  factor: string;
  impact: 'low' | 'moderate' | 'high';
  evidence: string;
  recommendations?: string[];
}

export interface RiskMitigationStrategy {
  strategy: string;
  targetRisk: string;
  priority: 'low' | 'medium' | 'high';
  timeline: string;
  expectedOutcome: string;
  monitoringPlan: string;
}

export interface MedicationProfile {
  currentMedications: CurrentMedication[];
  medicationHistory: MedicationHistoryItem[];
  adherenceProfile: MedicationAdherence;
  interactions: DrugInteraction[];
  contraindications: MedicationContraindication[];
  pharmacogenomics?: PharmacogenomicProfile;
  medicationReconciliation: MedicationReconciliationStatus;
}

export interface CurrentMedication {
  medication: string;
  genericName?: string;
  dosage: string;
  frequency: string;
  route: string;
  indication: string;
  prescriber?: string;
  startDate?: string;
  endDate?: string;
  adherence?: 'excellent' | 'good' | 'fair' | 'poor';
  sideEffects?: string[];
  effectiveness?: 'very_effective' | 'effective' | 'somewhat_effective' | 'ineffective';
}

export interface SocialDeterminantsProfile {
  socioeconomicStatus: SocioeconomicFactors;
  socialSupport: SocialSupportNetwork;
  healthBehaviors: HealthBehaviorProfile;
  environmentalFactors: EnvironmentalFactors;
  culturalFactors: CulturalConsiderations;
  healthLiteracy: HealthLiteracyAssessment;
  accessBarriers: HealthcareAccessBarriers;
}

export interface SocioeconomicFactors {
  employmentStatus?: string;
  incomeLevel?: 'low' | 'moderate' | 'high';
  housingStability?: 'stable' | 'unstable' | 'homeless';
  foodSecurity?: 'secure' | 'insecure';
  transportationAccess?: 'reliable' | 'limited' | 'none';
  financialStress?: 'low' | 'moderate' | 'high';
}

export interface HealthBehaviorProfile {
  smokingStatus: 'never' | 'former' | 'current';
  alcoholUse: 'none' | 'light' | 'moderate' | 'heavy';
  substanceUse?: SubstanceUseProfile;
  physicalActivity: PhysicalActivityProfile;
  dietaryPatterns: DietaryProfile;
  sleepHygiene: SleepHygieneProfile;
  stressManagement: StressManagementProfile;
}

export interface PatientContextualInsights {
  communicationPreferences: CommunicationPreferences;
  healthGoals: HealthGoal[];
  barriers: HealthcareBarrier[];
  motivators: HealthMotivator[];
  decisionMakingStyle: DecisionMakingProfile;
  healthBeliefs: HealthBeliefProfile;
  careExperiences: CareExperienceProfile;
}

export class PatientContextAggregator extends EventEmitter {
  private patientProfiles: Map<string, ComprehensivePatientProfile> = new Map();
  private profileUpdateQueue: Map<string, ProfileUpdateTask[]> = new Map();

  constructor() {
    super();
    console.log('👤 Initializing Patient Context Aggregator...');
    this.startProfileProcessing();
  }

  /**
   * Build comprehensive patient profile from conversation history
   */
  async buildPatientProfile(sessionId: string, patientId?: string): Promise<ComprehensivePatientProfile> {
    try {
      console.log(`👤 Building patient profile for session: ${sessionId}`);

      const effectivePatientId = patientId || sessionId;

      // Get existing profile or create new one
      let profile = this.patientProfiles.get(effectivePatientId) || this.createEmptyProfile(effectivePatientId);

      // Get conversation history
      const conversationHistory = await memoryManager.getConversationHistory(sessionId);

      // Get contextual memories
      const contextualMemories = await contextualMemoryEngine.searchContextualMemories({
        query: '',
        sessionId,
        maxResults: 100,
        includeRelated: true
      });

      // Extract and synthesize information
      profile = await this.synthesizePatientInformation(profile, conversationHistory, contextualMemories);

      // Perform risk stratification
      profile.riskProfile = await this.performRiskStratification(profile);

      // Analyze health trends
      profile.healthTrends = await this.analyzeHealthTrends(profile, contextualMemories);

      // Generate contextual insights
      profile.contextualInsights = await this.generatePatientInsights(profile, conversationHistory);

      // Calculate data quality metrics
      profile.dataQuality = this.calculateDataQuality(profile);

      // Update timestamp
      profile.lastUpdated = new Date().toISOString();

      // Store updated profile
      this.patientProfiles.set(effectivePatientId, profile);

      console.log(`✅ Patient profile built/updated for: ${effectivePatientId}`);
      this.emit('profile_updated', profile);

      return profile;

    } catch (error) {
      console.error('❌ Failed to build patient profile:', error);
      throw error;
    }
  }

  /**
   * Get enriched patient context for agent decision making
   */
  async getEnrichedPatientContext(sessionId: string, currentMessage: string): Promise<EnrichedPatientContext> {
    try {
      const profile = await this.buildPatientProfile(sessionId);
      
      // Get relevant contextual information based on current message
      const relevantHistory = await this.getRelevantMedicalHistory(profile, currentMessage);
      const riskAlerts = this.generateRiskAlerts(profile, currentMessage);
      const careRecommendations = await this.generateCareRecommendations(profile, currentMessage);
      const contextualFlags = this.identifyContextualFlags(profile, currentMessage);

      return {
        profile,
        relevantHistory,
        riskAlerts,
        careRecommendations,
        contextualFlags,
        patientSummary: this.generatePatientSummary(profile),
        clinicalDecisionSupport: await this.generateClinicalDecisionSupport(profile, currentMessage),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Failed to get enriched patient context:', error);
      throw error;
    }
  }

  /**
   * Create empty patient profile template
   */
  private createEmptyProfile(patientId: string): ComprehensivePatientProfile {
    return {
      patientId,
      demographics: {},
      medicalHistory: {
        chronicConditions: [],
        pastIllnesses: [],
        surgicalHistory: [],
        familyHistory: [],
        immunizationStatus: [],
        allergyProfile: {
          drugAllergies: [],
          foodAllergies: [],
          environmentalAllergies: []
        },
        medicalTimeline: [],
        diagnosticHistory: []
      },
      currentHealth: {
        chiefComplaints: [],
        currentSymptoms: [],
        vitalSigns: {},
        functionalStatus: {} as FunctionalAssessment,
        mentalHealthStatus: {} as MentalHealthAssessment,
        painAssessment: {} as PainAssessment,
        sleepAssessment: {} as SleepAssessment,
        nutritionalStatus: {} as NutritionalAssessment
      },
      riskProfile: {
        cardiovascularRisk: {} as RiskAssessment,
        diabetesRisk: {} as RiskAssessment,
        cancerRisk: {} as RiskAssessment,
        fallRisk: {} as RiskAssessment,
        medicationRisk: {} as RiskAssessment,
        overallRiskScore: 0,
        riskFactors: [],
        protectiveFactors: [],
        riskMitigationStrategies: []
      },
      medicationProfile: {
        currentMedications: [],
        medicationHistory: [],
        adherenceProfile: {} as MedicationAdherence,
        interactions: [],
        contraindications: [],
        medicationReconciliation: {} as MedicationReconciliationStatus
      },
      socialContext: {
        socioeconomicStatus: {},
        socialSupport: {} as SocialSupportNetwork,
        healthBehaviors: {} as HealthBehaviorProfile,
        environmentalFactors: {} as EnvironmentalFactors,
        culturalFactors: {} as CulturalConsiderations,
        healthLiteracy: {} as HealthLiteracyAssessment,
        accessBarriers: {} as HealthcareAccessBarriers
      },
      careCoordination: {} as CareCoordinationProfile,
      preferences: {} as PatientPreferences,
      healthTrends: {} as HealthTrendAnalysis,
      contextualInsights: {
        communicationPreferences: {} as CommunicationPreferences,
        healthGoals: [],
        barriers: [],
        motivators: [],
        decisionMakingStyle: {} as DecisionMakingProfile,
        healthBeliefs: {} as HealthBeliefProfile,
        careExperiences: {} as CareExperienceProfile
      },
      lastUpdated: new Date().toISOString(),
      dataQuality: {
        completeness: 0,
        accuracy: 0,
        timeliness: 0,
        consistency: 0,
        overallScore: 0
      } as DataQualityMetrics
    };
  }

  /**
   * Synthesize patient information from various sources
   */
  private async synthesizePatientInformation(
    profile: ComprehensivePatientProfile,
    conversationHistory: ConversationMessage[],
    contextualMemories: any[]
  ): Promise<ComprehensivePatientProfile> {
    // Extract demographics from conversations
    profile.demographics = await this.extractDemographics(conversationHistory);

    // Extract medical history
    profile.medicalHistory = await this.extractMedicalHistory(conversationHistory, contextualMemories);

    // Extract current health status
    profile.currentHealth = await this.extractCurrentHealth(conversationHistory, contextualMemories);

    // Extract medication information
    profile.medicationProfile = await this.extractMedicationProfile(conversationHistory, contextualMemories);

    // Extract social determinants
    profile.socialContext = await this.extractSocialContext(conversationHistory);

    return profile;
  }

  // Placeholder implementations for complex extraction methods
  private async extractDemographics(history: ConversationMessage[]): Promise<PatientDemographics> {
    return {};
  }

  private async extractMedicalHistory(history: ConversationMessage[], memories: any[]): Promise<MedicalHistoryProfile> {
    return this.createEmptyProfile('').medicalHistory;
  }

  private async extractCurrentHealth(history: ConversationMessage[], memories: any[]): Promise<CurrentHealthStatus> {
    return this.createEmptyProfile('').currentHealth;
  }

  private async extractMedicationProfile(history: ConversationMessage[], memories: any[]): Promise<MedicationProfile> {
    return this.createEmptyProfile('').medicationProfile;
  }

  private async extractSocialContext(history: ConversationMessage[]): Promise<SocialDeterminantsProfile> {
    return this.createEmptyProfile('').socialContext;
  }

  private async performRiskStratification(profile: ComprehensivePatientProfile): Promise<RiskStratificationProfile> {
    return profile.riskProfile;
  }

  private async analyzeHealthTrends(profile: ComprehensivePatientProfile, memories: any[]): Promise<HealthTrendAnalysis> {
    return {} as HealthTrendAnalysis;
  }

  private async generatePatientInsights(profile: ComprehensivePatientProfile, history: ConversationMessage[]): Promise<PatientContextualInsights> {
    return profile.contextualInsights;
  }

  private calculateDataQuality(profile: ComprehensivePatientProfile): DataQualityMetrics {
    return {
      completeness: 0.7,
      accuracy: 0.8,
      timeliness: 0.9,
      consistency: 0.8,
      overallScore: 0.8
    };
  }

  private async getRelevantMedicalHistory(profile: ComprehensivePatientProfile, message: string): Promise<any[]> {
    return [];
  }

  private generateRiskAlerts(profile: ComprehensivePatientProfile, message: string): RiskAlert[] {
    return [];
  }

  private async generateCareRecommendations(profile: ComprehensivePatientProfile, message: string): Promise<CareRecommendation[]> {
    return [];
  }

  private identifyContextualFlags(profile: ComprehensivePatientProfile, message: string): ContextualFlag[] {
    return [];
  }

  private generatePatientSummary(profile: ComprehensivePatientProfile): PatientSummary {
    return {} as PatientSummary;
  }

  private async generateClinicalDecisionSupport(profile: ComprehensivePatientProfile, message: string): Promise<ClinicalDecisionSupport> {
    return {} as ClinicalDecisionSupport;
  }

  private startProfileProcessing(): void {
    // Start background profile processing
  }
}

// Additional interfaces
interface EnrichedPatientContext {
  profile: ComprehensivePatientProfile;
  relevantHistory: any[];
  riskAlerts: RiskAlert[];
  careRecommendations: CareRecommendation[];
  contextualFlags: ContextualFlag[];
  patientSummary: PatientSummary;
  clinicalDecisionSupport: ClinicalDecisionSupport;
  timestamp: string;
}

interface RiskAlert {
  type: string;
  severity: string;
  description: string;
  recommendation: string;
}

interface CareRecommendation {
  type: string;
  priority: string;
  description: string;
  evidence: string;
}

interface ContextualFlag {
  type: string;
  description: string;
  actionRequired: boolean;
}

interface PatientSummary {
  keyPoints: string[];
  riskFactors: string[];
  activeProblems: string[];
  medications: string[];
}

interface ClinicalDecisionSupport {
  recommendations: string[];
  alerts: string[];
  guidelines: string[];
}

interface DataQualityMetrics {
  completeness: number;
  accuracy: number;
  timeliness: number;
  consistency: number;
  overallScore: number;
}

// Placeholder interfaces for complex types
interface ProfileUpdateTask { }
interface MedicationHistoryItem { }
interface MedicationAdherence { }
interface DrugInteraction { }
interface MedicationContraindication { }
interface PharmacogenomicProfile { }
interface MedicationReconciliationStatus { }
interface SocialSupportNetwork { }
interface EnvironmentalFactors { }
interface CulturalConsiderations { }
interface HealthLiteracyAssessment { }
interface HealthcareAccessBarriers { }
interface CareCoordinationProfile { }
interface PatientPreferences { }
interface HealthTrendAnalysis { }
interface CommunicationPreferences { }
interface HealthGoal { }
interface HealthcareBarrier { }
interface HealthMotivator { }
interface DecisionMakingProfile { }
interface HealthBeliefProfile { }
interface CareExperienceProfile { }
interface FunctionalAssessment { }
interface MentalHealthAssessment { }
interface PainAssessment { }
interface SleepAssessment { }
interface NutritionalAssessment { }
interface SubstanceUseProfile { }
interface PhysicalActivityProfile { }
interface DietaryProfile { }
interface SleepHygieneProfile { }
interface StressManagementProfile { }
interface MedicalTimelineEvent { }
interface DiagnosticResult { }

// Export singleton instance
export const patientContextAggregator = new PatientContextAggregator();
export default patientContextAggregator;
