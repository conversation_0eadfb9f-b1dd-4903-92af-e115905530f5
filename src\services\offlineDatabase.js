/**
 * Offline Database Service using Dexie.js
 * Versioned IndexedDB schema for PWA offline functionality
 */

import <PERSON>ie from 'dexie';

class OfflineDatabase extends <PERSON>ie {
  constructor() {
    super('VoiceHealthDB');
    
    // Version 1: Initial schema
    this.version(1).stores({
      consultations: '++id, sessionId, timestamp, status, patientData',
      messages: '++id, sessionId, role, content, timestamp, synced',
      audioChunks: '++id, sessionId, chunkIndex, audioData, timestamp, synced',
      triageResults: '++id, sessionId, result, timestamp, synced',
      syncQueue: '++id, type, data, timestamp, retryCount, status'
    });

    // Version 2: Add user preferences and cache
    this.version(2).stores({
      consultations: '++id, sessionId, timestamp, status, patientData',
      messages: '++id, sessionId, role, content, timestamp, synced',
      audioChunks: '++id, sessionId, chunkIndex, audioData, timestamp, synced',
      triageResults: '++id, sessionId, result, timestamp, synced',
      syncQueue: '++id, type, data, timestamp, retryCount, status',
      userPreferences: '++id, key, value, timestamp',
      responseCache: '++id, requestHash, response, timestamp, ttl'
    });

    // Version 3: Add analytics and error logs
    this.version(3).stores({
      consultations: '++id, sessionId, timestamp, status, patientData, region',
      messages: '++id, sessionId, role, content, timestamp, synced, provider',
      audioChunks: '++id, sessionId, chunkIndex, audioData, timestamp, synced, quality',
      triageResults: '++id, sessionId, result, timestamp, synced, confidence',
      syncQueue: '++id, type, data, timestamp, retryCount, status, priority',
      userPreferences: '++id, key, value, timestamp',
      responseCache: '++id, requestHash, response, timestamp, ttl',
      analyticsEvents: '++id, event, data, timestamp, synced',
      errorLogs: '++id, error, component, timestamp, synced, errorId'
    });

    // Table references
    this.consultations = this.consultations;
    this.messages = this.messages;
    this.audioChunks = this.audioChunks;
    this.triageResults = this.triageResults;
    this.syncQueue = this.syncQueue;
    this.userPreferences = this.userPreferences;
    this.responseCache = this.responseCache;
    this.analyticsEvents = this.analyticsEvents;
    this.errorLogs = this.errorLogs;

    this.setupHooks();
  }

  /**
   * Setup database hooks for automatic timestamps
   */
  setupHooks() {
    // Auto-timestamp on creation
    this.consultations.hook('creating', (primKey, obj, trans) => {
      obj.timestamp = obj.timestamp || new Date().toISOString();
    });

    this.messages.hook('creating', (primKey, obj, trans) => {
      obj.timestamp = obj.timestamp || new Date().toISOString();
      obj.synced = obj.synced || false;
    });

    this.syncQueue.hook('creating', (primKey, obj, trans) => {
      obj.timestamp = obj.timestamp || new Date().toISOString();
      obj.retryCount = obj.retryCount || 0;
      obj.status = obj.status || 'pending';
      obj.priority = obj.priority || 'normal';
    });
  }

  /**
   * Store consultation session
   */
  async storeConsultation(sessionData) {
    try {
      const consultation = {
        sessionId: sessionData.sessionId,
        patientData: sessionData.patientData,
        status: sessionData.status || 'active',
        region: sessionData.region || 'ghana',
        timestamp: new Date().toISOString()
      };

      const id = await this.consultations.add(consultation);
      return { id, ...consultation };
    } catch (error) {
      console.error('Failed to store consultation:', error);
      throw error;
    }
  }

  /**
   * Store message with auto-sync queuing
   */
  async storeMessage(message) {
    try {
      const messageData = {
        ...message,
        timestamp: message.timestamp || new Date().toISOString(),
        synced: false
      };

      const id = await this.messages.add(messageData);
      
      // Queue for sync if online
      await this.queueForSync('message', { id, ...messageData });
      
      return { id, ...messageData };
    } catch (error) {
      console.error('Failed to store message:', error);
      throw error;
    }
  }

  /**
   * Store audio chunk for offline persistence
   */
  async storeAudioChunk(sessionId, chunkIndex, audioData, quality = 'medium') {
    try {
      const chunk = {
        sessionId,
        chunkIndex,
        audioData: audioData instanceof ArrayBuffer ? audioData : await this.convertToArrayBuffer(audioData),
        quality,
        synced: false,
        timestamp: new Date().toISOString()
      };

      const id = await this.audioChunks.add(chunk);
      
      // Queue for sync
      await this.queueForSync('audioChunk', { id, sessionId, chunkIndex });
      
      return id;
    } catch (error) {
      console.error('Failed to store audio chunk:', error);
      throw error;
    }
  }

  /**
   * Store triage result
   */
  async storeTriageResult(sessionId, result) {
    try {
      const triageData = {
        sessionId,
        result,
        confidence: result.confidence || 0,
        synced: false,
        timestamp: new Date().toISOString()
      };

      const id = await this.triageResults.add(triageData);
      
      // Queue for sync
      await this.queueForSync('triageResult', { id, ...triageData });
      
      return { id, ...triageData };
    } catch (error) {
      console.error('Failed to store triage result:', error);
      throw error;
    }
  }

  /**
   * Queue item for background sync
   */
  async queueForSync(type, data, priority = 'normal') {
    try {
      const queueItem = {
        type,
        data,
        priority,
        timestamp: new Date().toISOString(),
        retryCount: 0,
        status: 'pending'
      };

      return await this.syncQueue.add(queueItem);
    } catch (error) {
      console.error('Failed to queue for sync:', error);
      throw error;
    }
  }

  /**
   * Get pending sync items
   */
  async getPendingSyncItems(limit = 50) {
    try {
      // Get items with status 'pending' or 'retry'
      const items = await this.syncQueue
        .where('status')
        .anyOfIgnoreCase(['pending', 'retry'])
        .reverse()
        .limit(limit)
        .toArray();
      
      // Sort by priority (high first, then normal)
      return items.sort((a, b) => {
        const priorityOrder = { 'high': 0, 'normal': 1, 'low': 2 };
        return (priorityOrder[a.priority] || 1) - (priorityOrder[b.priority] || 1);
      });
    } catch (error) {
      console.error('Failed to get pending sync items:', error);
      return [];
    }
  }

  /**
   * Mark sync item as completed
   */
  async markSyncCompleted(id) {
    try {
      await this.syncQueue.update(id, { 
        status: 'completed',
        completedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to mark sync completed:', error);
    }
  }

  /**
   * Increment retry count for failed sync
   */
  async incrementRetryCount(id) {
    try {
      const item = await this.syncQueue.get(id);
      if (item) {
        const newRetryCount = (item.retryCount || 0) + 1;
        const status = newRetryCount >= 5 ? 'failed' : 'retry';
        
        await this.syncQueue.update(id, {
          retryCount: newRetryCount,
          status,
          lastRetry: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to increment retry count:', error);
    }
  }

  /**
   * Get consultation history
   */
  async getConsultationHistory(limit = 20) {
    try {
      return await this.consultations
        .orderBy('timestamp')
        .reverse()
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('Failed to get consultation history:', error);
      return [];
    }
  }

  /**
   * Get session messages
   */
  async getSessionMessages(sessionId) {
    try {
      return await this.messages
        .where('sessionId')
        .equals(sessionId)
        .orderBy('timestamp')
        .toArray();
    } catch (error) {
      console.error('Failed to get session messages:', error);
      return [];
    }
  }

  /**
   * Cache API response
   */
  async cacheResponse(requestHash, response, ttlMinutes = 30) {
    try {
      const cacheData = {
        requestHash,
        response,
        ttl: ttlMinutes * 60 * 1000, // Convert to milliseconds
        timestamp: new Date().toISOString()
      };

      // Remove existing cache for this request
      await this.responseCache.where('requestHash').equals(requestHash).delete();
      
      return await this.responseCache.add(cacheData);
    } catch (error) {
      console.error('Failed to cache response:', error);
    }
  }

  /**
   * Get cached response
   */
  async getCachedResponse(requestHash) {
    try {
      const cached = await this.responseCache
        .where('requestHash')
        .equals(requestHash)
        .first();

      if (cached) {
        const age = Date.now() - new Date(cached.timestamp).getTime();
        if (age < cached.ttl) {
          return cached.response;
        } else {
          // Remove expired cache
          await this.responseCache.delete(cached.id);
        }
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get cached response:', error);
      return null;
    }
  }

  /**
   * Clean up old data
   */
  async cleanup(daysToKeep = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffISO = cutoffDate.toISOString();

      // Clean up old completed sync items
      await this.syncQueue
        .where('status')
        .equals('completed')
        .and(item => item.timestamp < cutoffISO)
        .delete();

      // Clean up old error logs
      await this.errorLogs
        .where('timestamp')
        .below(cutoffISO)
        .delete();

      // Clean up expired cache
      await this.responseCache
        .where('timestamp')
        .below(cutoffISO)
        .delete();

      console.log('Database cleanup completed');
    } catch (error) {
      console.error('Failed to cleanup database:', error);
    }
  }

  /**
   * Convert data to ArrayBuffer for storage
   */
  async convertToArrayBuffer(data) {
    if (data instanceof ArrayBuffer) return data;
    if (data instanceof Blob) {
      return await data.arrayBuffer();
    }
    // Convert string or other data
    const encoder = new TextEncoder();
    return encoder.encode(JSON.stringify(data)).buffer;
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      const stats = {
        consultations: await this.consultations.count(),
        messages: await this.messages.count(),
        audioChunks: await this.audioChunks.count(),
        syncPending: await this.syncQueue.where('status').equals('pending').count(),
        cacheSize: await this.responseCache.count(),
        lastSync: await this.syncQueue
          .where('status')
          .equals('completed')
          .last()
          .then(item => item?.completedAt || 'Never')
      };

      return stats;
    } catch (error) {
      console.error('Failed to get database stats:', error);
      return {};
    }
  }
}

// Create singleton instance
const offlineDB = new OfflineDatabase();

export default offlineDB;
