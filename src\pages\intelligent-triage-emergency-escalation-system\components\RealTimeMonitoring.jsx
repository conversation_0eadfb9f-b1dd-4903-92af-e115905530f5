import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RealTimeMonitoring = ({ triageQueue, emergencyAlerts, lastUpdate, onRefresh }) => {
  const [monitoringView, setMonitoringView] = useState('overview');
  const [selectedTimeRange, setSelectedTimeRange] = useState('1hour');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [alertSound, setAlertSound] = useState(true);
  const [filterLevel, setFilterLevel] = useState('all');

  const timeRanges = [
    { value: '15min', label: 'Last 15 minutes' },
    { value: '1hour', label: 'Last hour' },
    { value: '6hours', label: 'Last 6 hours' },
    { value: '24hours', label: 'Last 24 hours' }
  ];

  const monitoringViews = [
    { id: 'overview', name: 'Overview', icon: 'BarChart3' },
    { id: 'queue', name: 'Queue Details', icon: 'List' },
    { id: 'analytics', name: 'Analytics', icon: 'TrendingUp' },
    { id: 'alerts', name: 'Alert History', icon: 'AlertTriangle' }
  ];

  // Calculate monitoring statistics
  const stats = {
    totalCases: triageQueue.length,
    criticalCases: triageQueue.filter(item => item.urgencyLevel === 'critical').length,
    urgentCases: triageQueue.filter(item => item.urgencyLevel === 'urgent').length,
    routineCases: triageQueue.filter(item => item.urgencyLevel === 'routine').length,
    averageUrgencyScore: triageQueue.length > 0 
      ? (triageQueue.reduce((sum, item) => sum + (item.urgencyScore || 0), 0) / triageQueue.length).toFixed(1)
      : 0,
    redFlagCases: triageQueue.filter(item => item.redFlags?.length > 0).length,
    responseTime: calculateAverageResponseTime(),
    escalationRate: calculateEscalationRate()
  };

  function calculateAverageResponseTime() {
    // Simulate response time calculation
    const times = triageQueue.map(item => {
      const assessmentTime = new Date(item.timestamp);
      const now = new Date();
      return Math.floor((now - assessmentTime) / (1000 * 60)); // Minutes
    });
    
    if (times.length === 0) return 0;
    return Math.floor(times.reduce((sum, time) => sum + time, 0) / times.length);
  }

  function calculateEscalationRate() {
    const totalCases = triageQueue.length;
    const escalatedCases = triageQueue.filter(item => 
      item.urgencyLevel === 'critical' || item.redFlags?.length > 0
    ).length;
    
    return totalCases > 0 ? Math.floor((escalatedCases / totalCases) * 100) : 0;
  }

  const getFilteredQueue = () => {
    let filtered = [...triageQueue];
    
    // Filter by urgency level
    if (filterLevel !== 'all') {
      filtered = filtered.filter(item => item.urgencyLevel === filterLevel);
    }
    
    // Filter by time range
    const now = new Date();
    const timeLimit = {
      '15min': 15 * 60 * 1000,
      '1hour': 60 * 60 * 1000,
      '6hours': 6 * 60 * 60 * 1000,
      '24hours': 24 * 60 * 60 * 1000
    }[selectedTimeRange];
    
    filtered = filtered.filter(item => {
      const itemTime = new Date(item.timestamp);
      return (now - itemTime) <= timeLimit;
    });
    
    return filtered;
  };

  const getUrgencyColor = (level) => {
    const colors = {
      critical: 'red',
      urgent: 'orange',
      semi_urgent: 'yellow',
      routine: 'green',
      self_care: 'blue'
    };
    return colors[level] || 'gray';
  };

  const getTimeSinceAssessment = (timestamp) => {
    const now = new Date();
    const assessmentTime = new Date(timestamp);
    const diffMinutes = Math.floor((now - assessmentTime) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  // Play alert sound for new critical cases
  useEffect(() => {
    if (alertSound && emergencyAlerts.length > 0) {
      // In a real implementation, you would play an actual alert sound
      console.log('🚨 Critical alert detected!');
    }
  }, [emergencyAlerts.length, alertSound]);

  const filteredQueue = getFilteredQueue();

  return (
    <div className="space-y-6">
      
      {/* Monitoring Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">
            Real-time Monitoring Dashboard
          </h3>
          <p className="text-text-secondary">
            Live consultation queue prioritization and emergency response coordination
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 mt-4 lg:mt-0">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-2 border border-border rounded-md bg-background text-sm"
          >
            {timeRanges.map(range => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="autoRefresh"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-1"
            />
            <label htmlFor="autoRefresh" className="text-sm text-text-secondary">
              Auto-refresh
            </label>
          </div>
          
          <Button
            variant="outline"
            onClick={onRefresh}
            iconName="RefreshCw"
            iconPosition="left"
            size="sm"
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Last Update Indicator */}
      <div className="flex items-center justify-between bg-surface-secondary rounded-lg p-3">
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-3 ${autoRefresh ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
          <span className="text-sm text-text-secondary">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="alertSound"
              checked={alertSound}
              onChange={(e) => setAlertSound(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="alertSound" className="text-sm text-text-secondary">
              Alert sounds
            </label>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-2xl font-bold text-red-800">{stats.criticalCases}</div>
          <div className="text-sm text-red-700">Critical Cases</div>
          <div className="text-xs text-red-600 mt-1">Immediate attention</div>
        </div>
        
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="text-2xl font-bold text-orange-800">{stats.urgentCases}</div>
          <div className="text-sm text-orange-700">Urgent Cases</div>
          <div className="text-xs text-orange-600 mt-1">Within 1-2 hours</div>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="text-2xl font-bold text-blue-800">{stats.averageUrgencyScore}</div>
          <div className="text-sm text-blue-700">Avg Score</div>
          <div className="text-xs text-blue-600 mt-1">Out of 10</div>
        </div>
        
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="text-2xl font-bold text-purple-800">{stats.responseTime}m</div>
          <div className="text-sm text-purple-700">Avg Response</div>
          <div className="text-xs text-purple-600 mt-1">Time to assess</div>
        </div>
      </div>

      {/* View Tabs */}
      <div className="bg-surface rounded-lg border border-border">
        <div className="border-b border-border">
          <nav className="flex overflow-x-auto">
            {monitoringViews.map((view) => (
              <button
                key={view.id}
                onClick={() => setMonitoringView(view.id)}
                className={`
                  flex-shrink-0 py-4 px-6 text-center transition-all duration-200 min-w-max
                  ${monitoringView === view.id
                    ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50' :'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                  }
                `}
              >
                <div className="flex items-center justify-center mb-1">
                  <Icon name={view.icon} size={18} className="mr-2" />
                  <span className="font-medium">{view.name}</span>
                </div>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          
          {/* Overview View */}
          {monitoringView === 'overview' && (
            <div className="space-y-6">
              
              {/* Queue Distribution */}
              <div>
                <h5 className="font-semibold text-text-primary mb-4">
                  Queue Distribution ({filteredQueue.length} cases)
                </h5>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {['critical', 'urgent', 'routine'].map(level => {
                    const count = filteredQueue.filter(item => item.urgencyLevel === level).length;
                    const percentage = filteredQueue.length > 0 ? Math.floor((count / filteredQueue.length) * 100) : 0;
                    const color = getUrgencyColor(level);
                    
                    return (
                      <div key={level} className={`bg-${color}-50 border border-${color}-200 rounded-lg p-4`}>
                        <div className="flex items-center justify-between mb-2">
                          <span className={`font-medium text-${color}-800 capitalize`}>{level}</span>
                          <span className={`text-2xl font-bold text-${color}-800`}>{count}</span>
                        </div>
                        <div className={`w-full bg-${color}-200 rounded-full h-2`}>
                          <div 
                            className={`bg-${color}-600 h-2 rounded-full`}
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <div className={`text-xs text-${color}-600 mt-1`}>{percentage}% of queue</div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Recent Activity */}
              <div>
                <h5 className="font-semibold text-text-primary mb-4">Recent Activity</h5>
                <div className="space-y-2">
                  {filteredQueue.slice(0, 5).map((item, index) => {
                    const urgencyColor = getUrgencyColor(item.urgencyLevel);
                    
                    return (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-surface-secondary rounded-lg">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full bg-${urgencyColor}-500 mr-3 ${
                            item.urgencyLevel === 'critical' ? 'animate-pulse' : ''
                          }`} />
                          <div>
                            <span className="text-sm font-medium text-text-primary">
                              Assessment #{index + 1}
                            </span>
                            <div className="flex items-center space-x-4 text-xs text-text-secondary">
                              <span className={`text-${urgencyColor}-600 font-medium`}>
                                {item.urgencyLevel?.toUpperCase()} - {item.urgencyScore}/10
                              </span>
                              {item.redFlags?.length > 0 && (
                                <span className="text-red-600">
                                  {item.redFlags.length} red flag{item.redFlags.length !== 1 ? 's' : ''}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <span className="text-xs text-text-secondary">
                          {getTimeSinceAssessment(item.timestamp)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Queue Details View */}
          {monitoringView === 'queue' && (
            <div className="space-y-4">
              
              {/* Queue Filters */}
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-text-secondary">Filter by urgency:</span>
                <select
                  value={filterLevel}
                  onChange={(e) => setFilterLevel(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-sm"
                >
                  <option value="all">All levels</option>
                  <option value="critical">Critical only</option>
                  <option value="urgent">Urgent only</option>
                  <option value="routine">Routine only</option>
                </select>
              </div>

              {/* Detailed Queue List */}
              <div className="space-y-3">
                {filteredQueue.map((item, index) => {
                  const urgencyColor = getUrgencyColor(item.urgencyLevel);
                  
                  return (
                    <div key={item.id} className={`bg-${urgencyColor}-50 border border-${urgencyColor}-200 rounded-lg p-4`}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start">
                          <div className={`w-4 h-4 rounded-full bg-${urgencyColor}-500 mr-4 mt-1 ${
                            item.urgencyLevel === 'critical' ? 'animate-pulse' : ''
                          }`} />
                          <div>
                            <div className="flex items-center space-x-3 mb-2">
                              <span className="font-medium text-text-primary">
                                Patient Assessment #{index + 1}
                              </span>
                              <span className={`text-xs px-2 py-1 rounded bg-${urgencyColor}-200 text-${urgencyColor}-800`}>
                                {item.urgencyLevel?.replace('_', ' ').toUpperCase()}
                              </span>
                              <span className="text-sm font-bold text-text-primary">
                                Score: {item.urgencyScore}/10
                              </span>
                            </div>
                            
                            {item.redFlags?.length > 0 && (
                              <div className="mb-2">
                                <span className="text-xs font-medium text-red-600">Red Flags: </span>
                                <span className="text-xs text-red-700">
                                  {item.redFlags.join(', ').replace(/_/g, ' ')}
                                </span>
                              </div>
                            )}
                            
                            {item.detectedConditions?.length > 0 && (
                              <div className="mb-2">
                                <span className="text-xs font-medium text-blue-600">Possible Conditions: </span>
                                <span className="text-xs text-blue-700">
                                  {item.detectedConditions.map(c => c.disease).join(', ').replace(/_/g, ' ')}
                                </span>
                              </div>
                            )}
                            
                            <div className="text-xs text-text-secondary">
                              Time Frame: {item.timeFrame} • {getTimeSinceAssessment(item.timestamp)}
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className={`text-lg font-bold text-${urgencyColor}-800`}>
                            #{index + 1}
                          </div>
                          <div className="text-xs text-text-secondary">
                            Queue Position
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
                
                {filteredQueue.length === 0 && (
                  <div className="text-center py-8">
                    <Icon name="List" size={48} className="text-gray-400 mx-auto mb-4" />
                    <p className="text-text-secondary">No cases match the current filters</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Analytics View */}
          {monitoringView === 'analytics' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                
                {/* Performance Metrics */}
                <div className="bg-surface-secondary rounded-lg p-4">
                  <h5 className="font-semibold text-text-primary mb-4">Performance Metrics</h5>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">Average Response Time</span>
                      <span className="text-sm font-medium text-text-primary">{stats.responseTime} minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">Escalation Rate</span>
                      <span className="text-sm font-medium text-text-primary">{stats.escalationRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">Red Flag Detection</span>
                      <span className="text-sm font-medium text-text-primary">{stats.redFlagCases} cases</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">Queue Efficiency</span>
                      <span className="text-sm font-medium text-green-600">94%</span>
                    </div>
                  </div>
                </div>

                {/* Regional Patterns */}
                <div className="bg-surface-secondary rounded-lg p-4">
                  <h5 className="font-semibold text-text-primary mb-4">Regional Health Patterns</h5>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">Malaria Suspected</span>
                      <span className="text-sm font-medium text-text-primary">
                        {filteredQueue.filter(item => 
                          item.detectedConditions?.some(c => c.disease === 'malaria')
                        ).length} cases
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">TB Suspected</span>
                      <span className="text-sm font-medium text-text-primary">
                        {filteredQueue.filter(item => 
                          item.detectedConditions?.some(c => c.disease === 'tuberculosis')
                        ).length} cases
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-secondary">Typhoid Suspected</span>
                      <span className="text-sm font-medium text-text-primary">
                        {filteredQueue.filter(item => 
                          item.detectedConditions?.some(c => c.disease === 'typhoid')
                        ).length} cases
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Alert History View */}
          {monitoringView === 'alerts' && (
            <div className="space-y-4">
              <h5 className="font-semibold text-text-primary">Emergency Alert History</h5>
              
              {emergencyAlerts.length === 0 ? (
                <div className="text-center py-8">
                  <Icon name="CheckCircle" size={48} className="text-green-500 mx-auto mb-4" />
                  <p className="text-text-secondary">No emergency alerts in selected time range</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {emergencyAlerts.map((alert, index) => {
                    const urgencyColor = getUrgencyColor(alert.urgencyLevel);
                    
                    return (
                      <div key={alert.id} className={`bg-${urgencyColor}-50 border border-${urgencyColor}-200 rounded-lg p-4`}>
                        <div className="flex items-start justify-between">
                          <div className="flex items-start">
                            <Icon name="AlertTriangle" size={20} className={`text-${urgencyColor}-600 mr-3 mt-0.5`} />
                            <div>
                              <div className="flex items-center space-x-3 mb-2">
                                <span className="font-medium text-text-primary">
                                  Alert #{index + 1}
                                </span>
                                <span className={`text-xs px-2 py-1 rounded bg-${urgencyColor}-200 text-${urgencyColor}-800`}>
                                  {alert.urgencyLevel?.toUpperCase()}
                                </span>
                              </div>
                              
                              <div className="text-sm text-text-secondary mb-2">
                                Urgency Score: {alert.urgencyScore}/10 • 
                                Red Flags: {alert.redFlags?.length || 0} • 
                                Time Frame: {alert.timeFrame}
                              </div>
                              
                              {alert.redFlags?.length > 0 && (
                                <div className="text-xs text-red-700">
                                  Critical symptoms: {alert.redFlags.join(', ').replace(/_/g, ' ')}
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <div className="text-xs text-text-secondary">
                              {getTimeSinceAssessment(alert.timestamp)}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

        </div>
      </div>

    </div>
  );
};

export default RealTimeMonitoring;