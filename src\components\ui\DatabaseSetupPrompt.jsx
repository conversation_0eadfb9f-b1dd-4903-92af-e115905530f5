import React, { useState, useEffect } from 'react';
import { AlertCircle, Database, CheckCircle, Loader2, Play, RefreshCw, ExternalLink } from 'lucide-react';
import Button from './Button';
import databaseSetup from '../../utils/databaseSetup';

const DatabaseSetupPrompt = ({ onSetupComplete, onClose, meta }) => {
  const [setupStatus, setSetupStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRunningSetup, setIsRunningSetup] = useState(false);
  const [setupResults, setSetupResults] = useState(null);
  const [showManualSteps, setShowManualSteps] = useState(false);

  useEffect(() => {
    checkSetupStatus();
  }, []);

  const checkSetupStatus = async () => {
    setIsLoading(true);
    try {
      const status = await databaseSetup.getSetupStatus();
      setSetupStatus(status);
    } catch (error) {
      console.error('Failed to check setup status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runAutoSetup = async () => {
    setIsRunningSetup(true);
    setSetupResults(null);

    try {
      const result = await databaseSetup.autoFixDatabaseSetup();
      setSetupResults(result);

      if (result.success) {
        // Recheck status after successful setup
        await checkSetupStatus();

        // Notify parent component
        if (onSetupComplete) {
          onSetupComplete(result);
        }
      }
    } catch (error) {
      console.error('Auto setup failed:', error);
      setSetupResults({
        success: false,
        error: error.message
      });
    } finally {
      setIsRunningSetup(false);
    }
  };

  const openSupabaseDashboard = () => {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    if (supabaseUrl) {
      const projectRef = supabaseUrl.split('//')[1]?.split('.')[0];
      window.open(`https://supabase.com/dashboard/project/${projectRef}/editor`, '_blank');
    } else {
      window.open('https://supabase.com/dashboard', '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="flex items-center justify-center">
            <Loader2 size={32} className="animate-spin text-blue-600" />
            <span className="ml-3 text-gray-600">Checking database setup...</span>
          </div>
        </div>
      </div>);

  }

  const isSetupComplete = setupStatus?.databaseSetup?.success && setupStatus?.subscriptionPlans?.hasPlans;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Database size={24} className="text-blue-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900">
            Database Setup Required
          </h2>
          {onClose &&
          <button
            onClick={onClose}
            className="ml-auto text-gray-400 hover:text-gray-600">

              ×
            </button>
          }
        </div>

        {/* Setup Status */}
        {setupStatus &&
        <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Current Status</h3>
            <div className="space-y-2">
              <div className="flex items-center">
                {setupStatus.databaseSetup.success ?
              <CheckCircle size={16} className="text-green-600 mr-2" /> :

              <AlertCircle size={16} className="text-red-600 mr-2" />
              }
                <span className="text-sm text-gray-700">
                  Database Tables: {setupStatus.databaseSetup.success ? 'Complete' : 'Missing'}
                </span>
              </div>
              
              <div className="flex items-center">
                {setupStatus.subscriptionPlans.hasPlans ?
              <CheckCircle size={16} className="text-green-600 mr-2" /> :

              <AlertCircle size={16} className="text-red-600 mr-2" />
              }
                <span className="text-sm text-gray-700">
                  Subscription Plans: {setupStatus.subscriptionPlans.hasPlans ? 'Populated' : 'Empty'}
                </span>
              </div>
            </div>

            {setupStatus.databaseSetup.missingTables?.length > 0 &&
          <div className="mt-3 p-3 bg-red-50 rounded-md">
                <p className="text-sm text-red-800">
                  Missing tables: {setupStatus.databaseSetup.missingTables.join(', ')}
                </p>
              </div>
          }
          </div>
        }

        {/* Setup Results */}
        {setupResults &&
        <div className={`mb-6 p-4 rounded-md ${
        setupResults.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`
        }>
            <div className="flex items-start">
              {setupResults.success ?
            <CheckCircle size={20} className="text-green-600 mt-0.5 mr-3 flex-shrink-0" /> :

            <AlertCircle size={20} className="text-red-600 mt-0.5 mr-3 flex-shrink-0" />
            }
              <div>
                <h4 className={`font-medium ${
              setupResults.success ? 'text-green-800' : 'text-red-800'}`
              }>
                  {setupResults.success ? 'Setup Successful!' : 'Setup Failed'}
                </h4>
                <p className={`text-sm mt-1 ${
              setupResults.success ? 'text-green-700' : 'text-red-700'}`
              }>
                  {setupResults.message || setupResults.error}
                </p>
              </div>
            </div>
          </div>
        }

        {/* Auto Setup Section */}
        {!isSetupComplete &&
        <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Automatic Setup</h3>
            <p className="text-sm text-gray-600 mb-4">
              We can automatically create the required database tables and populate them with default data.
            </p>
            
            <Button
            onClick={runAutoSetup}
            disabled={isRunningSetup}
            className="mb-4">

              {isRunningSetup ?
            <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  Setting up database...
                </> :

            <>
                  <Play size={16} className="mr-2" />
                  Run Auto Setup
                </>
            }
            </Button>

            <div className="flex items-center space-x-4">
              <Button
              variant="outline"
              size="sm"
              onClick={checkSetupStatus}
              disabled={isLoading}>

                <RefreshCw size={14} className="mr-1" />
                Refresh Status
              </Button>
              
              <Button
              variant="outline"
              size="sm"
              onClick={() => setShowManualSteps(!showManualSteps)}>

                Manual Setup Instructions
              </Button>
            </div>
          </div>
        }

        {/* Manual Setup Instructions */}
        {showManualSteps &&
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Manual Setup Steps</h3>
            <ol className="text-sm text-gray-700 space-y-2 list-decimal list-inside">
              <li>
                Open your Supabase dashboard
                <Button
                variant="link"
                size="sm"
                onClick={openSupabaseDashboard}
                className="ml-2">

                  <ExternalLink size={12} className="mr-1" />
                  Open Dashboard
                </Button>
              </li>
              <li>Navigate to the SQL Editor</li>
              <li>Execute the migration files in order:</li>
              <ul className="ml-4 mt-1 space-y-1 list-disc list-inside">
                <li><code className="text-xs bg-gray-200 px-1 rounded">supabase/migrations/20241216120000_auth_and_consultation_system.sql</code></li>
                <li><code className="text-xs bg-gray-200 px-1 rounded">supabase/migrations/20241217000000_payment_system.sql</code></li>
              </ul>
              <li>Verify that all tables were created successfully</li>
              <li>Refresh this page to continue</li>
            </ol>
          </div>
        }

        {/* Success Actions */}
        {isSetupComplete &&
        <div className="flex justify-end space-x-3">
            <Button
            variant="outline"
            onClick={checkSetupStatus}>

              <RefreshCw size={16} className="mr-2" />
              Refresh
            </Button>
            <Button
            onClick={() => {
              if (onSetupComplete) {
                onSetupComplete({ success: true });
              }
              if (onClose) {
                onClose();
              }
            }}>

              Continue
            </Button>
          </div>
        }

        {/* Alternative Actions */}
        {!isSetupComplete &&
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:justify-between items-center pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Database setup is required to use subscription features
            </p>
            <div className="flex space-x-2">
              {onClose &&
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}>

                  Continue with Demo
                </Button>
            }
              <Button
              variant="link"
              size="sm"
              onClick={openSupabaseDashboard}>

                <ExternalLink size={12} className="mr-1" />
                Supabase Dashboard
              </Button>
            </div>
          </div>
        }
      </div>
    </div>);

};

export default DatabaseSetupPrompt;