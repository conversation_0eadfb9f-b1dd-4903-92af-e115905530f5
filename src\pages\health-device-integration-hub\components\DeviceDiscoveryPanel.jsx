import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const DeviceDiscoveryPanel = ({ 
  onDeviceFound = () => {},
  onConnectDevice = () => {},
  className = ''
}) => {
  const [isScanning, setIsScanning] = useState(false);
  const [discoveredDevices, setDiscoveredDevices] = useState([]);
  const [scanMethod, setScanMethod] = useState('bluetooth');

  // Mock discovered devices
  const mockDevices = [
    {
      id: 'device-1',
      name: 'Fitbit Charge 5',
      type: 'fitness-tracker',
      signal: 85,
      connectionType: 'bluetooth',
      manufacturer: 'Fitbit',
      isConnectable: true
    },
    {
      id: 'device-2',
      name: 'Omron BP Monitor',
      type: 'blood-pressure',
      signal: 72,
      connectionType: 'bluetooth',
      manufacturer: 'Omron',
      isConnectable: true
    },
    {
      id: 'device-3',
      name: 'Smart Scale Pro',
      type: 'smart-scale',
      signal: 68,
      connectionType: 'wifi',
      manufacturer: 'Withings',
      isConnectable: false,
      reason: 'Already connected to another account'
    }
  ];

  useEffect(() => {
    let interval;
    if (isScanning) {
      interval = setInterval(() => {
        // Simulate device discovery
        const randomDevice = mockDevices[Math.floor(Math.random() * mockDevices.length)];
        setDiscoveredDevices(prev => {
          const exists = prev.find(d => d.id === randomDevice.id);
          if (!exists) {
            return [...prev, randomDevice];
          }
          return prev;
        });
      }, 2000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isScanning]);

  const handleStartScan = () => {
    setIsScanning(true);
    setDiscoveredDevices([]);
    
    // Stop scanning after 10 seconds
    setTimeout(() => {
      setIsScanning(false);
    }, 10000);
  };

  const handleStopScan = () => {
    setIsScanning(false);
  };

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'fitness-tracker': return 'Watch';
      case 'blood-pressure': return 'Heart';
      case 'glucose-meter': return 'Droplet';
      case 'smart-scale': return 'Scale';
      case 'oximeter': return 'Circle';
      case 'thermometer': return 'Thermometer';
      default: return 'Smartphone';
    }
  };

  const getSignalStrength = (signal) => {
    if (signal >= 80) return { bars: 4, color: 'text-success-500' };
    if (signal >= 60) return { bars: 3, color: 'text-success-500' };
    if (signal >= 40) return { bars: 2, color: 'text-warning-500' };
    return { bars: 1, color: 'text-error-500' };
  };

  const renderSignalBars = (signal) => {
    const { bars, color } = getSignalStrength(signal);
    return (
      <div className="flex items-end space-x-1">
        {[1, 2, 3, 4].map((bar) => (
          <div
            key={bar}
            className={`w-1 transition-colors ${
              bar <= bars ? color : 'text-secondary-200'
            }`}
            style={{ height: `${bar * 3 + 2}px` }}
          >
            <div className="w-full h-full bg-current rounded-full"></div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-warning-50 rounded-lg flex items-center justify-center">
            <Icon name="Search" size={20} color="var(--color-warning-500)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Device Discovery
            </h3>
            <p className="text-sm text-text-secondary">
              Find and connect new health devices
            </p>
          </div>
        </div>
      </div>

      {/* Scan Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <label className="block text-sm font-medium text-text-primary mb-2">Scan Method</label>
          <div className="flex gap-2">
            <button
              onClick={() => setScanMethod('bluetooth')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm transition-fast ${
                scanMethod === 'bluetooth' ?'bg-primary-50 text-primary-600 border-2 border-primary-200' :'bg-background text-text-secondary hover:bg-secondary-50'
              }`}
            >
              <Icon name="Bluetooth" size={16} />
              <span>Bluetooth</span>
            </button>
            <button
              onClick={() => setScanMethod('wifi')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm transition-fast ${
                scanMethod === 'wifi'
                  ? 'bg-primary-50 text-primary-600 border-2 border-primary-200' :'bg-background text-text-secondary hover:bg-secondary-50'
              }`}
            >
              <Icon name="Wifi" size={16} />
              <span>WiFi</span>
            </button>
            <button
              onClick={() => setScanMethod('qr')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm transition-fast ${
                scanMethod === 'qr' ?'bg-primary-50 text-primary-600 border-2 border-primary-200' :'bg-background text-text-secondary hover:bg-secondary-50'
              }`}
            >
              <Icon name="QrCode" size={16} />
              <span>QR Code</span>
            </button>
          </div>
        </div>

        <div className="flex items-end">
          {isScanning ? (
            <button
              onClick={handleStopScan}
              className="flex items-center space-x-2 px-4 py-2 bg-error-50 text-error-600 rounded-lg hover:bg-error-100 transition-fast"
            >
              <Icon name="Square" size={16} />
              <span>Stop Scan</span>
            </button>
          ) : (
            <button
              onClick={handleStartScan}
              className="flex items-center space-x-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-fast"
            >
              <Icon name="Search" size={16} />
              <span>Start Scan</span>
            </button>
          )}
        </div>
      </div>

      {/* Scanning Status */}
      {isScanning && (
        <div className="mb-6 p-4 bg-primary-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Icon name="Search" size={16} color="var(--color-primary-600)" className="animate-pulse" />
            <div>
              <h4 className="text-sm font-medium text-primary-800">Scanning for devices...</h4>
              <p className="text-sm text-primary-700">
                Make sure your devices are in pairing mode and nearby
              </p>
            </div>
          </div>
        </div>
      )}

      {/* QR Code Scanner */}
      {scanMethod === 'qr' && (
        <div className="mb-6 p-6 bg-background rounded-lg text-center">
          <div className="w-32 h-32 bg-secondary-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <Icon name="QrCode" size={48} color="var(--color-text-muted)" />
          </div>
          <h4 className="font-medium text-text-primary mb-2">Scan QR Code</h4>
          <p className="text-sm text-text-secondary">
            Point your camera at the device's QR code to connect
          </p>
          <button className="mt-4 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-fast">
            Open Camera
          </button>
        </div>
      )}

      {/* Discovered Devices */}
      <div className="space-y-3">
        <h4 className="font-medium text-text-primary">
          Discovered Devices ({discoveredDevices.length})
        </h4>

        {discoveredDevices.length === 0 && !isScanning ? (
          <div className="text-center py-8">
            <Icon name="Search" size={48} color="var(--color-text-muted)" className="mx-auto mb-4" />
            <h4 className="text-lg font-medium text-text-primary mb-2">No devices found</h4>
            <p className="text-text-secondary mb-4">
              Start scanning to discover nearby health devices
            </p>
          </div>
        ) : (
          discoveredDevices.map((device) => (
            <div key={device.id} className="border border-border rounded-lg p-4 hover:border-border-active transition-fast">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-secondary-50 rounded-lg flex items-center justify-center">
                    <Icon name={getDeviceIcon(device.type)} size={20} color="var(--color-text-secondary)" />
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-text-primary">{device.name}</h4>
                    <p className="text-sm text-text-secondary">{device.manufacturer}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-text-muted capitalize">{device.connectionType}</span>
                      <span className="text-xs text-text-muted">•</span>
                      <span className="text-xs text-text-muted">{device.signal}% signal</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {renderSignalBars(device.signal)}
                  
                  {device.isConnectable ? (
                    <button
                      onClick={() => onConnectDevice(device)}
                      className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-fast text-sm"
                    >
                      Connect
                    </button>
                  ) : (
                    <div className="text-right">
                      <span className="block text-xs text-error-600 font-medium">Cannot Connect</span>
                      <span className="block text-xs text-text-muted">{device.reason}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Connection Tips */}
      <div className="mt-6 p-4 bg-background rounded-lg">
        <div className="flex items-start space-x-3">
          <Icon name="Info" size={16} color="var(--color-primary-500)" />
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-2">Connection Tips</h4>
            <ul className="text-sm text-text-secondary space-y-1">
              <li>• Make sure devices are in pairing/setup mode</li>
              <li>• Keep devices close to your phone/computer</li>
              <li>• Check that Bluetooth/WiFi is enabled</li>
              <li>• Some devices may require manufacturer apps first</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceDiscoveryPanel;