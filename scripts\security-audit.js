/**
 * COMPREHENSIVE SECURITY AUDIT SCRIPT
 * 
 * Verifies all security fixes implemented in Phase 1:
 * - No client-side API key exposure
 * - Proper authentication setup
 * - Memory system security
 * - Backend proxy configuration
 */

import fs from 'fs';
import path from 'path';

console.log('🔒 VoiceHealth AI - Comprehensive Security Audit\n');
console.log('='.repeat(50));

let totalIssues = 0;
let criticalIssues = 0;

// 1. Environment Security Audit
console.log('\n1. 🌍 ENVIRONMENT SECURITY AUDIT');
console.log('-'.repeat(35));

const dangerousClientKeys = [
  'VITE_OPENAI_API_KEY',
  'VITE_ANTHROPIC_API_KEY', 
  'VITE_ELEVENLABS_API_KEY',
  'VITE_COHERE_API_KEY',
  'VITE_GEMINI_API_KEY',
  'VITE_PAYSTACK_SECRET_KEY',
  'VITE_SUPABASE_SERVICE_ROLE_KEY'
];

console.log('Checking for exposed API keys in environment files...');

const envFiles = ['.env.local', '.env.example', '.env.production'];
let envSecure = true;

envFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    console.log(`\nChecking ${file}:`);
    
    dangerousClientKeys.forEach(key => {
      // Check if key exists and is not commented out
      const keyRegex = new RegExp(`^\\s*${key}\\s*=\\s*[^#]`, 'm');
      if (keyRegex.test(content)) {
        console.log(`  ❌ CRITICAL: ${key} exposed in ${file}`);
        criticalIssues++;
        envSecure = false;
      } else {
        console.log(`  ✅ ${key} - Secure`);
      }
    });
  }
});

console.log(`\n📊 Environment Security: ${envSecure ? '✅ SECURE' : '❌ VULNERABLE'}`);
if (!envSecure) {
  console.log('🚨 CRITICAL: Client-side API keys found! These must be removed immediately.');
}

// 2. Code Security Audit
console.log('\n2. 💻 CODE SECURITY AUDIT');
console.log('-'.repeat(25));

const securityChecks = [
  {
    name: 'Health Check Service',
    file: 'src/services/healthCheck.js',
    checks: [
      {
        pattern: /VITE_OPENAI_API_KEY|VITE_ANTHROPIC_API_KEY/,
        description: 'Client-side API key usage',
        critical: true
      },
      {
        pattern: /\/ai-health/,
        description: 'Secure backend health check endpoint',
        critical: false,
        shouldExist: true
      }
    ]
  },
  {
    name: 'Memory Manager',
    file: 'src/services/MemoryManager.ts',
    checks: [
      {
        pattern: /supabase/i,
        description: 'Supabase integration',
        critical: false,
        shouldExist: true
      },
      {
        pattern: /Map\(\)/,
        description: 'In-memory Map usage (should be removed)',
        critical: true,
        shouldExist: false
      }
    ]
  },
  {
    name: 'AI Orchestrator',
    file: 'src/services/aiOrchestrator.ts',
    checks: [
      {
        pattern: /MemoryManager/,
        description: 'Persistent memory integration',
        critical: false,
        shouldExist: true
      },
      {
        pattern: /conversationMemory.*Map/,
        description: 'Old in-memory Map usage',
        critical: true,
        shouldExist: false
      }
    ]
  }
];

securityChecks.forEach(check => {
  console.log(`\nChecking ${check.name} (${check.file}):`);
  
  if (fs.existsSync(check.file)) {
    const content = fs.readFileSync(check.file, 'utf8');
    
    check.checks.forEach(test => {
      const found = test.pattern.test(content);
      const expected = test.shouldExist !== false;
      const passed = found === expected;
      
      if (passed) {
        console.log(`  ✅ ${test.description}`);
      } else {
        const severity = test.critical ? 'CRITICAL' : 'WARNING';
        console.log(`  ❌ ${severity}: ${test.description}`);
        if (test.critical) criticalIssues++;
        totalIssues++;
      }
    });
  } else {
    console.log(`  ❌ CRITICAL: File not found - ${check.file}`);
    criticalIssues++;
  }
});

// 3. Authentication Security Audit
console.log('\n3. 🔐 AUTHENTICATION SECURITY AUDIT');
console.log('-'.repeat(35));

console.log('Checking authentication setup...');

// Check for double AuthProvider wrapping
if (fs.existsSync('src/index.jsx')) {
  const indexContent = fs.readFileSync('src/index.jsx', 'utf8');
  if (indexContent.includes('AuthProvider')) {
    console.log('  ❌ WARNING: AuthProvider found in index.jsx (should only be in App.jsx)');
    totalIssues++;
  } else {
    console.log('  ✅ No AuthProvider in index.jsx (correct)');
  }
}

// Check App.jsx for single AuthProvider
if (fs.existsSync('src/App.jsx')) {
  const appContent = fs.readFileSync('src/App.jsx', 'utf8');
  const authProviderMatches = (appContent.match(/AuthProvider/g) || []).length;
  if (authProviderMatches === 1) {
    console.log('  ✅ Single AuthProvider in App.jsx (correct)');
  } else {
    console.log(`  ❌ WARNING: ${authProviderMatches} AuthProvider instances in App.jsx`);
    totalIssues++;
  }
}

// 4. Backend Security Audit
console.log('\n4. 🖥️ BACKEND SECURITY AUDIT');
console.log('-'.repeat(25));

console.log('Checking backend security configuration...');

if (fs.existsSync('api/ai-proxy.js')) {
  const apiContent = fs.readFileSync('api/ai-proxy.js', 'utf8');
  
  // Check for proper environment variable usage
  if (apiContent.includes('process.env.OPENAI_API_KEY')) {
    console.log('  ✅ Server-side OpenAI API key usage');
  } else {
    console.log('  ❌ CRITICAL: Server-side OpenAI API key not found');
    criticalIssues++;
  }
  
  // Check for health check endpoint
  if (apiContent.includes('/ai-health')) {
    console.log('  ✅ Secure AI health check endpoint');
  } else {
    console.log('  ❌ WARNING: AI health check endpoint not found');
    totalIssues++;
  }
  
  // Check for authentication middleware
  if (apiContent.includes('authenticateUser')) {
    console.log('  ✅ Authentication middleware present');
  } else {
    console.log('  ❌ CRITICAL: Authentication middleware not found');
    criticalIssues++;
  }
} else {
  console.log('  ❌ CRITICAL: Backend API proxy not found');
  criticalIssues++;
}

// 5. Database Security Audit
console.log('\n5. 🗄️ DATABASE SECURITY AUDIT');
console.log('-'.repeat(25));

console.log('Checking database security configuration...');

const migrationFiles = fs.readdirSync('supabase/migrations').filter(f => f.endsWith('.sql'));
console.log(`  ✅ Found ${migrationFiles.length} migration files`);

// Check for RLS policies
const authMigration = migrationFiles.find(f => f.includes('auth_and_consultation'));
if (authMigration) {
  const migrationContent = fs.readFileSync(`supabase/migrations/${authMigration}`, 'utf8');
  
  if (migrationContent.includes('ENABLE ROW LEVEL SECURITY')) {
    console.log('  ✅ Row Level Security enabled');
  } else {
    console.log('  ❌ CRITICAL: Row Level Security not enabled');
    criticalIssues++;
  }
  
  if (migrationContent.includes('conversation_messages')) {
    console.log('  ✅ Conversation messages table exists');
  } else {
    console.log('  ❌ CRITICAL: Conversation messages table not found');
    criticalIssues++;
  }
}

// 6. Final Security Report
console.log('\n' + '='.repeat(50));
console.log('📊 SECURITY AUDIT SUMMARY');
console.log('='.repeat(50));

console.log(`\n🔍 Total Issues Found: ${totalIssues}`);
console.log(`🚨 Critical Issues: ${criticalIssues}`);

if (criticalIssues === 0 && totalIssues === 0) {
  console.log('\n🎉 ✅ ALL SECURITY CHECKS PASSED!');
  console.log('🔒 System is secure and ready for deployment.');
} else if (criticalIssues === 0) {
  console.log('\n⚠️ ✅ NO CRITICAL ISSUES FOUND');
  console.log(`📝 ${totalIssues} minor warnings to address.`);
} else {
  console.log('\n🚨 ❌ CRITICAL SECURITY ISSUES FOUND!');
  console.log('🛑 System is NOT secure for deployment.');
  console.log('🔧 Fix critical issues before proceeding.');
}

console.log('\n📋 NEXT STEPS:');
if (criticalIssues > 0) {
  console.log('1. 🚨 Fix all critical security issues immediately');
  console.log('2. 🔄 Re-run this audit script');
  console.log('3. 🧪 Run security tests');
} else {
  console.log('1. ✅ Security audit passed');
  console.log('2. 🧪 Run comprehensive test suite');
  console.log('3. 🚀 Proceed with deployment preparation');
}

console.log('\n' + '='.repeat(50));

// Exit with appropriate code
process.exit(criticalIssues > 0 ? 1 : 0);
