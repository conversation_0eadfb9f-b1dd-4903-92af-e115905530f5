import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const HelpSection = ({ selectedLanguage }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getLocalizedText = (key) => {
    const texts = {
      question: {
        en: 'Why do we need this?',
        tw: 'Adɛn na yɛhia yei?',
        yo: 'Kilode ti a nilo eyi?',
        sw: 'Kwa nini tunahitaji hii?',
        af: 'Hoekom het ons dit nodig?'
      },
      answer: {
        en: 'Your country selection helps us provide culturally appropriate healthcare advice, connect you with local specialists, and ensure our recommendations align with regional medical practices and available treatments.',
        tw: 'Wo ɔman a wuyi no boa yɛn ma yɛde amammerɛ mu akwahosan afotuoɔ, yɛde wo ka mpɔntonyi a wɔwɔ mpɔtam hɔ ho, na yɛhwɛ sɛ yɛn nnyinasosɛm no ne mpɔtam hɔ aduro nhyehyɛe ne ayaresa a ɛwɔ hɔ hyia.',
        yo: 'Yiyan orilẹ-ede rẹ ran wa lọwọ lati pese imọran ilera ti o baamu aṣa, so ọ pọ pẹlu awọn amoye agbegbe, ki o si rii daju pe awọn iṣeduro wa ni ibamu pẹlu awọn iṣe iṣoogun agbegbe ati awọn itọju ti o wa.',
        sw: 'Uchaguzi wako wa nchi unatusaidia kutoa ushauri wa afya unaofaa kitamaduni, kukuunganisha na wataalamu wa eneo, na kuhakikisha mapendekezo yetu yanalingana na mazoea ya matibabu ya kikanda na matibabu yaliyopo.',
        af: 'Jou land keuse help ons om kultureel toepaslike gesondheidsadvies te verskaf, jou met plaaslike spesialiste te verbind, en te verseker dat ons aanbevelings ooreenstem met streeks mediese praktyke en beskikbare behandelings.'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="bg-secondary-50 rounded-xl p-4 mb-8">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between text-left"
      >
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center">
            <Icon name="HelpCircle" size={16} color="var(--color-warning)" />
          </div>
          <span className="text-text-primary font-medium">
            {getLocalizedText('question')}
          </span>
        </div>
        <Icon 
          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
          size={20} 
          color="var(--color-text-secondary)" 
        />
      </button>

      {isExpanded && (
        <div className="mt-4 pl-11">
          <p className="text-text-secondary leading-relaxed">
            {getLocalizedText('answer')}
          </p>
        </div>
      )}
    </div>
  );
};

export default HelpSection;