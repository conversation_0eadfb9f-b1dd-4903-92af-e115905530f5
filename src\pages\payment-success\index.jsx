import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, Download, ArrowRight, Loader2, Home, Calendar } from 'lucide-react';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import { usePayment } from '../../contexts/PaymentContext';

const PaymentSuccess = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { verifyPayment, subscription, refreshSubscription } = usePayment();
  
  const [isVerifying, setIsVerifying] = useState(true);
  const [paymentData, setPaymentData] = useState(null);
  const [verificationError, setVerificationError] = useState(null);

  const reference = searchParams.get('reference');

  useEffect(() => {
    if (reference) {
      handlePaymentVerification();
    } else {
      setIsVerifying(false);
      setVerificationError('No payment reference found');
    }
  }, [reference]);

  const handlePaymentVerification = async () => {
    try {
      setIsVerifying(true);
      setVerificationError(null);

      const result = await verifyPayment(reference);
      
      if (result.success) {
        setPaymentData(result.data);
        // Refresh subscription data
        await refreshSubscription();
      } else {
        setVerificationError(result.error);
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      setVerificationError('Failed to verify payment. Please contact support.');
    } finally {
      setIsVerifying(false);
    }
  };

  const formatAmount = (amount, currency) => {
    const formatter = new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency || 'NGN',
      minimumFractionDigits: 2
    });
    return formatter.format(amount);
  };

  const getPaymentType = () => {
    if (paymentData?.metadata?.subscription_id) return 'Subscription';
    if (paymentData?.metadata?.consultation_id) return 'Consultation';
    return 'Payment';
  };

  const handleDownloadReceipt = () => {
    // Implement receipt download logic
    console.log('Downloading receipt for reference:', reference);
    // You can generate a PDF receipt or redirect to a receipt endpoint
  };

  if (isVerifying) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <Loader2 size={48} className="animate-spin text-blue-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Verifying Payment
            </h2>
            <p className="text-gray-600">
              Please wait while we confirm your payment...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (verificationError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-md mx-auto pt-20 px-4">
          <div className="bg-white rounded-lg shadow-sm border border-red-200 p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              Payment Verification Failed
            </h1>
            <p className="text-gray-600 mb-6">
              {verificationError}
            </p>
            <div className="space-y-3">
              <Button
                onClick={handlePaymentVerification}
                className="w-full"
              >
                Try Again
              </Button>
              <Button
                onClick={() => navigate('/payment-plans')}
                variant="outline"
                className="w-full"
              >
                Back to Plans
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-2xl mx-auto pt-20 px-4">
        {/* Success Message */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle size={40} className="text-green-600" />
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Successful!
          </h1>
          
          <p className="text-lg text-gray-600 mb-6">
            Thank you for your {getPaymentType().toLowerCase()}. Your payment has been processed successfully.
          </p>

          {/* Payment Details */}
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div>
                <label className="text-sm font-medium text-gray-500">Amount Paid</label>
                <p className="text-lg font-semibold text-gray-900">
                  {formatAmount(paymentData?.amount, paymentData?.currency)}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Payment Method</label>
                <p className="text-lg font-semibold text-gray-900 capitalize">
                  {paymentData?.channel || 'Card'}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Reference</label>
                <p className="text-sm font-mono text-gray-700">
                  {paymentData?.reference}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Date</label>
                <p className="text-sm text-gray-700">
                  {paymentData?.paid_at ? new Date(paymentData.paid_at).toLocaleString() : 'Now'}
                </p>
              </div>
            </div>
          </div>

          {/* Subscription Details */}
          {subscription && getPaymentType() === 'Subscription' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">
                Subscription Activated
              </h3>
              <div className="text-left">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-blue-700">Plan</label>
                    <p className="text-blue-900 font-semibold">
                      {subscription.subscription_plans?.name}
                    </p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-blue-700">Valid Until</label>
                    <p className="text-blue-900 font-semibold">
                      {new Date(subscription.expires_at).toLocaleDateString()}
                    </p>
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-blue-700">Consultations</label>
                    <p className="text-blue-900 font-semibold">
                      {subscription.subscription_plans?.consultation_credits === 0 
                        ? 'Unlimited consultations' 
                        : `${subscription.consultation_credits_remaining} consultations available`
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={handleDownloadReceipt}
              variant="outline"
              className="flex items-center justify-center"
            >
              <Download size={16} className="mr-2" />
              Download Receipt
            </Button>
            
            {getPaymentType() === 'Subscription' ? (
              <Button
                onClick={() => navigate('/voice-consultation-interface')}
                className="flex items-center justify-center"
              >
                <Calendar size={16} className="mr-2" />
                Start Consultation
                <ArrowRight size={16} className="ml-2" />
              </Button>
            ) : (
              <Button
                onClick={() => navigate('/session-dashboard-history')}
                className="flex items-center justify-center"
              >
                <Home size={16} className="mr-2" />
                Go to Dashboard
                <ArrowRight size={16} className="ml-2" />
              </Button>
            )}
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            What's Next?
          </h2>
          
          <div className="space-y-4">
            {getPaymentType() === 'Subscription' ? (
              <>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">1</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Start Your First Consultation</h3>
                    <p className="text-sm text-gray-600">
                      Begin your AI-powered health consultation journey with our expert agents.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">2</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Customize Your Agents</h3>
                    <p className="text-sm text-gray-600">
                      Personalize your AI agents to match your communication preferences and health needs.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">3</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Complete Your Profile</h3>
                    <p className="text-sm text-gray-600">
                      Add your medical history and preferences for more personalized consultations.
                    </p>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex items-start">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <CheckCircle size={16} className="text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Consultation Paid</h3>
                  <p className="text-sm text-gray-600">
                    Your consultation has been paid for. You can now proceed with your session.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;