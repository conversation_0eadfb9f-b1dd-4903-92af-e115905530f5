/**
 * TRADITIONAL MEDICINE INTEGRATION SERVICE
 * 
 * Provides safe integration of traditional medicine with modern healthcare,
 * including safety alerts, drug-herb interactions, and healer collaboration.
 * 
 * FEATURES:
 * - Traditional remedy safety assessment
 * - Drug-herb interaction warnings
 * - Traditional healer collaboration protocols
 * - Cultural medicine education
 * - Evidence-based traditional medicine validation
 * - Regional traditional medicine databases
 * - Safety monitoring and reporting
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { culturalAdaptationService } from './CulturalAdaptationService';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface TraditionalRemedy {
  id: string;
  name: string;
  scientificName?: string;
  localNames: { [language: string]: string };
  regions: string[];
  plantPartsUsed: string[];
  activeCompounds: string[];
  traditionalUses: string[];
  preparationMethods: string[];
  dosageGuidelines: string[];
  safetyProfile: SafetyProfile;
  contraindications: string[];
  sideEffects: string[];
  drugInteractions: DrugInteraction[];
  pregnancySafety: 'safe' | 'caution' | 'avoid' | 'unknown';
  breastfeedingSafety: 'safe' | 'caution' | 'avoid' | 'unknown';
  pediatricSafety: 'safe' | 'caution' | 'avoid' | 'unknown';
  evidenceLevel: 'A' | 'B' | 'C' | 'D';
  traditionalHealerNotes?: string;
}

export interface SafetyProfile {
  summary: string;
  sideEffects: string[];
  toxicity: 'very_low' | 'low' | 'moderate' | 'high' | 'very_high';
  allergyRisk: 'low' | 'medium' | 'high';
  qualityControl: string[];
  adulteration: string[];
}

export interface DrugInteraction {
  drug: string;
  interactionType: 'synergistic' | 'antagonistic' | 'additive' | 'unknown';
  severity: 'mild' | 'moderate' | 'severe' | 'contraindicated';
  mechanism: string;
  clinicalEffect: string;
  management: string;
  evidenceLevel: 'A' | 'B' | 'C' | 'D';
}

export interface TraditionalHealer {
  id: string;
  name: string;
  location: GeographicLocation;
  specialties: string[];
  languagesSpoken: string[];
  certificationStatus: string;
  collaborationLevel: 'none' | 'referral' | 'consultation' | 'partnership';
  contactInfo: ContactInfo;
  availabilitySchedule: AvailabilitySchedule;
  consultationFees: ConsultationFees;
  verified: boolean;
  culturalBackground: string;
  yearsOfExperience: number;
  patientTestimonials?: string[];
}

export interface GeographicLocation {
  country: string;
  region: string;
  city: string;
  coordinates?: { latitude: number; longitude: number };
  address?: string;
}

export interface ContactInfo {
  phoneNumber?: string;
  email?: string;
  whatsapp?: string;
  preferredContactMethod: 'phone' | 'email' | 'whatsapp' | 'in_person';
}

export interface AvailabilitySchedule {
  monday?: TimeSlot[];
  tuesday?: TimeSlot[];
  wednesday?: TimeSlot[];
  thursday?: TimeSlot[];
  friday?: TimeSlot[];
  saturday?: TimeSlot[];
  sunday?: TimeSlot[];
  emergencyAvailable: boolean;
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string;   // HH:MM format
}

export interface ConsultationFees {
  currency: string;
  initialConsultation: number;
  followUpConsultation: number;
  emergencyConsultation?: number;
  paymentMethods: string[];
}

export interface TraditionalMedicineAssessment {
  remedy: TraditionalRemedy;
  safetyScore: number; // 0-100
  interactionWarnings: InteractionWarning[];
  recommendations: string[];
  culturalContext: string;
  evidenceQuality: string;
  monitoringRequirements: string[];
}

export interface InteractionWarning {
  type: 'drug_interaction' | 'contraindication' | 'side_effect' | 'quality_concern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  evidenceLevel: string;
}

export interface HealerCollaborationRequest {
  patientId: string;
  condition: string;
  currentMedications: string[];
  traditionalRemediesUsed: string[];
  culturalPreferences: any;
  urgencyLevel: 'routine' | 'urgent' | 'emergency';
  collaborationType: 'consultation' | 'referral' | 'joint_care';
  languagePreference: string;
}

export interface HealerCollaborationResponse {
  healerId: string;
  healerName: string;
  availability: string;
  recommendedApproach: string;
  safetyConsiderations: string[];
  culturalGuidance: string[];
  estimatedCost: number;
  contactInformation: ContactInfo;
}

// =====================================================
// TRADITIONAL MEDICINE INTEGRATION SERVICE
// =====================================================

export class TraditionalMedicineIntegrationService {
  private supabase: SupabaseClient;
  private cache: Map<string, any> = new Map();
  private readonly cacheTimeout = 15 * 60 * 1000; // 15 minutes

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for traditional medicine integration');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    console.log('✅ TraditionalMedicineIntegrationService initialized');
  }

  /**
   * Assess safety of traditional remedy
   */
  async assessTraditionalRemedySafety(
    remedyName: string,
    currentMedications: string[] = [],
    patientConditions: string[] = [],
    region: string = 'GH'
  ): Promise<TraditionalMedicineAssessment> {
    try {
      const startTime = performance.now();
      const cacheKey = `safety_${remedyName}_${currentMedications.join('_')}_${region}`;

      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }

      // Get remedy information from database
      const { data: remedies, error } = await this.supabase
        .rpc('search_traditional_remedies', {
          p_search_term: remedyName,
          p_region: region
        });

      if (error || !remedies || remedies.length === 0) {
        console.error('❌ Error finding traditional remedy:', error);
        return this.createUnknownRemedyAssessment(remedyName);
      }

      const remedy = this.mapDatabaseRemedyToInterface(remedies[0]);

      // Check drug interactions
      const interactionWarnings = await this.checkDrugHerbInteractions(
        currentMedications,
        [remedyName]
      );

      // Check contraindications
      const contraindicationWarnings = this.checkContraindications(
        remedy,
        patientConditions
      );

      // Calculate safety score
      const safetyScore = this.calculateSafetyScore(
        remedy,
        interactionWarnings,
        contraindicationWarnings
      );

      // Generate recommendations
      const recommendations = this.generateSafetyRecommendations(
        remedy,
        interactionWarnings,
        contraindicationWarnings,
        safetyScore
      );

      const assessment: TraditionalMedicineAssessment = {
        remedy,
        safetyScore,
        interactionWarnings: [...interactionWarnings, ...contraindicationWarnings],
        recommendations,
        culturalContext: this.getCulturalContext(remedy, region),
        evidenceQuality: this.getEvidenceQualityDescription(remedy.evidenceLevel),
        monitoringRequirements: this.getMonitoringRequirements(remedy, interactionWarnings)
      };

      // Cache result
      this.cache.set(cacheKey, assessment);
      setTimeout(() => this.cache.delete(cacheKey), this.cacheTimeout);

      const responseTime = performance.now() - startTime;
      console.log(`✅ Traditional remedy safety assessed in ${responseTime.toFixed(2)}ms`);

      return assessment;
    } catch (error) {
      console.error('❌ Error assessing traditional remedy safety:', error);
      return this.createErrorAssessment(remedyName, error.message);
    }
  }

  /**
   * Check drug-herb interactions
   */
  async checkDrugHerbInteractions(
    medications: string[],
    traditionalRemedies: string[]
  ): Promise<InteractionWarning[]> {
    try {
      if (medications.length === 0 || traditionalRemedies.length === 0) {
        return [];
      }

      const { data: interactions, error } = await this.supabase
        .rpc('check_comprehensive_drug_interactions', {
          p_medications: medications,
          p_traditional_remedies: traditionalRemedies,
          p_population: 'general'
        });

      if (error) {
        console.error('❌ Error checking drug-herb interactions:', error);
        return [];
      }

      return (interactions || [])
        .filter((interaction: any) => interaction.traditional_remedy)
        .map((interaction: any) => ({
          type: 'drug_interaction' as const,
          severity: this.mapSeverityLevel(interaction.severity),
          description: `${interaction.drug_a} may interact with ${interaction.traditional_remedy}: ${interaction.clinical_effects}`,
          recommendation: interaction.clinical_management,
          evidenceLevel: interaction.evidence_level
        }));
    } catch (error) {
      console.error('❌ Error checking drug-herb interactions:', error);
      return [];
    }
  }

  /**
   * Find traditional healers for collaboration
   */
  async findTraditionalHealers(
    request: HealerCollaborationRequest
  ): Promise<HealerCollaborationResponse[]> {
    try {
      const { data: healers, error } = await this.supabase
        .from('traditional_healers')
        .select('*')
        .contains('specialties', [request.condition])
        .contains('languages_spoken', [request.languagePreference])
        .eq('verified', true)
        .order('collaboration_level', { ascending: false })
        .limit(5);

      if (error) {
        console.error('❌ Error finding traditional healers:', error);
        return [];
      }

      return (healers || []).map((healer: any) => ({
        healerId: healer.id,
        healerName: healer.name,
        availability: this.formatAvailability(healer.availability_schedule),
        recommendedApproach: this.getRecommendedApproach(healer, request),
        safetyConsiderations: this.getHealerSafetyConsiderations(healer, request),
        culturalGuidance: this.getHealerCulturalGuidance(healer, request),
        estimatedCost: healer.consultation_fees?.initialConsultation || 0,
        contactInformation: healer.contact_info
      }));
    } catch (error) {
      console.error('❌ Error finding traditional healers:', error);
      return [];
    }
  }

  /**
   * Get traditional medicine education content
   */
  async getTraditionalMedicineEducation(
    condition: string,
    region: string,
    languagePreference: string = 'en'
  ): Promise<{
    overview: string;
    commonRemedies: TraditionalRemedy[];
    safetyGuidelines: string[];
    integrationTips: string[];
    culturalContext: string;
  }> {
    try {
      // Get relevant traditional remedies
      const { data: remedies, error } = await this.supabase
        .rpc('search_traditional_remedies', {
          p_search_term: condition,
          p_region: region,
          p_language: languagePreference
        });

      if (error) {
        console.error('❌ Error getting traditional medicine education:', error);
        return this.getFallbackEducationContent(condition, region);
      }

      const commonRemedies = (remedies || [])
        .slice(0, 5)
        .map((remedy: any) => this.mapDatabaseRemedyToInterface(remedy));

      return {
        overview: this.generateEducationOverview(condition, region, languagePreference),
        commonRemedies,
        safetyGuidelines: this.getGeneralSafetyGuidelines(),
        integrationTips: this.getIntegrationTips(),
        culturalContext: this.getEducationalCulturalContext(region, languagePreference)
      };
    } catch (error) {
      console.error('❌ Error getting traditional medicine education:', error);
      return this.getFallbackEducationContent(condition, region);
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private mapDatabaseRemedyToInterface(dbRemedy: any): TraditionalRemedy {
    return {
      id: dbRemedy.id,
      name: dbRemedy.name,
      scientificName: dbRemedy.scientific_name,
      localNames: dbRemedy.local_names || {},
      regions: dbRemedy.regions || [],
      plantPartsUsed: dbRemedy.plant_parts_used || [],
      activeCompounds: dbRemedy.active_compounds || [],
      traditionalUses: dbRemedy.traditional_uses || [],
      preparationMethods: dbRemedy.preparation_methods || [],
      dosageGuidelines: dbRemedy.dosage_guidelines || [],
      safetyProfile: dbRemedy.safety_profile || {
        summary: 'Safety profile under review',
        sideEffects: [],
        toxicity: 'unknown',
        allergyRisk: 'medium',
        qualityControl: [],
        adulteration: []
      },
      contraindications: dbRemedy.contraindications || [],
      sideEffects: dbRemedy.side_effects || [],
      drugInteractions: this.parseDrugInteractions(dbRemedy.drug_interactions),
      pregnancySafety: dbRemedy.pregnancy_safety || 'unknown',
      breastfeedingSafety: dbRemedy.breastfeeding_safety || 'unknown',
      pediatricSafety: dbRemedy.pediatric_safety || 'unknown',
      evidenceLevel: dbRemedy.evidence_level || 'D',
      traditionalHealerNotes: dbRemedy.traditional_healer_notes
    };
  }

  private parseDrugInteractions(interactionsData: any): DrugInteraction[] {
    if (!interactionsData || !interactionsData.interactions) {
      return [];
    }

    return interactionsData.interactions.map((interaction: any) => ({
      drug: interaction.drug,
      interactionType: interaction.type || 'unknown',
      severity: interaction.severity || 'moderate',
      mechanism: interaction.mechanism || 'Unknown mechanism',
      clinicalEffect: interaction.effect || 'Unknown effect',
      management: interaction.management || 'Monitor closely',
      evidenceLevel: interaction.evidence || 'C'
    }));
  }

  private checkContraindications(
    remedy: TraditionalRemedy,
    patientConditions: string[]
  ): InteractionWarning[] {
    const warnings: InteractionWarning[] = [];

    remedy.contraindications.forEach(contraindication => {
      if (patientConditions.some(condition => 
        condition.toLowerCase().includes(contraindication.toLowerCase()) ||
        contraindication.toLowerCase().includes(condition.toLowerCase())
      )) {
        warnings.push({
          type: 'contraindication',
          severity: 'high',
          description: `${remedy.name} is contraindicated for ${contraindication}`,
          recommendation: `Avoid using ${remedy.name} due to contraindication`,
          evidenceLevel: remedy.evidenceLevel
        });
      }
    });

    return warnings;
  }

  private calculateSafetyScore(
    remedy: TraditionalRemedy,
    interactionWarnings: InteractionWarning[],
    contraindicationWarnings: InteractionWarning[]
  ): number {
    let score = 70; // Base score

    // Adjust based on evidence level
    switch (remedy.evidenceLevel) {
      case 'A': score += 20; break;
      case 'B': score += 10; break;
      case 'C': score += 0; break;
      case 'D': score -= 10; break;
    }

    // Adjust based on safety profile
    switch (remedy.safetyProfile.toxicity) {
      case 'very_low': score += 15; break;
      case 'low': score += 10; break;
      case 'moderate': score -= 5; break;
      case 'high': score -= 15; break;
      case 'very_high': score -= 25; break;
    }

    // Adjust based on warnings
    const totalWarnings = interactionWarnings.length + contraindicationWarnings.length;
    score -= totalWarnings * 10;

    // Adjust based on warning severity
    const criticalWarnings = [...interactionWarnings, ...contraindicationWarnings]
      .filter(w => w.severity === 'critical' || w.severity === 'high').length;
    score -= criticalWarnings * 15;

    return Math.max(0, Math.min(100, score));
  }

  private generateSafetyRecommendations(
    remedy: TraditionalRemedy,
    interactionWarnings: InteractionWarning[],
    contraindicationWarnings: InteractionWarning[],
    safetyScore: number
  ): string[] {
    const recommendations: string[] = [];

    if (safetyScore >= 80) {
      recommendations.push(`${remedy.name} appears to be generally safe for use`);
    } else if (safetyScore >= 60) {
      recommendations.push(`${remedy.name} may be used with caution and monitoring`);
    } else if (safetyScore >= 40) {
      recommendations.push(`${remedy.name} requires careful consideration and medical supervision`);
    } else {
      recommendations.push(`${remedy.name} may pose significant safety concerns`);
    }

    if (interactionWarnings.length > 0) {
      recommendations.push('Monitor for drug interactions with current medications');
    }

    if (contraindicationWarnings.length > 0) {
      recommendations.push('Avoid use due to contraindications with current conditions');
    }

    if (remedy.evidenceLevel === 'C' || remedy.evidenceLevel === 'D') {
      recommendations.push('Limited scientific evidence available - use with caution');
    }

    recommendations.push('Always inform healthcare providers about traditional medicine use');
    recommendations.push('Ensure quality and authenticity of traditional preparations');

    return recommendations;
  }

  private getCulturalContext(remedy: TraditionalRemedy, region: string): string {
    const localName = remedy.localNames[this.getRegionLanguage(region)] || remedy.name;
    return `${remedy.name} (locally known as ${localName}) is traditionally used in ${region} for ${remedy.traditionalUses.join(', ')}`;
  }

  private getRegionLanguage(region: string): string {
    const regionLanguages: { [key: string]: string } = {
      'GH': 'tw',
      'NG': 'yo',
      'KE': 'sw',
      'ZA': 'zu'
    };
    return regionLanguages[region] || 'en';
  }

  private getEvidenceQualityDescription(evidenceLevel: string): string {
    const descriptions: { [key: string]: string } = {
      'A': 'High-quality evidence from systematic reviews and randomized controlled trials',
      'B': 'Moderate-quality evidence from well-designed studies',
      'C': 'Low-quality evidence from observational studies',
      'D': 'Very low-quality evidence or expert opinion only'
    };
    return descriptions[evidenceLevel] || 'Evidence quality not assessed';
  }

  private getMonitoringRequirements(
    remedy: TraditionalRemedy,
    warnings: InteractionWarning[]
  ): string[] {
    const requirements: string[] = [];

    if (warnings.some(w => w.severity === 'high' || w.severity === 'critical')) {
      requirements.push('Close medical supervision required');
    }

    if (remedy.drugInteractions.length > 0) {
      requirements.push('Monitor for signs of drug interactions');
    }

    if (remedy.sideEffects.length > 0) {
      requirements.push(`Watch for side effects: ${remedy.sideEffects.join(', ')}`);
    }

    requirements.push('Regular follow-up with healthcare provider');

    return requirements;
  }

  private mapSeverityLevel(severity: string): 'low' | 'medium' | 'high' | 'critical' {
    switch (severity.toLowerCase()) {
      case 'mild': return 'low';
      case 'moderate': return 'medium';
      case 'severe': return 'high';
      case 'contraindicated': return 'critical';
      default: return 'medium';
    }
  }

  private createUnknownRemedyAssessment(remedyName: string): TraditionalMedicineAssessment {
    return {
      remedy: {
        id: 'unknown',
        name: remedyName,
        localNames: {},
        regions: [],
        plantPartsUsed: [],
        activeCompounds: [],
        traditionalUses: [],
        preparationMethods: [],
        dosageGuidelines: [],
        safetyProfile: {
          summary: 'Safety profile unknown',
          sideEffects: [],
          toxicity: 'unknown' as any,
          allergyRisk: 'medium',
          qualityControl: [],
          adulteration: []
        },
        contraindications: [],
        sideEffects: [],
        drugInteractions: [],
        pregnancySafety: 'unknown',
        breastfeedingSafety: 'unknown',
        pediatricSafety: 'unknown',
        evidenceLevel: 'D'
      },
      safetyScore: 30,
      interactionWarnings: [{
        type: 'quality_concern',
        severity: 'medium',
        description: 'Unknown traditional remedy - safety profile not available',
        recommendation: 'Consult with healthcare provider before use',
        evidenceLevel: 'D'
      }],
      recommendations: [
        'Unknown remedy - exercise extreme caution',
        'Consult with qualified traditional healer',
        'Inform healthcare provider about use',
        'Monitor for any adverse effects'
      ],
      culturalContext: 'Traditional remedy not found in database',
      evidenceQuality: 'No evidence available',
      monitoringRequirements: ['Close monitoring required', 'Report any adverse effects immediately']
    };
  }

  private createErrorAssessment(remedyName: string, errorMessage: string): TraditionalMedicineAssessment {
    return {
      remedy: {
        id: 'error',
        name: remedyName,
        localNames: {},
        regions: [],
        plantPartsUsed: [],
        activeCompounds: [],
        traditionalUses: [],
        preparationMethods: [],
        dosageGuidelines: [],
        safetyProfile: {
          summary: 'Assessment error occurred',
          sideEffects: [],
          toxicity: 'unknown' as any,
          allergyRisk: 'high',
          qualityControl: [],
          adulteration: []
        },
        contraindications: [],
        sideEffects: [],
        drugInteractions: [],
        pregnancySafety: 'unknown',
        breastfeedingSafety: 'unknown',
        pediatricSafety: 'unknown',
        evidenceLevel: 'D'
      },
      safetyScore: 20,
      interactionWarnings: [{
        type: 'quality_concern',
        severity: 'high',
        description: `Error assessing ${remedyName}: ${errorMessage}`,
        recommendation: 'Consult healthcare provider before use',
        evidenceLevel: 'D'
      }],
      recommendations: [
        'Assessment error occurred - use extreme caution',
        'Consult qualified healthcare provider',
        'Do not use without professional guidance'
      ],
      culturalContext: 'Assessment unavailable due to system error',
      evidenceQuality: 'Assessment failed',
      monitoringRequirements: ['Professional medical supervision required']
    };
  }

  private formatAvailability(schedule: any): string {
    if (!schedule) return 'Availability not specified';
    
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const availableDays = days.filter(day => schedule[day] && schedule[day].length > 0);
    
    if (availableDays.length === 0) return 'No regular schedule available';
    
    return `Available ${availableDays.length} days per week${schedule.emergencyAvailable ? ' (emergency consultations available)' : ''}`;
  }

  private getRecommendedApproach(healer: any, request: HealerCollaborationRequest): string {
    switch (request.collaborationType) {
      case 'consultation':
        return 'Traditional medicine consultation to complement modern treatment';
      case 'referral':
        return 'Referral for traditional healing approach';
      case 'joint_care':
        return 'Collaborative care combining traditional and modern medicine';
      default:
        return 'General traditional medicine consultation';
    }
  }

  private getHealerSafetyConsiderations(healer: any, request: HealerCollaborationRequest): string[] {
    return [
      'Inform healer about all current medications',
      'Discuss any allergies or medical conditions',
      'Ensure traditional remedies are properly prepared',
      'Monitor for any adverse reactions',
      'Maintain communication with primary healthcare provider'
    ];
  }

  private getHealerCulturalGuidance(healer: any, request: HealerCollaborationRequest): string[] {
    return [
      'Respect traditional healing practices and protocols',
      'Be open about cultural beliefs and preferences',
      'Understand that healing may involve spiritual elements',
      'Follow traditional preparation and usage instructions',
      'Maintain respectful communication throughout treatment'
    ];
  }

  private generateEducationOverview(condition: string, region: string, language: string): string {
    return `Traditional medicine has been used for centuries in ${region} to treat ${condition}. While many traditional remedies have cultural significance and may provide benefits, it's important to use them safely alongside modern medical care.`;
  }

  private getGeneralSafetyGuidelines(): string[] {
    return [
      'Always inform your healthcare provider about traditional medicine use',
      'Ensure traditional remedies are obtained from reputable sources',
      'Be aware of potential interactions with modern medications',
      'Start with small doses to test for allergic reactions',
      'Stop use and seek medical attention if adverse effects occur',
      'Pregnant and breastfeeding women should exercise extra caution',
      'Children should only use traditional remedies under professional guidance'
    ];
  }

  private getIntegrationTips(): string[] {
    return [
      'Use traditional medicine as a complement to, not replacement for, modern medical care',
      'Maintain open communication between traditional healers and healthcare providers',
      'Keep detailed records of traditional remedies used and their effects',
      'Schedule regular check-ups to monitor overall health',
      'Be patient - traditional medicine often works gradually',
      'Respect both traditional and modern medical approaches'
    ];
  }

  private getEducationalCulturalContext(region: string, language: string): string {
    const contexts: { [key: string]: string } = {
      'GH': 'In Ghana, traditional medicine is deeply rooted in cultural beliefs and practices, often involving spiritual elements alongside herbal remedies.',
      'NG': 'Nigerian traditional medicine encompasses diverse ethnic practices, with emphasis on holistic healing and community involvement.',
      'KE': 'Kenyan traditional medicine often incorporates plant-based remedies with cultural rituals and community healing practices.',
      'ZA': 'South African traditional medicine includes diverse indigenous practices with strong emphasis on ancestral guidance and natural healing.'
    };
    return contexts[region] || 'Traditional medicine practices vary by region and culture, often incorporating spiritual and community elements.';
  }

  private getFallbackEducationContent(condition: string, region: string): any {
    return {
      overview: `Traditional medicine information for ${condition} in ${region} is currently unavailable.`,
      commonRemedies: [],
      safetyGuidelines: this.getGeneralSafetyGuidelines(),
      integrationTips: this.getIntegrationTips(),
      culturalContext: 'Cultural context information unavailable'
    };
  }

  /**
   * Clear cache for memory management
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Traditional medicine integration cache cleared');
  }
}

// Export singleton instance
export const traditionalMedicineIntegrationService = new TraditionalMedicineIntegrationService();
