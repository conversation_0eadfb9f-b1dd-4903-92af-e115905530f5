import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AudioPlaybackControls = ({ 
  audioSessions = [],
  currentSessionId = null,
  onPlaySession = () => {},
  onPauseSession = () => {},
  onSeekTo = () => {},
  onSpeedChange = () => {},
  className = ''
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [volume, setVolume] = useState(0.8);
  const [selectedSession, setSelectedSession] = useState(null);
  const [showSpeedMenu, setShowSpeedMenu] = useState(false);

  const audioRef = useRef(null);
  const progressRef = useRef(null);

  const speedOptions = [0.5, 0.75, 1, 1.25, 1.5, 2];

  useEffect(() => {
    if (currentSessionId && audioSessions.length > 0) {
      const session = audioSessions.find(s => s.id === currentSessionId);
      setSelectedSession(session);
    }
  }, [currentSessionId, audioSessions]);

  const handlePlayPause = () => {
    if (isPlaying) {
      setIsPlaying(false);
      onPauseSession(selectedSession?.id);
    } else {
      setIsPlaying(true);
      onPlaySession(selectedSession?.id);
    }
  };

  const handleSeek = (e) => {
    if (progressRef.current && selectedSession) {
      const rect = progressRef.current.getBoundingClientRect();
      const percent = (e.clientX - rect.left) / rect.width;
      const newTime = percent * duration;
      setCurrentTime(newTime);
      onSeekTo(selectedSession.id, newTime);
    }
  };

  const handleSpeedChange = (speed) => {
    setPlaybackSpeed(speed);
    setShowSpeedMenu(false);
    onSpeedChange(selectedSession?.id, speed);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSessionSelect = (session) => {
    setSelectedSession(session);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(session.duration || 0);
  };

  if (!audioSessions.length) {
    return (
      <div className={`bg-surface border border-border rounded-xl p-6 ${className}`}>
        <div className="text-center">
          <Icon name="Volume2" size={48} color="var(--color-text-muted)" />
          <p className="text-text-muted font-caption mt-4">
            No audio sessions available
          </p>
          <p className="text-xs text-text-muted mt-2">
            Audio will appear here after your consultation
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Icon name="Volume2" size={20} color="var(--color-text-primary)" />
            <h3 className="font-semibold text-text-primary font-heading">
              Audio Playback
            </h3>
          </div>
          
          {selectedSession && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-text-secondary">
                {selectedSession.title || `Session ${selectedSession.id?.slice(-8)}`}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Session Selector */}
      {audioSessions.length > 1 && (
        <div className="p-4 border-b border-border">
          <div className="space-y-2">
            <label className="text-sm font-medium text-text-primary">
              Select Audio Session
            </label>
            <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
              {audioSessions.map((session, index) => (
                <button
                  key={session.id || index}
                  onClick={() => handleSessionSelect(session)}
                  className={`flex items-center justify-between p-3 rounded-lg border transition-fast ${
                    selectedSession?.id === session.id
                      ? 'bg-primary-50 border-primary-200 text-primary-700' :'bg-secondary-50 border-secondary-200 hover:bg-secondary-100'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Icon 
                      name="Play" 
                      size={16} 
                      color={selectedSession?.id === session.id ? 'var(--color-primary)' : 'var(--color-text-secondary)'}
                    />
                    <div className="text-left">
                      <div className="text-sm font-medium">
                        {session.title || `Session ${session.id?.slice(-8) || index + 1}`}
                      </div>
                      <div className="text-xs text-text-secondary">
                        {session.date} • {formatTime(session.duration || 0)}
                      </div>
                    </div>
                  </div>
                  
                  {session.hasTranscript && (
                    <Icon name="FileText" size={14} color="var(--color-text-muted)" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Player */}
      {selectedSession && (
        <div className="p-4">
          {/* Progress Bar */}
          <div className="mb-4">
            <div 
              ref={progressRef}
              onClick={handleSeek}
              className="w-full h-2 bg-secondary-100 rounded-full cursor-pointer relative"
            >
              <div 
                className="h-2 bg-primary-500 rounded-full transition-all duration-100"
                style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
              ></div>
              <div 
                className="absolute top-0 w-4 h-4 bg-primary-600 rounded-full transform -translate-y-1 -translate-x-2 transition-all duration-100"
                style={{ left: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
              ></div>
            </div>
            
            <div className="flex justify-between text-xs text-text-secondary mt-1">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          </div>

          {/* Player Controls */}
          <div className="flex items-center justify-center space-x-4 mb-4">
            {/* Previous */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentTime(Math.max(0, currentTime - 10))}
              iconName="SkipBack"
              className="p-2"
              title="Skip back 10s"
            />

            {/* Play/Pause */}
            <Button
              variant="primary"
              size="md"
              onClick={handlePlayPause}
              iconName={isPlaying ? "Pause" : "Play"}
              className="w-12 h-12 rounded-full"
            />

            {/* Next */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentTime(Math.min(duration, currentTime + 10))}
              iconName="SkipForward"
              className="p-2"
              title="Skip forward 10s"
            />
          </div>

          {/* Additional Controls */}
          <div className="flex items-center justify-between">
            {/* Volume Control */}
            <div className="flex items-center space-x-2">
              <Icon name="Volume2" size={16} color="var(--color-text-secondary)" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(parseFloat(e.target.value))}
                className="w-20 h-1 bg-secondary-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Speed Control */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSpeedMenu(!showSpeedMenu)}
                className="text-xs"
              >
                {playbackSpeed}x
              </Button>
              
              {showSpeedMenu && (
                <div className="absolute bottom-full right-0 mb-2 bg-surface border border-border rounded-lg shadow-elevated p-2 z-10">
                  <div className="space-y-1">
                    {speedOptions.map(speed => (
                      <button
                        key={speed}
                        onClick={() => handleSpeedChange(speed)}
                        className={`block w-full text-left px-3 py-1 rounded text-sm transition-fast ${
                          speed === playbackSpeed
                            ? 'bg-primary-50 text-primary-600' :'hover:bg-secondary-50'
                        }`}
                      >
                        {speed}x
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Audio Visualization */}
          {isPlaying && (
            <div className="mt-4 pt-4 border-t border-border">
              <div className="flex items-center justify-center space-x-1 h-12 bg-secondary-50 rounded-lg">
                {[...Array(20)].map((_, i) => (
                  <div
                    key={i}
                    className="voice-waveform-bar bg-primary-500"
                    style={{
                      height: `${Math.random() * 30 + 10}px`,
                      animationDelay: `${i * 0.05}s`
                    }}
                  ></div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AudioPlaybackControls;