import React from 'react';
import { Check } from 'lucide-react';

const HealthInterestCard = ({ 
  interest, 
  isSelected, 
  onSelect, 
  selectedLanguage = 'en' 
}) => {
  const getLocalizedText = (textObj) => {
    return textObj?.[selectedLanguage] || textObj?.en || '';
  };

  return (
    <div
      onClick={() => onSelect(interest.id)}
      className={`
        relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 
        hover:shadow-md hover:scale-105 active:scale-95
        ${isSelected 
          ? 'border-primary bg-primary-50 shadow-md' 
          : 'border-secondary-200 bg-white hover:border-primary-300'
        }
      `}
    >
      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-3 right-3 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
          <Check size={16} className="text-white" />
        </div>
      )}

      {/* Icon */}
      <div className={`
        w-12 h-12 rounded-lg flex items-center justify-center mb-3 transition-colors duration-200
        ${isSelected ? 'bg-primary text-white' : 'bg-secondary-100 text-primary'}
      `}>
        <span className="text-2xl">{interest.icon}</span>
      </div>

      {/* Title */}
      <h3 className={`
        font-semibold text-base mb-2 transition-colors duration-200
        ${isSelected ? 'text-primary' : 'text-text-primary'}
      `}>
        {getLocalizedText(interest.title)}
      </h3>

      {/* Description */}
      <p className="text-sm text-text-secondary leading-relaxed">
        {getLocalizedText(interest.description)}
      </p>
    </div>
  );
};

export default HealthInterestCard;