/**
 * Background Sync Service
 * Mutation envelope system with retry logic for offline data synchronization
 */

import offlineDB from './offlineDatabase';

class BackgroundSyncService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.syncInterval = null;
    this.retryDelays = [1000, 2000, 5000, 10000, 30000]; // Exponential backoff
    this.maxRetries = 5;
    
    this.setupEventListeners();
    this.startPeriodicSync();
  }

  /**
   * Setup online/offline event listeners
   */
  setupEventListeners() {
    window.addEventListener('online', () => {
      console.log('Network connection restored');
      this.isOnline = true;
      this.triggerSync();
    });

    window.addEventListener('offline', () => {
      console.log('Network connection lost');
      this.isOnline = false;
    });

    // Listen for visibility change to sync when app becomes active
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.isOnline) {
        this.triggerSync();
      }
    });
  }

  /**
   * Start periodic sync when online
   */
  startPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.triggerSync();
      }
    }, 60000); // Sync every minute when online
  }

  /**
   * Trigger sync process
   */
  async triggerSync() {
    if (!this.isOnline || this.syncInProgress) {
      return;
    }

    try {
      this.syncInProgress = true;
      console.log('Starting background sync...');
      
      const syncResults = await this.performSync();
      
      console.log('Background sync completed:', syncResults);
      
      // Emit sync completion event
      this.emitSyncEvent('sync-completed', syncResults);
      
    } catch (error) {
      console.error('Background sync failed:', error);
      this.emitSyncEvent('sync-failed', { error: error.message });
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Perform synchronization of pending items
   */
  async performSync() {
    const pendingItems = await offlineDB.getPendingSyncItems(50);
    
    const results = {
      total: pendingItems.length,
      successful: 0,
      failed: 0,
      retried: 0
    };

    for (const item of pendingItems) {
      try {
        const success = await this.syncItem(item);
        
        if (success) {
          await offlineDB.markSyncCompleted(item.id);
          results.successful++;
        } else {
          await offlineDB.incrementRetryCount(item.id);
          results.retried++;
        }
        
      } catch (error) {
        console.error(`Failed to sync item ${item.id}:`, error);
        await offlineDB.incrementRetryCount(item.id);
        results.failed++;
      }
    }

    return results;
  }

  /**
   * Sync individual item based on type
   */
  async syncItem(item) {
    const { type, data } = item;
    
    switch (type) {
      case 'message':
        return await this.syncMessage(data);
      case 'audioChunk':
        return await this.syncAudioChunk(data);
      case 'triageResult':
        return await this.syncTriageResult(data);
      case 'consultation':
        return await this.syncConsultation(data);
      case 'analyticsEvent':
        return await this.syncAnalyticsEvent(data);
      case 'errorLog':
        return await this.syncErrorLog(data);
      default:
        console.warn(`Unknown sync type: ${type}`);
        return false;
    }
  }

  /**
   * Sync message to server
   */
  async syncMessage(messageData) {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          session_id: messageData.sessionId,
          role: messageData.role,
          content: messageData.content,
          timestamp: messageData.timestamp,
          provider: messageData.provider,
          local_id: messageData.id
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update local record with server ID
        await offlineDB.messages.update(messageData.id, {
          synced: true,
          serverId: result.id,
          syncedAt: new Date().toISOString()
        });
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to sync message:', error);
      return false;
    }
  }

  /**
   * Sync audio chunk to server
   */
  async syncAudioChunk(chunkData) {
    try {
      // Get audio data from database
      const chunk = await offlineDB.audioChunks.get(chunkData.id);
      if (!chunk) return false;

      const formData = new FormData();
      formData.append('sessionId', chunkData.sessionId);
      formData.append('chunkIndex', chunkData.chunkIndex);
      formData.append('audioData', new Blob([chunk.audioData], { type: 'audio/wav' }));
      formData.append('quality', chunk.quality);
      formData.append('timestamp', chunk.timestamp);

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/audio/chunks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: formData
      });

      if (response.ok) {
        await offlineDB.audioChunks.update(chunkData.id, {
          synced: true,
          syncedAt: new Date().toISOString()
        });
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to sync audio chunk:', error);
      return false;
    }
  }

  /**
   * Sync triage result to server
   */
  async syncTriageResult(triageData) {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/triage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          session_id: triageData.sessionId,
          result: triageData.result,
          confidence: triageData.confidence,
          timestamp: triageData.timestamp,
          local_id: triageData.id
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        await offlineDB.triageResults.update(triageData.id, {
          synced: true,
          serverId: result.id,
          syncedAt: new Date().toISOString()
        });
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to sync triage result:', error);
      return false;
    }
  }

  /**
   * Sync consultation data
   */
  async syncConsultation(consultationData) {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/consultations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          session_id: consultationData.sessionId,
          patient_data: consultationData.patientData,
          status: consultationData.status,
          region: consultationData.region,
          timestamp: consultationData.timestamp,
          local_id: consultationData.id
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        await offlineDB.consultations.update(consultationData.id, {
          synced: true,
          serverId: result.id,
          syncedAt: new Date().toISOString()
        });
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to sync consultation:', error);
      return false;
    }
  }

  /**
   * Sync analytics event
   */
  async syncAnalyticsEvent(eventData) {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          event: eventData.event,
          data: eventData.data,
          timestamp: eventData.timestamp,
          local_id: eventData.id
        })
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to sync analytics event:', error);
      return false;
    }
  }

  /**
   * Sync error log
   */
  async syncErrorLog(errorData) {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/errors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          error: errorData.error,
          component: errorData.component,
          error_id: errorData.errorId,
          timestamp: errorData.timestamp,
          local_id: errorData.id
        })
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to sync error log:', error);
      return false;
    }
  }

  /**
   * Get authentication token
   */
  async getAuthToken() {
    // This should integrate with your auth system
    // For now, return a placeholder
    return localStorage.getItem('supabase.auth.token') || '';
  }

  /**
   * Emit sync events for UI updates
   */
  emitSyncEvent(type, data) {
    const event = new CustomEvent('background-sync', {
      detail: { type, data, timestamp: new Date().toISOString() }
    });
    window.dispatchEvent(event);
  }

  /**
   * Force sync now (manual trigger)
   */
  async forcSync() {
    if (!this.isOnline) {
      throw new Error('Cannot sync while offline');
    }
    
    return await this.triggerSync();
  }

  /**
   * Get sync status
   */
  async getSyncStatus() {
    const pendingCount = await offlineDB.syncQueue
      .where('status')
      .anyOf(['pending', 'retry'])
      .count();

    const failedCount = await offlineDB.syncQueue
      .where('status')
      .equals('failed')
      .count();

    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      pendingItems: pendingCount,
      failedItems: failedCount,
      lastSync: await this.getLastSyncTime()
    };
  }

  /**
   * Get last successful sync time
   */
  async getLastSyncTime() {
    try {
      const lastSyncItem = await offlineDB.syncQueue
        .where('status')
        .equals('completed')
        .last();
      
      return lastSyncItem?.completedAt || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Cleanup and stop service
   */
  destroy() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    window.removeEventListener('online', this.triggerSync);
    window.removeEventListener('offline', this.triggerSync);
    document.removeEventListener('visibilitychange', this.triggerSync);
  }
}

// Create singleton instance
const backgroundSync = new BackgroundSyncService();

export default backgroundSync;
