import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import asyncMessagingService from '../../../utils/asyncMessagingService';

import { useAuth } from '../../../contexts/AuthContext';

const AsyncMessagePanel = ({
  sessionId,
  recipientId = null,
  isVisible = false,
  onToggleVisibility = () => {},
  className = '', Notification
}) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [messageText, setMessageText] = useState('');
  const [priority, setPriority] = useState('normal');
  const [queueStatus, setQueueStatus] = useState(null);
  const [unreadCount, setUnreadCount] = useState(0);

  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const recordingTimerRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const audioRef = useRef(null);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    if (user?.id) {
      loadMessages();
      loadUnreadCount();
      setupMessageListener();
    }

    return () => {
      cleanupRecording();
    };
  }, [user?.id, sessionId]);

  useEffect(() => {
    if (isVisible && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isVisible]);

  useEffect(() => {
    // Update queue status periodically
    const interval = setInterval(() => {
      const status = asyncMessagingService.getQueueStatus();
      setQueueStatus(status);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const loadMessages = async () => {
    try {
      setIsLoading(true);
      const result = await asyncMessagingService.getUserMessages(user.id, {
        sessionId,
        includeAudio: false,
        limit: 100
      });

      if (result.success) {
        setMessages(result.data);
      }
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const result = await asyncMessagingService.getUnreadCount(user.id);
      if (result.success) {
        setUnreadCount(result.count);
      }
    } catch (error) {
      console.error('Failed to load unread count:', error);
    }
  };

  const setupMessageListener = () => {
    const removeListener = asyncMessagingService.addMessageListener(user.id, (newMessage) => {
      setMessages((prev) => [newMessage, ...prev]);
      setUnreadCount((prev) => prev + 1);
    });

    // Listen for browser notification events
    const handleMessageReceived = (event) => {
      const message = event.detail;
      setMessages((prev) => {
        const exists = prev.find((m) => m.id === message.messageId);
        if (!exists) {
          return [message, ...prev];
        }
        return prev;
      });
      setUnreadCount((prev) => prev + 1);
    };

    window.addEventListener('voiceMessageReceived', handleMessageReceived);

    return () => {
      removeListener();
      window.removeEventListener('voiceMessageReceived', handleMessageReceived);
    };
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });

      // Setup audio context for level monitoring
      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      source.connect(analyserRef.current);

      // Setup media recorder
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm; codecs=opus'
      });

      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await sendVoiceMessage(audioBlob);
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setRecordingDuration(0);

      // Start recording timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 1);
        updateAudioLevel();
      }, 1000);

    } catch (error) {
      console.error('Failed to start recording:', error);
      alert('Failed to access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }
    cleanupRecording();
  };

  const cleanupRecording = () => {
    setIsRecording(false);
    setRecordingDuration(0);
    setAudioLevel(0);

    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  };

  const updateAudioLevel = () => {
    if (analyserRef.current) {
      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
      setAudioLevel(average);
    }
  };

  const sendVoiceMessage = async (audioBlob) => {
    try {
      const messageData = {
        audioBlob,
        senderId: user.id,
        recipientId: recipientId || user.id, // For testing, send to self if no recipient
        sessionId,
        duration: recordingDuration,
        transcription: messageText.trim() || null,
        quality: 'medium',
        priority,
        urgent: priority === 'urgent',
        requiresResponse: priority !== 'low'
      };

      const result = await asyncMessagingService.sendVoiceMessage(messageData);

      if (result.success) {
        setMessageText('');
        setPriority('normal');

        // Add to local messages list
        const newMessage = {
          id: result.messageId,
          type: 'voice_message',
          sender_id: user.id,
          recipient_id: recipientId || user.id,
          session_id: sessionId,
          priority,
          status: 'pending',
          duration: recordingDuration,
          transcription: messageData.transcription,
          created_at: new Date().toISOString(),
          sender: { full_name: user.full_name || 'You' }
        };

        setMessages((prev) => [newMessage, ...prev]);

        // Show success notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Voice Message Sent', {
            body: `Your message is being delivered (${formatDuration(recordingDuration)})`,
            icon: '/favicon.ico'
          });
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Failed to send voice message:', error);
      alert('Failed to send voice message. Please try again.');
    }
  };

  const playMessage = async (message) => {
    try {
      if (currentlyPlaying === message.id) {
        // Pause current playback
        if (audioRef.current) {
          audioRef.current.pause();
        }
        setCurrentlyPlaying(null);
        return;
      }

      // Stop any current playback
      if (audioRef.current) {
        audioRef.current.pause();
      }

      // Load audio data if not already loaded
      if (!message.audioData && message.audio_storage_id) {
        const result = await asyncMessagingService.getMessage(message.id, user.id);
        if (result.success && result.data.audioData) {
          message.audioData = result.data.audioData;
        } else {
          throw new Error('Failed to load audio data');
        }
      }

      if (message.audioData?.audioBlob) {
        const audioUrl = URL.createObjectURL(message.audioData.audioBlob);
        audioRef.current = new Audio(audioUrl);

        audioRef.current.onloadedmetadata = () => {
          setCurrentlyPlaying(message.id);
          audioRef.current.play();
        };

        audioRef.current.ontimeupdate = () => {
          const progress = audioRef.current.currentTime / audioRef.current.duration * 100;
          setPlaybackPosition(progress);
        };

        audioRef.current.onended = () => {
          setCurrentlyPlaying(null);
          setPlaybackPosition(0);
          URL.revokeObjectURL(audioUrl);
        };

        audioRef.current.onerror = () => {
          setCurrentlyPlaying(null);
          throw new Error('Audio playback failed');
        };
      }
    } catch (error) {
      console.error('Failed to play message:', error);
      alert('Failed to play voice message.');
    }
  };

  const markAsRead = async (messageId) => {
    try {
      const success = await asyncMessagingService.markMessageAsRead(messageId);
      if (success) {
        setMessages((prev) => prev.map((msg) =>
        msg.id === messageId ? { ...msg, status: 'read' } : msg
        ));
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Failed to mark message as read:', error);
    }
  };

  const deleteMessage = async (messageId) => {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
      const result = await asyncMessagingService.deleteMessage(messageId, user.id);
      if (result.success) {
        setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Failed to delete message:', error);
      alert('Failed to delete message.');
    }
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':return 'Clock';
      case 'delivering':return 'Send';
      case 'delivered':return 'Check';
      case 'read':return 'CheckCheck';
      case 'failed':return 'AlertCircle';
      default:return 'MessageCircle';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':return 'text-warning-600';
      case 'delivering':return 'text-primary-600';
      case 'delivered':return 'text-success-600';
      case 'read':return 'text-success-700';
      case 'failed':return 'text-error-600';
      default:return 'text-text-secondary';
    }
  };

  const getPriorityBadge = (priority) => {
    const styles = {
      urgent: 'bg-error-100 text-error-800',
      high: 'bg-warning-100 text-warning-800',
      normal: 'bg-secondary-100 text-secondary-800',
      low: 'bg-gray-100 text-gray-800'
    };
    return styles[priority] || styles.normal;
  };

  if (!isVisible) {
    return (
      <div className={`fixed top-20 right-4 z-50 ${className}`}>
        <Button
          onClick={onToggleVisibility}
          className="relative bg-primary-500 hover:bg-primary-600 text-white rounded-full p-3 shadow-lg"
          size="sm">

          <Icon name="MessageSquare" size={20} />
          {unreadCount > 0 &&
          <span className="absolute -top-2 -right-2 bg-error-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          }
        </Button>
      </div>);

  }

  return (
    <div className={`bg-surface rounded-xl shadow-minimal border border-border ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <Icon name="MessageSquare" size={20} color="var(--color-text-primary)" />
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              Async Messages
            </h3>
            <p className="text-sm text-text-secondary">
              Voice messages for this session
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {queueStatus && queueStatus.pending > 0 &&
          <div className="flex items-center space-x-1 text-xs text-warning-600">
              <Icon name="Clock" size={12} />
              <span>{queueStatus.pending} pending</span>
            </div>
          }
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleVisibility}
            iconName="X" />

        </div>
      </div>

      {/* Message List */}
      <div className="h-80 overflow-y-auto p-4 space-y-3">
        {isLoading ?
        <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Icon name="Loader" size={24} className="animate-spin text-primary-500 mx-auto mb-2" />
              <p className="text-sm text-text-secondary">Loading messages...</p>
            </div>
          </div> :
        messages.length === 0 ?
        <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Icon name="MessageSquare" size={48} className="text-secondary-400 mx-auto mb-4" />
              <p className="text-text-secondary">No async messages yet</p>
              <p className="text-sm text-text-muted">Record a voice message to get started</p>
            </div>
          </div> :

        messages.map((message) =>
        <div
          key={message.id}
          className={`flex space-x-3 p-3 rounded-lg ${
          message.sender_id === user.id ?
          'bg-primary-50 ml-8' : 'bg-secondary-50 mr-8'}`
          }>

              {/* Avatar */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
          message.sender_id === user.id ?
          'bg-primary-500 text-white' : 'bg-secondary-500 text-white'}`
          }>
                {(message.sender?.full_name || 'U')[0].toUpperCase()}
              </div>

              {/* Message Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-text-primary">
                      {message.sender_id === user.id ? 'You' : message.sender?.full_name || 'Unknown'}
                    </span>
                    <span className={`px-2 py-0.5 text-xs rounded-full ${getPriorityBadge(message.priority)}`}>
                      {message.priority}
                    </span>
                  </div>
                  <span className="text-xs text-text-muted">
                    {formatTimestamp(message.created_at)}
                  </span>
                </div>

                {/* Audio Player */}
                <div className="bg-white rounded-lg p-3 border border-secondary-200">
                  <div className="flex items-center space-x-3">
                    <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => playMessage(message)}
                  iconName={currentlyPlaying === message.id ? "Pause" : "Play"}
                  className="flex-shrink-0" />

                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                        <span>Voice Message</span>
                        <span>{formatDuration(message.duration || 0)}</span>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="w-full bg-secondary-200 rounded-full h-1">
                        <div
                      className="bg-primary-500 h-1 rounded-full transition-all duration-200"
                      style={{
                        width: currentlyPlaying === message.id ? `${playbackPosition}%` : '0%'
                      }}>
                    </div>
                      </div>
                    </div>
                  </div>

                  {/* Transcription */}
                  {message.transcription &&
              <div className="mt-2 text-sm text-text-secondary italic">
                      "{message.transcription}"
                    </div>
              }
                </div>

                {/* Message Actions */}
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-2">
                    <Icon
                  name={getStatusIcon(message.status)}
                  size={12}
                  className={getStatusColor(message.status)} />

                    <span className={`text-xs ${getStatusColor(message.status)}`}>
                      {message.status.charAt(0).toUpperCase() + message.status.slice(1)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    {message.recipient_id === user.id && message.status === 'delivered' &&
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => markAsRead(message.id)}
                  iconName="Check"
                  className="text-xs" />

                }
                    <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteMessage(message.id)}
                  iconName="Trash2"
                  className="text-xs text-error-600 hover:text-error-700" />

                  </div>
                </div>
              </div>
            </div>
        )
        }
        <div ref={messagesEndRef} />
      </div>

      {/* Recording Controls */}
      <div className="border-t border-border p-4">
        {isRecording ?
        <div className="space-y-3">
            {/* Recording Indicator */}
            <div className="flex items-center justify-center space-x-3 text-error-600">
              <div className="w-3 h-3 bg-error-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Recording: {formatDuration(recordingDuration)}</span>
            </div>

            {/* Audio Level */}
            <div className="w-full bg-secondary-200 rounded-full h-2">
              <div
              className="bg-error-500 h-2 rounded-full transition-all duration-200"
              style={{ width: `${Math.min(audioLevel, 100)}%` }}>
            </div>
            </div>

            {/* Stop Recording */}
            <Button
            onClick={stopRecording}
            className="w-full"
            iconName="Square"
            iconPosition="left">

              Stop Recording
            </Button>
          </div> :

        <div className="space-y-3">
            {/* Message Options */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-text-secondary mb-1">
                  Priority
                </label>
                <select
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                className="w-full text-sm border border-border rounded-md px-2 py-1 bg-surface">

                  <option value="low">Low</option>
                  <option value="normal">Normal</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>

            {/* Optional Text Note */}
            <div>
              <label className="block text-xs font-medium text-text-secondary mb-1">
                Optional Note (for transcription)
              </label>
              <input
              type="text"
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              placeholder="Brief description of your message..."
              className="w-full text-sm border border-border rounded-md px-3 py-2 bg-surface"
              maxLength={200} />

            </div>

            {/* Start Recording */}
            <Button
            onClick={startRecording}
            className="w-full"
            iconName="Mic"
            iconPosition="left">

              Record Voice Message
            </Button>
          </div>
        }
      </div>

      {/* Queue Status */}
      {queueStatus && (queueStatus.total > 0 || queueStatus.isProcessing) &&
      <div className="border-t border-border p-3 bg-secondary-50">
          <div className="flex items-center justify-between text-xs text-text-secondary">
            <span>Message Queue</span>
            <div className="flex items-center space-x-3">
              {queueStatus.isProcessing &&
            <div className="flex items-center space-x-1">
                  <Icon name="Loader" size={12} className="animate-spin" />
                  <span>Processing...</span>
                </div>
            }
              <span>
                {queueStatus.pending} pending · {queueStatus.delivering} sending
              </span>
            </div>
          </div>
        </div>
      }
    </div>);

};

export default AsyncMessagePanel;