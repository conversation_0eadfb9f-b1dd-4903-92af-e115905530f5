import React, { useState } from 'react';
import { FileText, Download, Eye, Calendar, DollarSign, Clock, User, Search, TrendingUp } from 'lucide-react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const ConsultationHistory = ({ currency }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('30');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  // Mock consultation history data
  const [consultations, setConsultations] = useState([
    {
      id: 1,
      date: '2024-01-15',
      time: '14:30',
      type: 'General Consultation',
      duration: 25,
      specialist: 'Dr. AI Assistant',
      agentCount: 1,
      cost: { NGN: 4200, USD: 8.5 },
      status: 'completed',
      features: ['Transcript', 'Recording'],
      member: '<PERSON>',
      symptoms: ['Headache', 'Fatigue'],
      prescription: true
    },
    {
      id: 2,
      date: '2024-01-12',
      time: '09:15',
      type: 'Specialist Consultation',
      duration: 35,
      specialist: 'Dr. Cardiology AI',
      agentCount: 2,
      cost: { NGN: 8750, USD: 17.5 },
      status: 'completed',
      features: ['Transcript', 'Recording', 'AI Summary'],
      member: 'Jane Doe',
      symptoms: ['Chest Pain', 'Shortness of Breath'],
      prescription: true
    },
    {
      id: 3,
      date: '2024-01-10',
      time: '16:45',
      type: 'Mental Health',
      duration: 40,
      specialist: 'Dr. Psychology AI',
      agentCount: 1,
      cost: { NGN: 6400, USD: 12.8 },
      status: 'completed',
      features: ['Transcript', 'Follow-up Reminder'],
      member: 'Teen Doe',
      symptoms: ['Anxiety', 'Sleep Issues'],
      prescription: false
    },
    {
      id: 4,
      date: '2024-01-08',
      time: '11:20',
      type: 'Pediatric Consultation',
      duration: 20,
      specialist: 'Dr. Pediatric AI',
      agentCount: 1,
      cost: { NGN: 3500, USD: 7 },
      status: 'completed',
      features: ['Transcript'],
      member: 'Child Doe',
      symptoms: ['Fever', 'Cough'],
      prescription: true
    },
    {
      id: 5,
      date: '2024-01-05',
      time: '13:10',
      type: 'Emergency Consultation',
      duration: 15,
      specialist: 'Dr. Emergency AI',
      agentCount: 3,
      cost: { NGN: 11250, USD: 22.5 },
      status: 'completed',
      features: ['Transcript', 'Recording', 'AI Summary', 'Priority Support'],
      member: 'John Doe',
      symptoms: ['Severe Headache', 'Nausea'],
      prescription: true
    }
  ]);

  const formatPrice = (price, currency) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'General Consultation':
        return '🩺';
      case 'Specialist Consultation':
        return '👨‍⚕️';
      case 'Emergency Consultation':
        return '🚨';
      case 'Mental Health':
        return '🧠';
      case 'Pediatric Consultation':
        return '👶';
      default:
        return '💊';
    }
  };

  const filteredConsultations = consultations
    .filter(consultation => {
      const matchesSearch = 
        consultation.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        consultation.member.toLowerCase().includes(searchTerm.toLowerCase()) ||
        consultation.specialist.toLowerCase().includes(searchTerm.toLowerCase()) ||
        consultation.symptoms.some(symptom => 
          symptom.toLowerCase().includes(searchTerm.toLowerCase())
        );
      
      const matchesType = selectedType === 'all' || consultation.type === selectedType;
      
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.date) - new Date(a.date);
        case 'cost':
          return b.cost[currency] - a.cost[currency];
        case 'duration':
          return b.duration - a.duration;
        default:
          return 0;
      }
    });

  const totalSpent = consultations.reduce((sum, consultation) => sum + consultation.cost[currency], 0);
  const totalConsultations = consultations.length;
  const averageCost = totalSpent / totalConsultations;

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Consultations</p>
              <p className="text-2xl font-bold text-gray-900">{totalConsultations}</p>
            </div>
            <FileText className="text-blue-500" size={24} />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Spent</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPrice(totalSpent, currency)}
              </p>
            </div>
            <DollarSign className="text-green-500" size={24} />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Average Cost</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPrice(averageCost, currency)}
              </p>
            </div>
            <TrendingUp className="text-purple-500" size={24} />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Duration</p>
              <p className="text-2xl font-bold text-gray-900">
                {consultations.reduce((sum, c) => sum + c.duration, 0)}m
              </p>
            </div>
            <Clock className="text-orange-500" size={24} />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search consultations..."
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Consultation Type
            </label>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Types</option>
              <option value="General Consultation">General</option>
              <option value="Specialist Consultation">Specialist</option>
              <option value="Emergency Consultation">Emergency</option>
              <option value="Mental Health">Mental Health</option>
              <option value="Pediatric Consultation">Pediatric</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Period
            </label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 3 months</option>
              <option value="365">Last year</option>
              <option value="all">All time</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="date">Date</option>
              <option value="cost">Cost</option>
              <option value="duration">Duration</option>
            </select>
          </div>
        </div>
      </div>

      {/* Consultation List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Consultation History ({filteredConsultations.length})
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Export functionality
                console.log('Export consultation history');
              }}
            >
              <Download size={16} className="mr-1" />
              Export
            </Button>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredConsultations.map((consultation) => (
            <div key={consultation.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="text-2xl">{getTypeIcon(consultation.type)}</div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900">{consultation.type}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(consultation.status)}`}>
                        {consultation.status}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                      <span className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        {formatDate(consultation.date)} at {consultation.time}
                      </span>
                      <span className="flex items-center">
                        <Clock size={14} className="mr-1" />
                        {consultation.duration} min
                      </span>
                      <span className="flex items-center">
                        <User size={14} className="mr-1" />
                        {consultation.member}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm text-gray-600">Specialist:</span>
                      <span className="text-sm font-medium text-gray-900">{consultation.specialist}</span>
                      {consultation.agentCount > 1 && (
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          {consultation.agentCount} agents
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm text-gray-600">Symptoms:</span>
                      <div className="flex flex-wrap gap-1">
                        {consultation.symptoms.map((symptom, index) => (
                          <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            {symptom}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">Features:</span>
                      <div className="flex flex-wrap gap-1">
                        {consultation.features.map((feature, index) => (
                          <span key={index} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                            {feature}
                          </span>
                        ))}
                        {consultation.prescription && (
                          <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                            Prescription
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900 mb-2">
                    {formatPrice(consultation.cost[currency], currency)}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Eye size={14} className="mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download size={14} className="mr-1" />
                      Receipt
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredConsultations.length === 0 && (
          <div className="p-12 text-center">
            <FileText size={48} className="text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No consultations found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedType !== 'all' ?'Try adjusting your search or filter criteria' :'You haven\'t had any consultations yet'
              }
            </p>
            {!searchTerm && selectedType === 'all' && (
              <Button onClick={() => {
                // Navigate to consultation booking
                console.log('Book consultation');
              }}>
                Book Your First Consultation
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ConsultationHistory;