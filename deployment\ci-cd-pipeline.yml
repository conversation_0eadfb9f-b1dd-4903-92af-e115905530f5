# VoiceHealth AI - CI/CD Pipeline Configuration
# Phase 4: Deployment Automation
# Comprehensive pipeline for automated testing, building, and deployment

name: VoiceHealth AI CI/CD Pipeline

on:
  push:
    branches: [ main, develop, release/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production-ghana
        - production-kenya
        - production-nigeria
        - production-south-africa
      skip_tests:
        description: 'Skip tests (emergency deployment only)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18.x'
  PYTHON_VERSION: '3.11'
  SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: voicehealth-ai

jobs:
  # =====================================================
  # SECURITY AND COMPLIANC<PERSON> CHECKS
  # =====================================================
  security-scan:
    name: Security & Compliance Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.skip_tests != 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Run HIPAA Compliance Check
      run: |
        echo "🔒 Running HIPAA compliance validation..."
        # Check for hardcoded secrets
        if grep -r "password\|secret\|key" --include="*.ts" --include="*.js" src/; then
          echo "❌ Potential hardcoded secrets found"
          exit 1
        fi
        echo "✅ HIPAA compliance check passed"

    - name: Security Vulnerability Scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'

    - name: Cultural Sensitivity Audit
      run: |
        echo "🌍 Running cultural sensitivity audit..."
        # Check for culturally insensitive terms
        python scripts/cultural-sensitivity-check.py
        echo "✅ Cultural sensitivity audit passed"

  # =====================================================
  # AUTOMATED TESTING SUITE
  # =====================================================
  test-suite:
    name: Comprehensive Test Suite
    runs-on: ubuntu-latest
    if: github.event.inputs.skip_tests != 'true'
    needs: security-scan
    
    strategy:
      matrix:
        test-type: [unit, integration, e2e, performance, cultural]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        npm run build

    - name: Setup test environment
      run: |
        echo "🔧 Setting up test environment for ${{ matrix.test-type }} tests..."
        cp .env.test .env
        npm run db:test:setup

    - name: Run Unit Tests
      if: matrix.test-type == 'unit'
      run: |
        echo "🧪 Running unit tests with 90%+ coverage requirement..."
        npm run test:unit -- --coverage --coverageThreshold='{"global":{"branches":90,"functions":90,"lines":90,"statements":90}}'

    - name: Run Integration Tests
      if: matrix.test-type == 'integration'
      run: |
        echo "🔗 Running integration tests..."
        npm run test:integration

    - name: Run E2E Tests
      if: matrix.test-type == 'e2e'
      run: |
        echo "🎭 Running end-to-end tests..."
        npm run test:e2e

    - name: Run Performance Tests
      if: matrix.test-type == 'performance'
      run: |
        echo "⚡ Running performance tests..."
        npm run test:performance
        # Validate emergency response time < 2 seconds
        npm run test:emergency-response-time

    - name: Run Cultural Sensitivity Tests
      if: matrix.test-type == 'cultural'
      run: |
        echo "🌍 Running cultural sensitivity tests..."
        npm run test:cultural-sensitivity
        npm run test:traditional-medicine-integration

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.test-type }}
        path: |
          coverage/
          test-results/
          performance-reports/

  # =====================================================
  # BUILD AND PACKAGE
  # =====================================================
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: test-suite
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: |
        echo "🏗️ Building VoiceHealth AI application..."
        npm run build:production
        
        # Validate build artifacts
        if [ ! -d "dist" ]; then
          echo "❌ Build failed - dist directory not found"
          exit 1
        fi
        
        echo "✅ Build completed successfully"

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          NODE_VERSION=${{ env.NODE_VERSION }}
          BUILD_DATE=${{ github.event.head_commit.timestamp }}
          VCS_REF=${{ github.sha }}

  # =====================================================
  # REGIONAL DEPLOYMENT VALIDATION
  # =====================================================
  validate-regional-config:
    name: Validate Regional Configuration
    runs-on: ubuntu-latest
    needs: build
    if: contains(github.event.inputs.environment, 'production')
    
    strategy:
      matrix:
        region: [ghana, kenya, nigeria, south-africa]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Validate regional configuration
      run: |
        echo "🌍 Validating configuration for ${{ matrix.region }}..."
        
        # Check regional config exists
        if [ ! -f "config/regions/${{ matrix.region }}.json" ]; then
          echo "❌ Regional configuration not found for ${{ matrix.region }}"
          exit 1
        fi
        
        # Validate cultural adaptations
        python scripts/validate-cultural-config.py --region ${{ matrix.region }}
        
        # Validate regulatory compliance
        python scripts/validate-regulatory-compliance.py --region ${{ matrix.region }}
        
        # Validate language support
        python scripts/validate-language-support.py --region ${{ matrix.region }}
        
        echo "✅ Regional configuration validated for ${{ matrix.region }}"

    - name: Test regional emergency protocols
      run: |
        echo "🚨 Testing emergency protocols for ${{ matrix.region }}..."
        npm run test:emergency-protocols -- --region ${{ matrix.region }}
        echo "✅ Emergency protocols validated"

  # =====================================================
  # STAGING DEPLOYMENT
  # =====================================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, validate-regional-config]
    if: github.ref == 'refs/heads/develop' || github.event.inputs.environment == 'staging'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup deployment tools
      run: |
        echo "🔧 Setting up deployment tools..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh

    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        
        # Update staging configuration
        envsubst < deployment/staging/docker-compose.yml.template > docker-compose.yml
        
        # Deploy with health checks
        docker-compose up -d
        
        # Wait for services to be healthy
        timeout 300 bash -c 'until docker-compose ps | grep -q "healthy"; do sleep 10; done'
        
        echo "✅ Staging deployment completed"

    - name: Run smoke tests
      run: |
        echo "💨 Running smoke tests on staging..."
        npm run test:smoke -- --environment staging
        echo "✅ Smoke tests passed"

    - name: Performance validation
      run: |
        echo "⚡ Validating performance on staging..."
        npm run test:performance -- --environment staging
        echo "✅ Performance validation passed"

  # =====================================================
  # PRODUCTION DEPLOYMENT
  # =====================================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, validate-regional-config]
    if: github.ref == 'refs/heads/main' || contains(github.event.inputs.environment, 'production')
    environment: 
      name: ${{ github.event.inputs.environment || 'production-ghana' }}
      url: https://${{ github.event.inputs.environment || 'production-ghana' }}.voicehealth.ai
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Extract region from environment
      id: region
      run: |
        ENV="${{ github.event.inputs.environment || 'production-ghana' }}"
        REGION=${ENV#production-}
        echo "region=$REGION" >> $GITHUB_OUTPUT
        echo "🌍 Deploying to region: $REGION"

    - name: Pre-deployment validation
      run: |
        echo "🔍 Running pre-deployment validation..."
        
        # Validate regional compliance
        python scripts/validate-deployment-readiness.py --region ${{ steps.region.outputs.region }}
        
        # Check database migrations
        npm run db:validate-migrations
        
        # Validate cultural configurations
        npm run validate:cultural-config -- --region ${{ steps.region.outputs.region }}
        
        echo "✅ Pre-deployment validation passed"

    - name: Blue-Green Deployment
      run: |
        echo "🔄 Starting blue-green deployment for ${{ steps.region.outputs.region }}..."
        
        # Deploy to green environment
        ./scripts/deploy-blue-green.sh \
          --region ${{ steps.region.outputs.region }} \
          --image ${{ needs.build.outputs.image-tag }} \
          --environment production
        
        echo "✅ Blue-green deployment completed"

    - name: Health check and validation
      run: |
        echo "🏥 Running health checks..."
        
        # Wait for services to be healthy
        timeout 600 bash -c './scripts/wait-for-health.sh --region ${{ steps.region.outputs.region }}'
        
        # Validate emergency response times
        npm run test:emergency-response -- --region ${{ steps.region.outputs.region }}
        
        # Validate cultural adaptations
        npm run test:cultural-validation -- --region ${{ steps.region.outputs.region }}
        
        echo "✅ Health checks passed"

    - name: Switch traffic
      run: |
        echo "🔀 Switching traffic to new deployment..."
        ./scripts/switch-traffic.sh --region ${{ steps.region.outputs.region }}
        echo "✅ Traffic switched successfully"

    - name: Post-deployment monitoring
      run: |
        echo "📊 Starting post-deployment monitoring..."
        ./scripts/monitor-deployment.sh --region ${{ steps.region.outputs.region }} --duration 300
        echo "✅ Post-deployment monitoring completed"

  # =====================================================
  # ROLLBACK CAPABILITY
  # =====================================================
  rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: failure() && (github.ref == 'refs/heads/main' || contains(github.event.inputs.environment, 'production'))
    needs: [deploy-production]
    environment: emergency-rollback
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Emergency rollback
      run: |
        echo "🚨 Initiating emergency rollback..."
        
        REGION="${{ github.event.inputs.environment || 'production-ghana' }}"
        REGION=${REGION#production-}
        
        # Rollback to previous stable version
        ./scripts/emergency-rollback.sh --region $REGION
        
        # Verify rollback success
        timeout 300 bash -c './scripts/wait-for-health.sh --region $REGION'
        
        echo "✅ Emergency rollback completed"

    - name: Notify stakeholders
      run: |
        echo "📢 Notifying stakeholders of rollback..."
        ./scripts/notify-rollback.sh --region ${{ steps.region.outputs.region }}

  # =====================================================
  # CLEANUP AND REPORTING
  # =====================================================
  cleanup:
    name: Cleanup and Reporting
    runs-on: ubuntu-latest
    if: always()
    needs: [deploy-staging, deploy-production, rollback]
    
    steps:
    - name: Cleanup old images
      run: |
        echo "🧹 Cleaning up old container images..."
        # Keep last 5 images per region
        ./scripts/cleanup-images.sh --keep 5

    - name: Generate deployment report
      run: |
        echo "📋 Generating deployment report..."
        ./scripts/generate-deployment-report.sh \
          --environment ${{ github.event.inputs.environment || 'staging' }} \
          --commit ${{ github.sha }} \
          --status ${{ job.status }}

    - name: Update monitoring dashboards
      run: |
        echo "📊 Updating monitoring dashboards..."
        ./scripts/update-dashboards.sh --deployment-id ${{ github.run_id }}

# =====================================================
# WORKFLOW NOTIFICATIONS
# =====================================================
  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    if: always()
    needs: [deploy-staging, deploy-production, rollback]
    
    steps:
    - name: Notify success
      if: success()
      run: |
        echo "✅ Deployment successful for ${{ github.event.inputs.environment || 'staging' }}"
        # Send success notification to Slack/Teams/Email

    - name: Notify failure
      if: failure()
      run: |
        echo "❌ Deployment failed for ${{ github.event.inputs.environment || 'staging' }}"
        # Send failure notification with rollback status
