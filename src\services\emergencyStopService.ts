/**
 * Emergency Stop Service
 * Optimized for < 2 second response time requirement
 * Provides immediate audio termination and local emergency state caching
 */

import { EmergencyStopEvent, EmergencyProtocolResult } from '../types/audio';

interface EmergencyStopConfig {
  readonly maxResponseTime: number; // 2000ms requirement
  readonly localCacheEnabled: boolean;
  readonly networkDependencyBypass: boolean;
  readonly immediateTermination: boolean;
}

interface EmergencyState {
  readonly isEmergencyActive: boolean;
  readonly emergencyReason: string;
  readonly emergencyStartTime: number;
  readonly sessionId: string;
  readonly userId: string;
  readonly responseTime?: number;
}

interface AudioTerminationResult {
  readonly success: boolean;
  readonly terminationTime: number;
  readonly componentsTerminated: readonly string[];
  readonly error?: string;
}

class EmergencyStopService {
  private config: EmergencyStopConfig;
  private emergencyState: EmergencyState | null = null;
  private localEmergencyCache: Map<string, EmergencyState> = new Map();
  private activeMediaRecorders: Set<MediaRecorder> = new Set();
  private activeMediaStreams: Set<MediaStream> = new Set();
  private activeAudioContexts: Set<AudioContext> = new Set();
  private emergencyCallbacks: Set<() => void> = new Set();

  constructor() {
    this.config = {
      maxResponseTime: 2000, // 2 seconds requirement
      localCacheEnabled: true,
      networkDependencyBypass: true,
      immediateTermination: true
    };

    // Pre-bind methods for immediate execution
    this.triggerEmergencyStop = this.triggerEmergencyStop.bind(this);
    this.terminateAllAudio = this.terminateAllAudio.bind(this);
  }

  /**
   * Register audio components for emergency termination
   * This allows immediate termination without searching for components
   */
  registerMediaRecorder(recorder: MediaRecorder): void {
    this.activeMediaRecorders.add(recorder);
    
    // Auto-cleanup when recorder stops
    recorder.addEventListener('stop', () => {
      this.activeMediaRecorders.delete(recorder);
    });
  }

  registerMediaStream(stream: MediaStream): void {
    this.activeMediaStreams.add(stream);
    
    // Auto-cleanup when stream ends
    stream.addEventListener('inactive', () => {
      this.activeMediaStreams.delete(stream);
    });
  }

  registerAudioContext(context: AudioContext): void {
    this.activeAudioContexts.add(context);
    
    // Auto-cleanup when context closes
    context.addEventListener('statechange', () => {
      if (context.state === 'closed') {
        this.activeAudioContexts.delete(context);
      }
    });
  }

  /**
   * Register emergency callback for immediate execution
   */
  registerEmergencyCallback(callback: () => void): void {
    this.emergencyCallbacks.add(callback);
  }

  unregisterEmergencyCallback(callback: () => void): void {
    this.emergencyCallbacks.delete(callback);
  }

  /**
   * Trigger emergency stop with < 2 second response time guarantee
   * Uses local caching and immediate termination to avoid network dependencies
   */
  async triggerEmergencyStop(
    reason: string,
    sessionId: string,
    userId: string = 'system'
  ): Promise<EmergencyProtocolResult> {
    const startTime = performance.now();
    
    try {
      console.log(`🚨 EMERGENCY STOP TRIGGERED: ${reason} at ${new Date().toISOString()}`);

      // IMMEDIATE ACTIONS (< 100ms target)
      
      // 1. Set emergency state immediately (local cache)
      this.emergencyState = {
        isEmergencyActive: true,
        emergencyReason: reason,
        emergencyStartTime: startTime,
        sessionId,
        userId
      };

      // 2. Cache emergency state locally for offline access
      if (this.config.localCacheEnabled) {
        this.localEmergencyCache.set(sessionId, this.emergencyState);
        localStorage.setItem(`emergency_${sessionId}`, JSON.stringify(this.emergencyState));
      }

      // 3. Execute emergency callbacks immediately
      this.emergencyCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error('Emergency callback failed:', error);
        }
      });

      // 4. Terminate all audio components immediately
      const terminationResult = await this.terminateAllAudio();

      const responseTime = performance.now() - startTime;

      // 5. Create emergency stop event
      const emergencyEvent: EmergencyStopEvent = {
        triggered: true,
        reason,
        timestamp: new Date().toISOString(),
        responseTime,
        userId,
        sessionId
      };

      // 6. Log emergency event (async, non-blocking)
      this.logEmergencyEventAsync(emergencyEvent);

      // 7. Trigger emergency protocols (async, non-blocking)
      this.triggerEmergencyProtocolsAsync(reason, sessionId, userId);

      const result: EmergencyProtocolResult = {
        success: true,
        protocolsTriggered: ['immediate_termination', 'local_cache', 'emergency_callbacks'],
        responseTime,
        error: terminationResult.success ? undefined : terminationResult.error
      };

      console.log(`✅ Emergency stop completed in ${responseTime.toFixed(2)}ms`);

      // Validate response time requirement
      if (responseTime > this.config.maxResponseTime) {
        console.warn(`⚠️ Emergency stop exceeded ${this.config.maxResponseTime}ms requirement: ${responseTime.toFixed(2)}ms`);
      }

      return result;

    } catch (error) {
      const responseTime = performance.now() - startTime;
      console.error(`❌ Emergency stop failed in ${responseTime.toFixed(2)}ms:`, error);

      return {
        success: false,
        protocolsTriggered: ['emergency_termination_attempted'],
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown emergency stop error'
      };
    }
  }

  /**
   * Immediately terminate all registered audio components
   * Target: < 50ms execution time
   */
  private async terminateAllAudio(): Promise<AudioTerminationResult> {
    const startTime = performance.now();
    const terminatedComponents: string[] = [];

    try {
      // Terminate MediaRecorders immediately
      for (const recorder of this.activeMediaRecorders) {
        try {
          if (recorder.state === 'recording' || recorder.state === 'paused') {
            recorder.stop();
            terminatedComponents.push('MediaRecorder');
          }
        } catch (error) {
          console.error('Failed to stop MediaRecorder:', error);
        }
      }

      // Terminate MediaStreams immediately
      for (const stream of this.activeMediaStreams) {
        try {
          stream.getTracks().forEach(track => {
            track.stop();
            terminatedComponents.push('MediaStreamTrack');
          });
        } catch (error) {
          console.error('Failed to stop MediaStream:', error);
        }
      }

      // Suspend AudioContexts immediately
      for (const context of this.activeAudioContexts) {
        try {
          if (context.state === 'running') {
            await context.suspend();
            terminatedComponents.push('AudioContext');
          }
        } catch (error) {
          console.error('Failed to suspend AudioContext:', error);
        }
      }

      const terminationTime = performance.now() - startTime;

      return {
        success: true,
        terminationTime,
        componentsTerminated: terminatedComponents
      };

    } catch (error) {
      const terminationTime = performance.now() - startTime;
      return {
        success: false,
        terminationTime,
        componentsTerminated: terminatedComponents,
        error: error instanceof Error ? error.message : 'Unknown termination error'
      };
    }
  }

  /**
   * Check if emergency is currently active
   */
  isEmergencyActive(): boolean {
    return this.emergencyState?.isEmergencyActive || false;
  }

  /**
   * Get current emergency state
   */
  getCurrentEmergencyState(): EmergencyState | null {
    return this.emergencyState;
  }

  /**
   * Get emergency state from local cache (for offline access)
   */
  getEmergencyStateFromCache(sessionId: string): EmergencyState | null {
    // Try memory cache first
    const memoryState = this.localEmergencyCache.get(sessionId);
    if (memoryState) {
      return memoryState;
    }

    // Try localStorage cache
    try {
      const cachedState = localStorage.getItem(`emergency_${sessionId}`);
      if (cachedState) {
        return JSON.parse(cachedState);
      }
    } catch (error) {
      console.error('Failed to retrieve emergency state from cache:', error);
    }

    return null;
  }

  /**
   * Clear emergency state
   */
  clearEmergencyState(sessionId: string): void {
    this.emergencyState = null;
    this.localEmergencyCache.delete(sessionId);
    localStorage.removeItem(`emergency_${sessionId}`);
  }

  /**
   * Log emergency event asynchronously (non-blocking)
   */
  private async logEmergencyEventAsync(event: EmergencyStopEvent): Promise<void> {
    try {
      // Use setTimeout to make this truly non-blocking
      setTimeout(async () => {
        try {
          const { default: auditLogger } = await import('../utils/auditLogger');
          await auditLogger.logEmergencyAccess(
            event.userId,
            'system',
            `Emergency stop triggered: ${event.reason}`,
            {
              session_id: event.sessionId,
              response_time: event.responseTime,
              timestamp: event.timestamp,
              emergency_protocols_triggered: true
            }
          );
        } catch (error) {
          console.error('Failed to log emergency event:', error);
        }
      }, 0);
    } catch (error) {
      console.error('Failed to schedule emergency event logging:', error);
    }
  }

  /**
   * Trigger emergency protocols asynchronously (non-blocking)
   */
  private async triggerEmergencyProtocolsAsync(
    reason: string,
    sessionId: string,
    userId: string
  ): Promise<void> {
    try {
      // Use setTimeout to make this truly non-blocking
      setTimeout(async () => {
        try {
          // Import emergency auth service for session management
          const { default: emergencyAuthService } = await import('./emergencyAuthService');
          
          // Create emergency session if needed
          if (reason === 'medical_emergency' || reason === 'critical_situation') {
            await emergencyAuthService.createEmergencySession(
              userId,
              sessionId,
              reason,
              'emergency_stop_service'
            );
          }

          console.log(`🚨 Emergency protocols triggered for session ${sessionId}`);
        } catch (error) {
          console.error('Failed to trigger emergency protocols:', error);
        }
      }, 0);
    } catch (error) {
      console.error('Failed to schedule emergency protocol triggering:', error);
    }
  }

  /**
   * Get emergency stop statistics
   */
  getEmergencyStats(): {
    activeRecorders: number;
    activeStreams: number;
    activeContexts: number;
    registeredCallbacks: number;
    cachedStates: number;
  } {
    return {
      activeRecorders: this.activeMediaRecorders.size,
      activeStreams: this.activeMediaStreams.size,
      activeContexts: this.activeAudioContexts.size,
      registeredCallbacks: this.emergencyCallbacks.size,
      cachedStates: this.localEmergencyCache.size
    };
  }
}

// Export singleton instance
export const emergencyStopService = new EmergencyStopService();
export default emergencyStopService;
