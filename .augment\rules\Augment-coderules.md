---
type: "always_apply"
---

# VoiceHealth AI Coding Rules & Best Practices for AI Assistants
 

## 🏥 **CRITICAL HEALTHCARE PRINCIPLES**

### **Patient Safety First**
```javascript
// ✅ ALWAYS implement fail-safes for critical medical functions
const processTriageAssessment = async (patientData) => {
  try {
    const result = await triageService.assess(patientData);
    
    // MANDATORY: Fallback for high-risk scenarios
    if (result.riskLevel === 'HIGH' && result.confidence < 0.8) {
      await emergencyEscalation.triggerHumanReview(patientData, result);
      return { ...result, humanReviewTriggered: true };
    }
    
    return result;
  } catch (error) {
    // CRITICAL: Never fail silently on medical assessments
    await emergencyEscalation.triggerFailsafe(patientData, error);
    throw new MedicalSystemError('Triage assessment failed', { patientData, error });
  }
};
```

### **Data Privacy & Security**
```javascript
// ✅ ALWAYS sanitize medical data before logging
const logMedicalEvent = (eventData) => {
  const sanitized = {
    ...eventData,
    // Remove all PII/PHI
    patientId: hashPatientId(eventData.patientId),
    symptoms: eventData.symptoms?.map(s => s.category), // Remove specific details
    // Never log: names, addresses, specific medical details
  };
  logger.info('Medical event processed', sanitized);
};

// ❌ NEVER log raw medical data
// logger.info('Patient consultation', { patientName, symptoms, diagnosis });
```

---

## 🏗️ **ARCHITECTURE & TECHNOLOGY STACK**

### **React 18 Patterns**

#### **Component Structure**
```jsx
// ✅ ALWAYS use functional components with modern hooks
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { usePayment } from '../../contexts/PaymentContext';

const VoiceConsultationComponent = ({ sessionId, onComplete }) => {
  // State management with descriptive names
  const [consultationState, setConsultationState] = useState('initializing');
  const [audioProcessingStatus, setAudioProcessingStatus] = useState('idle');
  
  // Memoize expensive computations
  const consultationConfig = useMemo(() => ({
    sessionId,
    aiProvider: 'openai',
    fallbackChain: ['anthropic', 'cohere']
  }), [sessionId]);
  
  // Stable callback references
  const handleAudioChunk = useCallback(async (audioData) => {
    try {
      setAudioProcessingStatus('processing');
      await speechEngine.processChunk(audioData, consultationConfig);
    } catch (error) {
      // Always handle errors gracefully in medical contexts
      await handleMedicalError(error, { sessionId, audioData: '[REDACTED]' });
    } finally {
      setAudioProcessingStatus('idle');
    }
  }, [consultationConfig, sessionId]);
  
  return (
    <div className="consultation-interface">
      {/* Component JSX */}
    </div>
  );
};
```

#### **Error Boundaries for Medical Components**
```jsx
// ✅ ALWAYS wrap medical components in error boundaries
class MedicalErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log to medical error tracking system
    medicalErrorLogger.logCriticalError({
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      sessionId: this.props.sessionId,
      timestamp: new Date().toISOString()
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <MedicalFallbackUI 
          message="Medical system temporarily unavailable"
          emergencyContact={this.props.emergencyContact}
          onRetry={() => this.setState({ hasError: false })}
        />
      );
    }
    return this.props.children;
  }
}
```

### **Supabase Integration Patterns**

#### **Database Operations**
```javascript
// ✅ ALWAYS use RLS-compliant queries with proper error handling
const consultationService = {
  async createConsultation(patientId, consultationData) {
    try {
      // Validate data before database insertion
      const validatedData = await validateMedicalData(consultationData);
      
      const { data, error } = await supabase
        .from('consultations')
        .insert({
          patient_id: patientId,
          ...validatedData,
          created_at: new Date().toISOString(),
          status: 'active'
        })
        .select()
        .single();

      if (error) {
        throw new DatabaseError('Failed to create consultation', { error, patientId });
      }

      // Audit trail for medical records
      await auditLogger.logMedicalRecord('consultation_created', {
        consultationId: data.id,
        patientId: hashPatientId(patientId)
      });

      return { success: true, data };
    } catch (error) {
      await errorHandler.handleMedicalDatabaseError(error, { patientId });
      throw error;
    }
  }
};
```

#### **Real-time Subscriptions**
```javascript
// ✅ ALWAYS handle subscription errors and cleanup
const useConsultationUpdates = (consultationId) => {
  const [updates, setUpdates] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('connecting');

  useEffect(() => {
    if (!consultationId) return;

    const subscription = supabase
      .channel(`consultation:${consultationId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'consultation_messages',
        filter: `consultation_id=eq.${consultationId}`
      }, (payload) => {
        setUpdates(prev => [...prev, payload.new]);
        setConnectionStatus('connected');
      })
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setConnectionStatus('connected');
        } else if (status === 'CHANNEL_ERROR') {
          setConnectionStatus('error');
          // Implement fallback polling for critical medical data
          startFallbackPolling(consultationId);
        }
      });

    return () => {
      subscription.unsubscribe();
    };
  }, [consultationId]);

  return { updates, connectionStatus };
};
```

### **PWA Offline-First Patterns**

#### **Service Worker Integration**
```javascript
// ✅ ALWAYS implement robust offline strategies for medical data
const offlineConsultationStrategy = {
  async handleOfflineConsultation(consultationData) {
    try {
      // Store consultation locally with high priority
      await offlineDatabase.consultations.add({
        ...consultationData,
        status: 'offline_pending',
        priority: 'high',
        created_at: new Date().toISOString()
      });

      // Queue for background sync with retry logic
      await backgroundSync.queueSync('consultation', {
        data: consultationData,
        retryCount: 0,
        maxRetries: 5,
        backoffMultiplier: 2
      });

      return { success: true, offline: true };
    } catch (error) {
      // Critical: Ensure offline medical data is never lost
      await emergencyStorage.storeConsultation(consultationData);
      throw new OfflineStorageError('Failed to store offline consultation', error);
    }
  }
};
```

#### **Background Sync Implementation**
```javascript
// ✅ ALWAYS prioritize medical data in sync queues
class MedicalBackgroundSync {
  constructor() {
    this.syncQueue = new PriorityQueue((a, b) => {
      // Medical consultations have highest priority
      const priorityMap = {
        'emergency': 1,
        'consultation': 2,
        'triage': 3,
        'analytics': 4
      };
      return priorityMap[a.type] - priorityMap[b.type];
    });
  }

  async queueMedicalData(type, data, options = {}) {
    const syncItem = {
      id: generateUUID(),
      type,
      data: await encryptMedicalData(data),
      timestamp: new Date().toISOString(),
      retryCount: 0,
      maxRetries: options.maxRetries || 5,
      priority: options.priority || 'normal'
    };

    await this.syncQueue.enqueue(syncItem);
    
    // Attempt immediate sync if online
    if (navigator.onLine) {
      this.processSyncQueue();
    }
  }
}
```

### **Redux Toolkit State Management**

#### **Medical State Slices**
```javascript
// ✅ ALWAYS use RTK Query for medical data with proper caching
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const medicalApi = createApi({
  reducerPath: 'medicalApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/medical/',
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      // Always include medical context headers
      headers.set('x-medical-context', 'consultation');
      return headers;
    },
  }),
  tagTypes: ['Consultation', 'TriageResult', 'MedicalRecord'],
  endpoints: (builder) => ({
    getConsultation: builder.query({
      query: (id) => `consultations/${id}`,
      providesTags: (result, error, id) => [{ type: 'Consultation', id }],
      // Cache medical data for offline access
      keepUnusedDataFor: 300, // 5 minutes
    }),
    
    createTriageAssessment: builder.mutation({
      query: (assessment) => ({
        url: 'triage/assess',
        method: 'POST',
        body: assessment,
      }),
      invalidatesTags: ['TriageResult'],
      // Always handle medical mutation errors
      transformErrorResponse: (response) => ({
        status: response.status,
        message: 'Medical assessment failed',
        originalError: response.data
      }),
    }),
  }),
});
```

#### **Medical State Management**
```javascript
// ✅ ALWAYS implement medical state with audit trails
const consultationSlice = createSlice({
  name: 'consultation',
  initialState: {
    activeSession: null,
    sessionHistory: [],
    triageResults: [],
    emergencyStatus: 'normal',
    auditTrail: []
  },
  reducers: {
    startConsultation: (state, action) => {
      const { sessionId, patientId, timestamp } = action.payload;

      state.activeSession = {
        id: sessionId,
        patientId: hashPatientId(patientId), // Never store raw patient ID
        startTime: timestamp,
        status: 'active',
        messages: []
      };

      // Audit trail for medical session
      state.auditTrail.push({
        action: 'consultation_started',
        sessionId,
        timestamp,
        userId: action.payload.userId
      });
    },

    updateEmergencyStatus: (state, action) => {
      const { status, reason, timestamp } = action.payload;

      // Critical: Always log emergency status changes
      state.emergencyStatus = status;
      state.auditTrail.push({
        action: 'emergency_status_changed',
        from: state.emergencyStatus,
        to: status,
        reason,
        timestamp,
        critical: true
      });

      // Trigger emergency protocols if needed
      if (status === 'critical') {
        // This would trigger side effects via middleware
        state.requiresImmediateEscalation = true;
      }
    }
  }
});
```

---

## 🎨 **TAILWINDCSS STYLING CONVENTIONS**

### **Medical UI Component Patterns**
```jsx
// ✅ ALWAYS use semantic color schemes for medical interfaces
const MedicalStatusIndicator = ({ status, urgency }) => {
  const statusClasses = {
    normal: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    critical: 'bg-red-100 text-red-800 border-red-200 animate-pulse',
    emergency: 'bg-red-600 text-white border-red-700 animate-bounce'
  };

  const urgencyClasses = {
    low: 'ring-1 ring-gray-300',
    medium: 'ring-2 ring-yellow-400',
    high: 'ring-2 ring-red-400',
    critical: 'ring-4 ring-red-600 shadow-lg'
  };

  return (
    <div className={`
      px-4 py-2 rounded-lg border-2 transition-all duration-200
      ${statusClasses[status]}
      ${urgencyClasses[urgency]}
      focus:outline-none focus:ring-4 focus:ring-blue-500
      aria-live="polite"
    `}>
      <span className="sr-only">Medical status: {status}, Urgency: {urgency}</span>
      {/* Status content */}
    </div>
  );
};
```

### **Responsive Medical Layouts**
```jsx
// ✅ ALWAYS ensure medical interfaces work on mobile devices
const ConsultationLayout = ({ children }) => (
  <div className="
    min-h-screen bg-gray-50
    grid grid-cols-1 lg:grid-cols-4 gap-4
    p-4 lg:p-6
    max-w-7xl mx-auto
  ">
    {/* Patient Info Panel - Always visible on mobile */}
    <aside className="
      lg:col-span-1
      bg-white rounded-lg shadow-sm border
      p-4 h-fit
      order-1 lg:order-1
    ">
      <PatientInfoPanel />
    </aside>

    {/* Main Consultation Area */}
    <main className="
      lg:col-span-2
      bg-white rounded-lg shadow-sm border
      p-6
      order-2 lg:order-2
      min-h-[600px]
    ">
      {children}
    </main>

    {/* Medical Tools Sidebar */}
    <aside className="
      lg:col-span-1
      bg-white rounded-lg shadow-sm border
      p-4
      order-3 lg:order-3
      space-y-4
    ">
      <MedicalToolsPanel />
    </aside>
  </div>
);
```

---

## 🌍 **REGIONAL & ACCESSIBILITY REQUIREMENTS**

### **Multi-language Implementation**
```javascript
// ✅ ALWAYS implement proper i18n for medical content
const medicalTranslations = {
  en: {
    'triage.chest_pain': 'Chest pain',
    'triage.breathing_difficulty': 'Difficulty breathing',
    'emergency.call_now': 'Call emergency services now'
  },
  sw: { // Swahili for Kenya/Tanzania
    'triage.chest_pain': 'Maumivu ya kifua',
    'triage.breathing_difficulty': 'Ugumu wa kupumua',
    'emergency.call_now': 'Piga simu ya dharura sasa'
  },
  ha: { // Hausa for Nigeria
    'triage.chest_pain': 'Ciwon kirji',
    'triage.breathing_difficulty': 'Wahalar numfashi',
    'emergency.call_now': 'Kira sabis na gaggawa yanzu'
  }
};

const useMedicalTranslation = () => {
  const { language } = useLanguage();

  const t = useCallback((key, params = {}) => {
    const translation = medicalTranslations[language]?.[key] ||
                       medicalTranslations.en[key] ||
                       key;

    // Log missing medical translations for critical review
    if (!medicalTranslations[language]?.[key]) {
      medicalLogger.warn('Missing medical translation', { key, language });
    }

    return interpolateParams(translation, params);
  }, [language]);

  return { t };
};
```

### **Paystack Integration Patterns**
```javascript
// ✅ ALWAYS handle African payment methods with proper error handling
const paystackService = {
  async processConsultationPayment(amount, currency, patientId) {
    try {
      // Validate currency for African markets
      const supportedCurrencies = ['NGN', 'GHS', 'ZAR', 'KES'];
      if (!supportedCurrencies.includes(currency)) {
        throw new PaymentError(`Unsupported currency: ${currency}`);
      }

      const paymentData = {
        amount: amount * 100, // Paystack uses kobo/pesewas
        currency,
        email: await getPatientEmail(patientId),
        reference: `consultation_${patientId}_${Date.now()}`,
        callback_url: `${window.location.origin}/payment-success`,
        metadata: {
          patient_id: hashPatientId(patientId),
          service_type: 'consultation',
          region: await getUserRegion(patientId)
        }
      };

      const response = await PaystackPop.setup({
        ...paymentData,
        onSuccess: (transaction) => {
          // Verify payment server-side before granting access
          this.verifyPayment(transaction.reference);
        },
        onCancel: () => {
          // Log payment cancellation for medical billing
          medicalBillingLogger.logPaymentCancellation(paymentData);
        }
      });

      response.openIframe();
    } catch (error) {
      await this.handlePaymentError(error, { patientId, amount, currency });
      throw error;
    }
  }
};
```

### **Voice-First Accessibility**
```javascript
// ✅ ALWAYS implement comprehensive voice accessibility
const VoiceAccessibilityProvider = ({ children }) => {
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [voiceLanguage, setVoiceLanguage] = useState('en-US');

  const speakMedicalContent = useCallback(async (text, priority = 'normal') => {
    if (!voiceEnabled) return;

    try {
      // Cancel any non-critical speech for medical emergencies
      if (priority === 'emergency') {
        speechSynthesis.cancel();
      }

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = priority === 'emergency' ? 1.2 : speechRate;
      utterance.lang = voiceLanguage;
      utterance.volume = priority === 'emergency' ? 1.0 : 0.8;

      // Add medical context to speech
      if (priority === 'emergency') {
        utterance.voice = speechSynthesis.getVoices()
          .find(voice => voice.name.includes('Medical')) ||
          speechSynthesis.getVoices()[0];
      }

      speechSynthesis.speak(utterance);
    } catch (error) {
      // Fallback to visual alerts for critical medical information
      if (priority === 'emergency') {
        showVisualEmergencyAlert(text);
      }
      console.error('Voice synthesis failed:', error);
    }
  }, [voiceEnabled, speechRate, voiceLanguage]);

  return (
    <VoiceAccessibilityContext.Provider value={{
      voiceEnabled,
      setVoiceEnabled,
      speakMedicalContent,
      speechRate,
      setSpeechRate
    }}>
      {children}
    </VoiceAccessibilityContext.Provider>
  );
};
```

---

## 🔧 **CODE QUALITY & MAINTENANCE**

### **Error Handling Patterns**
```javascript
// ✅ ALWAYS implement comprehensive error handling for medical systems
class MedicalError extends Error {
  constructor(message, context = {}) {
    super(message);
    this.name = 'MedicalError';
    this.context = context;
    this.timestamp = new Date().toISOString();
    this.severity = context.severity || 'medium';
    this.patientImpact = context.patientImpact || 'low';
  }
}

class EmergencySystemError extends MedicalError {
  constructor(message, context = {}) {
    super(message, { ...context, severity: 'critical', patientImpact: 'high' });
    this.name = 'EmergencySystemError';
    // Immediately trigger emergency protocols
    this.triggerEmergencyProtocol();
  }

  triggerEmergencyProtocol() {
    // Implement immediate escalation
    emergencyEscalation.triggerSystemFailure(this);
  }
}

const medicalErrorHandler = {
  async handleError(error, context = {}) {
    const errorId = generateUUID();

    // Always log medical errors with full context
    await medicalLogger.logError({
      id: errorId,
      error: error.message,
      stack: error.stack,
      context: {
        ...context,
        patientId: context.patientId ? hashPatientId(context.patientId) : null
      },
      severity: error.severity || 'medium',
      timestamp: new Date().toISOString()
    });

    // Escalate critical medical errors immediately
    if (error.severity === 'critical' || error instanceof EmergencySystemError) {
      await emergencyEscalation.escalateError(error, errorId);
    }

    // Provide user-friendly error messages
    const userMessage = this.getUserFriendlyMessage(error);
    return { errorId, message: userMessage, severity: error.severity };
  },

  getUserFriendlyMessage(error) {
    const messageMap = {
      'NetworkError': 'Connection issue detected. Your consultation data is saved offline.',
      'PaymentError': 'Payment processing failed. Please try again or contact support.',
      'MedicalSystemError': 'Medical system temporarily unavailable. Emergency services: 911',
      'TriageError': 'Assessment system unavailable. Please seek immediate medical attention if urgent.'
    };

    return messageMap[error.name] || 'An unexpected error occurred. Your safety is our priority.';
  }
};
```

### **Testing Strategies**
```javascript
// ✅ ALWAYS test medical functionality with comprehensive scenarios
describe('Medical Triage System', () => {
  beforeEach(async () => {
    await setupMedicalTestEnvironment();
    await seedTestPatientData();
  });

  describe('Emergency Scenarios', () => {
    test('should immediately escalate chest pain with high severity', async () => {
      const patientData = {
        symptoms: ['chest_pain', 'shortness_of_breath'],
        severity: 9,
        duration: '30_minutes'
      };

      const result = await triageService.assess(patientData);

      expect(result.urgency).toBe('EMERGENCY');
      expect(result.escalated).toBe(true);
      expect(result.recommendedAction).toBe('IMMEDIATE_MEDICAL_ATTENTION');

      // Verify emergency escalation was triggered
      expect(emergencyEscalation.triggerEmergencyResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          patientId: expect.any(String),
          urgency: 'EMERGENCY'
        })
      );
    });

    test('should handle triage system failure gracefully', async () => {
      // Simulate system failure
      jest.spyOn(triageService, 'assess').mockRejectedValue(new Error('System failure'));

      const patientData = { symptoms: ['headache'], severity: 5 };

      await expect(async () => {
        await triageService.assess(patientData);
      }).rejects.toThrow('System failure');

      // Verify failsafe was triggered
      expect(emergencyEscalation.triggerFailsafe).toHaveBeenCalled();
    });
  });

  describe('Offline Functionality', () => {
    test('should store consultation data offline when network unavailable', async () => {
      // Simulate offline state
      Object.defineProperty(navigator, 'onLine', { value: false });

      const consultationData = {
        patientId: 'test-patient-123',
        symptoms: ['fever', 'cough'],
        timestamp: new Date().toISOString()
      };

      const result = await consultationService.createConsultation(consultationData);

      expect(result.offline).toBe(true);
      expect(result.success).toBe(true);

      // Verify data was stored in offline database
      const storedData = await offlineDatabase.consultations
        .where('patientId').equals('test-patient-123')
        .first();

      expect(storedData).toBeDefined();
      expect(storedData.status).toBe('offline_pending');
    });
  });
});
```

### **Performance Optimization**
```javascript
// ✅ ALWAYS optimize for mobile devices in African markets
const performanceOptimizations = {
  // Lazy load non-critical medical components
  lazyLoadMedicalComponents: () => {
    const MedicalHistoryPanel = lazy(() =>
      import('./components/MedicalHistoryPanel').then(module => ({
        default: module.MedicalHistoryPanel
      }))
    );

    const AdvancedDiagnostics = lazy(() =>
      import('./components/AdvancedDiagnostics')
    );

    return { MedicalHistoryPanel, AdvancedDiagnostics };
  },

  // Optimize audio processing for low-end devices
  optimizeAudioProcessing: {
    // Use lower sample rates for voice in poor network conditions
    getOptimalAudioConfig: (networkSpeed) => ({
      sampleRate: networkSpeed === 'slow' ? 16000 : 44100,
      bitRate: networkSpeed === 'slow' ? 64 : 128,
      channels: 1, // Mono for medical voice
      format: 'webm' // Better compression for mobile
    }),

    // Implement audio chunking for real-time processing
    processAudioInChunks: async function* (audioStream, chunkSize = 1024) {
      const reader = audioStream.getReader();
      let buffer = new Uint8Array(0);

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          // Combine with existing buffer
          const newBuffer = new Uint8Array(buffer.length + value.length);
          newBuffer.set(buffer);
          newBuffer.set(value, buffer.length);
          buffer = newBuffer;

          // Yield chunks when buffer is large enough
          while (buffer.length >= chunkSize) {
            yield buffer.slice(0, chunkSize);
            buffer = buffer.slice(chunkSize);
          }
        }

        // Yield remaining data
        if (buffer.length > 0) {
          yield buffer;
        }
      } finally {
        reader.releaseLock();
      }
    }
  }
};
```

---

## 📁 **FILE ORGANIZATION & NAMING CONVENTIONS**

### **Directory Structure Rules**
```
src/
├── components/
│   ├── medical/              # Medical-specific components
│   │   ├── triage/          # Triage system components
│   │   ├── consultation/    # Consultation interface components
│   │   └── emergency/       # Emergency escalation components
│   ├── ui/                  # Generic UI components
│   └── accessibility/       # Accessibility-focused components
├── services/
│   ├── medical/             # Medical business logic
│   │   ├── triage.service.js
│   │   ├── consultation.service.js
│   │   └── emergency.service.js
│   ├── ai/                  # AI orchestration services
│   └── infrastructure/      # Infrastructure services (PWA, sync, etc.)
├── hooks/
│   ├── medical/             # Medical-specific hooks
│   └── infrastructure/      # Infrastructure hooks
├── utils/
│   ├── medical/             # Medical utilities
│   ├── security/            # Security and encryption utilities
│   └── regional/            # Regional/localization utilities
└── types/
    ├── medical.types.js     # Medical data types
    ├── api.types.js         # API response types
    └── regional.types.js    # Regional/payment types
```

### **Naming Conventions**
```javascript
// ✅ ALWAYS use descriptive, medical-context names
// Components
const TriageAssessmentPanel = () => {};
const EmergencyEscalationButton = () => {};
const VoiceConsultationInterface = () => {};

// Services
class MedicalTriageService {}
class EmergencyEscalationService {}
class VoiceProcessingEngine {}

// Hooks
const useMedicalTriage = () => {};
const useEmergencyEscalation = () => {};
const useVoiceConsultation = () => {};

// Constants
const MEDICAL_URGENCY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
  EMERGENCY: 'emergency'
};

const AFRICAN_CURRENCIES = {
  NIGERIA: 'NGN',
  GHANA: 'GHS',
  KENYA: 'KES',
  SOUTH_AFRICA: 'ZAR'
};
```

---

## 🔒 **SECURITY & COMPLIANCE RULES**

### **Data Encryption & Privacy**
```javascript
// ✅ ALWAYS encrypt sensitive medical data
const medicalDataEncryption = {
  async encryptPatientData(data) {
    const key = await this.getEncryptionKey();
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv: crypto.getRandomValues(new Uint8Array(12)) },
      key,
      new TextEncoder().encode(JSON.stringify(data))
    );

    return {
      encrypted: Array.from(new Uint8Array(encrypted)),
      timestamp: new Date().toISOString(),
      version: '1.0'
    };
  },

  async decryptPatientData(encryptedData) {
    const key = await this.getEncryptionKey();
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv: new Uint8Array(encryptedData.iv) },
      key,
      new Uint8Array(encryptedData.encrypted)
    );

    return JSON.parse(new TextDecoder().decode(decrypted));
  }
};

// ✅ ALWAYS implement data retention policies
const dataRetentionService = {
  async scheduleDataCleanup(patientId, dataType, retentionPeriod) {
    const cleanupDate = new Date();
    cleanupDate.setDate(cleanupDate.getDate() + retentionPeriod);

    await supabase.from('data_retention_schedule').insert({
      patient_id: hashPatientId(patientId),
      data_type: dataType,
      cleanup_date: cleanupDate.toISOString(),
      status: 'scheduled'
    });
  }
};
```

### **Audit Trail Implementation**
```javascript
// ✅ ALWAYS maintain comprehensive audit trails for medical actions
const auditTrailService = {
  async logMedicalAction(action, context) {
    const auditEntry = {
      id: generateUUID(),
      action,
      user_id: context.userId,
      patient_id: context.patientId ? hashPatientId(context.patientId) : null,
      session_id: context.sessionId,
      timestamp: new Date().toISOString(),
      ip_address: await this.getHashedIP(context.ipAddress),
      user_agent: context.userAgent ? this.sanitizeUserAgent(context.userAgent) : null,
      details: this.sanitizeAuditDetails(context.details),
      severity: context.severity || 'info'
    };

    await supabase.from('medical_audit_trail').insert(auditEntry);

    // Also log to external audit system for compliance
    await externalAuditSystem.log(auditEntry);
  }
};
```

---

## 🚀 **DEVELOPMENT WORKFLOW RULES**

### **Component Development Guidelines**
```javascript
// ✅ ALWAYS follow this component development pattern
const MedicalComponent = ({
  patientId,
  sessionId,
  onEmergency,
  onComplete
}) => {
  // 1. Hooks at the top
  const { user } = useAuth();
  const { t } = useMedicalTranslation();
  const [medicalState, setMedicalState] = useState(initialState);

  // 2. Derived state and memoized values
  const isEmergencyCase = useMemo(() =>
    medicalState.urgency === 'EMERGENCY', [medicalState.urgency]
  );

  // 3. Event handlers with error boundaries
  const handleMedicalEvent = useCallback(async (eventData) => {
    try {
      await medicalService.processEvent(eventData);
      await auditTrailService.logMedicalAction('event_processed', {
        userId: user.id,
        patientId,
        sessionId,
        details: eventData
      });
    } catch (error) {
      await medicalErrorHandler.handleError(error, { patientId, sessionId });
      if (error.severity === 'critical') {
        onEmergency?.(error);
      }
    }
  }, [user.id, patientId, sessionId, onEmergency]);

  // 4. Effects for medical monitoring
  useEffect(() => {
    if (isEmergencyCase) {
      // Immediate emergency protocols
      emergencyEscalation.triggerEmergencyResponse({
        patientId,
        sessionId,
        urgency: medicalState.urgency
      });
    }
  }, [isEmergencyCase, patientId, sessionId, medicalState.urgency]);

  // 5. Early returns for error states
  if (medicalState.error) {
    return (
      <MedicalErrorFallback
        error={medicalState.error}
        onRetry={() => setMedicalState(initialState)}
        emergencyContact="911"
      />
    );
  }

  // 6. Main render with accessibility
  return (
    <div
      className="medical-component"
      role="region"
      aria-label={t('medical.component.label')}
      aria-live={isEmergencyCase ? 'assertive' : 'polite'}
    >
      {/* Component content */}
    </div>
  );
};
```

### **Service Layer Architecture**
```javascript
// ✅ ALWAYS structure services with this pattern
class MedicalService {
  constructor(dependencies = {}) {
    this.supabase = dependencies.supabase || supabase;
    this.logger = dependencies.logger || medicalLogger;
    this.encryption = dependencies.encryption || medicalDataEncryption;
    this.auditTrail = dependencies.auditTrail || auditTrailService;
  }

  async processPatientData(patientData, options = {}) {
    const operationId = generateUUID();

    try {
      // 1. Validate input data
      const validatedData = await this.validatePatientData(patientData);

      // 2. Encrypt sensitive data
      const encryptedData = await this.encryption.encryptPatientData(validatedData);

      // 3. Process with audit trail
      await this.auditTrail.logMedicalAction('patient_data_processing_started', {
        operationId,
        patientId: patientData.id,
        dataTypes: Object.keys(validatedData)
      });

      // 4. Main processing logic
      const result = await this.executeProcessing(encryptedData, options);

      // 5. Log successful completion
      await this.auditTrail.logMedicalAction('patient_data_processing_completed', {
        operationId,
        patientId: patientData.id,
        success: true
      });

      return result;

    } catch (error) {
      // 6. Comprehensive error handling
      await this.handleProcessingError(error, { operationId, patientData });
      throw error;
    }
  }

  async validatePatientData(data) {
    // Medical data validation logic
    const schema = {
      id: { required: true, type: 'string' },
      symptoms: { required: true, type: 'array' },
      severity: { required: true, type: 'number', min: 1, max: 10 }
    };

    return validateMedicalSchema(data, schema);
  }
}
```

### **Database Migration Patterns**
```sql
-- ✅ ALWAYS follow this migration pattern for medical data
-- Migration: 20241220000000_add_medical_feature.sql

-- 1. Create tables with proper constraints
CREATE TABLE IF NOT EXISTS medical_consultations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID NOT NULL,
  session_id UUID NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'active',
  urgency_level VARCHAR(20) NOT NULL DEFAULT 'normal',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

  -- Medical-specific constraints
  CONSTRAINT valid_urgency_level CHECK (
    urgency_level IN ('low', 'normal', 'high', 'critical', 'emergency')
  ),
  CONSTRAINT valid_status CHECK (
    status IN ('active', 'completed', 'cancelled', 'emergency_escalated')
  )
);

-- 2. Add RLS policies for medical data
ALTER TABLE medical_consultations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own consultations" ON medical_consultations
  FOR ALL USING (
    auth.uid() = patient_id OR
    EXISTS (
      SELECT 1 FROM medical_staff
      WHERE user_id = auth.uid() AND role IN ('doctor', 'nurse', 'admin')
    )
  );

-- 3. Create audit trigger
CREATE OR REPLACE FUNCTION audit_medical_consultation_changes()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO medical_audit_trail (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    timestamp
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
    auth.uid(),
    NOW()
  );

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER audit_medical_consultations
  AFTER INSERT OR UPDATE OR DELETE ON medical_consultations
  FOR EACH ROW EXECUTE FUNCTION audit_medical_consultation_changes();

-- 4. Add indexes for performance
CREATE INDEX idx_medical_consultations_patient_id ON medical_consultations(patient_id);
CREATE INDEX idx_medical_consultations_urgency ON medical_consultations(urgency_level);
CREATE INDEX idx_medical_consultations_status ON medical_consultations(status);
```

---

## 🎯 **AI PROVIDER INTEGRATION RULES**

### **AI Orchestration Patterns**
```javascript
// ✅ ALWAYS implement robust AI provider fallback chains
class MedicalAIOrchestrator {
  constructor() {
    this.providers = {
      primary: { name: 'OpenAI', endpoint: '/api/openai', priority: 1 },
      secondary: { name: 'Anthropic', endpoint: '/api/anthropic', priority: 2 },
      fallback: { name: 'Cohere', endpoint: '/api/cohere', priority: 3 }
    };

    this.medicalPrompts = {
      triage: 'You are a medical triage assistant. Assess urgency based on symptoms.',
      consultation: 'You are a medical consultation AI. Provide helpful medical guidance.',
      emergency: 'EMERGENCY: Provide immediate medical guidance and escalation recommendations.'
    };
  }

  async processConsultation(messages, context) {
    const medicalContext = this.buildMedicalContext(context);

    for (const [key, provider] of Object.entries(this.providers)) {
      try {
        const response = await this.callProvider(provider, messages, medicalContext);

        // Validate medical response
        const validatedResponse = await this.validateMedicalResponse(response);

        // Log successful AI interaction
        await medicalLogger.logAIInteraction({
          provider: provider.name,
          success: true,
          responseTime: response.responseTime,
          patientId: hashPatientId(context.patientId)
        });

        return validatedResponse;

      } catch (error) {
        await this.handleProviderError(provider, error, context);

        // Continue to next provider unless it's the last one
        if (key === 'fallback') {
          throw new MedicalAIError('All AI providers failed', { context, error });
        }
      }
    }
  }

  buildMedicalContext(context) {
    return {
      urgency: context.urgency || 'normal',
      symptoms: context.symptoms || [],
      medicalHistory: context.medicalHistory || [],
      region: context.region || 'general',
      language: context.language || 'en',
      emergencyProtocols: context.urgency === 'emergency'
    };
  }
}
```

### **Medical Response Validation**
```javascript
// ✅ ALWAYS validate AI responses for medical safety
const medicalResponseValidator = {
  async validateMedicalResponse(response) {
    const validationChecks = [
      this.checkForDangerousAdvice(response),
      this.checkForMedicalDisclaimer(response),
      this.checkForEmergencyEscalation(response),
      this.checkForRegionalCompliance(response)
    ];

    const results = await Promise.all(validationChecks);
    const failed = results.filter(r => !r.passed);

    if (failed.length > 0) {
      throw new MedicalValidationError('AI response failed safety checks', {
        failedChecks: failed,
        originalResponse: response
      });
    }

    return {
      ...response,
      validated: true,
      validationTimestamp: new Date().toISOString()
    };
  },

  checkForDangerousAdvice(response) {
    const dangerousPatterns = [
      /ignore.*(doctor|medical|professional)/i,
      /don't.*(seek|get).*(help|treatment)/i,
      /avoid.*(hospital|emergency)/i
    ];

    const hasDangerousContent = dangerousPatterns.some(pattern =>
      pattern.test(response.content)
    );

    return {
      passed: !hasDangerousContent,
      check: 'dangerous_advice',
      details: hasDangerousContent ? 'Contains potentially dangerous medical advice' : null
    };
  }
};
```

---

## 📋 **MANDATORY CHECKLIST FOR ALL CODE CHANGES**

### **Pre-Development Checklist**
- [ ] **Patient Safety Impact Assessment**: Will this change affect patient safety?
- [ ] **Data Privacy Review**: Does this handle medical/personal data?
- [ ] **Regional Compliance Check**: Does this work for all target African markets?
- [ ] **Offline Functionality**: Will this work offline?
- [ ] **Emergency Scenarios**: How does this behave in emergency situations?

### **During Development Checklist**
- [ ] **Error Handling**: Comprehensive error handling implemented?
- [ ] **Audit Logging**: All medical actions logged to audit trail?
- [ ] **Data Encryption**: Sensitive data encrypted at rest and in transit?
- [ ] **Accessibility**: Voice-first and screen reader compatible?
- [ ] **Performance**: Optimized for low-end mobile devices?

### **Pre-Commit Checklist**
- [ ] **Medical Tests**: All medical scenarios tested?
- [ ] **Offline Tests**: Offline functionality verified?
- [ ] **Security Tests**: No PII/PHI leaks in logs or errors?
- [ ] **Regional Tests**: Tested with African currencies/languages?
- [ ] **Emergency Tests**: Emergency escalation paths verified?

### **Deployment Checklist**
- [ ] **Database Migrations**: Medical data migrations tested?
- [ ] **Rollback Plan**: Emergency rollback procedure ready?
- [ ] **Monitoring**: Medical error monitoring configured?
- [ ] **Compliance**: Regional compliance requirements met?
- [ ] **Documentation**: Medical procedures documented?

---

## 🚨 **CRITICAL NEVER-DO RULES**

### **Absolute Prohibitions**
```javascript
// ❌ NEVER do these in medical applications:

// 1. NEVER log raw medical data
console.log('Patient data:', patientData); // FORBIDDEN

// 2. NEVER ignore medical errors
try {
  await triageAssessment();
} catch (error) {
  // Silent failure - FORBIDDEN
}

// 3. NEVER store unencrypted medical data
localStorage.setItem('patient', JSON.stringify(patientData)); // FORBIDDEN

// 4. NEVER skip emergency escalation
if (urgency === 'EMERGENCY') {
  // TODO: Add escalation later - FORBIDDEN
}

// 5. NEVER use non-medical AI for medical decisions
const response = await generalAI.chat(medicalSymptoms); // FORBIDDEN

// 6. NEVER bypass audit trails for medical actions
await updatePatientRecord(data); // Without audit logging - FORBIDDEN
```

### **Emergency Override Protocol**
```javascript
// ✅ ONLY acceptable override pattern for true emergencies
const EMERGENCY_OVERRIDE_CODE = 'MEDICAL_EMERGENCY_OVERRIDE_2024';

const emergencyOverride = {
  async bypassNormalFlow(reason, authorizedBy, patientId) {
    // This should ONLY be used in life-threatening situations
    if (!this.isAuthorizedForOverride(authorizedBy)) {
      throw new UnauthorizedOverrideError('Emergency override not authorized');
    }

    // Log the override immediately
    await auditTrailService.logEmergencyOverride({
      reason,
      authorizedBy,
      patientId: hashPatientId(patientId),
      timestamp: new Date().toISOString(),
      overrideCode: EMERGENCY_OVERRIDE_CODE
    });

    // Notify medical director immediately
    await emergencyNotification.notifyMedicalDirector({
      type: 'EMERGENCY_OVERRIDE',
      reason,
      authorizedBy,
      patientId: hashPatientId(patientId)
    });
  }
};
```

---

## 📚 **CONCLUSION**

These rules prioritize **patient safety**, **data security**, and **system reliability** above all else. When in doubt:

1. **Choose the safer option** for patient wellbeing
2. **Encrypt and audit** all medical data interactions
3. **Test thoroughly** with emergency scenarios
4. **Implement comprehensive error handling** with fallbacks
5. **Maintain offline functionality** for critical features
6. **Follow regional compliance** requirements
7. **Document all medical procedures** and decisions

Remember: In healthcare applications, **system failures can directly impact human lives**. Every line of code should be written with this responsibility in mind.
