/**
 * PHASE 2 SERVICE INTEGRATION TEST SUITE
 * 
 * Comprehensive testing for Phase 2 service integration including:
 * - AI Orchestrator integration with new services
 * - Authentication service functionality
 * - Service method implementations
 * - Performance validation
 * - Emergency protocol testing
 * 
 * TARGET: 90%+ test coverage for all integrated services
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { aiOrchestrator } from '../services/aiOrchestrator';
import { authenticationService } from '../services/AuthenticationService';
import { clinicalDocumentationService } from '../services/ClinicalDocumentationService';
import { advancedRiskStratificationService } from '../services/AdvancedRiskStratificationService';
import { culturalValidationService } from '../services/CulturalValidationService';

// =====================================================
// AI ORCHESTRATOR INTEGRATION TESTS
// =====================================================

describe('AI Orchestrator Service Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateClinicalDocumentation', () => {
    it('should generate clinical documentation from voice input', async () => {
      const audioTranscription = 'Patient complains of headache and fever for 3 days';
      const patientId = 'test-patient-123';
      const providerId = 'test-provider-456';
      const culturalContext = {
        cultureCode: 'akan',
        languagePreference: 'en',
        familyInvolvementLevel: 'high'
      };

      const result = await aiOrchestrator.generateClinicalDocumentation(
        audioTranscription,
        patientId,
        providerId,
        culturalContext
      );

      expect(result.success).toBe(true);
      expect(result.documentation).toBeDefined();
      expect(result.documentation.clinicalNote).toBeDefined();
      expect(result.documentation.clinicalNote.chiefComplaint).toContain('headache');
      expect(result.documentation.culturalAdaptations).toBeDefined();
      expect(result.timestamp).toBeDefined();
    });

    it('should handle errors gracefully in clinical documentation generation', async () => {
      const invalidTranscription = '';
      const patientId = 'test-patient-123';
      const providerId = 'test-provider-456';

      await expect(
        aiOrchestrator.generateClinicalDocumentation(
          invalidTranscription,
          patientId,
          providerId
        )
      ).rejects.toThrow();
    });
  });

  describe('performRiskStratification', () => {
    it('should perform advanced risk stratification', async () => {
      const patientData = {
        id: 'test-patient-123',
        demographics: {
          age: 45,
          gender: 'female',
          country: 'GH'
        },
        medicalHistory: ['hypertension'],
        symptoms: ['headache', 'dizziness'],
        behavioralFactors: {
          smokingStatus: 'never',
          physicalActivity: 'moderate',
          dietQuality: 'good',
          adherenceToMedications: 'good'
        },
        socioeconomicFactors: {
          income: 'middle',
          education: 'secondary',
          healthcareAccess: 'moderate'
        },
        environmentalFactors: []
      };

      const culturalContext = {
        cultureCode: 'akan',
        traditionalMedicineOpenness: 4
      };

      const result = await aiOrchestrator.performRiskStratification(
        patientData,
        culturalContext
      );

      expect(result.success).toBe(true);
      expect(result.riskAssessment).toBeDefined();
      expect(result.riskAssessment.overallRiskScore).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.overallRiskScore).toBeLessThanOrEqual(100);
      expect(result.riskAssessment.riskCategory).toMatch(/low|moderate|high|critical/);
      expect(result.riskAssessment.predictiveAnalytics).toBeDefined();
    });

    it('should handle missing patient data gracefully', async () => {
      const incompletePatientData = {
        id: 'test-patient-123'
      };

      await expect(
        aiOrchestrator.performRiskStratification(incompletePatientData)
      ).rejects.toThrow();
    });
  });

  describe('validateCulturalContent', () => {
    it('should validate cultural appropriateness of content', async () => {
      const content = 'Please take your medication as prescribed and follow up with your family doctor.';
      const culturalContext = {
        cultureCode: 'akan',
        languagePreference: 'en',
        country: 'GH',
        ethnicGroup: 'akan',
        religiousContext: ['christian'],
        familyStructure: 'extended'
      };

      const result = await aiOrchestrator.validateCulturalContent(
        content,
        culturalContext
      );

      expect(result.success).toBe(true);
      expect(result.validation).toBeDefined();
      expect(result.validation.overallScore).toBeGreaterThanOrEqual(0);
      expect(result.validation.overallScore).toBeLessThanOrEqual(100);
      expect(result.validation.culturalSensitivity).toBeDefined();
      expect(result.validation.biasDetection).toBeDefined();
    });

    it('should detect cultural insensitivity', async () => {
      const insensitiveContent = 'This primitive treatment approach is backward and uncivilized.';
      const culturalContext = {
        cultureCode: 'akan',
        country: 'GH',
        ethnicGroup: 'akan'
      };

      const result = await aiOrchestrator.validateCulturalContent(
        insensitiveContent,
        culturalContext
      );

      expect(result.success).toBe(true);
      expect(result.validation.overallScore).toBeLessThan(50);
      expect(result.validation.biasDetection.biasDetected).toBe(true);
      expect(result.validation.biasDetection.biasTypes.length).toBeGreaterThan(0);
    });
  });

  describe('optimizePerformance', () => {
    it('should optimize cache performance', async () => {
      const result = await aiOrchestrator.optimizePerformance('cache_optimization');

      expect(result.success).toBe(true);
      expect(result.optimizationResult).toBeDefined();
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.timestamp).toBeDefined();
    });

    it('should optimize query performance', async () => {
      const queryData = {
        query: 'SELECT * FROM patients WHERE country = $1',
        params: ['GH'],
        cacheKey: 'patients_ghana'
      };

      const result = await aiOrchestrator.optimizePerformance('query_optimization', queryData);

      expect(result.success).toBe(true);
      expect(result.optimizationResult).toBeDefined();
      expect(result.responseTime).toBeGreaterThan(0);
    });
  });

  describe('monitorRegionalDeployment', () => {
    it('should monitor regional deployment status', async () => {
      const country = 'GH';

      const result = await aiOrchestrator.monitorRegionalDeployment(country);

      expect(result.success).toBe(true);
      expect(result.deploymentStatus).toBeDefined();
      expect(result.systemHealth).toBeDefined();
      expect(result.timestamp).toBeDefined();
    });
  });
});

// =====================================================
// AUTHENTICATION SERVICE TESTS
// =====================================================

describe('Authentication Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('authenticate', () => {
    it('should authenticate valid user credentials', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'validPassword123',
        clientInfo: {
          userAgent: 'Mozilla/5.0 Test Browser',
          ipAddress: '***********'
        }
      };

      // Mock Supabase auth response
      vi.spyOn(authenticationService['supabase'].auth, 'signInWithPassword')
        .mockResolvedValue({
          data: {
            user: { id: 'test-user-123', email: '<EMAIL>' },
            session: { access_token: 'mock-token' }
          },
          error: null
        });

      // Mock user profile
      vi.spyOn(authenticationService as any, 'getUserProfile')
        .mockResolvedValue({
          id: 'test-user-123',
          email: '<EMAIL>',
          role: 'provider',
          profile: {
            firstName: 'Test',
            lastName: 'Provider',
            country: 'GH',
            language: 'en',
            timezone: 'GMT'
          },
          permissions: [
            { resource: 'patient_data', actions: ['read', 'write'] }
          ],
          lastLogin: new Date(),
          mfaEnabled: false,
          emergencyAccess: false,
          status: 'active'
        });

      const result = await authenticationService.authenticate(request);

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.accessToken).toBeDefined();
      expect(result.refreshToken).toBeDefined();
      expect(result.expiresIn).toBe(8 * 60 * 60); // 8 hours
    });

    it('should handle emergency authentication with bypass', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'emergency',
        emergencyOverride: true,
        clientInfo: {
          userAgent: 'Emergency Browser',
          ipAddress: '*************'
        }
      };

      const startTime = Date.now();
      const result = await authenticationService.authenticate(request);
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(result.user?.role).toBe('emergency');
      expect(result.user?.emergencyAccess).toBe(true);
      expect(result.emergencyBypass).toBe(true);
      expect(result.expiresIn).toBe(2 * 60 * 60); // 2 hours
      expect(responseTime).toBeLessThan(50); // Emergency response time < 50ms
    });

    it('should require MFA when enabled', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'validPassword123'
      };

      // Mock user with MFA enabled
      vi.spyOn(authenticationService['supabase'].auth, 'signInWithPassword')
        .mockResolvedValue({
          data: {
            user: { id: 'mfa-user-123', email: '<EMAIL>' },
            session: { access_token: 'mock-token' }
          },
          error: null
        });

      vi.spyOn(authenticationService as any, 'getUserProfile')
        .mockResolvedValue({
          id: 'mfa-user-123',
          email: '<EMAIL>',
          role: 'admin',
          mfaEnabled: true,
          permissions: [],
          status: 'active'
        });

      const result = await authenticationService.authenticate(request);

      expect(result.success).toBe(false);
      expect(result.mfaRequired).toBe(true);
      expect(result.error).toBe('MFA code required');
    });

    it('should handle invalid credentials', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'wrongPassword'
      };

      vi.spyOn(authenticationService['supabase'].auth, 'signInWithPassword')
        .mockResolvedValue({
          data: { user: null, session: null },
          error: { message: 'Invalid credentials' }
        });

      const result = await authenticationService.authenticate(request);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid credentials');
    });

    it('should implement rate limiting for failed attempts', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'wrongPassword'
      };

      // Mock failed authentication multiple times
      vi.spyOn(authenticationService['supabase'].auth, 'signInWithPassword')
        .mockResolvedValue({
          data: { user: null, session: null },
          error: { message: 'Invalid credentials' }
        });

      // Attempt authentication 6 times (exceeds limit of 5)
      for (let i = 0; i < 6; i++) {
        await authenticationService.authenticate(request);
      }

      const result = await authenticationService.authenticate(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Too many failed attempts');
    });
  });

  describe('validateToken', () => {
    it('should validate valid JWT token', async () => {
      const mockToken = 'valid-jwt-token';

      vi.spyOn(authenticationService['supabase'].auth, 'getUser')
        .mockResolvedValue({
          data: { user: { id: 'test-user-123' } },
          error: null
        });

      vi.spyOn(authenticationService as any, 'getUserProfile')
        .mockResolvedValue({
          id: 'test-user-123',
          email: '<EMAIL>',
          role: 'provider',
          permissions: [
            { resource: 'patient_data', actions: ['read'] }
          ],
          status: 'active'
        });

      const result = await authenticationService.validateToken(mockToken);

      expect(result.valid).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.permissions).toBeDefined();
      expect(result.expiresAt).toBeDefined();
    });

    it('should reject invalid JWT token', async () => {
      const invalidToken = 'invalid-jwt-token';

      vi.spyOn(authenticationService['supabase'].auth, 'getUser')
        .mockResolvedValue({
          data: { user: null },
          error: { message: 'Invalid token' }
        });

      const result = await authenticationService.validateToken(invalidToken);

      expect(result.valid).toBe(false);
      expect(result.error).toBe('Invalid token');
    });
  });

  describe('hasPermission', () => {
    it('should grant permission for authorized resource and action', () => {
      const user = {
        id: 'test-user-123',
        email: '<EMAIL>',
        role: 'provider' as const,
        permissions: [
          { resource: 'patient_data', actions: ['read', 'write'] }
        ],
        emergencyAccess: false,
        profile: {} as any,
        lastLogin: new Date(),
        mfaEnabled: false,
        status: 'active' as const
      };

      const hasPermission = authenticationService.hasPermission(
        user,
        'patient_data',
        'read'
      );

      expect(hasPermission).toBe(true);
    });

    it('should deny permission for unauthorized resource and action', () => {
      const user = {
        id: 'test-user-123',
        email: '<EMAIL>',
        role: 'provider' as const,
        permissions: [
          { resource: 'patient_data', actions: ['read'] }
        ],
        emergencyAccess: false,
        profile: {} as any,
        lastLogin: new Date(),
        mfaEnabled: false,
        status: 'active' as const
      };

      const hasPermission = authenticationService.hasPermission(
        user,
        'admin_panel',
        'write'
      );

      expect(hasPermission).toBe(false);
    });

    it('should grant all permissions to admin users', () => {
      const adminUser = {
        id: 'admin-user-123',
        email: '<EMAIL>',
        role: 'admin' as const,
        permissions: [],
        emergencyAccess: false,
        profile: {} as any,
        lastLogin: new Date(),
        mfaEnabled: false,
        status: 'active' as const
      };

      const hasPermission = authenticationService.hasPermission(
        adminUser,
        'any_resource',
        'any_action'
      );

      expect(hasPermission).toBe(true);
    });

    it('should grant emergency permissions to emergency users', () => {
      const emergencyUser = {
        id: 'emergency-user-123',
        email: '<EMAIL>',
        role: 'emergency' as const,
        permissions: [],
        emergencyAccess: true,
        profile: {} as any,
        lastLogin: new Date(),
        mfaEnabled: false,
        status: 'active' as const
      };

      const hasEmergencyPermission = authenticationService.hasPermission(
        emergencyUser,
        'emergency_protocols',
        'execute'
      );

      const hasPatientPermission = authenticationService.hasPermission(
        emergencyUser,
        'patient_data',
        'read'
      );

      expect(hasEmergencyPermission).toBe(true);
      expect(hasPatientPermission).toBe(true);
    });
  });
});

// =====================================================
// PERFORMANCE VALIDATION TESTS
// =====================================================

describe('Performance Validation', () => {
  describe('Emergency Response Times', () => {
    it('should meet emergency authentication response time target (<50ms)', async () => {
      const request = {
        email: '<EMAIL>',
        password: 'emergency',
        emergencyOverride: true
      };

      const startTime = Date.now();
      const result = await authenticationService.authenticate(request);
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(50);
    });

    it('should meet emergency protocol response time target (<2000ms)', async () => {
      const startTime = Date.now();
      
      // Simulate emergency protocol execution
      const emergencyResponse = await aiOrchestrator.optimizePerformance('emergency_protocol');
      
      const responseTime = Date.now() - startTime;

      expect(emergencyResponse.success).toBe(true);
      expect(responseTime).toBeLessThan(2000);
    });
  });

  describe('Service Integration Performance', () => {
    it('should complete clinical documentation generation within acceptable time', async () => {
      const startTime = Date.now();
      
      const result = await aiOrchestrator.generateClinicalDocumentation(
        'Patient has fever and headache',
        'test-patient',
        'test-provider'
      );
      
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(5000); // 5 seconds max
    });

    it('should complete risk stratification within acceptable time', async () => {
      const patientData = {
        id: 'test-patient',
        demographics: { age: 45, gender: 'female', country: 'GH' },
        medicalHistory: ['hypertension'],
        symptoms: ['headache'],
        behavioralFactors: {
          smokingStatus: 'never',
          physicalActivity: 'moderate',
          dietQuality: 'good',
          adherenceToMedications: 'good'
        },
        socioeconomicFactors: {
          income: 'middle',
          education: 'secondary',
          healthcareAccess: 'moderate'
        },
        environmentalFactors: []
      };

      const startTime = Date.now();
      const result = await aiOrchestrator.performRiskStratification(patientData);
      const responseTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(3000); // 3 seconds max
    });
  });
});

// =====================================================
// ENCRYPTION SERVICE TESTS
// =====================================================

describe('Encryption Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('encryptData and decryptData', () => {
    it('should encrypt and decrypt data successfully', async () => {
      const { encryptionService } = await import('../services/EncryptionService');

      const plaintext = 'Sensitive patient data that needs encryption';

      const encrypted = await encryptionService.encryptData(plaintext);

      expect(encrypted.data).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      expect(encrypted.tag).toBeDefined();
      expect(encrypted.salt).toBeDefined();
      expect(encrypted.keyVersion).toBe(1);
      expect(encrypted.algorithm).toBe('AES-256-GCM');

      const decrypted = await encryptionService.decryptData(encrypted);

      expect(decrypted.success).toBe(true);
      expect(decrypted.data).toBe(plaintext);
      expect(decrypted.keyVersion).toBe(1);
    });

    it('should handle empty plaintext', async () => {
      const { encryptionService } = await import('../services/EncryptionService');

      await expect(
        encryptionService.encryptData('')
      ).rejects.toThrow('Plaintext data is required for encryption');
    });

    it('should handle invalid encrypted data', async () => {
      const { encryptionService } = await import('../services/EncryptionService');

      const invalidEncryptedData = {
        data: 'invalid',
        iv: 'invalid',
        tag: 'invalid',
        salt: 'invalid',
        keyVersion: 1,
        algorithm: 'AES-256-GCM',
        timestamp: new Date().toISOString()
      };

      const result = await encryptionService.decryptData(invalidEncryptedData);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('PHI encryption and decryption', () => {
    it('should encrypt and decrypt PHI with HIPAA compliance', async () => {
      const { encryptionService } = await import('../services/EncryptionService');

      const phi = {
        patientName: 'John Doe',
        dateOfBirth: '1980-01-01',
        medicalRecordNumber: 'MRN123456',
        diagnosis: 'Hypertension'
      };
      const patientId = 'patient-123';
      const requesterId = 'provider-456';

      const encrypted = await encryptionService.encryptPHI(phi, patientId);

      expect(encrypted.data).toBeDefined();
      expect(encrypted.keyVersion).toBeDefined();

      const decrypted = await encryptionService.decryptPHI(encrypted, requesterId);

      expect(decrypted.success).toBe(true);
      expect(decrypted.data).toBeDefined();

      const decryptedPHI = JSON.parse(decrypted.data!);
      expect(decryptedPHI.patientName).toBe(phi.patientName);
      expect(decryptedPHI.diagnosis).toBe(phi.diagnosis);
    });
  });

  describe('hash generation and verification', () => {
    it('should generate and verify hash correctly', async () => {
      const { encryptionService } = await import('../services/EncryptionService');

      const data = 'Important data that needs integrity verification';

      const hashResult = await encryptionService.generateHash(data);

      expect(hashResult.hash).toBeDefined();
      expect(hashResult.algorithm).toBe('SHA-256');
      expect(hashResult.salt).toBeDefined();
      expect(hashResult.timestamp).toBeDefined();

      const isValid = await encryptionService.verifyHash(data, hashResult);
      expect(isValid).toBe(true);

      const isInvalid = await encryptionService.verifyHash('tampered data', hashResult);
      expect(isInvalid).toBe(false);
    });
  });

  describe('key management', () => {
    it('should rotate keys successfully', async () => {
      const { encryptionService } = await import('../services/EncryptionService');

      const initialKeyVersion = encryptionService.getCurrentKeyVersion();

      const rotationResult = await encryptionService.rotateKeys();

      expect(rotationResult.success).toBe(true);
      expect(rotationResult.newKeyVersion).toBe(initialKeyVersion + 1);
      expect(encryptionService.getCurrentKeyVersion()).toBe(initialKeyVersion + 1);
      expect(encryptionService.hasKeyVersion(initialKeyVersion)).toBe(true);
      expect(encryptionService.hasKeyVersion(initialKeyVersion + 1)).toBe(true);
    });
  });
});

// =====================================================
// PERFORMANCE VALIDATION TESTS
// =====================================================

describe('Performance Validation Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateEmergencyPerformance', () => {
    it('should validate emergency performance compliance', async () => {
      const { performanceValidationService } = await import('../services/PerformanceValidationService');

      const result = await performanceValidationService.validateEmergencyPerformance();

      expect(result.emergencyProtocolTime).toBeLessThan(2000);
      expect(result.emergencyAuthTime).toBeLessThan(50);
      expect(result.emergencyNotificationTime).toBeLessThan(500);
      expect(result.compliant).toBe(true);
      expect(result.details).toBeDefined();
      expect(result.details.length).toBeGreaterThan(0);
    });
  });

  describe('runPerformanceTestSuite', () => {
    it('should run comprehensive performance test suite', async () => {
      const { performanceValidationService } = await import('../services/PerformanceValidationService');

      // This is a longer test, so we'll use a shorter version for CI
      const report = await performanceValidationService.runPerformanceTestSuite();

      expect(report.timestamp).toBeDefined();
      expect(report.overallStatus).toMatch(/pass|fail|warning/);
      expect(report.testResults).toBeDefined();
      expect(report.testResults.length).toBeGreaterThan(0);
      expect(report.summary).toBeDefined();
      expect(report.summary.totalTests).toBeGreaterThan(0);
      expect(report.summary.emergencyCompliance).toBeDefined();
      expect(report.summary.authenticationCompliance).toBeDefined();
    }, 30000); // 30 second timeout for performance tests
  });

  describe('recordMetric and getMetrics', () => {
    it('should record and retrieve performance metrics', async () => {
      const { performanceValidationService } = await import('../services/PerformanceValidationService');

      const metric = {
        operation: 'test_operation',
        responseTime: 150,
        timestamp: new Date(),
        success: true,
        target: 200,
        category: 'api' as const
      };

      performanceValidationService.recordMetric(metric);

      const metrics = performanceValidationService.getMetrics('api');
      expect(metrics.length).toBeGreaterThan(0);

      const recordedMetric = metrics.find(m => m.operation === 'test_operation');
      expect(recordedMetric).toBeDefined();
      expect(recordedMetric?.responseTime).toBe(150);
    });
  });

  describe('getPerformanceStatus', () => {
    it('should provide real-time performance status', async () => {
      const { performanceValidationService } = await import('../services/PerformanceValidationService');

      const status = performanceValidationService.getPerformanceStatus();

      expect(status.timestamp).toBeDefined();
      expect(status.totalMetrics).toBeGreaterThanOrEqual(0);
      expect(status.emergencyCompliance).toBeDefined();
      expect(status.authenticationCompliance).toBeDefined();
      expect(status.averageResponseTime).toBeGreaterThanOrEqual(0);
      expect(status.categories).toBeDefined();
      expect(status.categories.emergency).toBeDefined();
      expect(status.categories.authentication).toBeDefined();
      expect(status.categories.api).toBeDefined();
    });
  });
});

// =====================================================
// INTEGRATION PERFORMANCE TESTS
// =====================================================

describe('Integration Performance Tests', () => {
  describe('End-to-End Performance Validation', () => {
    it('should validate complete emergency workflow performance', async () => {
      const startTime = Date.now();

      // Simulate complete emergency workflow
      const { authenticationService } = await import('../services/AuthenticationService');
      const { aiOrchestrator } = await import('../services/aiOrchestrator');

      // Emergency authentication
      const authResult = await authenticationService.authenticate({
        email: '<EMAIL>',
        password: 'emergency',
        emergencyOverride: true
      });

      // Emergency protocol execution
      const protocolResult = await aiOrchestrator.optimizePerformance('emergency_protocol');

      const totalTime = Date.now() - startTime;

      expect(authResult.success).toBe(true);
      expect(protocolResult.success).toBe(true);
      expect(totalTime).toBeLessThan(2000); // Total emergency workflow < 2 seconds
    });

    it('should validate clinical workflow performance', async () => {
      const startTime = Date.now();

      const { aiOrchestrator } = await import('../services/aiOrchestrator');

      // Clinical documentation generation
      const docResult = await aiOrchestrator.generateClinicalDocumentation(
        'Patient has fever and headache',
        'test-patient',
        'test-provider'
      );

      // Risk stratification
      const riskResult = await aiOrchestrator.performRiskStratification({
        id: 'test-patient',
        demographics: { age: 45, gender: 'female', country: 'GH' },
        medicalHistory: ['hypertension'],
        symptoms: ['headache'],
        behavioralFactors: {
          smokingStatus: 'never',
          physicalActivity: 'moderate',
          dietQuality: 'good',
          adherenceToMedications: 'good'
        },
        socioeconomicFactors: {
          income: 'middle',
          education: 'secondary',
          healthcareAccess: 'moderate'
        },
        environmentalFactors: []
      });

      const totalTime = Date.now() - startTime;

      expect(docResult.success).toBe(true);
      expect(riskResult.success).toBe(true);
      expect(totalTime).toBeLessThan(10000); // Clinical workflow < 10 seconds
    });
  });
});
