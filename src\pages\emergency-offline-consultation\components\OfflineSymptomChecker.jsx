import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const OfflineSymptomChecker = ({ onSymptomAssessment }) => {
  const [selectedSymptoms, setSelectedSymptoms] = useState([]);
  const [severityLevel, setSeverityLevel] = useState('');
  const [duration, setDuration] = useState('');

  const commonSymptoms = [
    { id: 'chest_pain', name: 'Chest Pain', severity: 'high', icon: 'Heart' },
    { id: 'difficulty_breathing', name: 'Difficulty Breathing', severity: 'high', icon: 'Lung' },
    { id: 'severe_headache', name: 'Severe Headache', severity: 'medium', icon: 'Brain' },
    { id: 'nausea_vomiting', name: 'Nausea/Vomiting', severity: 'medium', icon: 'AlertTriangle' },
    { id: 'fever', name: 'Fever', severity: 'medium', icon: 'Thermometer' },
    { id: 'dizziness', name: 'Dizziness', severity: 'low', icon: 'RotateCcw' },
    { id: 'fatigue', name: 'Fatigue', severity: 'low', icon: 'Battery' },
    { id: 'abdominal_pain', name: 'Abdominal Pain', severity: 'medium', icon: 'Circle' }
  ];

  const severityOptions = [
    { value: 'mild', label: 'Mild (1-3)', color: 'green' },
    { value: 'moderate', label: 'Moderate (4-6)', color: 'yellow' },
    { value: 'severe', label: 'Severe (7-10)', color: 'red' }
  ];

  const durationOptions = [
    { value: 'minutes', label: 'Minutes' },
    { value: 'hours', label: 'Hours' },
    { value: 'days', label: 'Days' },
    { value: 'weeks', label: 'Weeks' }
  ];

  const handleSymptomToggle = (symptomId) => {
    setSelectedSymptoms(prev => {
      if (prev.includes(symptomId)) {
        return prev.filter(id => id !== symptomId);
      }
      return [...prev, symptomId];
    });
  };

  const handleAssessment = () => {
    const assessment = {
      symptoms: selectedSymptoms,
      severity: severityLevel,
      duration: duration,
      timestamp: new Date()
    };
    onSymptomAssessment?.(assessment);
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="bg-surface rounded-xl shadow-minimal border border-border p-6">
      <div className="flex items-center mb-4">
        <Icon name="Stethoscope" size={24} className="text-primary mr-3" />
        <h3 className="text-xl font-semibold text-text-primary">Offline Symptom Checker</h3>
      </div>

      <div className="space-y-6">
        {/* Symptom Selection */}
        <div>
          <h4 className="text-sm font-medium text-text-primary mb-3">Select your symptoms:</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {commonSymptoms.map((symptom) => (
              <button
                key={symptom.id}
                onClick={() => handleSymptomToggle(symptom.id)}
                className={`p-3 rounded-lg border-2 transition-all duration-200 text-left ${
                  selectedSymptoms.includes(symptom.id)
                    ? 'border-primary bg-primary-50'
                    : `border-border hover:border-primary-200 ${getSeverityColor(symptom.severity)}`
                }`}
              >
                <div className="flex items-center mb-2">
                  <Icon name={symptom.icon} size={16} className="mr-2" />
                  <span className="text-xs font-medium">{symptom.name}</span>
                </div>
                <div className="text-xs opacity-70">
                  {symptom.severity} priority
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Severity Level */}
        <div>
          <h4 className="text-sm font-medium text-text-primary mb-3">Pain/Discomfort Level (1-10):</h4>
          <div className="grid grid-cols-3 gap-3">
            {severityOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setSeverityLevel(option.value)}
                className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                  severityLevel === option.value
                    ? 'border-primary bg-primary-50' :'border-border hover:border-primary-200'
                }`}
              >
                <div className={`w-4 h-4 rounded-full mx-auto mb-2 ${
                  option.color === 'green' ? 'bg-green-500' :
                  option.color === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm font-medium">{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Duration */}
        <div>
          <h4 className="text-sm font-medium text-text-primary mb-3">How long have you experienced this?</h4>
          <div className="grid grid-cols-4 gap-3">
            {durationOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setDuration(option.value)}
                className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                  duration === option.value
                    ? 'border-primary bg-primary-50' :'border-border hover:border-primary-200'
                }`}
              >
                <span className="text-sm font-medium">{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Assessment Button */}
        <div className="pt-4 border-t border-border">
          <Button
            onClick={handleAssessment}
            disabled={selectedSymptoms.length === 0 || !severityLevel || !duration}
            iconName="Search"
            iconPosition="left"
            fullWidth
          >
            Get Offline Assessment
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OfflineSymptomChecker;