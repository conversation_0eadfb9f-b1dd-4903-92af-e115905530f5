/**
 * ERROR BOUNDARIES INDEX
 * 
 * Central export point for all medical error boundary components
 * that provide comprehensive error handling for healthcare applications.
 */

export { default as MedicalErrorBoundary } from './MedicalErrorBoundary';
export { default as EncryptionErrorBoundary } from './EncryptionErrorBoundary';
export { default as NetworkErrorBoundary } from './NetworkErrorBoundary';
export { default as MedicalErrorProvider } from './MedicalErrorProvider';

// Re-export types for convenience
export type {
  MedicalDataError,
  EncryptionError,
  NetworkError
} from '../../types';
