import React from 'react';
import Icon from '../../../components/AppIcon';

const LocationDetection = ({ detectedCountry, onSelect, selectedLanguage }) => {
  if (!detectedCountry) {
    return (
      <div className="bg-surface border border-border rounded-xl p-4 mb-6 flex items-center space-x-3">
        <div className="w-5 h-5 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
        <span className="text-text-secondary">
          {selectedLanguage === 'en' ? 'Detecting your location...' :
           selectedLanguage === 'tw' ? 'Wɔrekyerɛ wo beaeɛ...' :
           selectedLanguage === 'yo' ? 'N ṣe ayẹwo ipo rẹ...' :
           selectedLanguage === 'sw'? 'Kutambua mahali pako...' : 'Besig om jou ligging op te spoor...'}
        </span>
      </div>
    );
  }

  const getCountryDisplayName = () => {
    const countryNames = {
      ghana: {
        en: 'Ghana',
        tw: 'Ghana',
        yo: 'Ghana',
        sw: 'Ghana',
        af: 'Ghana'
      }
    };
    return countryNames[detectedCountry]?.[selectedLanguage] || 'Ghana';
  };

  const getLocalizedText = (key) => {
    const texts = {
      detected: {
        en: 'Detected Location',
        tw: 'Beaeɛ a Wɔkyerɛeɛ',
        yo: 'Ipo Ti A Rii',
        sw: 'Mahali Palipogundulika',
        af: 'Opgespoor Ligging'
      },
      use: {
        en: 'Use This Location',
        tw: 'Fa Beaeɛ Yi Di Dwuma',
        yo: 'Lo Ipo Yii',
        sw: 'Tumia Mahali Hapa',
        af: 'Gebruik Hierdie Ligging'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="bg-success-50 border border-success-200 rounded-xl p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-success-500 rounded-lg flex items-center justify-center">
            <Icon name="MapPin" size={16} color="white" />
          </div>
          <div>
            <p className="text-sm font-medium text-success-700">
              {getLocalizedText('detected')}
            </p>
            <p className="text-success-600 font-semibold flex items-center space-x-2">
              <span>🇬🇭</span>
              <span>{getCountryDisplayName()}</span>
            </p>
          </div>
        </div>
        
        <button
          onClick={() => onSelect(detectedCountry)}
          className="px-4 py-2 bg-success-500 text-white text-sm font-medium rounded-lg hover:bg-success-600 transition-colors duration-200 flex items-center space-x-2"
        >
          <Icon name="Check" size={16} color="white" />
          <span>{getLocalizedText('use')}</span>
        </button>
      </div>
    </div>
  );
};

export default LocationDetection;