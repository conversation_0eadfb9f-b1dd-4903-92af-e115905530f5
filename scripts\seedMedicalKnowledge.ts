/**
 * MEDICAL KNOWLEDGE DATABASE SEEDING SCRIPT
 * 
 * Seeds the vector database with curated medical knowledge sources
 * for the VoiceHealth AI RAG system.
 * 
 * Usage:
 * npm run seed-knowledge
 * or
 * node scripts/seedMedicalKnowledge.js
 */

import { documentIngestionPipeline } from '../src/services/DocumentIngestionPipeline';
import { vectorSearchService } from '../src/services/VectorSearchService';
import { sampleMedicalKnowledge } from '../src/data/sampleMedicalKnowledge';

interface SeedingOptions {
  clearExisting?: boolean;
  batchSize?: number;
  embeddingModel?: 'openai' | 'biobert' | 'clinicalbert';
  validateAfterSeeding?: boolean;
}

class MedicalKnowledgeSeeder {
  private options: SeedingOptions;

  constructor(options: SeedingOptions = {}) {
    this.options = {
      clearExisting: false,
      batchSize: 5,
      embeddingModel: 'openai',
      validateAfterSeeding: true,
      ...options
    };
  }

  /**
   * Main seeding function
   */
  async seedDatabase(): Promise<void> {
    console.log('🌱 Starting medical knowledge database seeding...');
    console.log(`📊 Configuration:`, this.options);

    try {
      // Health check before starting
      await this.performHealthChecks();

      // Clear existing data if requested
      if (this.options.clearExisting) {
        await this.clearExistingData();
      }

      // Seed medical knowledge
      const result = await this.ingestMedicalKnowledge();

      // Validate seeding if requested
      if (this.options.validateAfterSeeding) {
        await this.validateSeeding();
      }

      // Display final statistics
      await this.displayStatistics();

      console.log('✅ Medical knowledge database seeding completed successfully!');

    } catch (error) {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    }
  }

  /**
   * Perform health checks before seeding
   */
  private async performHealthChecks(): Promise<void> {
    console.log('🔍 Performing health checks...');

    // Check vector search service
    const vectorHealth = await vectorSearchService.healthCheck();
    if (!vectorHealth.healthy) {
      throw new Error(`Vector search service unhealthy: ${vectorHealth.details}`);
    }
    console.log('✅ Vector search service healthy');

    // Check document ingestion pipeline
    try {
      await documentIngestionPipeline.getIngestionStats();
      console.log('✅ Document ingestion pipeline healthy');
    } catch (error) {
      throw new Error(`Document ingestion pipeline unhealthy: ${error.message}`);
    }
  }

  /**
   * Clear existing medical knowledge data
   */
  private async clearExistingData(): Promise<void> {
    console.log('🗑️ Clearing existing medical knowledge data...');
    
    // Note: In a real implementation, this would call Supabase to clear tables
    // For now, we'll just log the action
    console.log('⚠️ Clear existing data not implemented - would clear medical_documents and document_embeddings tables');
  }

  /**
   * Ingest medical knowledge documents
   */
  private async ingestMedicalKnowledge(): Promise<any> {
    console.log(`📚 Ingesting ${sampleMedicalKnowledge.length} medical knowledge documents...`);

    const ingestionOptions = {
      chunkSize: 1000,
      chunkOverlap: 200,
      embeddingModel: this.options.embeddingModel,
      skipDuplicates: true,
      validateContent: true,
      extractTerminology: true
    };

    // Process documents in batches
    const batches = this.createBatches(sampleMedicalKnowledge, this.options.batchSize!);
    let totalSuccessful = 0;
    let totalFailed = 0;

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`📦 Processing batch ${i + 1}/${batches.length} (${batch.length} documents)...`);

      try {
        const batchResult = await documentIngestionPipeline.ingestBatch(batch, ingestionOptions);
        
        totalSuccessful += batchResult.successfulIngestions;
        totalFailed += batchResult.failedIngestions;

        console.log(`✅ Batch ${i + 1} completed: ${batchResult.successfulIngestions}/${batch.length} successful`);

        // Display any errors
        batchResult.results.forEach((result, index) => {
          if (!result.success && result.errors.length > 0) {
            console.error(`❌ Document "${batch[index].title}" failed:`, result.errors);
          }
        });

        // Add delay between batches to avoid overwhelming the system
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        console.error(`❌ Batch ${i + 1} failed:`, error);
        totalFailed += batch.length;
      }
    }

    console.log(`📊 Ingestion summary: ${totalSuccessful} successful, ${totalFailed} failed`);

    return {
      totalDocuments: sampleMedicalKnowledge.length,
      successfulIngestions: totalSuccessful,
      failedIngestions: totalFailed
    };
  }

  /**
   * Validate seeding by performing test searches
   */
  private async validateSeeding(): Promise<void> {
    console.log('🔍 Validating seeding with test searches...');

    const testQueries = [
      {
        query: 'cardiovascular disease prevention',
        expectedSpecialty: 'cardiology',
        description: 'Cardiology guidelines search'
      },
      {
        query: 'diabetes management',
        expectedSpecialty: 'endocrinology',
        description: 'Diabetes protocol search'
      },
      {
        query: 'depression screening',
        expectedSpecialty: 'psychiatry',
        description: 'Mental health guidelines search'
      },
      {
        query: 'chest pain evaluation',
        expectedSpecialty: 'emergency_medicine',
        description: 'Emergency protocol search'
      },
      {
        query: 'nutrition guidelines',
        expectedSpecialty: 'nutrition',
        description: 'Nutritional advice search'
      }
    ];

    let validationsPassed = 0;
    let validationsFailed = 0;

    for (const testQuery of testQueries) {
      try {
        console.log(`🔍 Testing: ${testQuery.description}...`);

        const searchResult = await vectorSearchService.searchDocuments(
          {
            query: testQuery.query,
            maxResults: 3,
            minRelevanceScore: 0.5
          },
          'seeding-validation-agent',
          'seeding-validation-session'
        );

        if (searchResult.documents.length > 0) {
          const hasExpectedSpecialty = searchResult.documents.some(doc => 
            doc.specialty.includes(testQuery.expectedSpecialty)
          );

          if (hasExpectedSpecialty) {
            console.log(`✅ ${testQuery.description}: Found ${searchResult.documents.length} relevant documents`);
            validationsPassed++;
          } else {
            console.warn(`⚠️ ${testQuery.description}: No documents found for expected specialty "${testQuery.expectedSpecialty}"`);
            validationsFailed++;
          }
        } else {
          console.warn(`⚠️ ${testQuery.description}: No documents found`);
          validationsFailed++;
        }

      } catch (error) {
        console.error(`❌ ${testQuery.description} failed:`, error);
        validationsFailed++;
      }

      // Small delay between searches
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(`📊 Validation summary: ${validationsPassed}/${testQueries.length} tests passed`);

    if (validationsFailed > 0) {
      console.warn(`⚠️ ${validationsFailed} validation tests failed - check seeding quality`);
    }
  }

  /**
   * Display final statistics
   */
  private async displayStatistics(): Promise<void> {
    console.log('📊 Displaying final database statistics...');

    try {
      const stats = await documentIngestionPipeline.getIngestionStats();
      
      console.log('\n📈 DATABASE STATISTICS:');
      console.log(`Total Documents: ${stats.totalDocuments}`);
      console.log(`Total Embeddings: ${stats.totalEmbeddings}`);
      
      console.log('\n📋 Documents by Type:');
      Object.entries(stats.documentsByType).forEach(([type, count]) => {
        console.log(`  ${type}: ${count}`);
      });
      
      console.log('\n🏥 Documents by Specialty:');
      Object.entries(stats.documentsBySpecialty).forEach(([specialty, count]) => {
        console.log(`  ${specialty}: ${count}`);
      });
      
      console.log('\n📊 Documents by Evidence Level:');
      Object.entries(stats.documentsByEvidenceLevel).forEach(([level, count]) => {
        console.log(`  Level ${level}: ${count}`);
      });

      // Vector search statistics
      const searchStats = vectorSearchService.getSearchStatistics();
      console.log('\n🔍 Vector Search Statistics:');
      console.log(`Cache Size: ${searchStats.cacheSize}`);
      console.log(`Cache Hit Rate: ${(searchStats.cacheHitRate * 100).toFixed(1)}%`);

    } catch (error) {
      console.error('❌ Failed to get statistics:', error);
    }
  }

  /**
   * Create batches from array
   */
  private createBatches<T>(array: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  const options: SeedingOptions = {
    clearExisting: args.includes('--clear'),
    batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '5'),
    embeddingModel: (args.find(arg => arg.startsWith('--model='))?.split('=')[1] as any) || 'openai',
    validateAfterSeeding: !args.includes('--no-validate')
  };

  const seeder = new MedicalKnowledgeSeeder(options);
  await seeder.seedDatabase();
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Seeding script failed:', error);
    process.exit(1);
  });
}

export { MedicalKnowledgeSeeder };
export default MedicalKnowledgeSeeder;
