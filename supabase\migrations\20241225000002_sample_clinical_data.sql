-- Sample Clinical Data for VoiceHealth AI
-- Phase 1 Implementation - Test Data
-- Created: 2025-01-06

-- =====================================================
-- SAMPLE CLINICAL PATHWAYS
-- =====================================================

INSERT INTO clinical_pathways (condition, region, pathway_steps, evidence_level, cultural_adaptations, emergency_protocols, traditional_medicine_considerations, specialty, age_group) VALUES
-- Ghana - Malaria Management
('Malaria', 'GH', 
 '{"steps": [
   {"step": 1, "action": "Rapid diagnostic test (RDT) or microscopy", "rationale": "Confirm malaria diagnosis", "timeframe": "within 30 minutes"},
   {"step": 2, "action": "Assess severity using WHO criteria", "rationale": "Determine treatment approach", "timeframe": "immediately"},
   {"step": 3, "action": "Administer appropriate antimalarial", "rationale": "Evidence-based treatment", "timeframe": "within 1 hour"},
   {"step": 4, "action": "Monitor for complications", "rationale": "Prevent severe malaria", "timeframe": "ongoing"}
 ]}',
 'A',
 '{"adaptations": [
   {"aspect": "Communication", "adaptation": "Explain in Twi if needed", "rationale": "Patient understanding"},
   {"aspect": "Family", "adaptation": "Include family in treatment decisions", "rationale": "Cultural expectation"}
 ]}',
 '{"protocols": [
   {"priority": 1, "action": "Check for danger signs", "timeframe": "immediately"},
   {"priority": 2, "action": "IV artesunate if severe", "timeframe": "within 30 minutes"}
 ]}',
 '{"considerations": [
   {"remedy": "Neem leaves", "relevance": "Traditional antimalarial", "safety": "Generally safe but inform about modern treatment"},
   {"remedy": "Bitter kola", "relevance": "Traditional fever reducer", "safety": "No known interactions with antimalarials"}
 ]}',
 'Infectious Diseases', 'adult'),

-- Kenya - Hypertension Management
('Hypertension', 'KE',
 '{"steps": [
   {"step": 1, "action": "Confirm elevated BP with multiple readings", "rationale": "Accurate diagnosis", "timeframe": "same visit"},
   {"step": 2, "action": "Assess cardiovascular risk factors", "rationale": "Risk stratification", "timeframe": "same visit"},
   {"step": 3, "action": "Lifestyle counseling", "rationale": "Non-pharmacological management", "timeframe": "same visit"},
   {"step": 4, "action": "Consider antihypertensive therapy", "rationale": "Evidence-based treatment", "timeframe": "if indicated"}
 ]}',
 'A',
 '{"adaptations": [
   {"aspect": "Diet", "adaptation": "Discuss traditional Kenyan foods and salt content", "rationale": "Culturally relevant dietary advice"},
   {"aspect": "Exercise", "adaptation": "Suggest culturally appropriate physical activities", "rationale": "Sustainable lifestyle changes"}
 ]}',
 '{"protocols": [
   {"priority": 1, "action": "Check for hypertensive emergency", "timeframe": "immediately"},
   {"priority": 2, "action": "IV antihypertensives if BP >180/120 with symptoms", "timeframe": "within 15 minutes"}
 ]}',
 '{"considerations": [
   {"remedy": "Garlic", "relevance": "Traditional BP management", "safety": "May enhance antihypertensive effects"},
   {"remedy": "Hibiscus tea", "relevance": "Traditional heart health", "safety": "Generally safe, mild BP lowering effect"}
 ]}',
 'Cardiology', 'adult'),

-- Nigeria - Diabetes Management
('Diabetes Type 2', 'NG',
 '{"steps": [
   {"step": 1, "action": "Confirm diagnosis with HbA1c or fasting glucose", "rationale": "Accurate diagnosis", "timeframe": "within 1 week"},
   {"step": 2, "action": "Assess for complications", "rationale": "Comprehensive evaluation", "timeframe": "within 2 weeks"},
   {"step": 3, "action": "Diabetes education", "rationale": "Patient empowerment", "timeframe": "ongoing"},
   {"step": 4, "action": "Initiate metformin if no contraindications", "rationale": "First-line therapy", "timeframe": "same visit"}
 ]}',
 'A',
 '{"adaptations": [
   {"aspect": "Diet", "adaptation": "Discuss traditional Nigerian foods and carbohydrate content", "rationale": "Culturally appropriate nutrition"},
   {"aspect": "Family", "adaptation": "Include family in diabetes education", "rationale": "Support system important in Nigerian culture"}
 ]}',
 '{"protocols": [
   {"priority": 1, "action": "Check for diabetic ketoacidosis", "timeframe": "immediately"},
   {"priority": 2, "action": "IV insulin if DKA confirmed", "timeframe": "within 30 minutes"}
 ]}',
 '{"considerations": [
   {"remedy": "Bitter leaf", "relevance": "Traditional diabetes management", "safety": "May lower blood sugar, monitor glucose levels"},
   {"remedy": "Moringa", "relevance": "Traditional health tonic", "safety": "Generally safe, may have mild glucose-lowering effect"}
 ]}',
 'Endocrinology', 'adult'),

-- South Africa - HIV Management
('HIV', 'ZA',
 '{"steps": [
   {"step": 1, "action": "Confirm HIV diagnosis with rapid test and ELISA", "rationale": "Accurate diagnosis", "timeframe": "same day"},
   {"step": 2, "action": "CD4 count and viral load", "rationale": "Staging and monitoring", "timeframe": "within 1 week"},
   {"step": 3, "action": "Initiate antiretroviral therapy", "rationale": "Immediate treatment recommended", "timeframe": "same day if possible"},
   {"step": 4, "action": "Counseling and support", "rationale": "Adherence and psychosocial support", "timeframe": "ongoing"}
 ]}',
 'A',
 '{"adaptations": [
   {"aspect": "Stigma", "adaptation": "Address HIV stigma sensitively", "rationale": "Reduce discrimination and improve adherence"},
   {"aspect": "Language", "adaptation": "Provide information in Zulu, Xhosa, or Afrikaans as needed", "rationale": "Patient understanding"}
 ]}',
 '{"protocols": [
   {"priority": 1, "action": "Assess for opportunistic infections", "timeframe": "immediately"},
   {"priority": 2, "action": "Treat opportunistic infections urgently", "timeframe": "within 2 hours"}
 ]}',
 '{"considerations": [
   {"remedy": "African potato", "relevance": "Traditional immune support", "safety": "No proven benefit, ensure ART adherence"},
   {"remedy": "Sutherlandia", "relevance": "Traditional immune booster", "safety": "Limited evidence, focus on proven ART"}
 ]}',
 'Infectious Diseases', 'adult');

-- =====================================================
-- SAMPLE DRUG INTERACTIONS
-- =====================================================

INSERT INTO drug_interactions_african (drug_a, drug_b, interaction_type, severity, mechanism, clinical_effects, clinical_management, population_specific, genetic_variants, evidence_level, source_reference) VALUES
-- Modern drug interactions
('Warfarin', 'Aspirin', 'pharmacodynamic', 'severe', 'Additive anticoagulant effects', 'Increased bleeding risk', 'Monitor INR closely, consider dose reduction', false, NULL, 'A', 'WHO Drug Interaction Guidelines'),
('Metformin', 'Contrast media', 'pharmacokinetic', 'moderate', 'Reduced renal clearance', 'Lactic acidosis risk', 'Hold metformin 48h before and after contrast', false, NULL, 'A', 'ADA Guidelines'),

-- Traditional medicine interactions
('Warfarin', NULL, 'pharmacodynamic', 'moderate', 'Enhanced anticoagulation', 'Increased bleeding risk', 'Monitor INR, educate about herbal interactions', true, ARRAY['CYP2C9'], 'B', 'African Pharmacology Journal'),
('Metformin', NULL, 'pharmacokinetic', 'mild', 'Potential glucose lowering', 'Hypoglycemia risk', 'Monitor blood glucose closely', true, NULL, 'C', 'Traditional Medicine Research');

-- Update the traditional remedy interactions
UPDATE drug_interactions_african 
SET traditional_remedy = 'Neem leaves' 
WHERE drug_a = 'Warfarin' AND traditional_remedy IS NULL;

UPDATE drug_interactions_african 
SET traditional_remedy = 'Bitter leaf' 
WHERE drug_a = 'Metformin' AND traditional_remedy IS NULL;

-- =====================================================
-- SAMPLE AFRICAN PHARMACOGENETICS
-- =====================================================

INSERT INTO african_pharmacogenetics (population, gene, variant, frequency, metabolizer_status, clinical_significance, evidence_level, source_study) VALUES
-- West African populations
('West_African', 'CYP2D6', '*4', 0.15, 'poor', 'Reduced metabolism of codeine, tramadol', 'A', 'West African Pharmacogenomics Study 2023'),
('West_African', 'CYP2D6', '*17', 0.25, 'intermediate', 'Reduced enzyme activity', 'A', 'Ghana Pharmacogenetics Research'),
('West_African', 'CYP2C19', '*2', 0.12, 'poor', 'Reduced clopidogrel activation', 'A', 'Nigerian Cardiovascular Genetics'),

-- East African populations
('East_African', 'CYP2D6', '*4', 0.08, 'poor', 'Lower frequency than West Africa', 'A', 'Kenya Pharmacogenetics Study'),
('East_African', 'CYP2D6', '*17', 0.30, 'intermediate', 'Higher frequency of reduced function', 'A', 'East African Genomics Consortium'),
('East_African', 'CYP2C19', '*2', 0.10, 'poor', 'Similar to global populations', 'A', 'Tanzania Medical Genetics'),

-- Southern African populations
('Southern_African', 'CYP2D6', '*4', 0.20, 'poor', 'Intermediate frequency', 'A', 'South African Pharmacogenomics'),
('Southern_African', 'CYP2D6', '*17', 0.18, 'intermediate', 'Moderate frequency', 'A', 'Botswana Genetics Research'),
('Southern_African', 'CYP2C19', '*2', 0.14, 'poor', 'Slightly higher than East Africa', 'A', 'Zimbabwe Pharmacology Study');

-- =====================================================
-- SAMPLE DOSING GUIDELINES
-- =====================================================

INSERT INTO african_dosing_guidelines (medication, population, indication, standard_dose, adjusted_dose, adjustment_factor, adjustment_reason, genetic_basis, age_group, evidence_level, monitoring_requirements) VALUES
-- Warfarin dosing
('Warfarin', 'West_African', 'Anticoagulation', '5mg daily', '2.5mg daily', 0.5, 'Higher sensitivity to warfarin', ARRAY['CYP2C9*2', 'CYP2C9*3'], 'adult', 'A', ARRAY['INR monitoring', 'Bleeding assessment']),
('Warfarin', 'East_African', 'Anticoagulation', '5mg daily', '3mg daily', 0.6, 'Moderate sensitivity increase', ARRAY['CYP2C9*2'], 'adult', 'B', ARRAY['INR monitoring']),

-- Clopidogrel dosing
('Clopidogrel', 'West_African', 'Antiplatelet therapy', '75mg daily', '150mg daily', 2.0, 'Reduced CYP2C19 activity', ARRAY['CYP2C19*2'], 'adult', 'A', ARRAY['Platelet function testing']),
('Clopidogrel', 'East_African', 'Antiplatelet therapy', '75mg daily', '75mg daily', 1.0, 'Standard dosing appropriate', NULL, 'adult', 'B', ARRAY['Clinical monitoring']),

-- Codeine dosing
('Codeine', 'West_African', 'Pain management', '30mg q6h', '15mg q6h', 0.5, 'High frequency of poor metabolizers', ARRAY['CYP2D6*4', 'CYP2D6*17'], 'adult', 'A', ARRAY['Pain assessment', 'Respiratory monitoring']),
('Codeine', 'Southern_African', 'Pain management', '30mg q6h', '20mg q6h', 0.67, 'Moderate frequency of poor metabolizers', ARRAY['CYP2D6*4'], 'adult', 'B', ARRAY['Pain assessment']);

-- =====================================================
-- SAMPLE TRADITIONAL REMEDIES
-- =====================================================

INSERT INTO traditional_remedies (name, scientific_name, local_names, regions, plant_parts_used, active_compounds, traditional_uses, preparation_methods, dosage_guidelines, safety_profile, contraindications, drug_interactions, pregnancy_safety, evidence_level) VALUES
-- West African remedies
('Neem', 'Azadirachta indica', 
 '{"twi": "Neem", "yo": "Dongoyaro", "ha": "Bedi"}',
 ARRAY['GH', 'NG', 'BF'],
 ARRAY['leaves', 'bark', 'seeds'],
 ARRAY['azadirachtin', 'nimbin', 'quercetin'],
 ARRAY['malaria', 'fever', 'skin conditions', 'diabetes'],
 ARRAY['boiled tea', 'powder', 'topical paste'],
 ARRAY['1-2 cups tea daily', '1-2g powder twice daily'],
 '{"summary": "Generally safe for short-term use", "side_effects": ["bitter taste", "nausea if excessive"], "toxicity": "low"}',
 ARRAY['pregnancy', 'breastfeeding', 'autoimmune conditions'],
 '{"interactions": [{"drug": "warfarin", "effect": "may enhance anticoagulation"}, {"drug": "diabetes medications", "effect": "may lower blood sugar"}]}',
 'avoid', 'B'),

('Bitter Kola', 'Garcinia kola',
 '{"twi": "Bese", "yo": "Orogbo", "ig": "Akiilu"}',
 ARRAY['GH', 'NG'],
 ARRAY['seeds', 'nuts'],
 ARRAY['caffeine', 'kolaviron', 'garcinia'],
 ARRAY['cough', 'fever', 'respiratory infections', 'energy'],
 ARRAY['chewed fresh', 'powder in water'],
 ARRAY['1-2 nuts daily', '500mg powder twice daily'],
 '{"summary": "Safe for most adults", "side_effects": ["insomnia if taken late", "stomach upset"], "toxicity": "low"}',
 ARRAY['insomnia', 'anxiety disorders', 'heart conditions'],
 '{"interactions": [{"drug": "stimulants", "effect": "additive effects"}, {"drug": "blood pressure medications", "effect": "may affect BP"}]}',
 'caution', 'C'),

-- East African remedies
('Moringa', 'Moringa oleifera',
 '{"sw": "Mlonge", "ki": "Muringa"}',
 ARRAY['KE', 'TZ', 'UG'],
 ARRAY['leaves', 'pods', 'seeds'],
 ARRAY['vitamin C', 'iron', 'protein', 'quercetin'],
 ARRAY['malnutrition', 'diabetes', 'high blood pressure', 'inflammation'],
 ARRAY['fresh leaves', 'dried powder', 'tea'],
 ARRAY['1-2 cups tea daily', '1-2 tsp powder daily'],
 '{"summary": "Very safe nutritional supplement", "side_effects": ["mild laxative effect"], "toxicity": "very low"}',
 ARRAY['none known'],
 '{"interactions": [{"drug": "diabetes medications", "effect": "may enhance glucose lowering"}, {"drug": "blood pressure medications", "effect": "mild additive effect"}]}',
 'safe', 'B'),

-- Southern African remedies
('African Potato', 'Hypoxis hemerocallidea',
 '{"zu": "Ilabatheka", "af": "Sterretjie", "xh": "Inkomfe"}',
 ARRAY['ZA', 'BW', 'ZW'],
 ARRAY['tuber', 'corm'],
 ARRAY['hypoxoside', 'rooperol', 'sterols'],
 ARRAY['immune support', 'HIV support', 'prostate health', 'general wellness'],
 ARRAY['dried powder', 'tincture', 'capsules'],
 ARRAY['200-400mg daily', '1-2 capsules twice daily'],
 '{"summary": "Generally safe but limited evidence", "side_effects": ["mild GI upset"], "toxicity": "low"}',
 ARRAY['autoimmune conditions', 'immunosuppressive therapy'],
 '{"interactions": [{"drug": "immunosuppressants", "effect": "may counteract effects"}, {"drug": "antiretrovirals", "effect": "no known interactions"}]}',
 'unknown', 'C'),

('Sutherlandia', 'Lessertia frutescens',
 '{"af": "Kankerbos", "zu": "Unwele", "xh": "Umnwele"}',
 ARRAY['ZA'],
 ARRAY['leaves', 'stems'],
 ARRAY['canavanine', 'pinitol', 'GABA'],
 ARRAY['immune support', 'cancer support', 'diabetes', 'stress'],
 ARRAY['tea', 'capsules', 'tincture'],
 ARRAY['1-2 cups tea daily', '200-400mg capsules'],
 '{"summary": "Generally safe but monitor for interactions", "side_effects": ["mild sedation", "GI upset"], "toxicity": "low to moderate"}',
 ARRAY['pregnancy', 'breastfeeding', 'severe liver disease'],
 '{"interactions": [{"drug": "diabetes medications", "effect": "may enhance glucose lowering"}, {"drug": "sedatives", "effect": "additive sedation"}]}',
 'avoid', 'C');

-- =====================================================
-- SAMPLE MEDICAL TERM TRANSLATIONS
-- =====================================================

INSERT INTO medical_term_translations (english_term, category, translations, cultural_context, usage_notes, sensitivity_level, pronunciation_guide) VALUES
-- Basic medical terms
('Blood pressure', 'vital_signs',
 '{"twi": "Mogya mu mframa", "yo": "Eje titun", "sw": "Shinikizo la damu", "zu": "Umfutho wegazi", "af": "Bloeddruk", "ha": "Matsin jini"}',
 'Universal medical concept across African cultures',
 'Explain as the force of blood against blood vessel walls',
 'low',
 '{"twi": "MO-gya mu m-FRA-ma", "sw": "shi-ni-KI-zo la DA-mu"}'),

('Diabetes', 'conditions',
 '{"twi": "Asikyire yare", "yo": "Ito", "sw": "Kisukari", "zu": "Isifo sikashukela", "af": "Suikersiekte", "ha": "Ciwon sukari"}',
 'Well-known condition across African communities',
 'Often understood as sugar disease or sweet urine disease',
 'medium',
 '{"sw": "ki-su-KA-ri", "zu": "i-SI-fo si-ka-shu-KE-la"}'),

('Malaria', 'conditions',
 '{"twi": "Atiridii", "yo": "Iba", "sw": "Malaria", "zu": "Umkhuhlane", "af": "Malaria", "ha": "Zazzabin ciwo"}',
 'Very familiar condition, often with traditional understanding',
 'May be associated with traditional causes like evil spirits',
 'high',
 '{"twi": "a-ti-RI-dii", "sw": "ma-LA-ri-a"}'),

('Heart attack', 'conditions',
 '{"twi": "Akoma mu yare", "yo": "Ikan okan", "sw": "Mshtuko wa moyo", "zu": "Ukuhlaselwa kwenhliziyo", "af": "Hartaanval", "ha": "Bugun zuciya"}',
 'Serious condition requiring immediate family notification',
 'Often requires explanation of heart function and blockage',
 'high',
 '{"sw": "msh-TU-ko wa MO-yo", "zu": "u-ku-hla-SE-lwa kwen-hli-ZI-yo"}'),

('Medicine', 'general',
 '{"twi": "Aduru", "yo": "Oogun", "sw": "Dawa", "zu": "Umuthi", "af": "Medisyne", "ha": "Magani"}',
 'Can refer to both traditional and modern medicine',
 'Clarify whether referring to traditional or modern medicine',
 'medium',
 '{"twi": "a-DU-ru", "sw": "DA-wa", "zu": "u-MU-thi"}');

-- Migration completed successfully
SELECT 'Sample clinical data migration completed successfully' as status;
