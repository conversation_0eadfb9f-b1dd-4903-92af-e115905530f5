import React from 'react';
import { Eye, ChevronDown, Palette, Type, Contrast } from 'lucide-react';

const VisualAccessibilitySection = ({ isExpanded, onToggle, settings, onSettingsChange }) => {
  const themeOptions = [
    { id: 'default', name: 'Default', description: 'Standard light theme' },
    { id: 'high-contrast', name: 'High Contrast', description: 'Enhanced contrast for better visibility' },
    { id: 'dark', name: 'Dark Mode', description: 'Dark theme for reduced eye strain' },
    { id: 'dark-high-contrast', name: 'Dark High Contrast', description: 'Dark theme with maximum contrast' }
  ];

  const fontSizes = [
    { id: 'small', name: 'Small', value: '0.875rem', description: 'Compact text size' },
    { id: 'medium', name: 'Medium', value: '1rem', description: 'Standard text size' },
    { id: 'large', name: 'Large', value: '1.125rem', description: 'Enlarged text for better readability' },
    { id: 'extra-large', name: 'Extra Large', value: '1.25rem', description: 'Maximum text size' }
  ];

  const colorBlindnessOptions = [
    { id: 'none', name: 'No Adjustment', description: 'Standard color scheme' },
    { id: 'protanopia', name: 'Protanopia', description: 'Red-blind color adjustment' },
    { id: 'deuteranopia', name: 'Deuteranopia', description: 'Green-blind color adjustment' },
    { id: 'tritanopia', name: 'Tritanopia', description: 'Blue-blind color adjustment' },
    { id: 'monochrome', name: 'Monochrome', description: 'Grayscale display' }
  ];

  const handleThemeChange = (themeId) => {
    onSettingsChange?.({
      ...settings,
      visualTheme: themeId
    });
  };

  const handleFontSizeChange = (sizeId) => {
    onSettingsChange?.({
      ...settings,
      fontSize: sizeId
    });
  };

  const handleColorBlindnessChange = (optionId) => {
    onSettingsChange?.({
      ...settings,
      colorBlindnessSupport: optionId
    });
  };

  return (
    <div className="bg-surface border border-border rounded-xl overflow-hidden">
      <button
        onClick={onToggle}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-secondary-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-success-50 rounded-lg">
            <Eye className="w-5 h-5 text-success-600" />
          </div>
          <div className="text-left">
            <h3 className="text-lg font-semibold text-text-primary">Visual Accessibility</h3>
            <p className="text-sm text-text-muted">Display and visual enhancement options</p>
          </div>
        </div>
        <ChevronDown className={`w-5 h-5 text-text-muted transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
      </button>

      {isExpanded && (
        <div className="px-6 pb-6 space-y-6">
          {/* Theme Selection */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Visual Theme
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {themeOptions.map((theme) => (
                <button
                  key={theme.id}
                  onClick={() => handleThemeChange(theme.id)}
                  className={`p-4 rounded-lg border text-left transition-all ${
                    settings?.visualTheme === theme.id
                      ? 'border-primary-500 bg-primary-50' :'border-border hover:border-border-active hover:bg-secondary-50'
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <Contrast className="w-4 h-4 text-primary-600" />
                    <h4 className="font-medium text-text-primary">{theme.name}</h4>
                  </div>
                  <p className="text-sm text-text-muted">{theme.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Font Size Control */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Font Size
            </label>
            <div className="space-y-3">
              {fontSizes.map((size) => (
                <button
                  key={size.id}
                  onClick={() => handleFontSizeChange(size.id)}
                  className={`w-full p-4 rounded-lg border text-left transition-all ${
                    settings?.fontSize === size.id
                      ? 'border-primary-500 bg-primary-50' :'border-border hover:border-border-active hover:bg-secondary-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Type className="w-4 h-4 text-primary-600" />
                      <div>
                        <h4 className="font-medium text-text-primary" style={{ fontSize: size.value }}>
                          {size.name}
                        </h4>
                        <p className="text-sm text-text-muted">{size.description}</p>
                      </div>
                    </div>
                    <span className="text-xs text-text-muted bg-secondary-100 px-2 py-1 rounded">
                      {size.value}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Color Blindness Support */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Color Blindness Support
            </label>
            <div className="space-y-3">
              {colorBlindnessOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleColorBlindnessChange(option.id)}
                  className={`w-full p-4 rounded-lg border text-left transition-all ${
                    settings?.colorBlindnessSupport === option.id
                      ? 'border-primary-500 bg-primary-50' :'border-border hover:border-border-active hover:bg-secondary-50'
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <Palette className="w-4 h-4 text-primary-600" />
                    <h4 className="font-medium text-text-primary">{option.name}</h4>
                  </div>
                  <p className="text-sm text-text-muted">{option.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Screen Reader Optimization */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Screen Reader Support</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Enhanced Screen Reader Mode</h5>
                  <p className="text-sm text-text-muted">Optimize interface for screen reader navigation</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    screenReaderOptimized: !settings?.screenReaderOptimized
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.screenReaderOptimized ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.screenReaderOptimized ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Detailed Descriptions</h5>
                  <p className="text-sm text-text-muted">Provide comprehensive alt-text and descriptions</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    detailedDescriptions: !settings?.detailedDescriptions
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.detailedDescriptions ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.detailedDescriptions ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Skip Navigation Links</h5>
                  <p className="text-sm text-text-muted">Add skip links for faster navigation</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    skipNavigation: !settings?.skipNavigation
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.skipNavigation ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.skipNavigation ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Motion and Animation Controls */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Motion & Animation</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Reduce Motion</h5>
                  <p className="text-sm text-text-muted">Minimize animations and transitions</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    reduceMotion: !settings?.reduceMotion
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.reduceMotion ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.reduceMotion ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Auto-play Media</h5>
                  <p className="text-sm text-text-muted">Automatically play videos and audio content</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    autoPlayMedia: !settings?.autoPlayMedia
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.autoPlayMedia ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.autoPlayMedia ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisualAccessibilitySection;