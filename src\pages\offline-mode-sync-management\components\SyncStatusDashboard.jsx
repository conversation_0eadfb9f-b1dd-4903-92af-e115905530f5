import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const SyncStatusDashboard = ({ 
  isOnline = true,
  cachedData = {},
  syncProgress = 0,
  onManualSync = () => {},
  className = ''
}) => {
  const [syncStatus, setSyncStatus] = useState('idle');
  const [lastSyncTime, setLastSyncTime] = useState(null);

  useEffect(() => {
    if (isOnline && syncProgress > 0) {
      setSyncStatus('syncing');
    } else if (isOnline && syncProgress === 100) {
      setSyncStatus('complete');
      setLastSyncTime(new Date());
    } else {
      setSyncStatus('idle');
    }
  }, [isOnline, syncProgress]);

  const formatLastSync = () => {
    if (!lastSyncTime) return 'Never';
    const now = new Date();
    const diff = now - lastSyncTime;
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  const getSyncStatusColor = () => {
    switch (syncStatus) {
      case 'syncing': return 'text-primary-500';
      case 'complete': return 'text-success-500';
      default: return 'text-text-secondary';
    }
  };

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            isOnline ? 'bg-success-50' : 'bg-error-50'
          }`}>
            <Icon 
              name={isOnline ? 'Wifi' : 'WifiOff'} 
              size={20} 
              color={isOnline ? 'var(--color-success-500)' : 'var(--color-error-500)'} 
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Sync Status
            </h3>
            <p className="text-sm text-text-secondary">
              {isOnline ? 'Online' : 'Offline Mode'}
            </p>
          </div>
        </div>
        
        <button
          onClick={onManualSync}
          disabled={!isOnline || syncStatus === 'syncing'}
          className="flex items-center space-x-2 px-4 py-2 bg-primary-50 hover:bg-primary-100 text-primary-600 rounded-lg transition-fast disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Icon 
            name="RefreshCw" 
            size={16} 
            className={syncStatus === 'syncing' ? 'animate-spin' : ''} 
          />
          <span className="text-sm font-medium">Sync Now</span>
        </button>
      </div>

      {/* Sync Progress */}
      {syncStatus === 'syncing' && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-text-secondary">Synchronizing data...</span>
            <span className="text-sm font-medium text-primary-600">{syncProgress}%</span>
          </div>
          <div className="w-full bg-secondary-100 rounded-full h-2">
            <div 
              className="bg-primary-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${syncProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-background rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center">
              <Icon name="Database" size={16} color="var(--color-primary-500)" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Cached Sessions</p>
              <p className="text-lg font-semibold text-text-primary">{cachedData?.sessions || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-background rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-success-50 rounded-lg flex items-center justify-center">
              <Icon name="Users" size={16} color="var(--color-success-500)" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Offline Agents</p>
              <p className="text-lg font-semibold text-text-primary">{cachedData?.agents || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-background rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-warning-50 rounded-lg flex items-center justify-center">
              <Icon name="Upload" size={16} color="var(--color-warning-500)" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Pending Uploads</p>
              <p className="text-lg font-semibold text-text-primary">{cachedData?.pending || 0}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Last Sync Info */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-2">
          <Icon name="Clock" size={14} color="var(--color-text-muted)" />
          <span className="text-text-muted">Last sync: {formatLastSync()}</span>
        </div>
        <div className={`flex items-center space-x-2 ${getSyncStatusColor()}`}>
          <div className={`w-2 h-2 rounded-full ${
            syncStatus === 'syncing' ? 'bg-primary-500 animate-pulse' :
            syncStatus === 'complete' ? 'bg-success-500' : 'bg-text-muted'
          }`}></div>
          <span className="capitalize">{syncStatus}</span>
        </div>
      </div>
    </div>
  );
};

export default SyncStatusDashboard;