import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SessionDetailModal = ({ 
  session, 
  isOpen, 
  onClose, 
  onDownloadTranscript,
  onDownloadAudio,
  onShareSession,
  className = '' 
}) => {
  const [activeTab, setActiveTab] = useState('transcript');
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [audioProgress, setAudioProgress] = useState(0);
  const [currentTimestamp, setCurrentTimestamp] = useState('0:00');
  const [downloadingTranscript, setDownloadingTranscript] = useState(false);
  const [downloadingAudio, setDownloadingAudio] = useState(false);
  const modalRef = useRef(null);

  const tabs = [
    { id: 'transcript', label: 'Transcript', icon: 'FileText' },
    { id: 'summary', label: 'Summary', icon: 'BookOpen' },
    { id: 'insights', label: 'Insights', icon: 'Brain' },
    { id: 'audio', label: 'Audio', icon: 'Volume2' }
  ];

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      modalRef.current?.focus();
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handlePlayPause = () => {
    setIsAudioPlaying(!isAudioPlaying);
    // Simulate audio progress
    if (!isAudioPlaying) {
      const interval = setInterval(() => {
        setAudioProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsAudioPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, 100);
    }
  };

  const handleDownloadTranscript = async () => {
    setDownloadingTranscript(true);
    try {
      await onDownloadTranscript(session.id);
    } finally {
      setDownloadingTranscript(false);
    }
  };

  const handleDownloadAudio = async () => {
    setDownloadingAudio(true);
    try {
      await onDownloadAudio(session.id);
    } finally {
      setDownloadingAudio(false);
    }
  };

  const formatTimestamp = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen || !session) return null;

  return (
    <div 
      className="fixed inset-0 z-300 flex items-center justify-center p-4 bg-black bg-opacity-50"
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
      ref={modalRef}
    >
      <div className={`bg-surface rounded-xl shadow-floating max-w-4xl w-full max-h-[90vh] overflow-hidden ${className}`}>
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h2 className="text-xl font-semibold text-text-primary font-heading">
                {session.title}
              </h2>
              <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${
                session.status === 'completed' ? 'bg-success-50 text-success-600 border-success-200' :
                session.status === 'active'? 'bg-primary-50 text-primary-600 border-primary-200' : 'bg-secondary-50 text-secondary-600 border-secondary-200'
              }`}>
                <Icon name={session.status === 'completed' ? 'CheckCircle' : session.status === 'active' ? 'Play' : 'Clock'} size={12} />
                {session.status}
              </span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-text-secondary">
              <span className="flex items-center space-x-1">
                <Icon name="Calendar" size={14} />
                <span>{session.date}</span>
              </span>
              <span className="flex items-center space-x-1">
                <Icon name="Clock" size={14} />
                <span>{session.duration}</span>
              </span>
              <span className="flex items-center space-x-1">
                <Icon name="Users" size={14} />
                <span>{session.agents?.length || 0} agents</span>
              </span>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 hover:bg-secondary-50 rounded-lg transition-fast"
            title="Close"
          >
            <Icon name="X" size={20} color="var(--color-text-secondary)" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-border">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 font-medium text-sm transition-fast ${
                activeTab === tab.id
                  ? 'text-primary-600 border-b-2 border-primary-500 bg-primary-50' :'text-text-secondary hover:text-text-primary hover:bg-secondary-50'
              }`}
            >
              <Icon name={tab.icon} size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'transcript' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-text-primary">Session Transcript</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadTranscript}
                  loading={downloadingTranscript}
                  iconName="Download"
                  iconPosition="left"
                >
                  Download PDF
                </Button>
              </div>
              
              <div className="space-y-4">
                {session.transcript?.map((entry, index) => (
                  <div key={index} className="flex space-x-4">
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        entry.speaker === 'Patient' ? 'bg-accent-50' : 'bg-primary-50'
                      }`}>
                        <Icon 
                          name={entry.speaker === 'Patient' ? 'User' : 'Bot'} 
                          size={14} 
                          color={entry.speaker === 'Patient' ? 'var(--color-accent)' : 'var(--color-primary)'}
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-text-primary text-sm">
                          {entry.speaker}
                        </span>
                        <span className="text-xs text-text-secondary">
                          {entry.timestamp}
                        </span>
                      </div>
                      <p className="text-text-secondary text-sm leading-relaxed">
                        {entry.content}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'summary' && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-text-primary mb-3">Consultation Summary</h3>
                <div className="bg-secondary-50 rounded-lg p-4">
                  <p className="text-text-secondary leading-relaxed">
                    {session.detailedSummary}
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-text-primary mb-3">Key Recommendations</h4>
                <ul className="space-y-2">
                  {session.recommendations?.map((rec, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <Icon name="CheckCircle" size={16} color="var(--color-success)" className="mt-0.5 flex-shrink-0" />
                      <span className="text-text-secondary text-sm">{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-text-primary mb-3">Follow-up Actions</h4>
                <ul className="space-y-2">
                  {session.followUpActions?.map((action, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <Icon name="ArrowRight" size={16} color="var(--color-primary)" className="mt-0.5 flex-shrink-0" />
                      <span className="text-text-secondary text-sm">{action}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'insights' && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-text-primary mb-4">AI Insights & Analysis</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-success-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Icon name="TrendingUp" size={16} color="var(--color-success)" />
                      <span className="font-medium text-success-600">Health Score</span>
                    </div>
                    <div className="text-2xl font-bold text-success-600">{session.insights?.healthScore || 85}/100</div>
                  </div>
                  
                  <div className="bg-warning-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Icon name="AlertTriangle" size={16} color="var(--color-warning)" />
                      <span className="font-medium text-warning-600">Risk Factors</span>
                    </div>
                    <div className="text-2xl font-bold text-warning-600">{session.insights?.riskFactors || 2}</div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-text-primary mb-2">Detected Patterns</h4>
                    <ul className="space-y-1">
                      {session.insights?.patterns?.map((pattern, index) => (
                        <li key={index} className="text-text-secondary text-sm">• {pattern}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-text-primary mb-2">Recommendations Priority</h4>
                    <div className="space-y-2">
                      {session.insights?.priorityRecommendations?.map((rec, index) => (
                        <div key={index} className="flex items-center space-x-3 p-2 bg-secondary-50 rounded-lg">
                          <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                            rec.priority === 'high' ? 'bg-error-500 text-white' :
                            rec.priority === 'medium'? 'bg-warning-500 text-white' : 'bg-success-500 text-white'
                          }`}>
                            {index + 1}
                          </span>
                          <span className="text-text-secondary text-sm">{rec.text}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'audio' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-text-primary">Audio Playback</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadAudio}
                  loading={downloadingAudio}
                  iconName="Download"
                  iconPosition="left"
                >
                  Download Audio
                </Button>
              </div>
              
              {/* Audio Player */}
              <div className="bg-secondary-50 rounded-lg p-6">
                <div className="flex items-center justify-center space-x-4 mb-4">
                  <Button
                    variant="primary"
                    onClick={handlePlayPause}
                    iconName={isAudioPlaying ? "Pause" : "Play"}
                    className="w-12 h-12 rounded-full"
                  />
                  <div className="flex-1">
                    <div className="flex justify-between text-sm text-text-secondary mb-1">
                      <span>{currentTimestamp}</span>
                      <span>{session.duration}</span>
                    </div>
                    <div className="w-full bg-secondary-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full transition-all duration-100"
                        style={{ width: `${audioProgress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                
                {/* Audio Waveform */}
                {isAudioPlaying && (
                  <div className="flex items-center justify-center space-x-1 h-16">
                    {[...Array(30)].map((_, i) => (
                      <div
                        key={i}
                        className="voice-waveform-bar bg-primary-500"
                        style={{
                          height: `${Math.random() * 40 + 10}px`,
                          animationDelay: `${i * 0.05}s`
                        }}
                      ></div>
                    ))}
                  </div>
                )}
                
                <div className="text-center text-sm text-text-secondary mt-4">
                  {isAudioPlaying ? 'Playing consultation audio...' : 'Click play to listen to the consultation'}
                </div>
              </div>
              
              {/* Agent Voice Profiles */}
              <div>
                <h4 className="font-medium text-text-primary mb-3">Agent Voice Profiles</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {session.agents?.map((agent, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-surface border border-border rounded-lg">
                      <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
                        <Icon name="User" size={16} color="white" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-text-primary text-sm">{agent.name}</div>
                        <div className="text-xs text-text-secondary">{agent.voiceProfile}</div>
                      </div>
                      <button className="p-2 hover:bg-secondary-50 rounded-lg transition-fast">
                        <Icon name="Play" size={14} color="var(--color-text-secondary)" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border bg-secondary-50">
          <div className="flex items-center space-x-4 text-sm text-text-secondary">
            <span>Session ID: {session.id}</span>
            <span>•</span>
            <span>Created: {session.createdAt}</span>
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              onClick={() => onShareSession(session.id)}
              iconName="Share2"
              iconPosition="left"
            >
              Share
            </Button>
            <Button
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionDetailModal;