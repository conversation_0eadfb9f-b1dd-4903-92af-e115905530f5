/**
 * EMERGENCY STOP MECHANISM TESTS
 * 
 * Tests for the critical emergency stop functionality across all
 * audio consultation interfaces with focus on patient safety.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

// Mock components and services
vi.mock('../utils/supabaseClient', () => ({
  supabase: {
    auth: {
      getSession: vi.fn().mockResolvedValue({
        data: { session: { access_token: 'mock-token' } }
      })
    },
    from: vi.fn().mockReturnValue({
      insert: vi.fn().mockResolvedValue({ data: {}, error: null })
    })
  }
}));

vi.mock('../utils/auditLogger', () => ({
  default: {
    logEmergencyAccess: vi.fn().mockResolvedValue(true)
  }
}));

// Mock audio storage service
vi.mock('../utils/audioStorageService', () => ({
  default: {
    storeAudioMessage: vi.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id',
      encrypted: true
    })
  }
}));

describe('Emergency Stop Mechanism', () => {
  let mockUser;
  let mockSessionData;

  beforeEach(() => {
    mockUser = {
      id: 'test-user-id',
      user_metadata: { role: 'patient' },
      full_name: 'Test Patient'
    };

    mockSessionData = {
      id: 'test-session-id',
      duration: 0,
      transcriptMessages: [],
      audioSessions: [],
      recommendations: []
    };

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Enhanced Voice Consultation Interface', () => {
    it('should display emergency stop button during active session', async () => {
      const EnhancedVoiceConsultationInterface = await import(
        '../pages/enhanced-voice-consultation-interface/index.jsx'
      ).then(module => module.default);

      render(
        <BrowserRouter>
          <EnhancedVoiceConsultationInterface />
        </BrowserRouter>
      );

      // Start a session first
      const startButton = screen.getByText(/Start Enhanced Consultation/i);
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(screen.getByText('EMERGENCY STOP')).toBeInTheDocument();
      });
    });

    it('should trigger emergency stop within 2 seconds', async () => {
      const startTime = Date.now();
      
      const EnhancedVoiceConsultationInterface = await import(
        '../pages/enhanced-voice-consultation-interface/index.jsx'
      ).then(module => module.default);

      render(
        <BrowserRouter>
          <EnhancedVoiceConsultationInterface />
        </BrowserRouter>
      );

      // Start session and trigger emergency stop
      const startButton = screen.getByText(/Start Enhanced Consultation/i);
      fireEvent.click(startButton);

      await waitFor(() => {
        const emergencyButton = screen.getByText('EMERGENCY STOP');
        fireEvent.click(emergencyButton);
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Verify response time is under 2 seconds
      expect(responseTime).toBeLessThan(2000);
    });

    it('should show emergency stop status after activation', async () => {
      const EnhancedVoiceConsultationInterface = await import(
        '../pages/enhanced-voice-consultation-interface/index.jsx'
      ).then(module => module.default);

      render(
        <BrowserRouter>
          <EnhancedVoiceConsultationInterface />
        </BrowserRouter>
      );

      // Start session and trigger emergency stop
      const startButton = screen.getByText(/Start Enhanced Consultation/i);
      fireEvent.click(startButton);

      await waitFor(() => {
        const emergencyButton = screen.getByText('EMERGENCY STOP');
        fireEvent.click(emergencyButton);
      });

      await waitFor(() => {
        expect(screen.getByText('Emergency Stop Activated')).toBeInTheDocument();
        expect(screen.getByText(/USER_INITIATED/i)).toBeInTheDocument();
      });
    });
  });

  describe('Real-time Multi-agent Collaboration', () => {
    it('should handle emergency stop in multi-agent environment', async () => {
      const RealTimeMultiAgentCollaboration = await import(
        '../pages/real-time-multi-agent-collaboration/index.jsx'
      ).then(module => module.default);

      render(
        <BrowserRouter>
          <RealTimeMultiAgentCollaboration />
        </BrowserRouter>
      );

      // Mock emergency stop function
      const mockEmergencyStop = vi.fn();
      
      // Simulate emergency stop trigger
      await mockEmergencyStop('medical_emergency');

      expect(mockEmergencyStop).toHaveBeenCalledWith('medical_emergency');
    });
  });

  describe('PWA Offline Mode', () => {
    it('should work in offline mode', async () => {
      const VoiceConsultationPWA = await import(
        '../components/VoiceConsultationPWA.jsx'
      ).then(module => module.default);

      // Mock offline state
      const mockPWA = {
        isOnline: false,
        storage: {
          store: vi.fn().mockResolvedValue(true)
        }
      };

      render(
        <VoiceConsultationPWA 
          sessionId="test-session"
          patientId="test-patient"
        />
      );

      // Emergency stop should work even offline
      const mockEmergencyStop = vi.fn();
      await mockEmergencyStop('user_initiated');

      expect(mockEmergencyStop).toHaveBeenCalled();
    });
  });

  describe('Audit Logging', () => {
    it('should log emergency stop events', async () => {
      const auditLogger = await import('../utils/auditLogger').then(m => m.default);
      
      // Simulate emergency stop logging
      await auditLogger.logEmergencyAccess(
        'test-user-id',
        'test-user-id',
        'Emergency stop triggered: user_initiated',
        {
          session_id: 'test-session-id',
          stop_reason: 'user_initiated',
          timestamp: new Date().toISOString(),
          response_time: '< 2 seconds'
        }
      );

      expect(auditLogger.logEmergencyAccess).toHaveBeenCalledWith(
        'test-user-id',
        'test-user-id',
        'Emergency stop triggered: user_initiated',
        expect.objectContaining({
          session_id: 'test-session-id',
          stop_reason: 'user_initiated',
          response_time: '< 2 seconds'
        })
      );
    });
  });

  describe('RBAC Permissions', () => {
    it('should allow patients to emergency stop their own consultations', () => {
      const hasPermission = (userRole, resource, action, context) => {
        if (userRole === 'patient' && 
            resource === 'audio_consultations' && 
            action === 'emergency_stop' &&
            context.patient_id === context.user_id) {
          return true;
        }
        return false;
      };

      const result = hasPermission('patient', 'audio_consultations', 'emergency_stop', {
        user_id: 'test-user-id',
        patient_id: 'test-user-id'
      });

      expect(result).toBe(true);
    });

    it('should allow emergency responders to stop any consultation', () => {
      const hasPermission = (userRole, resource, action) => {
        if (userRole === 'emergency_responder' && 
            resource === 'audio_consultations' && 
            action === 'emergency_stop') {
          return true;
        }
        return false;
      };

      const result = hasPermission('emergency_responder', 'audio_consultations', 'emergency_stop');

      expect(result).toBe(true);
    });
  });

  describe('Performance Requirements', () => {
    it('should complete emergency stop within 2 seconds', async () => {
      const startTime = performance.now();
      
      // Simulate emergency stop operations
      const emergencyStopOperations = async () => {
        // Stop recording
        await new Promise(resolve => setTimeout(resolve, 10));
        
        // Save session data
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // Log audit event
        await new Promise(resolve => setTimeout(resolve, 30));
        
        // Update UI state
        await new Promise(resolve => setTimeout(resolve, 10));
      };

      await emergencyStopOperations();
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(2000); // Must complete within 2 seconds
    });
  });
});
