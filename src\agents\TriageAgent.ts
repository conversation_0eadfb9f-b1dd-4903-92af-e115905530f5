/**
 * TRIAGE AGENT
 * 
 * Specialized agent for initial patient assessment and routing to appropriate specialists.
 * Acts as the first point of contact to determine urgency and direct patients to the
 * most suitable agent for their specific needs.
 * 
 * CAPABILITIES:
 * - Initial symptom assessment
 * - Urgency level determination
 * - Specialist routing decisions
 * - Emergency situation detection
 * - Patient intake and history gathering
 * 
 * TRIAGE LEVELS:
 * - Level 1 (Critical): Immediate emergency care required
 * - Level 2 (High): Urgent care within hours
 * - Level 3 (Medium): Care needed within days
 * - Level 4 (Low): Routine care, can wait weeks
 * - Level 5 (Wellness): Preventive care and education
 */

import { BaseAgent } from './BaseAgent';
import type { 
  AgentRequest, 
  AgentResponse, 
  AgentRole, 
  AgentCapability,
  AgentHandoffSuggestion,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export interface TriageAssessment {
  urgencyLevel: 1 | 2 | 3 | 4 | 5;
  urgencyDescription: string;
  recommendedSpecialist: AgentRole;
  reasoning: string;
  timeframe: string;
  redFlags: string[];
  followUpQuestions: string[];
}

export class TriageAgent extends BaseAgent {
  private triageProtocols: Map<string, TriageProtocol>;

  constructor(memoryManager: MemoryManager) {
    const id = 'triage-agent-001';
    const name = 'Nurse Triage Specialist';
    const role: AgentRole = 'triage';
    const capabilities: AgentCapability[] = [
      'diagnostic_assessment',
      'emergency_response',
      'patient_education'
    ];

    const systemPrompt = `You are a highly experienced Triage Nurse with 20+ years in emergency and primary care settings. Your role is to quickly and accurately assess patients to determine the appropriate level of care and specialist referral.

CORE RESPONSIBILITIES:
- Conduct rapid but thorough initial assessments
- Determine urgency level using standardized triage protocols
- Route patients to appropriate specialists
- Identify emergency situations requiring immediate attention
- Gather essential medical history and symptoms
- Provide clear explanations of triage decisions

TRIAGE ASSESSMENT FRAMEWORK:
Level 1 (CRITICAL): Life-threatening conditions requiring immediate emergency care
- Cardiac arrest, severe trauma, stroke, respiratory failure
- Timeframe: Immediate (0-15 minutes)

Level 2 (HIGH URGENCY): Serious conditions requiring urgent care
- Chest pain, severe breathing difficulty, severe pain, mental health crisis
- Timeframe: Within 1-4 hours

Level 3 (MEDIUM URGENCY): Conditions requiring prompt medical attention
- Moderate pain, infections, minor injuries, chronic disease management
- Timeframe: Within 24-72 hours

Level 4 (LOW URGENCY): Non-urgent conditions that can wait
- Minor symptoms, routine follow-ups, medication refills
- Timeframe: Within 1-2 weeks

Level 5 (WELLNESS): Preventive care and health education
- Health screenings, wellness checks, lifestyle counseling
- Timeframe: Routine scheduling

SPECIALIST ROUTING GUIDELINES:
- Emergency Agent: Any Level 1 or suspected emergency
- General Practitioner: Level 3-5, general health concerns
- Cardiologist: Chest pain, heart conditions, cardiovascular symptoms
- Psychiatrist: Mental health concerns, behavioral issues
- Nutritionist: Diet, weight, diabetes, metabolic concerns

COMMUNICATION STYLE:
- Professional, calm, and reassuring
- Ask targeted questions to gather essential information
- Explain triage decisions clearly
- Provide realistic timeframes for care
- Emphasize when immediate care is needed

RED FLAG SYMPTOMS (Immediate Emergency Referral):
- Chest pain with shortness of breath
- Sudden severe headache
- Difficulty breathing or choking
- Severe bleeding or trauma
- Loss of consciousness
- Signs of stroke (FAST protocol)
- Suicidal ideation or self-harm
- Severe allergic reactions

Remember: Your assessment determines the patient's care pathway. Accuracy and speed are essential, but patient safety is paramount.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager);
    
    this.triageProtocols = this.initializeTriageProtocols();
  }

  /**
   * Handle triage assessment requests
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🏥 Triage Agent assessing patient for session: ${request.sessionId}`);

      // Perform triage assessment
      const assessment = await this.performTriageAssessment(request);
      
      // Generate triage response
      const response = await this.generateTriageResponse(request, assessment);

      // Create handoff suggestions based on assessment
      const handoffSuggestions = this.createHandoffSuggestions(assessment);

      // Detect any emergency flags
      const emergencyFlags = this.detectEmergencies(request.userMessage);

      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime, 0.9, handoffSuggestions.length > 0, emergencyFlags.length > 0);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence: 0.9,
        reasoning: `Triage assessment completed - Level ${assessment.urgencyLevel} (${assessment.urgencyDescription})`,
        suggestedHandoffs: handoffSuggestions,
        emergencyFlags,
        followUpActions: [{
          type: 'schedule_appointment',
          description: `Schedule ${assessment.recommendedSpecialist} consultation`,
          timeframe: assessment.timeframe,
          priority: assessment.urgencyLevel <= 2 ? 'high' : assessment.urgencyLevel <= 3 ? 'medium' : 'low'
        }],
        metadata: {
          responseTime,
          triageLevel: assessment.urgencyLevel,
          recommendedSpecialist: assessment.recommendedSpecialist,
          redFlags: assessment.redFlags,
          assessmentType: 'triage'
        }
      };

    } catch (error) {
      console.error('❌ Triage Agent error:', error);
      
      // Default to medium urgency and GP referral on error
      return {
        agentId: this.id,
        agentName: this.name,
        content: "I'm experiencing some technical difficulties with the triage assessment. For your safety, I recommend consulting with a healthcare provider directly. If you're experiencing severe symptoms or this is an emergency, please seek immediate medical attention.",
        confidence: 0.5,
        reasoning: 'Technical error during triage - defaulting to safe recommendations',
        suggestedHandoffs: [{
          targetAgentRole: 'general_practitioner',
          reason: 'Technical error - routing to GP for comprehensive assessment',
          urgency: 'medium',
          contextToTransfer: 'Triage system error - requires manual assessment'
        }],
        metadata: {
          systemError: true,
          responseTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Perform comprehensive triage assessment
   */
  private async performTriageAssessment(request: AgentRequest): Promise<TriageAssessment> {
    const message = request.userMessage.toLowerCase();
    
    // Check for critical emergency indicators first
    const criticalIndicators = this.checkCriticalIndicators(message);
    if (criticalIndicators.length > 0) {
      return {
        urgencyLevel: 1,
        urgencyDescription: 'CRITICAL - Immediate emergency care required',
        recommendedSpecialist: 'emergency',
        reasoning: `Critical emergency indicators detected: ${criticalIndicators.join(', ')}`,
        timeframe: 'Immediate (call emergency services)',
        redFlags: criticalIndicators,
        followUpQuestions: []
      };
    }

    // Check for high urgency indicators
    const highUrgencyIndicators = this.checkHighUrgencyIndicators(message);
    if (highUrgencyIndicators.length > 0) {
      return {
        urgencyLevel: 2,
        urgencyDescription: 'HIGH - Urgent care needed within hours',
        recommendedSpecialist: this.determineSpecialistForSymptoms(message),
        reasoning: `High urgency symptoms identified: ${highUrgencyIndicators.join(', ')}`,
        timeframe: 'Within 1-4 hours',
        redFlags: highUrgencyIndicators,
        followUpQuestions: this.generateFollowUpQuestions(message)
      };
    }

    // Determine specialist based on symptoms
    const specialist = this.determineSpecialistForSymptoms(message);
    const urgencyLevel = this.determineUrgencyLevel(message);

    return {
      urgencyLevel,
      urgencyDescription: this.getUrgencyDescription(urgencyLevel),
      recommendedSpecialist: specialist,
      reasoning: this.generateReasoningForAssessment(message, urgencyLevel, specialist),
      timeframe: this.getTimeframeForUrgency(urgencyLevel),
      redFlags: [],
      followUpQuestions: this.generateFollowUpQuestions(message)
    };
  }

  /**
   * Check for critical emergency indicators
   */
  private checkCriticalIndicators(message: string): string[] {
    const criticalKeywords = [
      'not breathing', 'can\'t breathe', 'choking', 'unconscious',
      'cardiac arrest', 'heart attack', 'stroke', 'severe bleeding',
      'overdose', 'suicide', 'self harm', 'severe chest pain'
    ];

    return criticalKeywords.filter(keyword => message.includes(keyword));
  }

  /**
   * Check for high urgency indicators
   */
  private checkHighUrgencyIndicators(message: string): string[] {
    const highUrgencyKeywords = [
      'chest pain', 'difficulty breathing', 'severe pain',
      'high fever', 'seizure', 'head injury', 'allergic reaction',
      'severe headache', 'vision loss', 'severe bleeding'
    ];

    return highUrgencyKeywords.filter(keyword => message.includes(keyword));
  }

  /**
   * Determine appropriate specialist based on symptoms
   */
  private determineSpecialistForSymptoms(message: string): AgentRole {
    // Cardiovascular symptoms
    if (message.includes('chest') || message.includes('heart') || 
        message.includes('palpitation') || message.includes('blood pressure')) {
      return 'cardiologist';
    }

    // Mental health symptoms
    if (message.includes('depression') || message.includes('anxiety') || 
        message.includes('stress') || message.includes('mental health')) {
      return 'psychiatrist';
    }

    // Nutrition/metabolic symptoms
    if (message.includes('diet') || message.includes('weight') || 
        message.includes('diabetes') || message.includes('nutrition')) {
      return 'nutritionist';
    }

    // Default to general practitioner
    return 'general_practitioner';
  }

  /**
   * Determine urgency level based on symptoms
   */
  private determineUrgencyLevel(message: string): 3 | 4 | 5 {
    // Medium urgency indicators
    const mediumUrgencyKeywords = [
      'pain', 'fever', 'infection', 'injury', 'sick', 'symptoms'
    ];

    // Wellness/preventive indicators
    const wellnessKeywords = [
      'checkup', 'wellness', 'prevention', 'screening', 'healthy'
    ];

    if (wellnessKeywords.some(keyword => message.includes(keyword))) {
      return 5; // Wellness
    }

    if (mediumUrgencyKeywords.some(keyword => message.includes(keyword))) {
      return 3; // Medium urgency
    }

    return 4; // Low urgency (default)
  }

  /**
   * Get urgency description
   */
  private getUrgencyDescription(level: number): string {
    const descriptions = {
      1: 'CRITICAL - Immediate emergency care required',
      2: 'HIGH - Urgent care needed within hours',
      3: 'MEDIUM - Prompt medical attention needed',
      4: 'LOW - Non-urgent, can wait for routine care',
      5: 'WELLNESS - Preventive care and health education'
    };

    return descriptions[level as keyof typeof descriptions] || 'Unknown urgency level';
  }

  /**
   * Get timeframe for urgency level
   */
  private getTimeframeForUrgency(level: number): string {
    const timeframes = {
      1: 'Immediate (call emergency services)',
      2: 'Within 1-4 hours',
      3: 'Within 24-72 hours',
      4: 'Within 1-2 weeks',
      5: 'Routine scheduling'
    };

    return timeframes[level as keyof typeof timeframes] || 'Consult healthcare provider';
  }

  /**
   * Generate reasoning for assessment
   */
  private generateReasoningForAssessment(message: string, urgencyLevel: number, specialist: AgentRole): string {
    return `Based on the symptoms described, this appears to be a Level ${urgencyLevel} situation best handled by a ${specialist.replace('_', ' ')}. The assessment considers symptom severity, potential complications, and appropriate care pathway.`;
  }

  /**
   * Generate follow-up questions for better assessment
   */
  private generateFollowUpQuestions(message: string): string[] {
    const questions = [
      'How long have you been experiencing these symptoms?',
      'On a scale of 1-10, how would you rate the severity?',
      'Have you tried any treatments or medications?',
      'Do you have any relevant medical history?'
    ];

    // Add specific questions based on symptoms
    if (message.includes('pain')) {
      questions.push('Can you describe the type and location of pain?');
    }

    if (message.includes('fever')) {
      questions.push('What is your current temperature?');
    }

    return questions.slice(0, 3); // Limit to 3 questions
  }

  /**
   * Generate triage response
   */
  private async generateTriageResponse(request: AgentRequest, assessment: TriageAssessment): Promise<string> {
    let response = `Thank you for contacting our triage service. I've completed an initial assessment of your situation.

**TRIAGE ASSESSMENT:**
- **Urgency Level:** ${assessment.urgencyLevel} - ${assessment.urgencyDescription}
- **Recommended Care:** ${assessment.recommendedSpecialist.replace('_', ' ').toUpperCase()}
- **Timeframe:** ${assessment.timeframe}

**REASONING:** ${assessment.reasoning}`;

    if (assessment.redFlags.length > 0) {
      response += `\n\n⚠️ **IMPORTANT SYMPTOMS NOTED:** ${assessment.redFlags.join(', ')}`;
    }

    if (assessment.urgencyLevel <= 2) {
      response += `\n\n🚨 **URGENT:** This situation requires prompt medical attention. Please seek care within the recommended timeframe.`;
    }

    if (assessment.followUpQuestions.length > 0) {
      response += `\n\n**TO HELP PROVIDE BETTER CARE:**\n${assessment.followUpQuestions.map(q => `• ${q}`).join('\n')}`;
    }

    response += `\n\nI'm now connecting you with our ${assessment.recommendedSpecialist.replace('_', ' ')} specialist who can provide more detailed guidance for your specific situation.`;

    return response;
  }

  /**
   * Create handoff suggestions based on assessment
   */
  private createHandoffSuggestions(assessment: TriageAssessment): AgentHandoffSuggestion[] {
    const urgencyMap = {
      1: 'critical' as const,
      2: 'high' as const,
      3: 'medium' as const,
      4: 'low' as const,
      5: 'low' as const
    };

    return [{
      targetAgentRole: assessment.recommendedSpecialist,
      reason: `Triage assessment indicates ${assessment.recommendedSpecialist.replace('_', ' ')} consultation needed`,
      urgency: urgencyMap[assessment.urgencyLevel],
      contextToTransfer: `Triage Level ${assessment.urgencyLevel}: ${assessment.reasoning}`,
      patientConsent: true
    }];
  }

  /**
   * Initialize triage protocols
   */
  private initializeTriageProtocols(): Map<string, TriageProtocol> {
    const protocols = new Map<string, TriageProtocol>();

    protocols.set('chest_pain', {
      name: 'Chest Pain Protocol',
      urgencyLevel: 2,
      redFlags: ['severe chest pain', 'shortness of breath', 'sweating', 'nausea'],
      questions: ['Location of pain?', 'Radiation to arm/jaw?', 'Associated symptoms?'],
      specialist: 'cardiologist'
    });

    protocols.set('mental_health', {
      name: 'Mental Health Protocol',
      urgencyLevel: 3,
      redFlags: ['suicidal thoughts', 'self harm', 'psychosis'],
      questions: ['Mood changes?', 'Sleep patterns?', 'Support system?'],
      specialist: 'psychiatrist'
    });

    return protocols;
  }

  /**
   * Enhanced confidence for triage situations
   */
  getConfidenceScore(request: AgentRequest): number {
    // Triage agent is confident for initial assessments
    return 0.9;
  }
}

interface TriageProtocol {
  name: string;
  urgencyLevel: number;
  redFlags: string[];
  questions: string[];
  specialist: AgentRole;
}

export default TriageAgent;
