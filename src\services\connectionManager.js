/**
 * WebSocket Connection Manager
 * Auto-reconnection with exponential backoff for voice and real-time services
 */

class ConnectionManager {
  constructor() {
    this.connections = new Map();
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 10;
    this.baseReconnectDelay = 1000; // 1 second
    this.maxReconnectDelay = 30000; // 30 seconds
    this.heartbeatInterval = 30000; // 30 seconds
    this.heartbeatTimeouts = new Map();
    
    this.setupNetworkEventListeners();
  }

  /**
   * Setup network event listeners
   */
  setupNetworkEventListeners() {
    window.addEventListener('online', () => {
      console.log('Network connection restored');
      this.reconnectAllConnections();
    });

    window.addEventListener('offline', () => {
      console.log('Network connection lost');
      this.handleNetworkOffline();
    });

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkConnectionHealth();
      }
    });
  }

  /**
   * Create or get existing WebSocket connection
   */
  async connect(connectionId, config) {
    const {
      url,
      protocols = [],
      onOpen = () => {},
      onMessage = () => {},
      onError = () => {},
      onClose = () => {},
      enableHeartbeat = true
    } = config;

    // Close existing connection if any
    if (this.connections.has(connectionId)) {
      this.disconnect(connectionId);
    }

    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(url, protocols);
        
        const connectionInfo = {
          id: connectionId,
          ws,
          config,
          isConnected: false,
          lastPing: null,
          lastPong: null,
          reconnectTimer: null
        };

        // Connection opened
        ws.onopen = (event) => {
          console.log(`WebSocket connected: ${connectionId}`);
          connectionInfo.isConnected = true;
          
          // Clear reconnect attempts
          this.reconnectAttempts.delete(connectionId);
          
          // Start heartbeat if enabled
          if (enableHeartbeat) {
            this.startHeartbeat(connectionId);
          }
          
          // Store connection
          this.connections.set(connectionId, connectionInfo);
          
          // Call user handler
          onOpen(event);
          
          resolve(connectionInfo);
        };

        // Message received
        ws.onmessage = (event) => {
          const connectionInfo = this.connections.get(connectionId);
          if (connectionInfo) {
            // Handle pong messages for heartbeat
            if (event.data === 'pong') {
              connectionInfo.lastPong = Date.now();
              return;
            }
            
            // Call user handler
            onMessage(event);
          }
        };

        // Connection error
        ws.onerror = (event) => {
          console.error(`WebSocket error for ${connectionId}:`, event);
          onError(event);
        };

        // Connection closed
        ws.onclose = (event) => {
          console.log(`WebSocket connection closed: ${connectionId}`, event.code, event.reason);
          connectionInfo.isConnected = false;
          
          // Stop heartbeat
          this.stopHeartbeat(connectionId);
          
          // Call user handler
          onClose(event);
          
          // Auto-reconnect if not manually closed
          if (event.code !== 1000 && event.code !== 1001) {
            this.scheduleReconnect(connectionId);
          } else {
            // Clean up if manually closed
            this.connections.delete(connectionId);
            this.reconnectAttempts.delete(connectionId);
          }
        };

        // Store initial connection info for potential reconnection
        this.connections.set(connectionId, connectionInfo);

      } catch (error) {
        console.error(`Failed to create WebSocket for ${connectionId}:`, error);
        reject(error);
      }
    });
  }

  /**
   * Send message through WebSocket
   */
  send(connectionId, data) {
    const connectionInfo = this.connections.get(connectionId);
    
    if (!connectionInfo) {
      throw new Error(`Connection ${connectionId} not found`);
    }

    if (!connectionInfo.isConnected || connectionInfo.ws.readyState !== WebSocket.OPEN) {
      throw new Error(`Connection ${connectionId} is not open`);
    }

    try {
      if (typeof data === 'object') {
        connectionInfo.ws.send(JSON.stringify(data));
      } else {
        connectionInfo.ws.send(data);
      }
    } catch (error) {
      console.error(`Failed to send message to ${connectionId}:`, error);
      throw error;
    }
  }

  /**
   * Close WebSocket connection
   */
  disconnect(connectionId, code = 1000, reason = 'Manual disconnect') {
    const connectionInfo = this.connections.get(connectionId);
    
    if (!connectionInfo) {
      return;
    }

    // Clear reconnect timer
    if (connectionInfo.reconnectTimer) {
      clearTimeout(connectionInfo.reconnectTimer);
    }

    // Stop heartbeat
    this.stopHeartbeat(connectionId);

    // Close WebSocket
    if (connectionInfo.ws && connectionInfo.ws.readyState !== WebSocket.CLOSED) {
      connectionInfo.ws.close(code, reason);
    }

    // Clean up
    this.connections.delete(connectionId);
    this.reconnectAttempts.delete(connectionId);
  }

  /**
   * Get connection status
   */
  getConnectionStatus(connectionId) {
    const connectionInfo = this.connections.get(connectionId);
    
    if (!connectionInfo) {
      return { status: 'not_found' };
    }

    return {
      status: connectionInfo.isConnected ? 'connected' : 'disconnected',
      readyState: connectionInfo.ws.readyState,
      lastPing: connectionInfo.lastPing,
      lastPong: connectionInfo.lastPong,
      reconnectAttempts: this.reconnectAttempts.get(connectionId) || 0
    };
  }

  /**
   * Get all connections status
   */
  getAllConnectionsStatus() {
    const status = {};
    
    for (const [connectionId, connectionInfo] of this.connections) {
      status[connectionId] = {
        isConnected: connectionInfo.isConnected,
        readyState: connectionInfo.ws.readyState,
        reconnectAttempts: this.reconnectAttempts.get(connectionId) || 0
      };
    }
    
    return status;
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  scheduleReconnect(connectionId) {
    const connectionInfo = this.connections.get(connectionId);
    
    if (!connectionInfo) {
      return;
    }

    const attempts = this.reconnectAttempts.get(connectionId) || 0;
    
    if (attempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnection attempts reached for ${connectionId}`);
      this.emit('maxReconnectAttemptsReached', { connectionId, attempts });
      return;
    }

    // Calculate delay with exponential backoff and jitter
    const delay = Math.min(
      this.baseReconnectDelay * Math.pow(2, attempts) + Math.random() * 1000,
      this.maxReconnectDelay
    );

    console.log(`Scheduling reconnect for ${connectionId} in ${delay}ms (attempt ${attempts + 1})`);

    connectionInfo.reconnectTimer = setTimeout(async () => {
      this.reconnectAttempts.set(connectionId, attempts + 1);
      
      try {
        await this.connect(connectionId, connectionInfo.config);
        console.log(`Successfully reconnected ${connectionId}`);
      } catch (error) {
        console.error(`Reconnection failed for ${connectionId}:`, error);
        this.scheduleReconnect(connectionId);
      }
    }, delay);
  }

  /**
   * Reconnect all connections
   */
  async reconnectAllConnections() {
    const reconnectPromises = [];
    
    for (const [connectionId, connectionInfo] of this.connections) {
      if (!connectionInfo.isConnected) {
        console.log(`Attempting to reconnect ${connectionId}`);
        reconnectPromises.push(
          this.connect(connectionId, connectionInfo.config)
            .catch(error => {
              console.error(`Failed to reconnect ${connectionId}:`, error);
              return null;
            })
        );
      }
    }
    
    const results = await Promise.allSettled(reconnectPromises);
    console.log(`Reconnection completed: ${results.filter(r => r.status === 'fulfilled').length} successful`);
  }

  /**
   * Handle network offline event
   */
  handleNetworkOffline() {
    console.log('Handling network offline event');
    
    for (const [connectionId, connectionInfo] of this.connections) {
      // Stop heartbeat
      this.stopHeartbeat(connectionId);
      
      // Clear reconnect timers
      if (connectionInfo.reconnectTimer) {
        clearTimeout(connectionInfo.reconnectTimer);
        connectionInfo.reconnectTimer = null;
      }
      
      // Mark as disconnected
      connectionInfo.isConnected = false;
    }
  }

  /**
   * Check connection health
   */
  checkConnectionHealth() {
    for (const [connectionId, connectionInfo] of this.connections) {
      if (connectionInfo.isConnected) {
        const now = Date.now();
        const pingTimeout = 60000; // 1 minute
        
        // Check if connection is responsive
        if (connectionInfo.lastPing && 
            (!connectionInfo.lastPong || connectionInfo.lastPong < connectionInfo.lastPing) &&
            now - connectionInfo.lastPing > pingTimeout) {
          console.warn(`Connection ${connectionId} appears unresponsive, forcing reconnect`);
          connectionInfo.ws.close(4000, 'Health check failed');
        }
      }
    }
  }

  /**
   * Start heartbeat for connection
   */
  startHeartbeat(connectionId) {
    this.stopHeartbeat(connectionId); // Clear existing
    
    const heartbeatTimer = setInterval(() => {
      const connectionInfo = this.connections.get(connectionId);
      
      if (!connectionInfo || !connectionInfo.isConnected || 
          connectionInfo.ws.readyState !== WebSocket.OPEN) {
        this.stopHeartbeat(connectionId);
        return;
      }
      
      try {
        connectionInfo.lastPing = Date.now();
        connectionInfo.ws.send('ping');
      } catch (error) {
        console.error(`Heartbeat failed for ${connectionId}:`, error);
        this.stopHeartbeat(connectionId);
      }
    }, this.heartbeatInterval);
    
    this.heartbeatTimeouts.set(connectionId, heartbeatTimer);
  }

  /**
   * Stop heartbeat for connection
   */
  stopHeartbeat(connectionId) {
    const timer = this.heartbeatTimeouts.get(connectionId);
    if (timer) {
      clearInterval(timer);
      this.heartbeatTimeouts.delete(connectionId);
    }
  }

  /**
   * Simple event emitter
   */
  emit(event, data) {
    const customEvent = new CustomEvent(`connection-${event}`, {
      detail: { ...data, timestamp: Date.now() }
    });
    window.dispatchEvent(customEvent);
  }

  /**
   * Clean up all connections
   */
  destroy() {
    console.log('Destroying Connection Manager');
    
    // Close all connections
    for (const [connectionId] of this.connections) {
      this.disconnect(connectionId);
    }
    
    // Clear all heartbeat timers
    for (const [connectionId] of this.heartbeatTimeouts) {
      this.stopHeartbeat(connectionId);
    }
    
    // Clear maps
    this.connections.clear();
    this.reconnectAttempts.clear();
    this.heartbeatTimeouts.clear();
  }
}

// Create singleton instance
const connectionManager = new ConnectionManager();

export default connectionManager;
