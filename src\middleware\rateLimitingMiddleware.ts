/**
 * RATE LIMITING MIDDLE<PERSON>RE FOR MEDICAL API ENDPOINTS
 * 
 * This middleware provides comprehensive rate limiting with:
 * - Medical endpoint protection with role-based limits
 * - Emergency bypass mechanisms for critical medical situations
 * - Real-time DDoS protection and attack mitigation
 * - HIPAA-compliant audit logging for all requests
 * - Performance optimization for high-volume medical workflows
 * - Context-aware rate limiting based on user behavior
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency medical requests must bypass rate limits
 * - Critical medical endpoints must remain available
 * - Rate limiting must not compromise patient safety
 * - All security events must be audited for compliance
 * - Middleware must maintain high performance under load
 */

import type { Request, Response, NextFunction } from 'express';
import type { UserRole } from '../types/auth';
import rateLimitingService from '../services/rateLimitingService';
import ddosProtectionService from '../services/ddosProtectionService';
import auditLogger from '../utils/auditLogger';

interface RateLimitRequest extends Request {
  user?: {
    id: string;
    role: UserRole;
    permissions: string[];
  };
  rateLimitInfo?: {
    limit: number;
    remaining: number;
    resetTime: number;
    retryAfter: number;
  };
  emergencyAccess?: {
    active: boolean;
    justification: string;
    expiresAt: number;
  };
}

interface MiddlewareConfig {
  readonly skipSuccessfulRequests: boolean;
  readonly skipFailedRequests: boolean;
  readonly enableDDoSProtection: boolean;
  readonly enableEmergencyBypass: boolean;
  readonly enableAuditLogging: boolean;
  readonly customKeyGenerator?: (req: RateLimitRequest) => string;
  readonly onLimitReached?: (req: RateLimitRequest, res: Response) => void;
  readonly emergencyEndpoints: string[];
  readonly medicalEndpoints: string[];
  readonly publicEndpoints: string[];
}

interface RateLimitResponse {
  readonly error: string;
  readonly message: string;
  readonly retryAfter: number;
  readonly limit: number;
  readonly remaining: number;
  readonly resetTime: number;
  readonly emergencyBypass?: {
    readonly available: boolean;
    readonly instructions: string;
  };
}

/**
 * Create rate limiting middleware for medical API endpoints
 */
export function createMedicalRateLimitMiddleware(
  config: Partial<MiddlewareConfig> = {}
): (req: RateLimitRequest, res: Response, next: NextFunction) => Promise<void> {
  const middlewareConfig: MiddlewareConfig = {
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    enableDDoSProtection: true,
    enableEmergencyBypass: true,
    enableAuditLogging: true,
    emergencyEndpoints: [
      '/api/emergency/*',
      '/api/medical/emergency/*',
      '/emergency-access',
      '/critical-alerts'
    ],
    medicalEndpoints: [
      '/api/medical/*',
      '/api/patient/*',
      '/api/provider/*',
      '/api/conditions/*',
      '/api/medications/*',
      '/api/symptoms/*'
    ],
    publicEndpoints: [
      '/api/auth/login',
      '/api/auth/register',
      '/api/health',
      '/api/status'
    ],
    ...config
  };

  return async (req: RateLimitRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const startTime = performance.now();
      const clientIP = getClientIP(req);
      const userAgent = req.get('User-Agent') || '';
      const endpoint = req.path;
      const method = req.method;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      // Skip rate limiting for public endpoints
      if (isPublicEndpoint(endpoint, middlewareConfig.publicEndpoints)) {
        return next();
      }

      // Check for emergency access
      const isEmergencyRequest = isEmergencyEndpoint(endpoint, middlewareConfig.emergencyEndpoints) ||
                                req.headers['x-emergency-access'] === 'true' ||
                                req.query.emergency === 'true';

      const emergencyJustification = req.headers['x-emergency-justification'] as string ||
                                   req.query.justification as string;

      // DDoS Protection Check
      if (middlewareConfig.enableDDoSProtection) {
        const ddosResult = await ddosProtectionService.analyzeRequest(
          clientIP,
          endpoint,
          {
            userAgent,
            method,
            payloadSize: getRequestSize(req),
            userRole,
            isEmergencyRequest,
            timestamp: Date.now()
          }
        );

        if (!ddosResult.allowed) {
          await logSecurityEvent(
            'ddos_request_blocked',
            'high',
            {
              client_ip: clientIP,
              endpoint,
              user_id: userId,
              user_role: userRole,
              reason: ddosResult.reason,
              action: ddosResult.action
            }
          );

          if (ddosResult.challengeRequired) {
            return sendChallengeResponse(res, ddosResult);
          }

          return sendDDoSBlockedResponse(res, ddosResult);
        }

        if (ddosResult.emergencyBypass) {
          await logSecurityEvent(
            'ddos_emergency_bypass',
            'medium',
            {
              client_ip: clientIP,
              endpoint,
              user_id: userId,
              user_role: userRole,
              emergency_access: true
            }
          );
          return next();
        }
      }

      // Rate Limiting Check
      if (userId) {
        const rateLimitResult = await rateLimitingService.checkLimit(
          userId,
          endpoint,
          {
            userRole,
            emergencyBypass: isEmergencyRequest,
            justification: emergencyJustification,
            urgency: determineUrgency(req),
            clientIP,
            userAgent
          }
        );

        // Set rate limit info for response headers
        req.rateLimitInfo = rateLimitResult.rateLimitInfo;

        if (!rateLimitResult.allowed) {
          await logSecurityEvent(
            'rate_limit_exceeded',
            'medium',
            {
              client_ip: clientIP,
              endpoint,
              user_id: userId,
              user_role: userRole,
              limit: rateLimitResult.rateLimitInfo.limit,
              remaining: rateLimitResult.rateLimitInfo.remaining,
              retry_after: rateLimitResult.retryAfter
            }
          );

          return sendRateLimitResponse(res, rateLimitResult, middlewareConfig);
        }

        if (rateLimitResult.emergencyBypassUsed) {
          await logSecurityEvent(
            'emergency_bypass_used',
            'high',
            {
              client_ip: clientIP,
              endpoint,
              user_id: userId,
              user_role: userRole,
              justification: emergencyJustification,
              emergency_access: true
            }
          );
        }
      }

      // Add rate limit headers to response
      addRateLimitHeaders(res, req.rateLimitInfo);

      // Log successful request if audit logging is enabled
      if (middlewareConfig.enableAuditLogging) {
        await logSecurityEvent(
          'api_request_allowed',
          'low',
          {
            client_ip: clientIP,
            endpoint,
            method,
            user_id: userId,
            user_role: userRole,
            processing_time: performance.now() - startTime,
            is_emergency: isEmergencyRequest
          }
        );
      }

      next();

    } catch (error) {
      await auditLogger.logSecurityEvent(
        'rate_limiting_middleware_error',
        'high',
        {
          endpoint: req.path,
          user_id: req.user?.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          client_ip: getClientIP(req)
        }
      );

      // Allow request to proceed on middleware error
      next();
    }
  };
}

/**
 * Emergency bypass middleware for critical medical situations
 */
export function createEmergencyBypassMiddleware(): (
  req: RateLimitRequest, 
  res: Response, 
  next: NextFunction
) => Promise<void> {
  return async (req: RateLimitRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const emergencyHeader = req.headers['x-emergency-bypass'] as string;
      const justification = req.headers['x-emergency-justification'] as string;
      const urgency = req.headers['x-emergency-urgency'] as string;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!emergencyHeader || !justification || !userId) {
        return next();
      }

      // Validate emergency bypass request
      const bypassResult = await rateLimitingService.requestEmergencyBypass(
        userId,
        req.path,
        justification,
        urgency as any || 'urgent',
        userRole || 'patient'
      );

      if (bypassResult.approved) {
        req.emergencyAccess = {
          active: true,
          justification,
          expiresAt: bypassResult.expiresAt || Date.now() + 3600000
        };

        await auditLogger.logEmergencyAccess(
          userId,
          userId,
          justification,
          {
            endpoint: req.path,
            bypass_id: bypassResult.bypassId,
            urgency,
            user_role: userRole,
            client_ip: getClientIP(req)
          }
        );
      } else if (bypassResult.requiresApproval) {
        return res.status(202).json({
          message: 'Emergency bypass request submitted for approval',
          requiresApproval: true,
          error: bypassResult.error
        });
      } else {
        return res.status(403).json({
          error: 'Emergency bypass denied',
          message: bypassResult.error,
          canRetry: false
        });
      }

      next();

    } catch (error) {
      await auditLogger.logSecurityEvent(
        'emergency_bypass_middleware_error',
        'high',
        {
          endpoint: req.path,
          user_id: req.user?.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      );

      next();
    }
  };
}

/**
 * Medical endpoint protection middleware
 */
export function createMedicalEndpointProtection(): (
  req: RateLimitRequest, 
  res: Response, 
  next: NextFunction
) => Promise<void> {
  return async (req: RateLimitRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const endpoint = req.path;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      // Check if this is a medical endpoint
      if (!isMedicalEndpoint(endpoint)) {
        return next();
      }

      // Require authentication for medical endpoints
      if (!userId) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'Medical endpoints require user authentication',
          endpoint
        });
      }

      // Enhanced rate limiting for medical endpoints
      const enhancedLimits = getEnhancedMedicalLimits(endpoint, userRole);
      
      const rateLimitResult = await rateLimitingService.checkLimit(
        userId,
        endpoint,
        {
          userRole,
          clientIP: getClientIP(req),
          userAgent: req.get('User-Agent')
        }
      );

      if (!rateLimitResult.allowed) {
        await auditLogger.logMedicalDataAccess(
          'rate_limit_exceeded',
          'medical_endpoint',
          endpoint,
          {
            user_id: userId,
            user_role: userRole,
            limit: rateLimitResult.rateLimitInfo.limit,
            remaining: rateLimitResult.rateLimitInfo.remaining,
            client_ip: getClientIP(req)
          }
        );

        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests to medical endpoint',
          retryAfter: rateLimitResult.retryAfter,
          limit: rateLimitResult.rateLimitInfo.limit,
          remaining: rateLimitResult.rateLimitInfo.remaining
        });
      }

      // Log medical endpoint access
      await auditLogger.logMedicalDataAccess(
        'endpoint_access',
        'medical_endpoint',
        endpoint,
        {
          user_id: userId,
          user_role: userRole,
          method: req.method,
          client_ip: getClientIP(req),
          timestamp: Date.now()
        }
      );

      next();

    } catch (error) {
      await auditLogger.logSecurityEvent(
        'medical_endpoint_protection_error',
        'high',
        {
          endpoint: req.path,
          user_id: req.user?.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      );

      next();
    }
  };
}

// Helper functions
function getClientIP(req: Request): string {
  return (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
         req.headers['x-real-ip'] as string ||
         req.connection.remoteAddress ||
         req.socket.remoteAddress ||
         'unknown';
}

function getRequestSize(req: Request): number {
  const contentLength = req.headers['content-length'];
  return contentLength ? parseInt(contentLength, 10) : 0;
}

function isPublicEndpoint(endpoint: string, publicEndpoints: string[]): boolean {
  return publicEndpoints.some(pattern => {
    if (pattern.endsWith('*')) {
      return endpoint.startsWith(pattern.slice(0, -1));
    }
    return endpoint === pattern;
  });
}

function isEmergencyEndpoint(endpoint: string, emergencyEndpoints: string[]): boolean {
  return emergencyEndpoints.some(pattern => {
    if (pattern.endsWith('*')) {
      return endpoint.startsWith(pattern.slice(0, -1));
    }
    return endpoint === pattern;
  });
}

function isMedicalEndpoint(endpoint: string): boolean {
  const medicalPatterns = [
    '/api/medical/',
    '/api/patient/',
    '/api/provider/',
    '/api/conditions/',
    '/api/medications/',
    '/api/symptoms/'
  ];
  
  return medicalPatterns.some(pattern => endpoint.includes(pattern));
}

function determineUrgency(req: RateLimitRequest): 'routine' | 'urgent' | 'critical' | 'emergency' {
  const urgencyHeader = req.headers['x-urgency'] as string;
  const emergencyHeader = req.headers['x-emergency-access'] as string;
  
  if (emergencyHeader === 'true') return 'emergency';
  if (urgencyHeader) return urgencyHeader as any;
  
  // Determine urgency based on endpoint
  if (req.path.includes('/emergency/')) return 'emergency';
  if (req.path.includes('/critical/')) return 'critical';
  if (req.path.includes('/urgent/')) return 'urgent';
  
  return 'routine';
}

function getEnhancedMedicalLimits(endpoint: string, userRole?: UserRole): any {
  // Return enhanced limits for medical endpoints
  return {
    windowMs: 60000,
    maxRequests: userRole === 'doctor' ? 500 : userRole === 'nurse' ? 300 : 100
  };
}

function addRateLimitHeaders(res: Response, rateLimitInfo?: any): void {
  if (!rateLimitInfo) return;
  
  res.set({
    'X-RateLimit-Limit': rateLimitInfo.limit.toString(),
    'X-RateLimit-Remaining': rateLimitInfo.remaining.toString(),
    'X-RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString(),
    'X-RateLimit-RetryAfter': rateLimitInfo.retryAfter.toString()
  });
}

function sendRateLimitResponse(
  res: Response, 
  rateLimitResult: any, 
  config: MiddlewareConfig
): void {
  const response: RateLimitResponse = {
    error: 'Rate limit exceeded',
    message: 'Too many requests. Please try again later.',
    retryAfter: rateLimitResult.retryAfter || 60,
    limit: rateLimitResult.rateLimitInfo.limit,
    remaining: rateLimitResult.rateLimitInfo.remaining,
    resetTime: rateLimitResult.rateLimitInfo.resetTime
  };

  if (config.enableEmergencyBypass) {
    response.emergencyBypass = {
      available: true,
      instructions: 'For emergency medical access, include X-Emergency-Access: true and X-Emergency-Justification headers'
    };
  }

  res.status(429).json(response);
}

function sendDDoSBlockedResponse(res: Response, ddosResult: any): void {
  res.status(429).json({
    error: 'Request blocked',
    message: 'Request blocked due to suspicious activity',
    reason: ddosResult.reason,
    action: ddosResult.action,
    retryAfter: 300 // 5 minutes
  });
}

function sendChallengeResponse(res: Response, ddosResult: any): void {
  res.status(429).json({
    error: 'Challenge required',
    message: 'Please complete the security challenge to continue',
    challengeType: 'captcha',
    challengeRequired: true
  });
}

async function logSecurityEvent(eventType: string, severity: string, details: any): Promise<void> {
  await auditLogger.logSecurityEvent(eventType, severity as any, details);
}

export default {
  createMedicalRateLimitMiddleware,
  createEmergencyBypassMiddleware,
  createMedicalEndpointProtection
};
