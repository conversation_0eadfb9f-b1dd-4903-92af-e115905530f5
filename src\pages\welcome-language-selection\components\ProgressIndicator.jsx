import React from 'react';

const ProgressIndicator = ({ currentStep, totalSteps }) => {
  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <div className="w-full max-w-md mx-auto space-y-3">
      {/* Progress Bar */}
      <div className="w-full bg-secondary-100 rounded-full h-2">
        <div 
          className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
      
      {/* Step Text */}
      <div className="flex justify-between items-center text-sm">
        <span className="text-text-secondary">
          Step {currentStep} of {totalSteps}
        </span>
        <span className="text-primary-600 font-medium">
          {Math.round(progressPercentage)}%
        </span>
      </div>
    </div>
  );
};

export default ProgressIndicator;