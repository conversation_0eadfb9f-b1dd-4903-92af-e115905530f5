import React, { useState } from 'react';

import Button from '../../../components/ui/Button';

const AdvancedFilterControls = ({ onFiltersChange, initialFilters = {} }) => {
  const [filters, setFilters] = useState({
    dateRange: { start: '', end: '', preset: '30d' },
    demographics: { ageGroup: 'all', gender: 'all', location: 'all' },
    medical: { conditions: [], specialties: [], severity: 'all' },
    behavior: { sessionType: 'all', deviceType: 'all', timeOfDay: 'all' },
    ...initialFilters
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const datePresets = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
    { value: '1y', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const ageGroups = [
    { value: 'all', label: 'All Ages' },
    { value: '18-25', label: '18-25' },
    { value: '26-35', label: '26-35' },
    { value: '36-50', label: '36-50' },
    { value: '51-65', label: '51-65' },
    { value: '65+', label: '65+' }
  ];

  const medicalConditions = [
    'Hypertension', 'Diabetes', 'Anxiety', 'Depression', 'Arthritis',
    'Asthma', 'Heart Disease', 'Obesity', 'Chronic Pain', 'Insomnia'
  ];

  const medicalSpecialties = [
    'General Practice', 'Cardiology', 'Dermatology', 'Mental Health',
    'Nutrition', 'Orthopedics', 'Neurology', 'Endocrinology'
  ];

  const handleFilterChange = (category, key, value) => {
    const newFilters = {
      ...filters,
      [category]: {
        ...filters[category],
        [key]: value
      }
    };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleArrayFilterToggle = (category, key, value) => {
    const currentArray = filters[category][key] || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    handleFilterChange(category, key, newArray);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      dateRange: { start: '', end: '', preset: '30d' },
      demographics: { ageGroup: 'all', gender: 'all', location: 'all' },
      medical: { conditions: [], specialties: [], severity: 'all' },
      behavior: { sessionType: 'all', deviceType: 'all', timeOfDay: 'all' }
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  const exportFilters = () => {
    const dataStr = JSON.stringify(filters, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'analytics-filters.json';
    link.click();
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.demographics?.ageGroup !== 'all') count++;
    if (filters.demographics?.gender !== 'all') count++;
    if (filters.demographics?.location !== 'all') count++;
    if (filters.medical?.conditions?.length > 0) count++;
    if (filters.medical?.specialties?.length > 0) count++;
    if (filters.medical?.severity !== 'all') count++;
    if (filters.behavior?.sessionType !== 'all') count++;
    if (filters.behavior?.deviceType !== 'all') count++;
    if (filters.behavior?.timeOfDay !== 'all') count++;
    return count;
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Advanced Filters
            </h3>
            {getActiveFiltersCount() > 0 && (
              <span className="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs font-medium">
                {getActiveFiltersCount()} active
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              iconName="X"
            >
              Clear All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              iconName={isExpanded ? 'ChevronUp' : 'ChevronDown'}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Filters - Always Visible */}
      <div className="p-4 bg-secondary-50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Date Range */}
          <div>
            <label className="block text-xs font-medium text-text-secondary mb-2">
              Date Range
            </label>
            <select
              value={filters.dateRange?.preset || '30d'}
              onChange={(e) => handleFilterChange('dateRange', 'preset', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {datePresets.map((preset) => (
                <option key={preset.value} value={preset.value}>
                  {preset.label}
                </option>
              ))}
            </select>
          </div>

          {/* Age Group */}
          <div>
            <label className="block text-xs font-medium text-text-secondary mb-2">
              Age Group
            </label>
            <select
              value={filters.demographics?.ageGroup || 'all'}
              onChange={(e) => handleFilterChange('demographics', 'ageGroup', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {ageGroups.map((group) => (
                <option key={group.value} value={group.value}>
                  {group.label}
                </option>
              ))}
            </select>
          </div>

          {/* Session Type */}
          <div>
            <label className="block text-xs font-medium text-text-secondary mb-2">
              Session Type
            </label>
            <select
              value={filters.behavior?.sessionType || 'all'}
              onChange={(e) => handleFilterChange('behavior', 'sessionType', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Sessions</option>
              <option value="consultation">Consultation</option>
              <option value="follow-up">Follow-up</option>
              <option value="emergency">Emergency</option>
              <option value="wellness">Wellness Check</option>
            </select>
          </div>
        </div>
      </div>

      {/* Advanced Filters - Expandable */}
      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* Custom Date Range */}
          {filters.dateRange?.preset === 'custom' && (
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Custom Date Range
              </label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-text-secondary mb-1">Start Date</label>
                  <input
                    type="date"
                    value={filters.dateRange?.start || ''}
                    onChange={(e) => handleFilterChange('dateRange', 'start', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-xs text-text-secondary mb-1">End Date</label>
                  <input
                    type="date"
                    value={filters.dateRange?.end || ''}
                    onChange={(e) => handleFilterChange('dateRange', 'end', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Demographics */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Demographics</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs text-text-secondary mb-1">Gender</label>
                <select
                  value={filters.demographics?.gender || 'all'}
                  onChange={(e) => handleFilterChange('demographics', 'gender', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">All Genders</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer-not-to-say">Prefer not to say</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs text-text-secondary mb-1">Location</label>
                <select
                  value={filters.demographics?.location || 'all'}
                  onChange={(e) => handleFilterChange('demographics', 'location', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">All Locations</option>
                  <option value="urban">Urban</option>
                  <option value="suburban">Suburban</option>
                  <option value="rural">Rural</option>
                </select>
              </div>
            </div>
          </div>

          {/* Medical Conditions */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Medical Conditions</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {medicalConditions.map((condition) => (
                <label
                  key={condition}
                  className="flex items-center p-2 border border-border rounded-lg cursor-pointer hover:bg-secondary-50 transition-fast"
                >
                  <input
                    type="checkbox"
                    checked={filters.medical?.conditions?.includes(condition) || false}
                    onChange={() => handleArrayFilterToggle('medical', 'conditions', condition)}
                    className="rounded border-border text-primary-600 focus:ring-primary-500 mr-2"
                  />
                  <span className="text-sm text-text-primary">{condition}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Medical Specialties */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Medical Specialties</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {medicalSpecialties.map((specialty) => (
                <label
                  key={specialty}
                  className="flex items-center p-2 border border-border rounded-lg cursor-pointer hover:bg-secondary-50 transition-fast"
                >
                  <input
                    type="checkbox"
                    checked={filters.medical?.specialties?.includes(specialty) || false}
                    onChange={() => handleArrayFilterToggle('medical', 'specialties', specialty)}
                    className="rounded border-border text-primary-600 focus:ring-primary-500 mr-2"
                  />
                  <span className="text-sm text-text-primary">{specialty}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Behavioral Filters */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Behavioral Patterns</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs text-text-secondary mb-1">Device Type</label>
                <select
                  value={filters.behavior?.deviceType || 'all'}
                  onChange={(e) => handleFilterChange('behavior', 'deviceType', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">All Devices</option>
                  <option value="desktop">Desktop</option>
                  <option value="mobile">Mobile</option>
                  <option value="tablet">Tablet</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs text-text-secondary mb-1">Time of Day</label>
                <select
                  value={filters.behavior?.timeOfDay || 'all'}
                  onChange={(e) => handleFilterChange('behavior', 'timeOfDay', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">All Times</option>
                  <option value="morning">Morning (6-12)</option>
                  <option value="afternoon">Afternoon (12-18)</option>
                  <option value="evening">Evening (18-24)</option>
                  <option value="night">Night (0-6)</option>
                </select>
              </div>

              <div>
                <label className="block text-xs text-text-secondary mb-1">Severity Level</label>
                <select
                  value={filters.medical?.severity || 'all'}
                  onChange={(e) => handleFilterChange('medical', 'severity', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">All Severities</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
            </div>
          </div>

          {/* Export Options */}
          <div className="flex items-center justify-between pt-4 border-t border-border">
            <div className="text-sm text-text-secondary">
              {getActiveFiltersCount()} filter{getActiveFiltersCount() !== 1 ? 's' : ''} applied
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={exportFilters}
              iconName="Download"
            >
              Export Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilterControls;