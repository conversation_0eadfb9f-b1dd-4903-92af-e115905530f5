/**
 * ENHANCED RATE LIMITING SERVICE WITH EMERGENCY BYPASS
 * 
 * This service provides comprehensive rate limiting with:
 * - Medical API endpoint protection with role-based limits
 * - Emergency bypass mechanisms for critical medical situations
 * - Adaptive rate limiting based on system load and user behavior
 * - DDoS protection with intelligent pattern detection
 * - HIPAA-compliant audit logging for all rate limiting events
 * - Performance optimization for high-volume medical workflows
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency medical access must bypass rate limits
 * - Critical medical data access must be prioritized
 * - Rate limiting must not compromise patient safety
 * - All bypass events must be audited for compliance
 * - System must maintain availability during attacks
 */

import type { UserRole, Permission } from '../types/auth';
import auditLogger from '../utils/auditLogger';

interface RateLimitConfig {
  readonly windowMs: number;
  readonly maxRequests: number;
  readonly skipSuccessfulRequests?: boolean;
  readonly skipFailedRequests?: boolean;
  readonly keyGenerator?: (req: any) => string;
  readonly onLimitReached?: (req: any, rateLimitInfo: RateLimitInfo) => void;
  readonly emergencyBypass?: EmergencyBypassConfig;
}

interface EmergencyBypassConfig {
  readonly enabled: boolean;
  readonly maxBypassesPerDay: number;
  readonly requiredJustification: boolean;
  readonly autoApprovalRoles: UserRole[];
  readonly criticalEndpoints: string[];
  readonly auditAllBypasses: boolean;
}

interface RateLimitInfo {
  readonly limit: number;
  readonly remaining: number;
  readonly resetTime: number;
  readonly retryAfter: number;
  readonly totalHits: number;
  readonly windowStart: number;
}

interface EmergencyBypassRequest {
  readonly id: string;
  readonly userId: string;
  readonly endpoint: string;
  readonly justification: string;
  readonly urgency: 'routine' | 'urgent' | 'critical' | 'emergency';
  readonly requestedAt: number;
  readonly approvedBy?: string;
  readonly approvedAt?: number;
  readonly expiresAt: number;
  readonly used: boolean;
  readonly usedAt?: number;
}

interface UserRateLimitProfile {
  readonly userId: string;
  readonly role: UserRole;
  readonly permissions: Permission[];
  readonly customLimits: Map<string, RateLimitConfig>;
  readonly emergencyBypassCount: number;
  readonly lastEmergencyBypass: number;
  readonly trustScore: number;
  readonly behaviorPattern: UserBehaviorPattern;
}

interface UserBehaviorPattern {
  readonly averageRequestRate: number;
  readonly peakRequestTimes: number[];
  readonly commonEndpoints: string[];
  readonly emergencyAccessHistory: number;
  readonly suspiciousActivityScore: number;
  readonly lastAnalyzed: number;
}

interface SystemLoadMetrics {
  readonly cpuUsage: number;
  readonly memoryUsage: number;
  readonly activeConnections: number;
  readonly responseTime: number;
  readonly errorRate: number;
  readonly emergencyRequestsActive: number;
}

class RateLimitingService {
  private readonly defaultConfigs: Map<string, RateLimitConfig>;
  private readonly roleBasedLimits: Map<UserRole, Map<string, RateLimitConfig>>;
  private readonly userProfiles: Map<string, UserRateLimitProfile>;
  private readonly emergencyBypasses: Map<string, EmergencyBypassRequest>;
  private readonly requestCounts: Map<string, Map<number, number>>;
  private readonly systemLoad: SystemLoadMetrics;
  private readonly adaptiveLimiting: boolean;
  private readonly emergencyMode: boolean;

  constructor() {
    this.defaultConfigs = new Map();
    this.roleBasedLimits = new Map();
    this.userProfiles = new Map();
    this.emergencyBypasses = new Map();
    this.requestCounts = new Map();
    this.adaptiveLimiting = true;
    this.emergencyMode = false;

    this.systemLoad = {
      cpuUsage: 0,
      memoryUsage: 0,
      activeConnections: 0,
      responseTime: 0,
      errorRate: 0,
      emergencyRequestsActive: 0
    };

    this.initializeDefaultConfigs();
    this.initializeRoleBasedLimits();
    this.startSystemMonitoring();
  }

  /**
   * Check if request is within rate limits
   */
  async checkLimit(
    userId: string,
    endpoint: string,
    options: {
      userRole?: UserRole;
      emergencyBypass?: boolean;
      justification?: string;
      urgency?: 'routine' | 'urgent' | 'critical' | 'emergency';
      clientIP?: string;
      userAgent?: string;
    } = {}
  ): Promise<{
    allowed: boolean;
    rateLimitInfo: RateLimitInfo;
    emergencyBypassUsed?: boolean;
    error?: string;
    retryAfter?: number;
  }> {
    const {
      userRole = 'patient',
      emergencyBypass = false,
      justification,
      urgency = 'routine',
      clientIP,
      userAgent
    } = options;

    try {
      // Get rate limit configuration
      const config = this.getRateLimitConfig(endpoint, userRole, userId);
      const key = this.generateKey(userId, endpoint);

      // Check for emergency bypass first
      if (emergencyBypass) {
        const bypassResult = await this.handleEmergencyBypass(
          userId,
          endpoint,
          justification || '',
          urgency,
          userRole
        );

        if (bypassResult.approved) {
          await this.logRateLimitEvent(
            'emergency_bypass_used',
            userId,
            endpoint,
            {
              bypass_id: bypassResult.bypassId,
              justification,
              urgency,
              user_role: userRole,
              client_ip: clientIP,
              user_agent: userAgent
            }
          );

          return {
            allowed: true,
            rateLimitInfo: this.createBypassRateLimitInfo(),
            emergencyBypassUsed: true
          };
        } else {
          return {
            allowed: false,
            rateLimitInfo: this.createDeniedRateLimitInfo(),
            error: bypassResult.error
          };
        }
      }

      // Apply adaptive rate limiting
      const adaptedConfig = this.adaptRateLimitConfig(config, userId, endpoint);

      // Check current request count
      const currentCount = this.getCurrentRequestCount(key, adaptedConfig.windowMs);
      const rateLimitInfo = this.createRateLimitInfo(
        adaptedConfig,
        currentCount,
        adaptedConfig.windowMs
      );

      if (currentCount >= adaptedConfig.maxRequests) {
        // Rate limit exceeded
        await this.logRateLimitEvent(
          'rate_limit_exceeded',
          userId,
          endpoint,
          {
            limit: adaptedConfig.maxRequests,
            current_count: currentCount,
            window_ms: adaptedConfig.windowMs,
            user_role: userRole,
            client_ip: clientIP,
            user_agent: userAgent
          }
        );

        // Check if this triggers suspicious activity
        await this.analyzeSuspiciousActivity(userId, endpoint, currentCount);

        return {
          allowed: false,
          rateLimitInfo,
          retryAfter: rateLimitInfo.retryAfter
        };
      }

      // Increment request count
      this.incrementRequestCount(key, adaptedConfig.windowMs);

      // Update user behavior pattern
      await this.updateUserBehaviorPattern(userId, endpoint);

      // Log successful request
      await this.logRateLimitEvent(
        'request_allowed',
        userId,
        endpoint,
        {
          limit: adaptedConfig.maxRequests,
          current_count: currentCount + 1,
          remaining: adaptedConfig.maxRequests - currentCount - 1,
          user_role: userRole
        }
      );

      return {
        allowed: true,
        rateLimitInfo: {
          ...rateLimitInfo,
          remaining: rateLimitInfo.remaining - 1,
          totalHits: currentCount + 1
        }
      };

    } catch (error) {
      await auditLogger.logSecurityEvent(
        'rate_limiting_error',
        'high',
        {
          user_id: userId,
          endpoint,
          error: error instanceof Error ? error.message : 'Unknown error',
          client_ip: clientIP
        }
      );

      // Allow request on error to prevent blocking legitimate users
      return {
        allowed: true,
        rateLimitInfo: this.createErrorRateLimitInfo(),
        error: 'Rate limiting service error'
      };
    }
  }

  /**
   * Request emergency bypass
   */
  async requestEmergencyBypass(
    userId: string,
    endpoint: string,
    justification: string,
    urgency: 'routine' | 'urgent' | 'critical' | 'emergency',
    userRole: UserRole
  ): Promise<{
    approved: boolean;
    bypassId?: string;
    expiresAt?: number;
    error?: string;
    requiresApproval?: boolean;
  }> {
    try {
      // Validate justification
      if (!justification || justification.trim().length < 10) {
        return {
          approved: false,
          error: 'Emergency bypass requires detailed justification (minimum 10 characters)'
        };
      }

      // Check daily bypass limit
      const userProfile = await this.getUserProfile(userId);
      const today = new Date().toDateString();
      const todayBypasses = Array.from(this.emergencyBypasses.values())
        .filter(bypass => 
          bypass.userId === userId && 
          new Date(bypass.requestedAt).toDateString() === today
        ).length;

      const maxBypassesPerDay = this.getMaxBypassesPerDay(userRole);
      if (todayBypasses >= maxBypassesPerDay) {
        await auditLogger.logSecurityEvent(
          'emergency_bypass_limit_exceeded',
          'high',
          {
            user_id: userId,
            user_role: userRole,
            daily_limit: maxBypassesPerDay,
            current_count: todayBypasses,
            endpoint,
            justification
          }
        );

        return {
          approved: false,
          error: `Daily emergency bypass limit exceeded (${maxBypassesPerDay})`
        };
      }

      // Create bypass request
      const bypassId = this.generateBypassId();
      const expiresAt = Date.now() + (60 * 60 * 1000); // 1 hour

      const bypassRequest: EmergencyBypassRequest = {
        id: bypassId,
        userId,
        endpoint,
        justification,
        urgency,
        requestedAt: Date.now(),
        expiresAt,
        used: false
      };

      // Auto-approve for critical/emergency urgency or privileged roles
      const autoApprovalRoles: UserRole[] = ['doctor', 'nurse', 'emergency_responder', 'admin'];
      const shouldAutoApprove = 
        urgency === 'critical' || 
        urgency === 'emergency' || 
        autoApprovalRoles.includes(userRole);

      if (shouldAutoApprove) {
        bypassRequest.approvedBy = 'system';
        bypassRequest.approvedAt = Date.now();
        
        this.emergencyBypasses.set(bypassId, bypassRequest);

        await auditLogger.logEmergencyAccess(
          userId,
          userId,
          justification,
          {
            bypass_id: bypassId,
            endpoint,
            urgency,
            auto_approved: true,
            user_role: userRole,
            expires_at: expiresAt
          }
        );

        return {
          approved: true,
          bypassId,
          expiresAt
        };
      } else {
        // Requires manual approval
        this.emergencyBypasses.set(bypassId, bypassRequest);

        await auditLogger.logEmergencyAccess(
          userId,
          userId,
          justification,
          {
            bypass_id: bypassId,
            endpoint,
            urgency,
            requires_approval: true,
            user_role: userRole
          }
        );

        return {
          approved: false,
          requiresApproval: true,
          error: 'Emergency bypass request submitted for approval'
        };
      }

    } catch (error) {
      await auditLogger.logSecurityEvent(
        'emergency_bypass_request_error',
        'high',
        {
          user_id: userId,
          endpoint,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      );

      return {
        approved: false,
        error: 'Failed to process emergency bypass request'
      };
    }
  }

  /**
   * Get user rate limit profile
   */
  async getUserProfile(userId: string): Promise<UserRateLimitProfile> {
    let profile = this.userProfiles.get(userId);
    
    if (!profile) {
      profile = {
        userId,
        role: 'patient',
        permissions: [],
        customLimits: new Map(),
        emergencyBypassCount: 0,
        lastEmergencyBypass: 0,
        trustScore: 1.0,
        behaviorPattern: {
          averageRequestRate: 0,
          peakRequestTimes: [],
          commonEndpoints: [],
          emergencyAccessHistory: 0,
          suspiciousActivityScore: 0,
          lastAnalyzed: Date.now()
        }
      };
      
      this.userProfiles.set(userId, profile);
    }
    
    return profile;
  }

  /**
   * Set system load metrics
   */
  setSystemLoad(metrics: Partial<SystemLoadMetrics>): void {
    Object.assign(this.systemLoad, metrics);
  }

  /**
   * Reset rate limiting state (for testing)
   */
  reset(): void {
    this.requestCounts.clear();
    this.emergencyBypasses.clear();
    this.userProfiles.clear();
  }

  // Private helper methods
  private initializeDefaultConfigs(): void {
    // Medical API endpoints
    this.defaultConfigs.set('/api/medical/conditions', {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100,
      emergencyBypass: {
        enabled: true,
        maxBypassesPerDay: 5,
        requiredJustification: true,
        autoApprovalRoles: ['doctor', 'nurse', 'emergency_responder'],
        criticalEndpoints: ['/api/medical/conditions'],
        auditAllBypasses: true
      }
    });

    this.defaultConfigs.set('/api/medical/medications', {
      windowMs: 60 * 1000,
      maxRequests: 150,
      emergencyBypass: {
        enabled: true,
        maxBypassesPerDay: 10,
        requiredJustification: true,
        autoApprovalRoles: ['doctor', 'pharmacist'],
        criticalEndpoints: ['/api/medical/medications'],
        auditAllBypasses: true
      }
    });

    this.defaultConfigs.set('/api/emergency/access', {
      windowMs: 60 * 1000,
      maxRequests: 10,
      emergencyBypass: {
        enabled: true,
        maxBypassesPerDay: 20,
        requiredJustification: true,
        autoApprovalRoles: ['emergency_responder', 'doctor', 'nurse'],
        criticalEndpoints: ['/api/emergency/access'],
        auditAllBypasses: true
      }
    });
  }

  private initializeRoleBasedLimits(): void {
    // Patient limits
    const patientLimits = new Map<string, RateLimitConfig>();
    patientLimits.set('/api/medical/conditions', {
      windowMs: 60 * 1000,
      maxRequests: 50
    });
    this.roleBasedLimits.set('patient', patientLimits);

    // Healthcare provider limits
    const providerLimits = new Map<string, RateLimitConfig>();
    providerLimits.set('/api/medical/conditions', {
      windowMs: 60 * 1000,
      maxRequests: 200
    });
    this.roleBasedLimits.set('healthcare_provider', providerLimits);

    // Doctor limits
    const doctorLimits = new Map<string, RateLimitConfig>();
    doctorLimits.set('/api/medical/conditions', {
      windowMs: 60 * 1000,
      maxRequests: 500
    });
    this.roleBasedLimits.set('doctor', doctorLimits);

    // Admin limits
    const adminLimits = new Map<string, RateLimitConfig>();
    adminLimits.set('/api/medical/conditions', {
      windowMs: 60 * 1000,
      maxRequests: 1000
    });
    this.roleBasedLimits.set('admin', adminLimits);
  }

  private getRateLimitConfig(endpoint: string, userRole: UserRole, userId: string): RateLimitConfig {
    // Check for user-specific custom limits
    const userProfile = this.userProfiles.get(userId);
    if (userProfile?.customLimits.has(endpoint)) {
      return userProfile.customLimits.get(endpoint)!;
    }

    // Check for role-based limits
    const roleLimits = this.roleBasedLimits.get(userRole);
    if (roleLimits?.has(endpoint)) {
      return roleLimits.get(endpoint)!;
    }

    // Fall back to default config
    return this.defaultConfigs.get(endpoint) || {
      windowMs: 60 * 1000,
      maxRequests: 100
    };
  }

  private adaptRateLimitConfig(
    baseConfig: RateLimitConfig,
    userId: string,
    endpoint: string
  ): RateLimitConfig {
    if (!this.adaptiveLimiting) return baseConfig;

    let adaptedConfig = { ...baseConfig };

    // Adapt based on system load
    if (this.systemLoad.cpuUsage > 0.8 || this.systemLoad.memoryUsage > 0.9) {
      adaptedConfig.maxRequests = Math.floor(adaptedConfig.maxRequests * 0.7);
    }

    // Adapt based on user trust score
    const userProfile = this.userProfiles.get(userId);
    if (userProfile) {
      const trustMultiplier = Math.max(0.5, Math.min(2.0, userProfile.trustScore));
      adaptedConfig.maxRequests = Math.floor(adaptedConfig.maxRequests * trustMultiplier);
    }

    // Adapt based on emergency mode
    if (this.emergencyMode) {
      adaptedConfig.maxRequests = Math.floor(adaptedConfig.maxRequests * 0.5);
    }

    return adaptedConfig;
  }

  private generateKey(userId: string, endpoint: string): string {
    return `${userId}:${endpoint}`;
  }

  private getCurrentRequestCount(key: string, windowMs: number): number {
    const now = Date.now();
    const windowStart = Math.floor(now / windowMs) * windowMs;
    
    const userCounts = this.requestCounts.get(key) || new Map();
    return userCounts.get(windowStart) || 0;
  }

  private incrementRequestCount(key: string, windowMs: number): void {
    const now = Date.now();
    const windowStart = Math.floor(now / windowMs) * windowMs;
    
    let userCounts = this.requestCounts.get(key);
    if (!userCounts) {
      userCounts = new Map();
      this.requestCounts.set(key, userCounts);
    }
    
    const currentCount = userCounts.get(windowStart) || 0;
    userCounts.set(windowStart, currentCount + 1);
    
    // Clean up old windows
    const cutoff = now - (windowMs * 2);
    for (const [timestamp] of userCounts) {
      if (timestamp < cutoff) {
        userCounts.delete(timestamp);
      }
    }
  }

  private createRateLimitInfo(
    config: RateLimitConfig,
    currentCount: number,
    windowMs: number
  ): RateLimitInfo {
    const now = Date.now();
    const windowStart = Math.floor(now / windowMs) * windowMs;
    const resetTime = windowStart + windowMs;
    
    return {
      limit: config.maxRequests,
      remaining: Math.max(0, config.maxRequests - currentCount),
      resetTime,
      retryAfter: Math.ceil((resetTime - now) / 1000),
      totalHits: currentCount,
      windowStart
    };
  }

  private createBypassRateLimitInfo(): RateLimitInfo {
    return {
      limit: Number.MAX_SAFE_INTEGER,
      remaining: Number.MAX_SAFE_INTEGER,
      resetTime: Date.now() + 3600000,
      retryAfter: 0,
      totalHits: 0,
      windowStart: Date.now()
    };
  }

  private createDeniedRateLimitInfo(): RateLimitInfo {
    return {
      limit: 0,
      remaining: 0,
      resetTime: Date.now() + 3600000,
      retryAfter: 3600,
      totalHits: 0,
      windowStart: Date.now()
    };
  }

  private createErrorRateLimitInfo(): RateLimitInfo {
    return {
      limit: 1000,
      remaining: 999,
      resetTime: Date.now() + 60000,
      retryAfter: 0,
      totalHits: 1,
      windowStart: Date.now()
    };
  }

  private async handleEmergencyBypass(
    userId: string,
    endpoint: string,
    justification: string,
    urgency: string,
    userRole: UserRole
  ): Promise<{ approved: boolean; bypassId?: string; error?: string }> {
    // Check if bypass already exists and is valid
    const existingBypass = Array.from(this.emergencyBypasses.values())
      .find(bypass => 
        bypass.userId === userId && 
        bypass.endpoint === endpoint && 
        !bypass.used && 
        bypass.expiresAt > Date.now()
      );

    if (existingBypass) {
      // Mark as used
      existingBypass.used = true;
      existingBypass.usedAt = Date.now();
      this.emergencyBypasses.set(existingBypass.id, existingBypass);
      
      return { approved: true, bypassId: existingBypass.id };
    }

    // Request new bypass
    const bypassResult = await this.requestEmergencyBypass(
      userId,
      endpoint,
      justification,
      urgency as any,
      userRole
    );

    return {
      approved: bypassResult.approved,
      bypassId: bypassResult.bypassId,
      error: bypassResult.error
    };
  }

  private getMaxBypassesPerDay(userRole: UserRole): number {
    const limits = {
      patient: 2,
      healthcare_provider: 5,
      nurse: 10,
      doctor: 15,
      specialist: 15,
      emergency_responder: 25,
      admin: 50,
      super_admin: 100
    };
    
    return limits[userRole] || 2;
  }

  private generateBypassId(): string {
    return `bypass_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async updateUserBehaviorPattern(userId: string, endpoint: string): Promise<void> {
    const profile = await this.getUserProfile(userId);
    
    // Update common endpoints
    if (!profile.behaviorPattern.commonEndpoints.includes(endpoint)) {
      profile.behaviorPattern.commonEndpoints.push(endpoint);
      if (profile.behaviorPattern.commonEndpoints.length > 10) {
        profile.behaviorPattern.commonEndpoints.shift();
      }
    }
    
    // Update last analyzed time
    profile.behaviorPattern.lastAnalyzed = Date.now();
    
    this.userProfiles.set(userId, profile);
  }

  private async analyzeSuspiciousActivity(
    userId: string,
    endpoint: string,
    requestCount: number
  ): Promise<void> {
    const profile = await this.getUserProfile(userId);
    
    // Check for unusual request patterns
    if (requestCount > profile.behaviorPattern.averageRequestRate * 5) {
      profile.behaviorPattern.suspiciousActivityScore += 0.1;
      
      await auditLogger.logSecurityEvent(
        'suspicious_request_pattern',
        'medium',
        {
          user_id: userId,
          endpoint,
          request_count: requestCount,
          average_rate: profile.behaviorPattern.averageRequestRate,
          suspicious_score: profile.behaviorPattern.suspiciousActivityScore
        }
      );
    }
    
    this.userProfiles.set(userId, profile);
  }

  private async logRateLimitEvent(
    eventType: string,
    userId: string,
    endpoint: string,
    details: any
  ): Promise<void> {
    await auditLogger.logSecurityEvent(
      eventType,
      'low',
      {
        user_id: userId,
        endpoint,
        timestamp: Date.now(),
        ...details
      }
    );
  }

  private startSystemMonitoring(): void {
    // Start periodic system monitoring
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000); // Every 30 seconds
  }

  private updateSystemMetrics(): void {
    // Update system load metrics
    // This would typically integrate with system monitoring tools
    this.systemLoad.cpuUsage = Math.random() * 0.5; // Placeholder
    this.systemLoad.memoryUsage = Math.random() * 0.7; // Placeholder
    this.systemLoad.activeConnections = this.requestCounts.size;
    this.systemLoad.emergencyRequestsActive = this.emergencyBypasses.size;
  }
}

export default new RateLimitingService();
