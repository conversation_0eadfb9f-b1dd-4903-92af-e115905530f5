/**
 * GOAL TRACKING API ENDPOINT
 * 
 * Provides secure API endpoints for session goal management,
 * conversation steering, and progress monitoring.
 * 
 * FEATURES:
 * - Dynamic goal creation and tracking
 * - Real-time progress monitoring
 * - Conversation steering guidance
 * - Goal completion assessment
 * - HIPAA-compliant goal management
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, param, query, validationResult } = require('express-validator');
const authMiddleware = require('./middleware/authMiddleware');
const rbacMiddleware = require('./middleware/rbacMiddleware');
const auditLogger = require('./utils/auditLogger');
const { createClient } = require('@supabase/supabase-js');

const router = express.Router();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Rate limiting configuration
const goalTrackingRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 200, // 200 requests per minute (high for real-time tracking)
  message: { error: 'Rate limit exceeded for goal tracking' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => req.user?.id || req.ip
});

// Validation middleware
const validateGoalCreation = [
  body('sessionId').isUUID().withMessage('Valid session ID required'),
  body('goalType').isString().isLength({ min: 1, max: 100 }).withMessage('Goal type required'),
  body('primaryGoal').isString().isLength({ min: 5, max: 500 }).withMessage('Primary goal required (5-500 chars)'),
  body('secondaryGoals').optional().isArray().withMessage('Secondary goals must be an array'),
  body('priorityLevel').isInt({ min: 1, max: 5 }).withMessage('Priority level must be 1-5'),
  body('completionCriteria').optional().isObject().withMessage('Completion criteria must be an object'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }
    next();
  }
];

const validateProgressUpdate = [
  body('goalId').isUUID().withMessage('Valid goal ID required'),
  body('milestone').isString().isLength({ min: 1, max: 200 }).withMessage('Milestone description required'),
  body('progressIncrement').isFloat({ min: 0, max: 100 }).withMessage('Progress increment must be 0-100'),
  body('conversationTurn').isInt({ min: 1 }).withMessage('Conversation turn must be positive integer'),
  body('confidenceScore').optional().isFloat({ min: 0, max: 1 }).withMessage('Confidence score must be 0-1'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }
    next();
  }
];

/**
 * POST /api/goal-tracking/goals
 * Create a new session goal
 */
router.post('/goals',
  goalTrackingRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'create'),
  validateGoalCreation,
  async (req, res) => {
    const startTime = Date.now();
    const {
      sessionId,
      goalType,
      primaryGoal,
      secondaryGoals = [],
      priorityLevel,
      completionCriteria = {}
    } = req.body;

    try {
      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Create new goal using RPC function
      const { data: goalId, error } = await supabase
        .rpc('create_session_goal', {
          p_session_id: sessionId,
          p_user_id: req.user.id,
          p_goal_type: goalType,
          p_primary_goal: primaryGoal,
          p_secondary_goals: secondaryGoals,
          p_priority_level: priorityLevel,
          p_completion_criteria: completionCriteria
        });

      if (error) {
        throw error;
      }

      // Audit log the goal creation
      await auditLogger.logDataAccess('goal_tracking', sessionId, true, {
        operation: 'create_session_goal',
        session_id: sessionId,
        goal_id: goalId,
        goal_type: goalType,
        priority_level: priorityLevel,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          goalId,
          sessionId,
          goalType,
          primaryGoal,
          secondaryGoals,
          priorityLevel,
          progressPercentage: 0,
          createdAt: new Date().toISOString()
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Goal creation error:', error);

      await auditLogger.logDataAccess('goal_tracking', sessionId, false, {
        operation: 'create_session_goal',
        session_id: sessionId,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to create session goal'
      });
    }
  }
);

/**
 * GET /api/goal-tracking/goals/:sessionId
 * Get all goals for a session
 */
router.get('/goals/:sessionId',
  goalTrackingRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'read'),
  [param('sessionId').isUUID().withMessage('Valid session ID required')],
  async (req, res) => {
    try {
      const { sessionId } = req.params;

      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Get active session goals
      const { data: goals, error } = await supabase
        .rpc('get_active_session_goals', { p_session_id: sessionId });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: goals || []
      });

    } catch (error) {
      console.error('❌ Get goals error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve session goals'
      });
    }
  }
);

/**
 * PUT /api/goal-tracking/progress
 * Update goal progress
 */
router.put('/progress',
  goalTrackingRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'update'),
  validateProgressUpdate,
  async (req, res) => {
    const startTime = Date.now();
    const {
      goalId,
      milestone,
      progressIncrement,
      conversationTurn,
      confidenceScore = 0.8
    } = req.body;

    try {
      // Verify goal ownership through session
      const { data: goal, error: goalError } = await supabase
        .from('session_goals')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', goalId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (goalError || !goal) {
        return res.status(403).json({
          success: false,
          error: 'Goal not found or access denied'
        });
      }

      // Update goal progress using RPC function
      const { data: result, error } = await supabase
        .rpc('update_goal_progress', {
          p_goal_id: goalId,
          p_milestone: milestone,
          p_progress_increment: progressIncrement,
          p_conversation_turn: conversationTurn,
          p_agent_id: 'goal-tracker-001',
          p_confidence_score: confidenceScore
        });

      if (error) {
        throw error;
      }

      // Audit log the progress update
      await auditLogger.logDataAccess('goal_tracking', goal.session_id, true, {
        operation: 'update_goal_progress',
        goal_id: goalId,
        session_id: goal.session_id,
        milestone,
        progress_increment: progressIncrement,
        conversation_turn: conversationTurn,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          goalId,
          milestone,
          progressIncrement,
          conversationTurn,
          updatedAt: new Date().toISOString()
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Progress update error:', error);

      await auditLogger.logDataAccess('goal_tracking', 'unknown', false, {
        operation: 'update_goal_progress',
        goal_id: goalId,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to update goal progress'
      });
    }
  }
);

/**
 * GET /api/goal-tracking/steering/:sessionId
 * Get steering guidance for current conversation turn
 */
router.get('/steering/:sessionId',
  goalTrackingRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'read'),
  [
    param('sessionId').isUUID().withMessage('Valid session ID required'),
    query('turnNumber').optional().isInt({ min: 1 }).withMessage('Turn number must be positive integer')
  ],
  async (req, res) => {
    try {
      const { sessionId } = req.params;
      const { turnNumber } = req.query;

      // Verify session ownership
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, user_id')
        .eq('id', sessionId)
        .eq('user_id', req.user.id)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Session not found or access denied'
        });
      }

      // Get steering guidance
      const { data: steeringGuidance, error } = await supabase
        .rpc('get_steering_guidance', {
          p_session_id: sessionId,
          p_turn_number: turnNumber ? parseInt(turnNumber) : null
        });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: steeringGuidance || []
      });

    } catch (error) {
      console.error('❌ Steering guidance error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve steering guidance'
      });
    }
  }
);

/**
 * POST /api/goal-tracking/steering
 * Add steering guidance for agents
 */
router.post('/steering',
  goalTrackingRateLimit,
  authMiddleware,
  rbacMiddleware('consultations', 'create'),
  [
    body('sessionId').isUUID().withMessage('Valid session ID required'),
    body('goalId').isUUID().withMessage('Valid goal ID required'),
    body('turnNumber').isInt({ min: 1 }).withMessage('Turn number must be positive integer'),
    body('steeringType').isIn(['redirect', 'clarify', 'deepen', 'summarize', 'conclude', 'escalate', 'refer', 'educate', 'reassure', 'investigate'])
      .withMessage('Valid steering type required'),
    body('steeringMessage').isString().isLength({ min: 10, max: 500 }).withMessage('Steering message required (10-500 chars)'),
    body('relevanceScore').isFloat({ min: 0, max: 1 }).withMessage('Relevance score must be 0-1'),
    body('urgencyLevel').isIn(['low', 'medium', 'high', 'critical']).withMessage('Valid urgency level required')
  ],
  async (req, res) => {
    const startTime = Date.now();
    const {
      sessionId,
      goalId,
      turnNumber,
      steeringType,
      steeringMessage,
      relevanceScore,
      urgencyLevel
    } = req.body;

    try {
      // Verify session and goal ownership
      const { data: goal, error: goalError } = await supabase
        .from('session_goals')
        .select('*, consultation_sessions!inner(user_id)')
        .eq('id', goalId)
        .eq('session_id', sessionId)
        .eq('consultation_sessions.user_id', req.user.id)
        .single();

      if (goalError || !goal) {
        return res.status(403).json({
          success: false,
          error: 'Goal or session not found or access denied'
        });
      }

      // Add steering guidance using RPC function
      const { data: result, error } = await supabase
        .rpc('add_steering_guidance', {
          p_session_id: sessionId,
          p_goal_id: goalId,
          p_turn_number: turnNumber,
          p_steering_type: steeringType,
          p_steering_message: steeringMessage,
          p_relevance_score: relevanceScore,
          p_urgency_level: urgencyLevel
        });

      if (error) {
        throw error;
      }

      // Audit log the steering guidance
      await auditLogger.logDataAccess('goal_tracking', sessionId, true, {
        operation: 'add_steering_guidance',
        session_id: sessionId,
        goal_id: goalId,
        steering_type: steeringType,
        urgency_level: urgencyLevel,
        turn_number: turnNumber,
        processing_time_ms: Date.now() - startTime
      });

      res.json({
        success: true,
        data: {
          sessionId,
          goalId,
          steeringType,
          steeringMessage,
          urgencyLevel,
          turnNumber,
          createdAt: new Date().toISOString()
        },
        metadata: {
          processingTime: Date.now() - startTime
        }
      });

    } catch (error) {
      console.error('❌ Steering guidance creation error:', error);

      await auditLogger.logDataAccess('goal_tracking', sessionId, false, {
        operation: 'add_steering_guidance',
        session_id: sessionId,
        goal_id: goalId,
        error_message: error.message,
        processing_time_ms: Date.now() - startTime
      });

      res.status(500).json({
        success: false,
        error: 'Failed to add steering guidance'
      });
    }
  }
);

module.exports = router;
