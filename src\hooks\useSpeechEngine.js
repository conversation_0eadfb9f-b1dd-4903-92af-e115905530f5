import { useState, useEffect, useCallback, useRef } from 'react';
import speechEngine from '../services/speechEngine';

/**
 * Hook for managing Speech Engine interactions
 */
export function useSpeechEngine() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [error, setError] = useState(null);
  const [settings, setSettings] = useState({
    noiseReduction: true,
    vadSensitivity: 0.5,
    sampleRate: 16000,
    bitDepth: 16
  });

  const engineRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Initialize speech engine
  useEffect(() => {
    const initializeEngine = async () => {
      try {
        console.log('🎤 Initializing Speech Engine...');
        await speechEngine.initialize(settings);
        engineRef.current = speechEngine;
        setIsInitialized(true);
        console.log('✅ Speech Engine initialized successfully');
      } catch (err) {
        console.error('❌ Failed to initialize Speech Engine:', err);
        setError(err.message);
      }
    };

    initializeEngine();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Start voice activity detection
  const startListening = useCallback(async () => {
    if (!isInitialized || isListening) return;

    try {
      setIsListening(true);
      setError(null);
      
      console.log('🎙️ Starting voice detection...');
      
      // Get microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: settings.noiseReduction,
          autoGainControl: true,
          sampleRate: settings.sampleRate
        }
      });

      // Set up audio analysis
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      
      source.connect(analyserRef.current);

      // Start audio level monitoring
      monitorAudioLevel();

      // Start speech engine listening
      await speechEngine.startListening(stream, {
        onVoiceStart: () => {
          console.log('🗣️ Voice activity detected');
          setIsProcessing(true);
        },
        onVoiceEnd: async (audioData) => {
          console.log('🔇 Voice activity ended');
          setIsProcessing(false);
          
          if (audioData) {
            await processVoiceData(audioData);
          }
        },
        onError: (err) => {
          console.error('❌ Speech engine error:', err);
          setError(err.message);
        }
      });

    } catch (err) {
      console.error('❌ Failed to start listening:', err);
      setError(err.message);
      setIsListening(false);
    }
  }, [isInitialized, isListening, settings]);

  // Stop listening
  const stopListening = useCallback(async () => {
    if (!isListening) return;

    try {
      console.log('⏹️ Stopping voice detection...');
      
      setIsListening(false);
      setIsProcessing(false);
      setAudioLevel(0);

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      if (audioContextRef.current) {
        await audioContextRef.current.close();
        audioContextRef.current = null;
      }

      await speechEngine.stopListening();
      
    } catch (err) {
      console.error('❌ Error stopping listening:', err);
      setError(err.message);
    }
  }, [isListening]);

  // Monitor audio levels for visual feedback
  const monitorAudioLevel = useCallback(() => {
    if (!analyserRef.current) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const updateLevel = () => {
      if (!analyserRef.current || !isListening) return;

      analyserRef.current.getByteFrequencyData(dataArray);
      
      // Calculate average audio level
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i];
      }
      const average = sum / bufferLength;
      const normalizedLevel = Math.min(100, (average / 128) * 100);
      
      setAudioLevel(normalizedLevel);
      
      animationFrameRef.current = requestAnimationFrame(updateLevel);
    };

    updateLevel();
  }, [isListening]);

  // Process voice data
  const processVoiceData = useCallback(async (audioData) => {
    if (!isInitialized) return;

    try {
      setIsProcessing(true);
      console.log('🎵 Processing voice data...', { size: audioData.length });

      const result = await speechEngine.processAudio(audioData, {
        language: 'en',
        enhanceAudio: settings.noiseReduction
      });

      return result;
    } catch (err) {
      console.error('❌ Voice processing error:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsProcessing(false);
    }
  }, [isInitialized, settings]);

  // Update settings
  const updateSettings = useCallback((newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
    
    if (isInitialized && speechEngine.updateSettings) {
      speechEngine.updateSettings(newSettings);
    }
  }, [isInitialized]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Get engine statistics
  const getStats = useCallback(() => {
    if (!isInitialized || !speechEngine.getStats) return null;
    return speechEngine.getStats();
  }, [isInitialized]);

  // Health check
  const healthCheck = useCallback(async () => {
    if (!isInitialized) return { status: 'not_initialized' };
    
    try {
      return await speechEngine.healthCheck();
    } catch (err) {
      return { status: 'error', message: err.message };
    }
  }, [isInitialized]);

  return {
    // State
    isInitialized,
    isListening,
    isProcessing,
    audioLevel,
    error,
    settings,
    
    // Actions
    startListening,
    stopListening,
    processVoiceData,
    updateSettings,
    clearError,
    getStats,
    healthCheck,
    
    // Computed
    canListen: isInitialized && !isListening,
    isActive: isListening || isProcessing,
    hasError: Boolean(error)
  };
}
