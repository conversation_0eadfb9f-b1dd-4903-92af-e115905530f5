import React, { useState } from 'react';
import { Mic, Volume2, ChevronDown, Play, Pause, RotateCcw } from 'lucide-react';

const VoiceAudioSection = ({ isExpanded, onToggle, settings, onSettingsChange }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentVoice, setCurrentVoice] = useState(settings?.selectedVoice || 'agent-1');

  const voiceOptions = [
    {
      id: 'agent-1',
      name: 'Dr. <PERSON>',
      gender: 'Female',
      accent: 'American',
      specialty: 'General Medicine',
      description: 'Warm, professional tone with clear articulation'
    },
    {
      id: 'agent-2',
      name: 'Dr. <PERSON>',
      gender: 'Male',
      accent: 'American',
      specialty: 'Cardiology',
      description: 'Calm, reassuring voice with medical expertise'
    },
    {
      id: 'agent-3',
      name: 'Dr. <PERSON><PERSON>',
      gender: 'Female',
      accent: 'British',
      specialty: 'Pediatrics',
      description: 'Gentle, caring tone ideal for family consultations'
    },
    {
      id: 'agent-4',
      name: 'Dr. <PERSON>',
      gender: 'Male',
      accent: 'Australian',
      specialty: 'Mental Health',
      description: 'Empathetic, understanding voice for sensitive topics'
    }
  ];

  const handleVoiceSelection = (voiceId) => {
    setCurrentVoice(voiceId);
    onSettingsChange?.({
      ...settings,
      selectedVoice: voiceId
    });
  };

  const handleSpeedChange = (speed) => {
    onSettingsChange?.({
      ...settings,
      speechSpeed: speed
    });
  };

  const handleVolumeChange = (volume) => {
    onSettingsChange?.({
      ...settings,
      volume: volume
    });
  };

  const togglePreview = () => {
    setIsPlaying(!isPlaying);
    // In real implementation, this would trigger actual voice playback
  };

  const resetAudioSettings = () => {
    onSettingsChange?.({
      ...settings,
      speechSpeed: 1.0,
      volume: 0.8,
      audioEnhancements: {
        volumeAmplification: false,
        frequencyAdjustment: false,
        visualSoundIndicators: false
      }
    });
  };

  return (
    <div className="bg-surface border border-border rounded-xl overflow-hidden">
      <button
        onClick={onToggle}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-secondary-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-accent-50 rounded-lg">
            <Mic className="w-5 h-5 text-accent-600" />
          </div>
          <div className="text-left">
            <h3 className="text-lg font-semibold text-text-primary">Voice & Audio Settings</h3>
            <p className="text-sm text-text-muted">Voice synthesis and audio accessibility</p>
          </div>
        </div>
        <ChevronDown className={`w-5 h-5 text-text-muted transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
      </button>

      {isExpanded && (
        <div className="px-6 pb-6 space-y-6">
          {/* Voice Selection */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              AI Agent Voice Selection
            </label>
            <div className="space-y-3">
              {voiceOptions.map((voice) => (
                <div
                  key={voice.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    currentVoice === voice.id
                      ? 'border-primary-500 bg-primary-50' :'border-border hover:border-border-active hover:bg-secondary-50'
                  }`}
                  onClick={() => handleVoiceSelection(voice.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-medium text-text-primary">{voice.name}</h4>
                        <span className="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-md">
                          {voice.gender}
                        </span>
                        <span className="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-md">
                          {voice.accent}
                        </span>
                      </div>
                      <p className="text-sm text-text-muted mb-1">{voice.description}</p>
                      <p className="text-xs text-text-muted">Specialty: {voice.specialty}</p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        togglePreview();
                      }}
                      className="ml-4 p-2 hover:bg-white rounded-lg transition-colors"
                    >
                      {isPlaying ? (
                        <Pause className="w-5 h-5 text-primary-600" />
                      ) : (
                        <Play className="w-5 h-5 text-primary-600" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Speech Speed Control */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Speech Speed
            </label>
            <div className="space-y-3">
              <input
                type="range"
                min="0.5"
                max="2.0"
                step="0.1"
                value={settings?.speechSpeed || 1.0}
                onChange={(e) => handleSpeedChange(parseFloat(e.target.value))}
                className="w-full h-2 bg-secondary-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-sm text-text-muted">
                <span>0.5x (Slower)</span>
                <span className="font-medium text-text-primary">
                  {settings?.speechSpeed || 1.0}x
                </span>
                <span>2.0x (Faster)</span>
              </div>
            </div>
          </div>

          {/* Volume Control */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Volume Level
            </label>
            <div className="flex items-center space-x-3">
              <Volume2 className="w-5 h-5 text-text-muted" />
              <div className="flex-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings?.volume || 0.8}
                  onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                  className="w-full h-2 bg-secondary-200 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
              <span className="text-sm font-medium text-text-primary min-w-[3rem]">
                {Math.round((settings?.volume || 0.8) * 100)}%
              </span>
            </div>
          </div>

          {/* Audio Accessibility Features */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Audio Accessibility</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Volume Amplification</h5>
                  <p className="text-sm text-text-muted">Enhance audio for hearing impairments</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    audioEnhancements: {
                      ...settings?.audioEnhancements,
                      volumeAmplification: !settings?.audioEnhancements?.volumeAmplification
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.audioEnhancements?.volumeAmplification ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.audioEnhancements?.volumeAmplification ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Frequency Adjustment</h5>
                  <p className="text-sm text-text-muted">Optimize audio frequencies for better clarity</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    audioEnhancements: {
                      ...settings?.audioEnhancements,
                      frequencyAdjustment: !settings?.audioEnhancements?.frequencyAdjustment
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.audioEnhancements?.frequencyAdjustment ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.audioEnhancements?.frequencyAdjustment ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Visual Sound Indicators</h5>
                  <p className="text-sm text-text-muted">Show visual cues for audio events</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    audioEnhancements: {
                      ...settings?.audioEnhancements,
                      visualSoundIndicators: !settings?.audioEnhancements?.visualSoundIndicators
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.audioEnhancements?.visualSoundIndicators ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.audioEnhancements?.visualSoundIndicators ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Reset Button */}
          <div className="pt-4 border-t border-border">
            <button
              onClick={resetAudioSettings}
              className="flex items-center space-x-2 px-4 py-2 text-sm text-text-muted hover:text-text-primary transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Audio Settings</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceAudioSection;