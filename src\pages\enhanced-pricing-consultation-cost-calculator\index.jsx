import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calculator, Users, TrendingUp, DollarSign, FileText } from 'lucide-react';
import Header from '../../components/ui/Header';


import PricingCalculator from './components/PricingCalculator';
import PricingTiers from './components/PricingTiers';
import FamilyAccountManager from './components/FamilyAccountManager';

import ConsultationHistory from './components/ConsultationHistory';
import PricingAnalytics from './components/PricingAnalytics';
import TransparentPricingModel from './components/TransparentPricingModel';
import { useAuth } from '../../contexts/AuthContext';
import { usePayment } from '../../contexts/PaymentContext';
import Icon from '../../components/AppIcon';


const EnhancedPricingConsultationCostCalculator = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { subscriptionPlans, subscription, hasActiveSubscription, getRemainingCredits, loading } = usePayment();

  const [activeTab, setActiveTab] = useState('calculator');
  const [calculatorResult, setCalculatorResult] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState('NGN');
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // Calculator state
  const [consultationType, setConsultationType] = useState('general');
  const [duration, setDuration] = useState(15);
  const [specialistCount, setSpecialistCount] = useState(1);
  const [premiumFeatures, setPremiumFeatures] = useState([]);
  const [familyMembers, setFamilyMembers] = useState(1);

  useEffect(() => {
    if (!user) {
      navigate('/authentication-demo-access');
    }
  }, [user, navigate]);

  const tabs = [
    { id: 'calculator', label: 'Cost Calculator', icon: Calculator },
    { id: 'pricing', label: 'Pricing Tiers', icon: DollarSign },
    { id: 'family', label: 'Family Plans', icon: Users },
    { id: 'history', label: 'Cost History', icon: FileText },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp, desktop: true }
  ];

  const handleCalculatorUpdate = (result) => {
    setCalculatorResult(result);
  };

  const renderMobileTabNavigation = () => (
    <div className="lg:hidden sticky top-16 z-40 bg-white border-b border-gray-200">
      <div className="flex overflow-x-auto scrollbar-hide">
        {tabs.filter(tab => !tab.desktop).map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-shrink-0 flex items-center px-4 py-3 text-sm font-medium border-b-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600' :'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon size={16} className="mr-2" />
              {tab.label}
            </button>
          );
        })}
      </div>
    </div>
  );

  const renderDesktopSidebar = () => (
    <div className="hidden lg:block w-64 bg-white border-r border-gray-200">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Pricing Tools
        </h2>
        <nav className="space-y-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-50 text-blue-700 border border-blue-200' :'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <Icon size={16} className="mr-3" />
                {tab.label}
              </button>
            );
          })}
        </nav>

        {/* Currency Selector */}
        <div className="mt-8">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Currency
          </label>
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setSelectedCurrency('NGN')}
              className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                selectedCurrency === 'NGN' ?'bg-white text-gray-900 shadow-sm' :'text-gray-600 hover:text-gray-900'
              }`}
            >
              NGN (₦)
            </button>
            <button
              onClick={() => setSelectedCurrency('USD')}
              className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                selectedCurrency === 'USD' ?'bg-white text-gray-900 shadow-sm' :'text-gray-600 hover:text-gray-900'
              }`}
            >
              USD ($)
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            Current Plan
          </h3>
          <div className="space-y-1">
            <p className="text-sm text-blue-700">
              {subscription?.subscription_plans?.name || 'No active plan'}
            </p>
            <p className="text-xs text-blue-600">
              Credits: {getRemainingCredits() || 0}
            </p>
            {hasActiveSubscription() && (
              <p className="text-xs text-blue-600">
                Valid until: {new Date(subscription?.expires_at).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'calculator':
        return (
          <PricingCalculator
            currency={selectedCurrency}
            onCalculatorUpdate={handleCalculatorUpdate}
            consultationType={consultationType}
            setConsultationType={setConsultationType}
            duration={duration}
            setDuration={setDuration}
            specialistCount={specialistCount}
            setSpecialistCount={setSpecialistCount}
            premiumFeatures={premiumFeatures}
            setPremiumFeatures={setPremiumFeatures}
            familyMembers={familyMembers}
            setFamilyMembers={setFamilyMembers}
          />
        );
      case 'pricing':
        return <PricingTiers currency={selectedCurrency} />;
      case 'family':
        return <FamilyAccountManager currency={selectedCurrency} />;
      case 'history':
        return <ConsultationHistory currency={selectedCurrency} />;
      case 'analytics':
        return <PricingAnalytics currency={selectedCurrency} />;
      default:
        return <PricingCalculator currency={selectedCurrency} onCalculatorUpdate={handleCalculatorUpdate} />;
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Mobile Tab Navigation */}
      {renderMobileTabNavigation()}

      <div className="flex">
        {/* Desktop Sidebar */}
        {renderDesktopSidebar()}

        {/* Main Content */}
        <div className="flex-1 pt-20 lg:pt-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            {/* Header Section */}
            <div className="mb-8">
              <div className="lg:hidden mb-4">
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setSelectedCurrency('NGN')}
                    className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                      selectedCurrency === 'NGN' ?'bg-white text-gray-900 shadow-sm' :'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    NGN (₦)
                  </button>
                  <button
                    onClick={() => setSelectedCurrency('USD')}
                    className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
                      selectedCurrency === 'USD' ?'bg-white text-gray-900 shadow-sm' :'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    USD ($)
                  </button>
                </div>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Enhanced Pricing & Cost Calculator
              </h1>
              <p className="text-gray-600">
                Get transparent pricing for VoiceHealth AI consultations with our advanced cost calculator
              </p>
            </div>

            {/* Content */}
            {renderContent()}

            {/* Transparent Pricing Information */}
            <TransparentPricingModel currency={selectedCurrency} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedPricingConsultationCostCalculator;