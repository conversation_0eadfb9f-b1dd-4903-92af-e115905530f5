import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const AuthenticationForm = ({ onLogin, onSignup, onDemoAccess, isLoading }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    rememberMe: false
  });
  const [isSignupMode, setIsSignupMode] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (isSignupMode) {
      if (formData.password !== formData.confirmPassword) {
        alert('Passwords do not match');
        return;
      }
      
      await onSignup?.(formData.email, formData.password, {
        full_name: formData.fullName,
        role: 'patient'
      });
    } else {
      await onLogin?.(formData);
    }
  };

  const toggleMode = () => {
    setIsSignupMode(!isSignupMode);
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
      rememberMe: false
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-text-primary font-heading">
          {isSignupMode ? 'Create Account' : 'Welcome Back'}
        </h2>
        <p className="mt-2 text-sm text-text-muted">
          {isSignupMode 
            ? 'Sign up for your VoiceHealth AI account' 
            : 'Sign in to continue your health journey'
          }
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {isSignupMode && (
          <Input
            label="Full Name"
            type="text"
            name="fullName"
            value={formData.fullName}
            onChange={handleInputChange}
            placeholder="Enter your full name"
            required
          />
        )}

        <Input
          label="Email Address"
          type="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          placeholder="Enter your email"
          required
        />

        <div className="relative">
          <Input
            label="Password"
            type={showPassword ? "text" : "password"}
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="Enter your password"
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-9 text-text-muted hover:text-text-secondary"
          >
            {showPassword ? '👁️' : '👁️‍🗨️'}
          </button>
        </div>

        {isSignupMode && (
          <Input
            label="Confirm Password"
            type={showPassword ? "text" : "password"}
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            placeholder="Confirm your password"
            required
          />
        )}

        {!isSignupMode && (
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className="rounded border-border text-primary focus:ring-primary"
              />
              <span className="ml-2 text-sm text-text-secondary">Remember me</span>
            </label>
            <button
              type="button"
              className="text-sm text-primary hover:text-primary-hover"
            >
              Forgot password?
            </button>
          </div>
        )}

        <Button
          type="submit"
          variant="primary"
          size="lg"
          className="w-full"
          isLoading={isLoading}
          disabled={isLoading}
        >
          {isSignupMode ? 'Create Account' : 'Sign In'}
        </Button>
      </form>

      <div className="text-center space-y-4">
        <button
          onClick={toggleMode}
          className="text-sm text-primary hover:text-primary-hover"
          disabled={isLoading}
        >
          {isSignupMode 
            ? 'Already have an account? Sign In' :'Need an account? Sign Up'
          }
        </button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-border"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-surface text-text-muted">Or</span>
          </div>
        </div>

        <Button
          variant="outline"
          size="md"
          onClick={onDemoAccess}
          disabled={isLoading}
          className="w-full"
        >
          Access Demo Account
        </Button>
      </div>
    </div>
  );
};

export default AuthenticationForm;