import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const QuickActionControls = ({ 
  isVoiceMode = true,
  isPaused = false,
  isEmergencyMode = false,
  onToggleMode = () => {},
  onPauseResume = () => {},
  onEmergencyStop = () => {},
  onEndSession = () => {},
  disabled = false,
  className = ''
}) => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationType, setConfirmationType] = useState(null);

  const handleEmergencyStop = () => {
    setConfirmationType('emergency');
    setShowConfirmation(true);
  };

  const handleEndSession = () => {
    setConfirmationType('end');
    setShowConfirmation(true);
  };

  const handleConfirm = () => {
    if (confirmationType === 'emergency') {
      onEmergencyStop();
    } else if (confirmationType === 'end') {
      onEndSession();
    }
    setShowConfirmation(false);
    setConfirmationType(null);
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    setConfirmationType(null);
  };

  return (
    <div className={`bg-surface border border-border rounded-xl p-4 shadow-minimal ${className}`}>
      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-300">
          <div className="bg-surface border border-border rounded-xl p-6 max-w-sm mx-4 shadow-floating">
            <div className="flex items-center space-x-3 mb-4">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                confirmationType === 'emergency' ? 'bg-error-50' : 'bg-warning-50'
              }`}>
                <Icon 
                  name={confirmationType === 'emergency' ? 'AlertTriangle' : 'LogOut'}
                  size={20}
                  color={confirmationType === 'emergency' ? 'var(--color-error)' : 'var(--color-warning)'}
                />
              </div>
              <div>
                <h3 className="font-semibold text-text-primary font-heading">
                  {confirmationType === 'emergency' ? 'Emergency Stop' : 'End Session'}
                </h3>
                <p className="text-sm text-text-secondary font-caption">
                  {confirmationType === 'emergency' ?'This will immediately stop all agents' :'This will end your consultation session'}
                </p>
              </div>
            </div>
            
            <p className="text-sm text-text-primary mb-6">
              {confirmationType === 'emergency' ?'Are you sure you want to perform an emergency stop? This action cannot be undone.' :'Are you sure you want to end this session? You can review the summary afterwards.'}
            </p>
            
            <div className="flex space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant={confirmationType === 'emergency' ? 'danger' : 'warning'}
                size="sm"
                onClick={handleConfirm}
                className="flex-1"
              >
                {confirmationType === 'emergency' ? 'Stop Now' : 'End Session'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-text-primary font-heading">
          Quick Actions
        </h3>
        <div className="flex items-center space-x-2">
          {isEmergencyMode && (
            <div className="flex items-center space-x-1 px-2 py-1 bg-error-50 rounded-full">
              <div className="w-2 h-2 bg-error-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-error-600 font-medium">
                Emergency Mode
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Primary Controls */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        {/* Voice/Text Mode Toggle */}
        <Button
          variant={isVoiceMode ? "primary" : "outline"}
          size="sm"
          onClick={onToggleMode}
          disabled={disabled}
          iconName={isVoiceMode ? "Mic" : "MessageSquare"}
          iconPosition="left"
          className="justify-center"
        >
          {isVoiceMode ? 'Voice Mode' : 'Text Mode'}
        </Button>

        {/* Pause/Resume */}
        <Button
          variant={isPaused ? "success" : "warning"}
          size="sm"
          onClick={onPauseResume}
          disabled={disabled}
          iconName={isPaused ? "Play" : "Pause"}
          iconPosition="left"
          className="justify-center"
        >
          {isPaused ? 'Resume' : 'Pause'}
        </Button>
      </div>

      {/* Secondary Controls */}
      <div className="space-y-3">
        {/* Session Controls */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.href = '/session-dashboard-history'}
            iconName="History"
            iconPosition="left"
            className="flex-1 justify-center"
          >
            History
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.href = '/patient-profile-setup'}
            iconName="Settings"
            iconPosition="left"
            className="flex-1 justify-center"
          >
            Settings
          </Button>
        </div>

        {/* Critical Actions */}
        <div className="flex space-x-3">
          <Button
            variant="danger"
            size="sm"
            onClick={handleEmergencyStop}
            disabled={disabled}
            iconName="Square"
            iconPosition="left"
            className="flex-1 justify-center"
          >
            Emergency Stop
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleEndSession}
            disabled={disabled}
            iconName="LogOut"
            iconPosition="left"
            className="flex-1 justify-center"
          >
            End Session
          </Button>
        </div>
      </div>

      {/* Status Information */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className={`text-sm font-medium ${
              isVoiceMode ? 'text-primary-600' : 'text-text-secondary'
            }`}>
              {isVoiceMode ? 'Voice' : 'Text'}
            </div>
            <div className="text-xs text-text-secondary">Mode</div>
          </div>
          
          <div>
            <div className={`text-sm font-medium ${
              isPaused ? 'text-warning-600' : 'text-success-600'
            }`}>
              {isPaused ? 'Paused' : 'Active'}
            </div>
            <div className="text-xs text-text-secondary">Status</div>
          </div>
          
          <div>
            <div className={`text-sm font-medium ${
              isEmergencyMode ? 'text-error-600' : 'text-success-600'
            }`}>
              {isEmergencyMode ? 'Emergency' : 'Normal'}
            </div>
            <div className="text-xs text-text-secondary">Priority</div>
          </div>
        </div>
      </div>

      {/* Quick Tips */}
      <div className="mt-4 p-3 bg-secondary-50 rounded-lg">
        <div className="flex items-start space-x-2">
          <Icon name="Info" size={14} color="var(--color-text-secondary)" />
          <div>
            <p className="text-xs text-text-secondary font-caption">
              <strong>Tip:</strong> Use voice mode for hands-free consultation, 
              or switch to text mode for detailed input. Emergency stop is available anytime.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickActionControls;