/**
 * RAG SYSTEM COMPREHENSIVE TEST SUITE
 * 
 * Tests the complete RAG (Retrieval-Augmented Generation) system including:
 * - Vector search functionality
 * - RAG tool execution
 * - Agent integration with RAG
 * - HIPAA compliance and security
 * - Performance and reliability
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import { vectorSearchService } from '../services/VectorSearchService';
import { documentIngestionPipeline } from '../services/DocumentIngestionPipeline';
import { RAGTool } from '../tools/RAGTool';
import { CardiologistAgent } from '../agents/CardiologistAgent';
import { GeneralPractitionerAgent } from '../agents/GeneralPractitionerAgent';
import { memoryManager } from '../services/MemoryManager';

// Test data
const testDocuments = [
  {
    title: 'Cardiovascular Disease Prevention Guidelines',
    content: 'Primary prevention of cardiovascular disease involves lifestyle modifications including regular exercise, healthy diet, smoking cessation, and blood pressure management. Evidence shows that these interventions can reduce cardiovascular risk by up to 80%.',
    documentType: 'guideline' as const,
    specialty: 'cardiology',
    source: 'American Heart Association',
    sourceUrl: 'https://www.heart.org/guidelines',
    evidenceLevel: 'A' as const,
    metadata: { year: 2024, version: '1.0' }
  },
  {
    title: 'Diabetes Management Protocol',
    content: 'Type 2 diabetes management requires a comprehensive approach including glycemic control, cardiovascular risk reduction, and regular monitoring. HbA1c targets should be individualized based on patient factors.',
    documentType: 'protocol' as const,
    specialty: 'endocrinology',
    source: 'American Diabetes Association',
    sourceUrl: 'https://www.diabetes.org/protocols',
    evidenceLevel: 'A' as const,
    metadata: { year: 2024, version: '2.1' }
  },
  {
    title: 'Mental Health Screening in Primary Care',
    content: 'Regular mental health screening in primary care settings can improve early detection and treatment of depression and anxiety disorders. PHQ-9 and GAD-7 are validated screening tools.',
    documentType: 'advisory' as const,
    specialty: 'psychiatry',
    source: 'US Preventive Services Task Force',
    sourceUrl: 'https://www.uspreventiveservicestaskforce.org',
    evidenceLevel: 'B' as const,
    metadata: { year: 2023, version: '1.5' }
  }
];

describe('RAG System Integration Tests', () => {
  let ragTool: RAGTool;
  let cardiologistAgent: CardiologistAgent;
  let gpAgent: GeneralPractitionerAgent;

  beforeAll(async () => {
    // Initialize components
    ragTool = new RAGTool();
    cardiologistAgent = new CardiologistAgent(memoryManager);
    gpAgent = new GeneralPractitionerAgent(memoryManager);

    // Initialize tools
    await ragTool.initialize();

    // Ingest test documents
    console.log('📚 Ingesting test documents...');
    for (const doc of testDocuments) {
      await documentIngestionPipeline.ingestDocument(doc, {
        chunkSize: 500,
        embeddingModel: 'openai',
        skipDuplicates: true
      });
    }
  });

  afterAll(async () => {
    await ragTool.shutdown();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Vector Search Service', () => {
    test('should perform semantic search successfully', async () => {
      const searchQuery = {
        query: 'cardiovascular disease prevention',
        maxResults: 3,
        minRelevanceScore: 0.5,
        specialtyFilter: 'cardiology'
      };

      const result = await vectorSearchService.searchDocuments(
        searchQuery,
        'test-agent-001',
        'test-session-001'
      );

      expect(result).toBeDefined();
      expect(result.documents).toBeInstanceOf(Array);
      expect(result.totalResults).toBeGreaterThan(0);
      expect(result.searchTime).toBeGreaterThan(0);
      expect(result.sources).toBeInstanceOf(Array);
      expect(result.averageRelevance).toBeGreaterThan(0);

      // Verify document structure
      if (result.documents.length > 0) {
        const doc = result.documents[0];
        expect(doc).toHaveProperty('id');
        expect(doc).toHaveProperty('title');
        expect(doc).toHaveProperty('content');
        expect(doc).toHaveProperty('specialty');
        expect(doc).toHaveProperty('evidenceLevel');
        expect(doc).toHaveProperty('relevanceScore');
      }
    });

    test('should filter by document type', async () => {
      const searchQuery = {
        query: 'medical guidelines',
        maxResults: 5,
        documentTypes: ['guideline'],
        minRelevanceScore: 0.3
      };

      const result = await vectorSearchService.searchDocuments(
        searchQuery,
        'test-agent-001',
        'test-session-001'
      );

      expect(result).toBeDefined();
      result.documents.forEach(doc => {
        expect(doc.documentType).toBe('guideline');
      });
    });

    test('should filter by evidence level', async () => {
      const searchQuery = {
        query: 'medical research',
        maxResults: 5,
        evidenceLevels: ['A', 'B'],
        minRelevanceScore: 0.3
      };

      const result = await vectorSearchService.searchDocuments(
        searchQuery,
        'test-agent-001',
        'test-session-001'
      );

      expect(result).toBeDefined();
      result.documents.forEach(doc => {
        expect(['A', 'B']).toContain(doc.evidenceLevel);
      });
    });

    test('should handle empty search results gracefully', async () => {
      const searchQuery = {
        query: 'nonexistent medical condition xyz123',
        maxResults: 5,
        minRelevanceScore: 0.9
      };

      const result = await vectorSearchService.searchDocuments(
        searchQuery,
        'test-agent-001',
        'test-session-001'
      );

      expect(result).toBeDefined();
      expect(result.documents).toHaveLength(0);
      expect(result.totalResults).toBe(0);
    });

    test('should perform health check successfully', async () => {
      const healthCheck = await vectorSearchService.healthCheck();
      
      expect(healthCheck).toBeDefined();
      expect(healthCheck).toHaveProperty('healthy');
      expect(healthCheck).toHaveProperty('details');
      expect(typeof healthCheck.healthy).toBe('boolean');
      expect(typeof healthCheck.details).toBe('string');
    });
  });

  describe('RAG Tool', () => {
    test('should execute knowledge retrieval successfully', async () => {
      const request = {
        query: 'cardiovascular disease prevention guidelines',
        parameters: {
          maxResults: 3,
          minRelevanceScore: 0.6,
          specialtyFilter: 'cardiology'
        },
        capabilities: ['knowledge_retrieval'],
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      const response = await ragTool.execute(request);

      expect(response).toBeDefined();
      expect(response.success).toBe(true);
      expect(response.toolId).toBe('rag-tool-001');
      expect(response.toolName).toBe('Medical Knowledge Retrieval');
      expect(response.confidence).toBeGreaterThan(0);
      expect(response.executionTime).toBeGreaterThan(0);

      if (response.result) {
        expect(response.result).toHaveProperty('documents');
        expect(response.result).toHaveProperty('summary');
        expect(response.result).toHaveProperty('sources');
        expect(response.result).toHaveProperty('citations');
      }
    });

    test('should handle tool request validation', async () => {
      const invalidRequest = {
        query: '', // Empty query
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      const response = await ragTool.execute(invalidRequest);

      expect(response).toBeDefined();
      expect(response.success).toBe(false);
      expect(response.error).toBeDefined();
    });

    test('should determine if it can handle requests', () => {
      const knowledgeRequest = {
        query: 'what are the latest guidelines for diabetes management',
        capabilities: ['knowledge_retrieval'],
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      const nonKnowledgeRequest = {
        query: 'hello how are you',
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      expect(ragTool.canHandle(knowledgeRequest)).toBe(true);
      expect(ragTool.canHandle(nonKnowledgeRequest)).toBe(false);
    });

    test('should generate appropriate citations', async () => {
      const request = {
        query: 'cardiovascular prevention',
        parameters: {
          maxResults: 2,
          citationFormat: 'simple'
        },
        capabilities: ['knowledge_retrieval'],
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      const response = await ragTool.execute(request);

      if (response.success && response.citations) {
        expect(response.citations).toBeInstanceOf(Array);
        response.citations.forEach(citation => {
          expect(citation).toHaveProperty('source');
          expect(citation).toHaveProperty('title');
          expect(citation).toHaveProperty('evidenceLevel');
          expect(citation).toHaveProperty('relevanceScore');
        });
      }
    });

    test('should perform health check', async () => {
      const healthCheck = await ragTool.healthCheck();
      
      expect(healthCheck).toBeDefined();
      expect(healthCheck).toHaveProperty('healthy');
      expect(healthCheck).toHaveProperty('details');
    });
  });

  describe('Agent Integration with RAG', () => {
    test('should integrate RAG tool with CardiologistAgent', async () => {
      const tools = cardiologistAgent.getAvailableTools();
      
      expect(tools).toBeInstanceOf(Array);
      expect(tools.length).toBeGreaterThan(0);
      
      const ragTool = tools.find(tool => tool.id === 'rag-tool-001');
      expect(ragTool).toBeDefined();
      expect(ragTool?.name).toBe('Medical Knowledge Retrieval');
    });

    test('should integrate RAG tool with GeneralPractitionerAgent', async () => {
      const tools = gpAgent.getAvailableTools();
      
      expect(tools).toBeInstanceOf(Array);
      expect(tools.length).toBeGreaterThan(0);
      
      const ragTool = tools.find(tool => tool.id === 'rag-tool-001');
      expect(ragTool).toBeDefined();
      expect(ragTool?.name).toBe('Medical Knowledge Retrieval');
    });

    test('should verify agents can use RAG tool', () => {
      expect(cardiologistAgent.canUseTool('rag-tool-001')).toBe(true);
      expect(gpAgent.canUseTool('rag-tool-001')).toBe(true);
      expect(cardiologistAgent.canUseTool('nonexistent-tool')).toBe(false);
    });

    test('should handle agent message with RAG enhancement', async () => {
      const request = {
        sessionId: 'test-session-001',
        userMessage: 'What are the latest guidelines for preventing heart disease?',
        urgencyLevel: 'medium' as const
      };

      const response = await cardiologistAgent.handleMessage(request);

      expect(response).toBeDefined();
      expect(response.agentId).toBe('cardiologist-agent-001');
      expect(response.content).toBeDefined();
      expect(response.confidence).toBeGreaterThan(0);
      
      // Check for RAG enhancement in metadata
      if (response.metadata) {
        expect(response.metadata).toHaveProperty('ragEnhanced');
        expect(response.metadata.ragEnhanced).toBe(true);
      }
    });
  });

  describe('HIPAA Compliance and Security', () => {
    test('should log document access for audit trail', async () => {
      const searchQuery = {
        query: 'diabetes management',
        maxResults: 2,
        specialtyFilter: 'endocrinology'
      };

      // Mock the access logging
      const logSpy = vi.spyOn(console, 'log');

      await vectorSearchService.searchDocuments(
        searchQuery,
        'test-agent-001',
        'test-session-001'
      );

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('Logged access to')
      );

      logSpy.mockRestore();
    });

    test('should validate request parameters for security', async () => {
      const maliciousRequest = {
        query: 'test',
        parameters: {
          maxResults: 10000, // Excessive results
          minRelevanceScore: -1 // Invalid score
        },
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      const response = await ragTool.execute(maliciousRequest);
      
      // Should handle gracefully without exposing system details
      expect(response).toBeDefined();
      expect(response.success).toBeDefined();
    });

    test('should not expose sensitive system information in errors', async () => {
      const request = {
        query: 'test query',
        sessionId: 'test-session-001',
        agentId: 'test-agent-001'
      };

      // Force an error condition
      vi.spyOn(vectorSearchService, 'searchDocuments').mockRejectedValueOnce(
        new Error('Database connection failed with credentials xyz123')
      );

      const response = await ragTool.execute(request);

      expect(response.success).toBe(false);
      if (response.error) {
        // Should not contain sensitive information
        expect(response.error).not.toContain('credentials');
        expect(response.error).not.toContain('password');
        expect(response.error).not.toContain('xyz123');
      }
    });
  });

  describe('Performance and Reliability', () => {
    test('should complete searches within acceptable time limits', async () => {
      const startTime = Date.now();
      
      const searchQuery = {
        query: 'medical guidelines',
        maxResults: 5,
        minRelevanceScore: 0.5
      };

      const result = await vectorSearchService.searchDocuments(
        searchQuery,
        'test-agent-001',
        'test-session-001'
      );

      const executionTime = Date.now() - startTime;
      
      expect(result).toBeDefined();
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    test('should handle concurrent requests', async () => {
      const requests = Array.from({ length: 5 }, (_, i) => ({
        query: `medical query ${i}`,
        maxResults: 3,
        minRelevanceScore: 0.5
      }));

      const promises = requests.map(query => 
        vectorSearchService.searchDocuments(
          query,
          'test-agent-001',
          'test-session-001'
        )
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.documents).toBeInstanceOf(Array);
      });
    });

    test('should maintain performance metrics', () => {
      const metrics = ragTool.getPerformanceMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics).toHaveProperty('totalExecutions');
      expect(metrics).toHaveProperty('averageExecutionTime');
      expect(metrics).toHaveProperty('successRate');
      expect(metrics).toHaveProperty('averageConfidence');
      expect(metrics).toHaveProperty('lastExecution');
    });
  });
});
