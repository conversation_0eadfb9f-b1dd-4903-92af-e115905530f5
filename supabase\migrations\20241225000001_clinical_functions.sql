-- Clinical Decision Support Database Functions
-- VoiceHealth AI - Phase 1 Implementation
-- Created: 2025-01-06

-- =====================================================
-- CLINICAL RECOMMENDATION FUNCTIONS
-- =====================================================

-- Function to get clinical recommendations with cultural context
CREATE OR REPLACE FUNCTION get_clinical_recommendations(
    p_condition TEXT,
    p_region TEXT DEFAULT 'GH',
    p_cultural_context JSONB DEFAULT '{}'::JSONB,
    p_evidence_level TEXT[] DEFAULT ARRAY['A', 'B', 'C', 'D'],
    p_specialty TEXT DEFAULT NULL,
    p_age_group TEXT DEFAULT 'adult'
)
RETURNS TABLE (
    pathway_id UUID,
    condition TEXT,
    pathway_steps JSONB,
    evidence_level TEXT,
    cultural_adaptations JSONB,
    emergency_protocols JSONB,
    traditional_medicine_considerations JSONB,
    confidence_score DECIMAL,
    specialty TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.condition,
        cp.pathway_steps,
        cp.evidence_level,
        cp.cultural_adaptations,
        cp.emergency_protocols,
        cp.traditional_medicine_considerations,
        CASE 
            WHEN cp.evidence_level = 'A' THEN 0.95
            WHEN cp.evidence_level = 'B' THEN 0.85
            WHEN cp.evidence_level = 'C' THEN 0.75
            ELSE 0.65
        END as confidence_score,
        cp.specialty
    FROM clinical_pathways cp
    WHERE 
        (cp.condition ILIKE '%' || p_condition || '%' OR p_condition ILIKE '%' || cp.condition || '%')
        AND cp.region = p_region
        AND cp.evidence_level = ANY(p_evidence_level)
        AND (p_specialty IS NULL OR cp.specialty = p_specialty)
        AND cp.age_group = p_age_group
    ORDER BY 
        CASE cp.evidence_level 
            WHEN 'A' THEN 1 
            WHEN 'B' THEN 2 
            WHEN 'C' THEN 3 
            ELSE 4 
        END,
        cp.updated_at DESC
    LIMIT 10;
END;
$$;

-- Function to check comprehensive drug interactions
CREATE OR REPLACE FUNCTION check_comprehensive_drug_interactions(
    p_medications TEXT[],
    p_traditional_remedies TEXT[] DEFAULT ARRAY[]::TEXT[],
    p_population TEXT DEFAULT 'general'
)
RETURNS TABLE (
    interaction_id UUID,
    drug_a TEXT,
    drug_b TEXT,
    traditional_remedy TEXT,
    interaction_type TEXT,
    severity TEXT,
    mechanism TEXT,
    clinical_effects TEXT,
    clinical_management TEXT,
    evidence_level TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    -- Modern drug-drug interactions
    SELECT 
        dia.id,
        dia.drug_a,
        dia.drug_b,
        dia.traditional_remedy,
        dia.interaction_type,
        dia.severity,
        dia.mechanism,
        dia.clinical_effects,
        dia.clinical_management,
        dia.evidence_level
    FROM drug_interactions_african dia
    WHERE 
        (dia.drug_a = ANY(p_medications) AND dia.drug_b = ANY(p_medications) AND dia.drug_a != dia.drug_b)
        AND (dia.population_specific = false OR p_population IN ('West_African', 'East_African', 'Southern_African'))
        AND dia.traditional_remedy IS NULL
    
    UNION ALL
    
    -- Drug-traditional medicine interactions
    SELECT 
        dia.id,
        dia.drug_a,
        dia.drug_b,
        dia.traditional_remedy,
        dia.interaction_type,
        dia.severity,
        dia.mechanism,
        dia.clinical_effects,
        dia.clinical_management,
        dia.evidence_level
    FROM drug_interactions_african dia
    WHERE 
        dia.drug_a = ANY(p_medications)
        AND dia.traditional_remedy = ANY(p_traditional_remedies)
        AND dia.traditional_remedy IS NOT NULL
    
    ORDER BY 
        CASE severity 
            WHEN 'contraindicated' THEN 1
            WHEN 'severe' THEN 2
            WHEN 'moderate' THEN 3
            ELSE 4
        END,
        CASE evidence_level
            WHEN 'A' THEN 1
            WHEN 'B' THEN 2
            WHEN 'C' THEN 3
            ELSE 4
        END;
END;
$$;

-- Function to get dosage adjustments for African populations
CREATE OR REPLACE FUNCTION get_african_dosage_adjustments(
    p_medications TEXT[],
    p_population TEXT DEFAULT 'general',
    p_age_group TEXT DEFAULT 'adult'
)
RETURNS TABLE (
    medication TEXT,
    population TEXT,
    indication TEXT,
    standard_dose TEXT,
    adjusted_dose TEXT,
    adjustment_factor DECIMAL,
    adjustment_reason TEXT,
    genetic_basis TEXT[],
    evidence_level TEXT,
    monitoring_requirements TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        adg.medication,
        adg.population,
        adg.indication,
        adg.standard_dose,
        adg.adjusted_dose,
        adg.adjustment_factor,
        adg.adjustment_reason,
        adg.genetic_basis,
        adg.evidence_level,
        adg.monitoring_requirements
    FROM african_dosing_guidelines adg
    WHERE 
        adg.medication = ANY(p_medications)
        AND (adg.population = p_population OR adg.population = 'general')
        AND adg.age_group = p_age_group
    ORDER BY 
        CASE adg.evidence_level
            WHEN 'A' THEN 1
            WHEN 'B' THEN 2
            WHEN 'C' THEN 3
            ELSE 4
        END,
        adg.adjustment_factor DESC;
END;
$$;

-- =====================================================
-- TRADITIONAL MEDICINE FUNCTIONS
-- =====================================================

-- Function to search traditional remedies by condition or symptoms
CREATE OR REPLACE FUNCTION search_traditional_remedies(
    p_search_term TEXT,
    p_region TEXT DEFAULT NULL,
    p_language TEXT DEFAULT 'en'
)
RETURNS TABLE (
    remedy_id UUID,
    name TEXT,
    local_name TEXT,
    traditional_uses TEXT[],
    safety_profile JSONB,
    contraindications TEXT[],
    drug_interactions JSONB,
    evidence_level TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tr.id,
        tr.name,
        COALESCE(tr.local_names ->> p_language, tr.name) as local_name,
        tr.traditional_uses,
        tr.safety_profile,
        tr.contraindications,
        tr.drug_interactions,
        tr.evidence_level
    FROM traditional_remedies tr
    WHERE 
        (tr.name ILIKE '%' || p_search_term || '%' 
         OR EXISTS (SELECT 1 FROM unnest(tr.traditional_uses) as use WHERE use ILIKE '%' || p_search_term || '%')
         OR tr.local_names::text ILIKE '%' || p_search_term || '%')
        AND (p_region IS NULL OR p_region = ANY(tr.regions))
    ORDER BY 
        CASE 
            WHEN tr.name ILIKE p_search_term || '%' THEN 1
            WHEN tr.name ILIKE '%' || p_search_term || '%' THEN 2
            ELSE 3
        END,
        CASE tr.evidence_level
            WHEN 'A' THEN 1
            WHEN 'B' THEN 2
            WHEN 'C' THEN 3
            ELSE 4
        END
    LIMIT 20;
END;
$$;

-- Function to get traditional medicine safety warnings
CREATE OR REPLACE FUNCTION get_traditional_medicine_warnings(
    p_remedy_names TEXT[],
    p_medications TEXT[] DEFAULT ARRAY[]::TEXT[],
    p_patient_conditions TEXT[] DEFAULT ARRAY[]::TEXT[]
)
RETURNS TABLE (
    remedy_name TEXT,
    warning_type TEXT,
    warning_message TEXT,
    severity TEXT,
    evidence_level TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    -- Contraindication warnings
    SELECT 
        tr.name,
        'contraindication' as warning_type,
        'Contraindicated for: ' || array_to_string(tr.contraindications, ', ') as warning_message,
        'high' as severity,
        tr.evidence_level
    FROM traditional_remedies tr
    WHERE 
        tr.name = ANY(p_remedy_names)
        AND tr.contraindications IS NOT NULL
        AND array_length(tr.contraindications, 1) > 0
    
    UNION ALL
    
    -- Drug interaction warnings
    SELECT 
        tr.name,
        'drug_interaction' as warning_type,
        'May interact with prescribed medications. Consult healthcare provider.' as warning_message,
        'medium' as severity,
        tr.evidence_level
    FROM traditional_remedies tr
    WHERE 
        tr.name = ANY(p_remedy_names)
        AND tr.drug_interactions IS NOT NULL
        AND jsonb_array_length(tr.drug_interactions) > 0
        AND array_length(p_medications, 1) > 0
    
    ORDER BY 
        CASE severity
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            ELSE 3
        END;
END;
$$;

-- =====================================================
-- CULTURAL ADAPTATION FUNCTIONS
-- =====================================================

-- Function to get cultural communication preferences
CREATE OR REPLACE FUNCTION get_cultural_communication_style(
    p_user_id UUID
)
RETURNS TABLE (
    culture_code TEXT,
    language_preference TEXT,
    communication_style JSONB,
    religious_considerations JSONB,
    family_involvement_preferences JSONB,
    traditional_medicine_openness INTEGER,
    preferred_explanation_style TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ecp.culture_code,
        ecp.language_preference,
        ecp.communication_style,
        ecp.religious_considerations,
        ecp.family_involvement_preferences,
        ecp.traditional_medicine_openness,
        ecp.preferred_explanation_style
    FROM enhanced_cultural_profiles ecp
    WHERE ecp.user_id = p_user_id;
END;
$$;

-- Function to translate medical terms
CREATE OR REPLACE FUNCTION translate_medical_terms(
    p_terms TEXT[],
    p_target_language TEXT DEFAULT 'en',
    p_cultural_context TEXT DEFAULT NULL
)
RETURNS TABLE (
    english_term TEXT,
    translated_term TEXT,
    cultural_context TEXT,
    usage_notes TEXT,
    sensitivity_level TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mtt.english_term,
        COALESCE(mtt.translations ->> p_target_language, mtt.english_term) as translated_term,
        mtt.cultural_context,
        mtt.usage_notes,
        mtt.sensitivity_level
    FROM medical_term_translations mtt
    WHERE 
        mtt.english_term = ANY(p_terms)
        OR mtt.translations ? p_target_language
    ORDER BY mtt.english_term;
END;
$$;

-- =====================================================
-- EMERGENCY PROTOCOL FUNCTIONS
-- =====================================================

-- Function to get emergency protocols with cultural adaptations
CREATE OR REPLACE FUNCTION get_emergency_protocols(
    p_condition TEXT,
    p_region TEXT DEFAULT 'GH',
    p_cultural_profile JSONB DEFAULT '{}'::JSONB
)
RETURNS TABLE (
    protocol_id UUID,
    condition TEXT,
    emergency_steps JSONB,
    cultural_adaptations JSONB,
    emergency_contacts JSONB,
    response_time_target INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.condition,
        cp.emergency_protocols,
        cp.cultural_adaptations,
        COALESCE(p_cultural_profile -> 'emergency_contact_hierarchy', '{}'::JSONB) as emergency_contacts,
        2 as response_time_target -- 2 seconds requirement
    FROM clinical_pathways cp
    WHERE 
        cp.condition ILIKE '%' || p_condition || '%'
        AND cp.region = p_region
        AND cp.emergency_protocols IS NOT NULL
        AND jsonb_array_length(cp.emergency_protocols) > 0
    ORDER BY cp.evidence_level
    LIMIT 1;
END;
$$;

-- =====================================================
-- ANALYTICS AND REPORTING FUNCTIONS
-- =====================================================

-- Function to get clinical decision support usage statistics
CREATE OR REPLACE FUNCTION get_clinical_usage_stats(
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    total_recommendations INTEGER,
    evidence_level_breakdown JSONB,
    top_conditions JSONB,
    regional_usage JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_recommendations,
        jsonb_build_object(
            'A', COUNT(*) FILTER (WHERE evidence_level = 'A'),
            'B', COUNT(*) FILTER (WHERE evidence_level = 'B'),
            'C', COUNT(*) FILTER (WHERE evidence_level = 'C'),
            'D', COUNT(*) FILTER (WHERE evidence_level = 'D')
        ) as evidence_level_breakdown,
        jsonb_agg(DISTINCT jsonb_build_object('condition', condition, 'count', 1)) as top_conditions,
        jsonb_agg(DISTINCT jsonb_build_object('region', region, 'count', 1)) as regional_usage
    FROM clinical_pathways
    WHERE created_at BETWEEN p_start_date AND p_end_date;
END;
$$;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_clinical_recommendations TO authenticated;
GRANT EXECUTE ON FUNCTION check_comprehensive_drug_interactions TO authenticated;
GRANT EXECUTE ON FUNCTION get_african_dosage_adjustments TO authenticated;
GRANT EXECUTE ON FUNCTION search_traditional_remedies TO authenticated;
GRANT EXECUTE ON FUNCTION get_traditional_medicine_warnings TO authenticated;
GRANT EXECUTE ON FUNCTION get_cultural_communication_style TO authenticated;
GRANT EXECUTE ON FUNCTION translate_medical_terms TO authenticated;
GRANT EXECUTE ON FUNCTION get_emergency_protocols TO authenticated;

-- Grant analytics functions to admin role
GRANT EXECUTE ON FUNCTION get_clinical_usage_stats TO authenticated;

-- =====================================================
-- FUNCTION COMMENTS
-- =====================================================

COMMENT ON FUNCTION get_clinical_recommendations IS 'Get evidence-based clinical recommendations with cultural context';
COMMENT ON FUNCTION check_comprehensive_drug_interactions IS 'Check drug interactions including traditional medicine';
COMMENT ON FUNCTION get_african_dosage_adjustments IS 'Get population-specific dosage adjustments for African populations';
COMMENT ON FUNCTION search_traditional_remedies IS 'Search traditional remedies by condition with safety information';
COMMENT ON FUNCTION get_traditional_medicine_warnings IS 'Get safety warnings for traditional medicine use';
COMMENT ON FUNCTION get_cultural_communication_style IS 'Get user cultural communication preferences';
COMMENT ON FUNCTION translate_medical_terms IS 'Translate medical terms with cultural context';
COMMENT ON FUNCTION get_emergency_protocols IS 'Get emergency protocols with cultural adaptations';

-- =====================================================
-- ENHANCED VECTOR SEARCH FUNCTIONS
-- =====================================================

-- Function to search documents with cultural context filtering
CREATE OR REPLACE FUNCTION match_documents_with_cultural_context(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5,
    evidence_levels text[] DEFAULT ARRAY['A', 'B', 'C', 'D'],
    cultural_context text[] DEFAULT NULL,
    region_filter text DEFAULT NULL,
    population_specific text DEFAULT 'general',
    traditional_medicine_relevant boolean DEFAULT false
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    document_type text,
    specialty text,
    source text,
    source_url text,
    evidence_level text,
    last_updated timestamp with time zone,
    similarity float,
    geographic_relevance float,
    metadata jsonb,
    cultural_context text[],
    region_applicability text[],
    traditional_medicine_refs text[],
    population_specific boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        md.id,
        md.title,
        md.content,
        md.document_type,
        md.specialty,
        md.source,
        md.source_url,
        md.evidence_level,
        md.last_updated,
        (1 - (md.embedding <=> query_embedding)) as similarity,
        CASE
            WHEN region_filter IS NOT NULL AND md.region_applicability @> ARRAY[region_filter] THEN 1.0
            WHEN region_filter IS NOT NULL THEN 0.5
            ELSE 0.8
        END as geographic_relevance,
        md.metadata,
        md.cultural_context,
        md.region_applicability,
        md.traditional_medicine_refs,
        CASE
            WHEN population_specific != 'general' AND md.specialty LIKE '%African%' THEN true
            ELSE false
        END as population_specific
    FROM medical_documents md
    WHERE
        (1 - (md.embedding <=> query_embedding)) > match_threshold
        AND (evidence_levels IS NULL OR md.evidence_level = ANY(evidence_levels))
        AND (cultural_context IS NULL OR md.cultural_context && cultural_context)
        AND (region_filter IS NULL OR md.region_applicability @> ARRAY[region_filter] OR md.region_applicability IS NULL)
        AND (NOT traditional_medicine_relevant OR md.traditional_medicine_refs IS NOT NULL)
    ORDER BY
        -- Prioritize by evidence level and cultural relevance
        CASE md.evidence_level
            WHEN 'A' THEN 1
            WHEN 'B' THEN 2
            WHEN 'C' THEN 3
            ELSE 4
        END,
        (1 - (md.embedding <=> query_embedding)) DESC,
        CASE
            WHEN region_filter IS NOT NULL AND md.region_applicability @> ARRAY[region_filter] THEN 1
            ELSE 2
        END
    LIMIT match_count;
END;
$$;

-- Grant permissions for the new function
GRANT EXECUTE ON FUNCTION match_documents_with_cultural_context TO authenticated;

-- Function to get culturally adapted medical explanations
CREATE OR REPLACE FUNCTION get_culturally_adapted_explanation(
    p_medical_term text,
    p_culture_code text DEFAULT 'en',
    p_explanation_style text DEFAULT 'simplified'
)
RETURNS TABLE (
    original_term text,
    adapted_explanation text,
    cultural_context text,
    explanation_style text,
    sensitivity_notes text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p_medical_term,
        CASE
            WHEN p_explanation_style = 'storytelling' THEN
                'Think of your body like a community. ' || mtt.translations ->> p_culture_code || '. ' || mtt.usage_notes
            WHEN p_explanation_style = 'simplified' THEN
                COALESCE(mtt.translations ->> p_culture_code, p_medical_term) ||
                CASE WHEN mtt.usage_notes IS NOT NULL THEN ' - ' || mtt.usage_notes ELSE '' END
            ELSE
                COALESCE(mtt.translations ->> p_culture_code, p_medical_term)
        END as adapted_explanation,
        mtt.cultural_context,
        p_explanation_style,
        CASE mtt.sensitivity_level
            WHEN 'high' THEN 'Use with cultural sensitivity and family involvement'
            WHEN 'medium' THEN 'Consider cultural context when explaining'
            ELSE 'Standard explanation appropriate'
        END as sensitivity_notes
    FROM medical_term_translations mtt
    WHERE mtt.english_term ILIKE '%' || p_medical_term || '%'
    LIMIT 1;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_culturally_adapted_explanation TO authenticated;

-- Function to get emergency protocols with cultural adaptations
CREATE OR REPLACE FUNCTION get_culturally_adapted_emergency_protocols(
    p_condition text,
    p_region text DEFAULT 'GH',
    p_culture_code text DEFAULT 'en',
    p_family_involvement_level text DEFAULT 'moderate'
)
RETURNS TABLE (
    protocol_step text,
    cultural_adaptation text,
    family_involvement_note text,
    urgency_level text,
    time_requirement text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        step.value ->> 'action' as protocol_step,
        CASE
            WHEN p_culture_code IN ('tw', 'yo', 'sw', 'zu', 'ha') THEN
                'Explain in local language: ' || (step.value ->> 'action')
            ELSE
                step.value ->> 'action'
        END as cultural_adaptation,
        CASE
            WHEN p_family_involvement_level = 'high' THEN
                'Ensure family members are informed and involved in decision-making'
            WHEN p_family_involvement_level = 'moderate' THEN
                'Consider involving immediate family members'
            ELSE
                'Patient-centered approach with family notification as appropriate'
        END as family_involvement_note,
        step.value ->> 'urgency' as urgency_level,
        step.value ->> 'timeframe' as time_requirement
    FROM clinical_pathways cp,
         jsonb_array_elements(cp.emergency_protocols -> 'protocols') as step
    WHERE
        cp.condition ILIKE '%' || p_condition || '%'
        AND cp.region = p_region
        AND cp.emergency_protocols IS NOT NULL
    ORDER BY (step.value ->> 'priority')::int
    LIMIT 10;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_culturally_adapted_emergency_protocols TO authenticated;

-- Migration completed successfully
SELECT 'Clinical Decision Support functions migration completed successfully' as status;
