/**
 * CIRCUIT BREAKER INTEGRATION TESTS
 * 
 * Comprehensive tests for circuit breaker integration across all services
 * including fallback mechanisms, recovery strategies, and emergency protocols.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { circuitBreakerService, CircuitBreakerOpenError } from '../../services/CircuitBreakerService';
import { supabaseCircuitBreaker } from '../../utils/supabaseCircuitBreaker';
import { AgentOrchestrator } from '../../services/AgentOrchestrator';
import { errorSanitizationService } from '../../services/ErrorSanitizationService';

describe('Circuit Breaker Integration Tests', () => {
  let orchestrator: AgentOrchestrator;
  let testSessionId: string;

  beforeEach(async () => {
    testSessionId = `cb-test-${Date.now()}`;
    orchestrator = new AgentOrchestrator();
    await orchestrator.initialize();
    
    // Reset all circuit breakers
    circuitBreakerService.resetAll();
    supabaseCircuitBreaker.reset();
  });

  afterEach(async () => {
    await orchestrator.cleanupSession(testSessionId);
  });

  describe('AI Provider Circuit Breaker Integration', () => {
    it('should fallback to next provider when circuit breaker opens', async () => {
      // Force OpenAI circuit breaker to open
      const openaiCircuitBreaker = circuitBreakerService.getCircuitBreaker('openai');
      openaiCircuitBreaker.forceState('open');

      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'Test message for provider fallback',
        userId: 'test-user',
        urgencyLevel: 'medium' as const
      };

      // Should fallback to next provider
      const response = await orchestrator.processRequest(testRequest);
      
      expect(response).toBeDefined();
      expect(response.metadata?.providerFallback).toBe(true);
      expect(response.metadata?.usedProvider).not.toBe('openai');

      console.log('✅ AI provider fallback integration validated');
    });

    it('should handle emergency bypass for critical requests', async () => {
      // Force all AI providers to open state
      ['openai', 'anthropic', 'cohere'].forEach(provider => {
        const cb = circuitBreakerService.getCircuitBreaker(provider);
        cb.forceState('open');
      });

      const emergencyRequest = {
        sessionId: testSessionId,
        userMessage: 'EMERGENCY: Patient is having a heart attack',
        userId: 'test-user',
        urgencyLevel: 'critical' as const
      };

      // Should bypass circuit breakers for emergency
      const response = await orchestrator.processRequest(emergencyRequest);
      
      expect(response).toBeDefined();
      expect(response.metadata?.emergencyBypass).toBe(true);

      console.log('✅ Emergency bypass integration validated');
    });
  });

  describe('Database Circuit Breaker Integration', () => {
    it('should handle database failures gracefully', async () => {
      // Force Supabase circuit breaker to open
      supabaseCircuitBreaker.forceState('open');

      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'Test message requiring database access',
        userId: 'test-user',
        urgencyLevel: 'medium' as const
      };

      // Should handle database failure gracefully
      const response = await orchestrator.processRequest(testRequest);
      
      expect(response).toBeDefined();
      expect(response.metadata?.databaseFallback).toBe(true);

      console.log('✅ Database circuit breaker integration validated');
    });

    it('should maintain emergency data access', async () => {
      // Force database circuit breaker to open
      supabaseCircuitBreaker.forceState('open');

      const emergencyRequest = {
        sessionId: testSessionId,
        userMessage: 'EMERGENCY: Need patient medical history immediately',
        userId: 'test-user',
        urgencyLevel: 'critical' as const
      };

      // Should bypass database circuit breaker for emergency
      const response = await orchestrator.processRequest(emergencyRequest);
      
      expect(response).toBeDefined();
      expect(response.metadata?.emergencyDatabaseAccess).toBe(true);

      console.log('✅ Emergency database access integration validated');
    });
  });

  describe('Circuit Breaker + Error Sanitization Integration', () => {
    it('should sanitize circuit breaker errors properly', async () => {
      const testCircuitBreaker = circuitBreakerService.getCircuitBreaker('test-service', {
        failureThreshold: 1,
        recoveryTimeoutMs: 1000
      });

      // Trigger circuit breaker failure
      try {
        await testCircuitBreaker.execute(async () => {
          throw new Error('Database connection failed: postgresql://user:<EMAIL>:5432/medical');
        });
      } catch (error) {
        // Should not expose sensitive connection details
        expect(error.message).not.toContain('secret');
        expect(error.message).not.toContain('postgresql://');
        expect(error.message).not.toContain('db.example.com');
      }

      console.log('✅ Circuit breaker error sanitization integration validated');
    });
  });

  describe('Circuit Breaker Recovery Integration', () => {
    it('should recover automatically after timeout', async () => {
      const testCircuitBreaker = circuitBreakerService.getCircuitBreaker('recovery-test', {
        failureThreshold: 1,
        recoveryTimeoutMs: 100 // Short timeout for testing
      });

      // Trigger failure to open circuit breaker
      try {
        await testCircuitBreaker.execute(async () => {
          throw new Error('Service temporarily unavailable');
        });
      } catch (error) {
        // Expected failure
      }

      // Verify circuit breaker is open
      expect(testCircuitBreaker.getMetrics().state).toBe('open');

      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 150));

      // Should be in half-open state now
      const metrics = testCircuitBreaker.getMetrics();
      expect(['half-open', 'closed']).toContain(metrics.state);

      console.log('✅ Circuit breaker recovery integration validated');
    });
  });

  describe('Circuit Breaker Monitoring Integration', () => {
    it('should provide comprehensive metrics across all services', async () => {
      // Get all circuit breaker metrics
      const allMetrics = circuitBreakerService.getAllMetrics();
      const supabaseMetrics = supabaseCircuitBreaker.getMetrics();
      
      expect(allMetrics).toBeDefined();
      expect(Array.isArray(allMetrics)).toBe(true);
      expect(supabaseMetrics).toBeDefined();

      // Verify metrics structure
      allMetrics.forEach(metric => {
        expect(metric).toHaveProperty('name');
        expect(metric).toHaveProperty('state');
        expect(metric).toHaveProperty('failureRate');
        expect(metric).toHaveProperty('successRate');
        expect(metric).toHaveProperty('totalRequests');
      });

      console.log('✅ Circuit breaker monitoring integration validated');
    });

    it('should track performance impact of circuit breakers', async () => {
      const startTime = Date.now();
      
      // Perform multiple operations
      const operations = Array.from({ length: 10 }, (_, i) => 
        orchestrator.processRequest({
          sessionId: `${testSessionId}-${i}`,
          userMessage: `Performance test message ${i}`,
          userId: 'test-user',
          urgencyLevel: 'low' as const
        })
      );

      await Promise.all(operations);
      const totalTime = Date.now() - startTime;

      // Verify reasonable performance
      expect(totalTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Check circuit breaker metrics
      const statusSummary = circuitBreakerService.getStatusSummary();
      expect(statusSummary.total).toBeGreaterThan(0);

      console.log(`✅ Circuit breaker performance impact validated - ${totalTime}ms for 10 operations`);
    });
  });
});
