import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';

const InterAgentDialogue = ({ 
  conversations = [],
  activeAgent = null,
  isCollaborating = false,
  onRequestClarification = () => {},
  className = ''
}) => {
  const [autoScroll, setAutoScroll] = useState(true);
  const [highlightedTopics, setHighlightedTopics] = useState([]);
  const messagesEndRef = useRef(null);
  const containerRef = useRef(null);

  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversations, autoScroll]);

  useEffect(() => {
    // Extract and highlight medical topics
    const topics = conversations
      .flatMap(conv => conv.content.match(/\b(diagnosis|treatment|medication|symptoms|condition)\b/gi) || [])
      .filter((topic, index, arr) => arr.indexOf(topic.toLowerCase()) === index);
    setHighlightedTopics(topics.slice(0, 5));
  }, [conversations]);

  const handleScroll = () => {
    if (containerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    }
  };

  const getAgentColor = (agentId) => {
    const colors = {
      'gp': 'primary',
      'cardiologist': 'success',
      'nutritionist': 'warning'
    };
    return colors[agentId] || 'secondary';
  };

  const getAgentIcon = (agentType) => {
    switch (agentType) {
      case 'General Practitioner':
        return 'Stethoscope';
      case 'Cardiologist':
        return 'Heart';
      case 'Nutritionist':
        return 'Apple';
      default:
        return 'User';
    }
  };

  const formatMessageContent = (content) => {
    // Highlight medical terms and patient references
    return content
      .replace(/\b(patient|diagnosis|treatment|medication|symptoms|condition|recommend|suggest)\b/gi, 
        '<span class="font-medium text-primary-600">$1</span>')
      .replace(/\b(\d+(?:\.\d+)?)\s*(mg|ml|units|times|days|weeks|months)\b/gi,
        '<span class="font-data text-warning-600">$1 $2</span>');
  };

  return (
    <div className={`bg-surface border border-border rounded-xl ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <Icon name="MessageSquare" size={20} color="var(--color-primary)" />
            <h3 className="font-semibold text-text-primary font-heading">
              Agent Discussion
            </h3>
          </div>
          
          {isCollaborating && (
            <div className="flex items-center space-x-2 px-3 py-1 bg-success-50 rounded-full">
              <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-success-600 font-medium">
                Live Collaboration
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setAutoScroll(!autoScroll)}
            className={`p-2 rounded-lg transition-fast ${
              autoScroll 
                ? 'bg-primary-50 text-primary-600' :'bg-secondary-50 text-text-secondary hover:bg-secondary-100'
            }`}
            title={autoScroll ? 'Disable auto-scroll' : 'Enable auto-scroll'}
          >
            <Icon name="ArrowDown" size={16} />
          </button>
          
          <button
            onClick={onRequestClarification}
            className="px-3 py-1.5 bg-warning-50 hover:bg-warning-100 text-warning-600 rounded-lg text-sm font-medium transition-fast"
          >
            <Icon name="Hand" size={14} className="mr-1" />
            Request Clarification
          </button>
        </div>
      </div>

      {/* Topic Highlights */}
      {highlightedTopics.length > 0 && (
        <div className="p-4 border-b border-border bg-secondary-50">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Tag" size={16} color="var(--color-text-secondary)" />
            <span className="text-sm font-medium text-text-secondary">
              Discussion Topics
            </span>
          </div>
          <div className="flex flex-wrap gap-2">
            {highlightedTopics.map((topic, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary-100 text-primary-600 rounded-full text-xs font-medium"
              >
                {topic}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Conversation Area */}
      <div 
        ref={containerRef}
        onScroll={handleScroll}
        className="h-96 overflow-y-auto p-4 space-y-4"
      >
        {conversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mb-4">
              <Icon name="MessageSquare" size={24} color="var(--color-text-secondary)" />
            </div>
            <h4 className="font-medium text-text-primary mb-2">
              Waiting for Agent Discussion
            </h4>
            <p className="text-sm text-text-secondary max-w-sm">
              Agents will begin collaborating once they have analyzed your case. 
              You'll be able to observe their professional discussion here.
            </p>
          </div>
        ) : (
          conversations.map((conversation, index) => (
            <div key={index} className="flex space-x-3">
              {/* Agent Avatar */}
              <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                `bg-${getAgentColor(conversation.agentId)}-50`
              } ${
                activeAgent?.id === conversation.agentId ? 'ring-2 ring-primary-500' : ''
              }`}>
                <Icon 
                  name={getAgentIcon(conversation.agentType)}
                  size={16}
                  color={`var(--color-${getAgentColor(conversation.agentId)})`}
                />
              </div>

              {/* Message Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className={`font-medium text-sm ${
                    `text-${getAgentColor(conversation.agentId)}-600`
                  }`}>
                    {conversation.agentName}
                  </span>
                  <span className="text-xs text-text-secondary">
                    {conversation.timestamp}
                  </span>
                  {conversation.isAddressing && (
                    <span className="text-xs bg-warning-100 text-warning-600 px-2 py-0.5 rounded-full">
                      → {conversation.isAddressing}
                    </span>
                  )}
                </div>
                
                <div 
                  className="text-sm text-text-primary leading-relaxed"
                  dangerouslySetInnerHTML={{ 
                    __html: formatMessageContent(conversation.content) 
                  }}
                />

                {/* Context Indicators */}
                {conversation.contextShared && (
                  <div className="mt-2 flex items-center space-x-2 text-xs text-text-secondary">
                    <Icon name="Share2" size={12} />
                    <span>Shared patient context with team</span>
                  </div>
                )}

                {/* Reaction/Agreement Indicators */}
                {conversation.reactions && conversation.reactions.length > 0 && (
                  <div className="mt-2 flex items-center space-x-1">
                    {conversation.reactions.map((reaction, idx) => (
                      <span
                        key={idx}
                        className="inline-flex items-center space-x-1 px-2 py-1 bg-success-50 text-success-600 rounded-full text-xs"
                      >
                        <Icon name="Check" size={10} />
                        <span>{reaction.agentName} agrees</span>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Typing Indicators */}
      {isCollaborating && (
        <div className="p-4 border-t border-border bg-secondary-50">
          <div className="flex items-center space-x-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-sm text-text-secondary">
              Agents are consulting...
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default InterAgentDialogue;