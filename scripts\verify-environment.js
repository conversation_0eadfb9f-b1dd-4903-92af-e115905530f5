/**
 * Environment Configuration Verification
 * Verifies that all security-related environment variables are properly configured
 */

console.log('🔍 VoiceHealth AI - Environment Security Verification\n');

// Check for secret keys that should NOT be present
const dangerousKeys = [
  'VITE_PAYSTACK_SECRET_KEY',
  'PAYSTACK_SECRET_KEY',
  'VITE_SUPABASE_SERVICE_ROLE_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
];

console.log('🚨 Checking for exposed secret keys...');
let secretKeysFound = false;

dangerousKeys.forEach(key => {
  if (process.env[key] || import.meta.env[key]) {
    console.log(`❌ SECURITY RISK: ${key} found in client environment!`);
    secretKeysFound = true;
  } else {
    console.log(`✅ ${key} - Not exposed (good)`);
  }
});

if (secretKeysFound) {
  console.log('\n🚨 CRITICAL: Secret keys found in client environment!');
  console.log('These must be removed before deployment.');
} else {
  console.log('\n✅ No secret keys exposed in client environment');
}

// Check for required public keys
console.log('\n🔑 Checking required public configuration...');

const requiredKeys = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_PAYSTACK_PUBLIC_KEY',
  'VITE_API_BASE_URL'
];

let missingKeys = false;

requiredKeys.forEach(key => {
  const value = import.meta.env[key];
  if (value) {
    if (key === 'VITE_API_BASE_URL') {
      if (value.includes('supabase.co/rest')) {
        console.log(`⚠️  ${key} - Points to Supabase REST API (should point to secure backend)`);
      } else {
        console.log(`✅ ${key} - Points to secure backend`);
      }
    } else {
      console.log(`✅ ${key} - Configured`);
    }
  } else {
    console.log(`❌ ${key} - Missing`);
    missingKeys = true;
  }
});

// Check security flags
console.log('\n🛡️ Checking security configuration...');

const securityFlags = [
  'VITE_ENCRYPTION_ENABLED',
  'VITE_AUDIT_LOGGING_ENABLED'
];

securityFlags.forEach(key => {
  const value = import.meta.env[key];
  if (value === 'true') {
    console.log(`✅ ${key} - Enabled`);
  } else {
    console.log(`⚠️  ${key} - Not enabled or missing`);
  }
});

// Environment-specific checks
console.log('\n🌍 Environment-specific verification...');

const nodeEnv = import.meta.env.VITE_NODE_ENV || 'development';
console.log(`📍 Environment: ${nodeEnv}`);

if (nodeEnv === 'production') {
  const apiUrl = import.meta.env.VITE_API_BASE_URL;
  if (apiUrl && apiUrl.startsWith('https://')) {
    console.log('✅ Production API URL uses HTTPS');
  } else {
    console.log('❌ Production API URL should use HTTPS');
  }
  
  const appUrl = import.meta.env.VITE_APP_URL;
  if (appUrl && appUrl.startsWith('https://')) {
    console.log('✅ Production App URL uses HTTPS');
  } else {
    console.log('❌ Production App URL should use HTTPS');
  }
} else {
  console.log('ℹ️  Development environment - HTTP URLs acceptable');
}

// Summary
console.log('\n📊 Security Verification Summary:');
console.log('================================');

if (secretKeysFound) {
  console.log('❌ CRITICAL SECURITY ISSUES FOUND');
  console.log('   - Secret keys exposed in client environment');
  console.log('   - MUST BE FIXED before deployment');
} else if (missingKeys) {
  console.log('⚠️  CONFIGURATION ISSUES FOUND');
  console.log('   - Some required keys are missing');
  console.log('   - Application may not function correctly');
} else {
  console.log('✅ ENVIRONMENT SECURITY VERIFIED');
  console.log('   - No secret keys exposed');
  console.log('   - All required keys configured');
  console.log('   - Ready for secure deployment');
}

console.log('\n🎯 Next Steps:');
if (secretKeysFound) {
  console.log('1. Remove secret keys from client environment files');
  console.log('2. Ensure secret keys are only in secure backend');
  console.log('3. Re-run this verification');
} else if (missingKeys) {
  console.log('1. Configure missing environment variables');
  console.log('2. Update .env files with correct values');
  console.log('3. Re-run this verification');
} else {
  console.log('1. ✅ Environment configuration verified');
  console.log('2. ✅ Ready to run security tests');
  console.log('3. ✅ Ready for deployment');
}

console.log('\n🔒 Security Checklist:');
console.log(`${secretKeysFound ? '❌' : '✅'} No secret keys in client environment`);
console.log(`${missingKeys ? '❌' : '✅'} All required keys configured`);
console.log(`${import.meta.env.VITE_API_BASE_URL?.includes('supabase.co/rest') ? '❌' : '✅'} API points to secure backend`);
console.log(`${import.meta.env.VITE_ENCRYPTION_ENABLED === 'true' ? '✅' : '❌'} Encryption enabled`);
console.log(`${import.meta.env.VITE_AUDIT_LOGGING_ENABLED === 'true' ? '✅' : '❌'} Audit logging enabled`);

export default {
  secretKeysFound,
  missingKeys,
  isSecure: !secretKeysFound && !missingKeys
};
