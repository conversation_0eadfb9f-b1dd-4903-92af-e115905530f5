import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import ConsultationStatusIndicator from '../../components/ui/ConsultationStatusIndicator';
import AgentCollaborationPanel from './components/AgentCollaborationPanel';
import InterAgentDialogue from './components/InterAgentDialogue';
import SessionControlPanel from './components/SessionControlPanel';
import AudioVisualization from './components/AudioVisualization';
import CollaborationProgressTracker from './components/CollaborationProgressTracker';

const RealTimeMultiAgentCollaboration = () => {
  const navigate = useNavigate();

  // Emergency override state
  const [emergencyOverride, setEmergencyOverride] = useState(false);
  const [emergencyReason, setEmergencyReason] = useState(null);
  const [emergencyToken, setEmergencyToken] = useState(null);
  
  // Mock data for agents
  const [activeAgents] = useState([
    {
      id: 'gp',
      name: 'Dr. <PERSON>',
      specialty: 'General Practitioner',
      status: 'analyzing',
      voiceProfile: 'Professional, Warm',
      gender: 'Female',
      accent: 'American English'
    },
    {
      id: 'cardiologist',
      name: 'Dr. Michael Rodriguez',
      specialty: 'Cardiologist',
      status: 'listening',
      voiceProfile: 'Authoritative, Clear',
      gender: 'Male',
      accent: 'Spanish-American'
    },
    {
      id: 'nutritionist',
      name: 'Dr. Emily Watson',
      specialty: 'Nutritionist',
      status: 'speaking',
      voiceProfile: 'Encouraging, Gentle',
      gender: 'Female',
      accent: 'British English'
    }
  ]);

  // Mock conversation data
  const [conversations] = useState([
    {
      agentId: 'gp',
      agentName: 'Dr. Sarah Chen',
      agentType: 'General Practitioner',
      content: `Based on the patient's symptoms of chest discomfort and fatigue, I'm seeing potential cardiovascular concerns. The patient mentioned experiencing these symptoms during physical activity, which could indicate exercise intolerance.`,
      timestamp: '2 minutes ago',
      contextShared: true
    },
    {
      agentId: 'cardiologist',
      agentName: 'Dr. Michael Rodriguez',
      agentType: 'Cardiologist',
      content: `I agree with Dr. Chen's assessment. The exercise-induced symptoms are concerning. We should consider an ECG and stress test. The patient's family history of heart disease also elevates the risk profile significantly.`,
      timestamp: '1 minute ago',
      isAddressing: 'Dr. Sarah Chen',
      reactions: [
        { agentName: 'Dr. Sarah Chen', type: 'agree' }
      ]
    },
    {
      agentId: 'nutritionist',
      agentName: 'Dr. Emily Watson',
      agentType: 'Nutritionist',
      content: `From a nutritional standpoint, the patient's current diet is high in processed foods and sodium. This could be contributing to the cardiovascular symptoms. I recommend a Mediterranean-style diet with reduced sodium intake and increased omega-3 fatty acids.`,
      timestamp: '30 seconds ago',
      contextShared: true
    }
  ]);

  // State management
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [sessionStatus, setSessionStatus] = useState('active');
  const [collaborationPhase, setCollaborationPhase] = useState('collaboration');
  const [isCollaborating, setIsCollaborating] = useState(true);
  const [phaseProgress, setPhaseProgress] = useState(75);
  const [audioPlaying, setAudioPlaying] = useState(true);
  const [audioVolume, setAudioVolume] = useState(0.7);
  const [audioMuted, setAudioMuted] = useState(false);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(180);

  // Emergency override detection on component mount
  useEffect(() => {
    const detectEmergencyOverride = async () => {
      try {
        // Check URL parameters for emergency override
        const urlParams = new URLSearchParams(window.location.search);
        const emergencyParam = urlParams.get('emergency');
        const emergencyReasonParam = urlParams.get('reason');

        if (emergencyParam === 'true') {
          console.log('🚨 Emergency override detected in multi-agent collaboration');

          // Import emergency authentication service
          const { default: emergencyAuthService } = await import('../../services/emergencyAuthService');

          // Create emergency session
          const sessionId = `multi-agent-emergency-${Date.now()}`;
          const reason = emergencyReasonParam || 'multi_agent_emergency';

          const emergencySession = await emergencyAuthService.createEmergencySession(
            'emergency_user',
            sessionId,
            reason,
            'multi_agent_collaboration'
          );

          if (emergencySession) {
            setEmergencyOverride(true);
            setEmergencyReason(reason);
            setEmergencyToken(emergencySession.emergencyToken);
            setSessionStatus('emergency_active');

            console.log(`✅ Emergency override activated for multi-agent collaboration: ${reason}`);

            // Auto-start collaboration in emergency mode
            setIsCollaborating(true);
            setCollaborationPhase('emergency_assessment');

            // Cache emergency state
            localStorage.setItem('emergency_multi_agent_session', JSON.stringify({
              isEmergencyActive: true,
              emergencyReason: reason,
              emergencyToken: emergencySession.emergencyToken,
              sessionId
            }));
          }
        }

        // Check for cached emergency state
        const cachedEmergencyState = localStorage.getItem('emergency_multi_agent_session');
        if (cachedEmergencyState && !emergencyOverride) {
          const emergencyData = JSON.parse(cachedEmergencyState);
          if (emergencyData.isEmergencyActive) {
            setEmergencyOverride(true);
            setEmergencyReason(emergencyData.emergencyReason);
            setEmergencyToken(emergencyData.emergencyToken);
            setSessionStatus('emergency_active');

            console.log('🚨 Restored multi-agent emergency session from cache');
          }
        }
      } catch (error) {
        console.error('Failed to detect emergency override in multi-agent collaboration:', error);
      }
    };

    detectEmergencyOverride();
  }, []);

  // Mock completed and upcoming tasks
  const [completedTasks] = useState([
    'Patient context shared across all agents',
    'Initial symptom analysis completed',
    'Medical history reviewed by specialists',
    'Risk factors identified and documented'
  ]);

  const [upcomingTasks] = useState([
    'Finalize treatment recommendations',
    'Prepare comprehensive consultation summary',
    'Schedule follow-up appointments',
    'Generate prescription recommendations'
  ]);

  useEffect(() => {
    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setPhaseProgress(prev => {
        const newProgress = prev + Math.random() * 5;
        return newProgress > 100 ? 100 : newProgress;
      });
    }, 3000);

    // Simulate time countdown
    const timeInterval = setInterval(() => {
      setEstimatedTimeRemaining(prev => Math.max(0, prev - 1));
    }, 1000);

    return () => {
      clearInterval(progressInterval);
      clearInterval(timeInterval);
    };
  }, []);

  // Event handlers
  const handleAgentSelect = (agent) => {
    setSelectedAgent(agent);
  };

  const handlePauseCollaboration = () => {
    setSessionStatus('paused');
    setIsCollaborating(false);
    setAudioPlaying(false);
  };

  const handleResumeCollaboration = () => {
    setSessionStatus('active');
    setIsCollaborating(true);
    setAudioPlaying(true);
  };

  const handleInjectQuestion = async (question) => {
    console.log('Injecting question:', question);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  const handleRedirectTopic = async (topic) => {
    console.log('Redirecting to topic:', topic);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  const handleEndSession = () => {
    navigate('/session-dashboard-history');
  };

  const handleRequestClarification = () => {
    console.log('Requesting clarification from agents');
  };

  const handleVolumeChange = (newVolume) => {
    setAudioVolume(newVolume);
    if (newVolume > 0 && audioMuted) {
      setAudioMuted(false);
    }
  };

  const handleMute = () => {
    setAudioMuted(!audioMuted);
  };

  const handlePlayPause = () => {
    setAudioPlaying(!audioPlaying);
  };

  const handlePhaseComplete = (phase) => {
    const phases = ['initializing', 'assessment', 'collaboration', 'synthesis', 'completed'];
    const currentIndex = phases.indexOf(phase);
    if (currentIndex < phases.length - 1) {
      setCollaborationPhase(phases[currentIndex + 1]);
      setPhaseProgress(0);
    }
  };

  /**
   * ENHANCED EMERGENCY STOP MECHANISM
   * Immediately terminates multi-agent collaboration and triggers emergency protocols
   */
  const handleEmergencyStop = async (reason = 'user_initiated') => {
    try {
      console.log('🚨 MULTI-AGENT EMERGENCY STOP TRIGGERED:', reason);

      // Immediately stop all collaboration activities
      setSessionStatus('emergency_stopped');
      setIsCollaborating(false);
      setAudioPlaying(false);

      // Stop any ongoing audio processing
      if (audioPlaying) {
        setAudioPlaying(false);
      }

      // Log emergency stop event for audit trail
      await logEmergencyStopEvent(reason);

      // Trigger emergency protocols if needed
      if (reason === 'medical_emergency' || reason === 'critical_situation') {
        await triggerEmergencyProtocols();
      }

      console.log('✅ Multi-agent emergency stop completed successfully');

    } catch (error) {
      console.error('❌ Multi-agent emergency stop failed:', error);
      // Ensure session is stopped even if logging fails
      setSessionStatus('emergency_stopped');
      setIsCollaborating(false);
      setAudioPlaying(false);
    }
  };

  /**
   * Log emergency stop event for audit trail
   */
  const logEmergencyStopEvent = async (reason) => {
    try {
      // Import audit logger
      const { default: auditLogger } = await import('../../utils/auditLogger');

      await auditLogger.logEmergencyAccess(
        'system', // Multi-agent system user
        'system',
        `Multi-agent emergency stop triggered: ${reason}`,
        {
          collaboration_phase: collaborationPhase,
          active_agents: activeAgents.length,
          stop_reason: reason,
          timestamp: new Date().toISOString(),
          response_time: '< 2 seconds'
        }
      );
    } catch (error) {
      console.error('Failed to log multi-agent emergency stop event:', error);
    }
  };

  /**
   * Trigger emergency protocols for critical situations
   */
  const triggerEmergencyProtocols = async () => {
    try {
      console.log('🚨 Triggering multi-agent emergency protocols...');

      // In production, this would:
      // 1. Alert all connected healthcare providers
      // 2. Escalate to emergency medical services
      // 3. Notify system administrators
      // 4. Trigger automated emergency responses

      console.log('Multi-agent emergency protocols would be triggered');
    } catch (error) {
      console.error('Failed to trigger multi-agent emergency protocols:', error);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Emergency Override Indicator */}
      {emergencyOverride && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 bg-error-600 text-white px-6 py-3 rounded-lg shadow-lg border-2 border-error-700">
          <div className="flex items-center space-x-3">
            <Icon name="AlertTriangle" size={20} color="white" />
            <div>
              <div className="font-bold text-sm">EMERGENCY PROTOCOLS ACTIVE</div>
              <div className="text-xs opacity-90">
                Reason: {emergencyReason?.replace(/_/g, ' ').toUpperCase()} |
                Authentication bypassed for patient safety
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Indicator */}
      <ConsultationStatusIndicator
        isActive={sessionStatus === 'active' || emergencyOverride}
        activeAgents={activeAgents}
        sessionProgress={phaseProgress}
        consultationPhase={emergencyOverride ? 'emergency_assessment' : collaborationPhase}
        onEmergencyStop={handleEmergencyStop}
        emergencyMode={emergencyOverride}
      />

      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-text-primary font-heading">
                  Multi-Agent Collaboration
                </h1>
                <p className="text-text-secondary mt-2">
                  Observe AI specialists collaborating on your health consultation in real-time
                </p>
              </div>
              
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  onClick={() => navigate('/voice-consultation-interface')}
                  iconName="ArrowLeft"
                  iconPosition="left"
                >
                  Back to Consultation
                </Button>
                
                <Button
                  variant="primary"
                  onClick={() => navigate('/session-dashboard-history')}
                  iconName="FileText"
                  iconPosition="left"
                >
                  View Summary
                </Button>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Agent Collaboration & Progress */}
            <div className="lg:col-span-1 space-y-6">
              <AgentCollaborationPanel
                activeAgents={activeAgents}
                collaborationPhase={collaborationPhase}
                onAgentSelect={handleAgentSelect}
                selectedAgent={selectedAgent}
              />
              
              <CollaborationProgressTracker
                currentPhase={collaborationPhase}
                phaseProgress={phaseProgress}
                estimatedTimeRemaining={estimatedTimeRemaining}
                completedTasks={completedTasks}
                upcomingTasks={upcomingTasks}
                onPhaseComplete={handlePhaseComplete}
              />
            </div>

            {/* Center Column - Dialogue & Audio */}
            <div className="lg:col-span-1 space-y-6">
              <InterAgentDialogue
                conversations={conversations}
                activeAgent={selectedAgent}
                isCollaborating={isCollaborating}
                onRequestClarification={handleRequestClarification}
              />
              
              <AudioVisualization
                activeAgent={selectedAgent || activeAgents.find(a => a.status === 'speaking')}
                isPlaying={audioPlaying}
                volume={audioVolume}
                onVolumeChange={handleVolumeChange}
                onPlayPause={handlePlayPause}
                onMute={handleMute}
                isMuted={audioMuted}
              />
            </div>

            {/* Right Column - Session Control */}
            <div className="lg:col-span-1">
              <SessionControlPanel
                sessionStatus={sessionStatus}
                collaborationPhase={collaborationPhase}
                onPauseCollaboration={handlePauseCollaboration}
                onResumeCollaboration={handleResumeCollaboration}
                onInjectQuestion={handleInjectQuestion}
                onRedirectTopic={handleRedirectTopic}
                onEndSession={handleEndSession}
                userInterventionEnabled={true}
              />
            </div>
          </div>

          {/* Mobile-Optimized Bottom Actions */}
          <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-surface border-t border-border p-4">
            <div className="flex space-x-3">
              <Button
                variant={sessionStatus === 'active' ? 'warning' : 'success'}
                onClick={sessionStatus === 'active' ? handlePauseCollaboration : handleResumeCollaboration}
                iconName={sessionStatus === 'active' ? 'Pause' : 'Play'}
                iconPosition="left"
                fullWidth
              >
                {sessionStatus === 'active' ? 'Pause' : 'Resume'}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleRequestClarification}
                iconName="MessageCircle"
                iconPosition="left"
                fullWidth
              >
                Ask Question
              </Button>
            </div>
          </div>

          {/* Emergency Notice */}
          <div className="mt-8 p-4 bg-error-50 border border-error-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <Icon name="AlertTriangle" size={20} color="var(--color-error)" />
              <div>
                <h4 className="font-medium text-error-600 mb-1">
                  Medical Emergency Notice
                </h4>
                <p className="text-sm text-error-600">
                  This is an AI consultation for informational purposes only. 
                  For medical emergencies, please call emergency services immediately or visit your nearest emergency room.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeMultiAgentCollaboration;