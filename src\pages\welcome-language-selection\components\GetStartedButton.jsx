import React from 'react';
import Icon from '../../../components/AppIcon';

const GetStartedButton = ({ onClick, isLoading, selectedLanguage }) => {
  const getLocalizedText = () => {
    const texts = {
      en: 'Get Started',
      tw: '<PERSON> Ase',
      yo: 'Bẹrẹ',
      sw: 'Anza',
      af: 'Begin'
    };
    return texts[selectedLanguage] || texts.en;
  };

  return (
    <button
      onClick={onClick}
      disabled={isLoading}
      className="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold py-4 px-8 rounded-xl shadow-elevated hover:shadow-floating transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none min-h-14 flex items-center justify-center space-x-3"
    >
      {isLoading ? (
        <>
          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </>
      ) : (
        <>
          <span className="text-lg">{getLocalizedText()}</span>
          <Icon name="ArrowRight" size={20} color="white" />
        </>
      )}
    </button>
  );
};

export default GetStartedButton;