/**
 * CONTEXT ASSEMBLY SERVICE
 * 
 * Combines patient profile, regional health data, conversation state, and medical knowledge
 * into structured context blocks for LLM prompts. This service transforms raw context data
 * into actionable intelligence for medical AI agents.
 * 
 * Key Features:
 * - Rich context block generation
 * - Token-optimized context assembly
 * - Medical history synthesis
 * - Regional health integration
 * - Conversation state management
 */

import type { EnhancedPatientContext } from './EnhancedPatientContextService';
import type { ConversationMessage } from '../types/memory';

export interface ContextBlock {
  patientProfile: string;
  medicalHistory: string;
  regionalContext: string;
  conversationContext: string;
  clinicalGuidance: string;
  emergencyAlerts: string;
  culturalConsiderations: string;
  totalTokens: number;
}

export interface ContextAssemblyOptions {
  maxTokens?: number;
  includeFullHistory?: boolean;
  prioritizeRecent?: boolean;
  includeRegionalContext?: boolean;
  includeCulturalContext?: boolean;
  conversationPhase?: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
}

export interface AssembledContext {
  systemPromptAddition: string;
  contextBlock: ContextBlock;
  priorityFlags: string[];
  recommendations: string[];
  tokenUsage: {
    contextTokens: number;
    remainingTokens: number;
    optimizationApplied: boolean;
  };
}

class ContextAssemblyService {
  private readonly MAX_CONTEXT_TOKENS = 2000; // Reserve tokens for context
  private readonly PRIORITY_SECTIONS = ['emergencyAlerts', 'patientProfile', 'medicalHistory'];

  constructor() {
    console.log('🔧 Context Assembly Service initialized');
  }

  /**
   * Assemble comprehensive context for agent prompts
   */
  async assembleContext(
    patientContext: EnhancedPatientContext,
    conversationHistory: ConversationMessage[],
    currentMessage: string,
    options: ContextAssemblyOptions = {}
  ): Promise<AssembledContext> {
    try {
      console.log('🧩 Assembling comprehensive context...');

      const {
        maxTokens = this.MAX_CONTEXT_TOKENS,
        includeFullHistory = false,
        prioritizeRecent = true,
        includeRegionalContext = true,
        includeCulturalContext = true,
        conversationPhase = 'assessment',
        urgencyLevel = 'medium'
      } = options;

      // Build context sections
      const contextBlock: ContextBlock = {
        patientProfile: this.buildPatientProfileContext(patientContext.patientProfile),
        medicalHistory: this.buildMedicalHistoryContext(patientContext.medicalHistory, includeFullHistory),
        regionalContext: includeRegionalContext ? 
          this.buildRegionalHealthContext(patientContext.regionalContext) : '',
        conversationContext: this.buildConversationContext(
          conversationHistory, 
          currentMessage, 
          conversationPhase,
          prioritizeRecent
        ),
        clinicalGuidance: this.buildClinicalGuidanceContext(patientContext, urgencyLevel),
        emergencyAlerts: this.buildEmergencyAlertsContext(patientContext.emergencyContext),
        culturalConsiderations: includeCulturalContext ? 
          this.buildCulturalContext(patientContext.culturalContext) : '',
        totalTokens: 0 // Will be calculated
      };

      // Calculate token usage and optimize if needed
      const tokenUsage = this.calculateAndOptimizeTokens(contextBlock, maxTokens);
      contextBlock.totalTokens = tokenUsage.contextTokens;

      // Generate priority flags and recommendations
      const priorityFlags = this.generatePriorityFlags(patientContext, urgencyLevel);
      const recommendations = this.generateContextualRecommendations(patientContext, conversationPhase);

      // Assemble final system prompt addition
      const systemPromptAddition = this.assembleSystemPromptAddition(
        contextBlock, 
        priorityFlags, 
        recommendations
      );

      console.log(`✅ Context assembled: ${tokenUsage.contextTokens} tokens`);

      return {
        systemPromptAddition,
        contextBlock,
        priorityFlags,
        recommendations,
        tokenUsage
      };

    } catch (error) {
      console.error('❌ Failed to assemble context:', error);
      throw new Error(`Context assembly failed: ${error.message}`);
    }
  }

  /**
   * Create simplified context format optimized for LLM consumption
   */
  async assembleSimplifiedContext(
    patientContext: EnhancedPatientContext,
    conversationHistory: ConversationMessage[],
    currentMessage: string,
    options: ContextAssemblyOptions = {}
  ): Promise<{
    contextSummary: string;
    priorityAlerts: string[];
    keyRecommendations: string[];
    tokenCount: number;
  }> {
    try {
      console.log('🧩 Assembling simplified context for LLM...');

      const {
        maxTokens = 1000, // Smaller limit for simplified context
        urgencyLevel = 'medium'
      } = options;

      // Build concise context summary
      let contextSummary = '';

      // Patient essentials
      if (patientContext.patientProfile) {
        const profile = patientContext.patientProfile;
        contextSummary += `Patient: ${profile.fullName || 'Unknown'}, ${profile.age || 'age unknown'}, ${profile.gender || 'gender unknown'}\n`;
        if (profile.country) {
          contextSummary += `Location: ${profile.city ? profile.city + ', ' : ''}${profile.country}\n`;
        }
      }

      // Critical medical history
      if (patientContext.medicalHistory) {
        const history = patientContext.medicalHistory;
        if (history.chronicConditions?.length > 0) {
          contextSummary += `Chronic Conditions: ${history.chronicConditions.slice(0, 3).join(', ')}\n`;
        }
        if (history.medications?.filter((med: any) => med.isCurrent)?.length > 0) {
          const currentMeds = history.medications.filter((med: any) => med.isCurrent).slice(0, 3);
          contextSummary += `Current Medications: ${currentMeds.map((med: any) => med.medicationName).join(', ')}\n`;
        }
        if (history.allergies?.length > 0) {
          contextSummary += `Allergies: ${history.allergies.join(', ')}\n`;
        }
      }

      // Regional context (if relevant)
      if (patientContext.regionalContext && options.includeRegionalContext) {
        const regional = patientContext.regionalContext;
        if (regional.commonConditions?.length > 0) {
          contextSummary += `Regional Health Factors: ${regional.commonConditions.slice(0, 2).join(', ')}\n`;
        }
      }

      // Generate priority alerts
      const priorityAlerts = this.generatePriorityFlags(patientContext, urgencyLevel);

      // Generate key recommendations
      const keyRecommendations = this.generateContextualRecommendations(patientContext, 'assessment').slice(0, 3);

      // Estimate token count (rough approximation: 1 token ≈ 4 characters)
      const tokenCount = Math.ceil(contextSummary.length / 4);

      console.log(`✅ Simplified context assembled: ${tokenCount} tokens`);

      return {
        contextSummary: contextSummary.trim(),
        priorityAlerts,
        keyRecommendations,
        tokenCount
      };

    } catch (error) {
      console.error('❌ Failed to assemble simplified context:', error);
      return {
        contextSummary: 'Context assembly failed - proceeding without patient context',
        priorityAlerts: [],
        keyRecommendations: [],
        tokenCount: 0
      };
    }
  }

  /**
   * Build patient profile context section
   */
  private buildPatientProfileContext(profile: any): string {
    const age = profile.age ? `${profile.age} years old` : 'age unknown';
    const location = profile.country && profile.city ? 
      `${profile.city}, ${profile.country}` : 
      profile.country || 'location unknown';

    return `PATIENT PROFILE:
- Name: ${profile.fullName}
- Age: ${age}
- Gender: ${profile.gender || 'not specified'}
- Location: ${location}
- Occupation: ${profile.occupation || 'not specified'}
- Insurance: ${profile.insuranceStatus || 'unknown'}
- Healthcare Access: ${profile.healthcareAccessLevel || 'unknown'}
- Primary Language: ${profile.preferredLanguage || 'English'}`;
  }

  /**
   * Build medical history context section
   */
  private buildMedicalHistoryContext(medicalHistory: any, includeFullHistory: boolean): string {
    const conditions = medicalHistory.conditions || [];
    const medications = medicalHistory.medications || [];
    const chronicConditions = medicalHistory.chronicConditions || [];

    let context = 'MEDICAL HISTORY:\n';

    // Current conditions
    if (chronicConditions.length > 0) {
      context += `- Chronic Conditions: ${chronicConditions.join(', ')}\n`;
    }

    // Current medications
    const currentMeds = medications.filter((med: any) => med.isCurrent);
    if (currentMeds.length > 0) {
      context += `- Current Medications: ${currentMeds.map((med: any) => 
        `${med.medicationName} ${med.dosage} ${med.frequency}`).join('; ')}\n`;
    }

    // Recent conditions (if not including full history)
    if (!includeFullHistory && conditions.length > 0) {
      const recentConditions = conditions.slice(0, 3);
      context += `- Recent Conditions: ${recentConditions.map((cond: any) => 
        `${cond.conditionName} (${cond.severity})`).join(', ')}\n`;
    }

    // Allergies
    if (medicalHistory.allergies && medicalHistory.allergies.length > 0) {
      context += `- Allergies: ${medicalHistory.allergies.join(', ')}\n`;
    }

    return context.trim() || 'MEDICAL HISTORY: No significant medical history recorded';
  }

  /**
   * Build regional health context section
   */
  private buildRegionalHealthContext(regionalContext: any): string {
    if (!regionalContext) return '';

    const commonConditions = regionalContext.commonConditions || [];
    const endemicDiseases = regionalContext.endemicDiseases || [];
    const seasonalRisks = regionalContext.seasonalRisks || [];

    return `REGIONAL HEALTH CONTEXT (${regionalContext.countryName}):
- Common Conditions: ${commonConditions.slice(0, 5).join(', ')}
- Endemic Diseases: ${endemicDiseases.slice(0, 3).join(', ')}
- Current Season: ${regionalContext.currentSeason || 'unknown'}
- Seasonal Risks: ${seasonalRisks.slice(0, 3).join(', ')}
- Healthcare Access: ${regionalContext.healthcareAccessLevel || 'unknown'}`;
  }

  /**
   * Build conversation context section
   */
  private buildConversationContext(
    history: ConversationMessage[], 
    currentMessage: string,
    phase: string,
    prioritizeRecent: boolean
  ): string {
    const relevantHistory = prioritizeRecent ? history.slice(-5) : history.slice(0, 5);
    
    let context = `CONVERSATION CONTEXT:
- Current Phase: ${phase}
- Current Message: "${currentMessage}"`;

    if (relevantHistory.length > 0) {
      context += '\n- Recent Exchange:\n';
      relevantHistory.forEach((msg, index) => {
        const speaker = msg.speaker === 'user' ? 'Patient' : 'Assistant';
        context += `  ${speaker}: "${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : '"}"\n`;
      });
    }

    return context;
  }

  /**
   * Build clinical guidance context
   */
  private buildClinicalGuidanceContext(patientContext: any, urgencyLevel: string): string {
    const guidance = [];

    // Add urgency-based guidance
    if (urgencyLevel === 'critical') {
      guidance.push('CRITICAL: Prioritize emergency assessment and immediate care recommendations');
    } else if (urgencyLevel === 'high') {
      guidance.push('HIGH PRIORITY: Conduct thorough assessment and consider urgent care needs');
    }

    // Add condition-specific guidance
    const chronicConditions = patientContext.medicalHistory?.chronicConditions || [];
    if (chronicConditions.includes('Diabetes')) {
      guidance.push('Monitor for diabetes-related complications');
    }
    if (chronicConditions.includes('Hypertension')) {
      guidance.push('Consider cardiovascular risk factors');
    }

    return guidance.length > 0 ? 
      `CLINICAL GUIDANCE:\n- ${guidance.join('\n- ')}` : 
      '';
  }

  /**
   * Build emergency alerts context
   */
  private buildEmergencyAlertsContext(emergencyContext: any): string {
    const alerts = [];

    if (emergencyContext?.criticalAllergies?.length > 0) {
      alerts.push(`CRITICAL ALLERGIES: ${emergencyContext.criticalAllergies.join(', ')}`);
    }

    if (emergencyContext?.medicalAlerts?.length > 0) {
      alerts.push(`MEDICAL ALERTS: ${emergencyContext.medicalAlerts.join(', ')}`);
    }

    return alerts.length > 0 ? 
      `EMERGENCY ALERTS:\n- ${alerts.join('\n- ')}` : 
      '';
  }

  /**
   * Build cultural context section
   */
  private buildCulturalContext(culturalContext: any): string {
    if (!culturalContext) return '';

    const considerations = [];

    if (culturalContext.familyInvolvementLevel === 'high') {
      considerations.push('Family involvement expected in medical decisions');
    }

    if (culturalContext.traditionalMedicineUse) {
      considerations.push('Patient may use traditional medicine alongside conventional treatment');
    }

    if (culturalContext.religiousConsiderations?.length > 0) {
      considerations.push(`Religious considerations: ${culturalContext.religiousConsiderations.join(', ')}`);
    }

    return considerations.length > 0 ? 
      `CULTURAL CONSIDERATIONS:\n- ${considerations.join('\n- ')}` : 
      '';
  }

  /**
   * Calculate token usage and optimize context if needed
   */
  private calculateAndOptimizeTokens(contextBlock: ContextBlock, maxTokens: number): any {
    // Rough token estimation (4 characters per token)
    const estimateTokens = (text: string) => Math.ceil(text.length / 4);

    let totalTokens = Object.values(contextBlock)
      .filter(value => typeof value === 'string')
      .reduce((sum, text) => sum + estimateTokens(text), 0);

    let optimizationApplied = false;

    // If over limit, optimize by truncating non-priority sections
    if (totalTokens > maxTokens) {
      optimizationApplied = true;
      
      // Truncate less critical sections first
      if (contextBlock.culturalConsiderations.length > 200) {
        contextBlock.culturalConsiderations = contextBlock.culturalConsiderations.substring(0, 200) + '...';
      }
      
      if (contextBlock.regionalContext.length > 300) {
        contextBlock.regionalContext = contextBlock.regionalContext.substring(0, 300) + '...';
      }

      // Recalculate
      totalTokens = Object.values(contextBlock)
        .filter(value => typeof value === 'string')
        .reduce((sum, text) => sum + estimateTokens(text), 0);
    }

    return {
      contextTokens: totalTokens,
      remainingTokens: maxTokens - totalTokens,
      optimizationApplied
    };
  }

  /**
   * Generate priority flags based on context
   */
  private generatePriorityFlags(patientContext: any, urgencyLevel: string): string[] {
    const flags = [];

    if (urgencyLevel === 'critical') {
      flags.push('EMERGENCY_CONSULTATION');
    }

    const chronicConditions = patientContext.medicalHistory?.chronicConditions || [];
    if (chronicConditions.length > 2) {
      flags.push('COMPLEX_MEDICAL_HISTORY');
    }

    if (patientContext.emergencyContext?.criticalAllergies?.length > 0) {
      flags.push('CRITICAL_ALLERGIES');
    }

    return flags;
  }

  /**
   * Generate contextual recommendations
   */
  private generateContextualRecommendations(patientContext: any, phase: string): string[] {
    const recommendations = [];

    if (phase === 'assessment') {
      recommendations.push('Conduct systematic symptom assessment');
      recommendations.push('Review relevant medical history');
    }

    const regionalContext = patientContext.regionalContext;
    if (regionalContext?.endemicDiseases?.length > 0) {
      recommendations.push(`Consider endemic diseases: ${regionalContext.endemicDiseases.slice(0, 2).join(', ')}`);
    }

    return recommendations;
  }

  /**
   * Assemble final system prompt addition
   */
  private assembleSystemPromptAddition(
    contextBlock: ContextBlock, 
    priorityFlags: string[], 
    recommendations: string[]
  ): string {
    let prompt = '\n\n=== PATIENT CONTEXT ===\n';
    
    // Add priority flags first
    if (priorityFlags.length > 0) {
      prompt += `🚨 PRIORITY FLAGS: ${priorityFlags.join(', ')}\n\n`;
    }

    // Add context sections
    Object.entries(contextBlock).forEach(([key, value]) => {
      if (typeof value === 'string' && value.trim() && key !== 'totalTokens') {
        prompt += `${value}\n\n`;
      }
    });

    // Add recommendations
    if (recommendations.length > 0) {
      prompt += `CONTEXTUAL RECOMMENDATIONS:\n- ${recommendations.join('\n- ')}\n\n`;
    }

    prompt += '=== END CONTEXT ===\n';
    prompt += 'Use this context to provide personalized, culturally appropriate, and medically relevant guidance.';

    return prompt;
  }
}

// Export singleton instance
export const contextAssemblyService = new ContextAssemblyService();
