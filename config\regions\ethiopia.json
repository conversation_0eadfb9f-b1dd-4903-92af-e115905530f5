{"countryCode": "ET", "countryName": "Ethiopia", "region": "East Africa", "capital": "Addis A<PERSON>ba", "timezone": "EAT", "currency": "ETB", "languages": [{"code": "am", "name": "Amharic", "localName": "አማርኛ", "primary": true, "supportLevel": "full", "voiceSupport": true, "medicalTerminology": true, "culturalAdaptation": true}, {"code": "en", "name": "English", "localName": "English", "primary": false, "supportLevel": "full", "voiceSupport": true, "medicalTerminology": true, "culturalAdaptation": true}, {"code": "om", "name": "Oromo", "localName": "<PERSON><PERSON><PERSON>", "primary": false, "supportLevel": "partial", "voiceSupport": false, "medicalTerminology": false, "culturalAdaptation": true}, {"code": "ti", "name": "<PERSON><PERSON><PERSON><PERSON>", "localName": "ትግርኛ", "primary": false, "supportLevel": "basic", "voiceSupport": false, "medicalTerminology": false, "culturalAdaptation": true}], "healthcareSystem": {"systemType": "public", "primaryCareStructure": "Health posts and health centers", "specialistAccess": "very_limited", "emergencyServices": {"emergencyNumber": "907", "responseTime": 90, "coverage": "urban_only", "integration": "basic"}, "traditionalMedicine": {"recognition": "informal", "regulation": "minimal", "integration": "parallel", "safetyProtocols": ["basic traditional medicine documentation", "patient safety awareness", "herb-drug interaction monitoring"]}, "healthInsurance": {"coverage": "expanding", "providers": ["CBHI", "SHI"], "digitalIntegration": false, "aiCoverage": false}, "digitalHealthReadiness": "low"}, "regulatory": {"healthAuthority": "Ministry of Health Ethiopia", "dataProtectionLaw": "Data Protection Proclamation (Draft)", "medicalDeviceRegulation": "EFDA Medical Device Regulation", "aiRegulation": "Digital Ethiopia 2025 Strategy", "telemedicineRegulation": "Telemedicine Guidelines (Draft)", "requiredApprovals": [{"authority": "Ministry of Health", "approvalType": "Health Technology Assessment", "status": "not_started", "conditions": [], "validUntil": null}, {"authority": "EFDA", "approvalType": "Medical Device Registration", "status": "not_started", "conditions": [], "validUntil": null}, {"authority": "Ministry of Innovation and Technology", "approvalType": "Digital Platform Registration", "status": "not_started", "conditions": [], "validUntil": null}], "complianceRequirements": [{"requirement": "Health data protection standards", "category": "data_protection", "status": "not_assessed", "evidence": [], "lastAssessed": "2025-01-06T00:00:00Z", "nextAssessment": "2025-02-06T00:00:00Z"}, {"requirement": "Medical technology validation", "category": "medical_device", "status": "not_assessed", "evidence": [], "lastAssessed": "2025-01-06T00:00:00Z", "nextAssessment": "2025-02-06T00:00:00Z"}, {"requirement": "Cultural and linguistic appropriateness", "category": "cultural_adaptation", "status": "not_assessed", "evidence": [], "lastAssessed": "2025-01-06T00:00:00Z", "nextAssessment": "2025-02-06T00:00:00Z"}]}, "technical": {"infrastructure": {"cloudProvider": "AWS", "region": "eu-west-1", "dataCenter": "Dublin", "backupRegion": "af-south-1", "scalingStrategy": "manual"}, "connectivity": {"internetPenetration": 21.0, "mobileNetworkCoverage": 65.0, "averageSpeed": 8.5, "reliability": "low", "costPerGB": 4.5}, "security": {"encryptionStandard": "AES-256-GCM", "authenticationMethod": "JWT + MFA", "accessControls": ["RBAC", "ABAC"], "auditRequirements": ["Health Data Protection", "Government Standards"], "incidentResponsePlan": "Ethiopia Incident Response Plan v1.0"}, "integration": {"existingSystems": [{"name": "HMIS", "type": "his", "vendor": "Ministry of Health Ethiopia", "version": "2.0", "integrationComplexity": "high", "integrationStatus": "not_started"}], "apiStandards": ["REST"], "dataFormats": ["JSON"], "interoperabilityLevel": "basic"}}, "cultural": {"primaryCultures": ["amhara", "oromo", "tigray", "sidama", "gurage"], "communicationStyles": ["respectful", "hierarchical", "community-oriented"], "familyStructures": ["extended", "nuclear", "clan-based"], "religiousConsiderations": ["orthodox_christian", "islamic", "traditional"], "genderConsiderations": ["respect for elders", "traditional gender roles", "family authority"], "ageRespectLevels": ["very high elder respect", "age-based hierarchy"], "traditionalPractices": ["traditional healing", "herbal medicine", "spiritual healing", "community healing rituals"], "culturalSensitivities": ["respect for traditional authority", "importance of family and community involvement", "religious considerations in healthcare", "traditional healing practices"]}, "emergency": {"protocols": [{"severity": "critical", "protocol": "Emergency response + community mobilization", "culturalAdaptations": ["Include family and community elders in emergency decisions", "Respect religious practices during emergencies", "Consider traditional healing alongside modern medicine"], "responseTime": 2, "escalationRules": ["Contact available emergency services", "Notify family head and community elders", "Alert religious leader if requested", "Mobilize community health workers"]}, {"severity": "high", "protocol": "Urgent care + cultural integration", "culturalAdaptations": ["Family and community consultation for treatment decisions", "Religious accommodation in treatment", "Traditional medicine integration"], "responseTime": 5, "escalationRules": ["Arrange urgent care through available channels", "Facilitate family and community consultation", "Provide religious accommodation", "Coordinate traditional medicine integration"]}], "culturalAdaptations": ["Community-centered decision making", "Religious accommodation", "Traditional healer integration", "Extended family involvement"], "familyNotificationRules": ["Notify family head and elders first", "Respect traditional hierarchy", "Include community leaders for major decisions"], "traditionalHealerIntegration": true, "responseTimeTargets": {"critical": 2, "high": 5, "medium": 15, "low": 60}}, "demographics": {"population": 123379000, "urbanPopulation": 22.0, "medianAge": 19.5, "literacyRate": 51.8, "healthLiteracyRate": 25.0, "internetUsers": 21.0, "mobileSubscribers": 44.0}, "economicFactors": {"gdpPerCapita": 936, "healthcareSpendingPerCapita": 27, "outOfPocketHealthSpending": 34.0, "povertyRate": 23.5, "unemploymentRate": 19.1}, "diseaseProfile": {"topCauses": ["respiratory infections", "diarrheal diseases", "tuberculosis", "malaria", "neonatal conditions", "maternal conditions", "malnutrition", "HIV/AIDS"], "endemicDiseases": ["malaria", "tuberculosis", "HIV/AIDS", "trachoma", "schistosomiasis", "onchocerciasis"], "emergingThreats": ["non-communicable diseases", "antimicrobial resistance", "climate-related diseases"]}, "seasonalFactors": {"mainRainySeasonMonths": [6, 7, 8, 9], "shortRainySeasonMonths": [2, 3, 4, 5], "drySeasonMonths": [10, 11, 12, 1], "malariaSeasonPeak": [7, 8, 9, 10], "meningitisSeasonPeak": [12, 1, 2, 3]}, "deployment": {"pilotRegions": ["Addis A<PERSON>ba", "Oromia"], "rolloutPhases": [{"phase": 1, "regions": ["Addis A<PERSON>ba"], "duration": "6 months", "targetPopulation": 5000000}, {"phase": 2, "regions": ["Oromia", "Amhara"], "duration": "12 months", "targetPopulation": 25000000}, {"phase": 3, "regions": ["Tigray", "SNNP", "Somali"], "duration": "18 months", "targetPopulation": 60000000}], "successMetrics": ["User adoption rate > 8%", "Clinical accuracy > 80%", "Cultural appropriateness > 85%", "Emergency response time < 2 seconds", "Patient satisfaction > 3.5/5.0"]}}