/**
 * PWA Testing Utilities
 * Helper functions for testing offline-first features
 */

/**
 * Network Simulation for Testing
 */
export class NetworkSimulator {
  constructor() {
    this.originalOnLine = navigator.onLine;
    this.isSimulating = false;
  }

  goOffline() {
    this.isSimulating = true;
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false
    });
    window.dispatchEvent(new Event('offline'));
    console.log('📡 Network: Simulating OFFLINE mode');
  }

  goOnline() {
    this.isSimulating = true;
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true
    });
    window.dispatchEvent(new Event('online'));
    console.log('📡 Network: Simulating ONLINE mode');
  }

  reset() {
    if (this.isSimulating) {
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: this.originalOnLine
      });
      this.isSimulating = false;
      console.log('📡 Network: Reset to original state');
    }
  }

  simulateIntermittent(intervalMs = 5000) {
    const toggle = () => {
      if (navigator.onLine) {
        this.goOffline();
      } else {
        this.goOnline();
      }
    };

    const interval = setInterval(toggle, intervalMs);
    console.log(`📡 Network: Simulating intermittent connectivity (${intervalMs}ms)`);
    
    return () => {
      clearInterval(interval);
      this.reset();
    };
  }
}

/**
 * IndexedDB Testing Helper
 */
export class IndexedDBTestHelper {
  static async clearAllDatabases() {
    try {
      if ('databases' in indexedDB) {
        const databases = await indexedDB.databases();
        
        for (const db of databases) {
          await new Promise((resolve, reject) => {
            const deleteReq = indexedDB.deleteDatabase(db.name);
            deleteReq.onsuccess = () => resolve();
            deleteReq.onerror = () => reject(deleteReq.error);
          });
        }
        
        console.log('🗃️ IndexedDB: All databases cleared');
        return true;
      }
    } catch (error) {
      console.error('Failed to clear databases:', error);
      return false;
    }
  }

  static async getDatabaseSizes() {
    if (!('estimate' in navigator.storage)) {
      return null;
    }

    try {
      const estimate = await navigator.storage.estimate();
      return {
        quota: estimate.quota,
        usage: estimate.usage,
        usagePercentage: ((estimate.usage / estimate.quota) * 100).toFixed(2)
      };
    } catch (error) {
      console.error('Failed to get storage estimate:', error);
      return null;
    }
  }

  static async monitorStorage(intervalMs = 10000) {
    const updateStorageInfo = async () => {
      const sizes = await this.getDatabaseSizes();
      if (sizes) {
        console.log(`💾 Storage: ${(sizes.usage / 1024 / 1024).toFixed(2)}MB / ${(sizes.quota / 1024 / 1024).toFixed(2)}MB (${sizes.usagePercentage}%)`);
      }
    };

    await updateStorageInfo();
    const interval = setInterval(updateStorageInfo, intervalMs);
    
    return () => {
      clearInterval(interval);
      console.log('💾 Storage: Stopped monitoring');
    };
  }
}

/**
 * Service Worker Testing Helper
 */
export class ServiceWorkerTestHelper {
  static async forceUpdate() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.update();
        console.log('⚙️ Service Worker: Forced update check');
        return true;
      } catch (error) {
        console.error('Failed to force SW update:', error);
        return false;
      }
    }
    return false;
  }

  static async skipWaiting() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready;
        if (registration.waiting) {
          registration.waiting.postMessage({ type: 'SKIP_WAITING' });
          console.log('⚙️ Service Worker: Skipped waiting');
          return true;
        }
      } catch (error) {
        console.error('Failed to skip waiting:', error);
      }
    }
    return false;
  }

  static async getStatus() {
    if (!('serviceWorker' in navigator)) {
      return { supported: false };
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      return {
        supported: true,
        installing: !!registration.installing,
        waiting: !!registration.waiting,
        active: !!registration.active,
        scope: registration.scope
      };
    } catch (error) {
      return { supported: true, error: error.message };
    }
  }

  static async clearAllCaches() {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log(`🗂️ Caches: Cleared ${cacheNames.length} caches`);
        return true;
      } catch (error) {
        console.error('Failed to clear caches:', error);
        return false;
      }
    }
    return false;
  }

  static async listCaches() {
    if (!('caches' in window)) {
      return [];
    }

    try {
      const cacheNames = await caches.keys();
      const cacheInfos = [];

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        
        let totalSize = 0;
        for (const request of keys) {
          const response = await cache.match(request);
          if (response) {
            const size = response.headers.get('content-length');
            if (size) {
              totalSize += parseInt(size, 10);
            }
          }
        }

        cacheInfos.push({
          name: cacheName,
          entries: keys.length,
          estimatedSize: totalSize
        });
      }

      return cacheInfos;
    } catch (error) {
      console.error('Failed to list caches:', error);
      return [];
    }
  }
}

/**
 * Voice Testing Helper
 */
export class VoiceTestHelper {
  static generateFakeAudioChunk(durationMs = 100) {
    const sampleRate = 44100;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    const buffer = new ArrayBuffer(samples * 2); // 16-bit audio
    const view = new Int16Array(buffer);
    
    // Generate sine wave test tone
    for (let i = 0; i < samples; i++) {
      const t = i / sampleRate;
      const frequency = 440; // A4 note
      const amplitude = 16384; // Half of max 16-bit value
      view[i] = Math.sin(2 * Math.PI * frequency * t) * amplitude;
    }
    
    return buffer;
  }

  static async simulateVoiceSession(onChunk, durationSeconds = 5, chunkIntervalMs = 100) {
    const totalChunks = Math.floor(durationSeconds * 1000 / chunkIntervalMs);
    
    console.log(`🎤 Voice: Simulating ${durationSeconds}s recording session`);
    
    for (let i = 0; i < totalChunks; i++) {
      const chunk = this.generateFakeAudioChunk(chunkIntervalMs);
      await onChunk(chunk, {
        chunkIndex: i,
        duration: chunkIntervalMs,
        quality: 'test'
      });
      
      await new Promise(resolve => setTimeout(resolve, chunkIntervalMs));
    }
    
    console.log('🎤 Voice: Recording simulation completed');
  }
}

/**
 * Performance Testing Helper
 */
export class PerformanceTestHelper {
  static measurements = new Map();

  static startMeasurement(name) {
    this.measurements.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  static endMeasurement(name) {
    const measurement = this.measurements.get(name);
    if (measurement) {
      measurement.endTime = performance.now();
      measurement.duration = measurement.endTime - measurement.startTime;
      console.log(`⏱️ Performance: ${name} took ${measurement.duration.toFixed(2)}ms`);
      return measurement.duration;
    }
    return null;
  }

  static getAllMeasurements() {
    const results = {};
    for (const [name, measurement] of this.measurements) {
      if (measurement.duration !== null) {
        results[name] = measurement.duration;
      }
    }
    return results;
  }

  static clearMeasurements() {
    this.measurements.clear();
    console.log('⏱️ Performance: Cleared all measurements');
  }
}

/**
 * Comprehensive PWA Test Suite
 */
export class PWATestSuite {
  constructor() {
    this.networkSim = new NetworkSimulator();
    this.results = [];
  }

  async testOfflineCapability(voicePersistence) {
    console.log('🧪 Testing: Offline capability');
    
    try {
      // Go offline
      this.networkSim.goOffline();
      
      // Generate and store voice chunks
      const chunk = VoiceTestHelper.generateFakeAudioChunk(1000);
      const result = await voicePersistence?.storeVoiceChunk?.(chunk, {
        quality: 'test',
        duration: 1000
      });
      
      this.results.push({
        test: 'offline-storage',
        passed: !!result,
        details: result ? `Stored chunk: ${result}` : 'Failed to store'
      });
      
      // Go back online
      this.networkSim.goOnline();
      
      // Test sync
      const syncResult = await voicePersistence?.syncPendingChunks?.();
      this.results.push({
        test: 'offline-sync',
        passed: !!syncResult,
        details: syncResult ? 'Sync completed' : 'Sync failed'
      });
      
    } catch (error) {
      this.results.push({
        test: 'offline-capability',
        passed: false,
        error: error.message
      });
    }
  }

  async testServiceWorker() {
    console.log('🧪 Testing: Service worker');
    
    try {
      const status = await ServiceWorkerTestHelper.getStatus();
      
      this.results.push({
        test: 'service-worker',
        passed: status.supported && status.active,
        details: status
      });
      
    } catch (error) {
      this.results.push({
        test: 'service-worker',
        passed: false,
        error: error.message
      });
    }
  }

  async runAllTests(voicePersistence) {
    console.log('🧪 Starting PWA Test Suite');
    this.results = [];
    
    await this.testServiceWorker();
    
    if (voicePersistence) {
      await this.testOfflineCapability(voicePersistence);
    }
    
    // Storage test
    const storageSize = await IndexedDBTestHelper.getDatabaseSizes();
    this.results.push({
      test: 'storage-estimation',
      passed: !!storageSize,
      details: storageSize ? `${(storageSize.usage / 1024 / 1024).toFixed(2)}MB used` : 'Not available'
    });
    
    console.log('🧪 Test Suite Completed:', this.results);
    return this.results;
  }

  cleanup() {
    this.networkSim.reset();
    console.log('🧪 Test suite cleanup completed');
  }

  getResults() {
    return this.results;
  }
}

// Utility function to run quick PWA health check
export const runPWAHealthCheck = async () => {
  const results = {
    serviceWorker: false,
    storage: false,
    network: false,
    backgroundSync: false
  };

  try {
    // Check Service Worker
    if ('serviceWorker' in navigator) {
      const registration = await navigator.serviceWorker.ready;
      results.serviceWorker = !!registration.active;
    }

    // Check Storage API
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      results.storage = !!estimate.quota;
    }

    // Check Network API
    results.network = 'onLine' in navigator;

    // Check Background Sync
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      results.backgroundSync = true;
    }

  } catch (error) {
    console.error('PWA Health Check Error:', error);
  }

  console.log('🏥 PWA Health Check:', results);
  return results;
};

export default {
  NetworkSimulator,
  IndexedDBTestHelper,
  ServiceWorkerTestHelper,
  VoiceTestHelper,
  PerformanceTestHelper,
  PWATestSuite,
  runPWAHealthCheck
};
