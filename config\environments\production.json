{"environment": "production", "description": "Production environment for VoiceHealth AI live deployment", "version": "1.0.0", "lastUpdated": "2025-01-06T00:00:00Z", "infrastructure": {"cloudProvider": "AWS", "regions": {"primary": "eu-west-1", "secondary": "af-south-1", "backup": "eu-central-1"}, "availabilityZones": ["eu-west-1a", "eu-west-1b", "eu-west-1c"], "vpc": {"cidr": "10.0.0.0/16", "subnets": {"public": ["********/24", "********/24", "********/24"], "private": ["*********/24", "*********/24", "*********/24"], "database": ["*********/24", "*********/24", "*********/24"]}}, "scaling": {"autoScaling": true, "minInstances": 5, "maxInstances": 50, "targetCPUUtilization": 60}}, "application": {"baseUrl": "https://voicehealth.ai", "apiBaseUrl": "https://api.voicehealth.ai", "frontendUrl": "https://app.voicehealth.ai", "adminUrl": "https://admin.voicehealth.ai", "features": {"enableDebugMode": false, "enableTestData": false, "enableMockServices": false, "enablePerformanceMonitoring": true, "enableDetailedLogging": false, "enableCulturalValidation": true, "enableTraditionalMedicine": true, "enableEmergencyProtocols": true, "enableVoiceProcessing": true, "enableAIOrchestration": true}, "limits": {"maxConcurrentUsers": 100000, "maxRequestsPerMinute": 1000000, "maxFileUploadSize": "25MB", "maxAudioDuration": 300, "maxSessionDuration": 3600}}, "database": {"type": "postgresql", "primary": {"host": "prod-db-primary.voicehealth.ai", "port": 5432, "name": "voicehealth_production", "ssl": true, "connectionPool": {"min": 20, "max": 100, "idleTimeout": 30000}}, "readReplicas": [{"host": "prod-db-replica-1.voicehealth.ai", "port": 5432, "ssl": true}, {"host": "prod-db-replica-2.voicehealth.ai", "port": 5432, "ssl": true}], "backup": {"enabled": true, "frequency": "hourly", "retention": "30 days", "crossRegionBackup": true}}, "redis": {"cluster": {"enabled": true, "nodes": ["prod-redis-1.voicehealth.ai:6379", "prod-redis-2.voicehealth.ai:6379", "prod-redis-3.voicehealth.ai:6379"]}, "ssl": true, "maxConnections": 1000, "keyPrefix": "voicehealth:prod:"}, "storage": {"provider": "aws_s3", "buckets": {"primary": "voicehealth-production-storage", "backup": "voicehealth-production-backup", "logs": "voicehealth-production-logs"}, "region": "eu-west-1", "encryption": "AES256", "versioning": true, "crossRegionReplication": true, "lifecycle": {"transitionToIA": 30, "transitionToGlacier": 90, "expiration": 2555}}, "security": {"encryption": {"algorithm": "AES-256-GCM", "keyRotation": "weekly"}, "authentication": {"jwtSecret": "${PRODUCTION_JWT_SECRET}", "jwtExpiration": "8h", "refreshTokenExpiration": "7d", "mfaEnabled": true, "mfaRequired": true}, "cors": {"allowedOrigins": ["https://app.voicehealth.ai", "https://admin.voicehealth.ai"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Requested-With"]}, "rateLimiting": {"enabled": true, "windowMs": 60000, "maxRequests": 5000, "emergencyBypass": true, "strictMode": true}, "waf": {"enabled": true, "rules": ["sql_injection", "xss", "rate_limiting", "geo_blocking"]}}, "monitoring": {"enabled": true, "provider": "cloudwatch", "metrics": {"collectInterval": 15, "retentionPeriod": "1 year"}, "logging": {"level": "info", "format": "json", "destination": "cloudwatch", "retention": "1 year"}, "alerts": {"enabled": true, "channels": ["email", "slack", "<PERSON><PERSON><PERSON><PERSON>"], "thresholds": {"errorRate": 1, "responseTime": 1000, "cpuUsage": 70, "memoryUsage": 80}}, "healthChecks": {"enabled": true, "interval": 15, "timeout": 3000, "endpoints": ["/health", "/api/health", "/api/ai-health"]}}, "aiServices": {"providers": {"openai": {"enabled": true, "apiKey": "${PRODUCTION_OPENAI_API_KEY}", "model": "gpt-4", "maxTokens": 1000, "temperature": 0.3}, "anthropic": {"enabled": true, "apiKey": "${PRODUCTION_ANTHROPIC_API_KEY}", "model": "claude-3-sonnet-20240229", "maxTokens": 1000}, "elevenlabs": {"enabled": true, "apiKey": "${PRODUCTION_ELEVENLABS_API_KEY}", "voiceId": "ErXwobaYiN019PkySvjV"}}, "fallback": {"enabled": true, "order": ["openai", "anthropic"], "timeout": 15000}}, "culturalServices": {"validation": {"enabled": true, "strictMode": true, "cacheResults": true, "cacheTTL": 7200}, "focusGroups": {"enabled": true, "mockData": false, "regions": ["GH", "KE", "NG", "ZA", "ET"]}, "traditionalMedicine": {"enabled": true, "safetyChecks": true, "integrationLevel": "collaborative"}}, "emergencyServices": {"enabled": true, "responseTimeTarget": 2000, "escalationRules": {"critical": ["immediate_notification", "family_contact", "emergency_services", "supervisor_alert"], "high": ["urgent_notification", "family_contact", "supervisor_alert"], "medium": ["standard_notification"], "low": ["routine_notification"]}, "notifications": {"email": {"enabled": true, "provider": "ses", "fromAddress": "<EMAIL>"}, "sms": {"enabled": true, "provider": "twi<PERSON>", "fromNumber": "${PRODUCTION_TWILIO_PHONE_NUMBER}"}, "voice": {"enabled": true, "provider": "twi<PERSON>", "fromNumber": "${PRODUCTION_TWILIO_VOICE_NUMBER}"}}}, "testing": {"enabled": false, "testData": {"enabled": false, "autoGenerate": false, "seedData": false}, "mockServices": {"enabled": false, "aiProviders": false, "paymentGateway": false, "emergencyServices": false}, "performance": {"enabled": true, "loadTesting": false, "stressTesting": false}}, "compliance": {"hipaa": {"enabled": true, "auditLogging": true, "dataEncryption": true, "accessControls": true, "businessAssociateAgreements": true}, "gdpr": {"enabled": true, "dataRetention": "7 years", "rightToErasure": true, "consentManagement": true, "dataProcessingAgreements": true}, "regional": {"ghana": {"dataProtectionAct": true, "healthServiceCompliance": true}, "kenya": {"dataProtectionAct": true, "healthMinistryCompliance": true}, "nigeria": {"ndpr": true, "nafdacCompliance": true}, "southAfrica": {"popia": true, "sahpraCompliance": true}, "ethiopia": {"dataProtection": true, "healthMinistryCompliance": true}}}, "deployment": {"strategy": "blue_green", "rollback": {"enabled": true, "automaticTriggers": ["high_error_rate", "performance_degradation", "health_check_failure"], "maxRollbackTime": 180}, "healthChecks": {"enabled": true, "warmupTime": 120, "healthyThreshold": 5, "unhealthyThreshold": 2}, "canaryDeployment": {"enabled": true, "trafficPercentage": 5, "duration": 300}}, "environmentVariables": {"NODE_ENV": "production", "LOG_LEVEL": "info", "ENABLE_CORS": "true", "ENABLE_RATE_LIMITING": "true", "ENABLE_MONITORING": "true", "ENABLE_HEALTH_CHECKS": "true", "DATABASE_SSL": "true", "REDIS_SSL": "true", "S3_ENCRYPTION": "true", "STRICT_SECURITY": "true"}}