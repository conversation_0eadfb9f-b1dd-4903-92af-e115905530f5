/**
 * AGENT ORCHESTRATOR SERVICE
 * 
 * Coordinates interactions between multiple AI agents, manages agent handoffs,
 * and provides a unified interface for the multi-agent consultation system.
 * Replaces the hardcoded agent management with dynamic agent coordination.
 * 
 * FEATURES:
 * - Dynamic agent selection and routing
 * - Agent handoff management
 * - Multi-agent collaboration
 * - Context transfer between agents
 * - Performance monitoring and optimization
 * - Emergency protocol coordination
 */

import { agentRegistry, type AgentRegistry } from './AgentRegistry';
import { memoryManager, type MemoryManager } from './MemoryManager';
import type { 
  IAgent, 
  AgentRequest, 
  AgentResponse, 
  AgentRole,
  AgentCapability,
  AgentHandoffSuggestion
} from '../agents/BaseAgent';

// Import concrete agent implementations
import GeneralPractitionerAgent from '../agents/GeneralPractitionerAgent';
import EmergencyAgent from '../agents/EmergencyAgent';
import TriageAgent from '../agents/TriageAgent';
import CardiologistAgent from '../agents/CardiologistAgent';
import NutritionistAgent from '../agents/NutritionistAgent';
import MentalHealthAgent from '../agents/MentalHealthAgent';
import { advancedContextIntegrator, type UnifiedContext } from './AdvancedContextIntegrator';
import { enhancedPatientContextService } from './EnhancedPatientContextService';
import { contextAssemblyService } from './ContextAssemblyService';
import { vocalAnalysisService } from './VocalAnalysisService';
import { goalTrackerAgent } from '../agents/GoalTrackerAgent';
import { consultationConclusionService } from './ConsultationConclusionService';
import { supabase } from '../utils/supabaseClient';
import type {
  EmotionalContext,
  VocalAnalysisRequest,
  VocalAnalysisResponse
} from '../types/emotional';

export interface OrchestrationRequest {
  sessionId: string;
  userMessage: string;
  userId?: string;
  currentAgentId?: string;
  requestedCapabilities?: AgentCapability[];
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  patientContext?: any;
  audioData?: Blob | ArrayBuffer; // For vocal analysis
  emotionalContext?: EmotionalContext; // Pre-analyzed emotional context
}

export interface OrchestrationResponse {
  agentResponse: AgentResponse;
  handoffOccurred: boolean;
  previousAgent?: string;
  nextRecommendedAgent?: string;
  collaboratingAgents?: string[];
  sessionContext: any;
  unifiedContext?: UnifiedContext;
  contextualInsights?: string[];
  performanceMetrics?: any;
}

export interface AgentHandoffContext {
  fromAgent: IAgent;
  toAgent: IAgent;
  reason: string;
  contextSummary: string;
  patientConsent: boolean;
  handoffTime: string;
}

export class AgentOrchestrator {
  private registry: AgentRegistry;
  private memory: MemoryManager;
  private activeAgents: Map<string, string> = new Map(); // sessionId -> agentId
  private handoffHistory: Map<string, AgentHandoffContext[]> = new Map(); // sessionId -> handoffs

  constructor() {
    this.registry = agentRegistry;
    this.memory = memoryManager;
    
    console.log('🎭 Initializing Agent Orchestrator...');
  }

  /**
   * Initialize the orchestrator and register all agents
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 Starting Agent Orchestrator initialization...');

      // Register core agents
      await this.registerCoreAgents();

      // Verify all agents are healthy
      const stats = this.registry.getRegistryStats();
      console.log(`✅ Agent Orchestrator initialized with ${stats.totalAgents} agents (${stats.healthyAgents} healthy)`);

    } catch (error) {
      console.error('❌ Failed to initialize Agent Orchestrator:', error);
      throw error;
    }
  }

  /**
   * Process a user request through the agent system with advanced context
   */
  async processRequest(request: OrchestrationRequest): Promise<OrchestrationResponse> {
    const startTime = Date.now();

    try {
      console.log(`🎯 Processing request with comprehensive context for session: ${request.sessionId}`);

      // Get conversation history for context
      const conversationHistory = await this.memory.getConversationHistory(request.sessionId);

      // Load comprehensive patient context if userId provided
      let enhancedPatientContext = null;
      let assembledContext = null;

      if (request.userId) {
        console.log(`👤 Loading comprehensive patient context for user: ${request.userId}`);
        enhancedPatientContext = await enhancedPatientContextService.loadPatientContext(
          request.userId,
          request.sessionId
        );

        // Assemble rich context for agent prompts
        assembledContext = await contextAssemblyService.assembleContext(
          enhancedPatientContext,
          conversationHistory,
          request.userMessage,
          {
            urgencyLevel: request.urgencyLevel,
            conversationPhase: 'assessment', // TODO: Determine from conversation state
            includeRegionalContext: true,
            includeCulturalContext: true
          }
        );
      }

      // Perform vocal analysis for emotional intelligence if audio data provided
      let emotionalContext: EmotionalContext | undefined = request.emotionalContext;

      if (request.audioData && !emotionalContext) {
        console.log('🎭 Analyzing vocal tone for emotional context...');

        try {
          const vocalAnalysisRequest: VocalAnalysisRequest = {
            audio_data: request.audioData,
            session_id: request.sessionId,
            user_id: request.userId,
            duration_ms: 5000, // Assume 5 second audio clip
            emergency_context: request.urgencyLevel === 'critical'
          };

          const vocalAnalysisResponse = await vocalAnalysisService.analyzeVocalTone(vocalAnalysisRequest);

          if (vocalAnalysisResponse.success && vocalAnalysisResponse.data) {
            emotionalContext = vocalAnalysisResponse.data;
            console.log(`✅ Emotional context analyzed: ${emotionalContext.sentiment} sentiment, ${emotionalContext.emotional_cues.join(', ')}`);
          } else {
            console.warn('⚠️ Vocal analysis failed, continuing without emotional context:', vocalAnalysisResponse.error);
          }
        } catch (error) {
          console.warn('⚠️ Vocal analysis error, continuing without emotional context:', error);
        }
      }

      // Create enhanced agent request with comprehensive context including emotional intelligence
      const agentRequest: AgentRequest = {
        sessionId: request.sessionId,
        userMessage: request.userMessage,
        conversationHistory,
        patientContext: enhancedPatientContext,
        urgencyLevel: request.urgencyLevel || 'medium',
        emotionalContext
      };

      // Enhance request with advanced context (existing system)
      const enhancedRequest = await advancedContextIntegrator.enhanceAgentRequest(agentRequest);

      // Determine which agent should handle this request (now with rich context)
      const selectedAgent = await this.selectAgentWithContext(enhancedRequest, conversationHistory);

      if (!selectedAgent) {
        throw new Error('No suitable agent found for request');
      }

      // Check if this is a handoff from another agent
      const currentAgentId = this.activeAgents.get(request.sessionId);
      const handoffOccurred = currentAgentId && currentAgentId !== selectedAgent.id;

      if (handoffOccurred) {
        await this.handleAgentHandoff(request.sessionId, currentAgentId!, selectedAgent.id, 'System-initiated handoff');
      }

      // Update active agent for session
      this.activeAgents.set(request.sessionId, selectedAgent.id);

      // Add assembled context to the enhanced request if available
      if (assembledContext) {
        enhancedRequest.assembledContext = assembledContext;
        console.log(`🧩 Context assembled: ${assembledContext.tokenUsage.contextTokens} tokens`);
      }

      // Get response from selected agent using enhanced request with comprehensive context
      const agentResponse = await selectedAgent.handleMessage(enhancedRequest);

      // Run goal tracking analysis in parallel (meta-agent)
      const goalTrackingPromise = this.runGoalTracking(enhancedRequest, agentResponse);

      // Analyze for consultation conclusion and trigger educational content if needed
      const conclusionAnalysisPromise = this.analyzeConsultationConclusion(
        request.sessionId,
        enhancedRequest,
        agentResponse,
        conversationHistory
      );

      // Save the response to memory
      await this.saveAgentResponse(request.sessionId, agentResponse);

      // Wait for background processes to complete (non-blocking for user experience)
      Promise.allSettled([goalTrackingPromise, conclusionAnalysisPromise]).then(results => {
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            const processName = index === 0 ? 'Goal tracking' : 'Conclusion analysis';
            console.warn(`${processName} failed but continuing:`, result.reason);
          }
        });
      });

      // Check if agent suggests handoffs
      let nextRecommendedAgent: string | undefined;
      if (agentResponse.suggestedHandoffs && agentResponse.suggestedHandoffs.length > 0) {
        const primaryHandoff = agentResponse.suggestedHandoffs[0];
        const targetAgent = this.registry.getAgentsByRole(primaryHandoff.targetAgentRole)[0];
        if (targetAgent) {
          nextRecommendedAgent = targetAgent.id;
        }
      }

      // Get session context
      const sessionContext = await this.memory.getConversationContext(request.sessionId);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Request processed in ${processingTime}ms by agent: ${selectedAgent.name} with advanced context`);

      // Extract contextual insights from unified context
      const contextualInsights = enhancedRequest.unifiedContext.synthesizedContext.keyInsights.map(insight => insight.insight);

      return {
        agentResponse,
        handoffOccurred,
        previousAgent: handoffOccurred ? currentAgentId : undefined,
        nextRecommendedAgent,
        collaboratingAgents: [], // TODO: Implement multi-agent collaboration
        sessionContext,
        unifiedContext: enhancedRequest.unifiedContext,
        contextualInsights,
        performanceMetrics: enhancedRequest.unifiedContext.performanceMetrics
      };

    } catch (error) {
      console.error('❌ Error processing request:', error);
      throw error;
    }
  }

  /**
   * Perform agent handoff
   */
  async performHandoff(
    sessionId: string, 
    targetAgentRole: AgentRole, 
    reason: string,
    patientConsent: boolean = true
  ): Promise<boolean> {
    try {
      console.log(`🔄 Performing handoff for session ${sessionId} to ${targetAgentRole}`);

      const currentAgentId = this.activeAgents.get(sessionId);
      if (!currentAgentId) {
        console.warn('⚠️ No current agent found for handoff');
        return false;
      }

      const targetAgents = this.registry.getAgentsByRole(targetAgentRole);
      if (targetAgents.length === 0) {
        console.warn(`⚠️ No agents found for role: ${targetAgentRole}`);
        return false;
      }

      const targetAgent = targetAgents[0]; // Select first available agent
      
      await this.handleAgentHandoff(sessionId, currentAgentId, targetAgent.id, reason, patientConsent);
      
      // Update active agent
      this.activeAgents.set(sessionId, targetAgent.id);

      console.log(`✅ Handoff completed: ${currentAgentId} → ${targetAgent.id}`);
      return true;

    } catch (error) {
      console.error('❌ Handoff failed:', error);
      return false;
    }
  }

  /**
   * Get current agent for a session
   */
  getCurrentAgent(sessionId: string): IAgent | null {
    const agentId = this.activeAgents.get(sessionId);
    return agentId ? this.registry.getAgent(agentId) : null;
  }

  /**
   * Get handoff history for a session
   */
  getHandoffHistory(sessionId: string): AgentHandoffContext[] {
    return this.handoffHistory.get(sessionId) || [];
  }

  /**
   * Get orchestrator statistics
   */
  getOrchestratorStats(): {
    activeSessions: number;
    totalHandoffs: number;
    agentUtilization: Record<string, number>;
    registryStats: any;
  } {
    const totalHandoffs = Array.from(this.handoffHistory.values())
      .reduce((sum, handoffs) => sum + handoffs.length, 0);

    const agentUtilization: Record<string, number> = {};
    this.activeAgents.forEach(agentId => {
      agentUtilization[agentId] = (agentUtilization[agentId] || 0) + 1;
    });

    return {
      activeSessions: this.activeAgents.size,
      totalHandoffs,
      agentUtilization,
      registryStats: this.registry.getRegistryStats()
    };
  }

  /**
   * Register core agents in the system
   */
  private async registerCoreAgents(): Promise<void> {
    console.log('📝 Registering core agents...');

    // Register Triage Agent (highest priority - first contact)
    const triageAgent = new TriageAgent(this.memory);
    await this.registry.registerAgent(triageAgent, 100);

    // Register Emergency Agent (critical situations)
    const emergencyAgent = new EmergencyAgent(this.memory);
    await this.registry.registerAgent(emergencyAgent, 95);

    // Register General Practitioner Agent (primary care)
    const gpAgent = new GeneralPractitionerAgent(this.memory);
    await this.registry.registerAgent(gpAgent, 80);

    // Register Specialist Agents
    const cardiologistAgent = new CardiologistAgent(this.memory);
    await this.registry.registerAgent(cardiologistAgent, 85);

    const nutritionistAgent = new NutritionistAgent(this.memory);
    await this.registry.registerAgent(nutritionistAgent, 75);

    const mentalHealthAgent = new MentalHealthAgent(this.memory);
    await this.registry.registerAgent(mentalHealthAgent, 85);

    console.log('✅ All agents registered successfully (6 total agents)');
  }

  /**
   * Select the most appropriate agent for a request with advanced context
   */
  private async selectAgentWithContext(enhancedRequest: any, conversationHistory: any[]): Promise<IAgent | null> {
    try {
      console.log('🎯 Selecting agent with advanced context analysis...');

      // Use unified context for intelligent agent selection
      const unifiedContext = enhancedRequest.unifiedContext;
      const riskAssessment = unifiedContext.synthesizedContext.riskAssessment;
      const contextualFlags = unifiedContext.synthesizedContext.contextualFlags;

      // Check for emergency situations first
      const criticalFlags = contextualFlags.filter(flag => flag.severity === 'emergency' || flag.severity === 'critical');
      if (criticalFlags.length > 0 || riskAssessment.overallRiskLevel === 'critical') {
        console.log('🚨 Critical situation detected - routing to emergency agent');
        return this.registry.getAgentsByRole('emergency')[0] || null;
      }

      // Check for high-risk situations
      if (riskAssessment.overallRiskLevel === 'high') {
        // Determine appropriate specialist based on risk factors
        const primaryRiskFactors = riskAssessment.riskFactors.slice(0, 3);
        for (const riskFactor of primaryRiskFactors) {
          if (riskFactor.category === 'clinical') {
            // Route to appropriate specialist based on clinical risk
            if (riskFactor.factor.includes('cardiac') || riskFactor.factor.includes('heart')) {
              return this.registry.getAgentsByRole('cardiologist')[0] || null;
            }
            if (riskFactor.factor.includes('mental') || riskFactor.factor.includes('psychiatric')) {
              return this.registry.getAgentsByRole('psychiatrist')[0] || null;
            }
          }
        }
      }

      // Use contextual insights for agent selection
      const keyInsights = unifiedContext.synthesizedContext.keyInsights;
      for (const insight of keyInsights) {
        if (insight.urgency === 'critical' || insight.urgency === 'high') {
          if (insight.type === 'clinical_pattern') {
            // Route based on clinical pattern
            if (insight.insight.includes('cardiovascular') || insight.insight.includes('cardiac')) {
              return this.registry.getAgentsByRole('cardiologist')[0] || null;
            }
            if (insight.insight.includes('nutrition') || insight.insight.includes('dietary')) {
              return this.registry.getAgentsByRole('nutritionist')[0] || null;
            }
            if (insight.insight.includes('mental health') || insight.insight.includes('psychiatric')) {
              return this.registry.getAgentsByRole('psychiatrist')[0] || null;
            }
          }
        }
      }

      // Fall back to original agent selection logic
      return this.selectAgent(enhancedRequest, conversationHistory);

    } catch (error) {
      console.error('❌ Error in context-based agent selection:', error);
      // Fall back to original selection
      return this.selectAgent(enhancedRequest, conversationHistory);
    }
  }

  /**
   * Select the best agent for a request (original method)
   */
  private async selectAgent(request: OrchestrationRequest, conversationHistory: any[]): Promise<IAgent | null> {
    // For new conversations, start with triage agent
    if (conversationHistory.length === 0) {
      const triageAgents = this.registry.getAgentsByRole('triage');
      if (triageAgents.length > 0) {
        console.log('🏥 New conversation - routing to Triage Agent');
        return triageAgents[0];
      }
    }

    // Check if current agent can continue handling the request
    const currentAgentId = this.activeAgents.get(request.sessionId);
    if (currentAgentId) {
      const currentAgent = this.registry.getAgent(currentAgentId);
      if (currentAgent && currentAgent.canHandle({
        sessionId: request.sessionId,
        userMessage: request.userMessage,
        conversationHistory,
        urgencyLevel: request.urgencyLevel
      })) {
        console.log(`🔄 Continuing with current agent: ${currentAgent.name}`);
        return currentAgent;
      }
    }

    // Use registry to select best agent
    const agentRequest: AgentRequest = {
      sessionId: request.sessionId,
      userMessage: request.userMessage,
      conversationHistory,
      urgencyLevel: request.urgencyLevel,
      requestedCapabilities: request.requestedCapabilities
    };

    const selection = this.registry.selectAgent(agentRequest, {
      urgencyLevel: request.urgencyLevel,
      requireHealthy: true
    });

    if (selection) {
      console.log(`🎯 Selected agent: ${selection.agent.name} (confidence: ${selection.confidence.toFixed(2)})`);
      return selection.agent;
    }

    console.warn('⚠️ No suitable agent found');
    return null;
  }

  /**
   * Handle agent handoff process
   */
  private async handleAgentHandoff(
    sessionId: string,
    fromAgentId: string,
    toAgentId: string,
    reason: string,
    patientConsent: boolean = true
  ): Promise<void> {
    const fromAgent = this.registry.getAgent(fromAgentId);
    const toAgent = this.registry.getAgent(toAgentId);

    if (!fromAgent || !toAgent) {
      throw new Error('Invalid agent IDs for handoff');
    }

    // Create handoff context
    const handoffContext: AgentHandoffContext = {
      fromAgent,
      toAgent,
      reason,
      contextSummary: `Handoff from ${fromAgent.name} to ${toAgent.name}: ${reason}`,
      patientConsent,
      handoffTime: new Date().toISOString()
    };

    // Save handoff to history
    if (!this.handoffHistory.has(sessionId)) {
      this.handoffHistory.set(sessionId, []);
    }
    this.handoffHistory.get(sessionId)!.push(handoffContext);

    // Log handoff to memory for audit trail
    await this.memory.saveMessage(
      sessionId,
      'system',
      'orchestrator',
      'Agent Orchestrator',
      `AGENT HANDOFF: ${fromAgent.name} → ${toAgent.name}. Reason: ${reason}`,
      0, // System messages get priority sequence
      {
        handoffType: 'agent_transfer',
        fromAgent: fromAgent.id,
        toAgent: toAgent.id,
        reason,
        patientConsent,
        timestamp: handoffContext.handoffTime
      }
    );

    console.log(`🔄 Agent handoff logged: ${fromAgent.name} → ${toAgent.name}`);
  }

  /**
   * Save agent response to memory
   */
  private async saveAgentResponse(sessionId: string, response: AgentResponse): Promise<void> {
    const conversationHistory = await this.memory.getConversationHistory(sessionId);
    const sequenceNumber = conversationHistory.length + 1;

    await this.memory.saveMessage(
      sessionId,
      'agent',
      response.agentId,
      response.agentName,
      response.content,
      sequenceNumber,
      {
        confidence: response.confidence,
        reasoning: response.reasoning,
        emergencyFlags: response.emergencyFlags,
        suggestedHandoffs: response.suggestedHandoffs,
        followUpActions: response.followUpActions,
        ...response.metadata
      }
    );
  }

  /**
   * Run goal tracking analysis with meta-agent
   */
  private async runGoalTracking(request: AgentRequest, response: AgentResponse): Promise<void> {
    try {
      console.log('🎯 Running goal tracking analysis...');

      // Run goal tracker agent to analyze conversation progress
      const goalAnalysis = await goalTrackerAgent.handleMessage(request);

      console.log(`✅ Goal tracking completed: ${goalAnalysis.metadata?.activeGoals || 0} active goals`);

    } catch (error) {
      console.error('❌ Goal tracking failed:', error);
      throw error;
    }
  }

  /**
   * Get steering guidance for current conversation turn
   */
  private async getSteeringGuidance(sessionId: string, turnNumber: number): Promise<any[]> {
    return await performanceMonitoringService.measurePerformance(
      'agent_orchestrator',
      'steering_guidance',
      async () => {
        try {
          const { data: steeringGuidance, error } = await supabase
            .rpc('get_steering_guidance', {
              p_session_id: sessionId,
              p_turn_number: turnNumber
            });

          if (error) {
            console.warn('Failed to get steering guidance:', error);
            return [];
          }

          return steeringGuidance || [];

        } catch (error) {
          console.error('Error getting steering guidance:', error);
          return [];
        }
      },
      { sessionId, turnNumber },
      sessionId
    );
  }

  /**
   * Analyze consultation for conclusion and trigger educational content
   */
  private async analyzeConsultationConclusion(
    sessionId: string,
    request: AgentRequest,
    response: AgentResponse,
    conversationHistory: ConversationMessage[]
  ): Promise<void> {
    try {
      console.log('🏁 Analyzing consultation for conclusion...');

      // Analyze for conclusion signals
      const conclusionAnalysis = await consultationConclusionService.analyzeForConclusion(
        sessionId,
        request,
        response,
        conversationHistory
      );

      console.log(`✅ Conclusion analysis completed: ${(conclusionAnalysis.conclusionProbability * 100).toFixed(0)}% probability`);

      // Log significant conclusions
      if (conclusionAnalysis.conclusionProbability > 0.75) {
        console.log(`🎓 Educational content generation triggered for session: ${sessionId}`);
      }

    } catch (error) {
      console.error('❌ Consultation conclusion analysis failed:', error);
      throw error;
    }
  }

  /**
   * Apply steering guidance to agent request
   */
  private applySteeringGuidance(request: AgentRequest, steeringGuidance: any[]): AgentRequest {
    if (steeringGuidance.length === 0) {
      return request;
    }

    // Add steering notes to the request
    const steeringNotes = steeringGuidance.map(guide =>
      `${guide.steering_type.toUpperCase()}: ${guide.steering_message}`
    );

    // Enhance the request with steering guidance
    const enhancedRequest = {
      ...request,
      steeringGuidance: steeringGuidance,
      steeringNotes: steeringNotes
    };

    console.log(`🎯 Applied ${steeringGuidance.length} steering guidance items`);

    return enhancedRequest;
  }

  /**
   * Shutdown the orchestrator
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Agent Orchestrator...');
    
    // Clear active sessions
    this.activeAgents.clear();
    this.handoffHistory.clear();

    // Registry will handle agent shutdown
    console.log('✅ Agent Orchestrator shutdown complete');
  }
}

// Export singleton instance
export const agentOrchestrator = new AgentOrchestrator();
export default agentOrchestrator;
