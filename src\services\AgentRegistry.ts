/**
 * AGENT REGISTRY SERVICE
 * 
 * Manages the dynamic registration, discovery, and lifecycle of AI agents.
 * Replaces the hardcoded agent lists with a proper registry system.
 * 
 * FEATURES:
 * - Dynamic agent registration and discovery
 * - Capability-based agent selection
 * - Health monitoring and failover
 * - Performance tracking and optimization
 * - HIPAA-compliant agent management
 * - Emergency agent prioritization
 */

import type { 
  IAgent, 
  AgentRole, 
  AgentCapability, 
  AgentRequest, 
  AgentResponse,
  AgentPerformanceMetrics 
} from '../agents/BaseAgent';

export interface AgentRegistryEntry {
  agent: IAgent;
  registeredAt: string;
  lastHealthCheck: string;
  isHealthy: boolean;
  priority: number; // Higher = more preferred
  loadFactor: number; // Current load (0-1)
}

export interface AgentSelectionCriteria {
  requiredCapabilities?: AgentCapability[];
  preferredRole?: AgentRole;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  excludeAgents?: string[]; // Agent IDs to exclude
  requireHealthy?: boolean;
  maxLoadFactor?: number;
}

export interface AgentSelectionResult {
  agent: IAgent;
  confidence: number;
  reasoning: string;
  alternativeAgents?: IAgent[];
}

export class AgentRegistry {
  private agents: Map<string, AgentRegistryEntry> = new Map();
  private roleIndex: Map<AgentRole, string[]> = new Map();
  private capabilityIndex: Map<AgentCapability, string[]> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    console.log('🏥 Initializing Agent Registry...');
    this.startHealthMonitoring();
  }

  /**
   * Register a new agent in the system
   */
  async registerAgent(agent: IAgent, priority: number = 50): Promise<boolean> {
    try {
      console.log(`📝 Registering agent: ${agent.name} (${agent.role})`);

      // Initialize the agent
      await agent.initialize();

      // Perform health check
      const health = await agent.healthCheck();
      if (!health.healthy) {
        console.warn(`⚠️ Agent ${agent.name} failed initial health check: ${health.details}`);
      }

      // Create registry entry
      const entry: AgentRegistryEntry = {
        agent,
        registeredAt: new Date().toISOString(),
        lastHealthCheck: new Date().toISOString(),
        isHealthy: health.healthy,
        priority,
        loadFactor: 0
      };

      // Add to main registry
      this.agents.set(agent.id, entry);

      // Update role index
      if (!this.roleIndex.has(agent.role)) {
        this.roleIndex.set(agent.role, []);
      }
      this.roleIndex.get(agent.role)!.push(agent.id);

      // Update capability index
      agent.capabilities.forEach(capability => {
        if (!this.capabilityIndex.has(capability)) {
          this.capabilityIndex.set(capability, []);
        }
        this.capabilityIndex.get(capability)!.push(agent.id);
      });

      console.log(`✅ Agent ${agent.name} registered successfully`);
      return true;

    } catch (error) {
      console.error(`❌ Failed to register agent ${agent.name}:`, error);
      return false;
    }
  }

  /**
   * Unregister an agent from the system
   */
  async unregisterAgent(agentId: string): Promise<boolean> {
    try {
      const entry = this.agents.get(agentId);
      if (!entry) {
        console.warn(`⚠️ Agent ${agentId} not found in registry`);
        return false;
      }

      console.log(`🗑️ Unregistering agent: ${entry.agent.name}`);

      // Shutdown the agent
      await entry.agent.shutdown();

      // Remove from main registry
      this.agents.delete(agentId);

      // Remove from role index
      const roleAgents = this.roleIndex.get(entry.agent.role);
      if (roleAgents) {
        const index = roleAgents.indexOf(agentId);
        if (index > -1) {
          roleAgents.splice(index, 1);
        }
      }

      // Remove from capability index
      entry.agent.capabilities.forEach(capability => {
        const capabilityAgents = this.capabilityIndex.get(capability);
        if (capabilityAgents) {
          const index = capabilityAgents.indexOf(agentId);
          if (index > -1) {
            capabilityAgents.splice(index, 1);
          }
        }
      });

      console.log(`✅ Agent ${entry.agent.name} unregistered successfully`);
      return true;

    } catch (error) {
      console.error(`❌ Failed to unregister agent ${agentId}:`, error);
      return false;
    }
  }

  /**
   * Select the best agent for a request
   */
  selectAgent(request: AgentRequest, criteria?: AgentSelectionCriteria): AgentSelectionResult | null {
    console.log(`🎯 Selecting agent for request with urgency: ${request.urgencyLevel || 'medium'}`);

    const {
      requiredCapabilities = [],
      preferredRole,
      urgencyLevel = request.urgencyLevel || 'medium',
      excludeAgents = [],
      requireHealthy = true,
      maxLoadFactor = 0.8
    } = criteria || {};

    // Get candidate agents
    let candidates: AgentRegistryEntry[] = [];

    // If specific role preferred, start with those agents
    if (preferredRole && this.roleIndex.has(preferredRole)) {
      const roleAgentIds = this.roleIndex.get(preferredRole)!;
      candidates = roleAgentIds
        .map(id => this.agents.get(id))
        .filter((entry): entry is AgentRegistryEntry => entry !== undefined);
    } else {
      // Get all agents
      candidates = Array.from(this.agents.values());
    }

    // Filter candidates
    candidates = candidates.filter(entry => {
      // Exclude specific agents
      if (excludeAgents.includes(entry.agent.id)) return false;

      // Health check
      if (requireHealthy && !entry.isHealthy) return false;

      // Load factor check
      if (entry.loadFactor > maxLoadFactor) return false;

      // Active check
      if (!entry.agent.isActive) return false;

      // Capability check
      if (requiredCapabilities.length > 0) {
        const hasRequiredCapabilities = requiredCapabilities.every(cap =>
          entry.agent.capabilities.includes(cap)
        );
        if (!hasRequiredCapabilities) return false;
      }

      // Agent-specific canHandle check
      if (!entry.agent.canHandle(request)) return false;

      return true;
    });

    if (candidates.length === 0) {
      console.warn('⚠️ No suitable agents found for request');
      return null;
    }

    // Score and rank candidates
    const scoredCandidates = candidates.map(entry => {
      let score = entry.agent.getConfidenceScore(request);

      // Priority bonus
      score += (entry.priority / 100) * 0.1;

      // Load factor penalty
      score -= entry.loadFactor * 0.2;

      // Emergency agent boost for critical requests
      if (urgencyLevel === 'critical' && entry.agent.role === 'emergency') {
        score += 0.3;
      }

      // Performance bonus
      const metrics = entry.agent.getPerformanceMetrics();
      if (metrics.totalRequests > 10) {
        score += (metrics.successRate - 0.5) * 0.1;
        score += (metrics.averageConfidence - 0.5) * 0.1;
      }

      return { entry, score };
    });

    // Sort by score (highest first)
    scoredCandidates.sort((a, b) => b.score - a.score);

    const selected = scoredCandidates[0];
    const alternatives = scoredCandidates.slice(1, 4).map(c => c.entry.agent);

    console.log(`✅ Selected agent: ${selected.entry.agent.name} (confidence: ${selected.score.toFixed(2)})`);

    return {
      agent: selected.entry.agent,
      confidence: selected.score,
      reasoning: `Selected based on capabilities, availability, and performance metrics`,
      alternativeAgents: alternatives
    };
  }

  /**
   * Get all agents by role
   */
  getAgentsByRole(role: AgentRole): IAgent[] {
    const agentIds = this.roleIndex.get(role) || [];
    return agentIds
      .map(id => this.agents.get(id)?.agent)
      .filter((agent): agent is IAgent => agent !== undefined);
  }

  /**
   * Get all agents by capability
   */
  getAgentsByCapability(capability: AgentCapability): IAgent[] {
    const agentIds = this.capabilityIndex.get(capability) || [];
    return agentIds
      .map(id => this.agents.get(id)?.agent)
      .filter((agent): agent is IAgent => agent !== undefined);
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): IAgent | null {
    return this.agents.get(agentId)?.agent || null;
  }

  /**
   * Get all registered agents
   */
  getAllAgents(): IAgent[] {
    return Array.from(this.agents.values()).map(entry => entry.agent);
  }

  /**
   * Get registry statistics
   */
  getRegistryStats(): {
    totalAgents: number;
    healthyAgents: number;
    agentsByRole: Record<string, number>;
    agentsByCapability: Record<string, number>;
    averageLoadFactor: number;
  } {
    const entries = Array.from(this.agents.values());
    
    const agentsByRole: Record<string, number> = {};
    const agentsByCapability: Record<string, number> = {};

    entries.forEach(entry => {
      // Count by role
      agentsByRole[entry.agent.role] = (agentsByRole[entry.agent.role] || 0) + 1;

      // Count by capability
      entry.agent.capabilities.forEach(cap => {
        agentsByCapability[cap] = (agentsByCapability[cap] || 0) + 1;
      });
    });

    const averageLoadFactor = entries.length > 0 
      ? entries.reduce((sum, entry) => sum + entry.loadFactor, 0) / entries.length
      : 0;

    return {
      totalAgents: entries.length,
      healthyAgents: entries.filter(e => e.isHealthy).length,
      agentsByRole,
      agentsByCapability,
      averageLoadFactor
    };
  }

  /**
   * Start health monitoring for all agents
   */
  private startHealthMonitoring(): void {
    // Check agent health every 5 minutes
    this.healthCheckInterval = setInterval(async () => {
      console.log('🏥 Running agent health checks...');
      
      for (const [agentId, entry] of this.agents.entries()) {
        try {
          const health = await entry.agent.healthCheck();
          entry.isHealthy = health.healthy;
          entry.lastHealthCheck = new Date().toISOString();

          if (!health.healthy) {
            console.warn(`⚠️ Agent ${entry.agent.name} health check failed: ${health.details}`);
          }
        } catch (error) {
          console.error(`❌ Health check failed for agent ${entry.agent.name}:`, error);
          entry.isHealthy = false;
          entry.lastHealthCheck = new Date().toISOString();
        }
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Shutdown the registry and all agents
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Agent Registry...');
    
    this.stopHealthMonitoring();

    // Shutdown all agents
    const shutdownPromises = Array.from(this.agents.values()).map(entry =>
      entry.agent.shutdown().catch(error =>
        console.error(`Failed to shutdown agent ${entry.agent.name}:`, error)
      )
    );

    await Promise.all(shutdownPromises);
    
    // Clear all data
    this.agents.clear();
    this.roleIndex.clear();
    this.capabilityIndex.clear();

    console.log('✅ Agent Registry shutdown complete');
  }
}

// Export singleton instance
export const agentRegistry = new AgentRegistry();
export default agentRegistry;
