import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ConversationManager = () => {
  const [queueMetrics, setQueueMetrics] = useState({
    totalInQueue: 8,
    averageWaitTime: 120,
    priorityQueue: 3,
    regularQueue: 5,
    maxConcurrent: 20,
    currentConcurrent: 12,
    resourceUtilization: 60,
    avgSessionDuration: 18
  });

  const [queuedSessions, setQueuedSessions] = useState([
    {
      id: 'queue_001',
      patientName: '<PERSON>',
      priority: 'urgent',
      estimatedWait: 30,
      requestedSpecialty: 'Emergency Medicine',
      symptoms: 'Chest pain, shortness of breath',
      queuePosition: 1,
      waitingTime: 45
    },
    {
      id: 'queue_002',
      patientName: '<PERSON>',
      priority: 'high',
      estimatedWait: 120,
      requestedSpecialty: 'Cardiology',
      symptoms: 'Heart palpitations',
      queuePosition: 2,
      waitingTime: 180
    },
    {
      id: 'queue_003',
      patientName: '<PERSON>',
      priority: 'normal',
      estimatedWait: 240,
      requestedSpecialty: 'General Practice',
      symptoms: 'Routine check-up',
      queuePosition: 3,
      waitingTime: 60
    },
    {
      id: 'queue_004',
      patientName: 'David Martinez',
      priority: 'normal',
      estimatedWait: 360,
      requestedSpecialty: 'Dermatology',
      symptoms: 'Skin rash, itching',
      queuePosition: 4,
      waitingTime: 90
    }
  ]);

  const [resourceAllocation, setResourceAllocation] = useState({
    emergency: { allocated: 4, available: 2, utilization: 50 },
    general: { allocated: 8, available: 3, utilization: 62.5 },
    specialty: { allocated: 6, available: 4, utilization: 33.3 },
    mental_health: { allocated: 2, available: 1, utilization: 50 }
  });

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-error-600 bg-error-100 border-error-200';
      case 'high': return 'text-warning-600 bg-warning-100 border-warning-200';
      case 'normal': return 'text-success-600 bg-success-100 border-success-200';
      case 'low': return 'text-gray-600 bg-gray-100 border-gray-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getUtilizationColor = (utilization) => {
    if (utilization >= 80) return 'text-error-600';
    if (utilization >= 60) return 'text-warning-600';
    return 'text-success-600';
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    return minutes > 60 ? `${Math.floor(minutes / 60)}h ${minutes % 60}m` : `${minutes}m`;
  };

  const handlePriorityChange = (sessionId, newPriority) => {
    setQueuedSessions(prev => prev.map(session => 
      session.id === sessionId 
        ? { ...session, priority: newPriority }
        : session
    ));
  };

  const handleStartSession = (sessionId) => {
    console.log(`Starting session: ${sessionId}`);
    setQueuedSessions(prev => prev.filter(session => session.id !== sessionId));
    setQueueMetrics(prev => ({
      ...prev,
      totalInQueue: prev.totalInQueue - 1,
      currentConcurrent: prev.currentConcurrent + 1
    }));
  };

  const handleResourceReallocation = (category, change) => {
    setResourceAllocation(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        allocated: Math.max(0, prev[category].allocated + change)
      }
    }));
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Conversation Manager
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Session queuing, priority handling, and resource allocation across concurrent consultations
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              iconName="BarChart"
            >
              Analytics
            </Button>
            <Button
              size="sm"
              variant="primary"
              iconName="Settings"
            >
              Configure
            </Button>
          </div>
        </div>
      </div>

      {/* Queue Metrics */}
      <div className="p-6 border-b border-border">
        <h4 className="text-sm font-medium text-text-primary mb-3">Queue Overview</h4>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <Icon name="Users" size={16} className="text-primary-500 mr-2" />
              <span className="text-xs text-text-secondary">Total in Queue</span>
            </div>
            <p className="text-xl font-semibold text-text-primary">{queueMetrics.totalInQueue}</p>
            <div className="text-xs text-text-secondary mt-1">
              <span className="text-error-600">{queueMetrics.priorityQueue} priority</span> • 
              <span className="text-success-600 ml-1">{queueMetrics.regularQueue} regular</span>
            </div>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <Icon name="Clock" size={16} className="text-warning-500 mr-2" />
              <span className="text-xs text-text-secondary">Avg Wait Time</span>
            </div>
            <p className="text-xl font-semibold text-text-primary">{formatTime(queueMetrics.averageWaitTime)}</p>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <Icon name="Activity" size={16} className="text-success-500 mr-2" />
              <span className="text-xs text-text-secondary">Concurrent Sessions</span>
            </div>
            <p className="text-xl font-semibold text-text-primary">
              {queueMetrics.currentConcurrent}/{queueMetrics.maxConcurrent}
            </p>
            <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
              <div
                className="h-1 rounded-full bg-success-500 transition-all duration-300"
                style={{ width: `${(queueMetrics.currentConcurrent / queueMetrics.maxConcurrent) * 100}%` }}
              />
            </div>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <Icon name="Gauge" size={16} className="text-purple-500 mr-2" />
              <span className="text-xs text-text-secondary">Resource Utilization</span>
            </div>
            <p className={`text-xl font-semibold ${getUtilizationColor(queueMetrics.resourceUtilization)}`}>
              {queueMetrics.resourceUtilization}%
            </p>
          </div>
        </div>
      </div>

      {/* Resource Allocation */}
      <div className="p-6 border-b border-border">
        <h4 className="text-sm font-medium text-text-primary mb-3">Resource Allocation</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(resourceAllocation).map(([category, resource]) => (
            <div key={category} className="bg-secondary-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary capitalize">
                  {category.replace('_', ' ')}
                </span>
                <div className="flex gap-1">
                  <button
                    onClick={() => handleResourceReallocation(category, -1)}
                    className="w-6 h-6 rounded text-xs bg-gray-200 hover:bg-gray-300 flex items-center justify-center"
                  >
                    <Icon name="Minus" size={12} />
                  </button>
                  <button
                    onClick={() => handleResourceReallocation(category, 1)}
                    className="w-6 h-6 rounded text-xs bg-primary-100 hover:bg-primary-200 flex items-center justify-center"
                  >
                    <Icon name="Plus" size={12} />
                  </button>
                </div>
              </div>
              
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-text-secondary">Allocated</span>
                  <span className="font-medium">{resource.allocated}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-text-secondary">Available</span>
                  <span className="font-medium text-success-600">{resource.available}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-text-secondary">Utilization</span>
                  <span className={`font-medium ${getUtilizationColor(resource.utilization)}`}>
                    {resource.utilization}%
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Queue Management */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-medium text-text-primary">Session Queue</h4>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" iconName="ArrowUpDown">
              Sort by Priority
            </Button>
            <Button size="sm" variant="outline" iconName="Filter">
              Filter
            </Button>
          </div>
        </div>

        <div className="space-y-3">
          {queuedSessions.map((session, index) => (
            <div
              key={session.id}
              className={`border rounded-lg p-4 ${getPriorityColor(session.priority)}`}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-semibold text-primary-600">#{session.queuePosition}</span>
                  </div>
                  <div>
                    <h5 className="font-medium text-text-primary">{session.patientName}</h5>
                    <p className="text-sm text-text-secondary">{session.requestedSpecialty}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <select
                    value={session.priority}
                    onChange={(e) => handlePriorityChange(session.id, e.target.value)}
                    className="text-xs border border-current rounded px-2 py-1 bg-transparent"
                  >
                    <option value="urgent">Urgent</option>
                    <option value="high">High</option>
                    <option value="normal">Normal</option>
                    <option value="low">Low</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                <div>
                  <p className="text-xs text-text-secondary mb-1">Symptoms</p>
                  <p className="text-sm text-text-primary">{session.symptoms}</p>
                </div>
                <div>
                  <p className="text-xs text-text-secondary mb-1">Waiting Time</p>
                  <p className="text-sm text-text-primary">{formatTime(session.waitingTime)}</p>
                </div>
                <div>
                  <p className="text-xs text-text-secondary mb-1">Estimated Wait</p>
                  <p className="text-sm text-text-primary">{formatTime(session.estimatedWait)}</p>
                </div>
              </div>

              <div className="flex gap-2 pt-2 border-t border-current border-opacity-20">
                <Button
                  size="sm"
                  variant="outline"
                  iconName="Play"
                  onClick={() => handleStartSession(session.id)}
                >
                  Start Now
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  iconName="MessageCircle"
                >
                  Message
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  iconName="Calendar"
                >
                  Reschedule
                </Button>
              </div>
            </div>
          ))}
        </div>

        {queuedSessions.length === 0 && (
          <div className="text-center py-8">
            <Icon name="CheckCircle" size={48} className="text-success-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">Queue Empty</h3>
            <p className="text-text-secondary">No sessions currently waiting</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationManager;