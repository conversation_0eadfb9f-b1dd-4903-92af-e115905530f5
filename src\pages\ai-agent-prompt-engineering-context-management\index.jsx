import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';

import SpecialistPromptEditor from './components/SpecialistPromptEditor';
import ContextAwareResponseSystem from './components/ContextAwareResponseSystem';
import CulturalCompetencyControls from './components/CulturalCompetencyControls';
import RedFlagDetectionPrompts from './components/RedFlagDetectionPrompts';
import RegionalAdaptationSettings from './components/RegionalAdaptationSettings';
import PromptTestingEnvironment from './components/PromptTestingEnvironment';

const AIAgentPromptEngineeringContextManagement = () => {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('specialists');
  const [selectedSpecialist, setSelectedSpecialist] = useState('general-practitioner');
  const [isLoading, setIsLoading] = useState(true);
  const [promptStats, setPromptStats] = useState({});

  // Mock data for AI agent prompt management
  const mockPromptStats = {
    totalPrompts: 45,
    activeSpecialists: 5,
    culturalAdaptations: 12,
    redFlagPatterns: 23,
    regionalVariations: 8,
    lastOptimized: '2024-01-15T14:30:00Z'
  };

  const specialists = [
    {
      id: 'general-practitioner',
      name: 'General Practitioner',
      icon: 'Stethoscope',
      description: 'Primary care and general medical consultations',
      promptCount: 8,
      culturalVariations: 4,
      lastUpdated: '2024-01-15'
    },
    {
      id: 'dietician',
      name: 'Dietician',
      icon: 'Apple',
      description: 'Nutrition counseling and dietary planning',
      promptCount: 6,
      culturalVariations: 5,
      lastUpdated: '2024-01-14'
    },
    {
      id: 'clinical-psychologist',
      name: 'Clinical Psychologist',
      icon: 'Brain',
      description: 'Mental health and psychological support',
      promptCount: 12,
      culturalVariations: 8,
      lastUpdated: '2024-01-13'
    },
    {
      id: 'paediatrician',
      name: 'Paediatrician',
      icon: 'Baby',
      description: 'Child and adolescent healthcare',
      promptCount: 9,
      culturalVariations: 3,
      lastUpdated: '2024-01-12'
    },
    {
      id: 'dermatologist',
      name: 'Dermatologist',
      icon: 'Zap',
      description: 'Skin, hair, and nail conditions',
      promptCount: 10,
      culturalVariations: 2,
      lastUpdated: '2024-01-11'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate loading prompt data
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPromptStats(mockPromptStats);
      setIsLoading(false);
    };

    loadData();
  }, []);

  const sidebarSections = [
    { id: 'specialists', label: 'Specialist Prompts', icon: 'Users', count: specialists.length },
    { id: 'context-aware', label: 'Context-Aware System', icon: 'Brain', count: promptStats.totalPrompts },
    { id: 'cultural-competency', label: 'Cultural Competency', icon: 'Globe', count: promptStats.culturalAdaptations },
    { id: 'red-flag-prompts', label: 'Red Flag Detection', icon: 'AlertTriangle', count: promptStats.redFlagPatterns },
    { id: 'regional-adaptation', label: 'Regional Adaptation', icon: 'MapPin', count: promptStats.regionalVariations },
    { id: 'testing', label: 'Prompt Testing', icon: 'TestTube', count: null }
  ];

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
              <Icon name="Bot" size={24} color="var(--primary-500)" />
            </div>
            <p className="text-text-secondary">Loading AI agent prompt configurations...</p>
          </div>
        </div>
      );
    }

    switch (activeSection) {
      case 'specialists':
        return (
          <SpecialistPromptEditor 
            specialists={specialists}
            selectedSpecialist={selectedSpecialist}
            onSpecialistChange={setSelectedSpecialist}
          />
        );
      case 'context-aware':
        return <ContextAwareResponseSystem />;
      case 'cultural-competency':
        return <CulturalCompetencyControls />;
      case 'red-flag-prompts':
        return <RedFlagDetectionPrompts />;
      case 'regional-adaptation':
        return <RegionalAdaptationSettings />;
      case 'testing':
        return <PromptTestingEnvironment specialists={specialists} />;
      default:
        return <SpecialistPromptEditor specialists={specialists} />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="flex">
        {/* Sidebar */}
        <div className="hidden lg:flex lg:flex-col lg:w-80 lg:fixed lg:inset-y-0 lg:pt-16 bg-surface border-r border-border">
          <div className="flex-1 flex flex-col min-h-0">
            <div className="p-4 border-b border-border">
              <h2 className="text-lg font-semibold text-text-primary font-heading">
                AI Agent Prompt Engineering
              </h2>
              <p className="text-sm text-text-secondary mt-1">
                Culturally-aware prompt management system
              </p>
            </div>
            
            <nav className="flex-1 px-2 py-4 space-y-1">
              {sidebarSections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeSection === section.id
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-500' :'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                  }`}
                >
                  <Icon name={section.icon} size={20} className="mr-3" />
                  <span className="flex-1 text-left">{section.label}</span>
                  {section.count && (
                    <span className="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs font-medium">
                      {section.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>

            <div className="p-4 border-t border-border">
              <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                <div className="flex items-center">
                  <Icon name="Zap" size={20} className="text-warning-600 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-warning-800">Auto-Optimization</p>
                    <p className="text-xs text-warning-600">
                      Last run: {new Date(promptStats.lastOptimized).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 lg:ml-80">
          <div className="px-4 sm:px-6 lg:px-8 pt-20 pb-12">
            {/* Mobile Header */}
            <div className="lg:hidden mb-6">
              <h1 className="text-2xl font-bold text-text-primary font-heading">
                AI Agent Prompt Engineering
              </h1>
              <p className="text-text-secondary mt-2">
                Configure culturally-aware AI agent responses
              </p>
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:flex lg:items-center lg:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-text-primary font-heading">
                  {activeSection === 'specialists' ? 'Specialist Prompt Templates' :
                   activeSection === 'context-aware' ? 'Context-Aware Response System' :
                   activeSection === 'cultural-competency' ? 'Cultural Competency Controls' :
                   activeSection === 'red-flag-prompts' ? 'Red Flag Detection Prompts' :
                   activeSection === 'regional-adaptation' ? 'Regional Adaptation Settings' :
                   activeSection === 'testing'? 'Prompt Testing Environment' : 'AI Agent Prompt Engineering'}
                </h1>
                <p className="text-text-secondary mt-2">
                  {activeSection === 'specialists' ? 'Domain-specific prompts for each medical specialty' :
                   activeSection === 'context-aware' ? 'Patient context integration and response optimization' :
                   activeSection === 'cultural-competency' ? 'Cultural sensitivity and religious considerations' :
                   activeSection === 'red-flag-prompts' ? 'Emergency detection and escalation protocols' :
                   activeSection === 'regional-adaptation' ? 'Africa-specific healthcare context adaptation' :
                   activeSection === 'testing'? 'Simulate and test prompt responses with patient scenarios' : 'Comprehensive AI agent prompt management system'}
                </p>
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin-dashboard')}
                  iconName="ArrowLeft"
                >
                  Back to Admin
                </Button>
                <Button
                  variant="primary"
                  iconName="Zap"
                >
                  Optimize All Prompts
                </Button>
              </div>
            </div>

            {/* Prompt Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="Bot" size={24} className="text-primary-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {promptStats.totalPrompts}
                    </p>
                    <p className="text-sm text-text-secondary">Total Prompts</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="Users" size={24} className="text-success-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {promptStats.activeSpecialists}
                    </p>
                    <p className="text-sm text-text-secondary">Active Specialists</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="Globe" size={24} className="text-warning-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {promptStats.culturalAdaptations}
                    </p>
                    <p className="text-sm text-text-secondary">Cultural Adaptations</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="AlertTriangle" size={24} className="text-error-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {promptStats.redFlagPatterns}
                    </p>
                    <p className="text-sm text-text-secondary">Red Flag Patterns</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content Area */}
            {renderMainContent()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AIAgentPromptEngineeringContextManagement;