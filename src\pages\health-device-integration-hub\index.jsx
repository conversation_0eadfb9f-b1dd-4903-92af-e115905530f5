import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import ConnectedDevicesGrid from './components/ConnectedDevicesGrid';
import APIIntegrationSection from './components/APIIntegrationSection';
import DataVisualizationDashboard from './components/DataVisualizationDashboard';
import DeviceDiscoveryPanel from './components/DeviceDiscoveryPanel';

const HealthDeviceIntegrationHub = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('devices');

  // Mock data - in real app, this would come from state management
  const [connectedDevices, setConnectedDevices] = useState([
    {
      id: 'device-1',
      name: 'Apple Watch Series 8',
      model: 'Series 8',
      manufacturer: 'Apple',
      type: 'fitness-tracker',
      status: 'connected',
      battery: 85,
      lastSync: '2024-01-16T08:30:00Z',
      dataPoints: 1250,
      metrics: [
        { name: 'Heart Rate', value: 72, unit: 'bpm' },
        { name: 'Steps Today', value: 8450, unit: 'steps' },
        { name: 'Active Minutes', value: 45, unit: 'min' }
      ]
    },
    {
      id: 'device-2',
      name: 'Omron Blood Pressure Monitor',
      model: 'HEM-7120',
      manufacturer: 'Omron',
      type: 'blood-pressure',
      status: 'connected',
      battery: 92,
      lastSync: '2024-01-16T07:15:00Z',
      dataPoints: 48,
      metrics: [
        { name: 'Systolic', value: 120, unit: 'mmHg' },
        { name: 'Diastolic', value: 80, unit: 'mmHg' },
        { name: 'Pulse', value: 74, unit: 'bpm' }
      ]
    },
    {
      id: 'device-3',
      name: 'Fitbit Charge 5',
      model: 'Charge 5',
      manufacturer: 'Fitbit',
      type: 'fitness-tracker',
      status: 'syncing',
      battery: 67,
      lastSync: '2024-01-15T22:45:00Z',
      dataPoints: 892,
      metrics: [
        { name: 'Steps', value: 9200, unit: 'steps' },
        { name: 'Sleep Score', value: 82, unit: '' },
        { name: 'Heart Rate Zones', value: 35, unit: 'min' }
      ]
    }
  ]);

  const [apiIntegrations, setApiIntegrations] = useState([
    {
      id: 'apple-health',
      connected: true,
      lastSync: '2024-01-16T08:00:00Z',
      dataPoints: 2500,
      status: 'active'
    },
    {
      id: 'google-fit',
      connected: false,
      lastSync: null,
      dataPoints: 0,
      status: 'inactive'
    },
    {
      id: 'fitbit',
      connected: true,
      lastSync: '2024-01-15T22:45:00Z',
      dataPoints: 892,
      status: 'active'
    }
  ]);

  const [healthData, setHealthData] = useState({
    heartRate: [],
    bloodPressure: [],
    steps: [],
    weight: []
  });

  const tabs = [
    { id: 'devices', name: 'Connected Devices', icon: 'Smartphone' },
    { id: 'discovery', name: 'Add Devices', icon: 'Search' },
    { id: 'integrations', name: 'API Integrations', icon: 'Link' },
    { id: 'analytics', name: 'Data Analytics', icon: 'BarChart3' }
  ];

  const handleConfigureDevice = (deviceId) => {
    console.log('Configure device:', deviceId);
    // In real app, would open device configuration modal
  };

  const handleDisconnectDevice = (deviceId) => {
    setConnectedDevices(prev => prev.filter(device => device.id !== deviceId));
  };

  const handleSyncDevice = (deviceId) => {
    setConnectedDevices(prev => 
      prev.map(device => 
        device.id === deviceId 
          ? { ...device, status: 'syncing' }
          : device
      )
    );

    // Simulate sync completion
    setTimeout(() => {
      setConnectedDevices(prev => 
        prev.map(device => 
          device.id === deviceId 
            ? { ...device, status: 'connected', lastSync: new Date().toISOString() }
            : device
        )
      );
    }, 3000);
  };

  const handleConnectService = (serviceId) => {
    setApiIntegrations(prev => 
      prev.map(integration => 
        integration.id === serviceId 
          ? { ...integration, connected: true, status: 'active', lastSync: new Date().toISOString() }
          : integration
      )
    );
  };

  const handleDisconnectService = (serviceId) => {
    setApiIntegrations(prev => 
      prev.map(integration => 
        integration.id === serviceId 
          ? { ...integration, connected: false, status: 'inactive', lastSync: null, dataPoints: 0 }
          : integration
      )
    );
  };

  const handleConfigureService = (serviceId) => {
    console.log('Configure service:', serviceId);
    // In real app, would open service configuration modal
  };

  const handleDeviceFound = (device) => {
    console.log('Device found:', device);
  };

  const handleConnectDevice = (device) => {
    const newDevice = {
      id: device.id,
      name: device.name,
      model: device.name,
      manufacturer: device.manufacturer,
      type: device.type,
      status: 'connected',
      battery: Math.floor(Math.random() * 100),
      lastSync: new Date().toISOString(),
      dataPoints: Math.floor(Math.random() * 1000),
      metrics: []
    };

    setConnectedDevices(prev => [...prev, newDevice]);
    setActiveTab('devices');
  };

  const handleExportData = () => {
    console.log('Export health data');
    // In real app, would trigger data export
  };

  const getTabContent = () => {
    switch (activeTab) {
      case 'devices':
        return (
          <ConnectedDevicesGrid
            devices={connectedDevices}
            onConfigureDevice={handleConfigureDevice}
            onDisconnectDevice={handleDisconnectDevice}
            onSyncDevice={handleSyncDevice}
          />
        );
      case 'discovery':
        return (
          <DeviceDiscoveryPanel
            onDeviceFound={handleDeviceFound}
            onConnectDevice={handleConnectDevice}
          />
        );
      case 'integrations':
        return (
          <APIIntegrationSection
            integrations={apiIntegrations}
            onConnectService={handleConnectService}
            onDisconnectService={handleDisconnectService}
            onConfigureService={handleConfigureService}
          />
        );
      case 'analytics':
        return (
          <DataVisualizationDashboard
            healthData={healthData}
            onExportData={handleExportData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-surface shadow-minimal border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-text-secondary hover:text-text-primary hover:bg-secondary-50 rounded-lg transition-fast"
              >
                <Icon name="ArrowLeft" size={20} />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-text-primary font-heading">
                  Health Device Integration Hub
                </h1>
                <p className="text-sm text-text-secondary">
                  Centralized management for health devices and data
                </p>
              </div>
            </div>

            {/* Stats */}
            <div className="hidden md:flex items-center space-x-6">
              <div className="text-center">
                <p className="text-lg font-semibold text-text-primary">{connectedDevices.length}</p>
                <p className="text-xs text-text-secondary">Connected Devices</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-semibold text-text-primary">
                  {apiIntegrations.filter(i => i.connected).length}
                </p>
                <p className="text-xs text-text-secondary">API Integrations</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-semibold text-text-primary">
                  {connectedDevices.reduce((sum, device) => sum + (device.dataPoints || 0), 0)}
                </p>
                <p className="text-xs text-text-secondary">Data Points</p>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex space-x-8 border-b border-border">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-1 py-4 border-b-2 text-sm font-medium transition-fast ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-secondary-300'
                }`}
              >
                <Icon name={tab.icon} size={16} />
                <span>{tab.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {getTabContent()}
      </div>

      {/* Privacy Notice */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-surface rounded-xl p-6 shadow-elevated">
          <div className="flex items-start space-x-3">
            <Icon name="Shield" size={20} color="var(--color-success-500)" />
            <div>
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-2">
                Privacy & Security
              </h3>
              <p className="text-sm text-text-secondary mb-4">
                Your health data is encrypted and stored securely. We comply with HIPAA and GDPR regulations. 
                Data is only shared with your explicit consent and for medical purposes.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Icon name="Lock" size={16} color="var(--color-success-500)" />
                  <span className="text-sm text-text-secondary">End-to-end encryption</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icon name="Shield" size={16} color="var(--color-success-500)" />
                  <span className="text-sm text-text-secondary">HIPAA compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icon name="Eye" size={16} color="var(--color-success-500)" />
                  <span className="text-sm text-text-secondary">You control data sharing</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HealthDeviceIntegrationHub;