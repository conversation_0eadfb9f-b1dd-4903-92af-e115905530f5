/**
 * COMPREHENSIVE SECURITY TESTS FOR RATE LIMITING AND DDOS PROTECTION
 * 
 * This test suite provides comprehensive security testing for rate limiting
 * and DDoS protection with focus on:
 * - Medical API endpoint protection
 * - Emergency bypass mechanisms
 * - User-based rate limiting
 * - IP-based rate limiting
 * - Adaptive rate limiting
 * - Attack pattern detection
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency access must bypass rate limits
 * - Medical data access must be protected
 * - Legitimate users must not be blocked
 * - Attack patterns must be detected and mitigated
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import rateLimitingService from '../../services/rateLimitingService';
import ddosProtectionService from '../../services/ddosProtectionService';

// Mock dependencies
vi.mock('../../utils/supabaseClient', () => ({
  supabase: global.testHelpers.createMockSupabaseClient()
}));

vi.mock('../../utils/auditLogger', () => ({
  default: global.testHelpers.createMockAuditLogger()
}));

describe('Rate Limiting and DDoS Protection Security Tests', () => {
  let mockAuditLogger;
  let mockSupabase;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAuditLogger = global.testHelpers.createMockAuditLogger();
    mockSupabase = global.testHelpers.createMockSupabaseClient();
    
    // Reset rate limiting state
    rateLimitingService.reset();
    ddosProtectionService.reset();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Medical API Rate Limiting', () => {
    it('should enforce rate limits for medical data endpoints', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const endpoint = '/api/medical/conditions';
      const limit = 100; // 100 requests per hour
      const window = 3600000; // 1 hour in milliseconds

      // Act - Make requests up to limit
      const requests = Array(limit).fill().map(() => 
        rateLimitingService.checkLimit(userId, endpoint, { limit, window })
      );

      const results = await Promise.all(requests);

      // Assert - All requests within limit should be allowed
      expect(results.every(result => result.allowed)).toBe(true);
      expect(results[results.length - 1].remaining).toBe(0);

      // Act - Exceed limit
      const exceededResult = await rateLimitingService.checkLimit(userId, endpoint, { limit, window });

      // Assert - Request over limit should be denied
      expect(exceededResult.allowed).toBe(false);
      expect(exceededResult.retryAfter).toBeGreaterThan(0);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'rate_limit_exceeded',
        'medium',
        expect.objectContaining({
          user_id: userId,
          endpoint,
          limit_type: 'user_endpoint'
        })
      );
    });

    it('should apply different limits for different user roles', async () => {
      // Arrange
      const patient = global.mockUser;
      const provider = global.mockProvider;
      const admin = global.mockAdmin;
      const endpoint = '/api/medical/conditions';

      // Act
      const patientLimit = await rateLimitingService.getUserLimit(patient.id, endpoint);
      const providerLimit = await rateLimitingService.getUserLimit(provider.id, endpoint);
      const adminLimit = await rateLimitingService.getUserLimit(admin.id, endpoint);

      // Assert
      expect(patientLimit.limit).toBe(100); // Standard patient limit
      expect(providerLimit.limit).toBe(500); // Higher limit for providers
      expect(adminLimit.limit).toBe(1000); // Highest limit for admins
    });

    it('should implement sliding window rate limiting', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const endpoint = '/api/medical/medications';
      const limit = 10;
      const window = 60000; // 1 minute

      // Act - Make requests at different times
      const firstBatch = Array(5).fill().map(() => 
        rateLimitingService.checkLimit(userId, endpoint, { limit, window })
      );
      await Promise.all(firstBatch);

      // Wait half the window
      await new Promise(resolve => setTimeout(resolve, 30000));

      const secondBatch = Array(5).fill().map(() => 
        rateLimitingService.checkLimit(userId, endpoint, { limit, window })
      );
      const secondResults = await Promise.all(secondBatch);

      // Wait for window to slide
      await new Promise(resolve => setTimeout(resolve, 35000));

      const thirdBatch = Array(5).fill().map(() => 
        rateLimitingService.checkLimit(userId, endpoint, { limit, window })
      );
      const thirdResults = await Promise.all(thirdBatch);

      // Assert
      expect(secondResults.every(result => result.allowed)).toBe(true);
      expect(thirdResults.every(result => result.allowed)).toBe(true);
    });

    it('should track rate limiting metrics for monitoring', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const endpoint = '/api/medical/symptoms';

      // Act
      await rateLimitingService.checkLimit(userId, endpoint);
      const metrics = await rateLimitingService.getMetrics(userId, endpoint);

      // Assert
      expect(metrics).toEqual(
        expect.objectContaining({
          totalRequests: expect.any(Number),
          allowedRequests: expect.any(Number),
          deniedRequests: expect.any(Number),
          currentUsage: expect.any(Number),
          averageRequestRate: expect.any(Number)
        })
      );
    });
  });

  describe('Emergency Bypass Mechanisms', () => {
    it('should bypass rate limits for emergency access', async () => {
      // Arrange
      const providerId = global.mockProvider.id;
      const endpoint = '/api/medical/emergency-access';
      const limit = 5; // Low limit for testing

      // Act - Exceed normal limit
      const normalRequests = Array(limit + 1).fill().map(() => 
        rateLimitingService.checkLimit(providerId, endpoint, { limit, window: 60000 })
      );
      await Promise.all(normalRequests);

      // Act - Request with emergency bypass
      const emergencyResult = await rateLimitingService.checkLimit(
        providerId, 
        endpoint, 
        { 
          limit, 
          window: 60000,
          emergencyBypass: true,
          justification: 'Patient in critical condition'
        }
      );

      // Assert
      expect(emergencyResult.allowed).toBe(true);
      expect(emergencyResult.bypassUsed).toBe(true);
      expect(mockAuditLogger.logEmergencyAccess).toHaveBeenCalledWith(
        providerId,
        expect.any(String),
        'Patient in critical condition',
        expect.objectContaining({
          rate_limit_bypass: true,
          endpoint
        })
      );
    });

    it('should limit emergency bypass usage', async () => {
      // Arrange
      const providerId = global.mockProvider.id;
      const endpoint = '/api/medical/emergency-access';
      const maxBypassesPerDay = 3;

      // Act - Use emergency bypass multiple times
      const bypassRequests = Array(maxBypassesPerDay + 1).fill().map((_, i) => 
        rateLimitingService.checkLimit(
          providerId, 
          endpoint, 
          { 
            emergencyBypass: true,
            justification: `Emergency ${i + 1}`
          }
        )
      );

      const results = await Promise.all(bypassRequests);

      // Assert
      expect(results.slice(0, maxBypassesPerDay).every(r => r.allowed)).toBe(true);
      expect(results[maxBypassesPerDay].allowed).toBe(false);
      expect(results[maxBypassesPerDay].error).toContain('emergency bypass limit exceeded');
    });

    it('should require justification for emergency bypass', async () => {
      // Arrange
      const providerId = global.mockProvider.id;
      const endpoint = '/api/medical/emergency-access';

      // Act - Request bypass without justification
      const result = await rateLimitingService.checkLimit(
        providerId, 
        endpoint, 
        { emergencyBypass: true }
      );

      // Assert
      expect(result.allowed).toBe(false);
      expect(result.error).toContain('justification required');
    });

    it('should auto-approve emergency bypass for critical situations', async () => {
      // Arrange
      const providerId = global.mockProvider.id;
      const endpoint = '/api/medical/emergency-access';

      // Act
      const result = await rateLimitingService.checkLimit(
        providerId, 
        endpoint, 
        { 
          emergencyBypass: true,
          urgency: 'critical',
          justification: 'Patient cardiac arrest'
        }
      );

      // Assert
      expect(result.allowed).toBe(true);
      expect(result.autoApproved).toBe(true);
      expect(result.requiresPostReview).toBe(true);
    });
  });

  describe('IP-Based Rate Limiting', () => {
    it('should enforce IP-based rate limits', async () => {
      // Arrange
      const ipAddress = '*************';
      const endpoint = '/api/medical/conditions';
      const limit = 1000; // 1000 requests per hour per IP
      const window = 3600000;

      // Act - Make requests from same IP
      const requests = Array(limit + 1).fill().map(() => 
        rateLimitingService.checkIPLimit(ipAddress, endpoint, { limit, window })
      );

      const results = await Promise.all(requests);

      // Assert
      expect(results.slice(0, limit).every(r => r.allowed)).toBe(true);
      expect(results[limit].allowed).toBe(false);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'ip_rate_limit_exceeded',
        'medium',
        expect.objectContaining({
          ip_address: ipAddress,
          endpoint
        })
      );
    });

    it('should detect and block suspicious IP patterns', async () => {
      // Arrange
      const suspiciousIP = '********';
      const endpoints = [
        '/api/medical/conditions',
        '/api/medical/medications',
        '/api/medical/symptoms',
        '/api/medical/providers'
      ];

      // Act - Rapid requests to multiple endpoints
      const rapidRequests = endpoints.flatMap(endpoint => 
        Array(100).fill().map(() => 
          rateLimitingService.checkIPLimit(suspiciousIP, endpoint, { 
            detectSuspiciousPatterns: true 
          })
        )
      );

      await Promise.all(rapidRequests);

      // Assert
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'suspicious_ip_pattern',
        'high',
        expect.objectContaining({
          ip_address: suspiciousIP,
          pattern_type: 'rapid_multi_endpoint_access',
          endpoints_accessed: endpoints.length
        })
      );
    });

    it('should implement IP whitelisting for trusted sources', async () => {
      // Arrange
      const trustedIP = '***********'; // Hospital network IP
      const endpoint = '/api/medical/emergency-access';
      
      await rateLimitingService.addTrustedIP(trustedIP, {
        reason: 'Hospital emergency department',
        approvedBy: global.mockAdmin.id
      });

      // Act - Make requests exceeding normal limits
      const requests = Array(2000).fill().map(() => 
        rateLimitingService.checkIPLimit(trustedIP, endpoint)
      );

      const results = await Promise.all(requests);

      // Assert
      expect(results.every(r => r.allowed)).toBe(true);
      expect(results[0].trustedSource).toBe(true);
    });
  });

  describe('DDoS Protection', () => {
    it('should detect and mitigate DDoS attacks', async () => {
      // Arrange
      const attackIPs = Array(100).fill().map((_, i) => `192.168.1.${i + 1}`);
      const endpoint = '/api/medical/conditions';

      // Act - Simulate DDoS attack
      const attackRequests = attackIPs.flatMap(ip => 
        Array(50).fill().map(() => 
          ddosProtectionService.checkRequest(ip, endpoint, {
            userAgent: 'AttackBot/1.0',
            timestamp: Date.now()
          })
        )
      );

      const results = await Promise.all(attackRequests);

      // Assert
      const blockedRequests = results.filter(r => !r.allowed);
      expect(blockedRequests.length).toBeGreaterThan(0);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'ddos_attack_detected',
        'critical',
        expect.objectContaining({
          attack_type: 'distributed',
          source_ips: expect.any(Array),
          requests_blocked: expect.any(Number)
        })
      );
    });

    it('should implement adaptive rate limiting during attacks', async () => {
      // Arrange
      const normalIP = '************';
      const endpoint = '/api/medical/medications';

      // Simulate attack detection
      await ddosProtectionService.setAttackMode(true, {
        severity: 'high',
        adaptiveLimits: true
      });

      // Act
      const result = await rateLimitingService.checkLimit(
        global.mockUser.id, 
        endpoint, 
        { adaptToAttackMode: true }
      );

      // Assert
      expect(result.limit).toBeLessThan(100); // Reduced limit during attack
      expect(result.attackModeActive).toBe(true);
    });

    it('should maintain emergency access during DDoS attacks', async () => {
      // Arrange
      const emergencyIP = '************';
      const endpoint = '/api/medical/emergency-access';

      // Simulate active DDoS attack
      await ddosProtectionService.setAttackMode(true, { severity: 'critical' });

      // Act
      const emergencyResult = await ddosProtectionService.checkRequest(
        emergencyIP, 
        endpoint, 
        {
          emergencyAccess: true,
          providerId: global.mockProvider.id,
          justification: 'Critical patient emergency'
        }
      );

      // Assert
      expect(emergencyResult.allowed).toBe(true);
      expect(emergencyResult.emergencyBypass).toBe(true);
    });

    it('should implement CAPTCHA challenges for suspicious requests', async () => {
      // Arrange
      const suspiciousIP = '**********';
      const endpoint = '/api/medical/conditions';

      // Act - Make suspicious requests
      const suspiciousRequests = Array(20).fill().map(() => 
        ddosProtectionService.checkRequest(suspiciousIP, endpoint, {
          userAgent: 'SuspiciousBot/1.0',
          noJavaScript: true
        })
      );

      const results = await Promise.all(suspiciousRequests);

      // Assert
      const challengeRequired = results.some(r => r.challengeRequired);
      expect(challengeRequired).toBe(true);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'captcha_challenge_issued',
        'medium',
        expect.objectContaining({
          ip_address: suspiciousIP,
          reason: 'suspicious_request_pattern'
        })
      );
    });
  });

  describe('Adaptive Rate Limiting', () => {
    it('should adjust limits based on system load', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const endpoint = '/api/medical/conditions';

      // Simulate high system load
      await rateLimitingService.setSystemLoad(0.9); // 90% load

      // Act
      const result = await rateLimitingService.checkLimit(userId, endpoint, {
        adaptToSystemLoad: true
      });

      // Assert
      expect(result.limit).toBeLessThan(100); // Reduced limit due to high load
      expect(result.systemLoadFactor).toBeCloseTo(0.9, 1);
    });

    it('should prioritize medical emergency requests during high load', async () => {
      // Arrange
      const providerId = global.mockProvider.id;
      const endpoint = '/api/medical/emergency-access';

      // Simulate very high system load
      await rateLimitingService.setSystemLoad(0.95);

      // Act
      const emergencyResult = await rateLimitingService.checkLimit(
        providerId, 
        endpoint, 
        {
          priority: 'emergency',
          adaptToSystemLoad: true
        }
      );

      const normalResult = await rateLimitingService.checkLimit(
        global.mockUser.id, 
        '/api/medical/conditions', 
        { adaptToSystemLoad: true }
      );

      // Assert
      expect(emergencyResult.allowed).toBe(true);
      expect(emergencyResult.priorityBypass).toBe(true);
      expect(normalResult.limit).toBeLessThan(emergencyResult.limit);
    });

    it('should implement machine learning-based pattern detection', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const endpoint = '/api/medical/conditions';

      // Simulate normal usage pattern
      const normalPattern = Array(50).fill().map((_, i) => ({
        timestamp: Date.now() - (i * 60000), // 1 request per minute
        endpoint,
        userId
      }));

      await rateLimitingService.learnUserPattern(userId, normalPattern);

      // Act - Sudden spike in requests
      const spikeRequests = Array(100).fill().map(() => 
        rateLimitingService.checkLimit(userId, endpoint, {
          useMLDetection: true
        })
      );

      const results = await Promise.all(spikeRequests);

      // Assert
      const anomalyDetected = results.some(r => r.anomalyDetected);
      expect(anomalyDetected).toBe(true);
      expect(mockAuditLogger.logSecurityEvent).toHaveBeenCalledWith(
        'usage_pattern_anomaly',
        'medium',
        expect.objectContaining({
          user_id: userId,
          anomaly_type: 'request_spike',
          confidence_score: expect.any(Number)
        })
      );
    });
  });

  describe('Performance Under Attack', () => {
    it('should maintain performance during rate limiting operations', async () => {
      // Arrange
      const concurrentRequests = 1000;
      const startTime = performance.now();

      // Act
      const requests = Array(concurrentRequests).fill().map((_, i) => 
        rateLimitingService.checkLimit(
          `user-${i % 100}`, 
          '/api/medical/conditions'
        )
      );

      await Promise.all(requests);
      const endTime = performance.now();

      // Assert
      const totalTime = endTime - startTime;
      const avgTimePerRequest = totalTime / concurrentRequests;
      expect(avgTimePerRequest).toBeLessThan(10); // Less than 10ms per request
    });

    it('should handle memory efficiently during high-volume attacks', async () => {
      // Arrange
      const initialMemory = process.memoryUsage().heapUsed;
      const attackRequests = 10000;

      // Act - Simulate large-scale attack
      const requests = Array(attackRequests).fill().map((_, i) => 
        ddosProtectionService.checkRequest(
          `192.168.${Math.floor(i / 256)}.${i % 256}`, 
          '/api/medical/conditions'
        )
      );

      await Promise.all(requests);
      const finalMemory = process.memoryUsage().heapUsed;

      // Assert
      const memoryIncrease = finalMemory - initialMemory;
      const memoryPerRequest = memoryIncrease / attackRequests;
      expect(memoryPerRequest).toBeLessThan(1024); // Less than 1KB per request
    });
  });
});
