import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ConversationHistorySidebar = ({ 
  conversationHistory = [],
  sessionNotes = [],
  keyRecommendations = [],
  isVisible = true,
  onToggleVisibility = () => {},
  onExportHistory = () => {},
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('conversation');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredHistory, setFilteredHistory] = useState([]);
  const historyRef = useRef(null);

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = conversationHistory.filter(item =>
        item.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.speaker?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredHistory(filtered);
    } else {
      setFilteredHistory(conversationHistory);
    }
  }, [searchQuery, conversationHistory]);

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getSpeakerIcon = (speaker) => {
    if (speaker === 'user') return 'User';
    if (speaker === 'system') return 'Bot';
    return 'Stethoscope';
  };

  const getSpeakerColor = (speaker) => {
    if (speaker === 'user') return 'primary';
    if (speaker === 'system') return 'secondary';
    return 'success';
  };

  const tabs = [
    { id: 'conversation', label: 'Conversation', icon: 'MessageCircle', count: conversationHistory.length },
    { id: 'notes', label: 'Notes', icon: 'FileText', count: sessionNotes.length },
    { id: 'recommendations', label: 'Key Points', icon: 'Star', count: keyRecommendations.length }
  ];

  if (!isVisible) {
    return (
      <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-100">
        <Button
          variant="primary"
          size="sm"
          onClick={onToggleVisibility}
          iconName="ChevronLeft"
          className="p-3 rounded-l-lg rounded-r-none shadow-elevated"
          title="Show conversation history"
        />
      </div>
    );
  }

  return (
    <div className={`bg-surface border-l border-border shadow-minimal h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-text-primary font-heading">
            Session Details
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleVisibility}
            iconName="ChevronRight"
            className="p-2"
            title="Hide sidebar"
          />
        </div>

        {/* Search */}
        <div className="relative">
          <Icon 
            name="Search" 
            size={16} 
            color="var(--color-text-secondary)"
            className="absolute left-3 top-1/2 transform -translate-y-1/2"
          />
          <input
            type="text"
            placeholder="Search conversation..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-border">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 px-3 py-3 text-sm font-medium transition-fast ${
              activeTab === tab.id
                ? 'text-primary-600 border-b-2 border-primary-500 bg-primary-50' :'text-text-secondary hover:text-text-primary hover:bg-secondary-50'
            }`}
          >
            <Icon name={tab.icon} size={16} />
            <span className="hidden lg:inline">{tab.label}</span>
            {tab.count > 0 && (
              <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                activeTab === tab.id
                  ? 'bg-primary-100 text-primary-600' :'bg-secondary-100 text-text-secondary'
              }`}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {/* Conversation Tab */}
        {activeTab === 'conversation' && (
          <div className="h-full overflow-y-auto p-4 space-y-4">
            {filteredHistory.length === 0 ? (
              <div className="text-center py-8">
                <Icon name="MessageCircle" size={48} color="var(--color-text-muted)" />
                <p className="text-text-muted font-caption mt-4">
                  {searchQuery ? 'No matching messages found' : 'No conversation yet'}
                </p>
              </div>
            ) : (
              filteredHistory.map((item, index) => (
                <div 
                  key={item.id || index}
                  className={`flex space-x-3 ${
                    item.speaker === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                  }`}
                >
                  {/* Speaker Avatar */}
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    item.speaker === 'user' ?'bg-primary-50' 
                      : item.speaker === 'system' ?'bg-secondary-50' :'bg-success-50'
                  }`}>
                    <Icon 
                      name={getSpeakerIcon(item.speaker)}
                      size={16}
                      color={`var(--color-${getSpeakerColor(item.speaker)})`}
                    />
                  </div>

                  {/* Message Content */}
                  <div className={`flex-1 ${
                    item.speaker === 'user' ? 'text-right' : 'text-left'
                  }`}>
                    <div className={`inline-block p-3 rounded-lg max-w-full ${
                      item.speaker === 'user' ?'bg-primary-500 text-white'
                        : item.speaker === 'system' ?'bg-secondary-100 text-text-primary' :'bg-success-50 text-success-700'
                    }`}>
                      <p className="text-sm leading-relaxed">
                        {item.content}
                      </p>
                      <div className={`text-xs mt-1 ${
                        item.speaker === 'user' ? 'text-primary-200' : 'text-text-muted'
                      }`}>
                        {formatTimestamp(item.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Notes Tab */}
        {activeTab === 'notes' && (
          <div className="h-full overflow-y-auto p-4 space-y-3">
            {sessionNotes.length === 0 ? (
              <div className="text-center py-8">
                <Icon name="FileText" size={48} color="var(--color-text-muted)" />
                <p className="text-text-muted font-caption mt-4">
                  No session notes yet
                </p>
              </div>
            ) : (
              sessionNotes.map((note, index) => (
                <div 
                  key={note.id || index}
                  className="p-3 bg-secondary-50 rounded-lg border border-secondary-200"
                >
                  <div className="flex items-start space-x-2">
                    <Icon name="FileText" size={16} color="var(--color-text-secondary)" />
                    <div className="flex-1">
                      <p className="text-sm text-text-primary">
                        {note.content}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-text-secondary">
                          {note.category || 'General'}
                        </span>
                        <span className="text-xs text-text-muted">
                          {formatTimestamp(note.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Recommendations Tab */}
        {activeTab === 'recommendations' && (
          <div className="h-full overflow-y-auto p-4 space-y-3">
            {keyRecommendations.length === 0 ? (
              <div className="text-center py-8">
                <Icon name="Star" size={48} color="var(--color-text-muted)" />
                <p className="text-text-muted font-caption mt-4">
                  No key recommendations yet
                </p>
              </div>
            ) : (
              keyRecommendations.map((rec, index) => (
                <div 
                  key={rec.id || index}
                  className="p-3 bg-accent-50 rounded-lg border border-accent-200"
                >
                  <div className="flex items-start space-x-2">
                    <Icon name="Star" size={16} color="var(--color-accent)" />
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-accent-700 mb-1">
                        {rec.title || 'Key Recommendation'}
                      </h4>
                      <p className="text-sm text-accent-600">
                        {rec.content}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-accent-500">
                          {rec.priority || 'Medium'} Priority
                        </span>
                        <span className="text-xs text-accent-400">
                          {rec.agent || 'AI Agent'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-border">
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onExportHistory}
            iconName="Download"
            iconPosition="left"
            className="w-full justify-center"
          >
            Export Session
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = '/session-dashboard-history'}
              iconName="History"
              className="justify-center text-xs"
            >
              All Sessions
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = '/patient-profile-setup'}
              iconName="Settings"
              className="justify-center text-xs"
            >
              Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversationHistorySidebar;