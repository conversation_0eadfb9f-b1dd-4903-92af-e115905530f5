import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const VoiceActivationButton = ({ 
  isListening = false,
  isProcessing = false,
  onStartListening = () => {},
  onStopListening = () => {},
  disabled = false,
  className = ''
}) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const [pulseAnimation, setPulseAnimation] = useState(false);

  useEffect(() => {
    if (isListening) {
      setPulseAnimation(true);
      // Simulate audio level changes
      const interval = setInterval(() => {
        setAudioLevel(Math.random() * 100);
      }, 100);
      return () => clearInterval(interval);
    } else {
      setPulseAnimation(false);
      setAudioLevel(0);
    }
  }, [isListening]);

  const handleClick = () => {
    if (isListening) {
      onStopListening();
    } else {
      onStartListening();
    }
  };

  const getButtonState = () => {
    if (isProcessing) return 'processing';
    if (isListening) return 'listening';
    return 'ready';
  };

  const buttonState = getButtonState();

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* Audio Waveform Visualization */}
      {isListening && (
        <div className="flex items-center justify-center space-x-1 h-12 mb-4">
          {[...Array(7)].map((_, i) => (
            <div
              key={i}
              className="voice-waveform-bar bg-primary-500"
              style={{
                height: `${Math.random() * 40 + 10}px`,
                animationDelay: `${i * 0.1}s`
              }}
            ></div>
          ))}
        </div>
      )}

      {/* Main Voice Button */}
      <button
        onClick={handleClick}
        disabled={disabled}
        className={`
          relative w-32 h-32 rounded-full flex items-center justify-center
          transition-all duration-300 transform touch-target
          ${buttonState === 'listening' ?'bg-primary-500 hover:bg-primary-600 animate-consultation-pulse scale-110' 
            : buttonState === 'processing' ?'bg-warning-500 hover:bg-warning-600 animate-ambient-float' :'bg-secondary-100 hover:bg-secondary-200 hover:scale-105'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          shadow-elevated hover:shadow-floating
        `}
        title={
          buttonState === 'listening' ? 'Stop listening' :
          buttonState === 'processing'? 'Processing...' : 'Start voice consultation'
        }
      >
        {/* Button Icon */}
        <Icon 
          name={
            buttonState === 'listening' ? 'MicOff' :
            buttonState === 'processing'? 'Brain' : 'Mic'
          }
          size={48}
          color={
            buttonState === 'listening' || buttonState === 'processing' ?'white' :'var(--color-text-secondary)'
          }
        />

        {/* Pulse Ring Animation */}
        {isListening && (
          <div className="absolute inset-0 rounded-full border-4 border-primary-300 animate-ping opacity-75"></div>
        )}

        {/* Audio Level Indicator */}
        {isListening && (
          <div 
            className="absolute inset-0 rounded-full border-4 border-primary-400 transition-all duration-100"
            style={{
              transform: `scale(${1 + (audioLevel / 200)})`,
              opacity: 0.6
            }}
          ></div>
        )}
      </button>

      {/* Status Text */}
      <div className="text-center">
        <p className="text-lg font-semibold text-text-primary font-heading">
          {buttonState === 'listening' ? 'Listening...' :
           buttonState === 'processing'? 'Processing...' : 'Tap to Start'}
        </p>
        <p className="text-sm text-text-secondary font-caption mt-1">
          {buttonState === 'listening' ? 'Speak clearly into your microphone' :
           buttonState === 'processing'? 'AI agents are analyzing your input' : 'Voice consultation ready'}
        </p>
      </div>

      {/* Quick Instructions */}
      {!isListening && !isProcessing && (
        <div className="text-center max-w-sm">
          <p className="text-xs text-text-muted font-caption">
            Press and hold to speak, or tap to toggle voice mode
          </p>
        </div>
      )}
    </div>
  );
};

export default VoiceActivationButton;