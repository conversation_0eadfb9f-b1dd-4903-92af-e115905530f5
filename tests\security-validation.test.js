/**
 * COMPREHENSIVE SECURITY VALIDATION TESTS
 * 
 * This test suite validates all critical security implementations
 * for HIPAA compliance and patient data protection.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import encryptionService from '../src/utils/encryptionService';
import medicalDataValidator from '../src/utils/medicalDataValidator';
import auditLogger from '../src/utils/auditLogger';

// Mock Supabase client
vi.mock('../src/utils/supabaseClient', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(() => Promise.resolve({
        data: { session: { access_token: 'mock-token-12345', user: { id: 'user-123', email: '<EMAIL>' } } }
      }))
    },
    from: vi.fn(() => ({
      insert: vi.fn(() => ({ select: vi.fn(() => ({ single: vi.fn(() => Promise.resolve({ data: { id: 'audit-123' }, error: null })) })) }))
    }))
  }
}));

describe('Security Implementation Validation', () => {
  
  describe('1. Medical Data Encryption', () => {
    const mockSessionToken = 'mock-session-token-for-testing';
    const sensitiveData = {
      condition_name: 'Hypertension',
      notes: 'Patient has high blood pressure',
      diagnosed_date: '2024-01-15',
      severity: 'moderate'
    };

    it('should encrypt sensitive medical data', async () => {
      const encrypted = await encryptionService.encryptMedicalData(sensitiveData, mockSessionToken);
      
      expect(encrypted.encrypted).toBe(true);
      expect(encrypted.data).toBeDefined();
      expect(encrypted.algorithm).toBe('AES-GCM');
      expect(encrypted.keyLength).toBe(256);
      expect(typeof encrypted.data).toBe('string');
      expect(encrypted.data).not.toContain('Hypertension'); // Data should be encrypted
    });

    it('should decrypt medical data correctly', async () => {
      const encrypted = await encryptionService.encryptMedicalData(sensitiveData, mockSessionToken);
      const decrypted = await encryptionService.decryptMedicalData(encrypted, mockSessionToken);
      
      expect(decrypted).toEqual(sensitiveData);
    });

    it('should fail decryption with wrong session token', async () => {
      const encrypted = await encryptionService.encryptMedicalData(sensitiveData, mockSessionToken);
      
      await expect(
        encryptionService.decryptMedicalData(encrypted, 'wrong-token')
      ).rejects.toThrow('Failed to decrypt medical data');
    });

    it('should validate encryption integrity', async () => {
      const isValid = await encryptionService.validateEncryption(sensitiveData, mockSessionToken);
      expect(isValid).toBe(true);
    });

    it('should handle secure localStorage operations', async () => {
      const key = 'test-medical-data';
      
      const stored = await encryptionService.setSecureItem(key, sensitiveData, mockSessionToken);
      expect(stored).toBe(true);
      
      const retrieved = await encryptionService.getSecureItem(key, mockSessionToken);
      expect(retrieved).toEqual(sensitiveData);
      
      encryptionService.removeSecureItem(key);
      const afterRemoval = await encryptionService.getSecureItem(key, mockSessionToken);
      expect(afterRemoval).toBeNull();
    });
  });

  describe('2. Input Validation & Sanitization', () => {
    
    it('should validate and sanitize medical conditions', () => {
      const validCondition = {
        condition_name: 'Diabetes Type 2',
        notes: 'Patient diagnosed with diabetes',
        diagnosed_date: '2024-01-15',
        severity: 'moderate'
      };

      const result = medicalDataValidator.validateMedicalConditionData(validCondition);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitized.condition_name).toBe('Diabetes Type 2');
    });

    it('should reject invalid medical condition data', () => {
      const invalidCondition = {
        condition_name: '<script>alert("xss")</script>',
        notes: 'A'.repeat(3000), // Too long
        diagnosed_date: 'invalid-date',
        severity: 'invalid-severity'
      };

      const result = medicalDataValidator.validateMedicalConditionData(invalidCondition);
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should sanitize XSS attempts', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello';
      const sanitized = medicalDataValidator.sanitizeInput(maliciousInput);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toBe('Hello');
    });

    it('should validate email formats', () => {
      const validEmail = medicalDataValidator.validateEmail('<EMAIL>');
      expect(validEmail.valid).toBe(true);
      
      const invalidEmail = medicalDataValidator.validateEmail('invalid-email');
      expect(invalidEmail.valid).toBe(false);
    });

    it('should validate medication data', () => {
      const validMedication = {
        medication_name: 'Metformin',
        dosage: '500mg',
        frequency: 'Twice daily',
        notes: 'Take with meals'
      };

      const result = medicalDataValidator.validateMedicationData(validMedication);
      
      expect(result.valid).toBe(true);
      expect(result.sanitized.medication_name).toBe('Metformin');
    });

    it('should enforce length limits', () => {
      const longCondition = {
        condition_name: 'A'.repeat(300), // Exceeds 200 char limit
        notes: 'Valid notes'
      };

      const result = medicalDataValidator.validateMedicalConditionData(longCondition);
      
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('too long'))).toBe(true);
    });
  });

  describe('3. Audit Logging', () => {
    
    beforeEach(() => {
      // Clear any existing logs
      auditLogger.logQueue = [];
    });

    it('should log medical data access events', async () => {
      await auditLogger.logMedicalDataAccess('read', 'medical_condition', 'condition-123', {
        success: true,
        user_id: 'user-123'
      });

      expect(auditLogger.logQueue).toHaveLength(1);
      const logEntry = auditLogger.logQueue[0];
      
      expect(logEntry.event_type).toBe('medical_data_access');
      expect(logEntry.action).toBe('read');
      expect(logEntry.resource_type).toBe('medical_condition');
      expect(logEntry.resource_id).toBe('condition-123');
      expect(logEntry.compliance_flags.hipaa_relevant).toBe(true);
      expect(logEntry.integrity_hash).toBeDefined();
    });

    it('should log authentication events', async () => {
      await auditLogger.logAuthenticationEvent('user_login', true, {
        method: 'password',
        email: '<EMAIL>'
      });

      expect(auditLogger.logQueue).toHaveLength(1);
      const logEntry = auditLogger.logQueue[0];
      
      expect(logEntry.event_type).toBe('user_login');
      expect(logEntry.action).toBe('success');
      expect(logEntry.metadata.authentication_method).toBe('password');
      expect(logEntry.compliance_flags.security_event).toBe(true);
    });

    it('should log security events with appropriate severity', async () => {
      await auditLogger.logSecurityEvent('data_breach_attempt', 'critical', 'Unauthorized access attempt', {
        ip_address: '*************'
      });

      expect(auditLogger.logQueue).toHaveLength(1);
      const logEntry = auditLogger.logQueue[0];
      
      expect(logEntry.event_type).toBe('data_breach_attempt');
      expect(logEntry.severity).toBe('critical');
      expect(logEntry.compliance_flags.immediate_review_required).toBe(true);
    });

    it('should generate unique audit IDs', () => {
      const id1 = auditLogger.generateAuditId();
      const id2 = auditLogger.generateAuditId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^audit_\d+_[a-z0-9]+$/);
    });

    it('should create integrity hashes', async () => {
      const logEntry = {
        id: 'test-123',
        timestamp: '2024-01-01T00:00:00Z',
        event_type: 'test_event',
        action: 'test_action'
      };

      const hash = await auditLogger.createIntegrityHash(logEntry);
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBe(64); // SHA-256 hex string
    });

    it('should not log sensitive medical data', async () => {
      await auditLogger.logConditionAccess('create', 'condition-123', true, {
        condition_name: 'Diabetes', // This should not appear in logs
        user_id: 'user-123'
      });

      const logEntry = auditLogger.logQueue[0];
      const logString = JSON.stringify(logEntry);
      
      // Verify sensitive data is not in the log
      expect(logString).not.toContain('Diabetes');
      expect(logEntry.metadata.data_type).toBeDefined();
      expect(logEntry.compliance_flags.sensitive_data_excluded).toBe(true);
    });
  });

  describe('4. Environment Security', () => {
    
    it('should not expose secret keys in environment', () => {
      // Check that secret keys are not accessible in client environment
      expect(import.meta.env.VITE_PAYSTACK_SECRET_KEY).toBeUndefined();
      expect(import.meta.env.PAYSTACK_SECRET_KEY).toBeUndefined();
      
      // Public key should be available
      expect(import.meta.env.VITE_PAYSTACK_PUBLIC_KEY).toBeDefined();
    });

    it('should have secure API base URL configured', () => {
      const apiUrl = import.meta.env.VITE_API_BASE_URL;
      
      expect(apiUrl).toBeDefined();
      expect(apiUrl).not.toContain('supabase.co/rest'); // Should point to secure backend
    });
  });

  describe('5. Data Protection Compliance', () => {
    
    it('should implement proper data retention', () => {
      expect(auditLogger.retentionDays).toBe(2555); // 7 years for HIPAA
    });

    it('should have tamper-proof audit logs', () => {
      expect(auditLogger.eventTypes.DATA_BREACH_ATTEMPT).toBeDefined();
      expect(auditLogger.eventTypes.MEDICAL_DATA_ACCESS).toBeDefined();
    });

    it('should encrypt data before storage', async () => {
      const testData = { sensitive: 'medical information' };
      const sessionToken = 'test-token';
      
      const encrypted = await encryptionService.encryptMedicalData(testData, sessionToken);
      
      expect(encrypted.encrypted).toBe(true);
      expect(encrypted.data).not.toContain('medical information');
    });
  });

  describe('6. Error Handling & Security', () => {
    
    it('should handle encryption failures gracefully', async () => {
      // Test with invalid session token
      const result = await encryptionService.encryptMedicalData({}, null);
      
      expect(result.encrypted).toBe(false);
      expect(result.data).toBeDefined();
    });

    it('should validate UUIDs properly', () => {
      const validUUID = medicalDataValidator.validateUUID('123e4567-e89b-12d3-a456-************');
      expect(validUUID.valid).toBe(true);
      
      const invalidUUID = medicalDataValidator.validateUUID('invalid-uuid');
      expect(invalidUUID.valid).toBe(false);
    });

    it('should handle date validation edge cases', () => {
      const futureDate = medicalDataValidator.validateDate('2030-01-01');
      expect(futureDate.valid).toBe(false);
      
      const validDate = medicalDataValidator.validateDate('2024-01-01');
      expect(validDate.valid).toBe(true);
    });
  });

  afterEach(() => {
    // Clean up after each test
    encryptionService.clearKeyCache();
    auditLogger.logQueue = [];
  });
});

// Integration test for complete security workflow
describe('Security Integration Tests', () => {
  
  it('should complete secure medical data workflow', async () => {
    const sessionToken = 'integration-test-token';
    const medicalData = {
      condition_name: 'Hypertension',
      notes: 'Patient has elevated blood pressure',
      diagnosed_date: '2024-01-15',
      severity: 'moderate'
    };

    // 1. Validate input
    const validation = medicalDataValidator.validateMedicalConditionData(medicalData);
    expect(validation.valid).toBe(true);

    // 2. Encrypt data
    const encrypted = await encryptionService.encryptMedicalData(validation.sanitized, sessionToken);
    expect(encrypted.encrypted).toBe(true);

    // 3. Log the operation
    await auditLogger.logConditionAccess('create', 'condition-123', true, {
      operation: 'integration_test',
      user_id: 'test-user'
    });

    // 4. Decrypt and verify
    const decrypted = await encryptionService.decryptMedicalData(encrypted, sessionToken);
    expect(decrypted.condition_name).toBe('Hypertension');

    // 5. Verify audit log
    expect(auditLogger.logQueue).toHaveLength(1);
    const auditEntry = auditLogger.logQueue[0];
    expect(auditEntry.compliance_flags.hipaa_relevant).toBe(true);
  });
});
