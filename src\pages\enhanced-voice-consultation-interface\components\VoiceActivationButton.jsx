import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const VoiceActivationButton = ({ 
  isRecording = false,
  audioLevel = 0,
  sessionPhase = 'ready',
  onActivate = () => {},
  disabled = false,
  showAdvancedVisuals = false,
  className = ''
}) => {
  const [pulseAnimation, setPulseAnimation] = useState(false);
  const [voiceActivityDetected, setVoiceActivityDetected] = useState(false);
  const [renderError, setRenderError] = useState(false);

  useEffect(() => {
    try {
      // Reset error state when props change
      setRenderError(false);
    } catch (error) {
      console.error('VoiceActivationButton render error:', error);
      setRenderError(true);
    }
  }, [isRecording, sessionPhase, audioLevel]);

  if (renderError) {
    return (
      <div className={`flex flex-col items-center space-y-6 ${className}`}>
        <div className="w-40 h-40 rounded-full bg-secondary-100 flex items-center justify-center">
          <Icon name="AlertCircle" size={48} color="var(--color-text-secondary)" />
        </div>
        <p className="text-text-secondary">Voice interface temporarily unavailable</p>
      </div>
    );
  }

  useEffect(() => {
    if (isRecording) {
      setPulseAnimation(true);
      // Voice activity detection based on audio level
      setVoiceActivityDetected(audioLevel > 20);
    } else {
      setPulseAnimation(false);
      setVoiceActivityDetected(false);
    }
  }, [isRecording, audioLevel]);

  const getButtonState = () => {
    if (sessionPhase === 'processing') return 'processing';
    if (sessionPhase === 'collaborating') return 'collaborating';
    if (sessionPhase === 'responding') return 'responding';
    if (isRecording) return 'listening';
    return 'ready';
  };

  const buttonState = getButtonState();

  const getButtonIcon = () => {
    switch (buttonState) {
      case 'listening': return voiceActivityDetected ? 'Mic' : 'MicOff';
      case 'processing': return 'Brain';
      case 'collaborating': return 'Users';
      case 'responding': return 'MessageCircle';
      default: return 'Mic';
    }
  };

  const getButtonColor = () => {
    switch (buttonState) {
      case 'listening': return 'bg-primary-500 hover:bg-primary-600';
      case 'processing': return 'bg-warning-500 hover:bg-warning-600';
      case 'collaborating': return 'bg-purple-500 hover:bg-purple-600';
      case 'responding': return 'bg-success-500 hover:bg-success-600';
      default: return 'bg-secondary-100 hover:bg-secondary-200';
    }
  };

  const getIconColor = () => {
    return (buttonState === 'ready') ? 'var(--color-text-secondary)' : 'white';
  };

  const getAnimationClass = () => {
    try {
      switch (buttonState) {
        case 'listening': return 'animate-consultation-pulse scale-110';
        case 'processing': return 'animate-ambient-float';
        case 'collaborating': return 'animate-collaboration-spin';
        case 'responding': return 'animate-enhanced-pulse';
        default: return 'hover:scale-105 transition-transform duration-200';
      }
    } catch (error) {
      console.warn('Animation class error:', error);
      return 'transition-transform duration-200';
    }
  };

  return (
    <div className={`flex flex-col items-center space-y-6 gpu-accelerated ${className}`}>
      {/* Advanced Audio Waveform Visualization */}
      {showAdvancedVisuals && isRecording && (
        <div className="flex items-center justify-center space-x-1 h-16 mb-4 gpu-accelerated">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="voice-waveform-bar gpu-accelerated"
              style={{
                backgroundColor: voiceActivityDetected ? 'rgb(59, 130, 246)' : 'rgb(156, 163, 175)',
                height: `${Math.random() * 50 + 10}px`,
                animationDelay: `${i * 0.08}s`,
                width: '4px',
                borderRadius: '2px',
                willChange: 'height, background-color'
              }}
            ></div>
          ))}
        </div>
      )}

      {/* Voice Activity Detection Indicator */}
      {showAdvancedVisuals && isRecording && (
        <div className="flex items-center space-x-4 mb-4 gpu-accelerated">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full transition-all duration-200 ${
              voiceActivityDetected ? 'bg-success-500 animate-pulse' : 'bg-secondary-300'
            }`}></div>
            <span className="text-sm text-text-secondary">
              {voiceActivityDetected ? 'Voice Detected' : 'Silence'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Icon name="Volume2" size={16} className="text-text-secondary" />
            <div className="w-24 h-2 bg-secondary-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-100 ${
                  voiceActivityDetected ? 'bg-primary-500' : 'bg-secondary-400'
                }`}
                style={{ 
                  width: `${Math.min(audioLevel, 100)}%`,
                  willChange: 'width'
                }}
              ></div>
            </div>
            <span className="text-xs text-text-secondary w-8">
              {Math.round(audioLevel)}%
            </span>
          </div>
        </div>
      )}

      {/* Main Voice Button with Enhanced Design */}
      <div className="relative gpu-accelerated">
        {/* Outer Ring Animation */}
        {isRecording && (
          <>
            <div className="absolute inset-0 rounded-full border-4 border-primary-300 animate-ping opacity-40 scale-110 gpu-accelerated"></div>
            <div className="absolute inset-0 rounded-full border-3 border-primary-400 animate-pulse opacity-60 scale-105 gpu-accelerated"></div>
          </>
        )}

        {/* Voice Activity Ring */}
        {showAdvancedVisuals && voiceActivityDetected && (
          <div 
            className="absolute inset-0 rounded-full border-4 border-success-400 transition-all duration-200 gpu-accelerated"
            style={{
              transform: `scale(${1 + (audioLevel / 300)})`,
              opacity: 0.7,
              willChange: 'transform, opacity'
            }}
          ></div>
        )}

        <button
          onClick={onActivate}
          disabled={disabled}
          className={`
            relative w-40 h-40 rounded-full flex items-center justify-center
            transition-all duration-300 transform touch-target gpu-accelerated
            ${getButtonColor()}
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            ${getAnimationClass()}
            shadow-elevated hover:shadow-floating
            border-4 border-white
          `}
          style={{ willChange: 'transform, box-shadow, background-color' }}
          title={
            buttonState === 'listening' ? 'Stop listening' :
            buttonState === 'processing' ? 'Processing audio...' :
            buttonState === 'collaborating' ? 'Agents collaborating...' :
            buttonState === 'responding'? 'Preparing response...' : 'Start enhanced voice consultation'
          }
        >
          {/* Button Icon with Dynamic Sizing */}
          <Icon 
            name={getButtonIcon()}
            size={buttonState === 'listening' ? 56 : 48}
            color={getIconColor()}
          />

          {/* Audio Level Visualization Overlay */}
          {showAdvancedVisuals && isRecording && (
            <div 
              className="absolute inset-4 rounded-full border-2 border-white transition-all duration-100 pointer-events-none gpu-accelerated"
              style={{
                opacity: 0.4,
                transform: `scale(${0.8 + (audioLevel / 500)})`,
                willChange: 'transform, opacity'
              }}
            ></div>
          )}

          {/* Phase-specific Visual Effects */}
          {buttonState === 'collaborating' && (
            <div className="absolute inset-0 rounded-full gpu-accelerated">
              <div className="absolute top-2 right-6 w-3 h-3 bg-white rounded-full animate-bounce"></div>
              <div className="absolute bottom-6 left-4 w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              <div className="absolute top-8 left-2 w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
            </div>
          )}

          {buttonState === 'processing' && (
            <div className="absolute inset-8 border-2 border-white border-dashed rounded-full animate-spin opacity-50 gpu-accelerated"></div>
          )}
        </button>
      </div>

      {/* Enhanced Status Text */}
      <div className="text-center gpu-accelerated">
        <p className="text-xl font-semibold text-text-primary font-heading">
          {buttonState === 'listening' ? 'Listening...' :
           buttonState === 'processing' ? 'Processing...' :
           buttonState === 'collaborating' ? 'AI Collaboration...' :
           buttonState === 'responding'? 'Preparing Response...' : 'Tap to Start'}
        </p>
        <p className="text-sm text-text-secondary font-caption mt-2">
          {buttonState === 'listening' ? 'Speak clearly into your microphone. Advanced noise suppression is active.' :
           buttonState === 'processing' ? 'AI agents are analyzing your input with advanced algorithms' :
           buttonState === 'collaborating' ? 'Multiple AI specialists are collaborating on your case' :
           buttonState === 'responding'? 'Generating personalized response with enhanced audio quality' : 'Enhanced voice consultation with real-time audio processing'}
        </p>
        
        {/* Advanced Phase Indicator */}
        {showAdvancedVisuals && buttonState !== 'ready' && (
          <div className="mt-3 gpu-accelerated">
            <div className="flex items-center justify-center space-x-2">
              <div className={`w-2 h-2 rounded-full transition-all duration-200 ${
                buttonState === 'listening' ? 'bg-primary-500 animate-pulse' : 'bg-secondary-300'
              }`}></div>
              <div className={`w-2 h-2 rounded-full transition-all duration-200 ${
                buttonState === 'processing' ? 'bg-warning-500 animate-pulse' : 'bg-secondary-300'
              }`}></div>
              <div className={`w-2 h-2 rounded-full transition-all duration-200 ${
                buttonState === 'collaborating' ? 'bg-purple-500 animate-pulse' : 'bg-secondary-300'
              }`}></div>
              <div className={`w-2 h-2 rounded-full transition-all duration-200 ${
                buttonState === 'responding' ? 'bg-success-500 animate-pulse' : 'bg-secondary-300'
              }`}></div>
            </div>
            <p className="text-xs text-text-muted mt-2">
              Enhanced processing pipeline active
            </p>
          </div>
        )}
      </div>

      {/* Quick Instructions for Enhanced Features */}
      {!isRecording && buttonState === 'ready' && showAdvancedVisuals && (
        <div className="text-center max-w-md gpu-accelerated">
          <p className="text-xs text-text-muted font-caption">
            Enhanced consultation includes: Real-time noise suppression • Echo cancellation • 
            Multi-agent collaboration • Advanced transcription • Secure audio processing
          </p>
        </div>
      )}
    </div>
  );
};

export default VoiceActivationButton;