import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';
import Button from '../../../components/ui/Button';

const SessionFilters = ({ 
  onFilterChange, 
  onSearchChange, 
  activeFilters = {},
  className = '' 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedAgents, setSelectedAgents] = useState([]);
  const [selectedTopics, setSelectedTopics] = useState([]);

  const statusOptions = [
    { value: 'all', label: 'All Sessions', icon: 'List' },
    { value: 'active', label: 'Active', icon: 'Play' },
    { value: 'completed', label: 'Completed', icon: 'CheckCircle' },
    { value: 'paused', label: 'Paused', icon: 'Pause' },
    { value: 'cancelled', label: 'Cancelled', icon: 'XCircle' }
  ];

  const agentTypes = [
    { value: 'gp', label: 'General Practitioner', specialty: 'General Medicine' },
    { value: 'cardiologist', label: 'Cardiologist', specialty: 'Heart Health' },
    { value: 'nutritionist', label: 'Nutritionist', specialty: 'Nutrition' },
    { value: 'psychiatrist', label: 'Psychiatrist', specialty: 'Mental Health' },
    { value: 'dermatologist', label: 'Dermatologist', specialty: 'Skin Care' }
  ];

  const medicalTopics = [
    'General Health', 'Heart Health', 'Nutrition', 'Mental Health', 
    'Skin Care', 'Diabetes', 'Hypertension', 'Weight Management',
    'Sleep Disorders', 'Allergies', 'Pain Management', 'Preventive Care'
  ];

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearchChange(value);
  };

  const handleStatusChange = (status) => {
    setSelectedStatus(status);
    onFilterChange({ ...activeFilters, status });
  };

  const handleAgentToggle = (agentType) => {
    const updatedAgents = selectedAgents.includes(agentType)
      ? selectedAgents.filter(a => a !== agentType)
      : [...selectedAgents, agentType];
    
    setSelectedAgents(updatedAgents);
    onFilterChange({ ...activeFilters, agents: updatedAgents });
  };

  const handleTopicToggle = (topic) => {
    const updatedTopics = selectedTopics.includes(topic)
      ? selectedTopics.filter(t => t !== topic)
      : [...selectedTopics, topic];
    
    setSelectedTopics(updatedTopics);
    onFilterChange({ ...activeFilters, topics: updatedTopics });
  };

  const handleDateRangeChange = (field, value) => {
    const updatedRange = { ...dateRange, [field]: value };
    setDateRange(updatedRange);
    onFilterChange({ ...activeFilters, dateRange: updatedRange });
  };

  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedStatus('all');
    setSelectedAgents([]);
    setSelectedTopics([]);
    setDateRange({ start: '', end: '' });
    onFilterChange({});
    onSearchChange('');
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (selectedStatus !== 'all') count++;
    if (selectedAgents.length > 0) count++;
    if (selectedTopics.length > 0) count++;
    if (dateRange.start || dateRange.end) count++;
    return count;
  };

  return (
    <div className={`bg-surface border border-border rounded-lg ${className}`}>
      {/* Search and Quick Filters */}
      <div className="p-4 border-b border-border">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="Search" size={16} color="var(--color-text-secondary)" />
            </div>
            <Input
              type="search"
              placeholder="Search sessions, topics, or agents..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10"
            />
          </div>
          
          {/* Filter Toggle */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsExpanded(!isExpanded)}
              iconName={isExpanded ? "ChevronUp" : "Filter"}
              iconPosition="left"
            >
              Filters
              {getActiveFilterCount() > 0 && (
                <span className="ml-2 px-2 py-0.5 bg-primary-500 text-white rounded-full text-xs">
                  {getActiveFilterCount()}
                </span>
              )}
            </Button>
            
            {getActiveFilterCount() > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                iconName="X"
                iconPosition="left"
              >
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Quick Status Filters */}
        <div className="flex flex-wrap gap-2 mt-4">
          {statusOptions.map((status) => (
            <button
              key={status.value}
              onClick={() => handleStatusChange(status.value)}
              className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-fast ${
                selectedStatus === status.value
                  ? 'bg-primary-500 text-white' :'bg-secondary-50 text-text-secondary hover:bg-secondary-100'
              }`}
            >
              <Icon name={status.icon} size={14} />
              <span>{status.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* Date Range */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Date Range</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-text-secondary mb-1">From</label>
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => handleDateRangeChange('start', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-xs text-text-secondary mb-1">To</label>
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => handleDateRangeChange('end', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Agent Types */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Agent Types</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
              {agentTypes.map((agent) => (
                <button
                  key={agent.value}
                  onClick={() => handleAgentToggle(agent.value)}
                  className={`flex items-center space-x-3 p-3 rounded-lg border transition-fast text-left ${
                    selectedAgents.includes(agent.value)
                      ? 'bg-primary-50 border-primary-200 text-primary-600' :'bg-surface border-border hover:bg-secondary-50'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    selectedAgents.includes(agent.value) ? 'bg-primary-500' : 'bg-secondary-200'
                  }`}>
                    <Icon 
                      name="User" 
                      size={14} 
                      color={selectedAgents.includes(agent.value) ? 'white' : 'var(--color-text-secondary)'}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{agent.label}</div>
                    <div className="text-xs text-text-secondary">{agent.specialty}</div>
                  </div>
                  {selectedAgents.includes(agent.value) && (
                    <Icon name="Check" size={16} color="var(--color-primary)" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Medical Topics */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Medical Topics</h4>
            <div className="flex flex-wrap gap-2">
              {medicalTopics.map((topic) => (
                <button
                  key={topic}
                  onClick={() => handleTopicToggle(topic)}
                  className={`px-3 py-1.5 rounded-full text-sm font-medium transition-fast ${
                    selectedTopics.includes(topic)
                      ? 'bg-accent-500 text-white' :'bg-secondary-50 text-text-secondary hover:bg-secondary-100'
                  }`}
                >
                  {topic}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionFilters;