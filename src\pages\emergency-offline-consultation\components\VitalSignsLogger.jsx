import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const VitalSignsLogger = ({ onVitalSignsUpdate }) => {
  const [vitalSigns, setVitalSigns] = useState({
    bloodPressure: { systolic: '', diastolic: '' },
    heartRate: '',
    temperature: '',
    respiratoryRate: '',
    oxygenSaturation: '',
    glucoseLevel: '',
    timestamp: null
  });

  const [isRecording, setIsRecording] = useState(false);

  const vitalSignsRanges = {
    heartRate: { normal: [60, 100], unit: 'BPM' },
    temperature: { normal: [97.0, 99.5], unit: '°F' },
    respiratoryRate: { normal: [12, 20], unit: '/min' },
    oxygenSaturation: { normal: [95, 100], unit: '%' },
    glucoseLevel: { normal: [70, 140], unit: 'mg/dL' },
    bloodPressure: { 
      systolic: { normal: [90, 140], unit: 'mmHg' },
      diastolic: { normal: [60, 90], unit: 'mmHg' }
    }
  };

  const handleInputChange = (field, value, subField = null) => {
    setVitalSigns(prev => {
      if (subField) {
        return {
          ...prev,
          [field]: {
            ...prev[field],
            [subField]: value
          }
        };
      }
      return {
        ...prev,
        [field]: value
      };
    });
  };

  const getVitalStatus = (field, value, subField = null) => {
    if (!value || value === '') return 'normal';
    
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return 'normal';

    let range;
    if (subField) {
      range = vitalSignsRanges[field][subField].normal;
    } else {
      range = vitalSignsRanges[field].normal;
    }

    if (numValue < range[0]) return 'low';
    if (numValue > range[1]) return 'high';
    return 'normal';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'normal': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleRecordVitals = () => {
    const recordedVitals = {
      ...vitalSigns,
      timestamp: new Date()
    };
    
    // Store in local storage
    const existingVitals = JSON.parse(localStorage.getItem('offlineVitalSigns') || '[]');
    existingVitals.push(recordedVitals);
    localStorage.setItem('offlineVitalSigns', JSON.stringify(existingVitals));
    
    onVitalSignsUpdate?.(recordedVitals);
    
    // Reset form
    setVitalSigns({
      bloodPressure: { systolic: '', diastolic: '' },
      heartRate: '',
      temperature: '',
      respiratoryRate: '',
      oxygenSaturation: '',
      glucoseLevel: '',
      timestamp: null
    });
  };

  const startVoiceRecording = () => {
    setIsRecording(true);
    // Simulate voice recording for vital signs
    setTimeout(() => {
      setIsRecording(false);
      // Mock voice-to-text result
      setVitalSigns(prev => ({
        ...prev,
        heartRate: '75',
        temperature: '98.6',
        bloodPressure: { systolic: '120', diastolic: '80' }
      }));
    }, 3000);
  };

  return (
    <div className="bg-surface rounded-xl shadow-minimal border border-border p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Icon name="Activity" size={24} className="text-primary mr-3" />
          <h3 className="text-xl font-semibold text-text-primary">Vital Signs Logger</h3>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={startVoiceRecording}
          disabled={isRecording}
          iconName={isRecording ? "Square" : "Mic"}
          iconPosition="left"
        >
          {isRecording ? 'Recording...' : 'Voice Input'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Blood Pressure */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-text-primary">Blood Pressure</label>
          <div className="flex space-x-2">
            <div className="flex-1">
              <input
                type="number"
                placeholder="Systolic"
                value={vitalSigns.bloodPressure.systolic}
                onChange={(e) => handleInputChange('bloodPressure', e.target.value, 'systolic')}
                className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <div className={`text-xs mt-1 px-2 py-1 rounded ${getStatusColor(getVitalStatus('bloodPressure', vitalSigns.bloodPressure.systolic, 'systolic'))}`}>
                {getVitalStatus('bloodPressure', vitalSigns.bloodPressure.systolic, 'systolic')} (90-140)
              </div>
            </div>
            <div className="flex items-center text-text-secondary">/</div>
            <div className="flex-1">
              <input
                type="number"
                placeholder="Diastolic"
                value={vitalSigns.bloodPressure.diastolic}
                onChange={(e) => handleInputChange('bloodPressure', e.target.value, 'diastolic')}
                className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <div className={`text-xs mt-1 px-2 py-1 rounded ${getStatusColor(getVitalStatus('bloodPressure', vitalSigns.bloodPressure.diastolic, 'diastolic'))}`}>
                {getVitalStatus('bloodPressure', vitalSigns.bloodPressure.diastolic, 'diastolic')} (60-90)
              </div>
            </div>
          </div>
        </div>

        {/* Heart Rate */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-text-primary">Heart Rate (BPM)</label>
          <input
            type="number"
            placeholder="e.g., 72"
            value={vitalSigns.heartRate}
            onChange={(e) => handleInputChange('heartRate', e.target.value)}
            className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <div className={`text-xs px-2 py-1 rounded ${getStatusColor(getVitalStatus('heartRate', vitalSigns.heartRate))}`}>
            {getVitalStatus('heartRate', vitalSigns.heartRate)} (60-100 BPM)
          </div>
        </div>

        {/* Temperature */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-text-primary">Temperature (°F)</label>
          <input
            type="number"
            step="0.1"
            placeholder="e.g., 98.6"
            value={vitalSigns.temperature}
            onChange={(e) => handleInputChange('temperature', e.target.value)}
            className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <div className={`text-xs px-2 py-1 rounded ${getStatusColor(getVitalStatus('temperature', vitalSigns.temperature))}`}>
            {getVitalStatus('temperature', vitalSigns.temperature)} (97.0-99.5°F)
          </div>
        </div>

        {/* Respiratory Rate */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-text-primary">Respiratory Rate (/min)</label>
          <input
            type="number"
            placeholder="e.g., 16"
            value={vitalSigns.respiratoryRate}
            onChange={(e) => handleInputChange('respiratoryRate', e.target.value)}
            className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <div className={`text-xs px-2 py-1 rounded ${getStatusColor(getVitalStatus('respiratoryRate', vitalSigns.respiratoryRate))}`}>
            {getVitalStatus('respiratoryRate', vitalSigns.respiratoryRate)} (12-20/min)
          </div>
        </div>

        {/* Oxygen Saturation */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-text-primary">Oxygen Saturation (%)</label>
          <input
            type="number"
            placeholder="e.g., 98"
            value={vitalSigns.oxygenSaturation}
            onChange={(e) => handleInputChange('oxygenSaturation', e.target.value)}
            className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <div className={`text-xs px-2 py-1 rounded ${getStatusColor(getVitalStatus('oxygenSaturation', vitalSigns.oxygenSaturation))}`}>
            {getVitalStatus('oxygenSaturation', vitalSigns.oxygenSaturation)} (95-100%)
          </div>
        </div>

        {/* Glucose Level */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-text-primary">Glucose Level (mg/dL)</label>
          <input
            type="number"
            placeholder="e.g., 100"
            value={vitalSigns.glucoseLevel}
            onChange={(e) => handleInputChange('glucoseLevel', e.target.value)}
            className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <div className={`text-xs px-2 py-1 rounded ${getStatusColor(getVitalStatus('glucoseLevel', vitalSigns.glucoseLevel))}`}>
            {getVitalStatus('glucoseLevel', vitalSigns.glucoseLevel)} (70-140 mg/dL)
          </div>
        </div>
      </div>

      {/* Record Button */}
      <div className="pt-4 border-t border-border">
        <Button
          onClick={handleRecordVitals}
          iconName="Save"
          iconPosition="left"
          fullWidth
          disabled={!Object.values(vitalSigns).some(v => 
            typeof v === 'string' ? v !== '' : Object.values(v).some(sv => sv !== '')
          )}
        >
          Record Vital Signs
        </Button>
        <p className="text-xs text-text-secondary mt-2 text-center">
          Vital signs will be saved locally and synced when connectivity is restored.
        </p>
      </div>
    </div>
  );
};

export default VitalSignsLogger;