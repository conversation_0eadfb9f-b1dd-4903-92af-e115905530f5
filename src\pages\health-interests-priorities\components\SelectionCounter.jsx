import React from 'react';

const SelectionCounter = ({ selectedCount, minSelections = 3, maxSelections = 5, selectedLanguage = 'en' }) => {
  const getLocalizedText = (key) => {
    const texts = {
      counter: {
        en: `${selectedCount} of ${maxSelections} selected`,
        tw: `${selectedCount} fi ${maxSelections} a wɔayi`,
        yo: `${selectedCount} lati ${maxSelections} ti a yan`,
        sw: `${selectedCount} kati ya ${maxSelections} zilizochaguliwa`,
        af: `${selectedCount} van ${maxSelections} gekies`
      },
      minimum: {
        en: `Select at least ${minSelections} areas`,
        tw: `Yi ${minSelections} mmeae no sua`,
        yo: `Yan o kere ju ${minSelections} agbegbe`,
        sw: `Chagua angalau maeneo ${minSelections}`,
        af: `Kies ten minste ${minSelections} areas`
      },
      optimal: {
        en: 'Great selection!',
        tw: '<PERSON>yie pa!',
        yo: '<PERSON>yan to dara!',
        sw: '<PERSON><PERSON><PERSON><PERSON> mzuri!',
        af: 'Go<PERSON>e keuse!'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  const isMinimumMet = selectedCount >= minSelections;
  const isOptimal = selectedCount >= minSelections && selectedCount <= maxSelections;

  return (
    <div className="text-center space-y-2">
      {/* Counter */}
      <div className={`
        inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200
        ${isOptimal 
          ? 'bg-success-100 text-success-600' 
          : isMinimumMet 
            ? 'bg-warning-100 text-warning-600' :'bg-secondary-100 text-text-secondary'
        }
      `}>
        {getLocalizedText('counter')}
      </div>

      {/* Status Message */}
      <p className={`
        text-sm transition-colors duration-200
        ${isOptimal 
          ? 'text-success-600' :'text-text-secondary'
        }
      `}>
        {isOptimal ? getLocalizedText('optimal') : getLocalizedText('minimum')}
      </p>
    </div>
  );
};

export default SelectionCounter;