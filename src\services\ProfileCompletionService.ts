/**
 * Profile Completion Service
 * 
 * Handles profile completion tracking and validation for enhanced user profiles
 * with new demographic fields and emergency contact information.
 */

import { supabase } from '../lib/supabase';

export interface ProfileCompletionData {
  userId: string;
  completionPercentage: number;
  sectionProgress: {
    personalInfo: number;
    medicalHistory: number;
    lifestyle: number;
    emergencyInfo: number;
  };
  missingFields: MissingField[];
  criticalMissing: string[];
  lastUpdated: string;
}

export interface MissingField {
  key: string;
  label: string;
  section: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  description?: string;
}

export interface ProfileValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
  completionData: ProfileCompletionData;
}

class ProfileCompletionService {
  private readonly FIELD_DEFINITIONS = {
    // Personal Information Fields
    personalInfo: {
      fullName: { label: 'Full Name', priority: 'critical', weight: 15 },
      email: { label: 'Email Address', priority: 'critical', weight: 15 },
      phoneNumber: { label: 'Phone Number', priority: 'high', weight: 10 },
      dateOfBirth: { label: 'Date of Birth', priority: 'critical', weight: 15 },
      gender: { label: 'Gender', priority: 'high', weight: 10 },
      country: { label: 'Country', priority: 'high', weight: 10 },
      city: { label: 'City', priority: 'medium', weight: 5 },
      occupation: { label: 'Occupation', priority: 'medium', weight: 5 },
      insuranceStatus: { label: 'Insurance Status', priority: 'high', weight: 10 }
    },
    // Emergency Contact Fields
    emergencyInfo: {
      emergencyContactName: { label: 'Emergency Contact Name', priority: 'critical', weight: 30 },
      emergencyContactPhone: { label: 'Emergency Contact Phone', priority: 'critical', weight: 30 },
      emergencyContactRelationship: { label: 'Emergency Contact Relationship', priority: 'high', weight: 20 },
      emergencyContactEmail: { label: 'Emergency Contact Email', priority: 'medium', weight: 20 }
    },
    // Medical History Fields
    medicalHistory: {
      medicalConditions: { label: 'Medical Conditions', priority: 'high', weight: 40 },
      currentMedications: { label: 'Current Medications', priority: 'high', weight: 30 },
      allergies: { label: 'Allergies', priority: 'critical', weight: 30 }
    },
    // Lifestyle Fields
    lifestyle: {
      exerciseFrequency: { label: 'Exercise Frequency', priority: 'medium', weight: 25 },
      sleepHours: { label: 'Sleep Hours', priority: 'medium', weight: 25 },
      stressLevel: { label: 'Stress Level', priority: 'medium', weight: 25 },
      smokingStatus: { label: 'Smoking Status', priority: 'high', weight: 25 }
    }
  };

  /**
   * Calculate comprehensive profile completion
   */
  async calculateProfileCompletion(userId: string): Promise<ProfileCompletionData> {
    try {
      // Load user profile data
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        throw new Error(`Failed to load profile: ${profileError.message}`);
      }

      // Load medical history
      const { data: medicalConditions } = await supabase
        .from('medical_conditions')
        .select('*')
        .eq('user_id', userId);

      const { data: medications } = await supabase
        .from('medications')
        .select('*')
        .eq('user_id', userId);

      // Prepare data for calculation
      const profileData = {
        personalInfo: {
          fullName: profile.full_name,
          email: profile.email,
          phoneNumber: profile.phone,
          dateOfBirth: profile.date_of_birth,
          gender: profile.gender,
          country: profile.country,
          city: profile.city,
          occupation: profile.occupation,
          insuranceStatus: profile.insurance_status
        },
        emergencyInfo: {
          emergencyContactName: profile.emergency_contact?.name,
          emergencyContactPhone: profile.emergency_contact?.phone,
          emergencyContactRelationship: profile.emergency_contact?.relationship,
          emergencyContactEmail: profile.emergency_contact?.email
        },
        medicalHistory: {
          medicalConditions: medicalConditions || [],
          currentMedications: medications?.filter(med => med.is_current) || [],
          allergies: [] // TODO: Add allergies table
        },
        lifestyle: {
          exerciseFrequency: null, // TODO: Add lifestyle fields
          sleepHours: null,
          stressLevel: null,
          smokingStatus: null
        }
      };

      // Calculate section progress
      const sectionProgress = {
        personalInfo: this.calculateSectionProgress('personalInfo', profileData.personalInfo),
        emergencyInfo: this.calculateSectionProgress('emergencyInfo', profileData.emergencyInfo),
        medicalHistory: this.calculateSectionProgress('medicalHistory', profileData.medicalHistory),
        lifestyle: this.calculateSectionProgress('lifestyle', profileData.lifestyle)
      };

      // Calculate overall completion percentage
      const completionPercentage = Math.round(
        (sectionProgress.personalInfo * 0.4) + // 40% weight
        (sectionProgress.emergencyInfo * 0.2) + // 20% weight
        (sectionProgress.medicalHistory * 0.3) + // 30% weight
        (sectionProgress.lifestyle * 0.1) // 10% weight
      );

      // Identify missing fields
      const missingFields = this.identifyMissingFields(profileData);
      const criticalMissing = missingFields
        .filter(field => field.priority === 'critical')
        .map(field => field.key);

      return {
        userId,
        completionPercentage,
        sectionProgress,
        missingFields,
        criticalMissing,
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error calculating profile completion:', error);
      throw error;
    }
  }

  /**
   * Calculate progress for a specific section
   */
  private calculateSectionProgress(section: string, data: any): number {
    const fieldDefs = this.FIELD_DEFINITIONS[section];
    if (!fieldDefs) return 0;

    let totalWeight = 0;
    let completedWeight = 0;

    Object.entries(fieldDefs).forEach(([fieldKey, fieldDef]) => {
      totalWeight += fieldDef.weight;
      
      if (this.isFieldComplete(fieldKey, data[fieldKey], section)) {
        completedWeight += fieldDef.weight;
      }
    });

    return totalWeight > 0 ? Math.round((completedWeight / totalWeight) * 100) : 0;
  }

  /**
   * Check if a field is complete
   */
  private isFieldComplete(fieldKey: string, value: any, section: string): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    // Special handling for array fields
    if (Array.isArray(value)) {
      return value.length > 0;
    }

    // Special handling for medical history
    if (section === 'medicalHistory') {
      if (fieldKey === 'medicalConditions' || fieldKey === 'currentMedications') {
        return Array.isArray(value) && value.length > 0;
      }
    }

    return true;
  }

  /**
   * Identify missing fields across all sections
   */
  private identifyMissingFields(profileData: any): MissingField[] {
    const missingFields: MissingField[] = [];

    Object.entries(this.FIELD_DEFINITIONS).forEach(([section, fieldDefs]) => {
      Object.entries(fieldDefs).forEach(([fieldKey, fieldDef]) => {
        if (!this.isFieldComplete(fieldKey, profileData[section]?.[fieldKey], section)) {
          missingFields.push({
            key: fieldKey,
            label: fieldDef.label,
            section,
            priority: fieldDef.priority,
            description: this.getFieldDescription(fieldKey, section)
          });
        }
      });
    });

    // Sort by priority
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    return missingFields.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
  }

  /**
   * Get field description for user guidance
   */
  private getFieldDescription(fieldKey: string, section: string): string {
    const descriptions: Record<string, string> = {
      country: 'Helps provide region-specific health recommendations',
      city: 'Enables local emergency services and healthcare provider suggestions',
      occupation: 'Allows for occupational health considerations',
      insuranceStatus: 'Helps tailor treatment recommendations based on coverage',
      emergencyContactName: 'Critical for emergency situations and family notifications',
      emergencyContactPhone: 'Essential for emergency contact during medical situations',
      medicalConditions: 'Important for understanding your health history',
      currentMedications: 'Critical for avoiding drug interactions',
      allergies: 'Essential for safe treatment recommendations'
    };

    return descriptions[fieldKey] || 'Helps provide better personalized healthcare';
  }

  /**
   * Validate profile completeness for consultation readiness
   */
  async validateForConsultation(userId: string): Promise<ProfileValidationResult> {
    const completionData = await this.calculateProfileCompletion(userId);
    const errors: Record<string, string> = {};
    const warnings: Record<string, string> = {};

    // Check critical fields
    if (completionData.criticalMissing.length > 0) {
      errors.critical = `Missing critical information: ${completionData.criticalMissing.join(', ')}`;
    }

    // Check minimum completion threshold
    if (completionData.completionPercentage < 50) {
      errors.completion = 'Profile must be at least 50% complete for consultations';
    }

    // Check emergency contact
    if (completionData.sectionProgress.emergencyInfo < 60) {
      warnings.emergency = 'Emergency contact information incomplete';
    }

    // Check medical history
    if (completionData.sectionProgress.medicalHistory < 30) {
      warnings.medical = 'Medical history information would improve consultation quality';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      warnings,
      completionData
    };
  }

  /**
   * Get profile completion summary for dashboard
   */
  async getCompletionSummary(userId: string): Promise<{
    percentage: number;
    nextSteps: string[];
    benefits: string[];
  }> {
    const completionData = await this.calculateProfileCompletion(userId);
    
    const nextSteps = completionData.missingFields
      .filter(field => field.priority === 'critical' || field.priority === 'high')
      .slice(0, 3)
      .map(field => `Add ${field.label.toLowerCase()}`);

    const benefits = [
      'Get personalized health recommendations',
      'Receive region-specific medical guidance',
      'Enable emergency contact notifications',
      'Access specialized consultation features'
    ];

    return {
      percentage: completionData.completionPercentage,
      nextSteps,
      benefits
    };
  }
}

export const profileCompletionService = new ProfileCompletionService();
export default profileCompletionService;
