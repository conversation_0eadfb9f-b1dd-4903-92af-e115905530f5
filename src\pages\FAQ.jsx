import React, { useState } from "react";
import { Link } from "react-router-dom";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";

const FAQ = () => {
  const [openSection, setOpenSection] = useState(null);

  const toggleSection = (index) => {
    setOpenSection(openSection === index ? null : index);
  };

  const faqSections = [
    {
      category: "Getting Started",
      questions: [
        {
          question: "What is VoiceHealth AI?",
          answer: "VoiceHealth AI is an AI-powered voice consultation platform that provides instant health guidance, intelligent triage, and emergency response. You can speak naturally about your health concerns and receive personalized medical insights in your preferred language."
        },
        {
          question: "How do I start using VoiceHealth AI?",
          answer: "Simply visit our platform, create an account, and start your first voice consultation. You can speak in English or any of our 12+ supported African languages. Our AI will guide you through the process and provide health insights based on your concerns."
        },
        {
          question: "Is VoiceHealth AI free to use?",
          answer: "We offer a free tier with limited consultations per month. For unlimited access and premium features like emergency response integration and detailed health reports, we offer affordable subscription plans starting from $9.99/month."
        },
        {
          question: "What languages does VoiceHealth AI support?",
          answer: "We support 12+ languages including English, Swahili, French, Arabic, Hausa, Yoruba, Igbo, Amharic, Oromo, and more. Our AI is trained to understand cultural context and regional health practices for each language."
        }
      ]
    },
    {
      category: "Medical Services",
      questions: [
        {
          question: "Can VoiceHealth AI diagnose medical conditions?",
          answer: "No, VoiceHealth AI does not provide medical diagnoses. We provide health insights, symptom assessment, and triage recommendations to help you understand when to seek professional medical care. Always consult with qualified healthcare providers for medical diagnoses and treatment."
        },
        {
          question: "What types of health concerns can I discuss?",
          answer: "You can discuss a wide range of health concerns including symptoms, medication questions, preventive care, mental health, nutrition, and general wellness. Our AI is trained on diverse medical knowledge and can provide guidance on most common health topics."
        },
        {
          question: "How accurate are the AI recommendations?",
          answer: "Our AI is trained on extensive medical literature and validated by healthcare professionals. However, AI recommendations should supplement, not replace, professional medical advice. We continuously improve our accuracy through machine learning and expert feedback."
        },
        {
          question: "What happens in a medical emergency?",
          answer: "If our AI detects signs of a medical emergency, it will immediately recommend contacting emergency services and can help connect you to local emergency numbers. Our emergency protocols are designed to respond within 2 seconds and prioritize your safety."
        }
      ]
    },
    {
      category: "Privacy & Security",
      questions: [
        {
          question: "Is my health information secure?",
          answer: "Yes, we use AES-256 encryption for all data at rest and in transit. We are HIPAA compliant and follow strict security protocols. Your voice recordings and health information are stored securely and never shared without your explicit consent."
        },
        {
          question: "Who can access my health data?",
          answer: "Only you and authorized healthcare providers (with your consent) can access your health data. Our AI processes your information to provide recommendations, but human staff cannot access your personal health information without proper authorization."
        },
        {
          question: "Can I delete my health data?",
          answer: "Yes, you have the right to request deletion of your health data. You can do this through your account settings or by contacting our privacy team. Note that some data may be retained for legal compliance or emergency response purposes."
        },
        {
          question: "Do you share data with third parties?",
          answer: "We do not sell or share your personal health information with third parties for marketing purposes. We may share de-identified, aggregated data for medical research with appropriate ethical approvals. All third-party vendors sign HIPAA-compliant agreements."
        }
      ]
    },
    {
      category: "Technical Support",
      questions: [
        {
          question: "What devices can I use VoiceHealth AI on?",
          answer: "VoiceHealth AI works on smartphones, tablets, and computers with internet access. Our platform is optimized for mobile devices and works offline for basic consultations. We support iOS, Android, and web browsers."
        },
        {
          question: "What if I have poor internet connection?",
          answer: "Our platform includes offline capabilities for basic consultations. Critical health information is cached locally, and consultations sync when you reconnect. Emergency features work even with limited connectivity."
        },
        {
          question: "How do I improve voice recognition accuracy?",
          answer: "Speak clearly in a quiet environment, use your preferred language, and ensure your microphone is working properly. Our AI learns from your speech patterns over time to improve accuracy. You can also type if voice recognition isn't working well."
        },
        {
          question: "What if the AI doesn't understand my concern?",
          answer: "You can rephrase your question, switch to typing, or use our guided symptom checker. If the AI still can't help, you'll be connected to our support team or directed to appropriate healthcare resources."
        }
      ]
    },
    {
      category: "Billing & Subscriptions",
      questions: [
        {
          question: "How does billing work?",
          answer: "We offer monthly and annual subscription plans. Billing is automatic and secure through our payment partners. You can view your billing history and manage your subscription in your account settings."
        },
        {
          question: "Can I cancel my subscription anytime?",
          answer: "Yes, you can cancel your subscription at any time through your account settings. Your access continues until the end of your current billing period. No cancellation fees apply."
        },
        {
          question: "Do you offer refunds?",
          answer: "We offer full refunds within 30 days of purchase for unused services. Emergency consultations and completed services are non-refundable. Contact our support team for refund requests."
        },
        {
          question: "Are there discounts for healthcare workers?",
          answer: "Yes, we offer special pricing for healthcare workers, students, and non-profit organizations. Contact our sales team with verification of your status to learn about available discounts."
        }
      ]
    }
  ];

  return (
    <>
      <Navbar />
      <main className="bg-white">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
          <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl sm:text-5xl font-extrabold mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl max-w-3xl mx-auto mb-8 opacity-90">
              Find answers to common questions about VoiceHealth AI, our services, and how to get the most out of your healthcare experience.
            </p>
            <div className="max-w-md mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  className="w-full px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Links */}
        <section className="py-8 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-wrap justify-center gap-4">
              {faqSections.map((section, index) => (
                <button
                  key={index}
                  onClick={() => {
                    const element = document.getElementById(`section-${index}`);
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="px-4 py-2 bg-white text-primary-600 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 text-sm font-medium"
                >
                  {section.category}
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {faqSections.map((section, sectionIndex) => (
              <div key={sectionIndex} id={`section-${sectionIndex}`} className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 pb-2 border-b border-gray-200">
                  {section.category}
                </h2>
                <div className="space-y-4">
                  {section.questions.map((faq, questionIndex) => {
                    const index = `${sectionIndex}-${questionIndex}`;
                    const isOpen = openSection === index;
                    
                    return (
                      <div key={questionIndex} className="bg-white border border-gray-200 rounded-lg shadow-sm">
                        <button
                          onClick={() => toggleSection(index)}
                          className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset"
                        >
                          <span className="text-lg font-medium text-gray-900">{faq.question}</span>
                          <svg
                            className={`w-5 h-5 text-gray-500 transform transition-transform duration-200 ${
                              isOpen ? 'rotate-180' : ''
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                        {isOpen && (
                          <div className="px-6 pb-4">
                            <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Emergency Information */}
        <section className="bg-red-50 py-12">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-red-100 border-l-4 border-red-400 p-6 rounded-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-red-800 mb-2">Medical Emergency?</h3>
                  <p className="text-red-700 mb-4">
                    If you're experiencing a medical emergency, don't wait for AI assistance. Contact emergency services immediately:
                  </p>
                  <ul className="text-red-700 space-y-1">
                    <li><strong>International Emergency:</strong> 112</li>
                    <li><strong>US/Canada:</strong> 911</li>
                    <li><strong>UK:</strong> 999</li>
                    <li><strong>South Africa:</strong> 10177</li>
                    <li><strong>Kenya:</strong> 999 or 112</li>
                    <li><strong>Nigeria:</strong> 199 or 112</li>
                  </ul>
                  <div className="mt-4">
                    <Link
                      to="/emergency-info"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                    >
                      View Emergency Information
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Still Have Questions?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Our support team is here to help you get the most out of VoiceHealth AI.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                <p className="text-gray-600 mb-4">Get detailed help via email</p>
                <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700 font-medium">
                  <EMAIL>
                </a>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Chat</h3>
                <p className="text-gray-600 mb-4">Chat with our support team</p>
                <button className="text-primary-600 hover:text-primary-700 font-medium">
                  Start Chat
                </button>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Contact Form</h3>
                <p className="text-gray-600 mb-4">Send us a detailed message</p>
                <Link to="/contact" className="text-primary-600 hover:text-primary-700 font-medium">
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default FAQ;
