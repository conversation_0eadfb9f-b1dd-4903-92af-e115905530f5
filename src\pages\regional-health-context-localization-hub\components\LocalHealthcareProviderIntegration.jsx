import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const LocalHealthcareProviderIntegration = ({ country, onIntegrationUpdate }) => {
  const [activeSection, setActiveSection] = useState('providers');
  const [integrationStatus, setIntegrationStatus] = useState({});

  const sections = [
    { id: 'providers', label: 'Healthcare Providers', icon: 'Building' },
    { id: 'referrals', label: 'Referral Networks', icon: 'ArrowRight' },
    { id: 'insurance', label: 'Insurance Systems', icon: 'CreditCard' },
    { id: 'compliance', label: 'Regulatory Compliance', icon: 'Shield' }
  ];

  // Mock data for healthcare providers
  const healthcareProviders = {
    publicHospitals: [
      {
        name: country?.id === 'ghana' ? 'Korle-Bu Teaching Hospital' :
              country?.id === 'nigeria' ? 'Lagos University Teaching Hospital' :
              country?.id === 'kenya' ? 'Kenyatta National Hospital' : 'Chris <PERSON>th Hospital',
        type: 'Teaching Hospital',
        location: country?.id === 'ghana' ? 'Accra' :
                 country?.id === 'nigeria' ? 'Lagos' :
                 country?.id === 'kenya' ? 'Nairobi' : 'Johannesburg',
        specialties: ['Emergency Medicine', 'Internal Medicine', 'Surgery', 'Pediatrics'],
        status: 'active',
        apiIntegrated: true,
        waitTime: '2-4 hours',
        contact: country?.id === 'ghana' ? '+233-302-674858' :
                country?.id === 'nigeria' ? '+234-1-7740564' :
                country?.id === 'kenya' ? '+254-20-2726300' : '+27-11-933-0111'
      },
      {
        name: country?.id === 'ghana' ? 'Tema General Hospital' :
              country?.id === 'nigeria' ? 'University College Hospital' :
              country?.id === 'kenya' ? 'Moi Teaching Hospital' : 'Groote Schuur Hospital',
        type: 'General Hospital',
        location: country?.id === 'ghana' ? 'Tema' :
                 country?.id === 'nigeria' ? 'Ibadan' :
                 country?.id === 'kenya' ? 'Eldoret' : 'Cape Town',
        specialties: ['General Medicine', 'Maternity', 'Emergency'],
        status: 'active',
        apiIntegrated: false,
        waitTime: '1-3 hours',
        contact: country?.id === 'ghana' ? '+233-303-202701' :
                country?.id === 'nigeria' ? '+234-22-413876' :
                country?.id === 'kenya' ? '+254-53-203300' : '+27-21-404-9111'
      }
    ],
    privateClinics: [
      {
        name: country?.id === 'ghana' ? 'Trust Hospital' :
              country?.id === 'nigeria' ? 'The Medical Centre' :
              country?.id === 'kenya' ? 'Nairobi Hospital' : 'Netcare Milpark Hospital',
        type: 'Private Hospital',
        location: country?.id === 'ghana' ? 'Accra' :
                 country?.id === 'nigeria' ? 'Lagos' :
                 country?.id === 'kenya' ? 'Nairobi' : 'Johannesburg',
        specialties: ['Cardiology', 'Oncology', 'Orthopedics', 'Dermatology'],
        status: 'active',
        apiIntegrated: true,
        waitTime: '30-60 minutes',
        contact: country?.id === 'ghana' ? '+233-302-763777' :
                country?.id === 'nigeria' ? '+234-1-2719000' :
                country?.id === 'kenya' ? '+254-20-2845000' : '+27-11-480-7111'
      }
    ],
    communityHealthCenters: [
      {
        name: 'Primary Health Center',
        type: 'Community Health Center',
        count: country?.healthcareAccess?.primaryHealthCenters,
        coverage: country?.healthcareAccess?.ruralCoverage,
        services: ['Basic consultation', 'Immunizations', 'Maternal health', 'Health education'],
        status: 'network',
        apiIntegrated: false
      }
    ]
  };

  const insuranceSystems = {
    ghana: {
      primary: 'National Health Insurance Scheme (NHIS)',
      coverage: '40% of population',
      benefits: ['Outpatient care', 'Inpatient care', 'Emergency care', 'Maternity care'],
      limitations: ['Dental care excluded', 'Some medications not covered'],
      registrationProcess: 'Visit NHIS office with ID and passport photos'
    },
    nigeria: {
      primary: 'National Health Insurance Authority (NHIA)',
      coverage: '12% of population',
      benefits: ['Basic healthcare', 'Emergency care', 'Maternal health'],
      limitations: ['Limited provider network', 'Co-payment required'],
      registrationProcess: 'Online registration or visit NHIA office'
    },
    kenya: {
      primary: 'National Hospital Insurance Fund (NHIF)',
      coverage: '20% of population',
      benefits: ['Inpatient care', 'Outpatient care', 'Emergency services'],
      limitations: ['Limited specialized care', 'Provider restrictions'],
      registrationProcess: 'Employer deduction or self-registration'
    },
    'south-africa': {
      primary: 'National Health Insurance (NHI) - In Development',
      coverage: '16% with medical aid',
      benefits: ['Comprehensive healthcare', 'Chronic medication', 'Specialist care'],
      limitations: ['Still being implemented', 'Private healthcare expensive'],
      registrationProcess: 'Universal coverage planned'
    }
  };

  const regulatoryCompliance = {
    dataProtection: {
      ghana: 'Data Protection Act, 2012',
      nigeria: 'Nigeria Data Protection Regulation (NDPR)',
      kenya: 'Data Protection Act, 2019',
      'south-africa': 'Protection of Personal Information Act (POPIA)'
    },
    medicalPractice: {
      ghana: 'Medical and Dental Council of Ghana',
      nigeria: 'Medical and Dental Council of Nigeria',
      kenya: 'Kenya Medical Practitioners and Dentists Council',
      'south-africa': 'Health Professions Council of South Africa'
    },
    telemedicine: {
      ghana: 'Limited regulation - developing framework',
      nigeria: 'Guidelines for Telemedicine Practice',
      kenya: 'Kenya Telemedicine Guidelines',
      'south-africa': 'HPCSA Guidelines for Telemedicine'
    }
  };

  const handleProviderIntegration = (provider, integrate) => {
    const newStatus = { ...integrationStatus };
    newStatus[provider.name] = integrate ? 'integrating' : 'disconnected';
    setIntegrationStatus(newStatus);
    
    // Simulate integration process
    setTimeout(() => {
      const updatedStatus = { ...newStatus };
      updatedStatus[provider.name] = integrate ? 'integrated' : 'disconnected';
      setIntegrationStatus(updatedStatus);
      onIntegrationUpdate?.(updatedStatus);
    }, 2000);
  };

  const getIntegrationStatus = (providerName) => {
    return integrationStatus[providerName] || 'disconnected';
  };

  const renderSection = () => {
    switch (activeSection) {
      case 'providers':
        return (
          <div className="space-y-6">
            {/* Public Hospitals */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Public Hospitals & Teaching Institutions</h4>
              <div className="space-y-4">
                {healthcareProviders.publicHospitals.map((hospital, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Icon name="Building" size={16} className="text-primary-500" />
                          <h5 className="font-medium text-text-primary">{hospital.name}</h5>
                          {hospital.apiIntegrated && (
                            <span className="text-xs bg-success-100 text-success-700 px-2 py-0.5 rounded-full">
                              API Ready
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-text-secondary mb-2">
                          {hospital.type} • {hospital.location}
                        </p>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {hospital.specialties.map((specialty, idx) => (
                            <span key={idx} className="text-xs bg-info-100 text-info-700 px-2 py-1 rounded">
                              {specialty}
                            </span>
                          ))}
                        </div>
                        <div className="text-sm text-text-secondary">
                          <p>Wait Time: {hospital.waitTime}</p>
                          <p>Contact: {hospital.contact}</p>
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-2 ml-4">
                        <div className={`flex items-center gap-1 text-sm ${
                          getIntegrationStatus(hospital.name) === 'integrated' ? 'text-success-600' :
                          getIntegrationStatus(hospital.name) === 'integrating'? 'text-warning-600' : 'text-text-secondary'
                        }`}>
                          <div className={`w-2 h-2 rounded-full ${
                            getIntegrationStatus(hospital.name) === 'integrated' ? 'bg-success-500' :
                            getIntegrationStatus(hospital.name) === 'integrating'? 'bg-warning-500 animate-pulse' : 'bg-text-secondary'
                          }`}></div>
                          <span className="capitalize">{getIntegrationStatus(hospital.name)}</span>
                        </div>
                        
                        <Button
                          variant={getIntegrationStatus(hospital.name) === 'integrated' ? 'outline' : 'primary'}
                          size="sm"
                          onClick={() => handleProviderIntegration(
                            hospital, 
                            getIntegrationStatus(hospital.name) !== 'integrated'
                          )}
                          disabled={getIntegrationStatus(hospital.name) === 'integrating'}
                          iconName={
                            getIntegrationStatus(hospital.name) === 'integrated' ? 'Unlink' :
                            getIntegrationStatus(hospital.name) === 'integrating' ? 'Loader' : 'Link'
                          }
                        >
                          {getIntegrationStatus(hospital.name) === 'integrated' ? 'Disconnect' :
                           getIntegrationStatus(hospital.name) === 'integrating' ? 'Connecting...' : 'Connect'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Private Clinics */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Private Healthcare Providers</h4>
              <div className="space-y-4">
                {healthcareProviders.privateClinics.map((clinic, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Icon name="Heart" size={16} className="text-accent-500" />
                          <h5 className="font-medium text-text-primary">{clinic.name}</h5>
                          {clinic.apiIntegrated && (
                            <span className="text-xs bg-success-100 text-success-700 px-2 py-0.5 rounded-full">
                              API Ready
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-text-secondary mb-2">
                          {clinic.type} • {clinic.location}
                        </p>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {clinic.specialties.map((specialty, idx) => (
                            <span key={idx} className="text-xs bg-accent-100 text-accent-700 px-2 py-1 rounded">
                              {specialty}
                            </span>
                          ))}
                        </div>
                        <div className="text-sm text-text-secondary">
                          <p>Wait Time: {clinic.waitTime}</p>
                          <p>Contact: {clinic.contact}</p>
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-2 ml-4">
                        <div className={`flex items-center gap-1 text-sm ${
                          getIntegrationStatus(clinic.name) === 'integrated' ? 'text-success-600' :
                          getIntegrationStatus(clinic.name) === 'integrating'? 'text-warning-600' : 'text-text-secondary'
                        }`}>
                          <div className={`w-2 h-2 rounded-full ${
                            getIntegrationStatus(clinic.name) === 'integrated' ? 'bg-success-500' :
                            getIntegrationStatus(clinic.name) === 'integrating'? 'bg-warning-500 animate-pulse' : 'bg-text-secondary'
                          }`}></div>
                          <span className="capitalize">{getIntegrationStatus(clinic.name)}</span>
                        </div>
                        
                        <Button
                          variant={getIntegrationStatus(clinic.name) === 'integrated' ? 'outline' : 'primary'}
                          size="sm"
                          onClick={() => handleProviderIntegration(
                            clinic, 
                            getIntegrationStatus(clinic.name) !== 'integrated'
                          )}
                          disabled={getIntegrationStatus(clinic.name) === 'integrating'}
                          iconName={
                            getIntegrationStatus(clinic.name) === 'integrated' ? 'Unlink' :
                            getIntegrationStatus(clinic.name) === 'integrating' ? 'Loader' : 'Link'
                          }
                        >
                          {getIntegrationStatus(clinic.name) === 'integrated' ? 'Disconnect' :
                           getIntegrationStatus(clinic.name) === 'integrating' ? 'Connecting...' : 'Connect'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Community Health Centers Network */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Community Health Centers Network</h4>
              {healthcareProviders.communityHealthCenters.map((network, index) => (
                <div key={index} className="p-4 bg-success-50 border border-success-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Icon name="Users" size={16} className="text-success-600" />
                      <h5 className="font-medium text-success-800">Community Health Network</h5>
                    </div>
                    <span className="text-xs bg-success-100 text-success-700 px-2 py-1 rounded-full">
                      Network Integration
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div>
                      <p className="text-sm text-success-600">Centers</p>
                      <p className="font-semibold text-success-800">
                        {network.count?.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-success-600">Rural Coverage</p>
                      <p className="font-semibold text-success-800">{network.coverage}</p>
                    </div>
                    <div>
                      <p className="text-sm text-success-600">Status</p>
                      <p className="font-semibold text-success-800 capitalize">{network.status}</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm text-success-600 mb-2">Available Services:</p>
                    <div className="flex flex-wrap gap-2">
                      {network.services.map((service, idx) => (
                        <span key={idx} className="text-xs bg-success-100 text-success-700 px-2 py-1 rounded">
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'referrals':
        return (
          <div className="space-y-6">
            {/* Referral Network Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                {
                  title: 'Active Referral Partners',
                  value: '24',
                  icon: 'Building',
                  color: 'primary'
                },
                {
                  title: 'Avg. Referral Time',
                  value: '2.5 days',
                  icon: 'Clock',
                  color: 'success'
                },
                {
                  title: 'Success Rate',
                  value: '87%',
                  icon: 'TrendingUp',
                  color: 'info'
                }
              ].map((stat, index) => (
                <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-text-secondary">{stat.title}</p>
                      <p className="text-2xl font-semibold text-text-primary">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-lg bg-${stat.color}-100`}>
                      <Icon name={stat.icon} size={24} className={`text-${stat.color}-500`} />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Referral Pathways */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Referral Pathways</h4>
              <div className="space-y-4">
                {[
                  {
                    from: 'AI Consultation',
                    to: 'Primary Care Physician',
                    condition: 'Requires physical examination',
                    priority: 'Standard',
                    timeframe: '1-3 days'
                  },
                  {
                    from: 'Primary Care',
                    to: 'Specialist Consultation',
                    condition: 'Complex conditions requiring specialist',
                    priority: 'High',
                    timeframe: '3-7 days'
                  },
                  {
                    from: 'AI Triage',
                    to: 'Emergency Services',
                    condition: 'Critical symptoms detected',
                    priority: 'Urgent',
                    timeframe: 'Immediate'
                  },
                  {
                    from: 'General Consultation',
                    to: 'Mental Health Services',
                    condition: 'Psychological support needed',
                    priority: 'Standard',
                    timeframe: '2-5 days'
                  }
                ].map((pathway, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded">
                            {pathway.from}
                          </span>
                          <Icon name="ArrowRight" size={16} className="text-text-secondary" />
                          <span className="px-2 py-1 bg-success-100 text-success-700 text-xs rounded">
                            {pathway.to}
                          </span>
                        </div>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        pathway.priority === 'Urgent' ? 'bg-error-100 text-error-700' :
                        pathway.priority === 'High'? 'bg-warning-100 text-warning-700' : 'bg-info-100 text-info-700'
                      }`}>
                        {pathway.priority}
                      </span>
                    </div>
                    <p className="text-sm text-text-secondary mb-1">{pathway.condition}</p>
                    <p className="text-xs text-text-secondary">Expected timeframe: {pathway.timeframe}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Referral Templates */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Automated Referral Templates</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  {
                    specialty: 'Cardiology',
                    template: 'Chest pain evaluation template',
                    fields: ['Symptoms duration', 'Risk factors', 'Previous cardiac history'],
                    usage: '12 times this month'
                  },
                  {
                    specialty: 'Dermatology',
                    template: 'Skin condition referral template',
                    fields: ['Lesion description', 'Location', 'Duration', 'Associated symptoms'],
                    usage: '8 times this month'
                  },
                  {
                    specialty: 'Mental Health',
                    template: 'Psychological evaluation template',
                    fields: ['Mood assessment', 'Sleep patterns', 'Social functioning'],
                    usage: '15 times this month'
                  },
                  {
                    specialty: 'Pediatrics',
                    template: 'Child health referral template',
                    fields: ['Growth parameters', 'Developmental milestones', 'Immunization status'],
                    usage: '6 times this month'
                  }
                ].map((template, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-text-primary">{template.specialty}</h5>
                      <span className="text-xs text-text-secondary">{template.usage}</span>
                    </div>
                    <p className="text-sm text-text-secondary mb-3">{template.template}</p>
                    <div className="space-y-1">
                      <p className="text-xs text-text-secondary font-medium">Required Fields:</p>
                      <div className="flex flex-wrap gap-1">
                        {template.fields.map((field, idx) => (
                          <span key={idx} className="text-xs bg-accent-100 text-accent-700 px-2 py-0.5 rounded">
                            {field}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'insurance':
        return (
          <div className="space-y-6">
            {/* Primary Insurance System */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Primary Insurance System</h4>
              <div className="p-4 bg-surface border border-border rounded-lg">
                {(() => {
                  const insurance = insuranceSystems[country?.id];
                  return (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <Icon name="CreditCard" size={20} className="text-primary-500" />
                        <h5 className="font-semibold text-text-primary">{insurance?.primary}</h5>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-text-secondary">Population Coverage</p>
                          <p className="font-medium text-text-primary">{insurance?.coverage}</p>
                        </div>
                        <div>
                          <p className="text-sm text-text-secondary">Registration Process</p>
                          <p className="font-medium text-text-primary">{insurance?.registrationProcess}</p>
                        </div>
                      </div>

                      <div className="mb-4">
                        <p className="text-sm text-text-secondary font-medium mb-2">Covered Benefits:</p>
                        <div className="flex flex-wrap gap-2">
                          {insurance?.benefits?.map((benefit, idx) => (
                            <span key={idx} className="text-xs bg-success-100 text-success-700 px-2 py-1 rounded">
                              {benefit}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-text-secondary font-medium mb-2">Limitations:</p>
                        <div className="space-y-1">
                          {insurance?.limitations?.map((limitation, idx) => (
                            <div key={idx} className="flex items-center gap-2">
                              <Icon name="AlertTriangle" size={12} className="text-warning-500" />
                              <span className="text-xs text-warning-700">{limitation}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>

            {/* Insurance Verification Integration */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Insurance Verification Integration</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  {
                    system: 'Real-time Eligibility Check',
                    status: 'In Development',
                    description: 'Verify patient insurance coverage in real-time',
                    features: ['Coverage verification', 'Benefit details', 'Co-payment amounts']
                  },
                  {
                    system: 'Pre-authorization System',
                    status: 'Planning Phase',
                    description: 'Automated pre-authorization for specialist referrals',
                    features: ['Automated requests', 'Status tracking', 'Appeal management']
                  }
                ].map((integration, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-text-primary">{integration.system}</h5>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        integration.status === 'Active' ? 'bg-success-100 text-success-700' :
                        integration.status === 'In Development'? 'bg-warning-100 text-warning-700' : 'bg-info-100 text-info-700'
                      }`}>
                        {integration.status}
                      </span>
                    </div>
                    <p className="text-sm text-text-secondary mb-3">{integration.description}</p>
                    <div className="space-y-1">
                      <p className="text-xs text-text-secondary font-medium">Features:</p>
                      {integration.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-primary-500 rounded-full"></div>
                          <span className="text-xs text-text-primary">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payment Integration */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Payment Processing Integration</h4>
              <div className="space-y-3">
                {[
                  {
                    method: 'Mobile Money',
                    providers: country?.id === 'ghana' ? ['MTN Mobile Money', 'Vodafone Cash'] :
                             country?.id === 'nigeria' ? ['Paga', 'OPay', 'PalmPay'] :
                             country?.id === 'kenya' ? ['M-Pesa', 'Airtel Money'] : 
                             ['FNB eWallet', 'Capitec Pay'],
                    status: 'Integrated',
                    usage: '75%'
                  },
                  {
                    method: 'Bank Transfer',
                    providers: ['Direct bank transfer', 'Online banking'],
                    status: 'Integrated',
                    usage: '15%'
                  },
                  {
                    method: 'Cash Payment',
                    providers: ['At healthcare facilities', 'Through agents'],
                    status: 'Supported',
                    usage: '10%'
                  }
                ].map((payment, index) => (
                  <div key={index} className="p-3 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Icon name="CreditCard" size={16} className="text-success-500" />
                        <h5 className="font-medium text-text-primary">{payment.method}</h5>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="text-sm text-text-secondary">{payment.usage} usage</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          payment.status === 'Integrated' ? 'bg-success-100 text-success-700' : 'bg-info-100 text-info-700'
                        }`}>
                          {payment.status}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {payment.providers.map((provider, idx) => (
                        <span key={idx} className="text-xs bg-accent-100 text-accent-700 px-2 py-0.5 rounded">
                          {provider}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'compliance':
        return (
          <div className="space-y-6">
            {/* Regulatory Framework */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Regulatory Compliance Framework</h4>
              <div className="space-y-4">
                {[
                  {
                    category: 'Data Protection',
                    regulation: regulatoryCompliance.dataProtection[country?.id],
                    status: 'Compliant',
                    requirements: [
                      'Patient consent for data processing',
                      'Data encryption and security measures',
                      'Right to data portability and erasure',
                      'Regular privacy impact assessments'
                    ]
                  },
                  {
                    category: 'Medical Practice',
                    regulation: regulatoryCompliance.medicalPractice[country?.id],
                    status: 'Under Review',
                    requirements: [
                      'AI system medical oversight',
                      'Professional liability considerations',
                      'Continuous medical education integration',
                      'Quality assurance protocols'
                    ]
                  },
                  {
                    category: 'Telemedicine',
                    regulation: regulatoryCompliance.telemedicine[country?.id],
                    status: 'Partially Compliant',
                    requirements: [
                      'Licensed practitioner oversight',
                      'Patient identity verification',
                      'Medical record keeping standards',
                      'Emergency protocol compliance'
                    ]
                  }
                ].map((compliance, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h5 className="font-medium text-text-primary">{compliance.category}</h5>
                        <p className="text-sm text-text-secondary">{compliance.regulation}</p>
                      </div>
                      <span className={`text-xs px-3 py-1 rounded-full ${
                        compliance.status === 'Compliant' ? 'bg-success-100 text-success-700' :
                        compliance.status === 'Under Review'? 'bg-warning-100 text-warning-700' : 'bg-info-100 text-info-700'
                      }`}>
                        {compliance.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm text-text-secondary font-medium">Key Requirements:</p>
                      {compliance.requirements.map((requirement, idx) => (
                        <div key={idx} className="flex items-start gap-2">
                          <div className={`w-2 h-2 rounded-full mt-1.5 ${
                            compliance.status === 'Compliant' ? 'bg-success-500' :
                            compliance.status === 'Under Review'? 'bg-warning-500' : 'bg-info-500'
                          }`}></div>
                          <span className="text-sm text-text-primary">{requirement}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Certification Status */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Certification & Accreditation Status</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  {
                    certification: 'ISO 27001 (Information Security)',
                    status: 'Certified',
                    validUntil: '2025-12-31',
                    scope: 'Healthcare data processing and storage'
                  },
                  {
                    certification: 'ISO 13485 (Medical Devices)',
                    status: 'In Progress',
                    validUntil: 'TBD',
                    scope: 'AI diagnostic assistance systems'
                  },
                  {
                    certification: 'HL7 FHIR Compliance',
                    status: 'Certified',
                    validUntil: 'Ongoing',
                    scope: 'Healthcare data interoperability'
                  },
                  {
                    certification: 'Local Medical AI Approval',
                    status: 'Pending',
                    validUntil: 'TBD',
                    scope: 'AI-assisted medical consultations'
                  }
                ].map((cert, index) => (
                  <div key={index} className="p-4 bg-surface border border-border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-text-primary">{cert.certification}</h5>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        cert.status === 'Certified' ? 'bg-success-100 text-success-700' :
                        cert.status === 'In Progress'? 'bg-warning-100 text-warning-700' : 'bg-info-100 text-info-700'
                      }`}>
                        {cert.status}
                      </span>
                    </div>
                    <p className="text-sm text-text-secondary mb-1">{cert.scope}</p>
                    {cert.validUntil !== 'TBD' && cert.validUntil !== 'Ongoing' && (
                      <p className="text-xs text-text-secondary">Valid until: {cert.validUntil}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Audit & Monitoring */}
            <div>
              <h4 className="font-medium text-text-primary mb-4">Audit & Continuous Monitoring</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  {
                    aspect: 'Data Access Logs',
                    frequency: 'Real-time',
                    lastAudit: '2024-01-15',
                    status: 'Normal'
                  },
                  {
                    aspect: 'AI Decision Auditing',
                    frequency: 'Daily',
                    lastAudit: '2024-01-20',
                    status: 'Normal'
                  },
                  {
                    aspect: 'Compliance Review',
                    frequency: 'Monthly',
                    lastAudit: '2024-01-01',
                    status: 'Scheduled'
                  },
                  {
                    aspect: 'Security Assessment',
                    frequency: 'Quarterly',
                    lastAudit: '2023-12-15',
                    status: 'Due Soon'
                  },
                  {
                    aspect: 'Medical Oversight',
                    frequency: 'Weekly',
                    lastAudit: '2024-01-18',
                    status: 'Normal'
                  },
                  {
                    aspect: 'Patient Feedback Review',
                    frequency: 'Bi-weekly',
                    lastAudit: '2024-01-10',
                    status: 'Action Required'
                  }
                ].map((audit, index) => (
                  <div key={index} className="p-3 bg-surface border border-border rounded-lg">
                    <h5 className="font-medium text-text-primary mb-1">{audit.aspect}</h5>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-text-secondary">Frequency:</span>
                        <span className="text-text-primary">{audit.frequency}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text-secondary">Last Audit:</span>
                        <span className="text-text-primary">{audit.lastAudit}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text-secondary">Status:</span>
                        <span className={`${
                          audit.status === 'Normal' ? 'text-success-600' :
                          audit.status === 'Scheduled' ? 'text-info-600' :
                          audit.status === 'Due Soon'? 'text-warning-600' : 'text-error-600'
                        }`}>
                          {audit.status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-2xl">{country?.flag}</span>
          <div>
            <h2 className="text-xl font-semibold text-text-primary font-heading">
              Healthcare Provider Integration - {country?.name}
            </h2>
            <p className="text-text-secondary">
              Manage local healthcare provider networks, referral systems, and regulatory compliance
            </p>
          </div>
        </div>
        
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => {}}
            iconName="Download"
          >
            Export Config
          </Button>
          
          <Button
            variant="primary"
            onClick={() => onIntegrationUpdate?.(integrationStatus)}
            iconName="Save"
          >
            Save Integration
          </Button>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="border-b border-border">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 ${
                activeSection === section.id
                  ? 'border-primary-500 text-primary-600' :'border-transparent text-text-secondary hover:text-text-primary hover:border-border'
              }`}
            >
              <Icon name={section.icon} size={16} />
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="bg-surface border border-border rounded-lg p-6">
        {renderSection()}
      </div>
    </div>
  );
};

export default LocalHealthcareProviderIntegration;