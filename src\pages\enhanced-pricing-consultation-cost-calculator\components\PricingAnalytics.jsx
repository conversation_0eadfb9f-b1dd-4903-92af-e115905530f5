import React, { useState } from 'react';
import { Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Download, RefreshCw, Eye, AlertCircle } from 'lucide-react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';


const PricingAnalytics = ({ currency }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('3months');
  const [selectedView, setSelectedView] = useState('overview');

  // Mock analytics data
  const spendingTrends = [
    { month: 'Oct', amount: currency === 'USD' ? 45 : 22500, consultations: 8, avgCost: currency === 'USD' ? 5.6 : 2800 },
    { month: 'Nov', amount: currency === 'USD' ? 62 : 31000, consultations: 11, avgCost: currency === 'USD' ? 5.6 : 2800 },
    { month: 'Dec', amount: currency === 'USD' ? 78 : 39000, consultations: 14, avgCost: currency === 'USD' ? 5.6 : 2800 },
    { month: 'Jan', amount: currency === 'USD' ? 85 : 42500, consultations: 16, avgCost: currency === 'USD' ? 5.3 : 2650 }
  ];

  const consultationBreakdown = [
    { type: 'General', count: 12, cost: currency === 'USD' ? 60 : 30000, color: '#3B82F6' },
    { type: 'Specialist', count: 8, cost: currency === 'USD' ? 120 : 60000, color: '#8B5CF6' },
    { type: 'Emergency', count: 3, cost: currency === 'USD' ? 60 : 30000, color: '#EF4444' },
    { type: 'Mental Health', count: 5, cost: currency === 'USD' ? 45 : 22500, color: '#10B981' },
    { type: 'Pediatric', count: 4, cost: currency === 'USD' ? 28 : 14000, color: '#F59E0B' }
  ];

  const familySpending = [
    { name: 'John Doe', amount: currency === 'USD' ? 125 : 62500, percentage: 35, consultations: 12 },
    { name: 'Jane Doe', amount: currency === 'USD' ? 98 : 49000, percentage: 28, consultations: 9 },
    { name: 'Teen Doe', amount: currency === 'USD' ? 67 : 33500, percentage: 20, consultations: 8 },
    { name: 'Child Doe', amount: currency === 'USD' ? 45 : 22500, percentage: 17, consultations: 5 }
  ];

  const savingsData = {
    familyDiscount: currency === 'USD' ? 67 : 33500,
    bulkPurchase: currency === 'USD' ? 23 : 11500,
    loyaltyBonus: currency === 'USD' ? 15 : 7500,
    totalSavings: currency === 'USD' ? 105 : 52500,
    potentialSavings: currency === 'USD' ? 45 : 22500
  };

  const formatPrice = (price, currency) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price);
  };

  const costOptimizationTips = [
    {
      title: 'Consider Family Plan Upgrade',
      description: 'Upgrading to Family Premium could save you up to 25% on consultations',
      savings: currency === 'USD' ? 30 : 15000,
      priority: 'high'
    },
    {
      title: 'Use Scheduled Consultations',
      description: 'Scheduled consultations are 15% cheaper than emergency consultations',
      savings: currency === 'USD' ? 18 : 9000,
      priority: 'medium'
    },
    {
      title: 'Bundle Premium Features',
      description: 'Get premium feature bundles instead of individual add-ons',
      savings: currency === 'USD' ? 12 : 6000,
      priority: 'low'
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Spent (Last 3 months)</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPrice(spendingTrends.reduce((sum, item) => sum + item.amount, 0), currency)}
              </p>
            </div>
            <DollarSign className="text-green-500" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <TrendingUp size={14} className="text-green-500 mr-1" />
            <span className="text-sm text-green-600">+23% from last period</span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg. Cost per Consultation</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPrice(
                  spendingTrends.reduce((sum, item) => sum + item.amount, 0) / 
                  spendingTrends.reduce((sum, item) => sum + item.consultations, 0), 
                  currency
                )}
              </p>
            </div>
            <TrendingDown className="text-blue-500" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <TrendingDown size={14} className="text-green-500 mr-1" />
            <span className="text-sm text-green-600">-8% cost optimization</span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Savings</p>
              <p className="text-2xl font-bold text-green-600">
                {formatPrice(savingsData.totalSavings, currency)}
              </p>
            </div>
            <TrendingUp className="text-green-500" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-sm text-gray-600">Family discount: 20%</span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Potential Savings</p>
              <p className="text-2xl font-bold text-orange-600">
                {formatPrice(savingsData.potentialSavings, currency)}
              </p>
            </div>
            <AlertCircle className="text-orange-500" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-sm text-orange-600">Available optimizations</span>
          </div>
        </div>
      </div>

      {/* Spending Trends Chart */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Spending Trends</h3>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Download size={14} className="mr-1" />
              Export
            </Button>
          </div>
        </div>
        
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={spendingTrends}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip formatter={(value) => formatPrice(value, currency)} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="amount" 
              stroke="#3B82F6" 
              strokeWidth={3}
              name="Amount Spent"
            />
            <Bar dataKey="consultations" fill="#10B981" name="Consultations" />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Cost Optimization Tips */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Cost Optimization Recommendations
        </h3>
        
        <div className="space-y-4">
          {costOptimizationTips.map((tip, index) => (
            <div key={index} className={`p-4 rounded-lg border-l-4 ${
              tip.priority === 'high' ? 'bg-red-50 border-red-400' :
              tip.priority === 'medium'? 'bg-yellow-50 border-yellow-400' : 'bg-green-50 border-green-400'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">{tip.title}</h4>
                  <p className="text-sm text-gray-600 mb-2">{tip.description}</p>
                  <span className={`text-sm font-medium ${
                    tip.priority === 'high' ? 'text-red-700' :
                    tip.priority === 'medium'? 'text-yellow-700' : 'text-green-700'
                  }`}>
                    Potential savings: {formatPrice(tip.savings, currency)}
                  </span>
                </div>
                <Button variant="outline" size="sm">
                  Apply
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderBreakdown = () => (
    <div className="space-y-6">
      {/* Consultation Type Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Spending by Consultation Type
          </h3>
          
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={consultationBreakdown}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="cost"
              >
                {consultationBreakdown.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatPrice(value, currency)} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Consultation Details
          </h3>
          
          <div className="space-y-3">
            {consultationBreakdown.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-gray-100 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <div>
                    <span className="font-medium text-gray-900">{item.type}</span>
                    <p className="text-sm text-gray-600">{item.count} consultations</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="font-semibold text-gray-900">
                    {formatPrice(item.cost, currency)}
                  </span>
                  <p className="text-sm text-gray-600">
                    {formatPrice(item.cost / item.count, currency)}/session
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Family Member Spending */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Family Member Spending
        </h3>
        
        <div className="space-y-4">
          {familySpending.map((member, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-gray-100 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-medium">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{member.name}</h4>
                  <p className="text-sm text-gray-600">{member.consultations} consultations</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="w-32">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Usage</span>
                    <span>{member.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${member.percentage}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-right">
                  <span className="font-semibold text-gray-900">
                    {formatPrice(member.amount, currency)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const tabOptions = [
    { id: 'overview', label: 'Overview', icon: TrendingUp },
    { id: 'breakdown', label: 'Detailed Breakdown', icon: Eye }
  ];

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Pricing Analytics</h2>
          <p className="text-gray-600">Track your spending and optimize costs</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1month">Last Month</option>
            <option value="3months">Last 3 Months</option>
            <option value="6months">Last 6 Months</option>
            <option value="1year">Last Year</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw size={14} className="mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabOptions.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setSelectedView(tab.id)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  selectedView === tab.id
                    ? 'border-blue-500 text-blue-600' :'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      {selectedView === 'overview' && renderOverview()}
      {selectedView === 'breakdown' && renderBreakdown()}
    </div>
  );
};

export default PricingAnalytics;