import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import triageService from '../../../utils/triageService';

const EmergencyContactIntegration = ({ userProfile, systemConfig }) => {
  const [emergencyContacts, setEmergencyContacts] = useState({});
  const [localHealthcareProviders, setLocalHealthcareProviders] = useState([]);
  const [personalContacts, setPersonalContacts] = useState([]);
  const [locationSharing, setLocationSharing] = useState({
    enabled: false,
    accuracy: 'high',
    shareWithEmergencyServices: true
  });
  const [testCallStatus, setTestCallStatus] = useState('');

  useEffect(() => {
    // Load emergency contacts based on user's country
    const contacts = triageService.getEmergencyContacts(userProfile?.country);
    setEmergencyContacts(contacts);
    
    // Load personal contacts from local storage
    const savedPersonalContacts = JSON.parse(localStorage.getItem('personalEmergencyContacts') || '[]');
    setPersonalContacts(savedPersonalContacts);
    
    // Load healthcare providers based on region
    loadLocalHealthcareProviders();
    
    // Load location sharing preferences
    const savedLocationSettings = JSON.parse(localStorage.getItem('locationSharingSettings') || '{}');
    setLocationSharing(prev => ({ ...prev, ...savedLocationSettings }));
  }, [userProfile]);

  const loadLocalHealthcareProviders = () => {
    // Simulate loading local healthcare providers based on country
    const providers = {
      ghana: [
        { name: 'Korle-Bu Teaching Hospital', phone: '+233 30 2665401', type: 'hospital', address: 'Accra' },
        { name: 'Komfo Anokye Teaching Hospital', phone: '+233 32 2022308', type: 'hospital', address: 'Kumasi' },
        { name: 'Ridge Hospital', phone: '+233 30 2231631', type: 'hospital', address: 'Accra' },
        { name: 'National Ambulance Service', phone: '193', type: 'ambulance', address: 'Nationwide' }
      ],
      nigeria: [
        { name: 'Lagos University Teaching Hospital', phone: '+234 1 7747614', type: 'hospital', address: 'Lagos' },
        { name: 'University College Hospital', phone: '+234 2 2410089', type: 'hospital', address: 'Ibadan' },
        { name: 'National Hospital Abuja', phone: '+234 9 4613686', type: 'hospital', address: 'Abuja' },
        { name: 'Lagos State Ambulance Service', phone: '199', type: 'ambulance', address: 'Lagos State' }
      ],
      kenya: [
        { name: 'Kenyatta National Hospital', phone: '+254 20 2726300', type: 'hospital', address: 'Nairobi' },
        { name: 'Moi Teaching Hospital', phone: '+254 53 2033471', type: 'hospital', address: 'Eldoret' },
        { name: 'Aga Khan Hospital', phone: '+254 20 3662000', type: 'hospital', address: 'Nairobi' },
        { name: 'St. John Ambulance', phone: '+254 20 2210000', type: 'ambulance', address: 'Nationwide' }
      ],
      south_africa: [
        { name: 'Chris Hani Baragwanath Hospital', phone: '+27 11 933 0000', type: 'hospital', address: 'Johannesburg' },
        { name: 'Groote Schuur Hospital', phone: '+27 21 404 9111', type: 'hospital', address: 'Cape Town' },
        { name: 'Charlotte Maxeke Hospital', phone: '+27 11 488 4911', type: 'hospital', address: 'Johannesburg' },
        { name: 'ER24 Ambulance', phone: '+27 84 124', type: 'ambulance', address: 'Nationwide' }
      ]
    };
    
    const countryProviders = providers[userProfile?.country?.toLowerCase()] || providers.ghana;
    setLocalHealthcareProviders(countryProviders);
  };

  const handleEmergencyCall = (phoneNumber, contactName, contactType) => {
    // Log the call attempt
    const callLog = {
      timestamp: new Date().toISOString(),
      contactName,
      phoneNumber,
      contactType,
      userLocation: locationSharing.enabled ? 'Location shared' : 'Location not shared'
    };
    
    const existingLogs = JSON.parse(localStorage.getItem('emergencyCallLogs') || '[]');
    localStorage.setItem('emergencyCallLogs', JSON.stringify([callLog, ...existingLogs]));
    
    // Share location if enabled
    if (locationSharing.enabled && locationSharing.shareWithEmergencyServices) {
      shareLocationWithEmergencyServices();
    }
    
    // Initiate the call
    window.location.href = `tel:${phoneNumber}`;
  };

  const shareLocationWithEmergencyServices = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          };
          
          // Store location for emergency services
          localStorage.setItem('lastEmergencyLocation', JSON.stringify(location));
          
          // In a real implementation, this would send location to emergency services
          console.log('Location shared with emergency services:', location);
          alert(`Location shared: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`);
        },
        (error) => {
          console.error('Location sharing failed:', error);
          alert('Failed to share location. Please share your location manually when calling emergency services.');
        },
        {
          enableHighAccuracy: locationSharing.accuracy === 'high',
          timeout: 10000,
          maximumAge: 30000
        }
      );
    }
  };

  const handleTestCall = async (phoneNumber, contactName) => {
    setTestCallStatus('testing');
    
    // Simulate a test of the emergency system
    setTimeout(() => {
      setTestCallStatus('success');
      alert(`Test completed for ${contactName}. Emergency contact system is functional.`);
      setTimeout(() => setTestCallStatus(''), 2000);
    }, 2000);
  };

  const handleAddPersonalContact = () => {
    const name = prompt('Enter contact name:');
    const phone = prompt('Enter phone number:');
    const relationship = prompt('Enter relationship (e.g., spouse, parent, doctor):');
    
    if (name && phone) {
      const newContact = {
        id: Math.random().toString(36).substr(2, 9),
        name,
        phone,
        relationship: relationship || 'Emergency Contact',
        dateAdded: new Date().toISOString()
      };
      
      const updatedContacts = [...personalContacts, newContact];
      setPersonalContacts(updatedContacts);
      localStorage.setItem('personalEmergencyContacts', JSON.stringify(updatedContacts));
    }
  };

  const handleRemovePersonalContact = (contactId) => {
    const updatedContacts = personalContacts.filter(contact => contact.id !== contactId);
    setPersonalContacts(updatedContacts);
    localStorage.setItem('personalEmergencyContacts', JSON.stringify(updatedContacts));
  };

  const handleLocationSharingChange = (setting, value) => {
    const updatedSettings = { ...locationSharing, [setting]: value };
    setLocationSharing(updatedSettings);
    localStorage.setItem('locationSharingSettings', JSON.stringify(updatedSettings));
  };

  const getContactTypeIcon = (type) => {
    const icons = {
      emergency: 'AlertTriangle',
      hospital: 'Building',
      ambulance: 'Truck',
      police: 'Shield',
      fire: 'Flame',
      personal: 'User'
    };
    return icons[type] || 'Phone';
  };

  const getContactTypeColor = (type) => {
    const colors = {
      emergency: 'red',
      hospital: 'blue',
      ambulance: 'green',
      police: 'indigo',
      fire: 'orange',
      personal: 'purple'
    };
    return colors[type] || 'gray';
  };

  return (
    <div className="space-y-6">
      
      {/* Integration Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">
            Emergency Contact Integration
          </h3>
          <p className="text-text-secondary">
            Local healthcare facilities, ambulance services, and crisis intervention teams
          </p>
        </div>
        
        <Button
          onClick={handleAddPersonalContact}
          iconName="Plus"
          iconPosition="left"
          size="sm"
        >
          Add Personal Contact
        </Button>
      </div>

      {/* Location Sharing Settings */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h4 className="font-semibold text-text-primary mb-4 flex items-center">
          <Icon name="MapPin" size={18} className="mr-2" />
          Automatic Location Sharing
        </h4>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm font-medium text-text-secondary">Enable Location Sharing</span>
              <p className="text-xs text-text-secondary">Automatically share your location during emergency calls</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={locationSharing.enabled}
                onChange={(e) => handleLocationSharingChange('enabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          {locationSharing.enabled && (
            <>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Location Accuracy
                </label>
                <select
                  value={locationSharing.accuracy}
                  onChange={(e) => handleLocationSharingChange('accuracy', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-md bg-background"
                >
                  <option value="high">High Accuracy (GPS)</option>
                  <option value="medium">Medium Accuracy (Network)</option>
                  <option value="low">Low Accuracy (Coarse)</option>
                </select>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-text-secondary">Share with Emergency Services</span>
                  <p className="text-xs text-text-secondary">Include location data when calling emergency numbers</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={locationSharing.shareWithEmergencyServices}
                    onChange={(e) => handleLocationSharingChange('shareWithEmergencyServices', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Government Emergency Services */}
        <div className="bg-surface rounded-lg border border-border p-6">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="Shield" size={18} className="mr-2" />
            Government Emergency Services - {userProfile?.country || 'Ghana'}
          </h4>
          
          <div className="space-y-3">
            {Object.entries(emergencyContacts).map(([type, number]) => {
              const color = getContactTypeColor(type);
              const icon = getContactTypeIcon(type);
              
              return (
                <div key={type} className={`bg-${color}-50 border border-${color}-200 rounded-lg p-4`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`bg-${color}-600 text-white rounded-full p-2 mr-3`}>
                        <Icon name={icon} size={16} />
                      </div>
                      <div>
                        <h5 className={`font-medium text-${color}-800 capitalize`}>
                          {type.replace('_', ' ')}
                        </h5>
                        <p className={`text-lg font-mono text-${color}-700`}>{number}</p>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestCall(number, type)}
                        loading={testCallStatus === 'testing'}
                        iconName="TestTube"
                        className={`border-${color}-300 text-${color}-700 hover:bg-${color}-100`}
                      >
                        Test
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleEmergencyCall(number, type, 'emergency')}
                        iconName="Phone"
                        className={`bg-${color}-600 hover:bg-${color}-700`}
                      >
                        Call
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Personal Emergency Contacts */}
        <div className="bg-surface rounded-lg border border-border p-6">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="Users" size={18} className="mr-2" />
            Personal Emergency Contacts
          </h4>
          
          {personalContacts.length === 0 ? (
            <div className="text-center py-8">
              <Icon name="UserPlus" size={48} className="text-gray-400 mx-auto mb-4" />
              <p className="text-text-secondary mb-4">No personal emergency contacts added</p>
              <Button
                onClick={handleAddPersonalContact}
                iconName="Plus"
                iconPosition="left"
                size="sm"
              >
                Add First Contact
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {personalContacts.map(contact => (
                <div key={contact.id} className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-purple-600 text-white rounded-full p-2 mr-3">
                        <Icon name="User" size={16} />
                      </div>
                      <div>
                        <h5 className="font-medium text-purple-800">{contact.name}</h5>
                        <p className="text-sm text-purple-600">{contact.relationship}</p>
                        <p className="text-lg font-mono text-purple-700">{contact.phone}</p>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemovePersonalContact(contact.id)}
                        iconName="Trash2"
                        className="border-red-300 text-red-700 hover:bg-red-100"
                      >
                        Remove
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleEmergencyCall(contact.phone, contact.name, 'personal')}
                        iconName="Phone"
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        Call
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

      </div>

      {/* Local Healthcare Providers */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h4 className="font-semibold text-text-primary mb-4 flex items-center">
          <Icon name="Building" size={18} className="mr-2" />
          Local Healthcare Providers
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {localHealthcareProviders.map((provider, index) => {
            const color = getContactTypeColor(provider.type);
            const icon = getContactTypeIcon(provider.type);
            
            return (
              <div key={index} className={`bg-${color}-50 border border-${color}-200 rounded-lg p-4`}>
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start">
                    <div className={`bg-${color}-600 text-white rounded-full p-2 mr-3 mt-1`}>
                      <Icon name={icon} size={16} />
                    </div>
                    <div>
                      <h5 className={`font-medium text-${color}-800`}>{provider.name}</h5>
                      <p className={`text-sm text-${color}-600 capitalize`}>{provider.type}</p>
                      <p className={`text-xs text-${color}-600`}>{provider.address}</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <p className={`text-sm font-mono text-${color}-700`}>{provider.phone}</p>
                  <Button
                    size="sm"
                    onClick={() => handleEmergencyCall(provider.phone, provider.name, provider.type)}
                    iconName="Phone"
                    className={`bg-${color}-600 hover:bg-${color}-700`}
                  >
                    Call
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Emergency Call History */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h4 className="font-semibold text-text-primary mb-4 flex items-center">
          <Icon name="History" size={18} className="mr-2" />
          Recent Emergency Calls
        </h4>
        
        {(() => {
          const callLogs = JSON.parse(localStorage.getItem('emergencyCallLogs') || '[]');
          
          if (callLogs.length === 0) {
            return (
              <div className="text-center py-8">
                <Icon name="Phone" size={48} className="text-gray-400 mx-auto mb-4" />
                <p className="text-text-secondary">No emergency calls made yet</p>
              </div>
            );
          }
          
          return (
            <div className="space-y-2">
              {callLogs.slice(0, 5).map((log, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-surface-secondary rounded-lg">
                  <div className="flex items-center">
                    <Icon name="Phone" size={16} className="mr-3 text-text-secondary" />
                    <div>
                      <span className="text-sm font-medium text-text-primary">{log.contactName}</span>
                      <p className="text-xs text-text-secondary">
                        {log.phoneNumber} • {log.contactType} • {log.userLocation}
                      </p>
                    </div>
                  </div>
                  <span className="text-xs text-text-secondary">
                    {new Date(log.timestamp).toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          );
        })()}
      </div>

    </div>
  );
};

export default EmergencyContactIntegration;