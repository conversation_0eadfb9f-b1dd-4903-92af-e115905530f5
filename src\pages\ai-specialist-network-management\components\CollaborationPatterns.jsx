import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CollaborationPatterns = ({ agents, collaborationData }) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('today');
  const [selectedAgent, setSelectedAgent] = useState(null);

  // Mock collaboration data
  const collaborationMetrics = {
    today: {
      totalCollaborations: 47,
      successRate: 94,
      averageCollaborationTime: '3.2m',
      patterns: [
        {
          agentIds: ['general-practitioner', 'dietician'],
          count: 12,
          successRate: 96,
          avgTime: '2.8m',
          commonCases: ['Diabetes management', 'Weight loss', 'Nutrition planning']
        },
        {
          agentIds: ['general-practitioner', 'clinical-psychologist'],
          count: 15,
          successRate: 92,
          avgTime: '4.1m',
          commonCases: ['Anxiety with physical symptoms', 'Depression screening', 'Stress management']
        },
        {
          agentIds: ['paediatrician', 'clinical-psychologist'],
          count: 8,
          successRate: 95,
          avgTime: '3.5m',
          commonCases: ['Child behavioral issues', 'School anxiety', 'Developmental concerns']
        },
        {
          agentIds: ['general-practitioner', 'dermatologist'],
          count: 7,
          successRate: 89,
          avgTime: '2.1m',
          commonCases: ['Skin condition diagnosis', 'Rash evaluation', 'Acne treatment']
        },
        {
          agentIds: ['dietician', 'clinical-psychologist'],
          count: 5,
          successRate: 98,
          avgTime: '3.8m',
          commonCases: ['Eating disorders', 'Emotional eating', 'Body image issues']
        }
      ]
    }
  };

  const getAgentByIds = (agentIds) => {
    return agentIds.map(id => agents.find(agent => agent.id === id)).filter(Boolean);
  };

  const getCollaborationStrength = (count) => {
    if (count >= 10) return { level: 'Strong', color: 'text-success-600', bg: 'bg-success-100' };
    if (count >= 5) return { level: 'Moderate', color: 'text-primary-600', bg: 'bg-primary-100' };
    return { level: 'Light', color: 'text-warning-600', bg: 'bg-warning-100' };
  };

  const currentMetrics = collaborationMetrics[selectedTimeframe];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-text-primary font-heading">Inter-Agent Collaboration Patterns</h2>
          <p className="text-text-secondary mt-1">
            Analysis of specialist coordination and multi-agent consultation patterns
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => window.history.back()}
          iconName="ArrowLeft"
        >
          Back to Overview
        </Button>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex gap-2">
          {[
            { id: 'today', label: 'Today' },
            { id: 'week', label: 'This Week' },
            { id: 'month', label: 'This Month' }
          ].map((timeframe) => (
            <button
              key={timeframe.id}
              onClick={() => setSelectedTimeframe(timeframe.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedTimeframe === timeframe.id
                  ? 'bg-primary-500 text-white' :'bg-surface border border-border text-text-primary hover:bg-primary-50'
              }`}
            >
              {timeframe.label}
            </button>
          ))}
        </div>

        <div className="flex items-center gap-4 text-sm text-text-secondary">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-success-500"></div>
            <span>High Success Rate (95%+)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-warning-500"></div>
            <span>Moderate Success Rate (90-94%)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-error-500"></div>
            <span>Needs Improvement (&lt;90%)</span>
          </div>
        </div>
      </div>

      {/* Collaboration Overview */}
      <div className="bg-gradient-to-r from-primary-50 to-accent-50 rounded-xl p-6 border border-primary-200">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Collaboration Overview</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600">{currentMetrics.totalCollaborations}</div>
            <div className="text-sm text-text-secondary">Total Collaborations</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-success-600">{currentMetrics.successRate}%</div>
            <div className="text-sm text-text-secondary">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-warning-600">{currentMetrics.averageCollaborationTime}</div>
            <div className="text-sm text-text-secondary">Avg Duration</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent-600">{currentMetrics.patterns.length}</div>
            <div className="text-sm text-text-secondary">Active Patterns</div>
          </div>
        </div>
      </div>

      {/* Collaboration Patterns */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Active Collaboration Patterns</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {currentMetrics.patterns.map((pattern, index) => {
            const collaboratingAgents = getAgentByIds(pattern.agentIds);
            const strength = getCollaborationStrength(pattern.count);
            const successRateColor = pattern.successRate >= 95 ? 'text-success-600' : 
                                   pattern.successRate >= 90 ? 'text-warning-600' : 'text-error-600';
            
            return (
              <div
                key={index}
                className="bg-surface border border-border rounded-xl p-6 hover:shadow-medium transition-all"
              >
                {/* Pattern Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="flex -space-x-2">
                      {collaboratingAgents.map((agent, agentIndex) => (
                        <div
                          key={agent.id}
                          className="w-10 h-10 rounded-full bg-primary-50 border-2 border-white flex items-center justify-center text-lg"
                        >
                          {agent.avatar}
                        </div>
                      ))}
                    </div>
                    <div>
                      <h4 className="font-semibold text-text-primary">
                        {collaboratingAgents.map(agent => agent.shortName).join(' + ')}
                      </h4>
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${strength.bg} ${strength.color}`}>
                          {strength.level} Bond
                        </span>
                        <span className="text-sm text-text-secondary">{pattern.count} collaborations</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-xl font-bold ${successRateColor}`}>{pattern.successRate}%</div>
                    <div className="text-xs text-text-secondary">Success Rate</div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className="text-lg font-bold text-text-primary">{pattern.count}</div>
                    <div className="text-xs text-text-secondary">Cases</div>
                  </div>
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className="text-lg font-bold text-text-primary">{pattern.avgTime}</div>
                    <div className="text-xs text-text-secondary">Avg Time</div>
                  </div>
                  <div className="text-center p-3 bg-background rounded-lg">
                    <div className={`text-lg font-bold ${successRateColor}`}>{pattern.successRate}%</div>
                    <div className="text-xs text-text-secondary">Success</div>
                  </div>
                </div>

                {/* Common Cases */}
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-text-primary mb-2">Common Cases</h5>
                  <div className="flex flex-wrap gap-2">
                    {pattern.commonCases.map((caseType, caseIndex) => (
                      <span 
                        key={caseIndex}
                        className="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full"
                      >
                        {caseType}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Collaboration Flow Visualization */}
                <div className="mb-4">
                  <h5 className="text-sm font-medium text-text-primary mb-2">Collaboration Flow</h5>
                  <div className="flex items-center justify-center gap-2">
                    {collaboratingAgents.map((agent, agentIndex) => (
                      <React.Fragment key={agent.id}>
                        <div className="flex flex-col items-center">
                          <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-sm">
                            {agent.avatar}
                          </div>
                          <span className="text-xs text-text-secondary mt-1">{agent.shortName}</span>
                        </div>
                        {agentIndex < collaboratingAgents.length - 1 && (
                          <div className="flex items-center">
                            <Icon name="ArrowRight" size={16} className="text-primary-500" />
                          </div>
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    iconName="BarChart3"
                    className="flex-1"
                  >
                    View Details
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    iconName="Settings"
                    className="flex-1"
                  >
                    Optimize
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Network Collaboration Map */}
      <div className="bg-surface border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Network Collaboration Map</h3>
        <div className="flex items-center justify-center py-8">
          <div className="relative">
            {/* Central Hub */}
            <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
              Network<br/>Hub
            </div>

            {/* Agent Nodes */}
            {agents.map((agent, index) => {
              const angle = (index * 72) - 90; // 360/5 = 72 degrees between each agent
              const radius = 120;
              const x = Math.cos(angle * Math.PI / 180) * radius;
              const y = Math.sin(angle * Math.PI / 180) * radius;
              
              return (
                <div
                  key={agent.id}
                  className="absolute w-16 h-16 bg-surface border-2 border-primary-300 rounded-full flex items-center justify-center transform -translate-x-1/2 -translate-y-1/2 cursor-pointer hover:scale-110 transition-all"
                  style={{
                    left: `calc(50% + ${x}px)`,
                    top: `calc(50% + ${y}px)`
                  }}
                  onClick={() => setSelectedAgent(agent)}
                >
                  <div className="text-center">
                    <div className="text-lg">{agent.avatar}</div>
                    <div className="text-xs font-medium text-text-primary">{agent.shortName}</div>
                  </div>
                </div>
              );
            })}

            {/* Collaboration Lines */}
            {currentMetrics.patterns.map((pattern, index) => {
              const agentIndices = pattern.agentIds.map(id => agents.findIndex(agent => agent.id === id));
              if (agentIndices.includes(-1)) return null;

              const [agent1Index, agent2Index] = agentIndices;
              const angle1 = (agent1Index * 72) - 90;
              const angle2 = (agent2Index * 72) - 90;
              const radius = 120;
              
              const x1 = Math.cos(angle1 * Math.PI / 180) * radius;
              const y1 = Math.sin(angle1 * Math.PI / 180) * radius;
              const x2 = Math.cos(angle2 * Math.PI / 180) * radius;
              const y2 = Math.sin(angle2 * Math.PI / 180) * radius;
              
              const lineLength = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
              const lineAngle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
              
              const strength = getCollaborationStrength(pattern.count);
              const strokeWidth = pattern.count >= 10 ? 3 : pattern.count >= 5 ? 2 : 1;
              
              return (
                <div
                  key={index}
                  className="absolute border-t border-primary-300 opacity-60"
                  style={{
                    width: `${lineLength}px`,
                    left: `calc(50% + ${x1}px)`,
                    top: `calc(50% + ${y1}px)`,
                    transformOrigin: '0 0',
                    transform: `rotate(${lineAngle}deg)`,
                    borderWidth: `${strokeWidth}px`
                  }}
                />
              );
            })}
          </div>
        </div>
        
        <div className="text-center text-sm text-text-secondary">
          Interactive collaboration network showing specialist relationships and consultation patterns
        </div>
      </div>
    </div>
  );
};

export default CollaborationPatterns;