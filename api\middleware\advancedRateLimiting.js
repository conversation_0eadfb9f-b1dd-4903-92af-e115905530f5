/**
 * ADVANCED RATE LIMITING MIDDLEWARE FOR MEDICAL API
 * 
 * This middleware provides sophisticated rate limiting with medical-specific
 * considerations, emergency bypass mechanisms, and DDoS protection while
 * maintaining HIPAA compliance and audit logging.
 * 
 * SECURITY FEATURES:
 * - Progressive rate limiting based on operation criticality
 * - Emergency bypass for critical medical operations
 * - IP-based DDoS protection with geographic analysis
 * - User-based rate limiting with role considerations
 * - Comprehensive audit logging for security monitoring
 * - Automatic threat detection and response
 * 
 * MEDICAL REQUIREMENTS:
 * - Emergency medical operations must never be blocked
 * - Healthcare providers get higher limits than patients
 * - Critical medical data operations have priority
 * - All rate limiting events are logged for compliance
 * - Graceful degradation for legitimate medical use
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Rate limiting configurations by operation type
const RATE_LIMITS = {
  // Emergency operations - very high limits
  emergency: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 1000,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    bypassable: false // Emergency operations are never limited
  },
  
  // Critical medical operations - high limits
  critical_medical: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    bypassable: true
  },
  
  // Standard medical operations - normal limits
  medical: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    bypassable: true
  },
  
  // Authentication operations - moderate limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    bypassable: false
  },
  
  // General API operations - standard limits
  general: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    bypassable: true
  },
  
  // Public/search operations - lower limits
  public: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    bypassable: false
  }
};

// Role-based multipliers
const ROLE_MULTIPLIERS = {
  'emergency_responder': 5.0,
  'admin': 3.0,
  'healthcare_provider': 2.0,
  'patient': 1.0
};

// DDoS protection thresholds
const DDOS_THRESHOLDS = {
  requests_per_second: 50,
  requests_per_minute: 1000,
  unique_endpoints_per_minute: 20,
  failed_requests_ratio: 0.8
};

// In-memory stores (in production, use Redis)
const requestCounts = new Map();
const ddosDetection = new Map();
const emergencyBypasses = new Map();

/**
 * Determine operation type from request
 */
function getOperationType(req) {
  const { path, method } = req;
  
  // Emergency operations
  if (path.includes('/emergency') || 
      path.includes('/critical') ||
      req.headers['x-emergency-override'] === 'true') {
    return 'emergency';
  }
  
  // Critical medical operations
  if ((path.includes('/medical') || path.includes('/consultation')) && 
      (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    return 'critical_medical';
  }
  
  // Medical data operations
  if (path.includes('/medical') || 
      path.includes('/consultation') || 
      path.includes('/patient')) {
    return 'medical';
  }
  
  // Authentication operations
  if (path.includes('/auth') || path.includes('/login') || path.includes('/register')) {
    return 'auth';
  }
  
  // Public operations
  if (path.includes('/public') || path.includes('/search') || path.includes('/health')) {
    return 'public';
  }
  
  return 'general';
}

/**
 * Get rate limit configuration for user
 */
function getRateLimitConfig(operationType, userRole) {
  const baseConfig = RATE_LIMITS[operationType];
  const multiplier = ROLE_MULTIPLIERS[userRole] || 1.0;
  
  return {
    ...baseConfig,
    maxRequests: Math.floor(baseConfig.maxRequests * multiplier)
  };
}

/**
 * Generate rate limiting key
 */
function generateRateLimitKey(identifier, operationType, windowStart) {
  return `ratelimit:${identifier}:${operationType}:${windowStart}`;
}

/**
 * Check if emergency bypass is active
 */
function checkEmergencyBypass(userId, operationType) {
  const bypassKey = `${userId}:${operationType}`;
  const bypass = emergencyBypasses.get(bypassKey);
  
  if (bypass && bypass.expiresAt > Date.now()) {
    return true;
  }
  
  // Clean up expired bypass
  if (bypass && bypass.expiresAt <= Date.now()) {
    emergencyBypasses.delete(bypassKey);
  }
  
  return false;
}

/**
 * Activate emergency bypass
 */
async function activateEmergencyBypass(userId, operationType, justification, duration = 3600000) {
  const bypassKey = `${userId}:${operationType}`;
  const expiresAt = Date.now() + duration;
  
  emergencyBypasses.set(bypassKey, {
    userId,
    operationType,
    justification,
    activatedAt: Date.now(),
    expiresAt
  });
  
  // Log emergency bypass activation
  await logSecurityEvent('emergency_bypass_activated', {
    user_id: userId,
    operation_type: operationType,
    justification,
    duration_ms: duration,
    expires_at: new Date(expiresAt).toISOString()
  });
}

/**
 * Detect DDoS patterns
 */
function detectDDoS(clientIp) {
  const now = Date.now();
  const oneSecondAgo = now - 1000;
  const oneMinuteAgo = now - 60000;
  
  let detection = ddosDetection.get(clientIp);
  if (!detection) {
    detection = {
      requests: [],
      endpoints: new Set(),
      failedRequests: 0,
      totalRequests: 0
    };
    ddosDetection.set(clientIp, detection);
  }
  
  // Clean old requests
  detection.requests = detection.requests.filter(timestamp => timestamp > oneMinuteAgo);
  
  // Add current request
  detection.requests.push(now);
  detection.totalRequests++;
  
  // Check thresholds
  const requestsLastSecond = detection.requests.filter(timestamp => timestamp > oneSecondAgo).length;
  const requestsLastMinute = detection.requests.length;
  const failedRatio = detection.totalRequests > 0 ? detection.failedRequests / detection.totalRequests : 0;
  
  const isDDoS = requestsLastSecond > DDOS_THRESHOLDS.requests_per_second ||
                 requestsLastMinute > DDOS_THRESHOLDS.requests_per_minute ||
                 detection.endpoints.size > DDOS_THRESHOLDS.unique_endpoints_per_minute ||
                 failedRatio > DDOS_THRESHOLDS.failed_requests_ratio;
  
  if (isDDoS) {
    logSecurityEvent('ddos_detected', {
      client_ip: clientIp,
      requests_per_second: requestsLastSecond,
      requests_per_minute: requestsLastMinute,
      unique_endpoints: detection.endpoints.size,
      failed_ratio: failedRatio
    });
  }
  
  return isDDoS;
}

/**
 * Log security events
 */
async function logSecurityEvent(eventType, details) {
  try {
    await supabase
      .from('security_logs')
      .insert({
        id: crypto.randomUUID(),
        event_type: eventType,
        details,
        timestamp: new Date().toISOString(),
        severity: eventType.includes('ddos') || eventType.includes('attack') ? 'high' : 'medium'
      });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
}

/**
 * Advanced rate limiting middleware
 */
const advancedRateLimiting = (options = {}) => {
  const {
    enableDDoSProtection = true,
    enableEmergencyBypass = true,
    enableGeoBlocking = false,
    blockedCountries = []
  } = options;

  return async (req, res, next) => {
    try {
      const clientIp = req.ip || req.connection.remoteAddress;
      const userAgent = req.headers['user-agent'] || '';
      const operationType = getOperationType(req);
      const user = req.user;
      const userRole = user?.user_metadata?.role || 'patient';
      const userId = user?.id || 'anonymous';
      
      // Get identifier for rate limiting (prefer user ID, fallback to IP)
      const identifier = userId !== 'anonymous' ? userId : clientIp;
      
      // Check for DDoS patterns
      if (enableDDoSProtection) {
        const detection = ddosDetection.get(clientIp);
        if (detection) {
          detection.endpoints.add(req.path);
        }
        
        if (detectDDoS(clientIp)) {
          await logSecurityEvent('rate_limit_ddos_block', {
            client_ip: clientIp,
            user_id: userId,
            operation_type: operationType,
            user_agent: userAgent,
            path: req.path
          });
          
          return res.status(429).json({
            success: false,
            error: 'Too many requests detected. Please try again later.',
            retryAfter: 300 // 5 minutes
          });
        }
      }
      
      // Check emergency bypass
      if (enableEmergencyBypass && checkEmergencyBypass(userId, operationType)) {
        req.emergencyBypass = true;
        return next();
      }
      
      // Get rate limit configuration
      const config = getRateLimitConfig(operationType, userRole);
      
      // Skip rate limiting for emergency operations
      if (operationType === 'emergency') {
        return next();
      }
      
      // Calculate window
      const now = Date.now();
      const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
      const windowEnd = windowStart + config.windowMs;
      
      // Generate rate limit key
      const rateLimitKey = generateRateLimitKey(identifier, operationType, windowStart);
      
      // Get current count
      let requestData = requestCounts.get(rateLimitKey);
      if (!requestData) {
        requestData = {
          count: 0,
          windowStart,
          windowEnd,
          firstRequest: now
        };
        requestCounts.set(rateLimitKey, requestData);
      }
      
      // Clean up expired entries
      if (now > requestData.windowEnd) {
        requestCounts.delete(rateLimitKey);
        requestData = {
          count: 0,
          windowStart,
          windowEnd,
          firstRequest: now
        };
        requestCounts.set(rateLimitKey, requestData);
      }
      
      // Check if limit exceeded
      if (requestData.count >= config.maxRequests) {
        // Log rate limit hit
        await logSecurityEvent('rate_limit_exceeded', {
          client_ip: clientIp,
          user_id: userId,
          user_role: userRole,
          operation_type: operationType,
          current_count: requestData.count,
          max_requests: config.maxRequests,
          window_ms: config.windowMs,
          path: req.path,
          user_agent: userAgent
        });
        
        // Update DDoS detection for failed requests
        if (enableDDoSProtection) {
          const detection = ddosDetection.get(clientIp);
          if (detection) {
            detection.failedRequests++;
          }
        }
        
        const retryAfter = Math.ceil((requestData.windowEnd - now) / 1000);
        
        return res.status(429).json({
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
          retryAfter,
          details: {
            operationType,
            maxRequests: config.maxRequests,
            windowMs: config.windowMs,
            currentCount: requestData.count,
            emergencyBypassAvailable: config.bypassable && enableEmergencyBypass
          }
        });
      }
      
      // Increment counter
      requestData.count++;
      
      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': config.maxRequests,
        'X-RateLimit-Remaining': Math.max(0, config.maxRequests - requestData.count),
        'X-RateLimit-Reset': new Date(requestData.windowEnd).toISOString(),
        'X-RateLimit-Operation-Type': operationType
      });
      
      // Continue to next middleware
      next();
      
    } catch (error) {
      console.error('Rate limiting middleware error:', error);
      
      // Log error but don't block request for medical safety
      await logSecurityEvent('rate_limit_error', {
        error_message: error.message,
        error_stack: error.stack,
        path: req.path,
        method: req.method
      });
      
      next();
    }
  };
};

/**
 * Emergency bypass endpoint
 */
const emergencyBypassEndpoint = async (req, res) => {
  try {
    const { operationType, justification, duration } = req.body;
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required for emergency bypass'
      });
    }
    
    // Check if user can request emergency bypass
    const userRole = user.user_metadata?.role;
    if (!['healthcare_provider', 'admin', 'emergency_responder'].includes(userRole)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions for emergency bypass'
      });
    }
    
    // Validate justification
    if (!justification || justification.length < 20) {
      return res.status(400).json({
        success: false,
        error: 'Emergency justification must be at least 20 characters'
      });
    }
    
    // Activate bypass
    await activateEmergencyBypass(user.id, operationType, justification, duration);
    
    res.json({
      success: true,
      message: 'Emergency bypass activated',
      expiresAt: new Date(Date.now() + (duration || 3600000)).toISOString()
    });
    
  } catch (error) {
    console.error('Emergency bypass error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to activate emergency bypass'
    });
  }
};

module.exports = {
  advancedRateLimiting,
  emergencyBypassEndpoint,
  activateEmergencyBypass,
  RATE_LIMITS,
  ROLE_MULTIPLIERS
};
