/**
 * CACHE TYPE DEFINITIONS FOR VOICEHEALTH AI
 * 
 * This file contains comprehensive type definitions for the cache management system
 * with focus on:
 * - Medical data prioritization and safety
 * - HIPAA-compliant cache structures
 * - Performance optimization types
 * - Cache versioning and migration
 * - Analytics and monitoring types
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Emergency data must be clearly identified
 * - Medical data must have proper priority levels
 * - Cache operations must maintain audit trails
 * - Data integrity must be preserved
 */

export type MedicalDataPriority = 'emergency' | 'critical' | 'high' | 'normal' | 'low';

export type CacheCleanupStrategy = 'lru' | 'priority' | 'size' | 'hybrid' | 'adaptive';

export type CacheDataType = 
  | 'medical_condition'
  | 'medication'
  | 'symptom'
  | 'medical_notes'
  | 'patient_data'
  | 'provider_notes'
  | 'emergency_data'
  | 'user_preferences'
  | 'system_config'
  | 'static_content';

export interface CacheEntry {
  readonly key: string;
  data: any;
  readonly encrypted: boolean;
  readonly compressed: boolean;
  readonly priority: MedicalDataPriority;
  readonly isEmergencyData: boolean;
  readonly patientId?: string;
  readonly dataType?: CacheDataType;
  readonly version: string;
  readonly createdAt: number;
  readonly expiresAt: number;
  lastAccessed: number;
  accessCount: number;
  readonly size: number;
  readonly metadata?: Record<string, any>;
}

export interface CacheConfig {
  readonly maxSizeBytes: number;
  readonly maxEntries: number;
  readonly defaultTTL: number;
  readonly emergencyDataTTL: number;
  readonly encryptionEnabled: boolean;
  readonly compressionEnabled: boolean;
  readonly analyticsEnabled: boolean;
  readonly cleanupStrategy: CacheCleanupStrategy;
  readonly priorityWeights: Record<MedicalDataPriority, number>;
  readonly emergencyDataProtection: boolean;
  readonly auditLoggingEnabled: boolean;
}

export interface CacheMetrics {
  readonly hitRate: number;
  readonly missRate: number;
  readonly evictionRate: number;
  readonly averageAccessTime: number;
  readonly memoryUsage: number;
  readonly emergencyDataCount: number;
  readonly totalEntries: number;
  readonly encryptedEntries: number;
  readonly compressedEntries: number;
  readonly priorityDistribution: Record<MedicalDataPriority, number>;
  readonly dataTypeDistribution: Record<CacheDataType, number>;
  readonly performanceMetrics: CachePerformanceMetrics;
}

export interface CachePerformanceMetrics {
  readonly averageSetTime: number;
  readonly averageGetTime: number;
  readonly averageCleanupTime: number;
  readonly cacheEfficiency: number;
  readonly memoryEfficiency: number;
  readonly compressionRatio: number;
  readonly encryptionOverhead: number;
}

export interface CacheVersionInfo {
  readonly version: string;
  readonly timestamp: number;
  readonly migrationRequired?: boolean;
  readonly compatibilityLevel?: number;
  readonly changeLog?: string[];
}

export interface CacheCleanupResult {
  readonly success: boolean;
  readonly entriesRemoved: number;
  readonly spaceFreed: number;
  readonly strategy: CacheCleanupStrategy;
  readonly duration: number;
  readonly emergencyDataPreserved: number;
  readonly errors?: string[];
}

export interface CacheWarmupConfig {
  readonly enabled: boolean;
  readonly emergencyDataFirst: boolean;
  readonly criticalUserWorkflows: string[];
  readonly preloadPatterns: string[];
  readonly maxWarmupTime: number;
  readonly batchSize: number;
}

export interface CacheConflictResolution {
  readonly strategy: 'last_write_wins' | 'merge' | 'user_choice' | 'medical_priority';
  readonly medicalDataPriority: boolean;
  readonly emergencyDataOverride: boolean;
  readonly auditConflicts: boolean;
  readonly userNotification: boolean;
}

export interface CacheAnalyticsEvent {
  readonly timestamp: number;
  readonly eventType: 'hit' | 'miss' | 'set' | 'evict' | 'cleanup' | 'error';
  readonly key: string;
  readonly dataType?: CacheDataType;
  readonly priority?: MedicalDataPriority;
  readonly isEmergencyData?: boolean;
  readonly patientId?: string;
  readonly duration?: number;
  readonly size?: number;
  readonly metadata?: Record<string, any>;
}

export interface CacheSecurityConfig {
  readonly encryptionAlgorithm: string;
  readonly keyRotationInterval: number;
  readonly auditAllAccess: boolean;
  readonly hipaaCompliant: boolean;
  readonly dataRetentionPolicy: number;
  readonly emergencyDataRetention: number;
  readonly accessLogging: boolean;
  readonly integrityChecking: boolean;
}

export interface CacheStorageBackend {
  readonly type: 'memory' | 'indexeddb' | 'localstorage' | 'hybrid';
  readonly persistent: boolean;
  readonly encrypted: boolean;
  readonly maxSize: number;
  readonly compressionSupport: boolean;
  readonly transactionSupport: boolean;
}

export interface CacheMigrationPlan {
  readonly fromVersion: string;
  readonly toVersion: string;
  readonly migrationSteps: CacheMigrationStep[];
  readonly rollbackPlan: CacheMigrationStep[];
  readonly dataIntegrityChecks: string[];
  readonly emergencyDataHandling: 'preserve' | 'migrate' | 'duplicate';
}

export interface CacheMigrationStep {
  readonly stepId: string;
  readonly description: string;
  readonly operation: 'transform' | 'move' | 'delete' | 'validate';
  readonly targetKeys?: string[];
  readonly transformFunction?: string;
  readonly validationRules?: string[];
  readonly rollbackOperation?: string;
}

export interface CacheHealthCheck {
  readonly timestamp: number;
  readonly overallHealth: 'healthy' | 'warning' | 'critical';
  readonly memoryUsage: number;
  readonly hitRate: number;
  readonly errorRate: number;
  readonly emergencyDataIntegrity: boolean;
  readonly encryptionStatus: boolean;
  readonly performanceScore: number;
  readonly recommendations: string[];
  readonly alerts: CacheAlert[];
}

export interface CacheAlert {
  readonly id: string;
  readonly severity: 'info' | 'warning' | 'error' | 'critical';
  readonly message: string;
  readonly timestamp: number;
  readonly category: 'performance' | 'security' | 'integrity' | 'capacity';
  readonly affectedKeys?: string[];
  readonly recommendedAction?: string;
  readonly emergencyDataAffected?: boolean;
}

export interface CacheOperationResult<T = any> {
  readonly success: boolean;
  readonly data?: T;
  readonly cached?: boolean;
  readonly fromCache?: boolean;
  readonly error?: string;
  readonly metadata?: {
    readonly priority?: MedicalDataPriority;
    readonly isEmergencyData?: boolean;
    readonly createdAt?: number;
    readonly accessCount?: number;
    readonly version?: string;
    readonly encrypted?: boolean;
    readonly compressed?: boolean;
  };
  readonly performance?: {
    readonly duration: number;
    readonly cacheHit: boolean;
    readonly encryptionTime?: number;
    readonly compressionTime?: number;
  };
}

export interface CacheSyncConfig {
  readonly enabled: boolean;
  readonly syncInterval: number;
  readonly conflictResolution: CacheConflictResolution;
  readonly emergencyDataPriority: boolean;
  readonly batchSize: number;
  readonly retryAttempts: number;
  readonly syncOnReconnect: boolean;
  readonly auditSyncOperations: boolean;
}

export interface CacheSyncOperation {
  readonly id: string;
  readonly type: 'upload' | 'download' | 'conflict_resolution';
  readonly keys: string[];
  readonly timestamp: number;
  readonly status: 'pending' | 'in_progress' | 'completed' | 'failed';
  readonly priority: MedicalDataPriority;
  readonly emergencyData: boolean;
  readonly conflictsDetected?: number;
  readonly conflictsResolved?: number;
  readonly error?: string;
  readonly duration?: number;
}

export interface CacheCompressionConfig {
  readonly enabled: boolean;
  readonly algorithm: 'gzip' | 'deflate' | 'brotli';
  readonly level: number;
  readonly minSizeThreshold: number;
  readonly excludeDataTypes: CacheDataType[];
  readonly emergencyDataCompression: boolean;
}

export interface CacheEncryptionConfig {
  readonly enabled: boolean;
  readonly algorithm: 'AES-256-GCM';
  readonly keyDerivation: 'PBKDF2';
  readonly saltLength: number;
  readonly ivLength: number;
  readonly tagLength: number;
  readonly keyRotationInterval: number;
  readonly emergencyDataEncryption: boolean;
  readonly auditEncryptionOperations: boolean;
}

export interface CacheMonitoringConfig {
  readonly enabled: boolean;
  readonly metricsInterval: number;
  readonly alertThresholds: {
    readonly memoryUsage: number;
    readonly hitRate: number;
    readonly errorRate: number;
    readonly responseTime: number;
  };
  readonly emergencyDataMonitoring: boolean;
  readonly performanceTracking: boolean;
  readonly securityEventTracking: boolean;
}

// Service Worker Cache Types
export interface ServiceWorkerCacheConfig {
  readonly cacheName: string;
  readonly version: string;
  readonly staticAssets: string[];
  readonly dynamicCaching: boolean;
  readonly networkFirst: string[];
  readonly cacheFirst: string[];
  readonly emergencyRoutes: string[];
  readonly maxAge: number;
  readonly maxEntries: number;
  readonly purgeOnUpdate: boolean;
}

export interface ServiceWorkerCacheStrategy {
  readonly name: 'network-first' | 'cache-first' | 'stale-while-revalidate' | 'network-only' | 'cache-only';
  readonly patterns: string[];
  readonly options: {
    readonly cacheName?: string;
    readonly networkTimeoutSeconds?: number;
    readonly cacheableResponse?: {
      readonly statuses: number[];
      readonly headers?: Record<string, string>;
    };
    readonly broadcastUpdate?: boolean;
    readonly plugins?: any[];
  };
}

// PWA Cache Management Types
export interface PWACacheManager {
  readonly version: string;
  readonly strategies: ServiceWorkerCacheStrategy[];
  readonly emergencyFallbacks: Record<string, string>;
  readonly offlinePages: string[];
  readonly criticalResources: string[];
  readonly updateNotification: boolean;
  readonly backgroundSync: boolean;
  readonly periodicSync: boolean;
}

export interface CacheKeyGenerator {
  generate(
    dataType: CacheDataType,
    identifier: string,
    options?: {
      readonly userId?: string;
      readonly patientId?: string;
      readonly version?: string;
      readonly timestamp?: number;
    }
  ): string;
  
  parse(key: string): {
    readonly dataType: CacheDataType;
    readonly identifier: string;
    readonly userId?: string;
    readonly patientId?: string;
    readonly version?: string;
    readonly timestamp?: number;
  } | null;
}

export interface CacheEventEmitter {
  on(event: 'hit' | 'miss' | 'set' | 'evict' | 'cleanup' | 'error', listener: (data: any) => void): void;
  off(event: string, listener: (data: any) => void): void;
  emit(event: string, data: any): void;
}

// Cache Plugin System Types
export interface CachePlugin {
  readonly name: string;
  readonly version: string;
  readonly priority: number;
  
  beforeSet?(key: string, data: any, options: any): Promise<{ key: string; data: any; options: any }>;
  afterSet?(key: string, result: CacheOperationResult): Promise<void>;
  beforeGet?(key: string): Promise<string>;
  afterGet?(key: string, result: CacheOperationResult): Promise<CacheOperationResult>;
  beforeEvict?(key: string, entry: CacheEntry): Promise<boolean>;
  afterEvict?(key: string, success: boolean): Promise<void>;
  onCleanup?(strategy: CacheCleanupStrategy, result: CacheCleanupResult): Promise<void>;
  onError?(error: Error, operation: string, context: any): Promise<void>;
}

export interface CachePluginManager {
  register(plugin: CachePlugin): void;
  unregister(pluginName: string): void;
  getPlugin(name: string): CachePlugin | null;
  executeHook(hookName: string, ...args: any[]): Promise<any>;
}

export default {
  // Export all types for external use
};
