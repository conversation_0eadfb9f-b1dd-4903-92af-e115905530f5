/**
 * API RESPONSE AND ERROR HANDLING TYPE DEFINITIONS
 * 
 * This file contains TypeScript type definitions for API responses,
 * error handling, and network communication to ensure type safety
 * across all medical data operations.
 * 
 * SAFETY REQUIREMENTS:
 * - Strict typing for all API responses
 * - Comprehensive error classification
 * - Type-safe error recovery mechanisms
 * - Clear distinction between client and server errors
 */

// Base API response structure
export interface ApiResponse<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly message?: string;
  readonly timestamp: string;
  readonly requestId?: string;
}

// Paginated response structure
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  readonly pagination: {
    readonly page: number;
    readonly limit: number;
    readonly total: number;
    readonly totalPages: number;
    readonly hasNext: boolean;
    readonly hasPrevious: boolean;
  };
}

// Error classification for medical systems
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';
export type ErrorCategory = 
  | 'validation'
  | 'authentication'
  | 'authorization'
  | 'network'
  | 'database'
  | 'encryption'
  | 'medical_data'
  | 'system'
  | 'external_service';

export interface ApiError {
  readonly code: string;
  readonly message: string;
  readonly category: ErrorCategory;
  readonly severity: ErrorSeverity;
  readonly details?: Record<string, unknown>;
  readonly timestamp: string;
  readonly requestId?: string;
  readonly patientSafetyImpact: boolean;
  readonly retryable: boolean;
  readonly suggestedAction?: string;
}

// HTTP status code mappings
export type HttpStatusCode = 
  | 200 // OK
  | 201 // Created
  | 204 // No Content
  | 400 // Bad Request
  | 401 // Unauthorized
  | 403 // Forbidden
  | 404 // Not Found
  | 409 // Conflict
  | 422 // Unprocessable Entity
  | 429 // Too Many Requests
  | 500 // Internal Server Error
  | 502 // Bad Gateway
  | 503 // Service Unavailable
  | 504; // Gateway Timeout

// Request configuration
export interface ApiRequestConfig {
  readonly method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  readonly url: string;
  readonly headers?: Record<string, string>;
  readonly params?: Record<string, string | number | boolean>;
  readonly data?: unknown;
  readonly timeout?: number;
  readonly retries?: number;
  readonly retryDelay?: number;
  readonly requiresAuth?: boolean;
  readonly emergencyOverride?: boolean;
}

// Rate limiting types
export interface RateLimitInfo {
  readonly limit: number;
  readonly remaining: number;
  readonly reset: number; // Unix timestamp
  readonly retryAfter?: number; // Seconds
}

export interface RateLimitedResponse<T> extends ApiResponse<T> {
  readonly rateLimit: RateLimitInfo;
}

// Offline/online status
export interface NetworkStatus {
  readonly online: boolean;
  readonly lastOnline?: string; // ISO date string
  readonly connectionType?: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  readonly effectiveType?: '2g' | '3g' | '4g' | '5g' | 'unknown';
}

// Sync status for offline operations
export type SyncStatus = 'pending' | 'syncing' | 'synced' | 'failed' | 'conflict';

export interface SyncOperation {
  readonly id: string;
  readonly type: 'create' | 'update' | 'delete';
  readonly resource: string;
  readonly resourceId: string;
  readonly data: unknown;
  readonly timestamp: string;
  readonly status: SyncStatus;
  readonly retryCount: number;
  readonly maxRetries: number;
  readonly lastError?: string;
  readonly priority: 'low' | 'normal' | 'high' | 'emergency';
}

// Medical data specific API types
export interface MedicalDataApiResponse<T> extends ApiResponse<T> {
  readonly source: 'online' | 'offline' | 'cache';
  readonly encrypted: boolean;
  readonly auditLogged: boolean;
  readonly syncStatus?: SyncStatus;
}

// Validation error details
export interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
  readonly value?: unknown;
}

export interface ValidationErrorResponse extends ApiResponse<never> {
  readonly validationErrors: ValidationError[];
}

// Authentication error types
export interface AuthenticationError extends ApiError {
  readonly category: 'authentication';
  readonly authErrorType: 'invalid_credentials' | 'token_expired' | 'token_invalid' | 'account_locked' | 'mfa_required';
  readonly requiresReauth: boolean;
}

// Authorization error types
export interface AuthorizationError extends ApiError {
  readonly category: 'authorization';
  readonly requiredPermission: {
    readonly resource: string;
    readonly action: string;
  };
  readonly userRole: string;
  readonly emergencyOverrideAvailable: boolean;
}

// Medical data error types
export interface MedicalDataError extends ApiError {
  readonly category: 'medical_data';
  readonly dataType: string;
  readonly operation: 'create' | 'read' | 'update' | 'delete' | 'encrypt' | 'decrypt';
  readonly patientId?: string;
  readonly criticalityLevel: 'routine' | 'important' | 'urgent' | 'critical';
}

// Encryption error types
export interface EncryptionError extends ApiError {
  readonly category: 'encryption';
  readonly operation: 'encrypt' | 'decrypt' | 'key_derivation';
  readonly algorithm: string;
  readonly dataCorrupted: boolean;
}

// Network error types
export interface NetworkError extends ApiError {
  readonly category: 'network';
  readonly networkErrorType: 'timeout' | 'connection_failed' | 'dns_error' | 'ssl_error';
  readonly offlineFallbackAvailable: boolean;
}

// Error recovery strategies
export type ErrorRecoveryStrategy = 
  | 'retry'
  | 'fallback_offline'
  | 'user_intervention'
  | 'emergency_protocol'
  | 'system_maintenance'
  | 'contact_support';

export interface ErrorRecoveryPlan {
  readonly strategy: ErrorRecoveryStrategy;
  readonly description: string;
  readonly automated: boolean;
  readonly estimatedRecoveryTime?: number; // Minutes
  readonly userActionRequired?: string;
  readonly emergencyContactInfo?: string;
}

// API client interface
export interface ApiClient {
  request<T>(config: ApiRequestConfig): Promise<ApiResponse<T>>;
  get<T>(url: string, params?: Record<string, unknown>): Promise<ApiResponse<T>>;
  post<T>(url: string, data?: unknown): Promise<ApiResponse<T>>;
  put<T>(url: string, data?: unknown): Promise<ApiResponse<T>>;
  patch<T>(url: string, data?: unknown): Promise<ApiResponse<T>>;
  delete<T>(url: string): Promise<ApiResponse<T>>;
  
  // Medical data specific methods
  getMedicalData<T>(endpoint: string, userId: string): Promise<MedicalDataApiResponse<T>>;
  postMedicalData<T>(endpoint: string, data: unknown): Promise<MedicalDataApiResponse<T>>;
  
  // Error handling
  handleError(error: unknown): ApiError;
  getRecoveryPlan(error: ApiError): ErrorRecoveryPlan;
  
  // Network status
  getNetworkStatus(): NetworkStatus;
  onNetworkStatusChange(callback: (status: NetworkStatus) => void): () => void;
}

// Webhook types for real-time updates
export interface WebhookPayload<T = unknown> {
  readonly event: string;
  readonly data: T;
  readonly timestamp: string;
  readonly signature: string;
  readonly version: string;
}

export interface MedicalDataWebhook extends WebhookPayload {
  readonly event: 'medical_data.created' | 'medical_data.updated' | 'medical_data.deleted' | 'consultation.status_changed';
  readonly patientId: string;
  readonly resourceType: string;
  readonly resourceId: string;
  readonly urgency: 'routine' | 'urgent' | 'emergency';
}

// Performance monitoring types
export interface ApiPerformanceMetrics {
  readonly endpoint: string;
  readonly method: string;
  readonly responseTime: number; // Milliseconds
  readonly statusCode: HttpStatusCode;
  readonly success: boolean;
  readonly timestamp: string;
  readonly userAgent?: string;
  readonly userId?: string;
}

// Health check types
export interface HealthCheckResponse {
  readonly status: 'healthy' | 'degraded' | 'unhealthy';
  readonly timestamp: string;
  readonly version: string;
  readonly services: {
    readonly database: 'up' | 'down' | 'degraded';
    readonly encryption: 'up' | 'down' | 'degraded';
    readonly authentication: 'up' | 'down' | 'degraded';
    readonly external_apis: 'up' | 'down' | 'degraded';
  };
  readonly metrics: {
    readonly uptime: number; // Seconds
    readonly responseTime: number; // Milliseconds
    readonly errorRate: number; // Percentage
    readonly activeConnections: number;
  };
}
