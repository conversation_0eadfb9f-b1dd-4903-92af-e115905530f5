import React from "react";
import { createRoot } from "react-dom/client";
// SECURITY FIX: Removed redundant AuthProvider wrapper - already in App.jsx
// import { AuthProvider } from "./contexts/AuthContext";
import App from "./App";
import "./styles/tailwind.css";
import "./styles/index.css";

const container = document.getElementById("root");
const root = createRoot(container);

// SECURITY FIX: Single AuthProvider in App.jsx prevents state conflicts
root.render(<App />);

// Register service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}