import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AgentOrchestrationPanel = () => {
  const [activeSessions, setActiveSessions] = useState([
    {
      id: 'session_001',
      patientName: '<PERSON>',
      currentAgent: 'General Practitioner',
      sessionType: 'consultation',
      duration: '08:34',
      handoffs: 2,
      agents: ['Tri<PERSON>', 'General Practitioner', 'Specialist'],
      status: 'active',
      priority: 'normal'
    },
    {
      id: 'session_002',
      patientName: '<PERSON>',
      currentAgent: 'Mental Health Specialist',
      sessionType: 'therapy',
      duration: '23:12',
      handoffs: 1,
      agents: ['Triage', 'Mental Health Specialist'],
      status: 'active',
      priority: 'high'
    },
    {
      id: 'session_003',
      patientName: '<PERSON>',
      currentAgent: 'Pediatrician',
      sessionType: 'consultation',
      duration: '05:47',
      handoffs: 3,
      agents: ['Tri<PERSON>', 'General Practitioner', 'Pediatrician', 'Nurse'],
      status: 'handoff_pending',
      priority: 'urgent'
    }
  ]);

  const [collaborationMetrics, setCollaborationMetrics] = useState({
    totalActiveSessions: 12,
    avgHandoffTime: 45,
    successfulHandoffs: 98.5,
    activeAgents: 8,
    contextSharingAccuracy: 99.2,
    avgDecisionTime: 2.3
  });

  const [selectedSession, setSelectedSession] = useState(null);

  const agentTypes = [
    { id: 'triage', name: 'Triage Agent', icon: 'Filter', color: 'text-blue-600' },
    { id: 'gp', name: 'General Practitioner', icon: 'Stethoscope', color: 'text-green-600' },
    { id: 'specialist', name: 'Specialist', icon: 'Brain', color: 'text-purple-600' },
    { id: 'mental-health', name: 'Mental Health', icon: 'Heart', color: 'text-pink-600' },
    { id: 'pediatrician', name: 'Pediatrician', icon: 'Baby', color: 'text-orange-600' },
    { id: 'nurse', name: 'Nurse', icon: 'UserCheck', color: 'text-indigo-600' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-success-600 bg-success-100';
      case 'handoff_pending': return 'text-warning-600 bg-warning-100';
      case 'waiting': return 'text-gray-600 bg-gray-100';
      case 'completed': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-error-600 bg-error-100';
      case 'high': return 'text-warning-600 bg-warning-100';
      case 'normal': return 'text-success-600 bg-success-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleSessionAction = (sessionId, action) => {
    console.log(`Session action: ${action} for session ${sessionId}`);
    
    if (action === 'intervene') {
      setActiveSessions(prev => prev.map(session => 
        session.id === sessionId 
          ? { ...session, status: 'admin_intervention' }
          : session
      ));
    }
  };

  const handleForceHandoff = (sessionId, targetAgent) => {
    console.log(`Force handoff for session ${sessionId} to ${targetAgent}`);
    setActiveSessions(prev => prev.map(session => 
      session.id === sessionId 
        ? { 
            ...session, 
            currentAgent: targetAgent, 
            handoffs: session.handoffs + 1,
            status: 'active'
          }
        : session
    ));
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Agent Orchestration
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Monitor active consultations with visual agent handoffs and collaboration workflows
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              iconName="Users"
            >
              Agent Pool
            </Button>
            <Button
              size="sm"
              variant="primary"
              iconName="Settings"
            >
              Configure
            </Button>
          </div>
        </div>
      </div>

      {/* Collaboration Metrics */}
      <div className="p-6 border-b border-border">
        <h4 className="text-sm font-medium text-text-primary mb-3">Collaboration Metrics</h4>
        <div className="grid grid-cols-2 lg:grid-cols-6 gap-4">
          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-1">
              <Icon name="Users" size={16} className="text-primary-500 mr-2" />
              <span className="text-xs text-text-secondary">Active Sessions</span>
            </div>
            <p className="text-lg font-semibold text-text-primary">{collaborationMetrics.totalActiveSessions}</p>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-1">
              <Icon name="Clock" size={16} className="text-success-500 mr-2" />
              <span className="text-xs text-text-secondary">Avg Handoff Time</span>
            </div>
            <p className="text-lg font-semibold text-text-primary">{collaborationMetrics.avgHandoffTime}s</p>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-1">
              <Icon name="CheckCircle" size={16} className="text-success-500 mr-2" />
              <span className="text-xs text-text-secondary">Successful Handoffs</span>
            </div>
            <p className="text-lg font-semibold text-success-600">{collaborationMetrics.successfulHandoffs}%</p>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-1">
              <Icon name="Bot" size={16} className="text-primary-500 mr-2" />
              <span className="text-xs text-text-secondary">Active Agents</span>
            </div>
            <p className="text-lg font-semibold text-text-primary">{collaborationMetrics.activeAgents}</p>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-1">
              <Icon name="Share" size={16} className="text-purple-500 mr-2" />
              <span className="text-xs text-text-secondary">Context Sharing</span>
            </div>
            <p className="text-lg font-semibold text-success-600">{collaborationMetrics.contextSharingAccuracy}%</p>
          </div>

          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="flex items-center mb-1">
              <Icon name="Zap" size={16} className="text-warning-500 mr-2" />
              <span className="text-xs text-text-secondary">Avg Decision Time</span>
            </div>
            <p className="text-lg font-semibold text-text-primary">{collaborationMetrics.avgDecisionTime}s</p>
          </div>
        </div>
      </div>

      {/* Active Sessions */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-medium text-text-primary">Active Consultation Sessions</h4>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" iconName="Filter">
              Filter
            </Button>
            <Button size="sm" variant="outline" iconName="RefreshCw">
              Refresh
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {activeSessions.map((session) => (
            <div key={session.id} className="border border-border rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                    <Icon name="User" size={20} className="text-primary-600" />
                  </div>
                  <div>
                    <h5 className="font-medium text-text-primary">{session.patientName}</h5>
                    <p className="text-sm text-text-secondary">Session {session.id}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(session.priority)}`}>
                    {session.priority}
                  </span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(session.status)}`}>
                    {session.status.replace('_', ' ')}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-secondary-50 rounded-lg p-3">
                  <div className="flex items-center mb-2">
                    <Icon name="Clock" size={16} className="text-text-secondary mr-2" />
                    <span className="text-sm text-text-secondary">Duration</span>
                  </div>
                  <p className="font-semibold text-text-primary">{session.duration}</p>
                </div>

                <div className="bg-secondary-50 rounded-lg p-3">
                  <div className="flex items-center mb-2">
                    <Icon name="ArrowRightLeft" size={16} className="text-text-secondary mr-2" />
                    <span className="text-sm text-text-secondary">Handoffs</span>
                  </div>
                  <p className="font-semibold text-text-primary">{session.handoffs}</p>
                </div>

                <div className="bg-secondary-50 rounded-lg p-3">
                  <div className="flex items-center mb-2">
                    <Icon name="Bot" size={16} className="text-text-secondary mr-2" />
                    <span className="text-sm text-text-secondary">Current Agent</span>
                  </div>
                  <p className="font-semibold text-text-primary">{session.currentAgent}</p>
                </div>
              </div>

              {/* Agent Flow Visualization */}
              <div className="mb-4">
                <h6 className="text-xs font-medium text-text-secondary mb-2">Agent Workflow</h6>
                <div className="flex items-center space-x-2 overflow-x-auto">
                  {session.agents.map((agent, index) => (
                    <React.Fragment key={agent}>
                      <div
                        className={`flex items-center px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
                          agent === session.currentAgent
                            ? 'bg-primary-100 text-primary-700 border border-primary-300'
                            : index < session.agents.indexOf(session.currentAgent)
                            ? 'bg-success-100 text-success-700' :'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {agent === session.currentAgent && (
                          <div className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></div>
                        )}
                        {index < session.agents.indexOf(session.currentAgent) && (
                          <Icon name="Check" size={12} className="mr-1" />
                        )}
                        {agent}
                      </div>
                      {index < session.agents.length - 1 && (
                        <Icon name="ChevronRight" size={16} className="text-text-secondary" />
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </div>

              {/* Session Actions */}
              <div className="flex gap-2 pt-2 border-t border-border">
                <Button
                  size="sm"
                  variant="outline"
                  iconName="Eye"
                  onClick={() => setSelectedSession(session.id)}
                >
                  Monitor
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  iconName="MessageCircle"
                >
                  Join
                </Button>
                {session.status === 'handoff_pending' && (
                  <Button
                    size="sm"
                    variant="outline"
                    iconName="ArrowRight"
                    onClick={() => handleForceHandoff(session.id, 'Specialist')}
                  >
                    Force Handoff
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  iconName="AlertTriangle"
                  onClick={() => handleSessionAction(session.id, 'intervene')}
                >
                  Intervene
                </Button>
              </div>
            </div>
          ))}
        </div>

        {activeSessions.length === 0 && (
          <div className="text-center py-8">
            <Icon name="Users" size={48} className="text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">No Active Sessions</h3>
            <p className="text-text-secondary">All consultation sessions are currently idle</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentOrchestrationPanel;