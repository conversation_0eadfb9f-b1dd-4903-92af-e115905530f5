{"short_name": "VoiceHealth AI", "name": "VoiceHealth AI - Smart Telemedicine Platform", "description": "AI-powered telemedicine platform with voice consultation and offline capabilities", "icons": [{"src": "favicon.ico", "sizes": "64x64 32x32 24x24 16x16", "type": "image/x-icon"}, {"src": "assets/images/icon-192.svg", "type": "image/svg+xml", "sizes": "192x192", "purpose": "any maskable"}, {"src": "assets/images/icon-512.svg", "type": "image/svg+xml", "sizes": "512x512", "purpose": "any maskable"}], "start_url": "/", "display": "standalone", "orientation": "portrait", "theme_color": "#3B82F6", "background_color": "#ffffff", "categories": ["health", "medical", "ai", "telemedicine"], "lang": "en-US", "scope": "/", "prefer_related_applications": false, "shortcuts": [{"name": "Start Consultation", "short_name": "Consult", "description": "Start a new voice consultation", "url": "/enhanced-voice-consultation-interface", "icons": [{"src": "assets/images/icon-192.svg", "sizes": "192x192"}]}, {"name": "Medical History", "short_name": "History", "description": "View medical history and records", "url": "/session-dashboard-history", "icons": [{"src": "assets/images/icon-192.svg", "sizes": "192x192"}]}, {"name": "Emergency Mode", "short_name": "Emergency", "description": "Access emergency offline consultation", "url": "/emergency-offline-consultation", "icons": [{"src": "assets/images/icon-192.svg", "sizes": "192x192"}]}]}