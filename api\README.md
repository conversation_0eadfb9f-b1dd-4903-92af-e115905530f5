# VoiceHealth AI - Secure Backend API

## 🔒 CRITICAL SECURITY NOTICE

This backend API handles sensitive operations that **MUST NOT** be performed client-side:
- Paystack secret key operations
- Medical data encryption/decryption
- HIPAA-compliant audit logging
- Secure payment processing

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Secure server environment (NOT client-accessible)

### Installation

1. **Install dependencies:**
```bash
cd api
npm install
```

2. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your actual values
```

3. **Start the server:**
```bash
# Development
npm run dev

# Production
npm start
```

## 🛡️ Security Features

### Authentication
- JWT token validation via Supabase
- User authorization for all operations
- Request validation and sanitization

### Rate Limiting
- General API: 100 requests/15 minutes per IP
- Payment endpoints: 10 requests/15 minutes per IP
- Configurable limits via environment variables

### Input Validation
- Email format validation
- Amount validation (positive numbers only)
- Reference format validation (alphanumeric + underscore/dash)
- SQL injection prevention
- XSS protection

### CORS Protection
- Whitelist-based origin validation
- Credentials support for authenticated requests
- Configurable allowed origins

## 📡 API Endpoints

### Payment Operations

#### Initialize Payment
```
POST /api/payments/initialize
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "amount": 1000,
  "currency": "NGN",
  "reference": "unique_reference",
  "callback_url": "https://yourapp.com/callback",
  "metadata": {
    "subscription_id": "sub_123",
    "consultation_session_id": "session_456"
  }
}
```

#### Verify Payment
```
GET /api/payments/verify/:reference
Authorization: Bearer <jwt_token>
```

### Health Check
```
GET /health
```

## 🏥 HIPAA Compliance

### Data Protection
- No sensitive medical data stored in logs
- User ID hashing for audit trails
- Secure token validation
- Input sanitization for all requests

### Audit Logging
- All payment operations logged (without sensitive data)
- User authentication events tracked
- Error events recorded with context
- Timestamp and user ID for all operations

## 🚀 Deployment

### Environment Variables (Required)

```bash
# Server
NODE_ENV=production
PORT=3001

# CORS
FRONTEND_URL=https://your-frontend-domain.com
PRODUCTION_URL=https://your-production-domain.com

# Paystack (KEEP SECRET)
PAYSTACK_SECRET_KEY=sk_live_your_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_public_key

# Supabase (KEEP SECRET)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Production Deployment Checklist

- [ ] Use HTTPS only
- [ ] Set NODE_ENV=production
- [ ] Configure proper CORS origins
- [ ] Set up monitoring (Sentry, etc.)
- [ ] Enable request logging
- [ ] Set up automated backups
- [ ] Configure firewall rules
- [ ] Set up SSL certificates
- [ ] Enable security headers
- [ ] Configure rate limiting
- [ ] Set up health checks
- [ ] Enable audit logging

### Recommended Hosting Platforms
- **Railway** (Easy deployment)
- **Heroku** (Simple scaling)
- **DigitalOcean App Platform** (Cost-effective)
- **AWS Lambda** (Serverless)
- **Google Cloud Run** (Container-based)

## 🧪 Testing

```bash
# Run tests
npm test

# Security audit
npm run security-audit

# Lint code
npm run lint
```

## 📊 Monitoring

### Health Checks
The `/health` endpoint provides server status:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

### Logging
- Request/response logging via Morgan
- Error logging with stack traces (dev only)
- Payment operation logging (sanitized)
- Authentication event logging

## 🔧 Configuration

### Rate Limiting
Adjust in `.env`:
```bash
RATE_LIMIT_WINDOW_MS=900000      # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100      # General limit
PAYMENT_RATE_LIMIT_MAX=10        # Payment limit
```

### CORS Origins
Add allowed origins in `server.js`:
```javascript
const allowedOrigins = [
  'https://your-frontend.com',
  'https://your-admin-panel.com'
];
```

## ⚠️ Security Warnings

1. **NEVER** expose this API publicly without authentication
2. **NEVER** commit `.env` files to version control
3. **ALWAYS** use HTTPS in production
4. **ALWAYS** validate and sanitize inputs
5. **NEVER** log sensitive data (passwords, tokens, medical info)
6. **ALWAYS** use the latest security patches

## 📞 Support

For security issues or deployment questions, contact the development team immediately.

**Remember: This API handles sensitive medical and financial data. Security is paramount.**
