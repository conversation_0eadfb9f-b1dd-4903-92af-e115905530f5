/**
 * EMERGENCY MEDICAL AGENT
 * 
 * Specialized agent for handling medical emergencies with <2 second response time.
 * Designed to quickly assess emergency situations and provide immediate guidance
 * while ensuring proper emergency services are contacted.
 * 
 * CRITICAL REQUIREMENTS:
 * - Response time must be < 2 seconds for critical emergencies
 * - Must prioritize patient safety above all else
 * - Must provide clear, actionable emergency guidance
 * - Must ensure emergency services are contacted when appropriate
 * - Must maintain audit trail for all emergency interactions
 * 
 * CAPABILITIES:
 * - Emergency situation assessment
 * - Critical symptom recognition
 * - Emergency protocol activation
 * - Crisis intervention guidance
 * - Emergency services coordination
 */

import { BaseAgent } from './BaseAgent';
import { aiOrchestrator } from '../services/aiOrchestrator';
import { contextAssemblyService } from '../services/ContextAssemblyService';
import { culturallyAwareEmergencyService, CulturalEmergencyProtocol } from '../services/CulturallyAwareEmergencyService';
import { enhancedClinicalDecisionSupportService } from '../services/ClinicalDecisionSupportService';
import type {
  AgentRequest,
  AgentResponse,
  AgentRole,
  AgentCapability,
  EmergencyFlag
} from './BaseAgent';
import type { MemoryManager } from '../services/MemoryManager';

export class EmergencyAgent extends BaseAgent {
  private emergencyProtocols: Map<string, EmergencyProtocol>;

  constructor(memoryManager: MemoryManager) {
    const id = 'emergency-agent-001';
    const name = 'Dr. Emergency Response';
    const role: AgentRole = 'emergency';
    const capabilities: AgentCapability[] = [
      'emergency_response',
      'diagnostic_assessment',
      'treatment_planning'
    ];

    const systemPrompt = `You are an Emergency Medical Response AI designed to handle critical medical situations with immediate, life-saving guidance.

CRITICAL MISSION: Provide immediate, clear, and actionable emergency medical guidance while ensuring appropriate emergency services are contacted.

EMERGENCY RESPONSE PROTOCOL:
1. IMMEDIATE ASSESSMENT: Quickly identify life-threatening conditions
2. CLEAR INSTRUCTIONS: Provide step-by-step emergency actions
3. EMERGENCY SERVICES: Direct to call emergency services when appropriate
4. CONTINUOUS MONITORING: Guide until professional help arrives
5. SAFETY FIRST: Prioritize patient and responder safety

CRITICAL CONDITIONS TO RECOGNIZE:
- Cardiac arrest, heart attack, stroke
- Severe breathing difficulties, choking
- Severe bleeding, trauma
- Loss of consciousness, seizures
- Severe allergic reactions
- Poisoning, overdose
- Severe burns, electrical injuries
- Mental health crises, suicide risk

RESPONSE REQUIREMENTS:
- Respond within 2 seconds for critical emergencies
- Use clear, simple language under stress
- Provide specific, actionable steps
- Emphasize when to call emergency services (911/999/112)
- Guide basic life support when appropriate
- Maintain calm, authoritative tone

SAFETY PROTOCOLS:
- Never delay emergency services for non-critical advice
- Always prioritize professional emergency care
- Provide guidance only within scope of first aid
- Emphasize scene safety for responders
- Document all emergency interactions for audit

Remember: Your role is to bridge the gap until professional emergency medical services arrive. Every second counts in emergency situations.`;

    super(id, name, role, capabilities, systemPrompt, memoryManager);
    
    this.emergencyProtocols = this.initializeEmergencyProtocols();
  }

  /**
   * Handle emergency requests with <2 second response time
   */
  async handleMessage(request: AgentRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🚨 EMERGENCY AGENT activated for session: ${request.sessionId}`);

      // Immediate emergency assessment
      const emergencyAssessment = this.assessEmergencyLevel(request.userMessage);
      
      // Generate immediate response based on emergency level
      const response = await this.generateEmergencyResponse(request, emergencyAssessment);

      const responseTime = Date.now() - startTime;
      
      // Ensure response time is under 2 seconds for critical emergencies
      if (emergencyAssessment.level === 'critical' && responseTime > 2000) {
        console.warn(`⚠️ Emergency response time exceeded 2 seconds: ${responseTime}ms`);
      }

      this.updateMetrics(responseTime, 0.95, false, true);

      // Log emergency interaction for audit
      await this.logEmergencyInteraction(request, emergencyAssessment, responseTime);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence: 0.95,
        reasoning: `Emergency assessment completed - ${emergencyAssessment.level} level emergency`,
        emergencyFlags: emergencyAssessment.flags,
        metadata: {
          responseTime,
          emergencyLevel: emergencyAssessment.level,
          protocolActivated: emergencyAssessment.protocol,
          auditLogged: true
        }
      };

    } catch (error) {
      console.error('❌ CRITICAL: Emergency Agent error:', error);
      
      // Even in error, provide emergency guidance
      return {
        agentId: this.id,
        agentName: this.name,
        content: "EMERGENCY SYSTEM ERROR - If this is a medical emergency, call emergency services (911/999/112) immediately. Do not wait for system recovery.",
        confidence: 0.9,
        reasoning: 'System error during emergency - defaulting to emergency services guidance',
        emergencyFlags: [{
          type: 'medical_emergency',
          severity: 'critical',
          description: 'Emergency system error - immediate professional help required',
          recommendedAction: 'Call emergency services immediately',
          timeToResponse: 0
        }],
        metadata: {
          systemError: true,
          responseTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Assess emergency level and determine appropriate protocol
   */
  private assessEmergencyLevel(message: string): EmergencyAssessment {
    const lowerMessage = message.toLowerCase();
    
    // Critical emergency keywords
    const criticalKeywords = [
      'not breathing', 'no pulse', 'unconscious', 'unresponsive',
      'cardiac arrest', 'heart attack', 'stroke', 'severe bleeding',
      'choking', 'overdose', 'suicide', 'self harm'
    ];

    // High emergency keywords
    const highKeywords = [
      'chest pain', 'difficulty breathing', 'severe pain',
      'allergic reaction', 'seizure', 'head injury', 'severe burn'
    ];

    // Medium emergency keywords
    const mediumKeywords = [
      'emergency', 'urgent', 'help', 'pain', 'injury', 'accident'
    ];

    const flags: EmergencyFlag[] = [];
    let level: 'critical' | 'high' | 'medium' | 'low' = 'low';
    let protocol = 'general_assessment';

    // Check for critical emergencies
    for (const keyword of criticalKeywords) {
      if (lowerMessage.includes(keyword)) {
        level = 'critical';
        protocol = this.getProtocolForKeyword(keyword);
        flags.push({
          type: 'medical_emergency',
          severity: 'critical',
          description: `Critical emergency detected: ${keyword}`,
          recommendedAction: 'Call emergency services immediately',
          timeToResponse: 0
        });
        break;
      }
    }

    // Check for high emergencies if not critical
    if (level !== 'critical') {
      for (const keyword of highKeywords) {
        if (lowerMessage.includes(keyword)) {
          level = 'high';
          protocol = this.getProtocolForKeyword(keyword);
          flags.push({
            type: 'medical_emergency',
            severity: 'high',
            description: `High priority emergency: ${keyword}`,
            recommendedAction: 'Seek immediate medical attention',
            timeToResponse: 1000
          });
          break;
        }
      }
    }

    // Check for medium emergencies
    if (level === 'low') {
      for (const keyword of mediumKeywords) {
        if (lowerMessage.includes(keyword)) {
          level = 'medium';
          protocol = 'general_emergency';
          flags.push({
            type: 'medical_emergency',
            severity: 'medium',
            description: `Emergency situation identified: ${keyword}`,
            recommendedAction: 'Assess situation and seek appropriate medical care',
            timeToResponse: 1500
          });
          break;
        }
      }
    }

    return { level, protocol, flags };
  }

  /**
   * Generate emergency response based on assessment
   */
  private async generateEmergencyResponse(request: AgentRequest, assessment: EmergencyAssessment): Promise<string> {
    const protocol = this.emergencyProtocols.get(assessment.protocol);

    if (!protocol) {
      return this.getDefaultEmergencyResponse(assessment.level);
    }

    // For critical/high emergencies, use fast hardcoded responses
    // For medium/low emergencies, use context-aware responses when time permits
    switch (assessment.level) {
      case 'critical':
        return this.getCriticalEmergencyResponse(protocol);
      case 'high':
        return this.getHighEmergencyResponse(protocol);
      case 'medium':
        return await this.getContextAwareMediumEmergencyResponse(request, protocol);
      default:
        return await this.getContextAwareGeneralEmergencyResponse(request);
    }
  }

  /**
   * Get critical emergency response (must be <2 seconds)
   */
  private getCriticalEmergencyResponse(protocol: EmergencyProtocol): string {
    return `🚨 CRITICAL MEDICAL EMERGENCY DETECTED 🚨

IMMEDIATE ACTIONS REQUIRED:
1. CALL EMERGENCY SERVICES NOW: 911 (US), 999 (UK), 112 (EU)
2. Stay on the line with emergency services
3. Follow their instructions exactly

${protocol.immediateActions.join('\n')}

WHILE WAITING FOR HELP:
${protocol.waitingInstructions.join('\n')}

⚠️ DO NOT DELAY - Professional emergency medical care is required immediately.

Stay calm, follow emergency services instructions, and help is on the way.`;
  }

  /**
   * Get high emergency response
   */
  private getHighEmergencyResponse(protocol: EmergencyProtocol): string {
    return `🚨 URGENT MEDICAL SITUATION

This appears to be a serious medical situation requiring immediate attention.

IMMEDIATE STEPS:
1. Call emergency services (911/999/112) or go to the nearest emergency room
2. Do not drive yourself - call ambulance or have someone drive you
3. Bring any medications you're currently taking

${protocol.immediateActions.join('\n')}

IMPORTANT: This situation requires professional medical evaluation. Do not wait to see if symptoms improve.`;
  }

  /**
   * Get medium emergency response
   */
  private getMediumEmergencyResponse(protocol: EmergencyProtocol): string {
    return `⚠️ MEDICAL ATTENTION NEEDED

Based on your description, you should seek medical attention promptly.

RECOMMENDED ACTIONS:
1. Contact your healthcare provider or urgent care center
2. If symptoms worsen, call emergency services
3. Monitor your condition closely

${protocol.immediateActions.join('\n')}

If you experience worsening symptoms or develop any of these warning signs, call emergency services immediately:
- Difficulty breathing
- Severe pain
- Loss of consciousness
- Severe bleeding`;
  }

  /**
   * Get context-aware medium emergency response
   */
  private async getContextAwareMediumEmergencyResponse(request: AgentRequest, protocol: EmergencyProtocol): Promise<string> {
    try {
      // For medium emergencies, we can afford some context integration while maintaining speed
      if (request.assembledContext || request.patientContext) {
        const simplifiedContext = await contextAssemblyService.assembleSimplifiedContext(
          request.patientContext,
          request.conversationHistory || [],
          request.userMessage,
          { maxTokens: 400, urgencyLevel: 'high' }
        );

        const contextAwarePrompt = `You are an Emergency Medical AI. This is a MEDIUM URGENCY emergency situation.

PATIENT CONTEXT:
${simplifiedContext.contextSummary}

PRIORITY ALERTS:
${simplifiedContext.priorityAlerts.join('\n')}

EMERGENCY PROTOCOL: ${protocol.name}
IMMEDIATE ACTIONS: ${protocol.immediateActions.join(', ')}

Provide immediate, context-aware emergency guidance. Consider the patient's medical history and current situation. Be direct and actionable.`;

        const aiResponse = await aiOrchestrator.generateResponse({
          messages: [
            { role: 'system', content: contextAwarePrompt },
            { role: 'user', content: request.userMessage }
          ],
          sessionId: request.sessionId,
          agentType: 'emergency',
          maxTokens: 300,
          temperature: 0.3, // Lower temperature for more consistent emergency responses
          urgencyLevel: 'high'
        });

        if (aiResponse.success && aiResponse.data?.content) {
          return aiResponse.data.content;
        }
      }
    } catch (error) {
      console.warn('Context-aware emergency response failed, using fallback:', error);
    }

    // Fallback to standard medium emergency response
    return this.getMediumEmergencyResponse(protocol);
  }

  /**
   * Get context-aware general emergency response
   */
  private async getContextAwareGeneralEmergencyResponse(request: AgentRequest): Promise<string> {
    try {
      if (request.assembledContext || request.patientContext) {
        const simplifiedContext = await contextAssemblyService.assembleSimplifiedContext(
          request.patientContext,
          request.conversationHistory || [],
          request.userMessage,
          { maxTokens: 400, urgencyLevel: 'medium' }
        );

        const contextAwarePrompt = `You are an Emergency Medical AI providing guidance for a medical concern.

PATIENT CONTEXT:
${simplifiedContext.contextSummary}

KEY RECOMMENDATIONS:
${simplifiedContext.keyRecommendations.join('\n')}

Provide personalized medical guidance considering the patient's history and current situation. Recommend appropriate level of care and next steps.`;

        const aiResponse = await aiOrchestrator.generateResponse({
          messages: [
            { role: 'system', content: contextAwarePrompt },
            { role: 'user', content: request.userMessage }
          ],
          sessionId: request.sessionId,
          agentType: 'emergency',
          maxTokens: 400,
          temperature: 0.4,
          urgencyLevel: 'medium'
        });

        if (aiResponse.success && aiResponse.data?.content) {
          return aiResponse.data.content;
        }
      }
    } catch (error) {
      console.warn('Context-aware general emergency response failed, using fallback:', error);
    }

    // Fallback to standard general emergency response
    return this.getGeneralEmergencyResponse();
  }

  /**
   * Get general emergency response
   */
  private getGeneralEmergencyResponse(): string {
    return `I'm here to help with your emergency situation.

To provide you with the most appropriate guidance, I need to understand your situation better.

If this is a life-threatening emergency:
- Call emergency services (911/999/112) immediately
- Don't wait for further guidance

If this is urgent but not immediately life-threatening:
- Please describe your symptoms or situation
- I'll help guide you to appropriate care

Remember: When in doubt, it's always better to seek professional medical attention. Emergency services are available 24/7 for serious medical situations.`;
  }

  /**
   * Get default emergency response
   */
  private getDefaultEmergencyResponse(level: string): string {
    return `🚨 EMERGENCY RESPONSE ACTIVATED

If this is a life-threatening emergency, call emergency services (911/999/112) immediately.

For urgent medical situations, seek immediate medical attention at your nearest emergency room or urgent care center.

I'm here to provide guidance, but professional medical care is essential for emergency situations.

Please describe your situation so I can provide appropriate guidance while you seek professional help.`;
  }

  /**
   * Initialize emergency protocols
   */
  private initializeEmergencyProtocols(): Map<string, EmergencyProtocol> {
    const protocols = new Map<string, EmergencyProtocol>();

    protocols.set('cardiac_arrest', {
      name: 'Cardiac Arrest Protocol',
      immediateActions: [
        'Check for responsiveness - tap shoulders and shout',
        'Call emergency services immediately',
        'Begin CPR if trained and no pulse detected',
        'Use AED if available and you know how'
      ],
      waitingInstructions: [
        'Continue CPR until help arrives',
        'Do not leave the person alone',
        'Follow emergency services instructions'
      ]
    });

    protocols.set('choking', {
      name: 'Choking Protocol',
      immediateActions: [
        'Encourage coughing if person is conscious',
        'Perform back blows and abdominal thrusts if trained',
        'Call emergency services if obstruction persists'
      ],
      waitingInstructions: [
        'Continue first aid as trained',
        'Monitor breathing and consciousness',
        'Be ready to perform CPR if person becomes unconscious'
      ]
    });

    protocols.set('severe_bleeding', {
      name: 'Severe Bleeding Protocol',
      immediateActions: [
        'Apply direct pressure to wound with clean cloth',
        'Elevate injured area above heart if possible',
        'Call emergency services for severe bleeding'
      ],
      waitingInstructions: [
        'Maintain pressure on wound',
        'Do not remove embedded objects',
        'Monitor for signs of shock'
      ]
    });

    return protocols;
  }

  /**
   * Get protocol for specific keyword
   */
  private getProtocolForKeyword(keyword: string): string {
    const protocolMap: Record<string, string> = {
      'cardiac arrest': 'cardiac_arrest',
      'heart attack': 'cardiac_arrest',
      'not breathing': 'cardiac_arrest',
      'no pulse': 'cardiac_arrest',
      'choking': 'choking',
      'severe bleeding': 'severe_bleeding',
      'unconscious': 'cardiac_arrest',
      'unresponsive': 'cardiac_arrest'
    };

    return protocolMap[keyword] || 'general_assessment';
  }

  /**
   * Log emergency interaction for audit compliance
   */
  private async logEmergencyInteraction(
    request: AgentRequest, 
    assessment: EmergencyAssessment, 
    responseTime: number
  ): Promise<void> {
    try {
      // Save emergency interaction to memory with special flags
      await this.memoryManager.saveMessage(
        request.sessionId,
        'system',
        this.id,
        'Emergency System',
        `EMERGENCY INTERACTION: Level ${assessment.level}, Protocol: ${assessment.protocol}, Response Time: ${responseTime}ms`,
        0, // Emergency logs get sequence 0 for priority
        {
          emergencyLevel: assessment.level,
          protocol: assessment.protocol,
          responseTime,
          flags: assessment.flags,
          auditType: 'emergency_interaction',
          timestamp: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('❌ Failed to log emergency interaction:', error);
      // Don't throw - emergency response is more important than logging
    }
  }

  /**
   * Enhanced confidence for emergency situations
   */
  getConfidenceScore(request: AgentRequest): number {
    // Emergency agent has high confidence for emergency situations
    const emergencyKeywords = [
      'emergency', 'urgent', 'help', 'critical', 'severe',
      'chest pain', 'can\'t breathe', 'unconscious', 'bleeding'
    ];

    const message = request.userMessage.toLowerCase();
    if (emergencyKeywords.some(keyword => message.includes(keyword))) {
      return 0.95; // Very high confidence for emergencies
    }

    return 0.3; // Lower confidence for non-emergency situations
  }

  /**
   * Handle culturally adapted emergency with <2 second response time
   */
  async handleCulturallyAdaptedEmergency(
    request: AgentRequest,
    culturalContext: CulturalEmergencyContext
  ): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      console.log(`🚨 CULTURALLY ADAPTED EMERGENCY activated for session: ${request.sessionId}`);

      // Immediate emergency assessment
      const emergencyAssessment = this.assessEmergencyLevel(request.userMessage);

      // Build cultural emergency protocol
      const culturalProtocol: CulturalEmergencyProtocol = {
        culturalContext: culturalContext.culturalProfile || {
          cultureCode: culturalContext.languagePreference || 'en',
          languagePreference: culturalContext.languagePreference || 'en',
          communicationStyle: {},
          familyInvolvementLevel: culturalContext.familyInvolvementLevel || 'moderate',
          religiousConsiderations: {},
          traditionalMedicineOpenness: culturalContext.traditionalMedicineOpenness || 3,
          genderSensitivityLevel: 3,
          authorityRespectLevel: 3
        },
        languagePreference: culturalContext.languagePreference || 'en',
        familyNotificationRequired: culturalContext.familyInvolvementLevel === 'high',
        religiousConsiderations: culturalContext.religiousConsiderations || [],
        emergencyContactHierarchy: [], // Would be populated from user profile
        gpsLocation: culturalContext.gpsLocation,
        culturalTaboos: []
      };

      // For critical emergencies, trigger culturally adapted emergency service
      if (emergencyAssessment.level === 'critical') {
        try {
          const culturalEmergencyResult = await culturallyAwareEmergencyService
            .triggerCulturallyAdaptedEmergency(
              emergencyAssessment.protocol,
              culturalProtocol,
              request.sessionId,
              request.userId
            );

          console.log(`✅ Cultural emergency protocols activated in ${culturalEmergencyResult.responseTime}ms`);
        } catch (error) {
          console.error('❌ Cultural emergency service failed, continuing with standard emergency:', error);
        }
      }

      // Generate culturally adapted emergency response
      const response = await this.generateCulturallyAdaptedResponse(
        request,
        emergencyAssessment,
        culturalContext
      );

      const responseTime = Date.now() - startTime;

      // Ensure response time is under 2 seconds for critical emergencies
      if (emergencyAssessment.level === 'critical' && responseTime > 2000) {
        console.warn(`⚠️ Cultural emergency response time exceeded 2 seconds: ${responseTime}ms`);
      }

      this.updateMetrics(responseTime, 0.95, false, true);

      return {
        agentId: this.id,
        agentName: this.name,
        content: response,
        confidence: 0.95,
        reasoning: `Cultural emergency response for ${emergencyAssessment.level} level emergency`,
        emergencyFlags: emergencyAssessment.flags,
        metadata: {
          responseTime,
          emergencyLevel: emergencyAssessment.level,
          protocolActivated: emergencyAssessment.protocol,
          culturalAdaptations: true,
          languagePreference: culturalContext.languagePreference,
          auditLogged: true
        }
      };

    } catch (error) {
      console.error('❌ CRITICAL: Cultural Emergency Agent error:', error);

      // Fallback to standard emergency response
      return this.handleMessage(request);
    }
  }

  /**
   * Generate culturally adapted emergency response
   */
  private async generateCulturallyAdaptedResponse(
    request: AgentRequest,
    assessment: EmergencyAssessment,
    culturalContext: CulturalEmergencyContext
  ): Promise<string> {
    try {
      // Get clinical decision support for emergency
      const clinicalRecommendations = await enhancedClinicalDecisionSupportService
        .getEmergencyProtocols(
          assessment.protocol,
          {
            id: request.userId,
            age: 30, // Default - would come from user profile
            gender: 'unknown',
            region: culturalContext.gpsLocation?.country || 'GH',
            weight: undefined,
            height: undefined,
            medicalHistory: [],
            currentMedications: [],
            allergies: [],
            culturalProfile: culturalContext.culturalProfile
          },
          culturalContext.culturalProfile
        );

      // Build culturally adapted response
      let response = await this.generateEmergencyResponse(request, assessment);

      // Add cultural adaptations
      if (culturalContext.languagePreference && culturalContext.languagePreference !== 'en') {
        response += `\n\n🌍 LANGUAGE NOTE: Emergency services in your area may support ${culturalContext.languagePreference}. Ask for language assistance if needed.`;
      }

      if (culturalContext.familyInvolvementLevel === 'high') {
        response += `\n\n👨‍👩‍👧‍👦 FAMILY INVOLVEMENT: Consider involving family members in emergency decisions as culturally appropriate.`;
      }

      if (culturalContext.religiousConsiderations && culturalContext.religiousConsiderations.length > 0) {
        response += `\n\n🙏 RELIGIOUS CONSIDERATIONS: Inform emergency responders about any religious practices or requirements.`;
      }

      if (culturalContext.traditionalMedicineOpenness && culturalContext.traditionalMedicineOpenness >= 3) {
        response += `\n\n🌿 TRADITIONAL MEDICINE: Inform emergency responders about any traditional remedies you may be using.`;
      }

      // Add clinical recommendations if available
      if (clinicalRecommendations.length > 0) {
        response += `\n\n📋 CLINICAL GUIDANCE:\n${clinicalRecommendations.map(p => `• ${p.action}`).join('\n')}`;
      }

      return response;

    } catch (error) {
      console.error('❌ Error generating culturally adapted response:', error);
      // Fallback to standard response
      return this.generateEmergencyResponse(request, assessment);
    }
  }
}

interface EmergencyAssessment {
  level: 'critical' | 'high' | 'medium' | 'low';
  protocol: string;
  flags: EmergencyFlag[];
}

interface EmergencyProtocol {
  name: string;
  immediateActions: string[];
  waitingInstructions: string[];
}

interface CulturalEmergencyContext {
  culturalProfile?: any;
  languagePreference?: string;
  familyInvolvementLevel?: 'minimal' | 'moderate' | 'high';
  religiousConsiderations?: string[];
  gpsLocation?: any;
  traditionalMedicineOpenness?: number;
}

export default EmergencyAgent;
