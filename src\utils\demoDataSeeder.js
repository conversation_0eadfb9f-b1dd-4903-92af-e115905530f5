import { createClient } from '@supabase/supabase-js';

/**
 * Demo Data Seeder for VoiceHealth AI
 * Creates demo accounts for testing purposes
 */

class DemoDataSeeder {
  constructor() {
    this.supabaseAdmin = null;
    this.initialized = false;
    
    this.demoAccounts = [
      {
        email: '<EMAIL>',
        password: 'HealthDemo123',
        full_name: 'Demo Patient',
        role: 'patient',
        phone: '+**********',
        date_of_birth: '1990-01-15'
      },
      {
        email: '<EMAIL>', 
        password: 'ProviderDemo123',
        full_name: 'Dr. Demo Provider',
        role: 'provider',
        phone: '+**********',
        specialization: 'general_practitioner',
        license_number: 'MD123456789'
      },
      {
        email: '<EMAIL>',
        password: 'AdminDemo123', 
        full_name: 'Admin Demo User',
        role: 'admin',
        phone: '+**********'
      }
    ];
  }

  /**
   * Initialize Supabase clients (lazy initialization)
   */
  initialize() {
    if (this.initialized) return;
    
    if (!process.env.VITE_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('Missing required environment variables: VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
    }
    
    // Use service role client for admin operations
    this.supabaseAdmin = createClient(
      process.env.VITE_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    this.initialized = true;
  }

  /**
   * Seed all demo accounts
   */
  async seedDemoAccounts() {
    this.initialize();
    console.log('🌱 Starting demo account seeding...');
    
    const results = [];
    
    for (const account of this.demoAccounts) {
      try {
        console.log(`Creating ${account.role} account: ${account.email}`);
        const result = await this.createDemoAccount(account);
        results.push({ ...account, ...result });
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`❌ Failed to create ${account.email}:`, error.message);
        results.push({ 
          ...account, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    console.log('🌱 Demo account seeding completed!');
    return results;
  }

  /**
   * Create a single demo account
   */
  async createDemoAccount(accountData) {
    this.initialize();
    const { email, password, full_name, role, phone, date_of_birth, specialization, license_number } = accountData;

    // Check if user already exists
    const { data: existingUser, error: checkError } = await this.supabaseAdmin.auth.admin.getUserByEmail(email);
    
    if (existingUser && !checkError) {
      console.log(`ℹ️  User ${email} already exists, updating profile...`);
      
      // Update existing user profile
      const { data: updateData, error: updateError } = await this.supabaseAdmin
        .from('user_profiles')
        .update({
          full_name,
          role,
          phone,
          date_of_birth,
          specialization,
          license_number,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)
        .select();

      if (updateError) {
        throw new Error(`Failed to update profile: ${updateError.message}`);
      }

      return { 
        success: true, 
        message: 'Updated existing account',
        userId: existingUser.id,
        profileData: updateData?.[0]
      };
    }

    // Create new user with admin API
    const { data: userData, error: userError } = await this.supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email for demo accounts
      user_metadata: {
        full_name,
        role
      }
    });

    if (userError) {
      throw new Error(`Failed to create user: ${userError.message}`);
    }

    // Create user profile
    const profileData = {
      id: userData.user.id,
      email,
      full_name,
      role,
      phone,
      date_of_birth,
      specialization,
      license_number,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: profileResult, error: profileError } = await this.supabaseAdmin
      .from('user_profiles')
      .insert(profileData)
      .select();

    if (profileError) {
      // If profile creation fails, try to delete the user to avoid orphaned records
      await this.supabaseAdmin.auth.admin.deleteUser(userData.user.id);
      throw new Error(`Failed to create profile: ${profileError.message}`);
    }

    return { 
      success: true, 
      message: 'Created new account',
      userId: userData.user.id,
      profileData: profileResult?.[0]
    };
  }

  /**
   * Remove all demo accounts (cleanup)
   */
  async cleanupDemoAccounts() {
    this.initialize();
    console.log('🧹 Cleaning up demo accounts...');
    
    const results = [];
    
    for (const account of this.demoAccounts) {
      try {
        const { data: user, error } = await this.supabaseAdmin.auth.admin.getUserByEmail(account.email);
        
        if (user && !error) {
          // Delete user profile first
          await this.supabaseAdmin
            .from('user_profiles')
            .delete()
            .eq('id', user.id);
          
          // Delete auth user
          await this.supabaseAdmin.auth.admin.deleteUser(user.id);
          
          console.log(`✅ Deleted ${account.email}`);
          results.push({ email: account.email, deleted: true });
        } else {
          console.log(`ℹ️  User ${account.email} not found`);
          results.push({ email: account.email, deleted: false, reason: 'not found' });
        }
      } catch (error) {
        console.error(`❌ Failed to delete ${account.email}:`, error.message);
        results.push({ 
          email: account.email, 
          deleted: false, 
          error: error.message 
        });
      }
    }
    
    console.log('🧹 Demo account cleanup completed!');
    return results;
  }

  /**
   * Test login for all demo accounts
   */
  async testDemoLogins() {
    this.initialize();
    console.log('🧪 Testing demo account logins...');
    
    if (!process.env.VITE_SUPABASE_ANON_KEY) {
      throw new Error('Missing required environment variable: VITE_SUPABASE_ANON_KEY');
    }
    
    // Create a regular supabase client for testing
    const testClient = createClient(
      process.env.VITE_SUPABASE_URL,
      process.env.VITE_SUPABASE_ANON_KEY
    );
    
    const results = [];
    
    for (const account of this.demoAccounts) {
      try {
        const { data, error } = await testClient.auth.signInWithPassword({
          email: account.email,
          password: account.password
        });
        
        if (error) {
          throw error;
        }
        
        // Get user profile
        const { data: profile, error: profileError } = await testClient
          .from('user_profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();
        
        // Sign out
        await testClient.auth.signOut();
        
        console.log(`✅ Login successful for ${account.email} (${profile?.role})`);
        results.push({ 
          email: account.email, 
          loginSuccess: true,
          role: profile?.role,
          profile: profile
        });
        
      } catch (error) {
        console.error(`❌ Login failed for ${account.email}:`, error.message);
        results.push({ 
          email: account.email, 
          loginSuccess: false,
          error: error.message 
        });
      }
    }
    
    console.log('🧪 Demo login testing completed!');
    return results;
  }
}

// Export singleton instance
const demoDataSeeder = new DemoDataSeeder();
export default demoDataSeeder;

// Helper functions for easy access
export const seedDemoAccounts = () => demoDataSeeder.seedDemoAccounts();
export const testDemoLogins = () => demoDataSeeder.testDemoLogins(); 
export const cleanupDemoAccounts = () => demoDataSeeder.cleanupDemoAccounts();
