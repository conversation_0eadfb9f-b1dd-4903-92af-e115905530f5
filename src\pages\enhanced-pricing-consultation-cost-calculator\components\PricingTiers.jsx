import React, { useState } from 'react';
import { <PERSON>, CheckCircle, Zap, Clock, Brain } from 'lucide-react';
import Button from '../../../components/ui/Button';

const PricingTiers = ({ currency }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  const pricingTiers = [
    {
      id: 'basic',
      name: 'Basic Care',
      description: 'Essential health consultations for individuals',
      icon: '🌱',
      prices: {
        monthly: { NGN: 12000, USD: 25 },
        yearly: { NGN: 120000, USD: 250 }
      },
      consultationMinutes: 100,
      agentAccess: 'Single AI Agent',
      features: [
        'Up to 100 consultation minutes',
        'General health consultations',
        'Basic AI health assessment',
        'Session transcripts',
        'Mobile app access',
        'Email support'
      ],
      limitations: [
        'No specialist consultations',
        'No premium features',
        'Limited session recording'
      ],
      popular: false,
      color: 'blue'
    },
    {
      id: 'premium',
      name: 'Premium Health',
      description: 'Advanced care with specialist access',
      icon: '⭐',
      prices: {
        monthly: { NGN: 25000, USD: 50 },
        yearly: { NGN: 250000, USD: 500 }
      },
      consultationMinutes: 300,
      agentAccess: 'Multi-Agent Collaboration',
      features: [
        'Up to 300 consultation minutes',
        'All consultation types including specialist',
        'Multi-agent AI collaboration',
        'Prescription generation',
        'Session recording & playback',
        'AI-generated health reports',
        'Priority support',
        'Family member access (up to 2)'
      ],
      limitations: [
        'Limited to 2 family members'
      ],
      popular: true,
      color: 'purple'
    },
    {
      id: 'family',
      name: 'Family Care',
      description: 'Comprehensive health management for families',
      icon: '👨‍👩‍👧‍👦',
      prices: {
        monthly: { NGN: 45000, USD: 90 },
        yearly: { NGN: 450000, USD: 900 }
      },
      consultationMinutes: 600,
      agentAccess: 'Full Specialist Network',
      features: [
        'Up to 600 consultation minutes',
        'All consultation types',
        'Full AI specialist network access',
        'Unlimited family members',
        'Shared credit pool',
        'Advanced health analytics',
        'Emergency consultation priority',
        'Custom health plans',
        'Dedicated account manager',
        '24/7 priority support'
      ],
      limitations: [],
      popular: false,
      color: 'green'
    }
  ];

  const formatPrice = (price, currency) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const calculateYearlySavings = (monthlyPrice, yearlyPrice) => {
    const monthlyCost = monthlyPrice * 12;
    const savings = monthlyCost - yearlyPrice;
    const percentage = Math.round((savings / monthlyCost) * 100);
    return { savings, percentage };
  };

  const getColorClasses = (color, isPopular) => {
    const colors = {
      blue: {
        border: isPopular ? 'border-blue-500' : 'border-blue-200',
        bg: 'bg-blue-50',
        text: 'text-blue-700',
        button: 'bg-blue-600 hover:bg-blue-700'
      },
      purple: {
        border: isPopular ? 'border-purple-500' : 'border-purple-200',
        bg: 'bg-purple-50',
        text: 'text-purple-700',
        button: 'bg-purple-600 hover:bg-purple-700'
      },
      green: {
        border: isPopular ? 'border-green-500' : 'border-green-200',
        bg: 'bg-green-50',
        text: 'text-green-700',
        button: 'bg-green-600 hover:bg-green-700'
      }
    };
    return colors[color];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Choose Your Health Plan
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Get access to AI-powered health consultations with transparent pricing and no hidden fees.
        </p>
      </div>

      {/* Billing Period Toggle */}
      <div className="flex justify-center">
        <div className="bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setSelectedPeriod('monthly')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === 'monthly' ?'bg-white text-gray-900 shadow-sm' :'text-gray-600 hover:text-gray-900'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setSelectedPeriod('yearly')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === 'yearly' ?'bg-white text-gray-900 shadow-sm' :'text-gray-600 hover:text-gray-900'
            }`}
          >
            Yearly
            <span className="ml-1 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
              Save up to 17%
            </span>
          </button>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {pricingTiers.map((tier) => {
          const colorClasses = getColorClasses(tier.color, tier.popular);
          const currentPrice = tier.prices[selectedPeriod][currency];
          const savings = selectedPeriod === 'yearly' 
            ? calculateYearlySavings(tier.prices.monthly[currency], tier.prices.yearly[currency])
            : null;

          return (
            <div
              key={tier.id}
              className={`relative bg-white rounded-lg shadow-sm border-2 transition-all duration-200 hover:shadow-lg ${
                tier.popular ? colorClasses.border + ' ring-2 ring-opacity-20' : 'border-gray-200'
              } ${tier.popular ? 'scale-105' : ''}`}
            >
              {/* Popular Badge */}
              {tier.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className={`${colorClasses.bg} ${colorClasses.text} px-3 py-1 rounded-full text-xs font-medium flex items-center border`}>
                    <Star size={12} className="mr-1" />
                    Most Popular
                  </div>
                </div>
              )}

              <div className="p-6">
                {/* Plan Header */}
                <div className="text-center mb-6">
                  <div className="text-3xl mb-2">{tier.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {tier.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {tier.description}
                  </p>
                  
                  {/* Price */}
                  <div className="mb-2">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatPrice(currentPrice, currency)}
                    </span>
                    <span className="text-gray-600 ml-1">
                      /{selectedPeriod === 'yearly' ? 'year' : 'month'}
                    </span>
                  </div>
                  
                  {/* Yearly Savings */}
                  {selectedPeriod === 'yearly' && savings && (
                    <p className="text-sm text-green-600 font-medium">
                      Save {formatPrice(savings.savings, currency)} ({savings.percentage}%)
                    </p>
                  )}
                  
                  {/* Consultation Minutes */}
                  <div className="flex items-center justify-center mt-2 text-sm text-gray-600">
                    <Clock size={14} className="mr-1" />
                    {tier.consultationMinutes} minutes/month
                  </div>
                </div>

                {/* Agent Access */}
                <div className={`${colorClasses.bg} rounded-lg p-3 mb-6`}>
                  <div className="flex items-center justify-center">
                    <Brain size={16} className={`${colorClasses.text} mr-2`} />
                    <span className={`font-medium ${colorClasses.text} text-sm`}>
                      {tier.agentAccess}
                    </span>
                  </div>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3 text-sm">
                    What's included:
                  </h4>
                  <ul className="space-y-2">
                    {tier.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle size={16} className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                        <span className="text-sm text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Button */}
                <Button
                  className={`w-full ${colorClasses.button} text-white`}
                  onClick={() => {
                    console.log('Selected plan:', tier.id);
                    // Navigate to payment
                  }}
                >
                  <Zap size={16} className="mr-2" />
                  Choose {tier.name}
                </Button>

                {/* Per-minute cost */}
                <div className="mt-3 text-center">
                  <p className="text-xs text-gray-500">
                    {formatPrice(currentPrice / tier.consultationMinutes, currency)} per minute
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Feature Comparison */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-6 text-center">
          Feature Comparison
        </h3>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Features</th>
                {pricingTiers.map(tier => (
                  <th key={tier.id} className="text-center py-3 px-4 font-medium text-gray-900">
                    {tier.name}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {[
                { feature: 'Consultation Minutes', values: ['100/month', '300/month', '600/month'] },
                { feature: 'AI Agent Access', values: ['Single', 'Multi-Agent', 'Full Network'] },
                { feature: 'Specialist Consultations', values: ['❌', '✅', '✅'] },
                { feature: 'Session Recording', values: ['Basic', '✅', '✅'] },
                { feature: 'Prescription Generation', values: ['❌', '✅', '✅'] },
                { feature: 'Family Members', values: ['1', '2', 'Unlimited'] },
                { feature: 'Emergency Priority', values: ['❌', '❌', '✅'] },
                { feature: 'Account Manager', values: ['❌', '❌', '✅'] }
              ].map((row, index) => (
                <tr key={index}>
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">{row.feature}</td>
                  {row.values.map((value, idx) => (
                    <td key={idx} className="py-3 px-4 text-sm text-center text-gray-600">
                      {value}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* FAQ */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Frequently Asked Questions
        </h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              Can I change plans anytime?
            </h4>
            <p className="text-sm text-gray-600">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect in your next billing cycle.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              What happens to unused minutes?
            </h4>
            <p className="text-sm text-gray-600">
              Unused consultation minutes roll over for up to 3 months, giving you flexibility in managing your health consultations.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              Is there a free trial?
            </h4>
            <p className="text-sm text-gray-600">
              We offer a 7-day free trial with 30 consultation minutes to help you experience our AI-powered health consultations.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingTiers;