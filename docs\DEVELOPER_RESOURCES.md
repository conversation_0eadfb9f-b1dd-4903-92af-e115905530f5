# VoiceHealth AI - Developer Resources

## Overview

This document provides comprehensive developer resources for working with VoiceHealth AI's enhanced features, including code examples, best practices, troubleshooting guides, and development tools.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Code Examples and Patterns](#code-examples-and-patterns)
3. [Testing Frameworks and Tools](#testing-frameworks-and-tools)
4. [Debugging and Troubleshooting](#debugging-and-troubleshooting)
5. [Performance Optimization](#performance-optimization)
6. [Security Best Practices](#security-best-practices)
7. [HIPAA Compliance Guidelines](#hipaa-compliance-guidelines)
8. [Contributing Guidelines](#contributing-guidelines)

---

## Development Environment Setup

### Prerequisites

```bash
# Required software versions
Node.js >= 18.0.0
npm >= 8.0.0
TypeScript >= 4.9.0
Vite >= 4.0.0

# Optional but recommended
Docker >= 20.0.0
Git >= 2.30.0
```

### Local Development Setup

```bash
# Clone repository
git clone https://github.com/voicehealth/ai.git
cd voicehealth-ai

# Install dependencies
npm install

# Set up environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Set up pre-commit hooks
npm run setup:hooks

# Start development server
npm run dev

# Run tests
npm run test
npm run test:integration
npm run test:performance
```

### Development Tools Configuration

```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.test.ts": "typescript",
    "*.spec.ts": "typescript"
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ]
}
```

### Recommended VS Code Extensions

```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.test-adapter-converter"
  ]
}
```

---

## Code Examples and Patterns

### 1. Service Implementation Pattern

```typescript
// src/services/ExampleService.ts
import { performanceMonitoringService } from './PerformanceMonitoringService';
import { errorSanitizationService } from './ErrorSanitizationService';
import { auditLogger } from '../utils/auditLogger';

export interface ExampleServiceConfig {
  timeout: number;
  retryAttempts: number;
  enableMonitoring: boolean;
}

export class ExampleService {
  private config: ExampleServiceConfig;
  private isInitialized = false;

  constructor(config: ExampleServiceConfig) {
    this.config = config;
    console.log('🔧 ExampleService initialized');
  }

  /**
   * Example method with full monitoring and error handling
   */
  async performOperation(
    operationData: any,
    context: { sessionId?: string; userId?: string } = {}
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    return await performanceMonitoringService.measurePerformance(
      'example_service',
      'perform_operation',
      async () => {
        try {
          // Validate input
          this.validateInput(operationData);

          // Perform operation
          const result = await this.executeOperation(operationData);

          // Audit log success
          await this.auditOperation('success', context, { result });

          return {
            success: true,
            data: result
          };

        } catch (error) {
          // Sanitize error
          const sanitizedError = errorSanitizationService.sanitizeError(
            error instanceof Error ? error : new Error(String(error)),
            {
              ...context,
              operation: 'perform_operation',
              component: 'ExampleService'
            }
          );

          // Audit log failure
          await this.auditOperation('failure', context, {
            errorId: sanitizedError.id
          });

          return {
            success: false,
            error: sanitizedError.userMessage
          };
        }
      },
      { operationData: typeof operationData },
      context.sessionId,
      context.userId
    );
  }

  private validateInput(data: any): void {
    if (!data) {
      throw new Error('Operation data is required');
    }
    // Add specific validation logic
  }

  private async executeOperation(data: any): Promise<any> {
    // Implement actual operation logic
    return { processed: true, data };
  }

  private async auditOperation(
    result: 'success' | 'failure',
    context: any,
    details: any
  ): Promise<void> {
    try {
      await auditLogger.logServiceOperation({
        service: 'ExampleService',
        operation: 'perform_operation',
        result,
        context,
        details,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to audit operation:', error);
    }
  }
}

// Export singleton instance
export const exampleService = new ExampleService({
  timeout: 5000,
  retryAttempts: 3,
  enableMonitoring: true
});
```

### 2. React Component Pattern

```tsx
// src/components/ExampleComponent.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { exampleService } from '../services/ExampleService';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { usePerformanceTracking } from '../hooks/usePerformanceTracking';

interface ExampleComponentProps {
  sessionId: string;
  userId: string;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export const ExampleComponent: React.FC<ExampleComponentProps> = ({
  sessionId,
  userId,
  onSuccess,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const { handleError } = useErrorHandler('ExampleComponent');
  const { trackOperation } = usePerformanceTracking();

  const performOperation = useCallback(async (operationData: any) => {
    setIsLoading(true);
    setError('');

    try {
      const result = await trackOperation(
        'example_component',
        'perform_operation',
        () => exampleService.performOperation(operationData, { sessionId, userId })
      );

      if (result.success) {
        setData(result.data);
        onSuccess?.(result.data);
      } else {
        const errorMessage = result.error || 'Operation failed';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (error) {
      const handledError = await handleError(error, {
        sessionId,
        userId,
        operation: 'perform_operation'
      });
      setError(handledError.error);
      onError?.(handledError.error);
    } finally {
      setIsLoading(false);
    }
  }, [sessionId, userId, onSuccess, onError, handleError, trackOperation]);

  return (
    <div className="example-component">
      <div className="component-header">
        <h3>Example Component</h3>
        <div className="status-indicators">
          {isLoading && <span className="loading">⏳ Loading...</span>}
          {error && <span className="error">❌ {error}</span>}
          {data && <span className="success">✅ Success</span>}
        </div>
      </div>

      <div className="component-content">
        {data ? (
          <div className="data-display">
            <pre>{JSON.stringify(data, null, 2)}</pre>
          </div>
        ) : (
          <div className="no-data">
            No data available
          </div>
        )}
      </div>

      <div className="component-actions">
        <button
          onClick={() => performOperation({ test: true })}
          disabled={isLoading}
          className="btn btn-primary"
        >
          {isLoading ? 'Processing...' : 'Perform Operation'}
        </button>
      </div>
    </div>
  );
};
```

### 3. Custom Hooks Pattern

```typescript
// src/hooks/useErrorHandler.ts
import { useCallback } from 'react';
import { errorSanitizationService } from '../services/ErrorSanitizationService';

export function useErrorHandler(component: string) {
  const handleError = useCallback(async (
    error: Error | string,
    context: {
      sessionId?: string;
      userId?: string;
      operation?: string;
    }
  ) => {
    const sanitizedError = errorSanitizationService.sanitizeError(
      error instanceof Error ? error : new Error(String(error)),
      {
        ...context,
        component
      }
    );

    console.error(`[${sanitizedError.id}] ${sanitizedError.technicalMessage}`);

    return {
      success: false,
      error: sanitizedError.userMessage,
      errorId: sanitizedError.id
    };
  }, [component]);

  return { handleError };
}

// src/hooks/usePerformanceTracking.ts
import { useCallback } from 'react';
import { performanceMonitoringService } from '../services/PerformanceMonitoringService';

export function usePerformanceTracking() {
  const trackOperation = useCallback(async <T>(
    feature: string,
    operation: string,
    operationFn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> => {
    return await performanceMonitoringService.measurePerformance(
      feature,
      operation,
      operationFn,
      metadata
    );
  }, []);

  return { trackOperation };
}

// src/hooks/useRealTimeUpdates.ts
import { useState, useEffect } from 'react';
import { realTimeAgentCommunication } from '../services/RealTimeAgentCommunication';

export function useRealTimeUpdates(sessionId: string) {
  const [goalProgress, setGoalProgress] = useState<any[]>([]);
  const [steeringGuidance, setSteeringGuidance] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const handleGoalProgress = (data: any) => {
      if (data.sessionId === sessionId) {
        setGoalProgress(data.data.goals || []);
      }
    };

    const handleSteeringGuidance = (data: any) => {
      if (data.sessionId === sessionId) {
        setSteeringGuidance(data.data.steeringGuidance || []);
      }
    };

    const handleConnectionStatus = (status: boolean) => {
      setIsConnected(status);
    };

    realTimeAgentCommunication.on('goal-progress-update', handleGoalProgress);
    realTimeAgentCommunication.on('steering-guidance-update', handleSteeringGuidance);
    realTimeAgentCommunication.on('connection-status', handleConnectionStatus);

    return () => {
      realTimeAgentCommunication.off('goal-progress-update', handleGoalProgress);
      realTimeAgentCommunication.off('steering-guidance-update', handleSteeringGuidance);
      realTimeAgentCommunication.off('connection-status', handleConnectionStatus);
    };
  }, [sessionId]);

  return {
    goalProgress,
    steeringGuidance,
    isConnected
  };
}
```

---

## Testing Frameworks and Tools

### 1. Unit Testing with Vitest

```typescript
// src/services/__tests__/ExampleService.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ExampleService } from '../ExampleService';

// Mock dependencies
vi.mock('../PerformanceMonitoringService');
vi.mock('../ErrorSanitizationService');
vi.mock('../../utils/auditLogger');

describe('ExampleService', () => {
  let service: ExampleService;

  beforeEach(() => {
    service = new ExampleService({
      timeout: 5000,
      retryAttempts: 3,
      enableMonitoring: true
    });
  });

  describe('performOperation', () => {
    it('should successfully perform operation with valid data', async () => {
      const operationData = { test: true };
      const context = { sessionId: 'test-session', userId: 'test-user' };

      const result = await service.performOperation(operationData, context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should handle errors gracefully', async () => {
      const invalidData = null;
      const context = { sessionId: 'test-session', userId: 'test-user' };

      const result = await service.performOperation(invalidData, context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.data).toBeUndefined();
    });

    it('should track performance metrics', async () => {
      const operationData = { test: true };
      const context = { sessionId: 'test-session', userId: 'test-user' };

      await service.performOperation(operationData, context);

      // Verify performance monitoring was called
      // This would depend on your mocking setup
    });
  });
});
```

### 2. Integration Testing

```typescript
// tests/integration/consultation.integration.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { setupTestEnvironment, cleanupTestEnvironment } from './helpers/testSetup';
import { agentOrchestrator } from '../../src/services/AgentOrchestrator';

describe('Consultation Integration Tests', () => {
  let testSessionId: string;
  let testUserId: string;

  beforeEach(async () => {
    const testEnv = await setupTestEnvironment();
    testSessionId = testEnv.sessionId;
    testUserId = testEnv.userId;
  });

  afterEach(async () => {
    await cleanupTestEnvironment(testSessionId);
  });

  it('should handle complete consultation flow', async () => {
    const response = await agentOrchestrator.processRequest({
      sessionId: testSessionId,
      userMessage: 'I have a headache',
      userId: testUserId,
      urgencyLevel: 'medium'
    });

    expect(response).toBeDefined();
    expect(response.response).toBeDefined();
    expect(response.agentId).toBeDefined();
    expect(response.metadata).toBeDefined();
  });

  it('should maintain performance requirements', async () => {
    const startTime = Date.now();

    const response = await agentOrchestrator.processRequest({
      sessionId: testSessionId,
      userMessage: 'EMERGENCY: chest pain',
      userId: testUserId,
      urgencyLevel: 'critical'
    });

    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(2000); // <2 seconds
    expect(response.metadata.emergencyFlags).toBeDefined();
  });
});
```

### 3. Performance Testing

```typescript
// tests/performance/load.test.ts
import { describe, it, expect } from 'vitest';
import { agentOrchestrator } from '../../src/services/AgentOrchestrator';

describe('Performance Load Tests', () => {
  it('should handle concurrent requests efficiently', async () => {
    const concurrentRequests = 50;
    const requests = Array.from({ length: concurrentRequests }, (_, i) =>
      agentOrchestrator.processRequest({
        sessionId: `load-test-${i}`,
        userMessage: `Load test message ${i}`,
        userId: `load-test-user-${i}`,
        urgencyLevel: 'low'
      })
    );

    const startTime = Date.now();
    const responses = await Promise.all(requests);
    const totalTime = Date.now() - startTime;

    expect(responses).toHaveLength(concurrentRequests);
    expect(responses.every(r => r.response)).toBe(true);
    expect(totalTime).toBeLessThan(30000); // <30 seconds for 50 requests
  });

  it('should maintain memory efficiency during extended use', async () => {
    const initialMemory = process.memoryUsage().heapUsed;

    // Simulate extended usage
    for (let i = 0; i < 100; i++) {
      await agentOrchestrator.processRequest({
        sessionId: `memory-test-${i}`,
        userMessage: `Memory test message ${i}`,
        userId: 'memory-test-user',
        urgencyLevel: 'low'
      });
    }

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;
    const memoryIncreaseMB = memoryIncrease / (1024 * 1024);

    expect(memoryIncreaseMB).toBeLessThan(100); // <100MB increase
  });
});
```

---

## Debugging and Troubleshooting

### 1. Debug Configuration

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug VoiceHealth AI",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/main.ts",
      "env": {
        "NODE_ENV": "development",
        "DEBUG": "voicehealth:*"
      },
      "console": "integratedTerminal",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/dist/**/*.js"]
    },
    {
      "name": "Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs",
      "args": ["run", "--reporter=verbose"],
      "env": {
        "NODE_ENV": "test"
      },
      "console": "integratedTerminal"
    }
  ]
}
```

### 2. Logging and Monitoring

```typescript
// src/utils/logger.ts
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel = LogLevel.INFO;

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  error(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.ERROR) {
      console.error(`❌ [ERROR] ${message}`, data || '');
    }
  }

  warn(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.WARN) {
      console.warn(`⚠️ [WARN] ${message}`, data || '');
    }
  }

  info(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.INFO) {
      console.info(`ℹ️ [INFO] ${message}`, data || '');
    }
  }

  debug(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.DEBUG) {
      console.debug(`🐛 [DEBUG] ${message}`, data || '');
    }
  }
}

export const logger = Logger.getInstance();
```

### 3. Common Issues and Solutions

```typescript
// src/utils/troubleshooting.ts
export class TroubleshootingGuide {
  static diagnosePerformanceIssue(metrics: any): string[] {
    const issues: string[] = [];

    if (metrics.averageResponseTime > 2000) {
      issues.push('Response time exceeds 2-second emergency requirement');
      issues.push('Check circuit breaker status and AI service health');
    }

    if (metrics.memoryUsage > 500 * 1024 * 1024) { // 500MB
      issues.push('High memory usage detected');
      issues.push('Verify memory cleanup is running properly');
    }

    if (metrics.errorRate > 0.05) { // 5%
      issues.push('High error rate detected');
      issues.push('Check error sanitization logs for patterns');
    }

    return issues;
  }

  static diagnoseRealTimeIssue(connectionStatus: any): string[] {
    const issues: string[] = [];

    if (!connectionStatus.connected) {
      issues.push('Real-time connection is down');
      issues.push('Check WebSocket server status and network connectivity');
    }

    if (connectionStatus.messageQueueSize > 100) {
      issues.push('Large message queue detected');
      issues.push('Check message processing performance');
    }

    return issues;
  }

  static diagnoseSecurityIssue(auditLogs: any[]): string[] {
    const issues: string[] = [];

    const failedLogins = auditLogs.filter(log =>
      log.operation === 'login' && log.result === 'failure'
    );

    if (failedLogins.length > 10) {
      issues.push('Multiple failed login attempts detected');
      issues.push('Consider implementing rate limiting or account lockout');
    }

    const unauthorizedAccess = auditLogs.filter(log =>
      log.category === 'security' && log.severity === 'high'
    );

    if (unauthorizedAccess.length > 0) {
      issues.push('Unauthorized access attempts detected');
      issues.push('Review access controls and permissions');
    }

    return issues;
  }
}
```