import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import AdminNavigationSidebar from './components/AdminNavigationSidebar';
import KPIOverviewCards from './components/KPIOverviewCards';
import UserManagementPanel from './components/UserManagementPanel';
import SystemMonitoringWidget from './components/SystemMonitoringWidget';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeView, setActiveView] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [systemMetrics, setSystemMetrics] = useState({});

  // Mock data
  const mockUsers = [
    {
      id: 'user_001',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      registrationDate: '2024-01-15',
      lastActivity: '2 hours ago',
      sessionCount: 23
    },
    {
      id: 'user_002',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      status: 'active',
      registrationDate: '2024-01-12',
      lastActivity: '1 day ago',
      sessionCount: 41
    },
    {
      id: 'user_003',
      name: 'Mike Davis',
      email: '<EMAIL>',
      status: 'suspended',
      registrationDate: '2024-01-10',
      lastActivity: '1 week ago',
      sessionCount: 8
    },
    {
      id: 'user_004',
      name: 'Emily Chen',
      email: '<EMAIL>',
      status: 'pending',
      registrationDate: '2024-01-18',
      lastActivity: 'Never',
      sessionCount: 0
    },
    {
      id: 'user_005',
      name: 'David Wilson',
      email: '<EMAIL>',
      status: 'active',
      registrationDate: '2024-01-08',
      lastActivity: '5 hours ago',
      sessionCount: 67
    }
  ];

  const mockKPIs = {
    totalUsers: { value: '12,543', change: '+8.2%', trend: 'up' },
    activeConsultations: { value: '234', change: '+12.5%', trend: 'up' },
    systemUptime: { value: '99.9%', change: '+0.1%', trend: 'up' },
    errorRate: { value: '0.02%', change: '-0.05%', trend: 'down' },
    avgResponseTime: { value: '145ms', change: '-15ms', trend: 'down' },
    dailyConsultations: { value: '1,847', change: '+23.1%', trend: 'up' }
  };

  const mockSystemMetrics = {
    serverHealth: {
      status: 'healthy',
      uptime: '99.9%',
      cpu: 45,
      memory: 62,
      disk: 78,
      network: 23
    },
    apiMetrics: {
      responseTime: '145ms',
      requests: '12.4K/min',
      errorRate: '0.02%',
      throughput: '2.3MB/s'
    },
    alerts: [
      {
        id: 1,
        level: 'warning',
        message: 'High memory usage detected on server-02',
        timestamp: '2 minutes ago',
        resolved: false
      },
      {
        id: 2,
        level: 'info',
        message: 'System maintenance scheduled for tonight',
        timestamp: '1 hour ago',
        resolved: false
      }
    ]
  };

  useEffect(() => {
    // Simulate loading
    const loadData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setUsers(mockUsers);
      setSystemMetrics(mockSystemMetrics);
      setIsLoading(false);
    };

    loadData();
  }, []);

  const handleSidebarNavigation = (action) => {
    setActiveView(action);
    setSidebarOpen(false);
  };

  const handleUserAction = (userId, action) => {
    console.log(`User action: ${action} for user ${userId}`);
    
    if (action === 'suspend' || action === 'activate') {
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { ...user, status: action === 'suspend' ? 'suspended' : 'active' }
          : user
      ));
    }
  };

  const handleQuickAction = (action) => {
    switch (action) {
      case 'emergency-stop': console.log('Emergency stop initiated');
        break;
      case 'suspend-user': setActiveView('users');
        break;
      case 'maintenance-mode': console.log('Maintenance mode toggled');
        break;
      default:
        console.log(`Quick action: ${action}`);
    }
  };

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
              <Icon name="Shield" size={24} color="var(--primary-500)" />
            </div>
            <p className="text-text-secondary">Loading admin dashboard...</p>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'users':
        return (
          <UserManagementPanel
            users={users}
            onUserAction={handleUserAction}
          />
        );
      
      case 'performance':
      case 'usage': case'errors':
        return (
          <SystemMonitoringWidget
            metrics={systemMetrics}
          />
        );
      
      case 'agents':
        return (
          <div className="bg-surface rounded-lg border border-border shadow-minimal p-8 text-center">
            <Icon name="Bot" size={48} className="text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              Agent Management
            </h3>
            <p className="text-text-secondary mb-4">
              Configure and manage AI agents for consultations
            </p>
            <Button
              variant="primary"
              onClick={() => navigate('/agent-customization-hub')}
              iconName="Settings"
            >
              Manage Agents
            </Button>
          </div>
        );
      
      case 'settings': case'security': case'maintenance':
        return (
          <div className="bg-surface rounded-lg border border-border shadow-minimal p-8 text-center">
            <Icon name="Settings" size={48} className="text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              Platform Settings
            </h3>
            <p className="text-text-secondary mb-4">
              {activeView === 'security' ? 'Security and compliance settings' :
               activeView === 'maintenance'? 'System maintenance and updates' : 'General platform configuration'}
            </p>
            <Button variant="outline" iconName="Wrench">
              Configure Settings
            </Button>
          </div>
        );
      
      default:
        return (
          <div className="space-y-8">
            {/* KPI Cards */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Key Performance Indicators
              </h2>
              <KPIOverviewCards kpis={mockKPIs} />
            </div>

            {/* System Monitoring */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                System Health
              </h2>
              <SystemMonitoringWidget metrics={systemMetrics} />
            </div>

            {/* Quick Actions Grid */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Quick Actions
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { id: 'users', label: 'Manage Users', icon: 'Users', description: 'User accounts and permissions' },
                  { id: 'performance', label: 'System Performance', icon: 'BarChart3', description: 'Monitor system metrics' },
                  { id: 'agents', label: 'Agent Config', icon: 'Bot', description: 'AI agent management' },
                  { id: 'settings', label: 'Platform Settings', icon: 'Settings', description: 'System configuration' }
                ].map((action) => (
                  <button
                    key={action.id}
                    onClick={() => setActiveView(action.id)}
                    className="bg-surface border border-border rounded-lg p-4 hover:shadow-small transition-all text-left"
                  >
                    <Icon name={action.icon} size={24} className="text-primary-500 mb-3" />
                    <h3 className="font-medium text-text-primary mb-1">{action.label}</h3>
                    <p className="text-sm text-text-secondary">{action.description}</p>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="flex">
        {/* Admin Sidebar */}
        <AdminNavigationSidebar
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          onNavigate={handleSidebarNavigation}
        />

        {/* Main Content */}
        <main className="flex-1 lg:ml-0">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
            {/* Mobile Header */}
            <div className="flex items-center justify-between mb-8 lg:hidden">
              <Button
                variant="ghost"
                onClick={() => setSidebarOpen(true)}
                iconName="Menu"
                size="sm"
              >
                Menu
              </Button>
              <h1 className="text-2xl font-bold text-text-primary font-heading">
                Admin Dashboard
              </h1>
              <div></div>
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:flex lg:items-center lg:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-text-primary font-heading">
                  {activeView === 'overview' ? 'Admin Dashboard' : 
                   activeView === 'users' ? 'User Management' :
                   activeView === 'performance' ? 'System Performance' :
                   activeView === 'agents' ? 'Agent Configuration' :
                   activeView === 'settings' ? 'Platform Settings' :
                   'Admin Dashboard'}
                </h1>
                <p className="text-text-secondary mt-2">
                  Comprehensive administrative oversight of VoiceHealth AI platform
                </p>
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/analytics-insights-dashboard')}
                  iconName="BarChart3"
                >
                  Analytics
                </Button>
                <Button
                  variant="primary"
                  onClick={() => handleQuickAction('emergency-stop')}
                  iconName="AlertTriangle"
                >
                  Emergency Controls
                </Button>
              </div>
            </div>

            {/* Main Content Area */}
            {renderMainContent()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;