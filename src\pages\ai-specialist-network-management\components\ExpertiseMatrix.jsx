import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ExpertiseMatrix = ({ agents, onAgentSelect }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('confidence');

  const expertiseCategories = [
    { id: 'all', name: 'All Specialties', icon: 'Grid' },
    { id: 'primary-care', name: 'Primary Care', icon: 'Heart' },
    { id: 'mental-health', name: 'Mental Health', icon: 'Brain' },
    { id: 'nutrition', name: 'Nutrition', icon: 'Apple' },
    { id: 'pediatric', name: 'Pediatric', icon: 'Baby' },
    { id: 'dermatology', name: 'Dermatology', icon: 'Eye' }
  ];

  const getExpertiseLevel = (score) => {
    if (score >= 95) return { level: 'Expert', color: 'text-success-600', bg: 'bg-success-100' };
    if (score >= 90) return { level: 'Advanced', color: 'text-primary-600', bg: 'bg-primary-100' };
    if (score >= 85) return { level: 'Proficient', color: 'text-warning-600', bg: 'bg-warning-100' };
    return { level: 'Developing', color: 'text-text-secondary', bg: 'bg-background' };
  };

  const sortedAgents = [...agents].sort((a, b) => {
    switch (sortBy) {
      case 'confidence':
        return b.confidenceScore - a.confidenceScore;
      case 'consultations':
        return b.consultationsToday - a.consultationsToday;
      case 'satisfaction':
        return b.performance.patientSatisfaction - a.performance.patientSatisfaction;
      case 'name':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-text-primary font-heading">Expertise Matrix</h2>
          <p className="text-text-secondary mt-1">
            Comprehensive view of specialist capabilities and performance metrics
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => window.history.back()}
          iconName="ArrowLeft"
        >
          Back to Overview
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-text-primary mb-2">
            Filter by Category
          </label>
          <div className="flex flex-wrap gap-2">
            {expertiseCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                  selectedCategory === category.id
                    ? 'bg-primary-500 text-white' :'bg-surface border border-border text-text-primary hover:bg-primary-50'
                }`}
              >
                <Icon name={category.icon} size={16} />
                {category.name}
              </button>
            ))}
          </div>
        </div>

        <div className="sm:w-48">
          <label className="block text-sm font-medium text-text-primary mb-2">
            Sort by
          </label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="confidence">Confidence Score</option>
            <option value="consultations">Consultations Today</option>
            <option value="satisfaction">Patient Satisfaction</option>
            <option value="name">Name</option>
          </select>
        </div>
      </div>

      {/* Expertise Matrix Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sortedAgents.map((agent) => {
          const expertiseLevel = getExpertiseLevel(agent.confidenceScore);
          
          return (
            <div
              key={agent.id}
              className="bg-surface border border-border rounded-xl p-6 hover:shadow-medium transition-all cursor-pointer"
              onClick={() => onAgentSelect?.(agent.id)}
            >
              {/* Agent Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-xl bg-primary-50 flex items-center justify-center text-2xl">
                    {agent.avatar}
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary">{agent.name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${expertiseLevel.bg} ${expertiseLevel.color}`}>
                        {expertiseLevel.level}
                      </span>
                      <span className="text-sm text-text-secondary">{agent.shortName}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary-600">{agent.confidenceScore}%</div>
                  <div className="text-xs text-text-secondary">Confidence</div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-background rounded-lg">
                  <div className="text-lg font-bold text-text-primary">{agent.consultationsToday}</div>
                  <div className="text-xs text-text-secondary">Today's Consults</div>
                </div>
                <div className="text-center p-3 bg-background rounded-lg">
                  <div className="text-lg font-bold text-text-primary">{agent.performance.patientSatisfaction}</div>
                  <div className="text-xs text-text-secondary">Satisfaction</div>
                </div>
                <div className="text-center p-3 bg-background rounded-lg">
                  <div className="text-lg font-bold text-text-primary">{agent.averageResponseTime}</div>
                  <div className="text-xs text-text-secondary">Response Time</div>
                </div>
              </div>

              {/* Expertise Areas */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-text-primary mb-2">Key Expertise Areas</h4>
                <div className="flex flex-wrap gap-1">
                  {agent.expertise?.slice(0, 6).map((skill, index) => (
                    <span 
                      key={index}
                      className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                    >
                      {skill}
                    </span>
                  ))}
                  {agent.expertise?.length > 6 && (
                    <span className="px-2 py-1 bg-surface border border-border text-text-secondary text-xs rounded-full">
                      +{agent.expertise.length - 6} more
                    </span>
                  )}
                </div>
              </div>

              {/* Enhanced Capabilities */}
              {agent.enhancedCapabilities && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-text-primary mb-2">Enhanced Capabilities</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(agent.enhancedCapabilities).map(([key, enabled]) => (
                      <div key={key} className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${enabled ? 'bg-success-500' : 'bg-text-secondary'}`}></div>
                        <span className="text-xs text-text-secondary capitalize">
                          {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Collaboration Partners */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-text-primary mb-2">Collaboration Network</h4>
                <div className="flex items-center gap-2">
                  {agent.collaborationPartners?.slice(0, 3).map((partnerId, index) => {
                    const partner = agents.find(a => a.id === partnerId);
                    return partner ? (
                      <div key={partnerId} className="flex items-center gap-1">
                        <span className="text-lg">{partner.avatar}</span>
                        <span className="text-xs text-text-secondary">{partner.shortName}</span>
                      </div>
                    ) : null;
                  })}
                  {agent.collaborationPartners?.length > 3 && (
                    <span className="text-xs text-text-secondary">
                      +{agent.collaborationPartners.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAgentSelect?.(agent.id);
                  }}
                  iconName="Settings"
                  className="flex-1"
                >
                  Configure
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  iconName="BarChart3"
                  className="flex-1"
                >
                  Analytics
                </Button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Statistics */}
      <div className="bg-gradient-to-r from-primary-50 to-accent-50 rounded-xl p-6 border border-primary-200">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Network Expertise Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-600">
              {Math.round(agents.reduce((sum, agent) => sum + agent.confidenceScore, 0) / agents.length)}%
            </div>
            <div className="text-sm text-text-secondary">Avg Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-success-600">
              {agents.reduce((sum, agent) => sum + agent.consultationsToday, 0)}
            </div>
            <div className="text-sm text-text-secondary">Total Consultations</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-warning-600">
              {Math.round((agents.reduce((sum, agent) => sum + agent.performance.patientSatisfaction, 0) / agents.length) * 10) / 10}
            </div>
            <div className="text-sm text-text-secondary">Avg Satisfaction</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-accent-600">
              {agents.filter(agent => agent.enhancedCapabilities && Object.values(agent.enhancedCapabilities).some(Boolean)).length}
            </div>
            <div className="text-sm text-text-secondary">Enhanced Agents</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpertiseMatrix;