import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Icon from '../AppIcon';
import Button from './Button';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { user, userProfile } = useAuth();

  const navigationItems = [
    {
      label: 'Consult',
      path: '/voice-consultation-interface',
      icon: 'Mic',
      tooltip: 'Start voice consultation',
      badge: null
    },
    {
      label: 'History',
      path: '/session-dashboard-history',
      icon: 'History',
      tooltip: 'View consultation history',
      badge: null
    },
    {
      label: 'Profile',
      path: '/patient-profile-setup',
      icon: 'User',
      tooltip: 'Manage your profile',
      badge: null
    },
    {
      label: 'Account',
      path: '/authentication-demo-access',
      icon: 'Settings',
      tooltip: 'Account settings',
      badge: null
    }
  ];

  const isActive = (path) => location.pathname === path;

  const handleNavigation = (path) => {
    window.location.href = path;
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-100 bg-surface border-b border-border shadow-minimal">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center mr-3">
                <Icon name="Activity" size={20} color="white" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-semibold text-text-primary font-heading">
                  VoiceHealth AI
                </span>
                <span className="text-xs text-text-secondary font-caption">
                  Intelligent Health Consultations
                </span>
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navigationItems.map((item) => (
              <button
                key={item.path}
                onClick={() => handleNavigation(item.path)}
                className={`nav-item ${isActive(item.path) ? 'active' : ''}`}
                title={item.tooltip}
              >
                <Icon name={item.icon} size={18} />
                <span className="font-medium">{item.label}</span>
                {item.badge && (
                  <span className="nav-badge">{item.badge}</span>
                )}
              </button>
            ))}
          </nav>

          {/* Consultation Status Indicator */}
          <div className="hidden lg:flex items-center space-x-4">
            <div className="flex items-center space-x-2 px-3 py-1.5 bg-secondary-50 rounded-lg">
              <div className="voice-input-indicator"></div>
              <span className="text-sm text-text-secondary font-caption">
                Ready
              </span>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              onClick={toggleMenu}
              className="p-2"
              iconName={isMenuOpen ? "X" : "Menu"}
              iconSize={20}
            >
              <span className="sr-only">
                {isMenuOpen ? 'Close menu' : 'Open menu'}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-surface border-t border-border shadow-elevated">
          <div className="px-4 py-3 space-y-1">
            {navigationItems.map((item) => (
              <button
                key={item.path}
                onClick={() => handleNavigation(item.path)}
                className={`w-full nav-item ${isActive(item.path) ? 'active' : ''}`}
              >
                <Icon name={item.icon} size={18} />
                <span className="font-medium">{item.label}</span>
                {item.badge && (
                  <span className="nav-badge ml-auto">{item.badge}</span>
                )}
              </button>
            ))}
            
            {/* Mobile Consultation Status */}
            <div className="flex items-center justify-center space-x-2 px-3 py-2 bg-secondary-50 rounded-lg mt-4">
              <div className="voice-input-indicator"></div>
              <span className="text-sm text-text-secondary font-caption">
                System Ready
              </span>
            </div>
            

          </div>
        </div>
      )}
      
      {/* Desktop User Menu - positioned absolutely in top right */}
      {user && (
        <div className="hidden md:block absolute top-4 right-4 z-50">
          <UserMenu />
        </div>
      )}
    </header>
  );
};

export default Header;