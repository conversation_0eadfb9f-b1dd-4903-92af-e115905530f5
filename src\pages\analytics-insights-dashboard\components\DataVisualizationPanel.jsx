import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const DataVisualizationPanel = ({ data = {} }) => {
  const [activeChart, setActiveChart] = useState('consultations');
  const [timeRange, setTimeRange] = useState('7d');
  const [chartType, setChartType] = useState('line');

  const chartOptions = [
    { id: 'consultations', label: 'Consultation Trends', icon: 'TrendingUp' },
    { id: 'users', label: 'User Engagement', icon: 'Users' },
    { id: 'specialties', label: 'Medical Specialties', icon: 'Stethoscope' },
    { id: 'satisfaction', label: 'Satisfaction Scores', icon: 'Star' },
    { id: 'performance', label: 'System Performance', icon: 'Zap' },
    { id: 'demographics', label: 'User Demographics', icon: 'Map' }
  ];

  const timeRanges = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
    { value: '1y', label: 'Last Year' }
  ];

  const chartTypes = [
    { value: 'line', label: 'Line Chart', icon: 'TrendingUp' },
    { value: 'bar', label: 'Bar Chart', icon: 'BarChart' },
    { value: 'pie', label: 'Pie Chart', icon: 'PieChart' },
    { value: 'area', label: 'Area Chart', icon: 'Activity' }
  ];

  const mockChartData = {
    consultations: {
      title: 'Daily Consultation Volume',
      data: [
        { date: '2024-01-15', value: 145 },
        { date: '2024-01-16', value: 167 },
        { date: '2024-01-17', value: 189 },
        { date: '2024-01-18', value: 203 },
        { date: '2024-01-19', value: 178 },
        { date: '2024-01-20', value: 234 },
        { date: '2024-01-21', value: 256 }
      ],
      insights: [
        'Peak consultation hours: 2-4 PM daily',
        '23% increase in weekend consultations',
        'Cardiology leads with 34% of all consultations'
      ]
    },
    users: {
      title: 'User Engagement Metrics',
      data: [
        { category: 'New Users', value: 89 },
        { category: 'Returning Users', value: 234 },
        { category: 'Active Sessions', value: 167 },
        { category: 'Completed Profiles', value: 145 }
      ],
      insights: [
        'User retention rate: 78%',
        'Average session duration: 12.5 minutes',
        'Profile completion drives 45% higher engagement'
      ]
    },
    specialties: {
      title: 'Medical Specialty Distribution',
      data: [
        { specialty: 'General Practice', count: 145, percentage: 34 },
        { specialty: 'Cardiology', count: 89, percentage: 21 },
        { specialty: 'Dermatology', count: 67, percentage: 16 },
        { specialty: 'Mental Health', count: 56, percentage: 13 },
        { specialty: 'Nutrition', count: 43, percentage: 10 },
        { specialty: 'Other', count: 25, percentage: 6 }
      ],
      insights: [
        'General Practice remains most requested',
        'Mental Health consultations up 67%',
        'Average consultation rating: 4.8/5.0'
      ]
    }
  };

  const currentData = mockChartData[activeChart] || mockChartData.consultations;

  const renderChart = () => {
    switch (activeChart) {
      case 'consultations':
        return (
          <div className="h-64 bg-secondary-50 rounded-lg flex items-center justify-center relative overflow-hidden">
            <div className="absolute inset-0 p-4">
              <svg className="w-full h-full" viewBox="0 0 400 200">
                <defs>
                  <linearGradient id="consultationGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="var(--primary-400)" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="var(--primary-400)" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
                <path
                  d="M 20 180 L 80 160 L 140 140 L 200 120 L 260 140 L 320 100 L 380 80"
                  stroke="var(--primary-500)"
                  strokeWidth="3"
                  fill="none"
                />
                <path
                  d="M 20 180 L 80 160 L 140 140 L 200 120 L 260 140 L 320 100 L 380 80 L 380 200 L 20 200 Z"
                  fill="url(#consultationGradient)"
                />
                {currentData.data?.map((point, index) => (
                  <circle
                    key={index}
                    cx={20 + index * 60}
                    cy={200 - (point.value / 300) * 180}
                    r="4"
                    fill="var(--primary-500)"
                  />
                ))}
              </svg>
            </div>
            <div className="absolute bottom-4 left-4 right-4 flex justify-between text-xs text-text-secondary">
              {currentData.data?.slice(0, 7).map((point, index) => (
                <span key={index}>{new Date(point.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
              ))}
            </div>
          </div>
        );

      case 'users':
        return (
          <div className="h-64 bg-secondary-50 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 h-full">
              {currentData.data?.map((item, index) => (
                <div
                  key={index}
                  className="bg-surface rounded-lg p-4 flex flex-col justify-between border border-border"
                >
                  <div>
                    <p className="text-xs text-text-secondary font-medium">{item.category}</p>
                    <p className="text-2xl font-bold text-text-primary mt-1">{item.value}</p>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(item.value / 250) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'specialties':
        return (
          <div className="h-64 bg-secondary-50 rounded-lg p-4">
            <div className="space-y-3 h-full overflow-y-auto">
              {currentData.data?.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-text-primary">{item.specialty}</span>
                      <span className="text-sm text-text-secondary">{item.count}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div className="h-64 bg-secondary-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Icon name="BarChart3" size={48} className="text-text-secondary mx-auto mb-2" />
              <p className="text-text-secondary">Chart visualization for {activeChart}</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Data Visualization
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Interactive charts and graphs for data analysis
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {timeRanges.map((range) => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
            
            <select
              value={chartType}
              onChange={(e) => setChartType(e.target.value)}
              className="px-3 py-2 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {chartTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            
            <Button variant="outline" size="sm" iconName="Download">
              Export
            </Button>
          </div>
        </div>

        {/* Chart Navigation */}
        <div className="flex flex-wrap gap-2 mt-4">
          {chartOptions.map((chart) => (
            <button
              key={chart.id}
              onClick={() => setActiveChart(chart.id)}
              className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-fast ${
                activeChart === chart.id
                  ? 'bg-primary-100 text-primary-700 border border-primary-200' :'bg-secondary-50 text-text-secondary hover:text-text-primary hover:bg-secondary-100'
              }`}
            >
              <Icon name={chart.icon} size={16} className="mr-2" />
              {chart.label}
            </button>
          ))}
        </div>
      </div>

      {/* Chart Content */}
      <div className="p-6">
        <div className="mb-4">
          <h4 className="text-base font-semibold text-text-primary mb-2">
            {currentData.title}
          </h4>
        </div>

        {/* Chart Visualization */}
        {renderChart()}

        {/* Insights */}
        <div className="mt-6">
          <h5 className="text-sm font-medium text-text-primary mb-3">Key Insights</h5>
          <div className="bg-secondary-50 rounded-lg p-4">
            <ul className="space-y-2">
              {currentData.insights?.map((insight, index) => (
                <li key={index} className="flex items-start">
                  <Icon name="TrendingUp" size={16} className="text-primary-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-text-secondary">{insight}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Chart Controls */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-border">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" iconName="ZoomIn">
              Zoom In
            </Button>
            <Button variant="ghost" size="sm" iconName="ZoomOut">
              Zoom Out
            </Button>
            <Button variant="ghost" size="sm" iconName="RotateCcw">
              Reset
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" iconName="Share">
              Share
            </Button>
            <Button variant="outline" size="sm" iconName="Settings">
              Customize
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataVisualizationPanel;