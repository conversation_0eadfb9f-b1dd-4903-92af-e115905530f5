/**
 * CONVERSATION CONTEXT MANAGER
 * 
 * Advanced conversation flow management system that tracks topics, maintains context,
 * and provides intelligent conversation guidance for enhanced agent performance.
 * 
 * FEATURES:
 * - Intelligent topic detection and tracking across conversation turns
 * - Context preservation and seamless handoffs between agents
 * - Conversation flow analysis and optimization
 * - Proactive conversation guidance and suggestions
 * - Multi-turn context understanding and memory
 * - Conversation quality assessment and improvement
 * - Dynamic conversation adaptation based on patient needs
 * - Contextual conversation summarization and insights
 */

import { EventEmitter } from 'events';
import { contextualMemoryEngine } from './ContextualMemoryEngine';
import { patientContextAggregator } from './PatientContextAggregator';
import { memoryManager } from './MemoryManager';
import type { ConversationMessage } from '../types/memory';

export interface ConversationContext {
  sessionId: string;
  conversationId: string;
  currentTopic: ConversationTopic;
  topicHistory: ConversationTopic[];
  conversationFlow: ConversationFlowState;
  contextualState: ContextualState;
  participantProfiles: ParticipantProfile[];
  conversationMetrics: ConversationMetrics;
  qualityAssessment: ConversationQualityAssessment;
  adaptationStrategy: ConversationAdaptationStrategy;
  lastUpdated: string;
}

export interface ConversationTopic {
  id: string;
  topic: string;
  category: TopicCategory;
  medicalDomain: MedicalDomain;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  complexity: 'simple' | 'moderate' | 'complex' | 'highly_complex';
  startTime: string;
  endTime?: string;
  duration?: number;
  resolution: TopicResolution;
  keyPoints: string[];
  relatedTopics: string[];
  followUpNeeded: boolean;
  agentHandoffs: AgentHandoff[];
}

export type TopicCategory = 
  | 'chief_complaint'
  | 'symptom_exploration'
  | 'medical_history'
  | 'medication_review'
  | 'lifestyle_factors'
  | 'diagnostic_discussion'
  | 'treatment_planning'
  | 'follow_up_care'
  | 'patient_education'
  | 'emotional_support'
  | 'administrative'
  | 'emergency_response';

export type MedicalDomain = 
  | 'general_medicine'
  | 'cardiology'
  | 'endocrinology'
  | 'neurology'
  | 'psychiatry'
  | 'gastroenterology'
  | 'pulmonology'
  | 'dermatology'
  | 'orthopedics'
  | 'emergency_medicine'
  | 'preventive_care'
  | 'nutrition'
  | 'mental_health';

export interface TopicResolution {
  status: 'unresolved' | 'partially_resolved' | 'resolved' | 'deferred' | 'escalated';
  resolutionSummary?: string;
  actionItems: ActionItem[];
  nextSteps: NextStep[];
  satisfactionLevel?: number; // 1-10 scale
}

export interface ActionItem {
  action: string;
  responsible: 'patient' | 'agent' | 'healthcare_provider' | 'system';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
}

export interface NextStep {
  step: string;
  timeframe: string;
  type: 'appointment' | 'test' | 'medication' | 'lifestyle' | 'follow_up' | 'referral';
  urgency: 'routine' | 'urgent' | 'emergent';
}

export interface AgentHandoff {
  fromAgent: string;
  toAgent: string;
  reason: string;
  contextTransferred: string;
  timestamp: string;
  success: boolean;
}

export interface ConversationFlowState {
  currentPhase: ConversationPhase;
  phaseHistory: ConversationPhaseTransition[];
  flowPattern: ConversationFlowPattern;
  interruptionCount: number;
  redirectionCount: number;
  clarificationRequests: ClarificationRequest[];
  conversationDepth: number;
  branchingPoints: BranchingPoint[];
}

export type ConversationPhase = 
  | 'greeting_and_introduction'
  | 'chief_complaint_gathering'
  | 'symptom_exploration'
  | 'history_taking'
  | 'assessment_and_analysis'
  | 'diagnosis_discussion'
  | 'treatment_planning'
  | 'education_and_counseling'
  | 'follow_up_planning'
  | 'closure_and_summary'
  | 'emergency_response'
  | 'agent_collaboration';

export interface ConversationPhaseTransition {
  fromPhase: ConversationPhase;
  toPhase: ConversationPhase;
  trigger: string;
  timestamp: string;
  duration: number;
  quality: 'smooth' | 'abrupt' | 'forced' | 'natural';
}

export type ConversationFlowPattern = 
  | 'linear_structured'
  | 'exploratory_branching'
  | 'problem_focused'
  | 'crisis_intervention'
  | 'collaborative_consultation'
  | 'educational_dialogue'
  | 'supportive_counseling';

export interface ClarificationRequest {
  request: string;
  context: string;
  timestamp: string;
  resolved: boolean;
  resolutionMethod?: string;
}

export interface BranchingPoint {
  point: string;
  options: string[];
  chosenPath: string;
  reasoning: string;
  timestamp: string;
}

export interface ContextualState {
  emotionalTone: EmotionalContext;
  communicationStyle: CommunicationStyleProfile;
  comprehensionLevel: ComprehensionAssessment;
  engagementLevel: EngagementMetrics;
  trustLevel: TrustAssessment;
  satisfactionIndicators: SatisfactionIndicators;
  culturalContext: CulturalContextFactors;
  linguisticContext: LinguisticContext;
}

export interface EmotionalContext {
  primaryEmotion: string;
  emotionalIntensity: number; // 1-10 scale
  emotionalStability: 'stable' | 'fluctuating' | 'volatile';
  stressLevel: number; // 1-10 scale
  anxietyLevel: number; // 1-10 scale
  emotionalSupport: 'needed' | 'adequate' | 'not_needed';
  emotionalTriggers: string[];
}

export interface CommunicationStyleProfile {
  preferredStyle: 'direct' | 'indirect' | 'collaborative' | 'supportive';
  informationProcessing: 'detail_oriented' | 'big_picture' | 'step_by_step';
  questioningStyle: 'frequent' | 'minimal' | 'clarifying' | 'challenging';
  responsePattern: 'immediate' | 'thoughtful' | 'hesitant' | 'verbose';
  technicalLanguageComfort: 'high' | 'medium' | 'low';
}

export interface ComprehensionAssessment {
  overallComprehension: number; // 1-10 scale
  medicalTerminologyUnderstanding: number;
  instructionFollowing: number;
  conceptGrasping: number;
  comprehensionBarriers: string[];
  adaptationNeeds: string[];
}

export interface EngagementMetrics {
  participationLevel: number; // 1-10 scale
  questionAsking: number;
  informationSharing: number;
  followThroughLikelihood: number;
  motivationLevel: number;
  engagementBarriers: string[];
  engagementFacilitators: string[];
}

export interface TrustAssessment {
  trustLevel: number; // 1-10 scale
  trustBuilding: 'increasing' | 'stable' | 'decreasing';
  trustFactors: string[];
  trustBarriers: string[];
  credibilityPerception: number;
  confidenceInRecommendations: number;
}

export interface ParticipantProfile {
  participantId: string;
  role: 'patient' | 'agent' | 'caregiver' | 'family_member';
  communicationPreferences: CommunicationPreferences;
  participationPattern: ParticipationPattern;
  contributionQuality: ContributionQuality;
}

export interface CommunicationPreferences {
  preferredLanguage: string;
  communicationChannel: 'text' | 'voice' | 'video' | 'mixed';
  responseTime: 'immediate' | 'quick' | 'thoughtful' | 'flexible';
  informationDepth: 'brief' | 'moderate' | 'detailed' | 'comprehensive';
  supportLevel: 'minimal' | 'moderate' | 'high' | 'intensive';
}

export interface ConversationMetrics {
  totalDuration: number;
  messageCount: number;
  averageResponseTime: number;
  topicCoverage: number;
  informationDensity: number;
  clarificationRatio: number;
  interruptionRate: number;
  satisfactionScore: number;
  effectivenessScore: number;
  efficiencyScore: number;
}

export interface ConversationQualityAssessment {
  overallQuality: number; // 1-10 scale
  clarity: number;
  completeness: number;
  relevance: number;
  empathy: number;
  professionalism: number;
  patientCenteredness: number;
  clinicalAccuracy: number;
  communicationEffectiveness: number;
  qualityImprovementAreas: string[];
  strengths: string[];
}

export interface ConversationAdaptationStrategy {
  currentStrategy: AdaptationApproach;
  adaptationTriggers: AdaptationTrigger[];
  personalizations: PersonalizationElement[];
  optimizations: ConversationOptimization[];
  futureAdaptations: PlannedAdaptation[];
}

export type AdaptationApproach = 
  | 'standard_protocol'
  | 'simplified_communication'
  | 'detailed_explanation'
  | 'emotional_support_focused'
  | 'efficiency_optimized'
  | 'collaborative_approach'
  | 'crisis_intervention';

export interface AdaptationTrigger {
  trigger: string;
  threshold: number;
  action: string;
  implemented: boolean;
}

export class ConversationContextManager extends EventEmitter {
  private conversationContexts: Map<string, ConversationContext> = new Map();
  private topicClassifier: TopicClassifier;
  private flowAnalyzer: ConversationFlowAnalyzer;
  private qualityAssessor: ConversationQualityAssessor;

  constructor() {
    super();
    console.log('💬 Initializing Conversation Context Manager...');
    this.topicClassifier = new TopicClassifier();
    this.flowAnalyzer = new ConversationFlowAnalyzer();
    this.qualityAssessor = new ConversationQualityAssessor();
    this.startContextProcessing();
  }

  /**
   * Update conversation context with new message
   */
  async updateConversationContext(
    sessionId: string,
    message: ConversationMessage,
    agentResponse?: string
  ): Promise<ConversationContext> {
    try {
      console.log(`💬 Updating conversation context for session: ${sessionId}`);

      // Get or create conversation context
      let context = this.conversationContexts.get(sessionId) || await this.createNewContext(sessionId);

      // Analyze current message for topic and intent
      const currentTopic = await this.topicClassifier.classifyTopic(message.content, context);
      
      // Update topic tracking
      context = await this.updateTopicTracking(context, currentTopic, message);

      // Analyze conversation flow
      context.conversationFlow = await this.flowAnalyzer.analyzeFlow(context, message);

      // Update contextual state
      context.contextualState = await this.updateContextualState(context, message, agentResponse);

      // Update conversation metrics
      context.conversationMetrics = this.updateConversationMetrics(context, message);

      // Assess conversation quality
      context.qualityAssessment = await this.qualityAssessor.assessQuality(context, message, agentResponse);

      // Determine adaptation strategy
      context.adaptationStrategy = await this.determineAdaptationStrategy(context);

      // Update timestamp
      context.lastUpdated = new Date().toISOString();

      // Store updated context
      this.conversationContexts.set(sessionId, context);

      console.log(`✅ Conversation context updated for session: ${sessionId}`);
      this.emit('context_updated', context);

      return context;

    } catch (error) {
      console.error('❌ Failed to update conversation context:', error);
      throw error;
    }
  }

  /**
   * Get rich conversation guidance for agents
   */
  async getConversationGuidance(sessionId: string, currentMessage: string): Promise<ConversationGuidance> {
    try {
      const context = this.conversationContexts.get(sessionId);
      if (!context) {
        throw new Error('No conversation context found');
      }

      // Generate contextual suggestions
      const suggestions = await this.generateContextualSuggestions(context, currentMessage);

      // Identify conversation opportunities
      const opportunities = this.identifyConversationOpportunities(context);

      // Generate flow recommendations
      const flowRecommendations = await this.generateFlowRecommendations(context);

      // Assess conversation risks
      const risks = this.assessConversationRisks(context);

      // Generate adaptation recommendations
      const adaptations = await this.generateAdaptationRecommendations(context);

      return {
        sessionId,
        currentContext: context,
        suggestions,
        opportunities,
        flowRecommendations,
        risks,
        adaptations,
        nextBestActions: await this.determineNextBestActions(context),
        conversationHealth: this.assessConversationHealth(context),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Failed to get conversation guidance:', error);
      throw error;
    }
  }

  /**
   * Create new conversation context
   */
  private async createNewContext(sessionId: string): Promise<ConversationContext> {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      sessionId,
      conversationId,
      currentTopic: {
        id: 'initial',
        topic: 'Initial Contact',
        category: 'chief_complaint',
        medicalDomain: 'general_medicine',
        urgency: 'medium',
        complexity: 'simple',
        startTime: new Date().toISOString(),
        resolution: {
          status: 'unresolved',
          actionItems: [],
          nextSteps: []
        },
        keyPoints: [],
        relatedTopics: [],
        followUpNeeded: false,
        agentHandoffs: []
      },
      topicHistory: [],
      conversationFlow: {
        currentPhase: 'greeting_and_introduction',
        phaseHistory: [],
        flowPattern: 'linear_structured',
        interruptionCount: 0,
        redirectionCount: 0,
        clarificationRequests: [],
        conversationDepth: 1,
        branchingPoints: []
      },
      contextualState: {
        emotionalTone: {
          primaryEmotion: 'neutral',
          emotionalIntensity: 5,
          emotionalStability: 'stable',
          stressLevel: 5,
          anxietyLevel: 5,
          emotionalSupport: 'not_needed',
          emotionalTriggers: []
        },
        communicationStyle: {
          preferredStyle: 'collaborative',
          informationProcessing: 'step_by_step',
          questioningStyle: 'clarifying',
          responsePattern: 'thoughtful',
          technicalLanguageComfort: 'medium'
        },
        comprehensionLevel: {
          overallComprehension: 8,
          medicalTerminologyUnderstanding: 6,
          instructionFollowing: 8,
          conceptGrasping: 7,
          comprehensionBarriers: [],
          adaptationNeeds: []
        },
        engagementLevel: {
          participationLevel: 7,
          questionAsking: 6,
          informationSharing: 7,
          followThroughLikelihood: 7,
          motivationLevel: 7,
          engagementBarriers: [],
          engagementFacilitators: []
        },
        trustLevel: {
          trustLevel: 7,
          trustBuilding: 'stable',
          trustFactors: [],
          trustBarriers: [],
          credibilityPerception: 7,
          confidenceInRecommendations: 7
        },
        satisfactionIndicators: {} as SatisfactionIndicators,
        culturalContext: {} as CulturalContextFactors,
        linguisticContext: {} as LinguisticContext
      },
      participantProfiles: [],
      conversationMetrics: {
        totalDuration: 0,
        messageCount: 0,
        averageResponseTime: 0,
        topicCoverage: 0,
        informationDensity: 0,
        clarificationRatio: 0,
        interruptionRate: 0,
        satisfactionScore: 7,
        effectivenessScore: 7,
        efficiencyScore: 7
      },
      qualityAssessment: {
        overallQuality: 7,
        clarity: 7,
        completeness: 5,
        relevance: 8,
        empathy: 7,
        professionalism: 8,
        patientCenteredness: 7,
        clinicalAccuracy: 7,
        communicationEffectiveness: 7,
        qualityImprovementAreas: [],
        strengths: []
      },
      adaptationStrategy: {
        currentStrategy: 'standard_protocol',
        adaptationTriggers: [],
        personalizations: [],
        optimizations: [],
        futureAdaptations: []
      },
      lastUpdated: new Date().toISOString()
    };
  }

  // Placeholder implementations for complex methods
  private async updateTopicTracking(context: ConversationContext, topic: ConversationTopic, message: ConversationMessage): Promise<ConversationContext> {
    return context;
  }

  private async updateContextualState(context: ConversationContext, message: ConversationMessage, response?: string): Promise<ContextualState> {
    return context.contextualState;
  }

  private updateConversationMetrics(context: ConversationContext, message: ConversationMessage): ConversationMetrics {
    const metrics = context.conversationMetrics;
    metrics.messageCount++;
    metrics.totalDuration = Date.now() - new Date(context.currentTopic.startTime).getTime();
    return metrics;
  }

  private async determineAdaptationStrategy(context: ConversationContext): Promise<ConversationAdaptationStrategy> {
    return context.adaptationStrategy;
  }

  private async generateContextualSuggestions(context: ConversationContext, message: string): Promise<ContextualSuggestion[]> {
    return [];
  }

  private identifyConversationOpportunities(context: ConversationContext): ConversationOpportunity[] {
    return [];
  }

  private async generateFlowRecommendations(context: ConversationContext): Promise<FlowRecommendation[]> {
    return [];
  }

  private assessConversationRisks(context: ConversationContext): ConversationRisk[] {
    return [];
  }

  private async generateAdaptationRecommendations(context: ConversationContext): Promise<AdaptationRecommendation[]> {
    return [];
  }

  private async determineNextBestActions(context: ConversationContext): Promise<NextBestAction[]> {
    return [];
  }

  private assessConversationHealth(context: ConversationContext): ConversationHealth {
    return {
      overallHealth: 'good',
      healthScore: 8,
      healthFactors: [],
      improvementAreas: []
    };
  }

  private startContextProcessing(): void {
    // Start background context processing
  }
}

// Helper classes (simplified implementations)
class TopicClassifier {
  async classifyTopic(content: string, context: ConversationContext): Promise<ConversationTopic> {
    return context.currentTopic;
  }
}

class ConversationFlowAnalyzer {
  async analyzeFlow(context: ConversationContext, message: ConversationMessage): Promise<ConversationFlowState> {
    return context.conversationFlow;
  }
}

class ConversationQualityAssessor {
  async assessQuality(context: ConversationContext, message: ConversationMessage, response?: string): Promise<ConversationQualityAssessment> {
    return context.qualityAssessment;
  }
}

// Additional interfaces
interface ConversationGuidance {
  sessionId: string;
  currentContext: ConversationContext;
  suggestions: ContextualSuggestion[];
  opportunities: ConversationOpportunity[];
  flowRecommendations: FlowRecommendation[];
  risks: ConversationRisk[];
  adaptations: AdaptationRecommendation[];
  nextBestActions: NextBestAction[];
  conversationHealth: ConversationHealth;
  timestamp: string;
}

interface ContextualSuggestion {
  type: string;
  suggestion: string;
  reasoning: string;
  priority: string;
}

interface ConversationOpportunity {
  opportunity: string;
  description: string;
  potential: string;
}

interface FlowRecommendation {
  recommendation: string;
  rationale: string;
  implementation: string;
}

interface ConversationRisk {
  risk: string;
  severity: string;
  mitigation: string;
}

interface AdaptationRecommendation {
  adaptation: string;
  trigger: string;
  expected_outcome: string;
}

interface NextBestAction {
  action: string;
  priority: string;
  timing: string;
}

interface ConversationHealth {
  overallHealth: string;
  healthScore: number;
  healthFactors: string[];
  improvementAreas: string[];
}

// Placeholder interfaces
interface SatisfactionIndicators { }
interface CulturalContextFactors { }
interface LinguisticContext { }
interface ParticipationPattern { }
interface ContributionQuality { }
interface PersonalizationElement { }
interface ConversationOptimization { }
interface PlannedAdaptation { }

// Export singleton instance
export const conversationContextManager = new ConversationContextManager();
export default conversationContextManager;
