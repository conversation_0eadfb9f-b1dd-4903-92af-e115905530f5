/**
 * SECURE FILE UPLOAD COMPONENT
 * 
 * HIPAA-compliant file upload component with encryption, validation,
 * and audit logging for medical document management.
 * 
 * FEATURES:
 * - Secure file upload with encryption
 * - File type and size validation
 * - Progress tracking and error handling
 * - RBAC access control integration
 * - Audit logging for compliance
 * - Emergency upload protocols
 */

import React, { useState, useRef } from 'react';
import { secureStorageService } from '../services/SecureStorageService';

const SecureFileUpload = ({ 
  bucketName = 'medical-records',
  userId,
  userRole = 'patient',
  onUploadComplete,
  onUploadError,
  emergencyMode = false,
  allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'],
  maxFileSize = 25 * 1024 * 1024 // 25MB
}) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileSelect = (file) => {
    if (!file) return;

    // Validate file type
    if (!allowedTypes.includes('*/*') && !allowedTypes.includes(file.type)) {
      setUploadStatus(`❌ File type not allowed: ${file.type}`);
      if (onUploadError) onUploadError(`File type not allowed: ${file.type}`);
      return;
    }

    // Validate file size
    if (file.size > maxFileSize) {
      const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
      const maxSizeMB = (maxFileSize / (1024 * 1024)).toFixed(2);
      setUploadStatus(`❌ File too large: ${sizeMB}MB (max: ${maxSizeMB}MB)`);
      if (onUploadError) onUploadError(`File too large: ${sizeMB}MB`);
      return;
    }

    setSelectedFile(file);
    setUploadStatus(`✅ File selected: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!selectedFile || !userId) {
      setUploadStatus('❌ No file selected or user not authenticated');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadStatus('🔐 Starting secure upload...');

    try {
      // Generate secure file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = selectedFile.name.split('.').pop();
      const secureFileName = `${userId}/${timestamp}-${Math.random().toString(36).substr(2, 9)}.${fileExtension}`;

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Prepare metadata
      const metadata = {
        originalName: selectedFile.name,
        uploadedBy: userId,
        userRole,
        emergencyMode,
        uploadTimestamp: new Date().toISOString(),
        fileType: selectedFile.type,
        fileSize: selectedFile.size
      };

      // Perform secure upload
      const uploadResult = await secureStorageService.secureUpload(
        bucketName,
        secureFileName,
        selectedFile,
        userId,
        metadata
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (uploadResult.success) {
        setUploadStatus(`✅ Upload completed successfully! File ID: ${uploadResult.fileId}`);
        
        if (onUploadComplete) {
          onUploadComplete({
            fileId: uploadResult.fileId,
            filePath: uploadResult.filePath,
            originalName: selectedFile.name,
            encryptionKey: uploadResult.encryptionKey,
            checksum: uploadResult.checksum,
            auditId: uploadResult.auditId
          });
        }

        // Reset form
        setTimeout(() => {
          setSelectedFile(null);
          setUploadProgress(0);
          setUploadStatus('');
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
        }, 3000);

      } else {
        setUploadStatus(`❌ Upload failed: ${uploadResult.error}`);
        if (onUploadError) onUploadError(uploadResult.error);
      }

    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus(`❌ Upload failed: ${error.message}`);
      if (onUploadError) onUploadError(error.message);
    } finally {
      setIsUploading(false);
    }
  };

  // Handle drag and drop
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          🔐 Secure File Upload
          {emergencyMode && (
            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
              EMERGENCY MODE
            </span>
          )}
        </h3>
        <div className="text-sm text-gray-600">
          <div>Bucket: {bucketName}</div>
          <div>Max size: {formatFileSize(maxFileSize)}</div>
          <div>Allowed types: {allowedTypes.join(', ')}</div>
        </div>
      </div>

      {/* Drag and Drop Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="space-y-4">
          <div className="text-4xl">📁</div>
          
          {selectedFile ? (
            <div className="space-y-2">
              <div className="font-medium text-gray-800">{selectedFile.name}</div>
              <div className="text-sm text-gray-600">
                {formatFileSize(selectedFile.size)} • {selectedFile.type}
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-gray-600">
                Drag and drop your file here, or
              </div>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                disabled={isUploading}
              >
                Choose File
              </button>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept={allowedTypes.join(',')}
            onChange={(e) => handleFileSelect(e.target.files[0])}
            disabled={isUploading}
          />
        </div>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Uploading...</span>
            <span>{uploadProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Status Message */}
      {uploadStatus && (
        <div className={`mt-4 p-3 rounded text-sm ${
          uploadStatus.startsWith('✅') 
            ? 'bg-green-50 text-green-800 border border-green-200'
            : uploadStatus.startsWith('❌')
            ? 'bg-red-50 text-red-800 border border-red-200'
            : 'bg-blue-50 text-blue-800 border border-blue-200'
        }`}>
          {uploadStatus}
        </div>
      )}

      {/* Upload Button */}
      {selectedFile && !isUploading && (
        <div className="mt-4 space-y-2">
          <button
            onClick={handleUpload}
            className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            disabled={!userId}
          >
            🔐 Secure Upload
          </button>
          
          <button
            onClick={() => {
              setSelectedFile(null);
              setUploadStatus('');
              if (fileInputRef.current) {
                fileInputRef.current.value = '';
              }
            }}
            className="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
        </div>
      )}

      {/* Security Notice */}
      <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded text-xs text-gray-600">
        <div className="font-medium mb-1">🛡️ Security Features:</div>
        <ul className="space-y-1">
          <li>• AES-256-GCM encryption</li>
          <li>• SHA-256 integrity verification</li>
          <li>• HIPAA-compliant audit logging</li>
          <li>• Role-based access control</li>
          <li>• Secure file validation</li>
        </ul>
      </div>
    </div>
  );
};

export default SecureFileUpload;
