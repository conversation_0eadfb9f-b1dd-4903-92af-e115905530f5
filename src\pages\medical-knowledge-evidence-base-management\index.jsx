import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';

import MedicalConditionsPanel from './components/MedicalConditionsPanel';
import AfricaSpecificDiseasesPanel from './components/AfricaSpecificDiseasesPanel';
import EvidenceBasedGuidelinesPanel from './components/EvidenceBasedGuidelinesPanel';
import RedFlagDetectionSystem from './components/RedFlagDetectionSystem';
import CulturalMedicineIntegration from './components/CulturalMedicineIntegration';
import KnowledgeBaseSearch from './components/KnowledgeBaseSearch';

const MedicalKnowledgeEvidenceBaseManagement = () => {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('conditions');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [searchResults, setSearchResults] = useState([]);
  const [knowledgeStats, setKnowledgeStats] = useState({});

  // Mock data for comprehensive medical knowledge management
  const mockKnowledgeStats = {
    totalConditions: 2847,
    africaSpecificConditions: 156,
    evidenceBasedGuidelines: 1423,
    redFlagCombinations: 89,
    culturalMedicineEntries: 234,
    lastUpdated: '2024-01-15T10:30:00Z'
  };

  const mockSearchResults = [
    {
      id: 'malaria_001',
      type: 'africa-specific',
      title: 'Malaria - Plasmodium falciparum',
      region: 'West Africa',
      symptoms: ['fever', 'chills', 'headache', 'nausea'],
      redFlags: ['cerebral symptoms', 'respiratory distress'],
      prevalence: 'High',
      confidence: 0.95
    },
    {
      id: 'hypertension_001',
      type: 'common-condition',
      title: 'Essential Hypertension',
      symptoms: ['headache', 'dizziness', 'chest pain'],
      redFlags: ['stroke symptoms', 'heart failure signs'],
      guidelines: 'WHO/ISH 2020',
      confidence: 0.92
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate loading medical knowledge data
      await new Promise(resolve => setTimeout(resolve, 1200));
      setKnowledgeStats(mockKnowledgeStats);
      setIsLoading(false);
    };

    loadData();
  }, []);

  const handleSearch = async (query) => {
    setSearchQuery(query);
    if (query.length > 2) {
      setIsLoading(true);
      // Simulate search
      await new Promise(resolve => setTimeout(resolve, 500));
      setSearchResults(mockSearchResults.filter(result => 
        result.title.toLowerCase().includes(query.toLowerCase()) ||
        result.symptoms?.some(symptom => symptom.toLowerCase().includes(query.toLowerCase()))
      ));
      setIsLoading(false);
    } else {
      setSearchResults([]);
    }
  };

  const sidebarSections = [
    { id: 'conditions', label: 'Medical Conditions', icon: 'Heart', count: knowledgeStats.totalConditions },
    { id: 'africa-diseases', label: 'Africa-Specific Diseases', icon: 'Globe', count: knowledgeStats.africaSpecificConditions },
    { id: 'guidelines', label: 'Evidence-Based Guidelines', icon: 'BookOpen', count: knowledgeStats.evidenceBasedGuidelines },
    { id: 'red-flags', label: 'Red Flag Detection', icon: 'AlertTriangle', count: knowledgeStats.redFlagCombinations },
    { id: 'cultural-medicine', label: 'Cultural Medicine', icon: 'Leaf', count: knowledgeStats.culturalMedicineEntries }
  ];

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
              <Icon name="Database" size={24} color="var(--primary-500)" />
            </div>
            <p className="text-text-secondary">Loading medical knowledge database...</p>
          </div>
        </div>
      );
    }

    switch (activeSection) {
      case 'conditions':
        return <MedicalConditionsPanel searchQuery={searchQuery} />;
      case 'africa-diseases':
        return <AfricaSpecificDiseasesPanel searchQuery={searchQuery} />;
      case 'guidelines':
        return <EvidenceBasedGuidelinesPanel searchQuery={searchQuery} />;
      case 'red-flags':
        return <RedFlagDetectionSystem searchQuery={searchQuery} />;
      case 'cultural-medicine':
        return <CulturalMedicineIntegration searchQuery={searchQuery} />;
      default:
        return <MedicalConditionsPanel searchQuery={searchQuery} />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="flex">
        {/* Sidebar */}
        <div className="hidden lg:flex lg:flex-col lg:w-80 lg:fixed lg:inset-y-0 lg:pt-16 bg-surface border-r border-border">
          <div className="flex-1 flex flex-col min-h-0">
            <div className="p-4 border-b border-border">
              <h2 className="text-lg font-semibold text-text-primary font-heading">
                Medical Knowledge Base
              </h2>
              <p className="text-sm text-text-secondary mt-1">
                Comprehensive Africa-focused medical database
              </p>
            </div>
            
            <nav className="flex-1 px-2 py-4 space-y-1">
              {sidebarSections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeSection === section.id
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-500' :'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                  }`}
                >
                  <Icon name={section.icon} size={20} className="mr-3" />
                  <span className="flex-1 text-left">{section.label}</span>
                  {section.count && (
                    <span className="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs font-medium">
                      {section.count?.toLocaleString()}
                    </span>
                  )}
                </button>
              ))}
            </nav>

            <div className="p-4 border-t border-border">
              <div className="bg-success-50 border border-success-200 rounded-lg p-3">
                <div className="flex items-center">
                  <Icon name="CheckCircle" size={20} className="text-success-600 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-success-800">Database Synced</p>
                    <p className="text-xs text-success-600">
                      Last updated: {new Date(knowledgeStats.lastUpdated).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 lg:ml-80">
          <div className="px-4 sm:px-6 lg:px-8 pt-20 pb-12">
            {/* Mobile Header */}
            <div className="lg:hidden mb-6">
              <h1 className="text-2xl font-bold text-text-primary font-heading">
                Medical Knowledge Management
              </h1>
              <p className="text-text-secondary mt-2">
                Comprehensive medical database with Africa-specific focus
              </p>
            </div>

            {/* Search Bar */}
            <div className="mb-8">
              <KnowledgeBaseSearch
                onSearch={handleSearch}
                searchResults={searchResults}
                isLoading={isLoading}
              />
            </div>

            {/* Desktop Header */}
            <div className="hidden lg:flex lg:items-center lg:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-text-primary font-heading">
                  {activeSection === 'conditions' ? 'Medical Conditions Database' :
                   activeSection === 'africa-diseases' ? 'Africa-Specific Diseases' :
                   activeSection === 'guidelines' ? 'Evidence-Based Guidelines' :
                   activeSection === 'red-flags' ? 'Red Flag Detection System' :
                   activeSection === 'cultural-medicine'? 'Cultural Medicine Integration' : 'Medical Knowledge Management'}
                </h1>
                <p className="text-text-secondary mt-2">
                  {activeSection === 'conditions' ? 'Comprehensive symptom mapping and diagnostic criteria' :
                   activeSection === 'africa-diseases' ? 'Endemic conditions across Ghana, Nigeria, Kenya, and South Africa' :
                   activeSection === 'guidelines' ? 'WHO/CDC-aligned treatment protocols' :
                   activeSection === 'red-flags' ? 'Critical symptom combinations and escalation triggers' :
                   activeSection === 'cultural-medicine'? 'Traditional healing practices with safety assessments' : 'Africa-focused medical knowledge database'}
                </p>
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin-dashboard')}
                  iconName="ArrowLeft"
                >
                  Back to Admin
                </Button>
                <Button
                  variant="primary"
                  iconName="Download"
                >
                  Export Knowledge Base
                </Button>
              </div>
            </div>

            {/* Knowledge Base Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="Heart" size={24} className="text-primary-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {knowledgeStats.totalConditions?.toLocaleString()}
                    </p>
                    <p className="text-sm text-text-secondary">Total Conditions</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="Globe" size={24} className="text-success-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {knowledgeStats.africaSpecificConditions}
                    </p>
                    <p className="text-sm text-text-secondary">Africa-Specific</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="BookOpen" size={24} className="text-warning-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {knowledgeStats.evidenceBasedGuidelines?.toLocaleString()}
                    </p>
                    <p className="text-sm text-text-secondary">Evidence Guidelines</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center">
                  <Icon name="AlertTriangle" size={24} className="text-error-500 mr-3" />
                  <div>
                    <p className="text-2xl font-bold text-text-primary">
                      {knowledgeStats.redFlagCombinations}
                    </p>
                    <p className="text-sm text-text-secondary">Red Flag Patterns</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content Area */}
            {renderMainContent()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default MedicalKnowledgeEvidenceBaseManagement;