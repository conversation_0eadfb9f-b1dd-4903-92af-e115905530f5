/**
 * Enhanced Agent Orchestrator
 * 
 * Integrates all enhanced services for comprehensive medical AI consultations
 * with advanced SOAP flow, clinical decision support, and optimized context assembly.
 */

import { enhancedSOAPFlowService, ConversationState, FlowControlDecision } from './EnhancedSOAPFlowService';
import { clinicalQuestionGeneratorService, QuestionSet } from './ClinicalQuestionGeneratorService';
import { clinicalDecisionSupportService, ClinicalDecision } from './ClinicalDecisionSupportService';
import { unifiedContextAssemblyService, OptimizedContext } from './UnifiedContextAssemblyService';
import { contextPerformanceOptimizer } from './ContextPerformanceOptimizer';
import { contextQualityMetricsService, ContextQualityReport } from './ContextQualityMetricsService';
import { enhancedPatientContextService, EnhancedPatientContext } from './EnhancedPatientContextService';
import { DiagnosticFrameworkService, SOAPAssessment } from './DiagnosticFrameworkService';

export interface EnhancedConsultationRequest {
  sessionId: string;
  userId: string;
  userMessage: string;
  conversationHistory: any[];
  urgencyLevel?: 'routine' | 'urgent' | 'emergent' | 'immediate';
  agentType?: string;
  specialtyFocus?: string;
}

export interface EnhancedConsultationResponse {
  agentResponse: string;
  conversationState: ConversationState;
  clinicalDecision: ClinicalDecision;
  nextQuestions: string[];
  contextQuality: ContextQualityReport;
  performanceMetrics: any;
  recommendations: ConsultationRecommendation[];
  urgencyEscalation?: boolean;
  specialistReferralNeeded?: boolean;
}

export interface ConsultationRecommendation {
  type: 'immediate_action' | 'follow_up' | 'specialist_referral' | 'diagnostic_test' | 'lifestyle_change';
  priority: 'immediate' | 'high' | 'medium' | 'low';
  description: string;
  rationale: string;
  timeframe?: string;
}

class EnhancedAgentOrchestrator {
  private diagnosticService: DiagnosticFrameworkService;

  constructor() {
    this.diagnosticService = new DiagnosticFrameworkService();
  }

  /**
   * Process enhanced medical consultation with full integration
   */
  async processEnhancedConsultation(request: EnhancedConsultationRequest): Promise<EnhancedConsultationResponse> {
    const startTime = Date.now();
    console.log(`🚀 Starting enhanced consultation for session: ${request.sessionId}`);

    try {
      // Step 1: Load comprehensive patient context
      const patientContext = await this.loadPatientContext(request.userId, request.sessionId);

      // Step 2: Initialize or update conversation state
      const conversationState = await this.manageConversationState(request, patientContext);

      // Step 3: Update SOAP assessment
      const soapAssessment = await this.updateSOAPAssessment(request, conversationState);

      // Step 4: Generate clinical decision support
      const clinicalDecision = await this.generateClinicalDecision(request, patientContext, soapAssessment);

      // Step 5: Assemble optimized context
      const optimizedContext = await this.assembleOptimizedContext(
        request,
        patientContext,
        soapAssessment,
        clinicalDecision
      );

      // Step 6: Generate intelligent questions
      const nextQuestions = await this.generateIntelligentQuestions(
        request,
        conversationState,
        soapAssessment,
        clinicalDecision
      );

      // Step 7: Generate agent response
      const agentResponse = await this.generateEnhancedResponse(
        request,
        optimizedContext,
        conversationState,
        clinicalDecision,
        nextQuestions
      );

      // Step 8: Assess context quality
      const contextQuality = this.assessContextQuality(optimizedContext, patientContext, request);

      // Step 9: Generate consultation recommendations
      const recommendations = this.generateConsultationRecommendations(
        clinicalDecision,
        conversationState,
        contextQuality
      );

      // Step 10: Check for urgency escalation
      const urgencyEscalation = this.checkUrgencyEscalation(clinicalDecision, conversationState);
      const specialistReferralNeeded = this.checkSpecialistReferralNeeded(clinicalDecision);

      // Step 11: Record performance metrics
      const performanceMetrics = this.recordPerformanceMetrics(startTime, optimizedContext);

      console.log(`✅ Enhanced consultation completed in ${Date.now() - startTime}ms`);

      return {
        agentResponse,
        conversationState,
        clinicalDecision,
        nextQuestions,
        contextQuality,
        performanceMetrics,
        recommendations,
        urgencyEscalation,
        specialistReferralNeeded
      };

    } catch (error) {
      console.error('❌ Enhanced consultation failed:', error);
      throw new Error(`Enhanced consultation failed: ${error.message}`);
    }
  }

  /**
   * Load comprehensive patient context
   */
  private async loadPatientContext(userId: string, sessionId: string): Promise<EnhancedPatientContext> {
    return await contextPerformanceOptimizer.optimizeContextAssembly(
      () => enhancedPatientContextService.loadPatientContext(userId, sessionId),
      `patient_context_${userId}`,
      { enableCaching: true, enableCompression: true }
    );
  }

  /**
   * Manage conversation state with SOAP flow
   */
  private async manageConversationState(
    request: EnhancedConsultationRequest,
    patientContext: EnhancedPatientContext
  ): Promise<ConversationState> {
    let conversationState = enhancedSOAPFlowService.getConversationState(request.sessionId);

    if (!conversationState) {
      // Initialize new conversation
      conversationState = enhancedSOAPFlowService.initializeConversationState(
        request.sessionId,
        request.userMessage
      );
    }

    return conversationState;
  }

  /**
   * Update SOAP assessment
   */
  private async updateSOAPAssessment(
    request: EnhancedConsultationRequest,
    conversationState: ConversationState
  ): Promise<SOAPAssessment> {
    // Get or initialize SOAP assessment
    let soapAssessment = this.diagnosticService.getSOAPAssessment(request.sessionId);

    if (!soapAssessment) {
      soapAssessment = this.diagnosticService.initializeSOAPAssessment(
        request.sessionId,
        request.userMessage,
        request.urgencyLevel || 'medium'
      );
    }

    // Update SOAP assessment with new information
    soapAssessment = this.diagnosticService.updateSOAPAssessment(
      request.sessionId,
      request.userMessage,
      conversationState.currentPhase
    );

    return soapAssessment;
  }

  /**
   * Generate clinical decision support
   */
  private async generateClinicalDecision(
    request: EnhancedConsultationRequest,
    patientContext: EnhancedPatientContext,
    soapAssessment: SOAPAssessment
  ): Promise<ClinicalDecision> {
    const clinicalContext = {
      chiefComplaint: request.userMessage,
      symptoms: this.extractSymptoms(request.userMessage, request.conversationHistory),
      patientAge: this.calculateAge(patientContext.patientProfile.dateOfBirth),
      patientGender: patientContext.patientProfile.gender as any,
      medicalHistory: patientContext.medicalHistory.conditions.map(c => c.conditionName),
      medications: patientContext.medicalHistory.medications.map(m => m.medicationName),
      allergies: patientContext.medicalHistory.allergies
    };

    return clinicalDecisionSupportService.generateClinicalDecision(clinicalContext, soapAssessment);
  }

  /**
   * Assemble optimized context
   */
  private async assembleOptimizedContext(
    request: EnhancedConsultationRequest,
    patientContext: EnhancedPatientContext,
    soapAssessment: SOAPAssessment,
    clinicalDecision: ClinicalDecision
  ): Promise<OptimizedContext> {
    return await unifiedContextAssemblyService.assembleOptimizedContext(
      patientContext,
      request.conversationHistory,
      request.userMessage,
      soapAssessment,
      clinicalDecision,
      {
        urgencyLevel: request.urgencyLevel || 'routine',
        conversationPhase: soapAssessment.currentPhase as any,
        priorityMode: this.determinePriorityMode(request.urgencyLevel),
        optimizeForSpeed: request.urgencyLevel === 'immediate'
      }
    );
  }

  /**
   * Generate intelligent questions
   */
  private async generateIntelligentQuestions(
    request: EnhancedConsultationRequest,
    conversationState: ConversationState,
    soapAssessment: SOAPAssessment,
    clinicalDecision: ClinicalDecision
  ): Promise<string[]> {
    // Process conversation turn for flow control
    const flowDecision = enhancedSOAPFlowService.processConversationTurn(
      request.sessionId,
      request.userMessage,
      soapAssessment
    );

    // Generate context for question generation
    const questionContext = {
      chiefComplaint: request.userMessage,
      currentPhase: conversationState.currentPhase,
      urgencyLevel: conversationState.urgencyLevel,
      conversationHistory: request.conversationHistory.map(msg => msg.content || msg.message)
    };

    // Generate questions based on urgency and flow
    if (flowDecision.urgencyEscalation) {
      const urgentQuestions = clinicalQuestionGeneratorService.generateUrgencyBasedQuestions(
        'urgent',
        questionContext
      );
      return urgentQuestions.map(q => q.text);
    }

    // Generate phase-appropriate questions
    const questionSet = clinicalQuestionGeneratorService.generateQuestions(questionContext);
    return questionSet.primaryQuestions.map(q => q.text);
  }

  /**
   * Generate enhanced agent response
   */
  private async generateEnhancedResponse(
    request: EnhancedConsultationRequest,
    optimizedContext: OptimizedContext,
    conversationState: ConversationState,
    clinicalDecision: ClinicalDecision,
    nextQuestions: string[]
  ): Promise<string> {
    // Use the optimized system prompt for better context integration
    const enhancedPrompt = this.buildEnhancedPrompt(
      optimizedContext.systemPrompt,
      conversationState,
      clinicalDecision,
      nextQuestions
    );

    // This would integrate with your existing AI service
    // For now, return a structured response
    return this.generateStructuredResponse(
      request.userMessage,
      enhancedPrompt,
      conversationState,
      clinicalDecision,
      nextQuestions
    );
  }

  /**
   * Assess context quality
   */
  private assessContextQuality(
    optimizedContext: OptimizedContext,
    patientContext: EnhancedPatientContext,
    request: EnhancedConsultationRequest
  ): ContextQualityReport {
    return contextQualityMetricsService.assessContextQuality(
      optimizedContext.contextSections,
      patientContext,
      {
        userId: request.userId,
        sessionId: request.sessionId,
        urgencyLevel: request.urgencyLevel,
        chiefComplaint: request.userMessage
      }
    );
  }

  /**
   * Generate consultation recommendations
   */
  private generateConsultationRecommendations(
    clinicalDecision: ClinicalDecision,
    conversationState: ConversationState,
    contextQuality: ContextQualityReport
  ): ConsultationRecommendation[] {
    const recommendations: ConsultationRecommendation[] = [];

    // Add clinical decision recommendations
    clinicalDecision.recommendedActions.forEach(action => {
      recommendations.push({
        type: this.mapActionToType(action.action),
        priority: action.priority as any,
        description: action.action,
        rationale: action.rationale,
        timeframe: action.timeframe
      });
    });

    // Add specialist referral recommendations
    clinicalDecision.specialistReferrals.forEach(referral => {
      recommendations.push({
        type: 'specialist_referral',
        priority: referral.urgency as any,
        description: `Referral to ${referral.specialty}`,
        rationale: referral.reason,
        timeframe: referral.expectedTimeframe
      });
    });

    // Add context quality recommendations
    contextQuality.recommendations.forEach(rec => {
      if (rec.priority === 'immediate' || rec.priority === 'high') {
        recommendations.push({
          type: 'follow_up',
          priority: rec.priority as any,
          description: rec.title,
          rationale: rec.description
        });
      }
    });

    return recommendations.sort((a, b) => {
      const priorityOrder = { immediate: 0, high: 1, medium: 2, low: 3 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }

  // Helper methods
  private checkUrgencyEscalation(clinicalDecision: ClinicalDecision, conversationState: ConversationState): boolean {
    return clinicalDecision.redFlags.some(flag => flag.severity === 'red') ||
           clinicalDecision.urgencyLevel === 'immediate';
  }

  private checkSpecialistReferralNeeded(clinicalDecision: ClinicalDecision): boolean {
    return clinicalDecision.specialistReferrals.some(referral => 
      referral.urgency === 'urgent' || referral.urgency === 'immediate'
    );
  }

  private recordPerformanceMetrics(startTime: number, optimizedContext: OptimizedContext): any {
    const processingTime = Date.now() - startTime;
    return {
      processingTime,
      tokenUtilization: optimizedContext.tokenUtilization,
      qualityScore: optimizedContext.estimatedResponseQuality,
      optimizationsApplied: optimizedContext.optimizationApplied.length
    };
  }

  private extractSymptoms(userMessage: string, conversationHistory: any[]): string[] {
    // Extract symptoms from user message and conversation history
    const symptoms: string[] = [];
    
    // Simple keyword extraction (would be enhanced with NLP)
    const symptomKeywords = ['pain', 'ache', 'fever', 'nausea', 'headache', 'cough', 'fatigue'];
    const message = userMessage.toLowerCase();
    
    symptomKeywords.forEach(keyword => {
      if (message.includes(keyword)) {
        symptoms.push(keyword);
      }
    });
    
    return symptoms;
  }

  private calculateAge(dateOfBirth: string): number {
    if (!dateOfBirth) return 0;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  private determinePriorityMode(urgencyLevel?: string): 'comprehensive' | 'focused' | 'emergency' {
    switch (urgencyLevel) {
      case 'immediate': return 'emergency';
      case 'urgent': return 'focused';
      default: return 'comprehensive';
    }
  }

  private buildEnhancedPrompt(
    systemPrompt: string,
    conversationState: ConversationState,
    clinicalDecision: ClinicalDecision,
    nextQuestions: string[]
  ): string {
    let enhancedPrompt = systemPrompt;
    
    // Add conversation state context
    enhancedPrompt += `\n**Conversation State:**\n`;
    enhancedPrompt += `Current Phase: ${conversationState.currentPhase}\n`;
    enhancedPrompt += `Urgency Level: ${conversationState.urgencyLevel}\n`;
    enhancedPrompt += `Turn: ${conversationState.conversationTurn}\n\n`;
    
    // Add clinical decision context
    if (clinicalDecision.redFlags.length > 0) {
      enhancedPrompt += `**Clinical Alerts:**\n`;
      clinicalDecision.redFlags.forEach(flag => {
        enhancedPrompt += `- ${flag.severity.toUpperCase()}: ${flag.description}\n`;
      });
      enhancedPrompt += '\n';
    }
    
    // Add suggested next questions
    if (nextQuestions.length > 0) {
      enhancedPrompt += `**Suggested Follow-up Questions:**\n`;
      nextQuestions.slice(0, 3).forEach((question, index) => {
        enhancedPrompt += `${index + 1}. ${question}\n`;
      });
      enhancedPrompt += '\n';
    }
    
    return enhancedPrompt;
  }

  private generateStructuredResponse(
    userMessage: string,
    enhancedPrompt: string,
    conversationState: ConversationState,
    clinicalDecision: ClinicalDecision,
    nextQuestions: string[]
  ): string {
    // This would integrate with your AI service
    // For now, return a structured response based on the enhanced context
    
    let response = `Thank you for sharing that information. `;
    
    // Add urgency-appropriate response
    if (conversationState.urgencyLevel === 'immediate') {
      response += `Based on your symptoms, this requires immediate attention. `;
    }
    
    // Add clinical insights
    if (clinicalDecision.differentialDiagnoses.length > 0) {
      const topDiagnosis = clinicalDecision.differentialDiagnoses[0];
      response += `I'm considering several possibilities, with ${topDiagnosis.condition} being a primary consideration. `;
    }
    
    // Add next question
    if (nextQuestions.length > 0) {
      response += `To better understand your condition, ${nextQuestions[0]}`;
    }
    
    return response;
  }

  private mapActionToType(action: string): ConsultationRecommendation['type'] {
    if (action.toLowerCase().includes('emergency') || action.toLowerCase().includes('911')) {
      return 'immediate_action';
    }
    if (action.toLowerCase().includes('test') || action.toLowerCase().includes('lab')) {
      return 'diagnostic_test';
    }
    if (action.toLowerCase().includes('specialist') || action.toLowerCase().includes('referral')) {
      return 'specialist_referral';
    }
    if (action.toLowerCase().includes('lifestyle') || action.toLowerCase().includes('diet')) {
      return 'lifestyle_change';
    }
    return 'follow_up';
  }
}

export const enhancedAgentOrchestrator = new EnhancedAgentOrchestrator();
export default enhancedAgentOrchestrator;
