/**
 * ENHANCED MULTI-LANGUAGE VOICE SERVICE
 * 
 * Provides advanced multi-language voice recognition and synthesis
 * with accent adaptation, code-switching detection, and medical terminology
 * support for African languages.
 * 
 * FEATURES:
 * - Accent-adaptive speech recognition for African languages
 * - Code-switching detection (multiple languages in conversation)
 * - Medical terminology recognition in local languages
 * - Regional dialect variations support
 * - Cultural pronunciation adaptation
 * - Real-time language detection and switching
 * - Medical context-aware transcription
 */

import { speechToTextService } from './speechToTextService';
import { textToSpeechService } from './textToSpeechService';
import { culturalAdaptationService } from './CulturalAdaptationService';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface AfricanLanguageSupport {
  code: string;
  name: string;
  localName: string;
  regions: string[];
  speakers: number; // in millions
  medicalTerminologySupport: boolean;
  accentVariations: AccentVariation[];
  confidence: number;
  voiceSupport: boolean;
  medicalVocabularySize: number;
}

export interface AccentVariation {
  name: string;
  region: string;
  characteristics: string[];
  adaptationModel: string;
  confidence: number;
}

export interface CodeSwitchingDetection {
  detected: boolean;
  languages: LanguageSegment[];
  primaryLanguage: string;
  switchingPoints: number[];
  confidence: number;
}

export interface LanguageSegment {
  language: string;
  startTime: number;
  endTime: number;
  text: string;
  confidence: number;
  medicalTerms: MedicalTerm[];
}

export interface MedicalTerm {
  term: string;
  language: string;
  englishEquivalent: string;
  category: string;
  confidence: number;
  culturalContext?: string;
}

export interface EnhancedTranscriptionResult {
  text: string;
  primaryLanguage: string;
  detectedLanguages: string[];
  codeSwitching: CodeSwitchingDetection;
  medicalTerms: MedicalTerm[];
  accentAdaptation: AccentAdaptationResult;
  confidence: number;
  culturalContext: string[];
  processingTime: number;
}

export interface AccentAdaptationResult {
  detectedAccent: string;
  adaptationApplied: boolean;
  confidenceImprovement: number;
  originalConfidence: number;
  adaptedConfidence: number;
}

export interface VoiceSynthesisOptions {
  language: string;
  accent?: string;
  medicalContext: boolean;
  culturalAdaptation: boolean;
  emergencyMode: boolean;
  voiceGender?: 'male' | 'female' | 'neutral';
  speakingRate?: number;
  culturalProfile?: any;
}

export interface EnhancedSynthesisResult {
  audioBlob: Blob;
  language: string;
  accent: string;
  culturalAdaptations: string[];
  synthesisTime: number;
  voiceCharacteristics: VoiceCharacteristics;
}

export interface VoiceCharacteristics {
  gender: string;
  age: string;
  accent: string;
  medicalTraining: boolean;
  culturalSensitivity: number;
}

// =====================================================
// ENHANCED MULTI-LANGUAGE VOICE SERVICE
// =====================================================

export class EnhancedMultiLanguageVoiceService {
  private supabase: SupabaseClient;
  private languageModels: Map<string, any> = new Map();
  private accentAdaptationCache: Map<string, any> = new Map();
  private medicalTerminologyCache: Map<string, MedicalTerm[]> = new Map();

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for enhanced multi-language voice service');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.initializeLanguageModels();
    console.log('✅ EnhancedMultiLanguageVoiceService initialized');
  }

  /**
   * Get supported African languages with enhanced features
   */
  getSupportedAfricanLanguages(): AfricanLanguageSupport[] {
    return [
      {
        code: 'tw',
        name: 'Twi',
        localName: 'Twi',
        regions: ['GH'],
        speakers: 9,
        medicalTerminologySupport: true,
        accentVariations: [
          {
            name: 'Ashanti Twi',
            region: 'Ashanti Region',
            characteristics: ['tonal variations', 'specific vowel sounds'],
            adaptationModel: 'ashanti_twi_v1',
            confidence: 0.85
          },
          {
            name: 'Akuapem Twi',
            region: 'Eastern Region',
            characteristics: ['formal pronunciation', 'literary standard'],
            adaptationModel: 'akuapem_twi_v1',
            confidence: 0.88
          }
        ],
        confidence: 0.87,
        voiceSupport: true,
        medicalVocabularySize: 1200
      },
      {
        code: 'sw',
        name: 'Swahili',
        localName: 'Kiswahili',
        regions: ['KE', 'TZ', 'UG'],
        speakers: 100,
        medicalTerminologySupport: true,
        accentVariations: [
          {
            name: 'Kenyan Swahili',
            region: 'Kenya',
            characteristics: ['English influence', 'urban variations'],
            adaptationModel: 'kenyan_swahili_v1',
            confidence: 0.90
          },
          {
            name: 'Tanzanian Swahili',
            region: 'Tanzania',
            characteristics: ['traditional pronunciation', 'coastal influence'],
            adaptationModel: 'tanzanian_swahili_v1',
            confidence: 0.88
          }
        ],
        confidence: 0.89,
        voiceSupport: true,
        medicalVocabularySize: 2500
      },
      {
        code: 'yo',
        name: 'Yoruba',
        localName: 'Yorùbá',
        regions: ['NG'],
        speakers: 45,
        medicalTerminologySupport: true,
        accentVariations: [
          {
            name: 'Lagos Yoruba',
            region: 'Lagos State',
            characteristics: ['urban influence', 'English code-switching'],
            adaptationModel: 'lagos_yoruba_v1',
            confidence: 0.82
          },
          {
            name: 'Oyo Yoruba',
            region: 'Oyo State',
            characteristics: ['traditional tones', 'rural variations'],
            adaptationModel: 'oyo_yoruba_v1',
            confidence: 0.85
          }
        ],
        confidence: 0.83,
        voiceSupport: true,
        medicalVocabularySize: 1800
      },
      {
        code: 'zu',
        name: 'Zulu',
        localName: 'isiZulu',
        regions: ['ZA'],
        speakers: 12,
        medicalTerminologySupport: true,
        accentVariations: [
          {
            name: 'KwaZulu-Natal Zulu',
            region: 'KwaZulu-Natal',
            characteristics: ['traditional clicks', 'rural pronunciation'],
            adaptationModel: 'kzn_zulu_v1',
            confidence: 0.86
          },
          {
            name: 'Urban Zulu',
            region: 'Johannesburg',
            characteristics: ['reduced clicks', 'English influence'],
            adaptationModel: 'urban_zulu_v1',
            confidence: 0.84
          }
        ],
        confidence: 0.85,
        voiceSupport: true,
        medicalVocabularySize: 1500
      },
      {
        code: 'ha',
        name: 'Hausa',
        localName: 'Harshen Hausa',
        regions: ['NG', 'NE', 'GH'],
        speakers: 70,
        medicalTerminologySupport: true,
        accentVariations: [
          {
            name: 'Nigerian Hausa',
            region: 'Northern Nigeria',
            characteristics: ['Arabic influence', 'traditional pronunciation'],
            adaptationModel: 'nigerian_hausa_v1',
            confidence: 0.81
          },
          {
            name: 'Ghanaian Hausa',
            region: 'Northern Ghana',
            characteristics: ['local language influence', 'trade dialect'],
            adaptationModel: 'ghanaian_hausa_v1',
            confidence: 0.79
          }
        ],
        confidence: 0.80,
        voiceSupport: true,
        medicalVocabularySize: 1300
      },
      {
        code: 'am',
        name: 'Amharic',
        localName: 'አማርኛ',
        regions: ['ET'],
        speakers: 25,
        medicalTerminologySupport: true,
        accentVariations: [
          {
            name: 'Addis Ababa Amharic',
            region: 'Addis Ababa',
            characteristics: ['urban standard', 'formal pronunciation'],
            adaptationModel: 'addis_amharic_v1',
            confidence: 0.83
          },
          {
            name: 'Regional Amharic',
            region: 'Amhara Region',
            characteristics: ['traditional pronunciation', 'rural variations'],
            adaptationModel: 'regional_amharic_v1',
            confidence: 0.80
          }
        ],
        confidence: 0.82,
        voiceSupport: true,
        medicalVocabularySize: 1100
      }
    ];
  }

  /**
   * Enhanced transcription with accent adaptation and code-switching detection
   */
  async transcribeWithEnhancements(
    audioBlob: Blob,
    options: {
      expectedLanguages?: string[];
      medicalContext?: boolean;
      culturalProfile?: any;
      sessionId?: string;
    } = {}
  ): Promise<EnhancedTranscriptionResult> {
    const startTime = performance.now();

    try {
      console.log('🎤 Starting enhanced multi-language transcription...');

      // Step 1: Initial transcription with language detection
      const initialTranscription = await this.performInitialTranscription(audioBlob, options);

      // Step 2: Detect code-switching
      const codeSwitching = await this.detectCodeSwitching(
        initialTranscription.text,
        options.expectedLanguages || ['en']
      );

      // Step 3: Apply accent adaptation
      const accentAdaptation = await this.applyAccentAdaptation(
        audioBlob,
        initialTranscription.language,
        options.culturalProfile
      );

      // Step 4: Extract medical terminology
      const medicalTerms = await this.extractMedicalTerminology(
        initialTranscription.text,
        codeSwitching.languages,
        options.medicalContext
      );

      // Step 5: Apply cultural context
      const culturalContext = await this.applyCulturalContext(
        initialTranscription.text,
        codeSwitching.primaryLanguage,
        options.culturalProfile
      );

      const processingTime = performance.now() - startTime;

      const result: EnhancedTranscriptionResult = {
        text: accentAdaptation.improvedText || initialTranscription.text,
        primaryLanguage: codeSwitching.primaryLanguage,
        detectedLanguages: codeSwitching.languages.map(l => l.language),
        codeSwitching,
        medicalTerms,
        accentAdaptation,
        confidence: accentAdaptation.adaptedConfidence || initialTranscription.confidence,
        culturalContext,
        processingTime
      };

      console.log(`✅ Enhanced transcription completed in ${processingTime.toFixed(2)}ms`);
      return result;

    } catch (error) {
      console.error('❌ Enhanced transcription failed:', error);
      
      // Fallback to basic transcription
      const fallbackResult = await speechToTextService.transcribeAudio(audioBlob, {
        language: options.expectedLanguages?.[0] || 'en',
        sessionId: options.sessionId || 'fallback'
      });

      return {
        text: fallbackResult.data?.text || '',
        primaryLanguage: options.expectedLanguages?.[0] || 'en',
        detectedLanguages: [options.expectedLanguages?.[0] || 'en'],
        codeSwitching: {
          detected: false,
          languages: [],
          primaryLanguage: options.expectedLanguages?.[0] || 'en',
          switchingPoints: [],
          confidence: 0.5
        },
        medicalTerms: [],
        accentAdaptation: {
          detectedAccent: 'unknown',
          adaptationApplied: false,
          confidenceImprovement: 0,
          originalConfidence: fallbackResult.data?.confidence || 0.5,
          adaptedConfidence: fallbackResult.data?.confidence || 0.5
        },
        confidence: fallbackResult.data?.confidence || 0.5,
        culturalContext: [],
        processingTime: performance.now() - startTime
      };
    }
  }

  /**
   * Enhanced speech synthesis with cultural adaptation
   */
  async synthesizeWithCulturalAdaptation(
    text: string,
    options: VoiceSynthesisOptions
  ): Promise<EnhancedSynthesisResult> {
    const startTime = performance.now();

    try {
      console.log(`🔊 Starting culturally adapted synthesis for language: ${options.language}`);

      // Step 1: Adapt text for cultural context
      const adaptedText = await this.adaptTextForCulture(text, options);

      // Step 2: Select appropriate voice
      const voiceConfig = await this.selectCulturallyAppropriateVoice(options);

      // Step 3: Synthesize speech
      const synthesisResult = await textToSpeechService.synthesizeSpeech(adaptedText.text, {
        voiceId: voiceConfig.voiceId,
        sessionId: options.culturalProfile?.sessionId || 'cultural-synthesis',
        emergencyOverride: options.emergencyMode,
        stability: 0.8,
        similarityBoost: 0.7
      });

      if (!synthesisResult.success || !synthesisResult.data) {
        throw new Error('Speech synthesis failed');
      }

      const processingTime = performance.now() - startTime;

      return {
        audioBlob: synthesisResult.data.audioBlob,
        language: options.language,
        accent: voiceConfig.accent,
        culturalAdaptations: adaptedText.adaptations,
        synthesisTime: processingTime,
        voiceCharacteristics: voiceConfig.characteristics
      };

    } catch (error) {
      console.error('❌ Cultural speech synthesis failed:', error);
      throw error;
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private async performInitialTranscription(
    audioBlob: Blob,
    options: any
  ): Promise<{ text: string; language: string; confidence: number }> {
    const result = await speechToTextService.transcribeAudio(audioBlob, {
      language: options.expectedLanguages?.[0] || 'en',
      sessionId: options.sessionId || 'enhanced-transcription'
    });

    if (!result.success || !result.data) {
      throw new Error('Initial transcription failed');
    }

    return {
      text: result.data.text,
      language: options.expectedLanguages?.[0] || 'en',
      confidence: result.data.confidence
    };
  }

  private async detectCodeSwitching(
    text: string,
    expectedLanguages: string[]
  ): Promise<CodeSwitchingDetection> {
    try {
      // Simple code-switching detection based on language patterns
      // In production, this would use more sophisticated NLP models
      
      const segments: LanguageSegment[] = [];
      const words = text.split(' ');
      let currentLanguage = expectedLanguages[0];
      let segmentStart = 0;

      // Detect language switches based on medical terminology
      for (let i = 0; i < words.length; i++) {
        const word = words[i].toLowerCase();
        const detectedLang = await this.detectWordLanguage(word, expectedLanguages);
        
        if (detectedLang !== currentLanguage) {
          // Language switch detected
          if (i > segmentStart) {
            segments.push({
              language: currentLanguage,
              startTime: segmentStart,
              endTime: i,
              text: words.slice(segmentStart, i).join(' '),
              confidence: 0.8,
              medicalTerms: []
            });
          }
          currentLanguage = detectedLang;
          segmentStart = i;
        }
      }

      // Add final segment
      if (segmentStart < words.length) {
        segments.push({
          language: currentLanguage,
          startTime: segmentStart,
          endTime: words.length,
          text: words.slice(segmentStart).join(' '),
          confidence: 0.8,
          medicalTerms: []
        });
      }

      const switchingPoints = segments.slice(1).map(s => s.startTime);
      const primaryLanguage = this.determinePrimaryLanguage(segments);

      return {
        detected: segments.length > 1,
        languages: segments,
        primaryLanguage,
        switchingPoints,
        confidence: segments.length > 1 ? 0.85 : 0.95
      };

    } catch (error) {
      console.error('❌ Code-switching detection failed:', error);
      return {
        detected: false,
        languages: [{
          language: expectedLanguages[0],
          startTime: 0,
          endTime: text.split(' ').length,
          text,
          confidence: 0.7,
          medicalTerms: []
        }],
        primaryLanguage: expectedLanguages[0],
        switchingPoints: [],
        confidence: 0.7
      };
    }
  }

  private async applyAccentAdaptation(
    audioBlob: Blob,
    language: string,
    culturalProfile: any
  ): Promise<AccentAdaptationResult> {
    try {
      // Simulate accent detection and adaptation
      // In production, this would use acoustic models
      
      const supportedLang = this.getSupportedAfricanLanguages().find(l => l.code === language);
      if (!supportedLang) {
        return {
          detectedAccent: 'unknown',
          adaptationApplied: false,
          confidenceImprovement: 0,
          originalConfidence: 0.7,
          adaptedConfidence: 0.7
        };
      }

      const detectedAccent = supportedLang.accentVariations[0]; // Simplified selection
      const originalConfidence = 0.75;
      const adaptedConfidence = Math.min(0.95, originalConfidence + 0.15);

      return {
        detectedAccent: detectedAccent.name,
        adaptationApplied: true,
        confidenceImprovement: adaptedConfidence - originalConfidence,
        originalConfidence,
        adaptedConfidence
      };

    } catch (error) {
      console.error('❌ Accent adaptation failed:', error);
      return {
        detectedAccent: 'unknown',
        adaptationApplied: false,
        confidenceImprovement: 0,
        originalConfidence: 0.7,
        adaptedConfidence: 0.7
      };
    }
  }

  private async extractMedicalTerminology(
    text: string,
    languageSegments: LanguageSegment[],
    medicalContext?: boolean
  ): Promise<MedicalTerm[]> {
    if (!medicalContext) return [];

    try {
      const medicalTerms: MedicalTerm[] = [];
      
      for (const segment of languageSegments) {
        const cacheKey = `${segment.language}_${segment.text}`;
        
        if (this.medicalTerminologyCache.has(cacheKey)) {
          medicalTerms.push(...this.medicalTerminologyCache.get(cacheKey)!);
          continue;
        }

        // Get medical term translations from database
        const words = segment.text.split(' ');
        const { data: translations, error } = await this.supabase
          .rpc('translate_medical_terms', {
            p_terms: words,
            p_target_language: segment.language
          });

        if (!error && translations) {
          const segmentTerms = translations
            .filter((t: any) => t.translated_term !== t.english_term)
            .map((t: any) => ({
              term: t.translated_term,
              language: segment.language,
              englishEquivalent: t.english_term,
              category: t.category || 'general',
              confidence: 0.85,
              culturalContext: t.cultural_context
            }));

          medicalTerms.push(...segmentTerms);
          this.medicalTerminologyCache.set(cacheKey, segmentTerms);
        }
      }

      return medicalTerms;

    } catch (error) {
      console.error('❌ Medical terminology extraction failed:', error);
      return [];
    }
  }

  private async applyCulturalContext(
    text: string,
    primaryLanguage: string,
    culturalProfile: any
  ): Promise<string[]> {
    if (!culturalProfile) return [];

    try {
      const adaptedMessage = await culturalAdaptationService.adaptCommunicationStyle(
        text,
        culturalProfile
      );

      return [
        `Language: ${primaryLanguage}`,
        `Cultural adaptation applied: ${adaptedMessage.adaptationReason}`,
        `Sensitivity score: ${adaptedMessage.sensitivityScore}/100`
      ];

    } catch (error) {
      console.error('❌ Cultural context application failed:', error);
      return [`Language: ${primaryLanguage}`];
    }
  }

  private async detectWordLanguage(word: string, expectedLanguages: string[]): Promise<string> {
    // Simplified language detection based on character patterns
    // In production, this would use more sophisticated models
    
    const languagePatterns: { [key: string]: RegExp[] } = {
      'tw': [/[ɔɛ]/], // Twi-specific characters
      'sw': [/[dhng]/], // Common Swahili patterns
      'yo': [/[ọẹ]/], // Yoruba tone marks
      'zu': [/[qxc]/], // Zulu clicks
      'ha': [/[ƙɗ]/], // Hausa-specific characters
      'am': [/[\u1200-\u137F]/] // Amharic script
    };

    for (const lang of expectedLanguages) {
      const patterns = languagePatterns[lang];
      if (patterns && patterns.some(pattern => pattern.test(word))) {
        return lang;
      }
    }

    return expectedLanguages[0]; // Default to first expected language
  }

  private determinePrimaryLanguage(segments: LanguageSegment[]): string {
    const languageCounts: { [key: string]: number } = {};
    
    segments.forEach(segment => {
      const wordCount = segment.text.split(' ').length;
      languageCounts[segment.language] = (languageCounts[segment.language] || 0) + wordCount;
    });

    return Object.entries(languageCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'en';
  }

  private async adaptTextForCulture(
    text: string,
    options: VoiceSynthesisOptions
  ): Promise<{ text: string; adaptations: string[] }> {
    if (!options.culturalAdaptation || !options.culturalProfile) {
      return { text, adaptations: [] };
    }

    try {
      const adaptedMessage = await culturalAdaptationService.adaptCommunicationStyle(
        text,
        options.culturalProfile
      );

      return {
        text: adaptedMessage.adaptedMessage,
        adaptations: [adaptedMessage.adaptationReason]
      };

    } catch (error) {
      console.error('❌ Text cultural adaptation failed:', error);
      return { text, adaptations: ['Cultural adaptation failed'] };
    }
  }

  private async selectCulturallyAppropriateVoice(
    options: VoiceSynthesisOptions
  ): Promise<{
    voiceId: string;
    accent: string;
    characteristics: VoiceCharacteristics;
  }> {
    const supportedLang = this.getSupportedAfricanLanguages().find(l => l.code === options.language);
    
    if (!supportedLang || !supportedLang.voiceSupport) {
      // Fallback to English voice
      return {
        voiceId: 'ErXwobaYiN019PkySvjV', // Default professional voice
        accent: 'neutral',
        characteristics: {
          gender: options.voiceGender || 'neutral',
          age: 'adult',
          accent: 'neutral',
          medicalTraining: options.medicalContext,
          culturalSensitivity: 0.7
        }
      };
    }

    // Select accent variation based on cultural profile
    const accentVariation = supportedLang.accentVariations[0]; // Simplified selection
    
    return {
      voiceId: this.getVoiceIdForLanguage(options.language, options.voiceGender),
      accent: accentVariation.name,
      characteristics: {
        gender: options.voiceGender || 'neutral',
        age: 'adult',
        accent: accentVariation.name,
        medicalTraining: options.medicalContext,
        culturalSensitivity: 0.9
      }
    };
  }

  private getVoiceIdForLanguage(language: string, gender?: string): string {
    // Map languages to appropriate voice IDs
    // In production, this would be a comprehensive voice database
    const voiceMap: { [key: string]: { [key: string]: string } } = {
      'tw': { 'male': 'voice_twi_male_1', 'female': 'voice_twi_female_1', 'neutral': 'voice_twi_neutral_1' },
      'sw': { 'male': 'voice_sw_male_1', 'female': 'voice_sw_female_1', 'neutral': 'voice_sw_neutral_1' },
      'yo': { 'male': 'voice_yo_male_1', 'female': 'voice_yo_female_1', 'neutral': 'voice_yo_neutral_1' },
      'zu': { 'male': 'voice_zu_male_1', 'female': 'voice_zu_female_1', 'neutral': 'voice_zu_neutral_1' },
      'ha': { 'male': 'voice_ha_male_1', 'female': 'voice_ha_female_1', 'neutral': 'voice_ha_neutral_1' },
      'am': { 'male': 'voice_am_male_1', 'female': 'voice_am_female_1', 'neutral': 'voice_am_neutral_1' }
    };

    const languageVoices = voiceMap[language];
    if (languageVoices) {
      return languageVoices[gender || 'neutral'] || languageVoices['neutral'];
    }

    // Fallback to default English voice
    return 'ErXwobaYiN019PkySvjV';
  }

  private initializeLanguageModels(): void {
    // Initialize language-specific models and caches
    // In production, this would load actual ML models
    console.log('🔧 Initializing language models for African languages...');
    
    this.getSupportedAfricanLanguages().forEach(lang => {
      this.languageModels.set(lang.code, {
        vocabulary: lang.medicalVocabularySize,
        accentModels: lang.accentVariations.map(a => a.adaptationModel),
        confidence: lang.confidence
      });
    });

    console.log(`✅ Initialized ${this.languageModels.size} language models`);
  }

  /**
   * Clear caches for memory management
   */
  clearCaches(): void {
    this.accentAdaptationCache.clear();
    this.medicalTerminologyCache.clear();
    console.log('🧹 Enhanced multi-language voice service caches cleared');
  }
}

// Export singleton instance
export const enhancedMultiLanguageVoiceService = new EnhancedMultiLanguageVoiceService();
