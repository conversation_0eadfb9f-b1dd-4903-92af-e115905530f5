-- Create async messaging tables for VoiceHealth AI
-- Run this migration after existing auth and consultation system migrations

-- Async Messages Table
CREATE TABLE IF NOT EXISTS async_messages (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL DEFAULT 'voice_message', -- 'voice_message', 'text_message', 'notification'
    sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT,
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'delivering', 'delivered', 'read', 'failed')),
    audio_storage_id TEXT, -- Reference to audio file in storage
    transcription TEXT,
    duration INTEGER DEFAULT 0, -- Duration in seconds
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Voice Messages Table (for encrypted audio file metadata with checksum support)
CREATE TABLE IF NOT EXISTS voice_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    speaker_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    speaker_name TEXT,
    message_type TEXT DEFAULT 'user_voice' CHECK (message_type IN ('user_voice', 'agent_voice', 'async_voice', 'session_audio')),
    cloud_url TEXT, -- URL to encrypted audio file in cloud storage
    duration INTEGER DEFAULT 0,
    quality TEXT DEFAULT 'medium' CHECK (quality IN ('low', 'medium', 'high')),
    transcription TEXT,
    confidence DECIMAL(3,2) DEFAULT 0.95,
    status TEXT DEFAULT 'pending_sync' CHECK (status IN ('pending_sync', 'synced', 'failed')),
    size INTEGER DEFAULT 0, -- Original file size in bytes
    encrypted BOOLEAN DEFAULT true, -- Indicates if audio data is encrypted
    encryption_algorithm TEXT DEFAULT 'AES-256-GCM',
    original_checksum TEXT, -- SHA-256 checksum of original audio data
    encrypted_checksum TEXT, -- SHA-256 checksum of encrypted data
    checksum_algorithm TEXT DEFAULT 'SHA-256',
    integrity_verified BOOLEAN DEFAULT false,
    integrity_last_checked TIMESTAMPTZ,
    data_integrity_hash TEXT, -- Legacy hash for data integrity validation
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Message Notifications Table
CREATE TABLE IF NOT EXISTS message_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id TEXT REFERENCES async_messages(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT DEFAULT 'async_message' CHECK (type IN ('async_message', 'voice_message', 'system_notification')),
    channel TEXT DEFAULT 'email' CHECK (channel IN ('email', 'push', 'sms', 'in_app')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audio Storage Stats Table (for tracking storage usage)
CREATE TABLE IF NOT EXISTS audio_storage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    total_files INTEGER DEFAULT 0,
    total_size BIGINT DEFAULT 0, -- Total size in bytes
    total_duration INTEGER DEFAULT 0, -- Total duration in seconds
    quota_limit BIGINT DEFAULT 104857600, -- 100MB default quota
    last_cleanup TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_async_messages_sender ON async_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_async_messages_recipient ON async_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_async_messages_session ON async_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_async_messages_status ON async_messages(status);
CREATE INDEX IF NOT EXISTS idx_async_messages_created_at ON async_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_async_messages_priority_status ON async_messages(priority, status);

CREATE INDEX IF NOT EXISTS idx_voice_messages_user ON voice_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_messages_session ON voice_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_voice_messages_status ON voice_messages(status);
CREATE INDEX IF NOT EXISTS idx_voice_messages_created_at ON voice_messages(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_message_notifications_recipient ON message_notifications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_message_notifications_message ON message_notifications(message_id);
CREATE INDEX IF NOT EXISTS idx_message_notifications_status ON message_notifications(status);

-- Row Level Security (RLS) Policies
ALTER TABLE async_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_storage_stats ENABLE ROW LEVEL SECURITY;

-- Async Messages Policies
CREATE POLICY "Users can view their own messages" ON async_messages
    FOR SELECT USING (
        auth.uid() = sender_id OR 
        auth.uid() = recipient_id
    );

CREATE POLICY "Users can insert their own messages" ON async_messages
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update their own messages" ON async_messages
    FOR UPDATE USING (
        auth.uid() = sender_id OR 
        auth.uid() = recipient_id
    );

CREATE POLICY "Users can delete their own messages" ON async_messages
    FOR DELETE USING (
        auth.uid() = sender_id OR 
        auth.uid() = recipient_id
    );

-- Voice Messages Policies
CREATE POLICY "Users can view their own voice messages" ON voice_messages
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() = speaker_id
    );

CREATE POLICY "Users can insert their own voice messages" ON voice_messages
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own voice messages" ON voice_messages
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own voice messages" ON voice_messages
    FOR DELETE USING (auth.uid() = user_id);

-- Message Notifications Policies
CREATE POLICY "Users can view their own notifications" ON message_notifications
    FOR SELECT USING (auth.uid() = recipient_id);

CREATE POLICY "System can insert notifications" ON message_notifications
    FOR INSERT WITH CHECK (true); -- Allow system to insert notifications

CREATE POLICY "Users can update their own notifications" ON message_notifications
    FOR UPDATE USING (auth.uid() = recipient_id);

-- Audio Storage Stats Policies
CREATE POLICY "Users can view their own storage stats" ON audio_storage_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own storage stats" ON audio_storage_stats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own storage stats" ON audio_storage_stats
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at columns
CREATE TRIGGER update_async_messages_updated_at 
    BEFORE UPDATE ON async_messages 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audio_storage_stats_updated_at 
    BEFORE UPDATE ON audio_storage_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update storage stats
CREATE OR REPLACE FUNCTION update_audio_storage_stats()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audio_storage_stats (user_id, total_files, total_size, total_duration)
    VALUES (NEW.user_id, 1, NEW.size, NEW.duration)
    ON CONFLICT (user_id) 
    DO UPDATE SET
        total_files = audio_storage_stats.total_files + 1,
        total_size = audio_storage_stats.total_size + NEW.size,
        total_duration = audio_storage_stats.total_duration + NEW.duration,
        updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update storage stats when voice messages are added
CREATE TRIGGER update_storage_stats_on_voice_message_insert
    AFTER INSERT ON voice_messages
    FOR EACH ROW EXECUTE FUNCTION update_audio_storage_stats();

-- Function to cleanup old voice messages (for storage management)
CREATE OR REPLACE FUNCTION cleanup_old_voice_messages(cleanup_days INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH deleted AS (
        DELETE FROM voice_messages 
        WHERE 
            created_at < NOW() - INTERVAL '1 day' * cleanup_days
            AND status = 'synced'
        RETURNING user_id, size, duration
    )
    UPDATE audio_storage_stats 
    SET 
        total_files = GREATEST(0, total_files - (SELECT COUNT(*) FROM deleted WHERE deleted.user_id = audio_storage_stats.user_id)),
        total_size = GREATEST(0, total_size - (SELECT COALESCE(SUM(size), 0) FROM deleted WHERE deleted.user_id = audio_storage_stats.user_id)),
        total_duration = GREATEST(0, total_duration - (SELECT COALESCE(SUM(duration), 0) FROM deleted WHERE deleted.user_id = audio_storage_stats.user_id)),
        last_cleanup = NOW(),
        updated_at = NOW()
    WHERE user_id IN (SELECT DISTINCT user_id FROM deleted);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Function to get user's message statistics
CREATE OR REPLACE FUNCTION get_user_message_stats(user_uuid UUID)
RETURNS TABLE (
    total_sent INTEGER,
    total_received INTEGER,
    unread_count INTEGER,
    total_audio_duration INTEGER,
    storage_used BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM async_messages WHERE sender_id = user_uuid),
        (SELECT COUNT(*)::INTEGER FROM async_messages WHERE recipient_id = user_uuid),
        (SELECT COUNT(*)::INTEGER FROM async_messages WHERE recipient_id = user_uuid AND status IN ('delivered', 'pending')),
        (SELECT COALESCE(SUM(duration), 0)::INTEGER FROM voice_messages WHERE user_id = user_uuid),
        (SELECT COALESCE(total_size, 0) FROM audio_storage_stats WHERE user_id = user_uuid);
END;
$$ language 'plpgsql';

-- Storage bucket for voice messages (if not exists)
INSERT INTO storage.buckets (id, name, public)
VALUES ('voice-messages', 'voice-messages', false)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for voice messages bucket
CREATE POLICY "Users can upload their own voice messages" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'voice-messages' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own voice messages" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'voice-messages' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own voice messages" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'voice-messages' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own voice messages" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'voice-messages' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Add notification preferences to user_profiles if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'notification_preferences'
    ) THEN
        ALTER TABLE user_profiles 
        ADD COLUMN notification_preferences JSONB DEFAULT '{
            "async_messages": true,
            "voice_messages": true,
            "consultation_reminders": true,
            "system_notifications": true,
            "email_notifications": true,
            "push_notifications": true
        }'::jsonb;
    END IF;
END $$;

-- Comments for documentation
COMMENT ON TABLE async_messages IS 'Stores asynchronous messages between users and AI agents';
COMMENT ON TABLE voice_messages IS 'Stores metadata for voice message audio files';
COMMENT ON TABLE message_notifications IS 'Tracks notification delivery status for messages';
COMMENT ON TABLE audio_storage_stats IS 'Tracks audio storage usage and quotas per user';

COMMENT ON FUNCTION cleanup_old_voice_messages IS 'Cleans up old voice messages to manage storage space';
COMMENT ON FUNCTION get_user_message_stats IS 'Returns comprehensive message statistics for a user';