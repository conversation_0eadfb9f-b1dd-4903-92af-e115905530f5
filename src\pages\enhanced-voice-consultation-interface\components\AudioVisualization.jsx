import React, { useEffect, useRef, useState } from 'react';
import Icon from '../../../components/AppIcon';

const AudioVisualization = ({
  frequencyData = new Uint8Array(256),
  audioLevel = 0,
  isRecording = false,
  currentPhase = 'ready',
  showSpectrumAnalysis = true,
  showVoiceActivityDetection = true,
  className = ''
}) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const [voiceActivity, setVoiceActivity] = useState(false);
  const [dominantFrequency, setDominantFrequency] = useState(0);
  const [spectralCentroid, setSpectralCentroid] = useState(0);
  const [renderError, setRenderError] = useState(false);

  useEffect(() => {
    if (!canvasRef.current) return;

    try {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        setRenderError(true);
        return;
      }
      
      const animate = () => {
        try {
          if (!isRecording) return;

          // Clear canvas with error handling
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // Draw frequency spectrum
          if (showSpectrumAnalysis) {
            drawFrequencySpectrum(ctx, canvas, frequencyData);
          }
          
          // Draw waveform
          drawWaveform(ctx, canvas, audioLevel);
          
          // Calculate voice activity
          if (showVoiceActivityDetection) {
            calculateVoiceActivity(frequencyData);
          }
          
          animationRef.current = requestAnimationFrame(animate);
        } catch (error) {
          console.error('Canvas animation error:', error);
          setRenderError(true);
        }
      };

      if (isRecording && !renderError) {
        animate();
      }

      return () => {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    } catch (error) {
      console.error('Canvas setup error:', error);
      setRenderError(true);
    }
  }, [isRecording, frequencyData, audioLevel, showSpectrumAnalysis, showVoiceActivityDetection, renderError]);

  const drawFrequencySpectrum = (ctx, canvas, data) => {
    try {
      const barWidth = canvas.width / data.length;
      const barHeightScale = canvas.height / 255;

      ctx.fillStyle = 'rgba(59, 130, 246, 0.6)'; // Blue spectrum

      for (let i = 0; i < data.length; i++) {
        const barHeight = data[i] * barHeightScale * 0.8;
        const x = i * barWidth;
        const y = canvas.height - barHeight;

        // Create gradient for each bar with error handling
        try {
          const gradient = ctx.createLinearGradient(0, y, 0, canvas.height);
          gradient.addColorStop(0, `rgba(59, 130, 246, ${data[i] / 255})`);
          gradient.addColorStop(1, `rgba(29, 78, 216, ${data[i] / 255})`);
          
          ctx.fillStyle = gradient;
          ctx.fillRect(x, y, barWidth - 1, barHeight);
        } catch (gradientError) {
          // Fallback to solid color
          ctx.fillStyle = 'rgba(59, 130, 246, 0.6)';
          ctx.fillRect(x, y, barWidth - 1, barHeight);
        }
      }

      // Find dominant frequency
      const maxIndex = data.indexOf(Math.max(...data));
      const nyquist = 22050; // Assuming 44.1kHz sample rate
      const dominantFreq = (maxIndex * nyquist) / data.length;
      setDominantFrequency(Math.round(dominantFreq));

      // Calculate spectral centroid
      let numerator = 0;
      let denominator = 0;
      for (let i = 0; i < data.length; i++) {
        const frequency = (i * nyquist) / data.length;
        numerator += frequency * data[i];
        denominator += data[i];
      }
      setSpectralCentroid(Math.round(numerator / denominator) || 0);
    } catch (error) {
      console.error('Frequency spectrum drawing error:', error);
    }
  };

  const drawWaveform = (ctx, canvas, level) => {
    try {
      const centerY = canvas.height / 2;
      const amplitude = (level / 100) * (canvas.height / 4);
      
      ctx.strokeStyle = 'rgba(16, 185, 129, 0.8)'; // Green waveform
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      for (let x = 0; x < canvas.width; x++) {
        const t = x / canvas.width;
        const wave1 = Math.sin(t * Math.PI * 4 + Date.now() * 0.01) * amplitude * 0.5;
        const wave2 = Math.sin(t * Math.PI * 6 + Date.now() * 0.008) * amplitude * 0.3;
        const y = centerY + wave1 + wave2;
        
        if (x === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.stroke();
    } catch (error) {
      console.error('Waveform drawing error:', error);
    }
  };

  const calculateVoiceActivity = (data) => {
    try {
      // Simple voice activity detection based on frequency analysis
      const voiceFreqRange = data.slice(20, 80); // Rough voice frequency range
      const voiceEnergy = voiceFreqRange.reduce((sum, val) => sum + val, 0) / voiceFreqRange.length;
      const threshold = 30; // Adjustable threshold
      
      setVoiceActivity(voiceEnergy > threshold);
    } catch (error) {
      console.error('Voice activity calculation error:', error);
      setVoiceActivity(false);
    }
  };

  const getPhaseColor = () => {
    switch (currentPhase) {
      case 'listening': return 'border-primary-500';
      case 'processing': return 'border-warning-500';
      case 'collaborating': return 'border-purple-500';
      case 'responding': return 'border-success-500';
      default: return 'border-secondary-300';
    }
  };

  // Error fallback
  if (renderError) {
    return (
      <div className={`bg-surface border rounded-xl p-6 border-secondary-300 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Icon name="AlertTriangle" size={20} color="var(--color-warning-600)" />
            <h3 className="font-semibold text-text-primary font-heading">
              Audio Visualization
            </h3>
          </div>
        </div>
        
        <div className="bg-secondary-50 rounded-lg p-8 text-center">
          <Icon name="Activity" size={48} className="mx-auto mb-4 text-secondary-400" />
          <p className="text-text-secondary">
            Audio visualization temporarily unavailable
          </p>
          <p className="text-xs text-text-muted mt-2">
            Audio processing continues normally
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface border rounded-xl p-6 gpu-accelerated ${getPhaseColor()} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <Icon name="Activity" size={20} color="var(--color-text-primary)" />
          <h3 className="font-semibold text-text-primary font-heading">
            Audio Analysis
          </h3>
          {showVoiceActivityDetection && (
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full transition-all duration-200 ${voiceActivity ? 'bg-success-500 animate-pulse' : 'bg-secondary-300'}`}></div>
              <span className="text-sm text-text-secondary">
                {voiceActivity ? 'Voice Detected' : 'No Voice'}
              </span>
            </div>
          )}
        </div>

        <div className="text-right">
          <div className="text-sm text-text-secondary">Audio Level</div>
          <div className="text-lg font-bold text-primary-600">
            {Math.round(audioLevel)}%
          </div>
        </div>
      </div>

      {/* Main Visualization Canvas */}
      <div className="bg-secondary-50 rounded-lg p-4 mb-4 gpu-accelerated">
        <canvas
          ref={canvasRef}
          width={400}
          height={150}
          className="w-full h-32 rounded animate-canvas-glow"
          style={{ 
            background: 'linear-gradient(to bottom, rgba(241,245,249,0.5), rgba(248,250,252,0.8))',
            willChange: 'filter'
          }}
        />
      </div>

      {/* Audio Metrics */}
      {(isRecording || currentPhase !== 'ready') && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 gpu-accelerated">
          <div className="text-center">
            <div className="text-xs text-text-secondary">Phase</div>
            <div className="text-sm font-medium text-text-primary capitalize">
              {currentPhase}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-text-secondary">Peak Freq</div>
            <div className="text-sm font-medium text-text-primary">
              {dominantFrequency} Hz
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-text-secondary">Centroid</div>
            <div className="text-sm font-medium text-text-primary">
              {spectralCentroid} Hz
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-text-secondary">Quality</div>
            <div className={`text-sm font-medium ${
              audioLevel > 60 ? 'text-success-600' : 
              audioLevel > 30 ? 'text-warning-600' : 'text-error-600'
            }`}>
              {audioLevel > 60 ? 'Good' : audioLevel > 30 ? 'Fair' : 'Low'}
            </div>
          </div>
        </div>
      )}

      {/* Visual Indicator Bars */}
      {isRecording && (
        <div className="flex items-center justify-center space-x-2 mb-4 gpu-accelerated">
          {[...Array(12)].map((_, i) => (
            <div
              key={i}
              className="voice-waveform-bar bg-primary-500"
              style={{
                height: `${Math.random() * 30 + 10}px`,
                animationDelay: `${i * 0.1}s`,
                opacity: audioLevel > (i * 8) ? 1 : 0.3,
                willChange: 'height, opacity'
              }}
            ></div>
          ))}
        </div>
      )}

      {/* Status Messages */}
      <div className="text-center">
        <p className="text-sm text-text-secondary">
          {!isRecording && currentPhase === 'ready' && 'Ready for audio input'}
          {isRecording && 'Real-time audio analysis active'}
          {!isRecording && currentPhase === 'processing' && 'Processing audio data...'}
          {!isRecording && currentPhase === 'collaborating' && 'Multi-agent collaboration in progress...'}
          {!isRecording && currentPhase === 'responding' && 'Preparing audio response...'}
        </p>
      </div>
    </div>
  );
};

export default AudioVisualization;