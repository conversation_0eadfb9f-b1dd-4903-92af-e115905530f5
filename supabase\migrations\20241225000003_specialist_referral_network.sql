-- Specialist Referral Network Database Schema
-- VoiceHealth AI - Phase 3 Implementation
-- Created: 2025-01-06

-- =====================================================
-- SPECIALIST DIRECTORY TABLES
-- =====================================================

-- Main specialists table
CREATE TABLE IF NOT EXISTS specialists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    title VARCHAR(100) NOT NULL,
    specialty VARCHAR(100) NOT NULL,
    sub_specialties TEXT[],
    license_number VARCHAR(100),
    license_country VARCHAR(10) NOT NULL,
    languages_spoken TEXT[] DEFAULT ARRAY['en'],
    cultural_competencies TEXT[],
    telemedicine_capable BOOLEAN DEFAULT FALSE,
    emergency_available BOOLEAN DEFAULT FALSE,
    rating DECIMAL(3,2) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
    review_count INTEGER DEFAULT 0,
    response_time INTEGER DEFAULT 24, -- hours
    wait_time INTEGER DEFAULT 7, -- days
    verified BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Specialist qualifications
CREATE TABLE IF NOT EXISTS specialist_qualifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    degree VARCHAR(200) NOT NULL,
    institution VARCHAR(300) NOT NULL,
    graduation_year INTEGER NOT NULL,
    country VARCHAR(10) NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Specialist locations and facilities
CREATE TABLE IF NOT EXISTS specialist_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    facility_name VARCHAR(300) NOT NULL,
    facility_type VARCHAR(50) CHECK (facility_type IN ('hospital', 'clinic', 'private_practice', 'telemedicine')),
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    region VARCHAR(100) NOT NULL,
    country VARCHAR(10) NOT NULL,
    postal_code VARCHAR(20),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    facility_rating DECIMAL(3,2) DEFAULT 0.0,
    emergency_services BOOLEAN DEFAULT FALSE,
    parking_available BOOLEAN DEFAULT FALSE,
    wheelchair_accessible BOOLEAN DEFAULT FALSE,
    public_transport_access BOOLEAN DEFAULT FALSE,
    primary_location BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contact information
CREATE TABLE IF NOT EXISTS specialist_contact_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    whatsapp VARCHAR(20),
    telemedicine_url TEXT,
    booking_url TEXT,
    preferred_contact_method VARCHAR(20) DEFAULT 'phone' CHECK (preferred_contact_method IN ('phone', 'email', 'whatsapp', 'online')),
    office_hours JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Consultation fees
CREATE TABLE IF NOT EXISTS specialist_consultation_fees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    currency VARCHAR(3) DEFAULT 'USD',
    initial_consultation DECIMAL(10,2) NOT NULL,
    follow_up_consultation DECIMAL(10,2) NOT NULL,
    telemedicine_consultation DECIMAL(10,2),
    emergency_consultation DECIMAL(10,2),
    payment_methods TEXT[] DEFAULT ARRAY['cash'],
    insurance_billing BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insurance providers accepted
CREATE TABLE IF NOT EXISTS specialist_insurance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    insurance_provider VARCHAR(200) NOT NULL,
    policy_types TEXT[],
    direct_billing BOOLEAN DEFAULT FALSE,
    copay_required BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- AVAILABILITY AND SCHEDULING TABLES
-- =====================================================

-- Specialist availability schedule
CREATE TABLE IF NOT EXISTS specialist_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    time TIME NOT NULL,
    duration INTEGER DEFAULT 30, -- minutes
    appointment_type VARCHAR(20) DEFAULT 'consultation' CHECK (appointment_type IN ('consultation', 'telemedicine', 'emergency', 'follow_up')),
    available BOOLEAN DEFAULT TRUE,
    cost DECIMAL(10,2),
    location_id UUID REFERENCES specialist_locations(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique time slots per specialist
    UNIQUE(specialist_id, date, time)
);

-- Scheduled appointments
CREATE TABLE IF NOT EXISTS specialist_appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    patient_id UUID, -- References auth.users(id) but not enforced for flexibility
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    appointment_type VARCHAR(20) NOT NULL,
    duration INTEGER DEFAULT 30,
    cost DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no_show')),
    cultural_preferences JSONB,
    special_requirements TEXT,
    telemedicine_link TEXT,
    confirmation_sent BOOLEAN DEFAULT FALSE,
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- REFERRAL TRACKING TABLES
-- =====================================================

-- Referral requests and recommendations
CREATE TABLE IF NOT EXISTS specialist_referrals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL,
    referring_agent VARCHAR(100), -- Which AI agent made the referral
    condition VARCHAR(200) NOT NULL,
    symptoms TEXT[],
    urgency_level VARCHAR(20) NOT NULL CHECK (urgency_level IN ('routine', 'urgent', 'emergency')),
    preferred_location VARCHAR(100),
    max_travel_distance INTEGER, -- kilometers
    cultural_preferences JSONB,
    insurance_info JSONB,
    budget_constraints JSONB,
    telemedicine_preference BOOLEAN DEFAULT FALSE,
    language_preference VARCHAR(10) DEFAULT 'en',
    previous_specialists UUID[],
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'recommendations_sent', 'appointment_scheduled', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Referral recommendations given to patients
CREATE TABLE IF NOT EXISTS referral_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referral_id UUID REFERENCES specialist_referrals(id) ON DELETE CASCADE,
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    match_score INTEGER CHECK (match_score >= 0 AND match_score <= 100),
    match_reasons TEXT[],
    estimated_wait_time INTEGER, -- days
    estimated_cost DECIMAL(10,2),
    cultural_compatibility INTEGER CHECK (cultural_compatibility >= 0 AND cultural_compatibility <= 100),
    quality_score INTEGER CHECK (quality_score >= 0 AND quality_score <= 100),
    distance_km DECIMAL(8,2),
    telemedicine_option BOOLEAN DEFAULT FALSE,
    emergency_available BOOLEAN DEFAULT FALSE,
    insurance_covered BOOLEAN DEFAULT FALSE,
    special_notes TEXT[],
    recommendation_rank INTEGER,
    patient_viewed BOOLEAN DEFAULT FALSE,
    patient_selected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Referral outcomes and quality tracking
CREATE TABLE IF NOT EXISTS referral_outcomes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referral_id UUID REFERENCES specialist_referrals(id) ON DELETE CASCADE,
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL,
    appointment_scheduled BOOLEAN DEFAULT FALSE,
    appointment_date TIMESTAMP WITH TIME ZONE,
    consultation_completed BOOLEAN DEFAULT FALSE,
    consultation_date TIMESTAMP WITH TIME ZONE,
    patient_satisfaction INTEGER CHECK (patient_satisfaction >= 1 AND patient_satisfaction <= 5),
    clinical_outcome TEXT,
    follow_up_required BOOLEAN DEFAULT FALSE,
    referral_quality INTEGER CHECK (referral_quality >= 1 AND referral_quality <= 5),
    feedback TEXT,
    outcome_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SPECIALIST REVIEWS AND RATINGS
-- =====================================================

-- Patient reviews of specialists
CREATE TABLE IF NOT EXISTS specialist_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    specialist_id UUID REFERENCES specialists(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL,
    appointment_id UUID REFERENCES specialist_appointments(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    cultural_sensitivity_rating INTEGER CHECK (cultural_sensitivity_rating >= 1 AND cultural_sensitivity_rating <= 5),
    communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
    wait_time_rating INTEGER CHECK (wait_time_rating >= 1 AND wait_time_rating <= 5),
    facility_rating INTEGER CHECK (facility_rating >= 1 AND facility_rating <= 5),
    would_recommend BOOLEAN,
    anonymous BOOLEAN DEFAULT FALSE,
    verified_patient BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Specialists indexes
CREATE INDEX IF NOT EXISTS idx_specialists_specialty ON specialists(specialty);
CREATE INDEX IF NOT EXISTS idx_specialists_location ON specialists(license_country);
CREATE INDEX IF NOT EXISTS idx_specialists_rating ON specialists(rating DESC);
CREATE INDEX IF NOT EXISTS idx_specialists_verified ON specialists(verified, active);
CREATE INDEX IF NOT EXISTS idx_specialists_emergency ON specialists(emergency_available);
CREATE INDEX IF NOT EXISTS idx_specialists_telemedicine ON specialists(telemedicine_capable);

-- Availability indexes
CREATE INDEX IF NOT EXISTS idx_availability_specialist_date ON specialist_availability(specialist_id, date);
CREATE INDEX IF NOT EXISTS idx_availability_date_available ON specialist_availability(date, available);
CREATE INDEX IF NOT EXISTS idx_availability_type ON specialist_availability(appointment_type);

-- Referral indexes
CREATE INDEX IF NOT EXISTS idx_referrals_patient ON specialist_referrals(patient_id);
CREATE INDEX IF NOT EXISTS idx_referrals_status ON specialist_referrals(status);
CREATE INDEX IF NOT EXISTS idx_referrals_urgency ON specialist_referrals(urgency_level);
CREATE INDEX IF NOT EXISTS idx_referrals_condition ON specialist_referrals(condition);

-- Recommendations indexes
CREATE INDEX IF NOT EXISTS idx_recommendations_referral ON referral_recommendations(referral_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_specialist ON referral_recommendations(specialist_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_score ON referral_recommendations(match_score DESC);

-- Location indexes for geographic queries
CREATE INDEX IF NOT EXISTS idx_locations_country_city ON specialist_locations(country, city);
CREATE INDEX IF NOT EXISTS idx_locations_coordinates ON specialist_locations(latitude, longitude);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE specialists ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_qualifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_contact_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_consultation_fees ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_insurance ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_outcomes ENABLE ROW LEVEL SECURITY;
ALTER TABLE specialist_reviews ENABLE ROW LEVEL SECURITY;

-- Public read policies for verified specialists
CREATE POLICY "public_read_verified_specialists" ON specialists 
    FOR SELECT USING (verified = true AND active = true);

CREATE POLICY "public_read_specialist_info" ON specialist_locations 
    FOR SELECT USING (true);

CREATE POLICY "public_read_contact_info" ON specialist_contact_info 
    FOR SELECT USING (true);

CREATE POLICY "public_read_consultation_fees" ON specialist_consultation_fees 
    FOR SELECT USING (true);

CREATE POLICY "public_read_availability" ON specialist_availability 
    FOR SELECT USING (available = true);

-- User-specific policies for appointments and referrals
CREATE POLICY "user_appointments" ON specialist_appointments 
    FOR ALL USING (auth.uid()::text = patient_id);

CREATE POLICY "user_referrals" ON specialist_referrals 
    FOR ALL USING (auth.uid()::text = patient_id);

CREATE POLICY "user_referral_recommendations" ON referral_recommendations 
    FOR SELECT USING (
        referral_id IN (
            SELECT id FROM specialist_referrals WHERE patient_id = auth.uid()::text
        )
    );

CREATE POLICY "user_reviews" ON specialist_reviews 
    FOR ALL USING (auth.uid()::text = patient_id);

-- Admin policies for data management
CREATE POLICY "admin_full_access_specialists" ON specialists 
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "admin_full_access_referrals" ON specialist_referrals 
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Apply updated_at triggers
CREATE TRIGGER update_specialists_updated_at 
    BEFORE UPDATE ON specialists 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_consultation_fees_updated_at 
    BEFORE UPDATE ON specialist_consultation_fees 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON specialist_appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_referrals_updated_at 
    BEFORE UPDATE ON specialist_referrals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_outcomes_updated_at 
    BEFORE UPDATE ON referral_outcomes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE specialists IS 'Main directory of healthcare specialists with qualifications and capabilities';
COMMENT ON TABLE specialist_availability IS 'Real-time availability schedule for specialist appointments';
COMMENT ON TABLE specialist_referrals IS 'Patient referral requests with cultural and clinical context';
COMMENT ON TABLE referral_recommendations IS 'AI-generated specialist recommendations with match scoring';
COMMENT ON TABLE referral_outcomes IS 'Quality tracking and outcome monitoring for referrals';
COMMENT ON TABLE specialist_reviews IS 'Patient feedback and ratings for continuous quality improvement';

-- Migration completed successfully
SELECT 'Specialist Referral Network schema migration completed successfully' as status;
