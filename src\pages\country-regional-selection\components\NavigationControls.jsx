import React from 'react';
import Icon from '../../../components/AppIcon';

const NavigationControls = ({ 
  onBack, 
  onNext, 
  isNextDisabled, 
  isLoading, 
  selectedLanguage 
}) => {
  const getLocalizedText = (key) => {
    const texts = {
      back: {
        en: 'Back',
        tw: 'San kɔ',
        yo: 'Pada',
        sw: 'Nyuma',
        af: 'Terug'
      },
      next: {
        en: 'Next',
        tw: 'Ɛtoɔ',
        yo: 'Tẹle',
        sw: 'Ijayo',
        af: 'Volgende'
      },
      selectCountry: {
        en: 'Please select a country to continue',
        tw: 'Yɛsrɛ yi ɔman bi na toa so',
        yo: 'Jọwọ yan orilẹ-ede kan lati tẹsiwaju',
        sw: 'Tafadhali chagua nchi ili kuendelea',
        af: 'Kies asseblief \'n land om voort te gaan'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="space-y-4">
      {/* Help Text */}
      {isNextDisabled && (
        <div className="text-center">
          <p className="text-sm text-text-muted">
            {getLocalizedText('selectCountry')}
          </p>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex space-x-4">
        {/* Back Button */}
        <button
          onClick={onBack}
          className="flex-1 bg-surface border border-border text-text-primary font-medium py-3 px-6 rounded-xl hover:bg-secondary-50 hover:border-secondary-300 transition-all duration-200 flex items-center justify-center space-x-2 min-h-14"
        >
          <Icon name="ArrowLeft" size={18} color="var(--color-text-primary)" />
          <span>{getLocalizedText('back')}</span>
        </button>

        {/* Next Button */}
        <button
          onClick={onNext}
          disabled={isNextDisabled || isLoading}
          className={`flex-1 font-medium py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 min-h-14 ${
            isNextDisabled || isLoading
              ? 'bg-secondary-200 text-secondary-400 cursor-not-allowed' :'bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white shadow-elevated hover:shadow-floating transform hover:scale-[1.02] active:scale-[0.98]'
          }`}
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span>Loading...</span>
            </>
          ) : (
            <>
              <span>{getLocalizedText('next')}</span>
              <Icon name="ArrowRight" size={18} color="currentColor" />
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default NavigationControls;