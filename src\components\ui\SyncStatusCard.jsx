import React, { useState, useEffect } from 'react';
import { RefreshC<PERSON>, Cloud, CloudOff, CheckCircle, XCircle, Clock } from 'lucide-react';
import pwaService from '../../utils/pwaService';
import offlineHealthRecordsService from '../../utils/offlineHealthRecordsService';

const SyncStatusCard = () => {
  const [syncStatus, setSyncStatus] = useState({
    isOnline: navigator.onLine,
    pendingCount: 0,
    syncing: false,
    lastSync: null,
    storageInfo: null
  });

  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const unsubscribe = pwaService.subscribe((event, data) => {
      setSyncStatus(prev => {
        switch (event) {
          case 'online':
            return { ...prev, isOnline: true };
          case 'offline':
            return { ...prev, isOnline: false };
          case 'pendingActionAdded':
            return { ...prev, pendingCount: data?.count || 0 };
          case 'syncStarted':
            return { ...prev, syncing: true };
          case 'syncCompleted':
            return { 
              ...prev, 
              syncing: false, 
              pendingCount: data?.remaining || 0,
              lastSync: new Date()
            };
          default:
            return prev;
        }
      });
    });

    // Load initial data
    loadStorageInfo();

    return unsubscribe;
  }, []);

  const loadStorageInfo = async () => {
    const storageInfo = await offlineHealthRecordsService.getStorageInfo();
    setSyncStatus(prev => ({
      ...prev,
      storageInfo,
      pendingCount: pwaService.getPendingActionsCount()
    }));
  };

  const handleManualSync = async () => {
    if (syncStatus.isOnline && !syncStatus.syncing) {
      await pwaService.forceSync();
    }
  };

  const handleClearOfflineData = async () => {
    if (confirm('Are you sure you want to clear all offline data? This action cannot be undone.')) {
      await offlineHealthRecordsService.clearAllOfflineData();
      await loadStorageInfo();
    }
  };

  const formatLastSync = (date) => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getTotalOfflineRecords = () => {
    if (!syncStatus.storageInfo) return 0;
    
    return Object.values(syncStatus.storageInfo).reduce((total, count) => total + count, 0);
  };

  return (
    <div className="bg-white rounded-lg shadow-md border">
      {/* Header */}
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Status Icon */}
            <div className={`p-2 rounded-full ${
              syncStatus.syncing ? 'bg-blue-100' : syncStatus.isOnline ?'bg-green-100' : 'bg-red-100'
            }`}>
              {syncStatus.syncing ? (
                <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
              ) : syncStatus.isOnline ? (
                syncStatus.pendingCount > 0 ? (
                  <CloudOff className="w-5 h-5 text-orange-600" />
                ) : (
                  <Cloud className="w-5 h-5 text-green-600" />
                )
              ) : (
                <CloudOff className="w-5 h-5 text-red-600" />
              )}
            </div>

            <div>
              <h3 className="font-semibold text-gray-900">
                Sync Status
              </h3>
              <p className="text-sm text-gray-600">
                {syncStatus.syncing ? 'Syncing data...' :
                 syncStatus.isOnline ? 
                   syncStatus.pendingCount > 0 ? 
                     `${syncStatus.pendingCount} items pending sync` : 
                     'All data synced' : 'Offline mode'}
              </p>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="flex items-center space-x-2">
            {syncStatus.pendingCount > 0 && (
              <div className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                {syncStatus.pendingCount}
              </div>
            )}
            
            <div className={`w-3 h-3 rounded-full ${
              syncStatus.isOnline ? 'bg-green-400' : 'bg-red-400'
            }`} />
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {expanded && (
        <div className="border-t p-4 space-y-4">
          {/* Connection Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-1">
                {syncStatus.isOnline ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
                <span className="text-sm font-medium">Connection</span>
              </div>
              <p className="text-xs text-gray-600">
                {syncStatus.isOnline ? 'Online' : 'Offline'}
              </p>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2 mb-1">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium">Last Sync</span>
              </div>
              <p className="text-xs text-gray-600">
                {formatLastSync(syncStatus.lastSync)}
              </p>
            </div>
          </div>

          {/* Offline Storage Info */}
          {syncStatus.storageInfo && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                Offline Storage ({getTotalOfflineRecords()} records)
              </h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-blue-700">Medical Conditions:</span>
                  <span className="font-medium">{syncStatus.storageInfo.medicalConditions || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Medications:</span>
                  <span className="font-medium">{syncStatus.storageInfo.medications || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Sessions:</span>
                  <span className="font-medium">{syncStatus.storageInfo.consultationSessions || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Messages:</span>
                  <span className="font-medium">{syncStatus.storageInfo.conversationMessages || 0}</span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <button
              onClick={handleManualSync}
              disabled={!syncStatus.isOnline || syncStatus.syncing}
              className="flex-1 bg-blue-500 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-1"
            >
              <RefreshCw className={`w-4 h-4 ${syncStatus.syncing ? 'animate-spin' : ''}`} />
              <span>{syncStatus.syncing ? 'Syncing...' : 'Sync Now'}</span>
            </button>

            <button
              onClick={handleClearOfflineData}
              className="px-3 py-2 border border-red-300 text-red-700 rounded-md text-sm font-medium hover:bg-red-50 transition-colors"
            >
              Clear Offline Data
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SyncStatusCard;