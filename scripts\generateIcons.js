/**
 * Generate PWA icons from favicon or create simple placeholders
 * This script creates the required icon files for the PWA manifest
 */

const fs = require('fs');
const path = require('path');

// Simple SVG template for PWA icons
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#3B82F6" rx="16"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size/4}" fill="white"/>
  <path d="M ${size/2-size/8} ${size/2} L ${size/2} ${size/2-size/8} L ${size/2+size/8} ${size/2} L ${size/2} ${size/2+size/8} Z" fill="#3B82F6"/>
  <text x="${size/2}" y="${size/2+size/12}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${size/8}" fill="white" font-weight="bold">VH</text>
</svg>
`;

// Convert SVG to simple base64 encoded data URL (for development only)
const svgToDataURL = (svgString) => {
  return `data:image/svg+xml;base64,${Buffer.from(svgString).toString('base64')}`;
};

// Create icon files
const createIcons = () => {
  const iconDir = path.join(__dirname, '../public/assets/images');
  
  // Ensure directory exists
  if (!fs.existsSync(iconDir)) {
    fs.mkdirSync(iconDir, { recursive: true });
  }
  
  // Create 192x192 icon
  const icon192 = createSVGIcon(192);
  fs.writeFileSync(path.join(iconDir, 'icon-192.svg'), icon192);
  
  // Create 512x512 icon
  const icon512 = createSVGIcon(512);
  fs.writeFileSync(path.join(iconDir, 'icon-512.svg'), icon512);
  
  console.log('✅ PWA icons created successfully!');
  console.log('📁 Location: public/assets/images/');
  console.log('📦 Files: icon-192.svg, icon-512.svg');
  console.log('⚠️  Note: Update manifest.json to use .svg files instead of .png');
};

if (require.main === module) {
  createIcons();
}

module.exports = { createIcons };
