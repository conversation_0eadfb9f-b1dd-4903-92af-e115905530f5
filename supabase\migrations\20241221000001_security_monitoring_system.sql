-- Security Monitoring and Rate Limiting System Migration
-- This migration creates the necessary tables and functions for comprehensive
-- security monitoring, rate limiting, and DDoS protection.

-- 1. Security Logs Table
CREATE TABLE IF NOT EXISTS public.security_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    client_ip INET,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    user_role TEXT,
    operation_type TEXT,
    endpoint TEXT,
    user_agent TEXT,
    details JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 2. Rate Limit Violations Table
CREATE TABLE IF NOT EXISTS public.rate_limit_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_ip INET NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    operation_type TEXT NOT NULL,
    endpoint TEXT NOT NULL,
    violation_count INTEGER NOT NULL DEFAULT 1,
    max_requests INTEGER NOT NULL,
    window_ms INTEGER NOT NULL,
    user_role TEXT,
    user_agent TEXT,
    first_violation_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_violation_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 3. DDoS Detection Table
CREATE TABLE IF NOT EXISTS public.ddos_detections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_ip INET NOT NULL,
    detection_type TEXT NOT NULL CHECK (detection_type IN ('requests_per_second', 'requests_per_minute', 'unique_endpoints', 'failed_ratio')),
    threshold_value NUMERIC NOT NULL,
    actual_value NUMERIC NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    blocked BOOLEAN NOT NULL DEFAULT false,
    block_duration_seconds INTEGER,
    details JSONB DEFAULT '{}',
    detected_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 4. Emergency Bypasses Table (extends the RBAC system)
CREATE TABLE IF NOT EXISTS public.emergency_bypasses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    operation_type TEXT NOT NULL,
    justification TEXT NOT NULL CHECK (length(justification) >= 20),
    activated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    activated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    used_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 5. IP Blocks Table
CREATE TABLE IF NOT EXISTS public.ip_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL,
    ip_range CIDR,
    block_type TEXT NOT NULL CHECK (block_type IN ('manual', 'automatic', 'ddos', 'rate_limit')),
    reason TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    blocked_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    blocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 6. Security Metrics Aggregation Table
CREATE TABLE IF NOT EXISTS public.security_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type TEXT NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    aggregation_period TEXT NOT NULL CHECK (aggregation_period IN ('minute', 'hour', 'day')),
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(metric_type, metric_name, aggregation_period, period_start)
);

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_logs_timestamp ON public.security_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON public.security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_severity ON public.security_logs(severity);
CREATE INDEX IF NOT EXISTS idx_security_logs_client_ip ON public.security_logs(client_ip);
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON public.security_logs(user_id);

CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_client_ip ON public.rate_limit_violations(client_ip);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_user_id ON public.rate_limit_violations(user_id);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_operation ON public.rate_limit_violations(operation_type);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_timestamp ON public.rate_limit_violations(last_violation_at DESC);

CREATE INDEX IF NOT EXISTS idx_ddos_detections_client_ip ON public.ddos_detections(client_ip);
CREATE INDEX IF NOT EXISTS idx_ddos_detections_detected_at ON public.ddos_detections(detected_at DESC);
CREATE INDEX IF NOT EXISTS idx_ddos_detections_severity ON public.ddos_detections(severity);

CREATE INDEX IF NOT EXISTS idx_emergency_bypasses_user_id ON public.emergency_bypasses(user_id);
CREATE INDEX IF NOT EXISTS idx_emergency_bypasses_operation ON public.emergency_bypasses(operation_type);
CREATE INDEX IF NOT EXISTS idx_emergency_bypasses_status ON public.emergency_bypasses(status);
CREATE INDEX IF NOT EXISTS idx_emergency_bypasses_expires ON public.emergency_bypasses(expires_at);

CREATE INDEX IF NOT EXISTS idx_ip_blocks_ip_address ON public.ip_blocks(ip_address);
CREATE INDEX IF NOT EXISTS idx_ip_blocks_active ON public.ip_blocks(is_active);
CREATE INDEX IF NOT EXISTS idx_ip_blocks_expires ON public.ip_blocks(expires_at);

CREATE INDEX IF NOT EXISTS idx_security_metrics_type_name ON public.security_metrics(metric_type, metric_name);
CREATE INDEX IF NOT EXISTS idx_security_metrics_period ON public.security_metrics(aggregation_period, period_start);

-- 8. Create updated_at triggers
CREATE TRIGGER update_emergency_bypasses_updated_at
    BEFORE UPDATE ON public.emergency_bypasses
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_ip_blocks_updated_at
    BEFORE UPDATE ON public.ip_blocks
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- 9. Security monitoring functions

-- Function to check if IP is blocked
CREATE OR REPLACE FUNCTION public.is_ip_blocked(check_ip INET)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS(
        SELECT 1 
        FROM public.ip_blocks 
        WHERE (ip_address = check_ip OR check_ip << ip_range)
        AND is_active = true 
        AND (expires_at IS NULL OR expires_at > NOW())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check active emergency bypass
CREATE OR REPLACE FUNCTION public.has_active_bypass(user_uuid UUID, op_type TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS(
        SELECT 1 
        FROM public.emergency_bypasses 
        WHERE user_id = user_uuid 
        AND operation_type = op_type 
        AND status = 'active' 
        AND expires_at > NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log security event
CREATE OR REPLACE FUNCTION public.log_security_event(
    p_event_type TEXT,
    p_severity TEXT,
    p_client_ip INET DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_details JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.security_logs (
        event_type,
        severity,
        client_ip,
        user_id,
        details
    ) VALUES (
        p_event_type,
        p_severity,
        p_client_ip,
        p_user_id,
        p_details
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to record rate limit violation
CREATE OR REPLACE FUNCTION public.record_rate_limit_violation(
    p_client_ip INET,
    p_user_id UUID,
    p_operation_type TEXT,
    p_endpoint TEXT,
    p_max_requests INTEGER,
    p_window_ms INTEGER,
    p_user_role TEXT DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    violation_id UUID;
    existing_violation public.rate_limit_violations%ROWTYPE;
BEGIN
    -- Check for existing violation in the last hour
    SELECT * INTO existing_violation
    FROM public.rate_limit_violations
    WHERE client_ip = p_client_ip
    AND operation_type = p_operation_type
    AND endpoint = p_endpoint
    AND last_violation_at > NOW() - INTERVAL '1 hour'
    ORDER BY last_violation_at DESC
    LIMIT 1;
    
    IF existing_violation.id IS NOT NULL THEN
        -- Update existing violation
        UPDATE public.rate_limit_violations
        SET violation_count = violation_count + 1,
            last_violation_at = NOW()
        WHERE id = existing_violation.id
        RETURNING id INTO violation_id;
    ELSE
        -- Create new violation record
        INSERT INTO public.rate_limit_violations (
            client_ip,
            user_id,
            operation_type,
            endpoint,
            max_requests,
            window_ms,
            user_role,
            user_agent
        ) VALUES (
            p_client_ip,
            p_user_id,
            p_operation_type,
            p_endpoint,
            p_max_requests,
            p_window_ms,
            p_user_role,
            p_user_agent
        ) RETURNING id INTO violation_id;
    END IF;
    
    RETURN violation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to aggregate security metrics
CREATE OR REPLACE FUNCTION public.aggregate_security_metrics()
RETURNS INTEGER AS $$
DECLARE
    processed_count INTEGER := 0;
    current_hour TIMESTAMPTZ;
    hour_start TIMESTAMPTZ;
    hour_end TIMESTAMPTZ;
BEGIN
    current_hour := date_trunc('hour', NOW());
    hour_start := current_hour - INTERVAL '1 hour';
    hour_end := current_hour;
    
    -- Aggregate rate limit violations per hour
    INSERT INTO public.security_metrics (
        metric_type,
        metric_name,
        metric_value,
        aggregation_period,
        period_start,
        period_end,
        details
    )
    SELECT 
        'rate_limiting',
        'violations_per_hour',
        COUNT(*),
        'hour',
        hour_start,
        hour_end,
        jsonb_build_object(
            'by_operation', jsonb_object_agg(operation_type, operation_count)
        )
    FROM (
        SELECT 
            operation_type,
            COUNT(*) as operation_count
        FROM public.rate_limit_violations
        WHERE last_violation_at >= hour_start AND last_violation_at < hour_end
        GROUP BY operation_type
    ) subq
    ON CONFLICT (metric_type, metric_name, aggregation_period, period_start) 
    DO UPDATE SET 
        metric_value = EXCLUDED.metric_value,
        details = EXCLUDED.details;
    
    GET DIAGNOSTICS processed_count = ROW_COUNT;
    
    -- Aggregate DDoS detections per hour
    INSERT INTO public.security_metrics (
        metric_type,
        metric_name,
        metric_value,
        aggregation_period,
        period_start,
        period_end,
        details
    )
    SELECT 
        'ddos_protection',
        'detections_per_hour',
        COUNT(*),
        'hour',
        hour_start,
        hour_end,
        jsonb_build_object(
            'by_severity', jsonb_object_agg(severity, severity_count),
            'unique_ips', COUNT(DISTINCT client_ip)
        )
    FROM (
        SELECT 
            severity,
            client_ip,
            COUNT(*) as severity_count
        FROM public.ddos_detections
        WHERE detected_at >= hour_start AND detected_at < hour_end
        GROUP BY severity, client_ip
    ) subq
    ON CONFLICT (metric_type, metric_name, aggregation_period, period_start) 
    DO UPDATE SET 
        metric_value = EXCLUDED.metric_value,
        details = EXCLUDED.details;
    
    RETURN processed_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old security data
CREATE OR REPLACE FUNCTION public.cleanup_security_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    retention_days INTEGER := 90; -- Keep security logs for 90 days
BEGIN
    -- Clean up old security logs (keep critical events longer)
    DELETE FROM public.security_logs 
    WHERE timestamp < NOW() - INTERVAL '90 days'
    AND severity NOT IN ('high', 'critical');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Clean up old rate limit violations
    DELETE FROM public.rate_limit_violations 
    WHERE last_violation_at < NOW() - INTERVAL '30 days';
    
    -- Clean up resolved DDoS detections
    DELETE FROM public.ddos_detections 
    WHERE resolved_at IS NOT NULL 
    AND resolved_at < NOW() - INTERVAL '30 days';
    
    -- Clean up expired emergency bypasses
    DELETE FROM public.emergency_bypasses 
    WHERE status = 'expired' 
    AND expires_at < NOW() - INTERVAL '7 days';
    
    -- Clean up expired IP blocks
    DELETE FROM public.ip_blocks 
    WHERE is_active = false 
    AND expires_at IS NOT NULL 
    AND expires_at < NOW() - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Row Level Security Policies

-- Security Logs (Admin only)
ALTER TABLE public.security_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can view security logs" ON public.security_logs
    FOR SELECT USING (public.has_role(auth.uid(), 'admin'));

CREATE POLICY "System can insert security logs" ON public.security_logs
    FOR INSERT WITH CHECK (true);

-- Rate Limit Violations (Admin only)
ALTER TABLE public.rate_limit_violations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can view rate limit violations" ON public.rate_limit_violations
    FOR SELECT USING (public.has_role(auth.uid(), 'admin'));

CREATE POLICY "System can insert rate limit violations" ON public.rate_limit_violations
    FOR INSERT WITH CHECK (true);

-- DDoS Detections (Admin only)
ALTER TABLE public.ddos_detections ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can view ddos detections" ON public.ddos_detections
    FOR SELECT USING (public.has_role(auth.uid(), 'admin'));

CREATE POLICY "System can insert ddos detections" ON public.ddos_detections
    FOR INSERT WITH CHECK (true);

-- Emergency Bypasses
ALTER TABLE public.emergency_bypasses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own bypasses" ON public.emergency_bypasses
    FOR SELECT USING (
        auth.uid() = user_id 
        OR public.has_role(auth.uid(), 'admin')
    );

CREATE POLICY "Authorized users can create bypasses" ON public.emergency_bypasses
    FOR INSERT WITH CHECK (
        auth.uid() = user_id 
        AND public.has_emergency_override(auth.uid())
    );

-- IP Blocks (Admin only)
ALTER TABLE public.ip_blocks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can manage IP blocks" ON public.ip_blocks
    FOR ALL USING (public.has_role(auth.uid(), 'admin'));

-- Security Metrics (Admin only)
ALTER TABLE public.security_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can view security metrics" ON public.security_metrics
    FOR SELECT USING (public.has_role(auth.uid(), 'admin'));

-- 11. Create scheduled jobs (if pg_cron is available)
-- These would typically be set up separately in production

-- Aggregate metrics every hour
-- SELECT cron.schedule('aggregate-security-metrics', '0 * * * *', 'SELECT public.aggregate_security_metrics();');

-- Cleanup old data daily
-- SELECT cron.schedule('cleanup-security-data', '0 2 * * *', 'SELECT public.cleanup_security_data();');

COMMENT ON TABLE public.security_logs IS 'Comprehensive security event logging for audit and monitoring';
COMMENT ON TABLE public.rate_limit_violations IS 'Rate limiting violations for analysis and blocking';
COMMENT ON TABLE public.ddos_detections IS 'DDoS attack detection and mitigation tracking';
COMMENT ON TABLE public.emergency_bypasses IS 'Emergency rate limit bypasses for critical medical operations';
COMMENT ON TABLE public.ip_blocks IS 'IP address blocking for security threats';
COMMENT ON TABLE public.security_metrics IS 'Aggregated security metrics for monitoring and reporting';
