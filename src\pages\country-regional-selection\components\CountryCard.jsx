import React from 'react';
import Icon from '../../../components/AppIcon';

const CountryCard = ({ country, isSelected, onSelect, selectedLanguage }) => {
  const getLocalizedName = () => {
    return country.name[selectedLanguage] || country.name.en;
  };

  const getLocalizedLocalName = () => {
    return country.localName[selectedLanguage] || country.localName.en;
  };

  const getLocalizedDescription = () => {
    return country.description[selectedLanguage] || country.description.en;
  };

  return (
    <button
      onClick={onSelect}
      className={`w-full p-6 rounded-xl border-2 transition-all duration-200 text-left group hover:shadow-elevated ${
        isSelected
          ? 'border-primary-500 bg-primary-50 shadow-elevated'
          : 'border-border bg-surface hover:border-primary-300 hover:bg-primary-25'
      }`}
    >
      {/* Country Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-3xl">{country.flag}</span>
          <div>
            <h3 className={`font-semibold text-lg ${
              isSelected ? 'text-primary-700' : 'text-text-primary'
            }`}>
              {getLocalizedName()}
            </h3>
            <p className="text-sm text-text-secondary">
              {getLocalizedLocalName()}
            </p>
          </div>
        </div>
        
        {/* Selection Indicator */}
        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
          isSelected
            ? 'border-primary-500 bg-primary-500' :'border-secondary-300 group-hover:border-primary-400'
        }`}>
          {isSelected && (
            <Icon name="Check" size={14} color="white" />
          )}
        </div>
      </div>

      {/* Description */}
      <p className="text-text-secondary text-sm leading-relaxed mb-4">
        {getLocalizedDescription()}
      </p>

      {/* Specialties */}
      <div className="flex flex-wrap gap-2">
        {country.specialties?.slice(0, 3).map((specialty, index) => (
          <span
            key={index}
            className={`px-3 py-1 rounded-full text-xs font-medium transition-colors duration-200 ${
              isSelected
                ? 'bg-primary-100 text-primary-700' :'bg-secondary-100 text-secondary-700 group-hover:bg-primary-100 group-hover:text-primary-700'
            }`}
          >
            {specialty}
          </span>
        ))}
      </div>

      {/* Healthcare Icon */}
      <div className="flex justify-end mt-4">
        <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors duration-200 ${
          isSelected
            ? 'bg-primary-100' :'bg-secondary-100 group-hover:bg-primary-100'
        }`}>
          <Icon 
            name="Stethoscope" 
            size={16} 
            color={isSelected ? 'var(--color-primary)' : 'var(--color-text-secondary)'} 
          />
        </div>
      </div>
    </button>
  );
};

export default CountryCard;