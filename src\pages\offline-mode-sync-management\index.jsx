import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import SyncStatusDashboard from './components/SyncStatusDashboard';
import OfflineCapabilitiesSection from './components/OfflineCapabilitiesSection';
import SyncControlsPanel from './components/SyncControlsPanel';
import OfflineSessionQueue from './components/OfflineSessionQueue';

const OfflineModeSync = () => {
  const navigate = useNavigate();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncProgress, setSyncProgress] = useState(0);
  const [emergencyMode, setEmergencyMode] = useState(false);

  // Mock data - in real app, this would come from state management
  const [cachedData, setCachedData] = useState({
    sessions: 12,
    agents: 5,
    pending: 3
  });

  const [offlineContent, setOfflineContent] = useState({
    agentProfiles: true,
    medicalKnowledge: false,
    emergencyProtocols: true,
    consultationTemplates: false
  });

  const [syncSettings, setSyncSettings] = useState({
    autoSync: true,
    syncFrequency: 'realtime',
    syncOnWifi: true,
    backgroundSync: false,
    conflictResolution: 'manual'
  });

  const [queuedSessions, setQueuedSessions] = useState([
    {
      id: '1',
      title: 'Cardiology Consultation',
      createdAt: '2024-01-15T10:30:00Z',
      priority: 'high',
      size: 2.5,
      duration: 25,
      error: null
    },
    {
      id: '2',
      title: 'General Health Check',
      createdAt: '2024-01-15T09:15:00Z',
      priority: 'medium',
      size: 1.8,
      duration: 18,
      error: 'Network timeout'
    },
    {
      id: '3',
      title: 'Follow-up Session',
      createdAt: '2024-01-14T16:45:00Z',
      priority: 'low',
      size: 1.2,
      duration: 12,
      error: null
    }
  ]);

  const [conflicts, setConflicts] = useState([
    {
      id: '1',
      type: 'Patient Profile',
      description: 'Local profile data differs from server version',
      timestamp: '2024-01-15T10:30:00Z'
    }
  ]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Auto-sync when coming back online
  useEffect(() => {
    if (isOnline && syncSettings.autoSync && queuedSessions.length > 0) {
      handleManualSync();
    }
  }, [isOnline]);

  const handleManualSync = () => {
    if (!isOnline) return;
    
    setSyncProgress(0);
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleDownloadContent = (contentId) => {
    setOfflineContent(prev => ({
      ...prev,
      [contentId]: true
    }));
  };

  const handleRemoveContent = (contentId) => {
    setOfflineContent(prev => ({
      ...prev,
      [contentId]: false
    }));
  };

  const handleUpdateSyncSettings = (newSettings) => {
    setSyncSettings(newSettings);
  };

  const handleResolveConflict = (conflictId, resolution) => {
    setConflicts(prev => prev.filter(c => c.id !== conflictId));
  };

  const handleChangePriority = (sessionId, newPriority) => {
    setQueuedSessions(prev => 
      prev.map(session => 
        session.id === sessionId ? { ...session, priority: newPriority } : session
      )
    );
  };

  const handleRemoveSession = (sessionId) => {
    setQueuedSessions(prev => prev.filter(session => session.id !== sessionId));
  };

  const handleRetryUpload = (sessionId) => {
    setQueuedSessions(prev => 
      prev.map(session => 
        session.id === sessionId ? { ...session, error: null } : session
      )
    );
  };

  const handleEmergencyMode = () => {
    setEmergencyMode(!emergencyMode);
  };

  const handleClearCache = () => {
    setCachedData({ sessions: 0, agents: 0, pending: 0 });
    setQueuedSessions([]);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-surface shadow-minimal border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-text-secondary hover:text-text-primary hover:bg-secondary-50 rounded-lg transition-fast"
              >
                <Icon name="ArrowLeft" size={20} />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-text-primary font-heading">
                  Offline Mode & Sync
                </h1>
                <p className="text-sm text-text-secondary">
                  Manage offline functionality and data synchronization
                </p>
              </div>
            </div>

            {/* Status Indicator */}
            <div className="flex items-center space-x-4">
              {emergencyMode && (
                <div className="flex items-center space-x-2 px-3 py-2 bg-error-50 text-error-600 rounded-lg">
                  <Icon name="AlertTriangle" size={16} />
                  <span className="text-sm font-medium">Emergency Mode</span>
                </div>
              )}
              
              <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
                isOnline ? 'bg-success-50 text-success-600' : 'bg-error-50 text-error-600'
              }`}>
                <Icon name={isOnline ? 'Wifi' : 'WifiOff'} size={16} />
                <span className="text-sm font-medium">
                  {isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column */}
          <div className="space-y-8">
            {/* Sync Status Dashboard */}
            <SyncStatusDashboard
              isOnline={isOnline}
              cachedData={cachedData}
              syncProgress={syncProgress}
              onManualSync={handleManualSync}
            />

            {/* Offline Capabilities */}
            <OfflineCapabilitiesSection
              offlineContent={offlineContent}
              storageUsed={65}
              storageLimit={100}
              onDownloadContent={handleDownloadContent}
              onRemoveContent={handleRemoveContent}
            />

            {/* Emergency Mode */}
            <div className="bg-surface rounded-xl p-6 shadow-elevated">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-error-50 rounded-lg flex items-center justify-center">
                    <Icon name="AlertTriangle" size={20} color="var(--color-error-500)" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary font-heading">
                      Emergency Mode
                    </h3>
                    <p className="text-sm text-text-secondary">
                      Basic consultation functionality using cached protocols
                    </p>
                  </div>
                </div>
                
                <button
                  onClick={handleEmergencyMode}
                  className={`px-4 py-2 rounded-lg font-medium transition-fast ${
                    emergencyMode
                      ? 'bg-error-500 text-white hover:bg-error-600' :'bg-error-50 text-error-600 hover:bg-error-100'
                  }`}
                >
                  {emergencyMode ? 'Disable' : 'Activate'}
                </button>
              </div>

              {emergencyMode && (
                <div className="p-4 bg-error-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Icon name="Info" size={16} color="var(--color-error-600)" />
                    <span className="text-sm font-medium text-error-800">Emergency Mode Active</span>
                  </div>
                  <p className="text-sm text-error-700">
                    Using cached medical protocols. Functionality is limited but essential features remain available.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Sync Controls */}
            <SyncControlsPanel
              syncSettings={syncSettings}
              onUpdateSettings={handleUpdateSyncSettings}
              onResolveConflict={handleResolveConflict}
              conflicts={conflicts}
            />

            {/* Session Queue */}
            <OfflineSessionQueue
              queuedSessions={queuedSessions}
              onChangePriority={handleChangePriority}
              onRemoveSession={handleRemoveSession}
              onRetryUpload={handleRetryUpload}
            />

            {/* Data Management */}
            <div className="bg-surface rounded-xl p-6 shadow-elevated">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-secondary-50 rounded-lg flex items-center justify-center">
                    <Icon name="Database" size={20} color="var(--color-text-secondary)" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary font-heading">
                      Data Management
                    </h3>
                    <p className="text-sm text-text-secondary">
                      Manage cached data and storage
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handleClearCache}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 
                           bg-error-50 text-error-600 rounded-lg hover:bg-error-100 transition-fast"
                >
                  <Icon name="Trash2" size={16} />
                  <span className="font-medium">Clear All Cache</span>
                </button>

                <div className="grid grid-cols-2 gap-4">
                  <button className="flex items-center justify-center space-x-2 px-4 py-3 
                                   bg-secondary-50 text-text-secondary rounded-lg hover:bg-secondary-100 transition-fast">
                    <Icon name="Download" size={16} />
                    <span className="text-sm font-medium">Export Data</span>
                  </button>
                  
                  <button className="flex items-center justify-center space-x-2 px-4 py-3 
                                   bg-secondary-50 text-text-secondary rounded-lg hover:bg-secondary-100 transition-fast">
                    <Icon name="Settings" size={16} />
                    <span className="text-sm font-medium">Optimize Storage</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfflineModeSync;