import React, { useState, useEffect } from 'react';
import { Calculator, Clock, Users, Star, Zap, Info } from 'lucide-react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const PricingCalculator = ({ 
  currency, 
  onCalculatorUpdate,
  consultationType,
  setConsultationType,
  duration,
  setDuration,
  specialistCount,
  setSpecialistCount,
  premiumFeatures,
  setPremiumFeatures,
  familyMembers,
  setFamilyMembers
}) => {
  const [calculationResult, setCalculationResult] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const consultationTypes = [
    { id: 'general', name: 'General Consultation', basePrice: { NGN: 2500, USD: 5 }, icon: '🩺' },
    { id: 'specialist', name: 'Specialist Consultation', basePrice: { NGN: 5000, USD: 10 }, icon: '👨‍⚕️' },
    { id: 'emergency', name: 'Emergency Consultation', basePrice: { NGN: 7500, USD: 15 }, icon: '🚨' },
    { id: 'mental', name: 'Mental Health', basePrice: { NGN: 4000, USD: 8 }, icon: '🧠' },
    { id: 'pediatric', name: 'Pediatric Consultation', basePrice: { NGN: 3500, USD: 7 }, icon: '👶' }
  ];

  const premiumFeatureOptions = [
    { id: 'transcript', name: 'Transcript Generation', price: { NGN: 500, USD: 1 } },
    { id: 'recording', name: 'Session Recording', price: { NGN: 750, USD: 1.5 } },
    { id: 'summary', name: 'AI Summary Report', price: { NGN: 1000, USD: 2 } },
    { id: 'prescription', name: 'Digital Prescription', price: { NGN: 1500, USD: 3 } },
    { id: 'followup', name: 'Follow-up Reminder', price: { NGN: 250, USD: 0.5 } }
  ];

  const specialistMultipliers = {
    1: { multiplier: 1, name: 'Single Agent' },
    2: { multiplier: 1.5, name: 'Dual Agent Collaboration' },
    3: { multiplier: 2, name: 'Multi-Agent Team' },
    4: { multiplier: 2.5, name: 'Full Specialist Panel' }
  };

  const familyDiscounts = {
    1: { discount: 0, name: 'Individual' },
    2: { discount: 0.1, name: 'Family (2 members)' },
    3: { discount: 0.15, name: 'Family (3 members)' },
    4: { discount: 0.2, name: 'Family (4+ members)' }
  };

  useEffect(() => {
    calculateCost();
  }, [consultationType, duration, specialistCount, premiumFeatures, familyMembers, currency]);

  const calculateCost = async () => {
    setIsCalculating(true);
    
    // Simulate calculation delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));

    const selectedType = consultationTypes.find(type => type.id === consultationType);
    const basePrice = selectedType.basePrice[currency];
    
    // Duration multiplier (base is 15 minutes)
    const durationMultiplier = duration / 15;
    
    // Specialist multiplier
    const specialistMultiplier = specialistMultipliers[specialistCount]?.multiplier || 1;
    
    // Premium features cost
    const premiumCost = premiumFeatures.reduce((total, featureId) => {
      const feature = premiumFeatureOptions.find(f => f.id === featureId);
      return total + (feature?.price[currency] || 0);
    }, 0);
    
    // Base consultation cost
    let consultationCost = basePrice * durationMultiplier * specialistMultiplier;
    
    // Total before discount
    const totalBeforeDiscount = consultationCost + premiumCost;
    
    // Family discount
    const familyDiscount = familyDiscounts[Math.min(familyMembers, 4)]?.discount || 0;
    const discountAmount = totalBeforeDiscount * familyDiscount;
    
    // Final total
    const finalTotal = totalBeforeDiscount - discountAmount;

    const result = {
      basePrice,
      consultationType: selectedType.name,
      duration,
      specialistCount,
      consultationCost,
      premiumCost,
      totalBeforeDiscount,
      familyDiscount,
      discountAmount,
      finalTotal,
      currency,
      breakdown: {
        basePriceFormatted: formatPrice(basePrice, currency),
        consultationCostFormatted: formatPrice(consultationCost, currency),
        premiumCostFormatted: formatPrice(premiumCost, currency),
        discountAmountFormatted: formatPrice(discountAmount, currency),
        finalTotalFormatted: formatPrice(finalTotal, currency)
      }
    };

    setCalculationResult(result);
    setIsCalculating(false);
    
    if (onCalculatorUpdate) {
      onCalculatorUpdate(result);
    }
  };

  const formatPrice = (amount, currency) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const handlePremiumFeatureToggle = (featureId) => {
    setPremiumFeatures(prev => 
      prev.includes(featureId)
        ? prev.filter(id => id !== featureId)
        : [...prev, featureId]
    );
  };

  return (
    <div className="space-y-6">
      {/* Calculator Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-4">
          <Calculator className="text-blue-600 mr-3" size={24} />
          <h2 className="text-xl font-semibold text-gray-900">
            Consultation Cost Calculator
          </h2>
        </div>
        <p className="text-gray-600">
          Get real-time cost estimates for your VoiceHealth AI consultations with transparent pricing breakdown.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Calculator Inputs */}
        <div className="space-y-6">
          {/* Consultation Type */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Consultation Type
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {consultationTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setConsultationType(type.id)}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    consultationType === type.id
                      ? 'border-blue-500 bg-blue-50' :'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center mb-2">
                    <span className="text-2xl mr-2">{type.icon}</span>
                    <span className="font-medium text-gray-900">{type.name}</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Base: {formatPrice(type.basePrice[currency], currency)}
                  </p>
                </button>
              ))}
            </div>
          </div>

          {/* Duration & Specialists */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Session Configuration
            </h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Clock size={16} className="inline mr-1" />
                  Duration (minutes)
                </label>
                <Input
                  type="number"
                  min="5"
                  max="120"
                  step="5"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Users size={16} className="inline mr-1" />
                  AI Specialists
                </label>
                <select
                  value={specialistCount}
                  onChange={(e) => setSpecialistCount(parseInt(e.target.value))}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.entries(specialistMultipliers).map(([count, data]) => (
                    <option key={count} value={count}>
                      {data.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Premium Features */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              <Star className="inline mr-2" size={20} />
              Premium Features
            </h3>
            
            <div className="space-y-3">
              {premiumFeatureOptions.map((feature) => (
                <label key={feature.id} className="flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center">
                    <Input
                      type="checkbox"
                      checked={premiumFeatures.includes(feature.id)}
                      onChange={() => handlePremiumFeatureToggle(feature.id)}
                      className="mr-3"
                    />
                    <span className="font-medium text-gray-900">{feature.name}</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    +{formatPrice(feature.price[currency], currency)}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Family Account */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Family Account Discount
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of Family Members
              </label>
              <select
                value={familyMembers}
                onChange={(e) => setFamilyMembers(parseInt(e.target.value))}
                className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {Object.entries(familyDiscounts).map(([count, data]) => (
                  <option key={count} value={count}>
                    {data.name} {data.discount > 0 && `(${(data.discount * 100)}% off)`}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Calculation Result */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Cost Breakdown
            </h3>
            
            {isCalculating ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Calculating...</span>
              </div>
            ) : calculationResult ? (
              <div className="space-y-4">
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Base Consultation</span>
                  <span className="font-medium">
                    {calculationResult.breakdown.consultationCostFormatted}
                  </span>
                </div>
                
                {calculationResult.premiumCost > 0 && (
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Premium Features</span>
                    <span className="font-medium">
                      {calculationResult.breakdown.premiumCostFormatted}
                    </span>
                  </div>
                )}
                
                {calculationResult.discountAmount > 0 && (
                  <div className="flex justify-between py-2 border-b border-gray-100 text-green-600">
                    <span>Family Discount</span>
                    <span className="font-medium">
                      -{calculationResult.breakdown.discountAmountFormatted}
                    </span>
                  </div>
                )}
                
                <div className="flex justify-between py-3 bg-blue-50 rounded-lg px-4 border-2 border-blue-200">
                  <span className="text-lg font-semibold text-blue-900">Total Cost</span>
                  <span className="text-2xl font-bold text-blue-900">
                    {calculationResult.breakdown.finalTotalFormatted}
                  </span>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4 mt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Session Details</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• {calculationResult.consultationType}</li>
                    <li>• {calculationResult.duration} minutes duration</li>
                    <li>• {specialistMultipliers[calculationResult.specialistCount]?.name}</li>
                    {calculationResult.premiumCost > 0 && (
                      <li>• {premiumFeatures.length} premium feature(s)</li>
                    )}
                    {calculationResult.discountAmount > 0 && (
                      <li>• Family discount applied</li>
                    )}
                  </ul>
                </div>

                <Button
                  className="w-full"
                  onClick={() => {
                    // Navigate to payment or booking
                    console.log('Proceed with consultation:', calculationResult);
                  }}
                >
                  <Zap size={16} className="mr-2" />
                  Book Consultation
                </Button>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Configure your consultation to see pricing
              </div>
            )}
          </div>

          {/* Cost Optimization Tips */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start">
              <Info size={16} className="text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-amber-800 mb-1">
                  Cost Optimization Tips
                </h4>
                <ul className="text-sm text-amber-700 space-y-1">
                  <li>• Family accounts get up to 20% discount</li>
                  <li>• Shorter sessions cost less per minute</li>
                  <li>• General consultations are most affordable</li>
                  <li>• Premium features are optional add-ons</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingCalculator;