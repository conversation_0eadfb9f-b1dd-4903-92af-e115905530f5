/**
 * ROLE-BASED ROUTE PROTECTION
 * 
 * This component provides route-level protection based on user roles
 * and permissions, ensuring users can only access authorized pages.
 * 
 * FEATURES:
 * - Route-level permission checking
 * - Automatic redirects for unauthorized access
 * - Loading states during authentication
 * - Emergency access integration for critical routes
 * - Audit logging for route access attempts
 * 
 * USAGE EXAMPLES:
 * <RoleBasedRoute
 *   requiredRoles={['healthcare_provider', 'admin']}
 *   requiredPermissions={[{ resource: 'patient_profiles', action: 'read' }]}
 *   redirectTo="/unauthorized"
 *   emergencyAccess={true}
 * >
 *   <AdminDashboard />
 * </RoleBasedRoute>
 */

import React, { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import type { UserRole, Permission, ResourceType, Action } from '../../types';
import { useAuth } from '../../contexts/SimpleAuthContext';
import { useRBAC } from '../../hooks/useRBAC';

interface RoleBasedRouteProps {
  readonly children: ReactNode;
  readonly requiredRoles?: UserRole[];
  readonly requiredPermissions?: Array<{
    readonly resource: ResourceType;
    readonly action: Action;
    readonly context?: Record<string, unknown>;
  }>;
  readonly redirectTo?: string;
  readonly emergencyAccess?: boolean;
  readonly loadingComponent?: ReactNode;
  readonly unauthorizedComponent?: ReactNode;
  readonly onAccessDenied?: (reason: string) => void;
}

interface AccessCheckResult {
  readonly hasAccess: boolean;
  readonly reason: string;
  readonly canUseEmergencyAccess: boolean;
}

export const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  redirectTo = '/unauthorized',
  emergencyAccess = false,
  loadingComponent,
  unauthorizedComponent,
  onAccessDenied
}) => {
  const { user, loading: authLoading } = useAuth();
  const { hasPermission, canEmergencyOverride, userRole } = useRBAC();
  const location = useLocation();
  const [accessResult, setAccessResult] = useState<AccessCheckResult | null>(null);

  // Check access when user or route changes
  useEffect(() => {
    if (authLoading) {
      setAccessResult(null);
      return;
    }

    if (!user) {
      setAccessResult({
        hasAccess: false,
        reason: 'User not authenticated',
        canUseEmergencyAccess: false
      });
      return;
    }

    const checkAccess = (): AccessCheckResult => {
      // Check required roles
      if (requiredRoles.length > 0) {
        const userRoleValue = user.user_metadata?.role as UserRole || 'patient';
        if (!requiredRoles.includes(userRoleValue)) {
          return {
            hasAccess: false,
            reason: `Role '${userRoleValue}' not in required roles: ${requiredRoles.join(', ')}`,
            canUseEmergencyAccess: emergencyAccess && canEmergencyOverride
          };
        }
      }

      // Check required permissions
      if (requiredPermissions.length > 0) {
        for (const permission of requiredPermissions) {
          if (!hasPermission(permission.resource, permission.action, permission.context)) {
            return {
              hasAccess: false,
              reason: `Missing permission: ${permission.action} on ${permission.resource}`,
              canUseEmergencyAccess: emergencyAccess && canEmergencyOverride
            };
          }
        }
      }

      return {
        hasAccess: true,
        reason: 'Access granted',
        canUseEmergencyAccess: false
      };
    };

    const result = checkAccess();
    setAccessResult(result);

    // Call access denied callback if provided
    if (!result.hasAccess && onAccessDenied) {
      onAccessDenied(result.reason);
    }
  }, [
    user,
    authLoading,
    requiredRoles,
    requiredPermissions,
    hasPermission,
    canEmergencyOverride,
    emergencyAccess,
    onAccessDenied,
    location.pathname
  ]);

  // Show loading state
  if (authLoading || !accessResult) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking access permissions...</p>
        </div>
      </div>
    );
  }

  // User not authenticated - redirect to login
  if (!user) {
    return <Navigate to="/authentication-demo-access" state={{ from: location }} replace />;
  }

  // User has access - render children
  if (accessResult.hasAccess) {
    return <>{children}</>;
  }

  // User doesn't have access but can use emergency access
  if (accessResult.canUseEmergencyAccess) {
    if (unauthorizedComponent) {
      return <>{unauthorizedComponent}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Access Restricted</h3>
            <p className="mt-2 text-sm text-gray-500">
              {accessResult.reason}
            </p>
            <p className="mt-2 text-sm text-gray-500">
              You can request emergency access if this is for a medical emergency.
            </p>
            <div className="mt-6 flex gap-3">
              <button
                onClick={() => window.history.back()}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
              >
                Go Back
              </button>
              <button
                onClick={() => {
                  // This would open emergency access modal
                  // For now, just redirect to a dedicated emergency access page
                  window.location.href = `/emergency-access?return=${encodeURIComponent(location.pathname)}`;
                }}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
              >
                Emergency Access
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // User doesn't have access and no emergency access available
  if (unauthorizedComponent) {
    return <>{unauthorizedComponent}</>;
  }

  // Default unauthorized page
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">Access Denied</h3>
          <p className="mt-2 text-sm text-gray-500">
            {accessResult.reason}
          </p>
          <p className="mt-2 text-sm text-gray-500">
            Your current role: <span className="font-medium">{userRole}</span>
          </p>
          {requiredRoles.length > 0 && (
            <p className="mt-2 text-sm text-gray-500">
              Required roles: <span className="font-medium">{requiredRoles.join(', ')}</span>
            </p>
          )}
          <div className="mt-6">
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleBasedRoute;
