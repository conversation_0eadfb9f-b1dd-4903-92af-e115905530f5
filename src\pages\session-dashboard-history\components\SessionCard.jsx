import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SessionCard = ({ 
  session, 
  onViewDetails, 
  onDownloadTranscript, 
  onReplayAudio, 
  onResumeSession,
  className = '' 
}) => {
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [downloadingTranscript, setDownloadingTranscript] = useState(false);

  const handleReplayAudio = () => {
    setIsAudioPlaying(!isAudioPlaying);
    onReplayAudio(session.id, !isAudioPlaying);
  };

  const handleDownloadTranscript = async () => {
    setDownloadingTranscript(true);
    try {
      await onDownloadTranscript(session.id);
    } finally {
      setDownloadingTranscript(false);
    }
  };

  const getStatusColor = () => {
    switch (session.status) {
      case 'active':
        return 'bg-success-50 text-success-600 border-success-200';
      case 'completed':
        return 'bg-secondary-50 text-secondary-600 border-secondary-200';
      case 'paused':
        return 'bg-warning-50 text-warning-600 border-warning-200';
      case 'cancelled':
        return 'bg-error-50 text-error-600 border-error-200';
      default:
        return 'bg-secondary-50 text-secondary-600 border-secondary-200';
    }
  };

  const getStatusIcon = () => {
    switch (session.status) {
      case 'active':
        return 'Play';
      case 'completed':
        return 'CheckCircle';
      case 'paused':
        return 'Pause';
      case 'cancelled':
        return 'XCircle';
      default:
        return 'Clock';
    }
  };

  return (
    <div className={`bg-surface border border-border rounded-lg p-6 hover:shadow-elevated transition-smooth ${className}`}>
      {/* Session Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className="font-semibold text-text-primary font-heading text-lg">
              {session.title}
            </h3>
            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor()}`}>
              <Icon name={getStatusIcon()} size={12} />
              {session.status}
            </span>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-text-secondary mb-3">
            <span className="flex items-center space-x-1">
              <Icon name="Calendar" size={14} />
              <span>{session.date}</span>
            </span>
            <span className="flex items-center space-x-1">
              <Icon name="Clock" size={14} />
              <span>{session.duration}</span>
            </span>
            <span className="flex items-center space-x-1">
              <Icon name="Users" size={14} />
              <span>{session.agents.length} agents</span>
            </span>
          </div>
        </div>
        
        {session.status === 'active' && (
          <div className="flex items-center space-x-2">
            <div className="voice-input-indicator animate-consultation-pulse"></div>
            <span className="text-sm text-success-600 font-medium">Live</span>
          </div>
        )}
      </div>

      {/* Participating Agents */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-text-primary mb-2">Participating Agents</h4>
        <div className="flex flex-wrap gap-2">
          {session.agents.map((agent, index) => (
            <div key={index} className="flex items-center space-x-2 px-3 py-1 bg-primary-50 rounded-full border border-primary-200">
              <div className="w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center">
                <Icon name="User" size={10} color="white" />
              </div>
              <span className="text-sm font-medium text-primary-600">{agent.name}</span>
              <span className="text-xs text-primary-500">({agent.specialty})</span>
            </div>
          ))}
        </div>
      </div>

      {/* Key Topics */}
      {session.keyTopics && session.keyTopics.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-text-primary mb-2">Key Topics Discussed</h4>
          <div className="flex flex-wrap gap-2">
            {session.keyTopics.slice(0, 3).map((topic, index) => (
              <span key={index} className="px-2 py-1 bg-accent-50 text-accent-600 rounded-md text-xs font-medium border border-accent-200">
                {topic}
              </span>
            ))}
            {session.keyTopics.length > 3 && (
              <span className="px-2 py-1 bg-secondary-50 text-secondary-600 rounded-md text-xs font-medium border border-secondary-200">
                +{session.keyTopics.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Session Summary Preview */}
      {session.summary && (
        <div className="mb-4 p-3 bg-secondary-50 rounded-lg">
          <p className="text-sm text-text-secondary line-clamp-2">
            {session.summary}
          </p>
        </div>
      )}

      {/* Audio Waveform (when playing) */}
      {isAudioPlaying && (
        <div className="mb-4 p-3 bg-primary-50 rounded-lg">
          <div className="flex items-center justify-center space-x-1 h-8">
            {[...Array(15)].map((_, i) => (
              <div
                key={i}
                className="voice-waveform-bar bg-primary-500"
                style={{
                  height: `${Math.random() * 20 + 8}px`,
                  animationDelay: `${i * 0.1}s`
                }}
              ></div>
            ))}
          </div>
          <div className="flex justify-between items-center mt-2 text-xs text-primary-600">
            <span>Playing: {session.currentAgent || 'Dr. Sarah'}</span>
            <span>{session.playbackTime || '2:34'} / {session.duration}</span>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant="primary"
          size="sm"
          onClick={() => onViewDetails(session.id)}
          iconName="Eye"
          iconPosition="left"
        >
          View Details
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleReplayAudio}
          iconName={isAudioPlaying ? "Pause" : "Play"}
          iconPosition="left"
        >
          {isAudioPlaying ? 'Pause' : 'Replay Audio'}
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDownloadTranscript}
          loading={downloadingTranscript}
          iconName="Download"
          iconPosition="left"
        >
          Transcript
        </Button>
        
        {session.status === 'active' || session.status === 'paused' ? (
          <Button
            variant="success"
            size="sm"
            onClick={() => onResumeSession(session.id)}
            iconName="ArrowRight"
            iconPosition="left"
          >
            Resume
          </Button>
        ) : null}
      </div>

      {/* Session Metrics */}
      {session.metrics && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-text-primary font-data">
                {session.metrics.accuracy}%
              </div>
              <div className="text-xs text-text-secondary">Accuracy</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-text-primary font-data">
                {session.metrics.interactions}
              </div>
              <div className="text-xs text-text-secondary">Interactions</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-text-primary font-data">
                {session.metrics.satisfaction}/5
              </div>
              <div className="text-xs text-text-secondary">Rating</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionCard;