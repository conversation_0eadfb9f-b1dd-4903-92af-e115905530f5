import React, { useState } from 'react';
import { CreditCard, TrendingUp, AlertCircle, Plus, RefreshCw, Clock } from 'lucide-react';
import Button from '../../../components/ui/Button';


const CreditPoolManager = ({ currency }) => {
  const [showTopUp, setShowTopUp] = useState(false);
  const [topUpAmount, setTopUpAmount] = useState('');
  const [selectedTopUpOption, setSelectedTopUpOption] = useState('');

  // Mock credit pool data
  const [creditPool, setCreditPool] = useState({
    totalCredits: 500,
    usedCredits: 180,
    remainingCredits: 320,
    monthlyAllowance: 600,
    renewalDate: '2024-02-15',
    autoTopUp: true,
    lowCreditThreshold: 50
  });

  const [usageProjection, setUsageProjection] = useState({
    dailyAverage: 12,
    weeklyTrend: 5,
    projectedEndDate: '2024-02-08',
    recommendedTopUp: 200
  });

  const topUpOptions = [
    { credits: 100, price: { NGN: 15000, USD: 30 }, bonus: 0, popular: false },
    { credits: 250, price: { NGN: 35000, USD: 70 }, bonus: 25, popular: true },
    { credits: 500, price: { NGN: 65000, USD: 130 }, bonus: 75, popular: false },
    { credits: 1000, price: { NGN: 120000, USD: 240 }, bonus: 200, popular: false }
  ];

  const formatPrice = (price, currency) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getCreditStatus = () => {
    const percentage = (creditPool.remainingCredits / creditPool.totalCredits) * 100;
    if (percentage <= 10) return { status: 'critical', color: 'red', message: 'Credits critically low' };
    if (percentage <= 25) return { status: 'low', color: 'yellow', message: 'Credits running low' };
    return { status: 'good', color: 'green', message: 'Credits healthy' };
  };

  const handleTopUp = (option) => {
    const newCredits = creditPool.remainingCredits + option.credits + option.bonus;
    setCreditPool({
      ...creditPool,
      remainingCredits: newCredits,
      totalCredits: creditPool.totalCredits + option.credits + option.bonus
    });
    setShowTopUp(false);
    // Here you would integrate with payment system
  };

  const creditStatus = getCreditStatus();

  return (
    <div className="space-y-6">
      {/* Credit Pool Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Current Balance</h3>
            <CreditCard className="text-blue-500" size={24} />
          </div>
          
          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-2xl font-bold text-gray-900">
                  {creditPool.remainingCredits}
                </span>
                <span className="text-sm text-gray-500">
                  / {creditPool.totalCredits} credits
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full ${
                    creditStatus.color === 'red' ? 'bg-red-500' :
                    creditStatus.color === 'yellow'? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${(creditPool.remainingCredits / creditPool.totalCredits) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className={`flex items-center text-sm ${
              creditStatus.color === 'red' ? 'text-red-600' :
              creditStatus.color === 'yellow'? 'text-yellow-600' : 'text-green-600'
            }`}>
              <AlertCircle size={14} className="mr-1" />
              {creditStatus.message}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Usage Projection</h3>
            <TrendingUp className="text-orange-500" size={24} />
          </div>
          
          <div className="space-y-3">
            <div>
              <span className="text-sm text-gray-600">Daily Average:</span>
              <span className="text-lg font-semibold text-gray-900 ml-2">
                {usageProjection.dailyAverage} credits
              </span>
            </div>
            <div>
              <span className="text-sm text-gray-600">Credits last until:</span>
              <span className="text-sm font-medium text-orange-600 ml-2">
                {formatDate(usageProjection.projectedEndDate)}
              </span>
            </div>
            <div className="pt-2 border-t border-gray-100">
              <span className="text-xs text-gray-500">
                Recommended top-up: {usageProjection.recommendedTopUp} credits
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Auto Top-up</h3>
            <RefreshCw className={`${creditPool.autoTopUp ? 'text-green-500' : 'text-gray-400'}`} size={24} />
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status:</span>
              <span className={`text-sm font-medium ${
                creditPool.autoTopUp ? 'text-green-600' : 'text-gray-600'
              }`}>
                {creditPool.autoTopUp ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Threshold:</span>
              <span className="text-sm font-medium text-gray-900">
                {creditPool.lowCreditThreshold} credits
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => {
                setCreditPool({
                  ...creditPool,
                  autoTopUp: !creditPool.autoTopUp
                });
              }}
            >
              {creditPool.autoTopUp ? 'Disable' : 'Enable'} Auto Top-up
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Button
            onClick={() => setShowTopUp(true)}
            className="flex items-center justify-center"
          >
            <Plus size={16} className="mr-2" />
            Top Up Credits
          </Button>
          
          <Button
            variant="outline"
            onClick={() => {
              // Navigate to purchase history
              console.log('View purchase history');
            }}
          >
            <Clock size={16} className="mr-2" />
            Purchase History
          </Button>
          
          <Button
            variant="outline"
            onClick={() => {
              // Navigate to usage analytics
              console.log('View usage analytics');
            }}
          >
            <TrendingUp size={16} className="mr-2" />
            Usage Analytics
          </Button>
        </div>
      </div>

      {/* Credit Usage by Family Member */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Credit Usage by Family Member
        </h3>
        
        <div className="space-y-4">
          {[
            { name: 'John Doe', avatar: '👨‍💼', used: 45, limit: 100, role: 'Admin' },
            { name: 'Jane Doe', avatar: '👩‍💼', used: 67, limit: 100, role: 'Member' },
            { name: 'Teen Doe', avatar: '👦', used: 23, limit: 75, role: 'Dependent' },
            { name: 'Child Doe', avatar: '👶', used: 12, limit: 50, role: 'Dependent' }
          ].map((member, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-gray-100 rounded-lg">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{member.avatar}</span>
                <div>
                  <h4 className="font-medium text-gray-900">{member.name}</h4>
                  <span className="text-sm text-gray-500">{member.role}</span>
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900 mb-1">
                  {member.used} / {member.limit} credits
                </div>
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${(member.used / member.limit) * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((member.used / member.limit) * 100)}% used
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top-up Modal */}
      {showTopUp && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Top Up Credits</h3>
              <button
                onClick={() => setShowTopUp(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {topUpOptions.map((option, index) => (
                <div
                  key={index}
                  onClick={() => setSelectedTopUpOption(option)}
                  className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedTopUpOption === option
                      ? 'border-blue-500 bg-blue-50' :'border-gray-200 hover:border-gray-300'
                  } ${option.popular ? 'ring-2 ring-blue-200' : ''}`}
                >
                  {option.popular && (
                    <div className="absolute -top-2 left-4 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
                      Popular
                    </div>
                  )}
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-1">
                      {option.credits}
                      {option.bonus > 0 && (
                        <span className="text-green-600 text-sm ml-1">
                          +{option.bonus}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 mb-2">credits</div>
                    <div className="text-lg font-semibold text-blue-600">
                      {formatPrice(option.price[currency], currency)}
                    </div>
                    {option.bonus > 0 && (
                      <div className="text-xs text-green-600 mt-1">
                        Bonus: {option.bonus} credits
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {selectedTopUpOption && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Order Summary</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Base Credits:</span>
                    <span>{selectedTopUpOption.credits}</span>
                  </div>
                  {selectedTopUpOption.bonus > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Bonus Credits:</span>
                      <span>+{selectedTopUpOption.bonus}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-medium border-t border-gray-200 pt-1">
                    <span>Total Credits:</span>
                    <span>{selectedTopUpOption.credits + selectedTopUpOption.bonus}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Amount:</span>
                    <span>{formatPrice(selectedTopUpOption.price[currency], currency)}</span>
                  </div>
                </div>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowTopUp(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => selectedTopUpOption && handleTopUp(selectedTopUpOption)}
                disabled={!selectedTopUpOption}
                className="flex-1"
              >
                <CreditCard size={16} className="mr-2" />
                Proceed to Payment
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreditPoolManager;