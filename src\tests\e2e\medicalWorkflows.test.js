/**
 * COMPREHENSIVE END-TO-END TESTS FOR CRITICAL MEDICAL WORKFLOWS
 * 
 * This test suite provides comprehensive end-to-end testing for critical medical workflows
 * with focus on:
 * - Patient data entry and management
 * - Emergency protocol activation
 * - Provider consultation workflows
 * - Medical data synchronization
 * - Error recovery and data preservation
 * - Performance under load
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - All medical data must be preserved during errors
 * - Emergency protocols must be tested thoroughly
 * - Data integrity must be maintained across workflows
 * - User experience must prioritize patient safety
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import App from '../../App';
import { AuthProvider } from '../../contexts/AuthContext';
import { MedicalErrorProvider } from '../../components/errorBoundaries/MedicalErrorProvider';

// Mock services
vi.mock('../../utils/supabaseClient', () => ({
  supabase: global.testHelpers.createMockSupabaseClient()
}));

vi.mock('../../utils/enhancedMedicalDataService', () => ({
  default: {
    getUserConditions: vi.fn(),
    addCondition: vi.fn(),
    updateCondition: vi.fn(),
    deleteCondition: vi.fn(),
    getUserMedications: vi.fn(),
    addMedication: vi.fn(),
    updateMedication: vi.fn(),
    deleteMedication: vi.fn(),
    getUserSymptoms: vi.fn(),
    addSymptom: vi.fn()
  }
}));

vi.mock('../../services/rbacService', () => ({
  default: {
    hasPermission: vi.fn(() => Promise.resolve(true)),
    requestEmergencyAccess: vi.fn(),
    checkEmergencyAccess: vi.fn()
  }
}));

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <MedicalErrorProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </MedicalErrorProvider>
  </BrowserRouter>
);

describe('Critical Medical Workflows E2E Tests', () => {
  let user;
  let mockMedicalDataService;
  let mockRbacService;

  beforeEach(async () => {
    vi.clearAllMocks();
    user = userEvent.setup();
    
    // Setup mock services
    const { default: medicalDataService } = await import('../../utils/enhancedMedicalDataService');
    const { default: rbacService } = await import('../../services/rbacService');
    
    mockMedicalDataService = medicalDataService;
    mockRbacService = rbacService;
    
    // Setup default successful responses
    mockMedicalDataService.getUserConditions.mockResolvedValue({
      success: true,
      data: [global.mockMedicalData.condition],
      source: 'online'
    });
    
    mockMedicalDataService.getUserMedications.mockResolvedValue({
      success: true,
      data: [global.mockMedicalData.medication],
      source: 'online'
    });
    
    mockMedicalDataService.getUserSymptoms.mockResolvedValue({
      success: true,
      data: [global.mockMedicalData.symptom],
      source: 'online'
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Patient Data Entry Workflow', () => {
    it('should complete full medical condition entry workflow', async () => {
      // Arrange
      mockMedicalDataService.addCondition.mockResolvedValue({
        success: true,
        data: {
          ...global.mockMedicalData.condition,
          id: 'new-condition-id-12345'
        }
      });

      render(<App />, { wrapper: TestWrapper });

      // Wait for app to load
      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Act - Navigate to medical conditions
      const conditionsLink = screen.getByRole('link', { name: /conditions/i });
      await user.click(conditionsLink);

      // Wait for conditions page to load
      await waitFor(() => {
        expect(screen.getByText(/Medical Conditions/i)).toBeInTheDocument();
      });

      // Click add condition button
      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Fill out condition form
      const conditionNameInput = screen.getByLabelText(/condition name/i);
      const severitySelect = screen.getByLabelText(/severity/i);
      const notesTextarea = screen.getByLabelText(/notes/i);

      await user.type(conditionNameInput, 'Diabetes Type 2');
      await user.selectOptions(severitySelect, 'moderate');
      await user.type(notesTextarea, 'Diagnosed during routine checkup');

      // Submit form
      const submitButton = screen.getByRole('button', { name: /save condition/i });
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        expect(mockMedicalDataService.addCondition).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            name: 'Diabetes Type 2',
            severity: 'moderate',
            notes: 'Diagnosed during routine checkup'
          })
        );
      });

      // Verify success message
      expect(screen.getByText(/condition added successfully/i)).toBeInTheDocument();
    });

    it('should handle medication entry with dosage validation', async () => {
      // Arrange
      mockMedicalDataService.addMedication.mockResolvedValue({
        success: true,
        data: {
          ...global.mockMedicalData.medication,
          id: 'new-medication-id-12345'
        }
      });

      render(<App />, { wrapper: TestWrapper });

      // Navigate to medications
      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      const medicationsLink = screen.getByRole('link', { name: /medications/i });
      await user.click(medicationsLink);

      await waitFor(() => {
        expect(screen.getByText(/Medications/i)).toBeInTheDocument();
      });

      // Add new medication
      const addButton = screen.getByRole('button', { name: /add medication/i });
      await user.click(addButton);

      // Fill medication form
      const medicationNameInput = screen.getByLabelText(/medication name/i);
      const dosageInput = screen.getByLabelText(/dosage/i);
      const frequencySelect = screen.getByLabelText(/frequency/i);

      await user.type(medicationNameInput, 'Metformin');
      await user.type(dosageInput, '500mg');
      await user.selectOptions(frequencySelect, 'twice daily');

      // Submit form
      const submitButton = screen.getByRole('button', { name: /save medication/i });
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        expect(mockMedicalDataService.addMedication).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            name: 'Metformin',
            dosage: '500mg',
            frequency: 'twice daily'
          })
        );
      });

      expect(screen.getByText(/medication added successfully/i)).toBeInTheDocument();
    });

    it('should validate medical data before submission', async () => {
      // Arrange
      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Navigate to conditions
      const conditionsLink = screen.getByRole('link', { name: /conditions/i });
      await user.click(conditionsLink);

      await waitFor(() => {
        expect(screen.getByText(/Medical Conditions/i)).toBeInTheDocument();
      });

      // Try to add condition with invalid data
      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Submit empty form
      const submitButton = screen.getByRole('button', { name: /save condition/i });
      await user.click(submitButton);

      // Assert validation errors
      await waitFor(() => {
        expect(screen.getByText(/condition name is required/i)).toBeInTheDocument();
      });

      // Verify service was not called
      expect(mockMedicalDataService.addCondition).not.toHaveBeenCalled();
    });
  });

  describe('Emergency Protocol Workflow', () => {
    it('should activate emergency access protocol', async () => {
      // Arrange
      mockRbacService.requestEmergencyAccess.mockResolvedValue({
        success: true,
        accessGranted: true,
        expiresAt: new Date(Date.now() + 3600000).toISOString()
      });

      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Act - Trigger emergency access
      const emergencyButton = screen.getByRole('button', { name: /emergency access/i });
      await user.click(emergencyButton);

      // Fill emergency access form
      const patientIdInput = screen.getByLabelText(/patient id/i);
      const justificationTextarea = screen.getByLabelText(/justification/i);

      await user.type(patientIdInput, 'emergency-patient-12345');
      await user.type(justificationTextarea, 'Patient experiencing chest pain, need immediate access to medical history');

      // Submit emergency access request
      const requestButton = screen.getByRole('button', { name: /request emergency access/i });
      await user.click(requestButton);

      // Assert
      await waitFor(() => {
        expect(mockRbacService.requestEmergencyAccess).toHaveBeenCalledWith(
          expect.any(String),
          'emergency-patient-12345',
          'Patient experiencing chest pain, need immediate access to medical history',
          expect.objectContaining({
            urgency: 'emergency'
          })
        );
      });

      expect(screen.getByText(/emergency access granted/i)).toBeInTheDocument();
    });

    it('should display emergency medical data with priority', async () => {
      // Arrange
      const emergencyCondition = global.mockEmergencyData.condition;
      mockMedicalDataService.getUserConditions.mockResolvedValue({
        success: true,
        data: [emergencyCondition, global.mockMedicalData.condition],
        source: 'online'
      });

      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Navigate to conditions
      const conditionsLink = screen.getByRole('link', { name: /conditions/i });
      await user.click(conditionsLink);

      // Assert emergency condition is displayed first with priority styling
      await waitFor(() => {
        const conditionsList = screen.getByTestId('conditions-list');
        const firstCondition = within(conditionsList).getAllByTestId('condition-item')[0];
        
        expect(within(firstCondition).getByText('Chest Pain')).toBeInTheDocument();
        expect(firstCondition).toHaveClass('emergency-priority');
      });
    });

    it('should handle emergency data synchronization', async () => {
      // Arrange
      const emergencyData = global.mockEmergencyData.condition;
      
      // Mock offline scenario
      mockMedicalDataService.getUserConditions
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          success: true,
          data: [emergencyData],
          source: 'offline',
          message: 'Showing offline emergency data'
        });

      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Navigate to conditions
      const conditionsLink = screen.getByRole('link', { name: /conditions/i });
      await user.click(conditionsLink);

      // Assert offline emergency data is displayed
      await waitFor(() => {
        expect(screen.getByText('Chest Pain')).toBeInTheDocument();
        expect(screen.getByText(/offline emergency data/i)).toBeInTheDocument();
      });
    });
  });

  describe('Provider Consultation Workflow', () => {
    it('should enable provider to access patient data', async () => {
      // Arrange
      mockRbacService.hasPermission.mockResolvedValue(true);
      
      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Act - Provider searches for patient
      const patientSearchInput = screen.getByLabelText(/search patient/i);
      await user.type(patientSearchInput, 'test-patient-12345');

      const searchButton = screen.getByRole('button', { name: /search/i });
      await user.click(searchButton);

      // Assert patient data is accessible
      await waitFor(() => {
        expect(mockRbacService.hasPermission).toHaveBeenCalledWith(
          expect.any(Object),
          'read:patient_medical_data',
          expect.objectContaining({
            patientId: 'test-patient-12345'
          })
        );
      });

      expect(screen.getByText(/patient medical data/i)).toBeInTheDocument();
    });

    it('should allow provider to add consultation notes', async () => {
      // Arrange
      mockMedicalDataService.addSymptom.mockResolvedValue({
        success: true,
        data: {
          ...global.mockMedicalData.symptom,
          id: 'consultation-symptom-12345'
        }
      });

      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Navigate to consultation interface
      const consultationLink = screen.getByRole('link', { name: /consultation/i });
      await user.click(consultationLink);

      // Add consultation notes
      const symptomInput = screen.getByLabelText(/symptom/i);
      const severitySlider = screen.getByLabelText(/severity/i);
      const notesTextarea = screen.getByLabelText(/consultation notes/i);

      await user.type(symptomInput, 'Fatigue');
      await user.type(severitySlider, '6');
      await user.type(notesTextarea, 'Patient reports increased fatigue over past week');

      // Submit consultation
      const submitButton = screen.getByRole('button', { name: /save consultation/i });
      await user.click(submitButton);

      // Assert
      await waitFor(() => {
        expect(mockMedicalDataService.addSymptom).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            name: 'Fatigue',
            severity: 6,
            notes: 'Patient reports increased fatigue over past week'
          })
        );
      });
    });
  });

  describe('Error Recovery and Data Preservation', () => {
    it('should preserve medical data during network errors', async () => {
      // Arrange
      mockMedicalDataService.addCondition
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          success: true,
          data: global.mockMedicalData.condition
        });

      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Navigate to conditions and add new condition
      const conditionsLink = screen.getByRole('link', { name: /conditions/i });
      await user.click(conditionsLink);

      await waitFor(() => {
        expect(screen.getByText(/Medical Conditions/i)).toBeInTheDocument();
      });

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Fill form
      const conditionNameInput = screen.getByLabelText(/condition name/i);
      await user.type(conditionNameInput, 'Test Condition');

      // Submit (will fail first time)
      const submitButton = screen.getByRole('button', { name: /save condition/i });
      await user.click(submitButton);

      // Assert error message and data preservation
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
        expect(screen.getByText(/data saved locally/i)).toBeInTheDocument();
      });

      // Retry submission
      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);

      // Assert successful submission
      await waitFor(() => {
        expect(screen.getByText(/condition added successfully/i)).toBeInTheDocument();
      });
    });

    it('should handle component errors with medical data preservation', async () => {
      // Arrange
      const ErrorComponent = () => {
        throw new Error('Component error');
      };

      const TestAppWithError = () => (
        <TestWrapper>
          <ErrorComponent />
        </TestWrapper>
      );

      // Act
      render(<TestAppWithError />);

      // Assert error boundary catches error and preserves data
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
        expect(screen.getByText(/medical data preserved/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /emergency access/i })).toBeInTheDocument();
      });
    });
  });

  describe('Performance Under Load', () => {
    it('should handle concurrent medical data operations', async () => {
      // Arrange
      const concurrentOperations = Array(10).fill().map((_, i) => ({
        type: 'condition',
        data: { name: `Condition ${i}`, severity: 'mild' }
      }));

      mockMedicalDataService.addCondition.mockImplementation(() => 
        Promise.resolve({
          success: true,
          data: { id: `condition-${Date.now()}` }
        })
      );

      render(<App />, { wrapper: TestWrapper });

      await waitFor(() => {
        expect(screen.getByText(/VoiceHealth AI/i)).toBeInTheDocument();
      });

      // Act - Simulate concurrent operations
      const startTime = performance.now();
      
      for (const operation of concurrentOperations) {
        const conditionsLink = screen.getByRole('link', { name: /conditions/i });
        await user.click(conditionsLink);
        
        const addButton = screen.getByRole('button', { name: /add condition/i });
        await user.click(addButton);
        
        const conditionNameInput = screen.getByLabelText(/condition name/i);
        await user.type(conditionNameInput, operation.data.name);
        
        const submitButton = screen.getByRole('button', { name: /save condition/i });
        await user.click(submitButton);
      }

      const endTime = performance.now();

      // Assert
      expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
      expect(mockMedicalDataService.addCondition).toHaveBeenCalledTimes(10);
    });
  });
});
