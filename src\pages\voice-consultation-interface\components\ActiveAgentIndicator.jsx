import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';

const ActiveAgentIndicator = ({ 
  currentAgent = null,
  isTransitioning = false,
  agentQueue = [],
  className = ''
}) => {
  const [displayAgent, setDisplayAgent] = useState(currentAgent);
  const [animationClass, setAnimationClass] = useState('');

  useEffect(() => {
    if (currentAgent && currentAgent !== displayAgent) {
      setAnimationClass('animate-ambient-float');
      setTimeout(() => {
        setDisplayAgent(currentAgent);
        setAnimationClass('');
      }, 300);
    }
  }, [currentAgent, displayAgent]);

  if (!displayAgent && !isTransitioning) {
    return (
      <div className={`flex items-center justify-center p-6 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mb-3">
            <Icon name="Users" size={24} color="var(--color-text-secondary)" />
          </div>
          <p className="text-sm text-text-secondary font-caption">
            No agent active
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 shadow-minimal ${className}`}>
      {/* Agent Transition Indicator */}
      {isTransitioning && (
        <div className="flex items-center justify-center mb-4">
          <div className="flex items-center space-x-2 px-3 py-1 bg-warning-50 rounded-full">
            <div className="w-2 h-2 bg-warning-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-warning-600 font-medium">
              Switching agents...
            </span>
          </div>
        </div>
      )}

      {/* Current Agent Display */}
      <div className={`flex items-center space-x-4 ${animationClass}`}>
        {/* Agent Avatar */}
        <div className="relative">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-secondary-100">
            {displayAgent?.avatar ? (
              <Image 
                src={displayAgent.avatar}
                alt={`${displayAgent.name} avatar`}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <Icon 
                  name={
                    displayAgent?.specialty === 'General Practitioner' ? 'Stethoscope' :
                    displayAgent?.specialty === 'Cardiologist' ? 'Heart' :
                    displayAgent?.specialty === 'Nutritionist'? 'Apple' : 'User'
                  }
                  size={24}
                  color="var(--color-text-secondary)"
                />
              </div>
            )}
          </div>
          
          {/* Active Indicator */}
          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-success-500 rounded-full border-2 border-surface flex items-center justify-center">
            <Icon name="Mic" size={12} color="white" />
          </div>
        </div>

        {/* Agent Information */}
        <div className="flex-1">
          <h3 className="font-semibold text-text-primary font-heading">
            {displayAgent?.name || 'AI Agent'}
          </h3>
          <p className="text-sm text-text-secondary font-caption">
            {displayAgent?.specialty || 'Medical Specialist'}
          </p>
          
          {/* Agent Status */}
          <div className="flex items-center space-x-2 mt-2">
            <div className="agent-indicator">
              Active
            </div>
            {displayAgent?.language && (
              <span className="text-xs text-text-muted">
                Speaking {displayAgent.language}
              </span>
            )}
          </div>
        </div>

        {/* Voice Indicator */}
        <div className="flex flex-col items-center space-y-1">
          <div className="voice-input-indicator"></div>
          <span className="text-xs text-text-secondary font-caption">
            Speaking
          </span>
        </div>
      </div>

      {/* Agent Queue */}
      {agentQueue.length > 0 && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Clock" size={14} color="var(--color-text-secondary)" />
            <span className="text-sm font-medium text-text-secondary">
              Next in queue
            </span>
          </div>
          
          <div className="flex space-x-2">
            {agentQueue.slice(0, 3).map((agent, index) => (
              <div 
                key={agent.id || index}
                className="flex items-center space-x-2 px-3 py-1 bg-secondary-50 rounded-lg"
              >
                <div className="w-6 h-6 rounded-full overflow-hidden bg-secondary-100">
                  {agent.avatar ? (
                    <Image 
                      src={agent.avatar}
                      alt={`${agent.name} avatar`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Icon 
                        name={
                          agent.specialty === 'General Practitioner' ? 'Stethoscope' :
                          agent.specialty === 'Cardiologist' ? 'Heart' :
                          agent.specialty === 'Nutritionist'? 'Apple' : 'User'
                        }
                        size={12}
                        color="var(--color-text-secondary)"
                      />
                    </div>
                  )}
                </div>
                <span className="text-xs text-text-secondary">
                  {agent.name}
                </span>
              </div>
            ))}
            
            {agentQueue.length > 3 && (
              <div className="flex items-center px-2">
                <span className="text-xs text-text-muted">
                  +{agentQueue.length - 3} more
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Agent Capabilities */}
      {displayAgent?.capabilities && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="flex flex-wrap gap-2">
            {displayAgent.capabilities.slice(0, 3).map((capability, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-primary-50 text-primary-600 text-xs rounded-full font-medium"
              >
                {capability}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActiveAgentIndicator;