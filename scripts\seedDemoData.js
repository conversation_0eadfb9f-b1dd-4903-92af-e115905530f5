#!/usr/bin/env node

/**
 * Demo Data Seeding Script for VoiceHealth AI
 * Usage: npm run seed-demo
 */

import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.join(__dirname, '../.env.local');
config({ path: envPath });

// Import seeder after env vars are loaded
import demoDataSeeder from '../src/utils/demoDataSeeder.js';

async function main() {
  const action = process.argv[2] || 'seed';
  
  console.log('🚀 VoiceHealth AI Demo Data Seeder');
  console.log('='.repeat(40));
  
  try {
    switch (action) {
      case 'seed':
        console.log('🌱 Seeding demo accounts...\n');
        const seedResults = await demoDataSeeder.seedDemoAccounts();
        
        console.log('\n📊 Seeding Results:');
        seedResults.forEach(result => {
          const status = result.success ? '✅' : '❌';
          console.log(`${status} ${result.email} (${result.role})`);
          if (result.message) console.log(`   ${result.message}`);
          if (result.error) console.log(`   Error: ${result.error}`);
        });
        
        // Test logins after seeding
        console.log('\n🧪 Testing demo logins...\n');
        const testResults = await demoDataSeeder.testDemoLogins();
        
        console.log('\n📊 Login Test Results:');
        testResults.forEach(result => {
          const status = result.loginSuccess ? '✅' : '❌';
          console.log(`${status} ${result.email} - ${result.loginSuccess ? 'Login OK' : result.error}`);
        });
        break;
        
      case 'test':
        console.log('🧪 Testing demo account logins...\n');
        const loginResults = await demoDataSeeder.testDemoLogins();
        
        console.log('\n📊 Login Test Results:');
        loginResults.forEach(result => {
          const status = result.loginSuccess ? '✅' : '❌';
          console.log(`${status} ${result.email} - ${result.loginSuccess ? 'Login OK' : result.error}`);
          if (result.profile) {
            console.log(`   Role: ${result.profile.role}, Name: ${result.profile.full_name}`);
          }
        });
        break;
        
      case 'cleanup':
        console.log('🧹 Cleaning up demo accounts...\n');
        const cleanupResults = await demoDataSeeder.cleanupDemoAccounts();
        
        console.log('\n📊 Cleanup Results:');
        cleanupResults.forEach(result => {
          const status = result.deleted ? '✅' : 'ℹ️';
          console.log(`${status} ${result.email} - ${result.deleted ? 'Deleted' : result.reason || result.error}`);
        });
        break;
        
      default:
        console.log('Usage: npm run seed-demo [action]');
        console.log('Actions:');
        console.log('  seed    - Create demo accounts (default)');
        console.log('  test    - Test demo account logins');
        console.log('  cleanup - Remove demo accounts');
        process.exit(1);
    }
    
    console.log('\n✨ Demo data seeding completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Error during demo data seeding:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

main();
