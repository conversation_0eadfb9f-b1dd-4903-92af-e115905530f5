/**
 * Create simple PNG icons using Canvas (Node.js version)
 * This creates actual PNG files instead of SVG for better browser compatibility
 */

const fs = require('fs');
const path = require('path');

// Simple Base64 encoded PNG icons (minimal blue squares with VH text)
const icon192Base64 = `iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAAdxJREFUeJzt3GEOwiAQRuEt9f4X9tqJJhoFZmDecxN/XEAI32xnZgZL/b39AHAEAwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNEMAIxmAGA0AwCjGQAYzQDAaAYARjMAMJoBgNF+ASsHAVWcSvkAAAAASUVORK5CYII=`;

const icon512Base64 = `iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAAhtJREFUeJzt3WERACAIQDEVQP4j5wIqQKhd7z0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADzMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABfQgABJoAmAPOFZcEeAAAAAElFTkSuQmCC`;

const createIcons = () => {
  const iconDir = path.join(__dirname, '../public/assets/images');
  
  // Ensure directory exists
  if (!fs.existsSync(iconDir)) {
    fs.mkdirSync(iconDir, { recursive: true });
  }
  
  // Create 192x192 PNG icon
  const icon192Buffer = Buffer.from(icon192Base64, 'base64');
  fs.writeFileSync(path.join(iconDir, 'icon-192.png'), icon192Buffer);
  
  // Create 512x512 PNG icon  
  const icon512Buffer = Buffer.from(icon512Base64, 'base64');
  fs.writeFileSync(path.join(iconDir, 'icon-512.png'), icon512Buffer);
  
  console.log('✅ PNG icons created successfully!');
  console.log('📁 Location: public/assets/images/');
  console.log('📦 Files: icon-192.png, icon-512.png');
};

if (require.main === module) {
  createIcons();
}

module.exports = { createIcons };
