import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AdminNavigationSidebar = ({ isOpen, onToggle, onNavigate }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] = useState(['dashboard']);

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'LayoutDashboard',
      path: '/admin-dashboard',
      items: [
        { id: 'overview', label: 'Overview', path: '/admin-dashboard' },
        { id: 'analytics', label: 'Analytics', path: '/analytics-insights-dashboard' }
      ]
    },
    {
      id: 'users',
      label: 'User Management',
      icon: 'Users',
      items: [
        { id: 'all-users', label: 'All Users', action: 'users' },
        { id: 'user-permissions', label: 'Permissions', action: 'permissions' },
        { id: 'user-activity', label: 'Activity Logs', action: 'activity' }
      ]
    },
    {
      id: 'system',
      label: 'System Analytics',
      icon: 'BarChart3',
      items: [
        { id: 'performance', label: 'Performance', action: 'performance' },
        { id: 'usage-stats', label: 'Usage Statistics', action: 'usage' },
        { id: 'error-logs', label: 'Error Logs', action: 'errors' }
      ]
    },
    {
      id: 'agents',
      label: 'Agent Configuration',
      icon: 'Bot',
      items: [
        { id: 'agent-management', label: 'Manage Agents', action: 'agents' },
        { id: 'voice-profiles', label: 'Voice Profiles', action: 'voices' },
        { id: 'agent-performance', label: 'Performance', action: 'agent-performance' }
      ]
    },
    {
      id: 'platform',
      label: 'Platform Settings',
      icon: 'Settings',
      items: [
        { id: 'general-settings', label: 'General', action: 'settings' },
        { id: 'security', label: 'Security', action: 'security' },
        { id: 'maintenance', label: 'Maintenance', action: 'maintenance' }
      ]
    }
  ];

  const quickActions = [
    { id: 'emergency-stop', label: 'Emergency Stop', icon: 'AlertTriangle', variant: 'danger' },
    { id: 'suspend-user', label: 'Suspend User', icon: 'UserX', variant: 'warning' },
    { id: 'maintenance-mode', label: 'Maintenance Mode', icon: 'Settings', variant: 'info' }
  ];

  const toggleSection = (sectionId) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleItemClick = (item) => {
    if (item.path) {
      navigate(item.path);
    } else if (item.action) {
      onNavigate?.(item.action);
    }
  };

  const isActiveItem = (item) => {
    if (item.path) {
      return location.pathname === item.path;
    }
    return false;
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full bg-surface border-r border-border shadow-large z-30 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0 lg:static lg:shadow-none w-80`}>
        
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center mr-3">
                <Icon name="Shield" size={20} color="white" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-text-primary font-heading">
                  Admin Panel
                </h2>
                <p className="text-xs text-text-secondary">
                  VoiceHealth AI
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              iconName="X"
              className="lg:hidden"
            />
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto p-4">
          <nav className="space-y-2">
            {navigationItems.map((section) => (
              <div key={section.id}>
                <button
                  onClick={() => toggleSection(section.id)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-text-secondary hover:text-text-primary hover:bg-secondary-50 rounded-lg transition-fast"
                >
                  <div className="flex items-center">
                    <Icon name={section.icon} size={18} className="mr-3" />
                    {section.label}
                  </div>
                  <Icon
                    name="ChevronDown"
                    size={16}
                    className={`transform transition-transform ${
                      expandedSections.includes(section.id) ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                {expandedSections.includes(section.id) && section.items && (
                  <div className="ml-6 mt-1 space-y-1">
                    {section.items.map((item) => (
                      <button
                        key={item.id}
                        onClick={() => handleItemClick(item)}
                        className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-fast ${
                          isActiveItem(item)
                            ? 'bg-primary-100 text-primary-700 font-medium' :'text-text-secondary hover:text-text-primary hover:bg-secondary-50'
                        }`}
                      >
                        {item.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Quick Actions */}
        <div className="p-4 border-t border-border bg-secondary-50">
          <h3 className="text-xs font-medium text-text-secondary uppercase tracking-wider mb-3">
            Quick Actions
          </h3>
          <div className="space-y-2">
            {quickActions.map((action) => (
              <Button
                key={action.id}
                variant={action.variant}
                size="sm"
                fullWidth
                iconName={action.icon}
                onClick={() => onNavigate?.(action.id)}
              >
                {action.label}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminNavigationSidebar;