import React from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const SpecialistSelectionCard = ({ 
  agent, 
  isSelected = false,
  onSelect,
  onVoicePreview,
  isPreviewPlaying = false,
  selectedLanguage = 'en'
}) => {
  const getAgentTypeIcon = () => {
    switch (agent.type) {
      case 'gp': return 'Stethoscope';
      case 'cardiologist': return 'Heart';
      case 'nutritionist': return 'Apple';
      default: return 'User';
    }
  };

  const getAgentTypeColor = () => {
    switch (agent.type) {
      case 'gp': return 'primary';
      case 'cardiologist': return 'error';
      case 'nutritionist': return 'success';
      default: return 'secondary';
    }
  };

  const getLocalizedText = (key) => {
    const texts = {
      select: {
        en: 'Select Specialist',
        tw: '<PERSON>',
        yo: '<PERSON>',
        sw: '<PERSON><PERSON>aal<PERSON>',
        af: '<PERSON><PERSON>'
      },
      selected: {
        en: 'Selected',
        tw: 'Wɔayi',
        yo: 'Ti a yan',
        sw: 'Imecha<PERSON><PERSON>wa',
        af: 'Gekies'
      },
      preview: {
        en: 'Preview Voice',
        tw: 'Hwɛ Nne',
        yo: 'Wo Ohun',
        sw: 'Hakiki Sauti',
        af: 'Voorskou Stem'
      },
      expertise: {
        en: 'Specializes in:',
        tw: 'Ɔyɛ adwuma wɔ:',
        yo: 'O ṣe amọjẹ ni:',
        sw: 'Anajua vizuri:',
        af: 'Spesialiseer in:'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  const handleSelection = () => {
    onSelect(agent.id);
  };

  return (
    <div className={`bg-surface border-2 rounded-xl shadow-minimal hover:shadow-elevated transition-all duration-300 transform hover:scale-[1.02] ${
      isSelected 
        ? 'border-primary-500 bg-primary-50 shadow-elevated' 
        : 'border-border hover:border-secondary-300'
    }`}>
      {/* Agent Header */}
      <div className="p-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="relative">
            <div className="w-16 h-16 rounded-full overflow-hidden bg-secondary-100">
              <Image
                src={agent.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${agent.id}`}
                alt={`${agent.name} avatar`}
                className="w-full h-full object-cover"
              />
            </div>
            <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center ${
              getAgentTypeColor() === 'primary' ? 'bg-primary-500' :
              getAgentTypeColor() === 'error' ? 'bg-error-500' :
              getAgentTypeColor() === 'success' ? 'bg-success-500' : 'bg-secondary-500'
            }`}>
              <Icon name={getAgentTypeIcon()} size={12} color="white" />
            </div>
            
            {isSelected && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                <Icon name="Check" size={14} color="white" />
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="font-semibold text-text-primary font-heading text-lg">
              {agent.name}
            </h3>
            <p className="text-text-secondary font-caption">
              {agent.specialty}
            </p>
            <div className="flex items-center space-x-2 mt-1">
              <Icon name="Volume2" size={14} color="var(--color-text-secondary)" />
              <span className="text-sm text-text-secondary">
                {agent.voiceProfile?.gender} • {agent.voiceProfile?.accent}
              </span>
            </div>
          </div>
        </div>

        {/* Expertise Focus */}
        <div className="mb-4">
          <p className="text-sm font-medium text-text-primary mb-2">
            {getLocalizedText('expertise')}
          </p>
          <div className="flex flex-wrap gap-2">
            {agent.expertiseFocus?.slice(0, 3).map((area, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-secondary-100 text-text-secondary text-sm rounded-full"
              >
                {area}
              </span>
            ))}
            {agent.expertiseFocus?.length > 3 && (
              <span className="px-3 py-1 bg-secondary-100 text-text-secondary text-sm rounded-full">
                +{agent.expertiseFocus.length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Sample Response Preview */}
        <div className="mb-4 p-4 bg-secondary-50 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
              getAgentTypeColor() === 'primary' ? 'bg-primary-500' :
              getAgentTypeColor() === 'error' ? 'bg-error-500' :
              getAgentTypeColor() === 'success' ? 'bg-success-500' : 'bg-secondary-500'
            }`}>
              <Icon name={getAgentTypeIcon()} size={14} color="white" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-text-primary font-caption leading-relaxed line-clamp-3">
                {agent.sampleResponse}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onVoicePreview?.(agent.id, agent.voiceProfile)}
            loading={isPreviewPlaying}
            iconName={isPreviewPlaying ? "Pause" : "Play"}
            iconPosition="left"
            className="flex-1"
          >
            {getLocalizedText('preview')}
          </Button>
          
          <Button
            variant={isSelected ? "success" : "primary"}
            size="sm"
            onClick={handleSelection}
            iconName={isSelected ? "Check" : "Plus"}
            iconPosition="left"
            className="flex-1"
          >
            {isSelected ? getLocalizedText('selected') : getLocalizedText('select')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SpecialistSelectionCard;