/**
 * COMPLETE MULTI-AGENT SYSTEM INTEGRATION TEST
 * 
 * Tests the entire multi-agent healthcare system end-to-end to verify
 * that all phases (1, 2, and 3) work together correctly.
 */

import { agentOrchestrator } from '../services/AgentOrchestrator';
import { agentRegistry } from '../services/AgentRegistry';
import { agentCommunicationProtocol } from '../services/AgentCommunicationProtocol';
import { multiAgentCollaborationEngine } from '../services/MultiAgentCollaborationEngine';
import { realTimeAgentCommunication } from '../services/RealTimeAgentCommunication';
import { collaborationUIService } from '../services/CollaborationUIService';
import { memoryManager } from '../services/MemoryManager';

describe('Complete Multi-Agent System Integration Tests', () => {
  const testSessionId = `complete-test-${Date.now()}`;

  beforeAll(async () => {
    // Initialize the complete system
    await agentOrchestrator.initialize();
  });

  afterAll(async () => {
    // Clean up test data
    await memoryManager.clearConversationMemory(testSessionId);
    await agentOrchestrator.shutdown();
  });

  describe('Phase 1: Infrastructure Verification', () => {
    test('should have persistent memory system', async () => {
      // Test memory persistence
      const success = await memoryManager.saveMessage(
        testSessionId,
        'user',
        'test-user',
        'Test User',
        'Testing memory persistence',
        1
      );

      expect(success).toBe(true);

      const messages = await memoryManager.getConversationHistory(testSessionId);
      expect(messages).toHaveLength(1);
      expect(messages[0].content).toBe('Testing memory persistence');
    });

    test('should have consolidated agent architecture', () => {
      const stats = agentRegistry.getRegistryStats();
      expect(stats.totalAgents).toBeGreaterThanOrEqual(6); // All our agents
      expect(stats.healthyAgents).toBeGreaterThan(0);
    });

    test('should have secure configuration', () => {
      // Verify no client-side API keys (would be in environment)
      expect(process.env.VITE_OPENAI_API_KEY).toBeUndefined();
      expect(process.env.VITE_ANTHROPIC_API_KEY).toBeUndefined();
    });
  });

  describe('Phase 2: Real Agent Architecture Verification', () => {
    test('should have all specialist agents registered', () => {
      const triageAgents = agentRegistry.getAgentsByRole('triage');
      const emergencyAgents = agentRegistry.getAgentsByRole('emergency');
      const gpAgents = agentRegistry.getAgentsByRole('general_practitioner');
      const cardiologyAgents = agentRegistry.getAgentsByRole('cardiologist');
      const nutritionAgents = agentRegistry.getAgentsByRole('nutritionist');
      const mentalHealthAgents = agentRegistry.getAgentsByRole('psychiatrist');

      expect(triageAgents).toHaveLength(1);
      expect(emergencyAgents).toHaveLength(1);
      expect(gpAgents).toHaveLength(1);
      expect(cardiologyAgents).toHaveLength(1);
      expect(nutritionAgents).toHaveLength(1);
      expect(mentalHealthAgents).toHaveLength(1);
    });

    test('should route new conversations to triage agent', async () => {
      const response = await agentOrchestrator.processRequest({
        sessionId: testSessionId,
        userMessage: 'I need medical advice',
        urgencyLevel: 'medium'
      });

      expect(response).toBeDefined();
      expect(response.agentResponse.agentName).toBe('Nurse Triage Specialist');
    });

    test('should detect emergency situations', async () => {
      const emergencySessionId = `emergency-complete-test-${Date.now()}`;
      
      const response = await agentOrchestrator.processRequest({
        sessionId: emergencySessionId,
        userMessage: 'I am having severe chest pain and cannot breathe',
        urgencyLevel: 'critical'
      });

      expect(response).toBeDefined();
      expect(response.agentResponse.emergencyFlags).toBeDefined();
      expect(response.agentResponse.emergencyFlags!.length).toBeGreaterThan(0);
      
      // Clean up
      await memoryManager.clearConversationMemory(emergencySessionId);
    });

    test('should perform agent handoffs', async () => {
      const handoffSessionId = `handoff-complete-test-${Date.now()}`;
      
      // Start with triage
      await agentOrchestrator.processRequest({
        sessionId: handoffSessionId,
        userMessage: 'I have heart palpitations',
        urgencyLevel: 'medium'
      });

      // Request handoff to cardiologist
      const handoffResult = await agentOrchestrator.performHandoff(
        handoffSessionId,
        'cardiologist',
        'Patient has cardiac symptoms requiring specialist evaluation'
      );

      expect(handoffResult).toBe(true);
      
      const currentAgent = agentOrchestrator.getCurrentAgent(handoffSessionId);
      expect(currentAgent?.role).toBe('cardiologist');
      
      // Clean up
      await memoryManager.clearConversationMemory(handoffSessionId);
    });
  });

  describe('Phase 3: Multi-Agent Collaboration Verification', () => {
    test('should enable agent-to-agent communication', async () => {
      const triageAgent = agentRegistry.getAgentsByRole('triage')[0];
      const cardiologyAgent = agentRegistry.getAgentsByRole('cardiologist')[0];

      const success = await agentCommunicationProtocol.sendMessage({
        fromAgentId: triageAgent.id,
        toAgentId: cardiologyAgent.id,
        sessionId: testSessionId,
        messageType: 'consultation_request',
        content: 'Patient with chest pain - requesting cardiology consultation',
        priority: 'high',
        requiresResponse: true
      });

      expect(success).toBe(true);

      const pendingMessages = agentCommunicationProtocol.getPendingMessages(cardiologyAgent.id);
      expect(pendingMessages.length).toBeGreaterThan(0);
    });

    test('should support multi-agent case collaboration', async () => {
      const collaborationSessionId = `collab-complete-test-${Date.now()}`;
      const gpAgent = agentRegistry.getAgentsByRole('general_practitioner')[0];

      const collaborationId = await multiAgentCollaborationEngine.initiateCollaboration(
        collaborationSessionId,
        {
          patientId: 'test-patient',
          chiefComplaint: 'Complex cardiac and mental health symptoms',
          symptoms: ['chest pain', 'anxiety', 'palpitations'],
          medicalHistory: ['hypertension'],
          currentMedications: ['lisinopril'],
          allergies: [],
          urgencyLevel: 'high' as const
        },
        gpAgent.id
      );

      expect(collaborationId).toBeDefined();
      expect(collaborationId).not.toBeNull();

      const activeCase = multiAgentCollaborationEngine.getCase(collaborationId!);
      expect(activeCase).toBeDefined();
      expect(activeCase!.assignedAgents.length).toBeGreaterThan(1);
      
      // Clean up
      await memoryManager.clearConversationMemory(collaborationSessionId);
    });

    test('should provide real-time agent presence tracking', () => {
      const allAgents = agentRegistry.getAllAgents();
      
      // Register agents for real-time communication
      allAgents.forEach(agent => {
        realTimeAgentCommunication.registerAgent(agent.id, agent.name);
      });

      const onlineAgents = realTimeAgentCommunication.getOnlineAgents();
      expect(onlineAgents.length).toBeGreaterThan(0);

      const availableAgents = realTimeAgentCommunication.getAvailableAgents();
      expect(availableAgents.length).toBeGreaterThan(0);
    });

    test('should connect UI to real backend data', () => {
      const uiAgentData = collaborationUIService.getUIAgentData();
      expect(uiAgentData.length).toBeGreaterThanOrEqual(6);

      uiAgentData.forEach(agent => {
        expect(agent.id).toBeDefined();
        expect(agent.name).toBeDefined();
        expect(agent.role).toBeDefined();
        expect(agent.specialization).toBeDefined();
        expect(['online', 'busy', 'away', 'offline', 'speaking', 'listening']).toContain(agent.status);
      });
    });
  });

  describe('End-to-End Medical Consultation Scenarios', () => {
    test('should handle complex cardiac case with multi-agent collaboration', async () => {
      const cardiacSessionId = `cardiac-e2e-test-${Date.now()}`;
      
      // Patient presents with chest pain
      const triageResponse = await agentOrchestrator.processRequest({
        sessionId: cardiacSessionId,
        userMessage: 'I have been experiencing chest pain and shortness of breath for the past hour',
        urgencyLevel: 'high'
      });

      expect(triageResponse.agentResponse.agentName).toBe('Nurse Triage Specialist');
      expect(triageResponse.agentResponse.suggestedHandoffs).toBeDefined();

      // Should suggest cardiology handoff
      const cardioHandoff = triageResponse.agentResponse.suggestedHandoffs?.find(
        handoff => handoff.targetAgentRole === 'cardiologist'
      );
      expect(cardioHandoff).toBeDefined();

      // Perform handoff to cardiologist
      const handoffSuccess = await agentOrchestrator.performHandoff(
        cardiacSessionId,
        'cardiologist',
        'Cardiac symptoms requiring specialist evaluation'
      );
      expect(handoffSuccess).toBe(true);

      // Cardiologist consultation
      const cardioResponse = await agentOrchestrator.processRequest({
        sessionId: cardiacSessionId,
        userMessage: 'The pain is crushing and radiates to my left arm',
        urgencyLevel: 'critical'
      });

      expect(cardioResponse.agentResponse.agentName).toBe('Dr. Michael Rodriguez');
      expect(cardioResponse.agentResponse.emergencyFlags).toBeDefined();
      
      // Clean up
      await memoryManager.clearConversationMemory(cardiacSessionId);
    });

    test('should handle mental health crisis with proper protocols', async () => {
      const mentalHealthSessionId = `mental-health-e2e-test-${Date.now()}`;
      
      // Patient expresses suicidal ideation
      const response = await agentOrchestrator.processRequest({
        sessionId: mentalHealthSessionId,
        userMessage: 'I have been feeling very depressed and sometimes think about ending my life',
        urgencyLevel: 'critical'
      });

      // Should route to mental health agent or emergency
      expect(['Dr. Jennifer Park', 'Dr. Emergency Response']).toContain(response.agentResponse.agentName);
      expect(response.agentResponse.emergencyFlags).toBeDefined();
      
      if (response.agentResponse.emergencyFlags && response.agentResponse.emergencyFlags.length > 0) {
        const criticalFlags = response.agentResponse.emergencyFlags.filter(flag => flag.severity === 'critical');
        expect(criticalFlags.length).toBeGreaterThan(0);
      }
      
      // Clean up
      await memoryManager.clearConversationMemory(mentalHealthSessionId);
    });

    test('should maintain conversation context across agent handoffs', async () => {
      const contextSessionId = `context-e2e-test-${Date.now()}`;
      
      // Initial triage
      await agentOrchestrator.processRequest({
        sessionId: contextSessionId,
        userMessage: 'I am a 45-year-old male with diabetes and high blood pressure',
        urgencyLevel: 'medium'
      });

      // Handoff to GP
      await agentOrchestrator.performHandoff(
        contextSessionId,
        'general_practitioner',
        'Patient needs comprehensive primary care evaluation'
      );

      // GP consultation should have access to previous context
      const gpResponse = await agentOrchestrator.processRequest({
        sessionId: contextSessionId,
        userMessage: 'I want to discuss my diabetes management',
        urgencyLevel: 'medium'
      });

      expect(gpResponse.agentResponse.agentName).toBe('Dr. Sarah Chen');
      
      // Check that conversation history is maintained
      const conversationHistory = await memoryManager.getConversationHistory(contextSessionId);
      expect(conversationHistory.length).toBeGreaterThan(2); // Multiple messages preserved
      
      // Clean up
      await memoryManager.clearConversationMemory(contextSessionId);
    });
  });

  describe('System Performance and Reliability', () => {
    test('should handle multiple concurrent sessions', async () => {
      const concurrentSessions = [];
      const sessionCount = 5;

      // Create multiple concurrent sessions
      for (let i = 0; i < sessionCount; i++) {
        const sessionId = `concurrent-test-${Date.now()}-${i}`;
        concurrentSessions.push(
          agentOrchestrator.processRequest({
            sessionId,
            userMessage: `Concurrent test message ${i}`,
            urgencyLevel: 'low'
          })
        );
      }

      // Wait for all sessions to complete
      const responses = await Promise.all(concurrentSessions);
      
      // All sessions should complete successfully
      responses.forEach(response => {
        expect(response).toBeDefined();
        expect(response.agentResponse.content).toBeDefined();
      });

      // Clean up
      for (let i = 0; i < sessionCount; i++) {
        const sessionId = `concurrent-test-${Date.now()}-${i}`;
        await memoryManager.clearConversationMemory(sessionId);
      }
    });

    test('should maintain system health under load', async () => {
      const registryStats = agentRegistry.getRegistryStats();
      const orchestratorStats = agentOrchestrator.getOrchestratorStats();
      const rtCommStats = realTimeAgentCommunication.getStats();

      expect(registryStats.healthyAgents).toBe(registryStats.totalAgents);
      expect(orchestratorStats.registryStats.totalAgents).toBeGreaterThan(0);
      expect(rtCommStats.totalAgents).toBeGreaterThanOrEqual(0);
    });

    test('should handle emergency response time requirements', async () => {
      const emergencySessionId = `emergency-timing-test-${Date.now()}`;
      const startTime = Date.now();
      
      const response = await agentOrchestrator.processRequest({
        sessionId: emergencySessionId,
        userMessage: 'EMERGENCY: I cannot breathe and have severe chest pain',
        urgencyLevel: 'critical'
      });

      const responseTime = Date.now() - startTime;
      
      expect(response).toBeDefined();
      expect(responseTime).toBeLessThan(5000); // Should respond within 5 seconds
      
      // If emergency flags are critical, response time should be even faster
      if (response.agentResponse.emergencyFlags?.some(flag => flag.severity === 'critical')) {
        expect(responseTime).toBeLessThan(3000); // Critical emergencies within 3 seconds
      }
      
      // Clean up
      await memoryManager.clearConversationMemory(emergencySessionId);
    });
  });
});

/**
 * Manual system verification function
 */
export async function verifyCompleteSystem() {
  console.log('🔍 Verifying Complete Multi-Agent Healthcare System...');
  
  try {
    // Initialize system
    await agentOrchestrator.initialize();
    
    // Verify Phase 1: Infrastructure
    console.log('✅ Phase 1: Infrastructure verified');
    const memoryHealth = await memoryManager.healthCheck();
    console.log('  - Memory system:', memoryHealth.healthy ? 'Healthy' : 'Unhealthy');
    
    // Verify Phase 2: Agent Architecture
    console.log('✅ Phase 2: Agent Architecture verified');
    const registryStats = agentRegistry.getRegistryStats();
    console.log(`  - Total agents: ${registryStats.totalAgents}`);
    console.log(`  - Healthy agents: ${registryStats.healthyAgents}`);
    
    // Verify Phase 3: Collaboration
    console.log('✅ Phase 3: Multi-Agent Collaboration verified');
    const rtStats = realTimeAgentCommunication.getStats();
    console.log(`  - Real-time communication: ${rtStats.totalAgents} agents registered`);
    
    console.log('🎉 Complete system verification successful!');
    console.log('🏥 VoiceHealth AI Multi-Agent System is fully operational');
    
  } catch (error) {
    console.error('❌ System verification failed:', error);
  }
}
