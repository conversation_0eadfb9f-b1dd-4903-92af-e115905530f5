import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const NetworkOrchestrationControls = ({ 
  networkMetrics = {}, 
  agents = [], 
  onEmergencyOverride, 
  onNetworkFailover 
}) => {
  const [loadBalancingMode, setLoadBalancingMode] = useState('automatic');
  const [routingStrategy, setRoutingStrategy] = useState('contextual');
  const [emergencyMode, setEmergencyMode] = useState(false);

  const handleLoadBalancingChange = (mode) => {
    setLoadBalancingMode(mode);
    console.log(`Load balancing mode changed to: ${mode}`);
  };

  const handleRoutingChange = (strategy) => {
    setRoutingStrategy(strategy);
    console.log(`Routing strategy changed to: ${strategy}`);
  };

  const handleEmergencyToggle = () => {
    setEmergencyMode(!emergencyMode);
    if (!emergencyMode) {
      onEmergencyOverride?.();
    }
  };

  const getAgentLoadDistribution = () => {
    if (!agents?.length) return [];
    
    return agents.map(agent => ({
      name: agent.shortName,
      load: agent.currentLoad || 0,
      queue: agent.queueLength || 0,
      status: agent.status
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-text-primary font-heading">
            Network Orchestration Controls
          </h2>
          <p className="text-text-secondary mt-1">
            Manage agent availability, load balancing, and automatic routing
          </p>
        </div>
        
        {emergencyMode && (
          <div className="flex items-center gap-2 px-3 py-2 bg-error-50 border border-error-200 rounded-lg">
            <Icon name="AlertTriangle" size={16} className="text-error-500" />
            <span className="text-sm font-medium text-error-700">Emergency Mode Active</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Load Balancing Controls */}
        <div className="bg-surface rounded-lg border border-border p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
            <Icon name="BarChart3" size={20} className="text-primary-500" />
            Load Balancing
          </h3>
          
          <div className="space-y-4">
            {/* Load Balancing Mode */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Balancing Strategy
              </label>
              <div className="space-y-2">
                {[
                  { value: 'automatic', label: 'Automatic', description: 'AI-optimized load distribution' },
                  { value: 'round-robin', label: 'Round Robin', description: 'Equal distribution across agents' },
                  { value: 'least-loaded', label: 'Least Loaded', description: 'Route to agents with lowest load' },
                  { value: 'specialty-first', label: 'Specialty First', description: 'Prioritize specialist expertise' }
                ].map((mode) => (
                  <label key={mode.value} className="flex items-center p-3 border border-border rounded-lg cursor-pointer hover:bg-background">
                    <input
                      type="radio"
                      name="loadBalancing"
                      value={mode.value}
                      checked={loadBalancingMode === mode.value}
                      onChange={(e) => handleLoadBalancingChange(e.target.value)}
                      className="mr-3"
                    />
                    <div>
                      <div className="font-medium text-text-primary">{mode.label}</div>
                      <div className="text-sm text-text-secondary">{mode.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Current Load Distribution */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-3">Current Load Distribution</h4>
              <div className="space-y-2">
                {getAgentLoadDistribution().map((agent, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-background rounded">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-text-primary">{agent.name}</span>
                      <span className={`w-2 h-2 rounded-full ${
                        agent.status === 'active' ? 'bg-success-500' :
                        agent.status === 'busy' ? 'bg-warning-500' : 'bg-error-500'
                      }`}></span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <div className={`text-sm font-medium ${
                          agent.load >= 80 ? 'text-error-500' :
                          agent.load >= 60 ? 'text-warning-500' : 'text-success-500'
                        }`}>
                          {agent.load}%
                        </div>
                        <div className="text-xs text-text-secondary">
                          Queue: {agent.queue}
                        </div>
                      </div>
                      <div className="w-16 bg-background rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            agent.load >= 80 ? 'bg-error-500' :
                            agent.load >= 60 ? 'bg-warning-500' : 'bg-success-500'
                          }`}
                          style={{ width: `${agent.load}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Routing Controls */}
        <div className="bg-surface rounded-lg border border-border p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
            <Icon name="Route" size={20} className="text-primary-500" />
            Automatic Routing
          </h3>
          
          <div className="space-y-4">
            {/* Routing Strategy */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Routing Strategy
              </label>
              <div className="space-y-2">
                {[
                  { value: 'contextual', label: 'Contextual', description: 'Route based on conversation context' },
                  { value: 'keyword', label: 'Keyword-based', description: 'Route using keyword matching' },
                  { value: 'ml-powered', label: 'ML-Powered', description: 'Machine learning classification' }
                ].map((strategy) => (
                  <label key={strategy.value} className="flex items-center p-3 border border-border rounded-lg cursor-pointer hover:bg-background">
                    <input
                      type="radio"
                      name="routing"
                      value={strategy.value}
                      checked={routingStrategy === strategy.value}
                      onChange={(e) => handleRoutingChange(e.target.value)}
                      className="mr-3"
                    />
                    <div>
                      <div className="font-medium text-text-primary">{strategy.label}</div>
                      <div className="text-sm text-text-secondary">{strategy.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Routing Rules */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-3">Active Routing Rules</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <span className="text-text-primary">Mental health keywords → MH Specialist</span>
                  <Icon name="Check" size={16} className="text-success-500" />
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <span className="text-text-primary">Skin condition images → Dermatologist</span>
                  <Icon name="Check" size={16} className="text-success-500" />
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <span className="text-text-primary">Child age &lt; 18 → Paediatrician</span>
                  <Icon name="Check" size={16} className="text-success-500" />
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <span className="text-text-primary">Nutrition questions → Dietician</span>
                  <Icon name="Check" size={16} className="text-success-500" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Agent Availability Controls */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
          <Icon name="Power" size={20} className="text-primary-500" />
          Agent Availability Management
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {agents?.map((agent) => (
            <div key={agent.id} className="border border-border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <span className="text-lg">{agent.avatar}</span>
                <div>
                  <div className="font-medium text-text-primary text-sm">{agent.shortName}</div>
                  <div className={`text-xs ${
                    agent.status === 'active' ? 'text-success-600' :
                    agent.status === 'busy' ? 'text-warning-600' : 'text-error-600'
                  }`}>
                    {agent.status}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button 
                  variant={agent.status === 'active' ? 'outline' : 'primary'}
                  size="sm" 
                  fullWidth
                  iconName={agent.status === 'active' ? 'Pause' : 'Play'}
                >
                  {agent.status === 'active' ? 'Pause' : 'Activate'}
                </Button>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  fullWidth
                  iconName="Settings"
                >
                  Configure
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Emergency Controls */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
          <Icon name="Shield" size={20} className="text-error-500" />
          Emergency Override Controls
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            variant={emergencyMode ? "danger" : "outline"}
            onClick={handleEmergencyToggle}
            iconName="AlertTriangle"
            fullWidth
          >
            {emergencyMode ? 'Disable Emergency Mode' : 'Emergency Override'}
          </Button>
          
          <Button
            variant="outline"
            onClick={onNetworkFailover}
            iconName="RefreshCw"
            fullWidth
          >
            Network Failover
          </Button>
          
          <Button
            variant="outline"
            iconName="Activity"
            fullWidth
          >
            System Health Check
          </Button>
        </div>
        
        <div className="mt-4 p-3 bg-warning-50 border border-warning-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Icon name="Info" size={16} className="text-warning-600 mt-0.5" />
            <div className="text-sm text-warning-700">
              <strong>Emergency Mode:</strong> Activates priority routing, increases response speed, 
              and enables direct escalation to human oversight for critical cases.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkOrchestrationControls;