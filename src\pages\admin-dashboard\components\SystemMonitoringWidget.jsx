import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SystemMonitoringWidget = ({ metrics = {} }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [alertsVisible, setAlertsVisible] = useState(true);

  const systemMetrics = {
    serverHealth: {
      status: 'healthy',
      uptime: '99.9%',
      cpu: 45,
      memory: 62,
      disk: 78,
      network: 23
    },
    apiMetrics: {
      responseTime: '145ms',
      requests: '12.4K/min',
      errorRate: '0.02%',
      throughput: '2.3MB/s'
    },
    alerts: [
      {
        id: 1,
        level: 'warning',
        message: 'High memory usage detected on server-02',
        timestamp: '2 minutes ago',
        resolved: false
      },
      {
        id: 2,
        level: 'info',
        message: 'System maintenance scheduled for tonight',
        timestamp: '1 hour ago',
        resolved: false
      }
    ],
    ...metrics
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'text-success-600 bg-success-100';
      case 'warning': return 'text-warning-600 bg-warning-100';
      case 'critical': return 'text-error-600 bg-error-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertColor = (level) => {
    switch (level) {
      case 'critical': return 'border-error-200 bg-error-50';
      case 'warning': return 'border-warning-200 bg-warning-50';
      case 'info': return 'border-primary-200 bg-primary-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getAlertIcon = (level) => {
    switch (level) {
      case 'critical': return 'AlertTriangle';
      case 'warning': return 'AlertCircle';
      case 'info': return 'Info';
      default: return 'Bell';
    }
  };

  const getMetricColor = (value) => {
    if (value >= 90) return 'text-error-600';
    if (value >= 70) return 'text-warning-600';
    return 'text-success-600';
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              System Monitoring
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Real-time system health and performance
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(systemMetrics.serverHealth?.status)}`}>
              <Icon name="Activity" size={16} className="mr-2" />
              {systemMetrics.serverHealth?.status}
            </span>
            <Button
              size="sm"
              variant="outline"
              iconName="RefreshCw"
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mt-4 bg-secondary-50 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview', icon: 'BarChart3' },
            { id: 'performance', label: 'Performance', icon: 'Zap' },
            { id: 'alerts', label: 'Alerts', icon: 'Bell' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-fast ${
                activeTab === tab.id
                  ? 'bg-surface text-text-primary shadow-minimal'
                  : 'text-text-secondary hover:text-text-primary'
              }`}
            >
              <Icon name={tab.icon} size={16} className="mr-2" />
              {tab.label}
              {tab.id === 'alerts' && systemMetrics.alerts?.filter(alert => !alert.resolved).length > 0 && (
                <span className="ml-2 bg-error-500 text-white text-xs rounded-full px-1.5 py-0.5">
                  {systemMetrics.alerts?.filter(alert => !alert.resolved).length}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Server Health */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-3">Server Health</h4>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { label: 'CPU Usage', value: systemMetrics.serverHealth?.cpu, unit: '%' },
                  { label: 'Memory', value: systemMetrics.serverHealth?.memory, unit: '%' },
                  { label: 'Disk Usage', value: systemMetrics.serverHealth?.disk, unit: '%' },
                  { label: 'Network', value: systemMetrics.serverHealth?.network, unit: '%' }
                ].map((metric) => (
                  <div key={metric.label} className="bg-secondary-50 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-text-secondary font-medium">{metric.label}</span>
                      <span className={`text-sm font-semibold ${getMetricColor(metric.value)}`}>
                        {metric.value}{metric.unit}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          metric.value >= 90 ? 'bg-error-500' :
                          metric.value >= 70 ? 'bg-warning-500' : 'bg-success-500'
                        }`}
                        style={{ width: `${metric.value}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Key Metrics */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-3">Key Metrics</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <Icon name="Clock" size={20} className="text-primary-500 mr-3" />
                    <div>
                      <p className="text-xs text-text-secondary">Uptime</p>
                      <p className="text-lg font-semibold text-text-primary">{systemMetrics.serverHealth?.uptime}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <Icon name="Zap" size={20} className="text-success-500 mr-3" />
                    <div>
                      <p className="text-xs text-text-secondary">Response Time</p>
                      <p className="text-lg font-semibold text-text-primary">{systemMetrics.apiMetrics?.responseTime}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <Icon name="BarChart" size={20} className="text-warning-500 mr-3" />
                    <div>
                      <p className="text-xs text-text-secondary">Requests/min</p>
                      <p className="text-lg font-semibold text-text-primary">{systemMetrics.apiMetrics?.requests}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-secondary-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <Icon name="AlertTriangle" size={20} className="text-error-500 mr-3" />
                    <div>
                      <p className="text-xs text-text-secondary">Error Rate</p>
                      <p className="text-lg font-semibold text-text-primary">{systemMetrics.apiMetrics?.errorRate}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <div className="space-y-6">
            <div className="bg-secondary-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-sm font-medium text-text-primary">API Performance</h4>
                <Button size="sm" variant="outline" iconName="Download">
                  Export
                </Button>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-text-secondary mb-1">Throughput</p>
                  <p className="text-xl font-semibold text-text-primary">{systemMetrics.apiMetrics?.throughput}</p>
                </div>
                <div>
                  <p className="text-xs text-text-secondary mb-1">Avg Response</p>
                  <p className="text-xl font-semibold text-text-primary">{systemMetrics.apiMetrics?.responseTime}</p>
                </div>
              </div>
            </div>
            
            <div className="h-64 bg-secondary-50 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <Icon name="TrendingUp" size={48} className="text-text-secondary mx-auto mb-2" />
                <p className="text-text-secondary">Performance charts would be rendered here</p>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'alerts' && (
          <div className="space-y-4">
            {alertsVisible && systemMetrics.alerts?.length > 0 ? (
              systemMetrics.alerts?.map((alert) => (
                <div key={alert.id} className={`border rounded-lg p-4 ${getAlertColor(alert.level)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start">
                      <Icon name={getAlertIcon(alert.level)} size={20} className="mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-text-primary mb-1">{alert.message}</p>
                        <p className="text-xs text-text-secondary">{alert.timestamp}</p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="ghost" iconName="Check">
                        Resolve
                      </Button>
                      <Button size="sm" variant="ghost" iconName="X">
                        Dismiss
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Icon name="CheckCircle" size={48} className="text-success-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">All Clear!</h3>
                <p className="text-text-secondary">No active alerts at this time</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemMonitoringWidget;