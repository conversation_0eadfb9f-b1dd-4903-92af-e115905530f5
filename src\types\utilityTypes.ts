/**
 * ENHANCED TYPESCRIPT UTILITY TYPES FOR MEDICAL SAFETY
 * 
 * This file contains advanced TypeScript utility types for:
 * - Strict type safety for medical data operations
 * - HIPAA-compliant data transformation types
 * - Emergency data access type guards
 * - Performance optimization type helpers
 * - Audit trail type enforcement
 * - Medical data validation types
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - All medical operations must be type-safe
 * - Emergency data must have special type handling
 * - Data transformations must preserve type safety
 * - Audit trails must be comprehensively typed
 * - Performance types must not compromise safety
 */

// Base utility types for medical data
export type NonEmptyString<T extends string> = T extends '' ? never : T;
export type PositiveNumber = number & { readonly __brand: 'PositiveNumber' };
export type MedicalId = string & { readonly __brand: 'MedicalId' };
export type PatientId = string & { readonly __brand: 'PatientId' };
export type ProviderId = string & { readonly __brand: 'ProviderId' };
export type EmergencyFlag = boolean & { readonly __brand: 'EmergencyFlag' };

// Branded types for medical safety
export type EncryptedData<T> = T & { readonly __encrypted: true };
export type ValidatedData<T> = T & { readonly __validated: true };
export type AuditedData<T> = T & { readonly __audited: true };
export type EmergencyData<T> = T & { readonly __emergency: true };

// Type-safe date handling
export type ISODateString = string & { readonly __brand: 'ISODateString' };
export type UnixTimestamp = number & { readonly __brand: 'UnixTimestamp' };

// Medical data state types
export type MedicalDataState<T> = {
  readonly data: T | null;
  readonly loading: boolean;
  readonly error: string | null;
  readonly lastUpdated: UnixTimestamp | null;
  readonly source: 'cache' | 'network' | 'offline';
  readonly isEmergency: boolean;
};

// Strict readonly types for immutable medical data
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends (infer U)[]
    ? readonly DeepReadonly<U>[]
    : T[P] extends object
    ? DeepReadonly<T[P]>
    : T[P];
};

// Required fields validator
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Optional fields helper
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Medical data creation types (without system fields)
export type CreateMedicalData<T> = Omit<T, 'id' | 'created_at' | 'updated_at' | 'patient_id'>;

// Medical data update types (only updatable fields)
export type UpdateMedicalData<T> = Partial<Omit<T, 'id' | 'created_at' | 'patient_id'>>;

// Emergency data access types
export type EmergencyAccessible<T> = T extends { is_emergency: true } ? T : never;
export type NonEmergencyData<T> = T extends { is_emergency: false } ? T : never;

// Type-safe API response wrapper
export type ApiResponse<T> = 
  | { readonly success: true; readonly data: T; readonly error?: never }
  | { readonly success: false; readonly data?: never; readonly error: string };

// Async operation result types
export type AsyncResult<T, E = Error> = Promise<
  | { readonly success: true; readonly data: T; readonly error?: never }
  | { readonly success: false; readonly data?: never; readonly error: E }
>;

// Medical data validation types
export type ValidationRule<T> = (value: T) => boolean | string;
export type ValidationSchema<T> = {
  readonly [K in keyof T]?: ValidationRule<T[K]>[];
};

// Type-safe event handlers
export type MedicalDataEventHandler<T> = (data: T, metadata: {
  readonly timestamp: UnixTimestamp;
  readonly userId: PatientId;
  readonly action: 'create' | 'read' | 'update' | 'delete';
  readonly isEmergency: boolean;
}) => void;

// Cache key generation types
export type CacheKeyTemplate<T extends string> = `medical:${T}:${string}`;
export type UserCacheKey<T extends string> = `user:${PatientId}:${T}`;
export type EmergencyCacheKey<T extends string> = `emergency:${T}:${string}`;

// Type-safe query builders
export type QueryFilter<T> = {
  readonly [K in keyof T]?: T[K] | readonly T[K][] | {
    readonly operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'in' | 'not_in';
    readonly value: T[K] | readonly T[K][];
  };
};

export type QueryOptions<T> = {
  readonly select?: readonly (keyof T)[];
  readonly where?: QueryFilter<T>;
  readonly orderBy?: {
    readonly field: keyof T;
    readonly direction: 'asc' | 'desc';
  };
  readonly limit?: PositiveNumber;
  readonly offset?: number;
  readonly includeDeleted?: boolean;
  readonly emergencyOnly?: boolean;
};

// Type-safe permission checking
export type PermissionCheck<T> = {
  readonly resource: T;
  readonly action: 'read' | 'write' | 'delete' | 'share';
  readonly context?: {
    readonly isEmergency?: boolean;
    readonly isOwner?: boolean;
    readonly hasConsent?: boolean;
  };
};

// Medical data transformation types
export type DataTransformer<TInput, TOutput> = (input: TInput) => TOutput | Promise<TOutput>;
export type MedicalDataMapper<TFrom, TTo> = (data: TFrom) => ValidatedData<TTo>;

// Type-safe audit logging
export type AuditLogEntry<T> = {
  readonly id: string;
  readonly timestamp: UnixTimestamp;
  readonly userId: PatientId;
  readonly action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'share';
  readonly resourceType: string;
  readonly resourceId: string;
  readonly changes?: {
    readonly before: Partial<T>;
    readonly after: Partial<T>;
  };
  readonly metadata: {
    readonly ipAddress?: string;
    readonly userAgent?: string;
    readonly isEmergencyAccess: boolean;
    readonly justification?: string;
  };
};

// Emergency access types
export type EmergencyAccessRequest = {
  readonly id: string;
  readonly userId: PatientId;
  readonly providerId: ProviderId;
  readonly justification: NonEmptyString<string>;
  readonly urgency: 'routine' | 'urgent' | 'critical' | 'emergency';
  readonly requestedAt: UnixTimestamp;
  readonly expiresAt: UnixTimestamp;
  readonly status: 'pending' | 'approved' | 'denied' | 'expired' | 'used';
};

// Type-safe error handling
export type MedicalDataError = {
  readonly code: string;
  readonly message: string;
  readonly field?: string;
  readonly severity: 'info' | 'warning' | 'error' | 'critical';
  readonly isRetryable: boolean;
  readonly context?: Record<string, unknown>;
};

// Performance monitoring types
export type PerformanceMetrics = {
  readonly operationType: 'read' | 'write' | 'cache' | 'validation';
  readonly duration: number;
  readonly cacheHit: boolean;
  readonly dataSize: number;
  readonly isEmergencyData: boolean;
};

// Type-safe configuration
export type MedicalServiceConfig = DeepReadonly<{
  readonly encryption: {
    readonly enabled: boolean;
    readonly algorithm: 'AES-256-GCM';
    readonly keyRotationInterval: number;
  };
  readonly caching: {
    readonly enabled: boolean;
    readonly ttl: {
      readonly emergency: number;
      readonly critical: number;
      readonly normal: number;
    };
    readonly maxSize: number;
  };
  readonly validation: {
    readonly strict: boolean;
    readonly emergencyBypass: boolean;
    readonly customRules: boolean;
  };
  readonly audit: {
    readonly enabled: boolean;
    readonly logLevel: 'minimal' | 'standard' | 'comprehensive';
    readonly retentionPeriod: number;
  };
}>;

// Conditional types for medical data access
export type ConditionalAccess<T, TCondition> = TCondition extends true ? T : never;
export type EmergencyConditionalAccess<T> = T extends { is_emergency: true } 
  ? T 
  : ConditionalAccess<T, false>;

// Type-safe hooks return types
export type UseMedicalDataReturn<T> = {
  readonly data: T | null;
  readonly loading: boolean;
  readonly error: MedicalDataError | null;
  readonly refetch: () => Promise<void>;
  readonly mutate: (newData: T) => void;
  readonly isStale: boolean;
  readonly lastUpdated: UnixTimestamp | null;
};

// Context provider types
export type MedicalDataContextValue<T> = {
  readonly state: MedicalDataState<T>;
  readonly actions: {
    readonly fetch: () => Promise<void>;
    readonly create: (data: CreateMedicalData<T>) => Promise<ApiResponse<T>>;
    readonly update: (id: MedicalId, data: UpdateMedicalData<T>) => Promise<ApiResponse<T>>;
    readonly delete: (id: MedicalId) => Promise<ApiResponse<void>>;
    readonly refresh: () => Promise<void>;
  };
  readonly emergency: {
    readonly requestAccess: (justification: string) => Promise<ApiResponse<EmergencyAccessRequest>>;
    readonly hasAccess: boolean;
    readonly accessExpiresAt: UnixTimestamp | null;
  };
};

// Type guards for runtime type checking
export type TypeGuard<T> = (value: unknown) => value is T;
export type MedicalDataTypeGuard<T> = TypeGuard<T> & {
  readonly dataType: string;
  readonly isEmergencyData: (value: T) => value is EmergencyData<T>;
  readonly isValidatedData: (value: T) => value is ValidatedData<T>;
};

// Utility functions for type creation
export const createMedicalId = (id: string): MedicalId => id as MedicalId;
export const createPatientId = (id: string): PatientId => id as PatientId;
export const createProviderId = (id: string): ProviderId => id as ProviderId;
export const createPositiveNumber = (num: number): PositiveNumber => {
  if (num <= 0) throw new Error('Number must be positive');
  return num as PositiveNumber;
};
export const createISODateString = (date: string): ISODateString => {
  if (!isValidISODate(date)) throw new Error('Invalid ISO date string');
  return date as ISODateString;
};
export const createUnixTimestamp = (timestamp: number): UnixTimestamp => {
  if (timestamp < 0) throw new Error('Timestamp must be non-negative');
  return timestamp as UnixTimestamp;
};

// Type validation helpers
export const isValidISODate = (date: string): boolean => {
  return !isNaN(Date.parse(date)) && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(date);
};

export const isPositiveNumber = (value: number): value is PositiveNumber => {
  return typeof value === 'number' && value > 0 && !isNaN(value) && isFinite(value);
};

export const isNonEmptyString = <T extends string>(value: T): value is NonEmptyString<T> => {
  return typeof value === 'string' && value.length > 0;
};

// Advanced type manipulation utilities
export type ExtractEmergencyData<T> = T extends { is_emergency: infer E }
  ? E extends true
    ? T
    : never
  : never;

export type FilterByProperty<T, K extends keyof T, V> = T extends Record<K, V> ? T : never;

export type MedicalDataUnion<T extends readonly unknown[]> = T[number];

export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

export type RequiredExcept<T, K extends keyof T> = Required<T> & Partial<Pick<T, K>>;

// Type-safe builder pattern for medical data
export type MedicalDataBuilder<T> = {
  readonly [K in keyof T]: (value: T[K]) => MedicalDataBuilder<T>;
} & {
  readonly build: () => ValidatedData<T>;
  readonly buildEmergency: () => EmergencyData<ValidatedData<T>>;
};

// Export all utility types
export type {
  DeepReadonly,
  RequiredFields,
  OptionalFields,
  CreateMedicalData,
  UpdateMedicalData,
  EmergencyAccessible,
  NonEmergencyData,
  ApiResponse,
  AsyncResult,
  ValidationRule,
  ValidationSchema,
  MedicalDataEventHandler,
  CacheKeyTemplate,
  UserCacheKey,
  EmergencyCacheKey,
  QueryFilter,
  QueryOptions,
  PermissionCheck,
  DataTransformer,
  MedicalDataMapper,
  AuditLogEntry,
  EmergencyAccessRequest,
  MedicalDataError,
  PerformanceMetrics,
  MedicalServiceConfig,
  ConditionalAccess,
  EmergencyConditionalAccess,
  UseMedicalDataReturn,
  MedicalDataContextValue,
  TypeGuard,
  MedicalDataTypeGuard
};

export default {
  // Export all utility functions and types
  createMedicalId,
  createPatientId,
  createProviderId,
  createPositiveNumber,
  createISODateString,
  createUnixTimestamp,
  isValidISODate,
  isPositiveNumber,
  isNonEmptyString
};
