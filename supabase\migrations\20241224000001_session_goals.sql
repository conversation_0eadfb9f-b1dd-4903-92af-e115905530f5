-- Session Goals Migration
-- Creates tables and functions for dynamic goal-oriented conversation flow
-- Date: 2024-12-24
-- Purpose: Enable meta-agent steering and focused consultations

-- Create session goals table
CREATE TABLE IF NOT EXISTS public.session_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    goal_type TEXT NOT NULL CHECK (goal_type IN (
        'symptom_assessment', 'diagnosis_clarification', 'treatment_planning',
        'medication_review', 'preventive_care', 'specialist_referral',
        'emergency_triage', 'follow_up_care', 'patient_education',
        'chronic_disease_management', 'mental_health_screening'
    )),
    primary_goal TEXT NOT NULL, -- Main objective of the session
    secondary_goals TEXT[] DEFAULT '{}', -- Additional objectives
    goal_status TEXT DEFAULT 'active' CHECK (goal_status IN ('active', 'completed', 'deferred', 'abandoned')),
    priority_level INTEGER DEFAULT 3 CHECK (priority_level BETWEEN 1 AND 5), -- 1=highest, 5=lowest
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage BETWEEN 0 AND 100),
    completion_criteria JSONB DEFAULT '{}', -- What needs to be achieved
    steering_notes TEXT[], -- Notes from meta-agent for conversation steering
    relevance_scores JSONB DEFAULT '{}', -- Relevance tracking for conversation turns
    estimated_duration_minutes INTEGER DEFAULT 15,
    actual_duration_minutes INTEGER,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMPTZ
);

-- Create conversation steering table for meta-agent guidance
CREATE TABLE IF NOT EXISTS public.conversation_steering (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    goal_id UUID REFERENCES public.session_goals(id) ON DELETE CASCADE,
    turn_number INTEGER NOT NULL, -- Which conversation turn this applies to
    steering_type TEXT NOT NULL CHECK (steering_type IN (
        'redirect', 'clarify', 'deepen', 'summarize', 'conclude',
        'escalate', 'refer', 'educate', 'reassure', 'investigate'
    )),
    steering_message TEXT NOT NULL, -- Guidance for the agent
    relevance_score DECIMAL(3,2) CHECK (relevance_score BETWEEN 0.00 AND 1.00),
    urgency_level TEXT DEFAULT 'medium' CHECK (urgency_level IN ('low', 'medium', 'high', 'critical')),
    applied BOOLEAN DEFAULT false, -- Whether the steering was applied
    effectiveness_score DECIMAL(3,2), -- How effective the steering was (0-1)
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create goal progress tracking table
CREATE TABLE IF NOT EXISTS public.goal_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    goal_id UUID REFERENCES public.session_goals(id) ON DELETE CASCADE,
    milestone TEXT NOT NULL, -- What was achieved
    progress_increment INTEGER NOT NULL, -- How much progress was made (0-100)
    conversation_turn INTEGER, -- Which turn achieved this progress
    agent_id TEXT, -- Which agent contributed to this progress
    confidence_score DECIMAL(3,2), -- Confidence in the progress assessment
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX idx_session_goals_session_id ON public.session_goals(session_id);
CREATE INDEX idx_session_goals_user_id ON public.session_goals(user_id);
CREATE INDEX idx_session_goals_status ON public.session_goals(goal_status);
CREATE INDEX idx_session_goals_priority ON public.session_goals(priority_level);
CREATE INDEX idx_conversation_steering_session_id ON public.conversation_steering(session_id);
CREATE INDEX idx_conversation_steering_goal_id ON public.conversation_steering(goal_id);
CREATE INDEX idx_conversation_steering_turn ON public.conversation_steering(turn_number);
CREATE INDEX idx_goal_progress_goal_id ON public.goal_progress(goal_id);

-- Enable Row Level Security
ALTER TABLE public.session_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_steering ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.goal_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies for session_goals
CREATE POLICY "Users can view their own session goals" ON public.session_goals
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own session goals" ON public.session_goals
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own session goals" ON public.session_goals
    FOR UPDATE USING (user_id = auth.uid());

-- Healthcare providers can view session goals for their sessions
CREATE POLICY "Healthcare providers can view session goals" ON public.session_goals
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.consultation_sessions cs
            JOIN public.user_profiles up ON up.id = auth.uid()
            WHERE cs.id = session_goals.session_id
            AND up.role IN ('provider', 'admin')
        )
    );

-- RLS Policies for conversation_steering
CREATE POLICY "Users can view steering for their sessions" ON public.conversation_steering
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.session_goals sg
            WHERE sg.id = conversation_steering.goal_id
            AND sg.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert steering guidance" ON public.conversation_steering
    FOR INSERT WITH CHECK (true); -- Allow system to insert steering

-- RLS Policies for goal_progress
CREATE POLICY "Users can view progress for their goals" ON public.goal_progress
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.session_goals sg
            WHERE sg.id = goal_progress.goal_id
            AND sg.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert goal progress" ON public.goal_progress
    FOR INSERT WITH CHECK (true); -- Allow system to track progress

-- Function to create session goal
CREATE OR REPLACE FUNCTION create_session_goal(
    p_session_id UUID,
    p_user_id UUID,
    p_goal_type TEXT,
    p_primary_goal TEXT,
    p_secondary_goals TEXT[] DEFAULT '{}',
    p_priority_level INTEGER DEFAULT 3,
    p_completion_criteria JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    goal_id UUID;
BEGIN
    INSERT INTO public.session_goals (
        session_id, user_id, goal_type, primary_goal, secondary_goals,
        priority_level, completion_criteria
    )
    VALUES (
        p_session_id, p_user_id, p_goal_type, p_primary_goal, p_secondary_goals,
        p_priority_level, p_completion_criteria
    )
    RETURNING id INTO goal_id;
    
    RETURN goal_id;
END;
$$;

-- Function to update goal progress
CREATE OR REPLACE FUNCTION update_goal_progress(
    p_goal_id UUID,
    p_milestone TEXT,
    p_progress_increment INTEGER,
    p_conversation_turn INTEGER DEFAULT NULL,
    p_agent_id TEXT DEFAULT NULL,
    p_confidence_score DECIMAL DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_progress INTEGER;
    new_progress INTEGER;
BEGIN
    -- Get current progress
    SELECT progress_percentage INTO current_progress
    FROM public.session_goals
    WHERE id = p_goal_id;
    
    IF current_progress IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Calculate new progress
    new_progress := LEAST(100, current_progress + p_progress_increment);
    
    -- Update goal progress
    UPDATE public.session_goals
    SET 
        progress_percentage = new_progress,
        updated_at = CURRENT_TIMESTAMP,
        completed_at = CASE WHEN new_progress = 100 THEN CURRENT_TIMESTAMP ELSE completed_at END,
        goal_status = CASE WHEN new_progress = 100 THEN 'completed' ELSE goal_status END
    WHERE id = p_goal_id;
    
    -- Insert progress record
    INSERT INTO public.goal_progress (
        goal_id, milestone, progress_increment, conversation_turn,
        agent_id, confidence_score
    )
    VALUES (
        p_goal_id, p_milestone, p_progress_increment, p_conversation_turn,
        p_agent_id, p_confidence_score
    );
    
    RETURN TRUE;
END;
$$;

-- Function to add steering guidance
CREATE OR REPLACE FUNCTION add_steering_guidance(
    p_session_id UUID,
    p_goal_id UUID,
    p_turn_number INTEGER,
    p_steering_type TEXT,
    p_steering_message TEXT,
    p_relevance_score DECIMAL DEFAULT NULL,
    p_urgency_level TEXT DEFAULT 'medium'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    steering_id UUID;
BEGIN
    INSERT INTO public.conversation_steering (
        session_id, goal_id, turn_number, steering_type, steering_message,
        relevance_score, urgency_level
    )
    VALUES (
        p_session_id, p_goal_id, p_turn_number, p_steering_type, p_steering_message,
        p_relevance_score, p_urgency_level
    )
    RETURNING id INTO steering_id;
    
    RETURN steering_id;
END;
$$;

-- Function to get active session goals
CREATE OR REPLACE FUNCTION get_active_session_goals(p_session_id UUID)
RETURNS TABLE (
    id UUID,
    goal_type TEXT,
    primary_goal TEXT,
    secondary_goals TEXT[],
    priority_level INTEGER,
    progress_percentage INTEGER,
    completion_criteria JSONB,
    steering_notes TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sg.id,
        sg.goal_type,
        sg.primary_goal,
        sg.secondary_goals,
        sg.priority_level,
        sg.progress_percentage,
        sg.completion_criteria,
        sg.steering_notes
    FROM public.session_goals sg
    WHERE sg.session_id = p_session_id
    AND sg.goal_status = 'active'
    ORDER BY sg.priority_level ASC, sg.created_at ASC;
END;
$$;

-- Function to get steering guidance for current turn
CREATE OR REPLACE FUNCTION get_steering_guidance(
    p_session_id UUID,
    p_turn_number INTEGER
)
RETURNS TABLE (
    steering_type TEXT,
    steering_message TEXT,
    relevance_score DECIMAL,
    urgency_level TEXT,
    goal_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cs.steering_type,
        cs.steering_message,
        cs.relevance_score,
        cs.urgency_level,
        cs.goal_id
    FROM public.conversation_steering cs
    WHERE cs.session_id = p_session_id
    AND cs.turn_number = p_turn_number
    AND cs.applied = false
    ORDER BY cs.urgency_level DESC, cs.relevance_score DESC;
END;
$$;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_session_goals_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

CREATE TRIGGER trigger_update_session_goals_updated_at
    BEFORE UPDATE ON public.session_goals
    FOR EACH ROW
    EXECUTE FUNCTION update_session_goals_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.session_goals IS 'Goals and objectives for consultation sessions';
COMMENT ON TABLE public.conversation_steering IS 'Meta-agent guidance for conversation steering';
COMMENT ON TABLE public.goal_progress IS 'Progress tracking for session goals';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.session_goals TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.conversation_steering TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.goal_progress TO authenticated;
GRANT EXECUTE ON FUNCTION create_session_goal(UUID, UUID, TEXT, TEXT, TEXT[], INTEGER, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION update_goal_progress(UUID, TEXT, INTEGER, INTEGER, TEXT, DECIMAL) TO authenticated;
GRANT EXECUTE ON FUNCTION add_steering_guidance(UUID, UUID, INTEGER, TEXT, TEXT, DECIMAL, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_active_session_goals(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_steering_guidance(UUID, INTEGER) TO authenticated;
