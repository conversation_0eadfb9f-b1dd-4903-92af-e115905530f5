import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const AgentCollaborationPanel = ({ 
  activeAgents = [],
  collaborationPhase = 'initializing',
  onAgentSelect = () => {},
  selectedAgent = null,
  className = ''
}) => {
  const [connectionLines, setConnectionLines] = useState([]);
  const [activeConnections, setActiveConnections] = useState([]);

  useEffect(() => {
    // Simulate dynamic connections between agents
    const interval = setInterval(() => {
      if (activeAgents.length > 1) {
        const randomConnections = [];
        for (let i = 0; i < activeAgents.length - 1; i++) {
          if (Math.random() > 0.6) {
            randomConnections.push({
              from: activeAgents[i].id,
              to: activeAgents[i + 1].id,
              type: 'data-sharing'
            });
          }
        }
        setActiveConnections(randomConnections);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [activeAgents]);

  const getAgentStatusColor = (agent) => {
    switch (agent.status) {
      case 'speaking':
        return 'primary';
      case 'listening':
        return 'success';
      case 'analyzing':
        return 'warning';
      case 'waiting':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const getPhaseDescription = () => {
    switch (collaborationPhase) {
      case 'initializing':
        return 'Agents connecting and sharing patient context';
      case 'assessment':
        return 'Initial assessment and symptom analysis';
      case 'collaboration':
        return 'Specialists discussing findings and recommendations';
      case 'synthesis':
        return 'Formulating comprehensive treatment plan';
      case 'completed':
        return 'Collaboration complete - recommendations ready';
      default:
        return 'Multi-agent consultation in progress';
    }
  };

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-text-primary font-heading">
            Agent Collaboration
          </h2>
          <p className="text-sm text-text-secondary font-caption mt-1">
            {getPhaseDescription()}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full animate-pulse ${
            collaborationPhase === 'collaboration' ? 'bg-success-500' :
            collaborationPhase === 'synthesis'? 'bg-warning-500' : 'bg-primary-500'
          }`}></div>
          <span className="text-sm font-medium text-text-primary capitalize">
            {collaborationPhase}
          </span>
        </div>
      </div>

      {/* Agent Network Visualization */}
      <div className="relative mb-6">
        <div className="flex items-center justify-center space-x-8 lg:space-x-12">
          {activeAgents.map((agent, index) => (
            <div key={agent.id} className="relative">
              {/* Agent Avatar */}
              <button
                onClick={() => onAgentSelect(agent)}
                className={`relative w-16 h-16 lg:w-20 lg:h-20 rounded-full border-4 transition-all duration-300 ${
                  selectedAgent?.id === agent.id 
                    ? 'border-primary-500 shadow-lg scale-110' 
                    : `border-${getAgentStatusColor(agent)}-300`
                } ${
                  agent.status === 'speaking' ? 'animate-consultation-pulse' : ''
                }`}
              >
                <div className={`w-full h-full rounded-full flex items-center justify-center ${
                  `bg-${getAgentStatusColor(agent)}-50`
                }`}>
                  <Icon 
                    name={
                      agent.specialty === 'General Practitioner' ? 'Stethoscope' :
                      agent.specialty === 'Cardiologist' ? 'Heart' :
                      agent.specialty === 'Nutritionist'? 'Apple' : 'User'
                    }
                    size={24}
                    color={`var(--color-${getAgentStatusColor(agent)})`}
                  />
                </div>
                
                {/* Status Indicator */}
                <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-surface flex items-center justify-center ${
                  `bg-${getAgentStatusColor(agent)}-500`
                }`}>
                  <Icon 
                    name={
                      agent.status === 'speaking' ? 'Mic' :
                      agent.status === 'listening' ? 'Ear' :
                      agent.status === 'analyzing'? 'Brain' : 'Clock'
                    }
                    size={12}
                    color="white"
                  />
                </div>
              </button>

              {/* Agent Info */}
              <div className="text-center mt-3">
                <div className="font-medium text-text-primary text-sm">
                  {agent.name}
                </div>
                <div className="text-xs text-text-secondary">
                  {agent.specialty}
                </div>
                <div className={`text-xs font-medium mt-1 ${
                  `text-${getAgentStatusColor(agent)}-600`
                }`}>
                  {agent.status}
                </div>
              </div>

              {/* Connection Lines */}
              {activeConnections.some(conn => conn.from === agent.id || conn.to === agent.id) && (
                <div className="absolute top-8 left-full w-8 h-0.5 bg-primary-300 animate-pulse hidden lg:block">
                  <div className="absolute right-0 top-0 w-2 h-2 bg-primary-500 rounded-full transform -translate-y-0.5 animate-ping"></div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Data Flow Indicators */}
        <div className="absolute inset-0 pointer-events-none">
          {activeConnections.map((connection, index) => (
            <div key={index} className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="flex items-center space-x-2 bg-primary-50 px-3 py-1 rounded-full border border-primary-200">
                <Icon name="ArrowRight" size={12} color="var(--color-primary)" />
                <span className="text-xs text-primary-600 font-medium">
                  Data Sharing
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Collaboration Metrics */}
      <div className="grid grid-cols-3 gap-4 p-4 bg-secondary-50 rounded-lg">
        <div className="text-center">
          <div className="text-lg font-semibold text-text-primary font-data">
            {activeAgents.filter(a => a.status === 'speaking' || a.status === 'listening').length}
          </div>
          <div className="text-xs text-text-secondary">Active</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-text-primary font-data">
            {activeConnections.length}
          </div>
          <div className="text-xs text-text-secondary">Connections</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-text-primary font-data">
            {Math.round(Math.random() * 30 + 70)}%
          </div>
          <div className="text-xs text-text-secondary">Consensus</div>
        </div>
      </div>

      {/* Quick Agent Actions */}
      <div className="mt-4 flex flex-wrap gap-2">
        {activeAgents.map((agent) => (
          <button
            key={agent.id}
            onClick={() => onAgentSelect(agent)}
            className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-fast ${
              selectedAgent?.id === agent.id
                ? 'bg-primary-500 text-primary-foreground'
                : 'bg-secondary-100 text-text-secondary hover:bg-secondary-200'
            }`}
          >
            Focus on {agent.name}
          </button>
        ))}
      </div>
    </div>
  );
};

export default AgentCollaborationPanel;