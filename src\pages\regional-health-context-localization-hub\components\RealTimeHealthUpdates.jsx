import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RealTimeHealthUpdates = ({ country, onUpdateSync, syncStatus }) => {
  const [lastSyncTime, setLastSyncTime] = useState(new Date());
  const [activeAlerts, setActiveAlerts] = useState([]);
  const [dataFeeds, setDataFeeds] = useState({});

  // Mock real-time health data
  const healthDataSources = {
    who: {
      name: 'World Health Organization',
      status: 'active',
      lastUpdate: '2024-01-20 14:30:00',
      reliability: 95,
      dataTypes: ['Disease outbreaks', 'Vaccination schedules', 'Health advisories']
    },
    nationalMinistry: {
      name: `${country?.name} Ministry of Health`,
      status: 'active',
      lastUpdate: '2024-01-20 16:45:00',
      reliability: 88,
      dataTypes: ['Local disease surveillance', 'Healthcare policies', 'Emergency protocols']
    },
    cdc: {
      name: 'Centers for Disease Control',
      status: 'active',
      lastUpdate: '2024-01-20 12:15:00',
      reliability: 92,
      dataTypes: ['Global health alerts', 'Travel advisories', 'Epidemic monitoring']
    },
    localSurveillance: {
      name: 'Local Health Surveillance',
      status: 'intermittent',
      lastUpdate: '2024-01-19 18:20:00',
      reliability: 75,
      dataTypes: ['Community health reports', 'Local outbreak data', 'Healthcare capacity']
    }
  };

  const currentHealthAlerts = [
    {
      id: 1,
      severity: 'high',
      title: 'Seasonal Influenza Increase',
      description: `Increased influenza cases reported across ${country?.name}. Healthcare facilities should prepare for higher patient volumes.`,
      source: 'National Ministry of Health',
      timestamp: '2024-01-20 15:30:00',
      region: 'Nationwide',
      actionRequired: true,
      affectedPopulation: 'All age groups, especially elderly and immunocompromised'
    },
    {
      id: 2,
      severity: 'medium',
      title: 'Medicine Supply Update',
      description: 'Temporary shortage of specific antibiotics in northern regions. Alternative treatments recommended.',
      source: 'Pharmaceutical Board',
      timestamp: '2024-01-20 10:15:00',
      region: 'Northern regions',
      actionRequired: false,
      affectedPopulation: 'Patients requiring antibiotic treatment'
    },
    {
      id: 3,
      severity: 'low',
      title: 'Vaccination Campaign Progress',
      description: 'National vaccination campaign shows 78% coverage. Continue promoting vaccination in underserved areas.',
      source: 'WHO Regional Office',
      timestamp: '2024-01-19 14:20:00',
      region: 'Nationwide',
      actionRequired: false,
      affectedPopulation: 'Children under 5 years'
    }
  ];

  const diseaseOutbreakData = {
    ghana: [
      { disease: 'Cholera', cases: 45, trend: 'decreasing', region: 'Greater Accra', lastUpdate: '2024-01-20' },
      { disease: 'Meningitis', cases: 12, trend: 'stable', region: 'Northern Region', lastUpdate: '2024-01-19' },
      { disease: 'Yellow Fever', cases: 3, trend: 'stable', region: 'Ashanti Region', lastUpdate: '2024-01-18' }
    ],
    nigeria: [
      { disease: 'Lassa Fever', cases: 78, trend: 'increasing', region: 'Edo State', lastUpdate: '2024-01-20' },
      { disease: 'Cholera', cases: 156, trend: 'decreasing', region: 'Kano State', lastUpdate: '2024-01-20' },
      { disease: 'Meningitis', cases: 89, trend: 'stable', region: 'Sokoto State', lastUpdate: '2024-01-19' }
    ],
    kenya: [
      { disease: 'Rift Valley Fever', cases: 23, trend: 'increasing', region: 'Rift Valley', lastUpdate: '2024-01-20' },
      { disease: 'Cholera', cases: 67, trend: 'stable', region: 'Nairobi County', lastUpdate: '2024-01-19' },
      { disease: 'Dengue Fever', cases: 34, trend: 'decreasing', region: 'Mombasa County', lastUpdate: '2024-01-18' }
    ],
    'south-africa': [
      { disease: 'Listeriosis', cases: 34, trend: 'stable', region: 'Gauteng Province', lastUpdate: '2024-01-20' },
      { disease: 'Tuberculosis', cases: 1245, trend: 'decreasing', region: 'Western Cape', lastUpdate: '2024-01-20' },
      { disease: 'Rabies', cases: 8, trend: 'stable', region: 'KwaZulu-Natal', lastUpdate: '2024-01-19' }
    ]
  };

  const healthSystemMetrics = {
    hospitalCapacity: {
      occupied: 72,
      available: 28,
      trend: 'stable',
      criticalCare: 45
    },
    medicineAvailability: {
      essential: 85,
      specialized: 67,
      emergency: 92,
      trend: 'improving'
    },
    healthWorkerAvailability: {
      doctors: 78,
      nurses: 82,
      specialists: 54,
      trend: 'stable'
    }
  };

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      if (Math.random() > 0.7) {
        // Simulate new alert
        const newAlert = {
          id: Date.now(),
          severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
          title: 'New Health Update',
          description: 'Simulated real-time health update for demonstration',
          source: 'Automated System',
          timestamp: new Date().toISOString(),
          region: 'Various',
          actionRequired: Math.random() > 0.5
        };
        setActiveAlerts(prev => [newAlert, ...prev.slice(0, 4)]);
      }
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleSyncNow = () => {
    onUpdateSync?.();
    setLastSyncTime(new Date());
  };

  const dismissAlert = (alertId) => {
    setActiveAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'text-secondary';
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'increasing': return 'TrendingUp';
      case 'decreasing': return 'TrendingDown';
      case 'stable': return 'Minus';
      default: return 'Activity';
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'increasing': return 'text-error-500';
      case 'decreasing': return 'text-success-500';
      case 'stable': return 'text-info-500';
      default: return 'text-text-secondary';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Sync Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-2xl">{country?.flag}</span>
          <div>
            <h2 className="text-xl font-semibold text-text-primary font-heading">
              Real-time Health Updates - {country?.name}
            </h2>
            <p className="text-text-secondary">
              Live health data synchronization from WHO, national health ministries, and local surveillance systems
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm text-text-secondary">Last Sync</p>
            <p className="font-medium text-text-primary">
              {lastSyncTime.toLocaleTimeString()}
            </p>
          </div>
          
          <Button
            variant="primary"
            onClick={handleSyncNow}
            disabled={syncStatus === 'syncing'}
            iconName={syncStatus === 'syncing' ? 'Loader' : 'RefreshCw'}
          >
            {syncStatus === 'syncing' ? 'Syncing...' : 'Sync Now'}
          </Button>
        </div>
      </div>

      {/* Data Sources Status */}
      <div>
        <h3 className="text-lg font-medium text-text-primary mb-4">Data Sources Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(healthDataSources).map(([key, source]) => (
            <div key={key} className="p-4 bg-surface border border-border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-text-primary">{source.name}</h4>
                <div className={`w-3 h-3 rounded-full ${
                  source.status === 'active' ? 'bg-success-500' :
                  source.status === 'intermittent' ? 'bg-warning-500' : 'bg-error-500'
                }`}></div>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-text-secondary">Reliability</span>
                  <span className="font-medium text-text-primary">{source.reliability}%</span>
                </div>
                <div className="w-full bg-border rounded-full h-2">
                  <div 
                    className="bg-primary-500 h-2 rounded-full transition-all"
                    style={{ width: `${source.reliability}%` }}
                  ></div>
                </div>
                <p className="text-text-secondary">
                  Last: {new Date(source.lastUpdate).toLocaleTimeString()}
                </p>
              </div>
              
              <div className="mt-3">
                <p className="text-xs text-text-secondary font-medium mb-1">Data Types:</p>
                <div className="flex flex-wrap gap-1">
                  {source.dataTypes.slice(0, 2).map((type, idx) => (
                    <span key={idx} className="text-xs bg-primary-100 text-primary-700 px-1 py-0.5 rounded">
                      {type}
                    </span>
                  ))}
                  {source.dataTypes.length > 2 && (
                    <span className="text-xs text-text-secondary">+{source.dataTypes.length - 2}</span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Active Health Alerts */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-text-primary">Active Health Alerts</h3>
          <span className="text-sm text-text-secondary">
            {activeAlerts.length || currentHealthAlerts.length} active alerts
          </span>
        </div>
        
        <div className="space-y-3">
          {(activeAlerts.length > 0 ? activeAlerts : currentHealthAlerts).map((alert) => (
            <div key={alert.id} className={`p-4 border-l-4 bg-surface border border-border rounded-lg border-l-${getSeverityColor(alert.severity)}-500`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`text-xs px-2 py-1 rounded-full bg-${getSeverityColor(alert.severity)}-100 text-${getSeverityColor(alert.severity)}-700 font-medium`}>
                      {alert.severity.toUpperCase()}
                    </span>
                    {alert.actionRequired && (
                      <span className="text-xs bg-warning-100 text-warning-700 px-2 py-1 rounded-full">
                        Action Required
                      </span>
                    )}
                    <span className="text-xs text-text-secondary">{alert.region}</span>
                  </div>
                  
                  <h4 className="font-medium text-text-primary mb-1">{alert.title}</h4>
                  <p className="text-sm text-text-secondary mb-2">{alert.description}</p>
                  
                  <div className="flex items-center gap-4 text-xs text-text-secondary">
                    <span>Source: {alert.source}</span>
                    <span>Time: {new Date(alert.timestamp).toLocaleString()}</span>
                    {alert.affectedPopulation && (
                      <span>Affected: {alert.affectedPopulation}</span>
                    )}
                  </div>
                </div>
                
                <button
                  onClick={() => dismissAlert(alert.id)}
                  className="p-1 text-text-secondary hover:text-text-primary"
                >
                  <Icon name="X" size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Disease Outbreak Monitoring */}
      <div>
        <h3 className="text-lg font-medium text-text-primary mb-4">Disease Outbreak Monitoring</h3>
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {(diseaseOutbreakData[country?.id] || []).map((outbreak, index) => (
              <div key={index} className="p-4 border border-border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-text-primary">{outbreak.disease}</h4>
                  <Icon 
                    name={getTrendIcon(outbreak.trend)} 
                    size={16} 
                    className={getTrendColor(outbreak.trend)}
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Cases</span>
                    <span className="font-semibold text-text-primary">{outbreak.cases}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Trend</span>
                    <span className={`text-sm font-medium capitalize ${getTrendColor(outbreak.trend)}`}>
                      {outbreak.trend}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Region</span>
                    <span className="text-sm text-text-primary">{outbreak.region}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Updated</span>
                    <span className="text-sm text-text-primary">{outbreak.lastUpdate}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Health System Capacity */}
      <div>
        <h3 className="text-lg font-medium text-text-primary mb-4">Health System Capacity Monitoring</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            {
              title: 'Hospital Capacity',
              data: healthSystemMetrics.hospitalCapacity,
              icon: 'Building',
              color: 'primary'
            },
            {
              title: 'Medicine Availability',
              data: healthSystemMetrics.medicineAvailability,
              icon: 'Pill',
              color: 'success'
            },
            {
              title: 'Health Worker Availability',
              data: healthSystemMetrics.healthWorkerAvailability,
              icon: 'Users',
              color: 'info'
            }
          ].map((metric, index) => (
            <div key={index} className="p-4 bg-surface border border-border rounded-lg">
              <div className="flex items-center gap-2 mb-4">
                <Icon name={metric.icon} size={20} className={`text-${metric.color}-500`} />
                <h4 className="font-medium text-text-primary">{metric.title}</h4>
              </div>
              
              <div className="space-y-3">
                {Object.entries(metric.data).filter(([key]) => key !== 'trend').map(([key, value]) => (
                  <div key={key}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-text-secondary capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <span className="font-medium text-text-primary">{value}%</span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div 
                        className={`bg-${metric.color}-500 h-2 rounded-full transition-all`}
                        style={{ width: `${value}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
                
                <div className="flex items-center gap-1 text-sm pt-2 border-t border-border">
                  <Icon 
                    name={getTrendIcon(metric.data.trend)} 
                    size={14} 
                    className={getTrendColor(metric.data.trend)}
                  />
                  <span className={`capitalize ${getTrendColor(metric.data.trend)}`}>
                    {metric.data.trend}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Vaccination Coverage Update */}
      <div>
        <h3 className="text-lg font-medium text-text-primary mb-4">Vaccination Coverage Tracking</h3>
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { vaccine: 'COVID-19', coverage: 78, target: 85, population: 'Adults 18+' },
              { vaccine: 'Influenza', coverage: 45, target: 60, population: 'High-risk groups' },
              { vaccine: 'Measles', coverage: 92, target: 95, population: 'Children <5' },
              { vaccine: 'Yellow Fever', coverage: 84, target: 90, population: 'All ages' }
            ].map((vaccine, index) => (
              <div key={index} className="text-center">
                <h5 className="font-medium text-text-primary mb-2">{vaccine.vaccine}</h5>
                <div className="relative w-20 h-20 mx-auto mb-2">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 100 100">
                    <circle
                      cx="50"
                      cy="50"
                      r="40"
                      stroke="var(--border)"
                      strokeWidth="10"
                      fill="transparent"
                    />
                    <circle
                      cx="50"
                      cy="50"
                      r="40"
                      stroke="var(--primary-500)"
                      strokeWidth="10"
                      fill="transparent"
                      strokeDasharray={`${(vaccine.coverage / 100) * 251.2} 251.2`}
                      className="transition-all duration-300"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-lg font-semibold text-text-primary">{vaccine.coverage}%</span>
                  </div>
                </div>
                <p className="text-xs text-text-secondary mb-1">Target: {vaccine.target}%</p>
                <p className="text-xs text-text-secondary">{vaccine.population}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Emergency Preparedness Status */}
      <div>
        <h3 className="text-lg font-medium text-text-primary mb-4">Emergency Preparedness Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            {
              category: 'Emergency Response Teams',
              status: 'Ready',
              details: '24/7 rapid response teams deployed in major cities',
              level: 'High'
            },
            {
              category: 'Medical Supply Reserves',
              status: 'Adequate',
              details: '3-month emergency medical supply reserves maintained',
              level: 'Medium'
            },
            {
              category: 'Disease Surveillance',
              status: 'Active',
              details: 'Real-time disease surveillance systems monitoring key indicators',
              level: 'High'
            },
            {
              category: 'Public Health Communication',
              status: 'Operational',
              details: 'Multi-channel public health messaging system ready for activation',
              level: 'Medium'
            }
          ].map((prep, index) => (
            <div key={index} className="p-4 bg-surface border border-border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-text-primary">{prep.category}</h4>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  prep.level === 'High' ? 'bg-success-100 text-success-700' :
                  prep.level === 'Medium'? 'bg-warning-100 text-warning-700' : 'bg-info-100 text-info-700'
                }`}>
                  {prep.level} Readiness
                </span>
              </div>
              <p className="text-sm text-success-600 font-medium mb-1">{prep.status}</p>
              <p className="text-sm text-text-secondary">{prep.details}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RealTimeHealthUpdates;