/**
 * MEMORY SYSTEM INTEGRATION TEST
 * 
 * Tests the new persistent memory system to ensure it works correctly
 * and replaces the broken in-memory Map system.
 */

import { MemoryManager } from '../services/MemoryManager';
import { aiOrchestrator } from '../services/aiOrchestrator';

describe('Memory System Integration Tests', () => {
  let memoryManager: MemoryManager;
  const testSessionId = `test-session-${Date.now()}`;

  beforeAll(() => {
    memoryManager = new MemoryManager();
  });

  afterAll(async () => {
    // Clean up test data
    await memoryManager.clearConversationMemory(testSessionId);
  });

  describe('MemoryManager', () => {
    test('should initialize without errors', () => {
      expect(memoryManager).toBeDefined();
    });

    test('should pass health check', async () => {
      const health = await memoryManager.healthCheck();
      expect(health.healthy).toBe(true);
    });

    test('should save and retrieve messages', async () => {
      // Save a test message
      const success = await memoryManager.saveMessage(
        testSessionId,
        'user',
        'test-user-id',
        'Test User',
        'Hello, this is a test message',
        1,
        { test: true }
      );

      expect(success).toBe(true);

      // Retrieve conversation history
      const messages = await memoryManager.getConversationHistory(testSessionId);
      expect(messages).toHaveLength(1);
      expect(messages[0].content).toBe('Hello, this is a test message');
      expect(messages[0].speaker_name).toBe('Test User');
      expect(messages[0].sequence_number).toBe(1);
    });

    test('should get conversation context', async () => {
      const context = await memoryManager.getConversationContext(testSessionId);
      expect(context).toBeDefined();
      expect(context?.session_id).toBe(testSessionId);
      expect(context?.messages).toHaveLength(1);
      expect(context?.total_messages).toBe(1);
    });

    test('should get conversation summary', async () => {
      const summary = await memoryManager.getConversationSummary(testSessionId, 5);
      expect(summary).toHaveLength(1);
      expect(summary[0].content).toBe('Hello, this is a test message');
    });
  });

  describe('AIOrchestrator Integration', () => {
    test('should initialize successfully', async () => {
      await expect(aiOrchestrator.initialize()).resolves.not.toThrow();
    });

    test('should have persistent memory manager', async () => {
      const context = await aiOrchestrator.getConversationMemory(testSessionId);
      expect(context).toBeDefined();
    });

    test('should clear conversation memory', async () => {
      const cleared = await aiOrchestrator.clearConversationMemory(testSessionId);
      expect(cleared).toBe(true);

      // Verify it's actually cleared
      const context = await aiOrchestrator.getConversationMemory(testSessionId);
      expect(context?.messages).toHaveLength(0);
    });
  });

  describe('Performance Tests', () => {
    test('should handle multiple messages efficiently', async () => {
      const startTime = Date.now();
      const testSession = `perf-test-${Date.now()}`;

      // Save 10 messages
      for (let i = 1; i <= 10; i++) {
        await memoryManager.saveMessage(
          testSession,
          i % 2 === 0 ? 'agent' : 'user',
          `speaker-${i}`,
          `Speaker ${i}`,
          `Message ${i} content`,
          i
        );
      }

      // Retrieve all messages
      const messages = await memoryManager.getConversationHistory(testSession);
      const endTime = Date.now();

      expect(messages).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete in under 5 seconds

      // Clean up
      await memoryManager.clearConversationMemory(testSession);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid session IDs gracefully', async () => {
      const messages = await memoryManager.getConversationHistory('invalid-session-id');
      expect(messages).toEqual([]);
    });

    test('should handle save failures gracefully', async () => {
      // Try to save with invalid data
      const success = await memoryManager.saveMessage(
        '', // empty session ID
        'user',
        'test-user',
        'Test User',
        'Test message',
        1
      );

      expect(success).toBe(false);
    });
  });
});

/**
 * Manual test function for development
 * Run this to verify the memory system works in development
 */
export async function testMemorySystemManually() {
  console.log('🧪 Testing Memory System...');
  
  try {
    const memoryManager = new MemoryManager();
    const testSession = `manual-test-${Date.now()}`;
    
    // Test health check
    console.log('1. Testing health check...');
    const health = await memoryManager.healthCheck();
    console.log('Health status:', health);
    
    // Test saving messages
    console.log('2. Testing message saving...');
    await memoryManager.saveMessage(
      testSession,
      'user',
      'test-user',
      'Test User',
      'Hello, testing the memory system!',
      1
    );
    
    await memoryManager.saveMessage(
      testSession,
      'agent',
      'test-agent',
      'Test Agent',
      'Hello! The memory system is working correctly.',
      2
    );
    
    // Test retrieving messages
    console.log('3. Testing message retrieval...');
    const messages = await memoryManager.getConversationHistory(testSession);
    console.log('Retrieved messages:', messages.length);
    
    // Test conversation context
    console.log('4. Testing conversation context...');
    const context = await memoryManager.getConversationContext(testSession);
    console.log('Context:', {
      sessionId: context?.session_id,
      messageCount: context?.total_messages,
      activeAgents: context?.active_agents
    });
    
    // Clean up
    console.log('5. Cleaning up...');
    await memoryManager.clearConversationMemory(testSession);
    
    console.log('✅ Memory system test completed successfully!');
    
  } catch (error) {
    console.error('❌ Memory system test failed:', error);
  }
}
