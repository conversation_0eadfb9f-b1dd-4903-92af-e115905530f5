import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const TriageAlgorithmConfiguration = ({ systemConfig, onConfigUpdate, userProfile }) => {
  const [config, setConfig] = useState({
    redFlagThreshold: 8,
    autoEscalationEnabled: true,
    culturalAdaptation: true,
    regionalSettings: 'ghana',
    symptomWeights: {
      chest_pain: 9,
      difficulty_breathing: 9,
      severe_headache: 8,
      high_fever: 7,
      persistent_cough: 6,
      loss_of_consciousness: 10,
      severe_bleeding: 10
    },
    escalationPathways: {
      critical: { timeLimit: 15, autoCall: true },
      urgent: { timeLimit: 60, autoCall: false },
      semi_urgent: { timeLimit: 240, autoCall: false }
    },
    regionalDiseaseWeights: {
      malaria: 1.2,
      tuberculosis: 1.1,
      typhoid: 1.1,
      hepatitis_b: 1.0
    },
    ...systemConfig
  });

  const [hasChanges, setHasChanges] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');

  const countries = [
    { code: 'ghana', name: 'Ghana', diseases: ['malaria', 'typhoid', 'hepatitis_b', 'tuberculosis'] },
    { code: 'nigeria', name: 'Nigeria', diseases: ['malaria', 'tuberculosis', 'lassa_fever', 'typhoid'] },
    { code: 'kenya', name: 'Kenya', diseases: ['malaria', 'tuberculosis', 'dengue', 'typhoid'] },
    { code: 'south_africa', name: 'South Africa', diseases: ['hiv', 'tuberculosis', 'hypertension', 'diabetes'] }
  ];

  const urgencyLevels = [
    { key: 'critical', name: 'Critical', color: 'red', description: 'Life-threatening conditions' },
    { key: 'urgent', name: 'Urgent', color: 'orange', description: 'Serious conditions requiring prompt care' },
    { key: 'semi_urgent', name: 'Semi-Urgent', color: 'yellow', description: 'Conditions that can wait a few hours' },
    { key: 'routine', name: 'Routine', color: 'green', description: 'Non-urgent conditions' }
  ];

  useEffect(() => {
    setConfig(prev => ({ ...prev, ...systemConfig }));
  }, [systemConfig]);

  const handleConfigChange = (section, key, value) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleDirectConfigChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleSaveConfig = () => {
    onConfigUpdate(config);
    localStorage.setItem('triageSystemConfig', JSON.stringify(config));
    setHasChanges(false);
    setSaveStatus('saved');
    setTimeout(() => setSaveStatus(''), 2000);
  };

  const handleResetConfig = () => {
    const defaultConfig = {
      redFlagThreshold: 8,
      autoEscalationEnabled: true,
      culturalAdaptation: true,
      regionalSettings: userProfile?.country || 'ghana',
      symptomWeights: {
        chest_pain: 9,
        difficulty_breathing: 9,
        severe_headache: 8,
        high_fever: 7,
        persistent_cough: 6,
        loss_of_consciousness: 10,
        severe_bleeding: 10
      },
      escalationPathways: {
        critical: { timeLimit: 15, autoCall: true },
        urgent: { timeLimit: 60, autoCall: false },
        semi_urgent: { timeLimit: 240, autoCall: false }
      },
      regionalDiseaseWeights: {
        malaria: 1.2,
        tuberculosis: 1.1,
        typhoid: 1.1,
        hepatitis_b: 1.0
      }
    };
    setConfig(defaultConfig);
    setHasChanges(true);
  };

  const selectedCountry = countries.find(c => c.code === config.regionalSettings) || countries[0];

  return (
    <div className="space-y-6">
      
      {/* Configuration Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">
            Triage Algorithm Configuration
          </h3>
          <p className="text-text-secondary">
            Customize symptom weights, red flag thresholds, and escalation pathways
          </p>
        </div>
        
        <div className="flex space-x-3">
          {hasChanges && (
            <Button
              variant="outline"
              onClick={handleResetConfig}
              iconName="RotateCcw"
              iconPosition="left"
              size="sm"
            >
              Reset
            </Button>
          )}
          <Button
            onClick={handleSaveConfig}
            disabled={!hasChanges}
            iconName={saveStatus === 'saved' ? 'Check' : 'Save'}
            iconPosition="left"
            size="sm"
            variant={saveStatus === 'saved' ? 'success' : 'primary'}
          >
            {saveStatus === 'saved' ? 'Saved' : 'Save Changes'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* General Settings */}
        <div className="bg-surface rounded-lg border border-border p-6">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="Settings" size={18} className="mr-2" />
            General Settings
          </h4>
          
          <div className="space-y-4">
            
            {/* Red Flag Threshold */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Red Flag Threshold (1-10)
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={config.redFlagThreshold}
                  onChange={(e) => handleDirectConfigChange('redFlagThreshold', parseInt(e.target.value))}
                  className="flex-1"
                />
                <span className="text-lg font-semibold text-text-primary w-8">
                  {config.redFlagThreshold}
                </span>
              </div>
              <p className="text-xs text-text-secondary mt-1">
                Symptoms scoring above this threshold trigger red flag alerts
              </p>
            </div>

            {/* Auto-escalation Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-text-secondary">
                  Auto-escalation
                </label>
                <p className="text-xs text-text-secondary">
                  Automatically trigger emergency protocols for critical cases
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.autoEscalationEnabled}
                  onChange={(e) => handleDirectConfigChange('autoEscalationEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* Cultural Adaptation Toggle */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-text-secondary">
                  Cultural Adaptation
                </label>
                <p className="text-xs text-text-secondary">
                  Include regional health patterns and cultural considerations
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={config.culturalAdaptation}
                  onChange={(e) => handleDirectConfigChange('culturalAdaptation', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* Regional Settings */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Regional Settings
              </label>
              <select
                value={config.regionalSettings}
                onChange={(e) => handleDirectConfigChange('regionalSettings', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md bg-background"
              >
                {countries.map(country => (
                  <option key={country.code} value={country.code}>
                    {country.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-text-secondary mt-1">
                Common diseases: {selectedCountry.diseases.join(', ')}
              </p>
            </div>
          </div>
        </div>

        {/* Escalation Pathways */}
        <div className="bg-surface rounded-lg border border-border p-6">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="AlertTriangle" size={18} className="mr-2" />
            Escalation Pathways
          </h4>
          
          <div className="space-y-4">
            {urgencyLevels.slice(0, 3).map(level => (
              <div key={level.key} className="border border-border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full bg-${level.color}-500 mr-3`} />
                    <div>
                      <span className="font-medium text-text-primary">{level.name}</span>
                      <p className="text-xs text-text-secondary">{level.description}</p>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-text-secondary mb-1">
                      Time Limit (minutes)
                    </label>
                    <input
                      type="number"
                      value={config.escalationPathways?.[level.key]?.timeLimit || 0}
                      onChange={(e) => handleConfigChange('escalationPathways', level.key, {
                        ...config.escalationPathways?.[level.key],
                        timeLimit: parseInt(e.target.value)
                      })}
                      className="w-full px-2 py-1 text-sm border border-border rounded bg-background"
                    />
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`autoCall-${level.key}`}
                      checked={config.escalationPathways?.[level.key]?.autoCall || false}
                      onChange={(e) => handleConfigChange('escalationPathways', level.key, {
                        ...config.escalationPathways?.[level.key],
                        autoCall: e.target.checked
                      })}
                      className="mr-2"
                    />
                    <label htmlFor={`autoCall-${level.key}`} className="text-xs text-text-secondary">
                      Auto-call
                    </label>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

      </div>

      {/* Symptom Weights Configuration */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h4 className="font-semibold text-text-primary mb-4 flex items-center">
          <Icon name="BarChart3" size={18} className="mr-2" />
          Symptom Weights Configuration
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(config.symptomWeights || {}).map(([symptom, weight]) => (
            <div key={symptom} className="border border-border rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary capitalize">
                  {symptom.replace('_', ' ')}
                </span>
                <span className="text-sm font-bold text-text-primary">
                  {weight}/10
                </span>
              </div>
              
              <input
                type="range"
                min="1"
                max="10"
                value={weight}
                onChange={(e) => handleConfigChange('symptomWeights', symptom, parseInt(e.target.value))}
                className="w-full"
              />
              
              <div className="flex justify-between text-xs text-text-secondary mt-1">
                <span>Low</span>
                <span>High</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Regional Disease Weights */}
      {config.culturalAdaptation && (
        <div className="bg-surface rounded-lg border border-border p-6">
          <h4 className="font-semibold text-text-primary mb-4 flex items-center">
            <Icon name="Globe" size={18} className="mr-2" />
            Regional Disease Weights - {selectedCountry.name}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {selectedCountry.diseases.map(disease => (
              <div key={disease} className="border border-border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-text-primary capitalize">
                    {disease.replace('_', ' ')}
                  </span>
                  <span className="text-sm font-bold text-text-primary">
                    {config.regionalDiseaseWeights?.[disease] || 1.0}x
                  </span>
                </div>
                
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={config.regionalDiseaseWeights?.[disease] || 1.0}
                  onChange={(e) => handleConfigChange('regionalDiseaseWeights', disease, parseFloat(e.target.value))}
                  className="w-full"
                />
                
                <div className="flex justify-between text-xs text-text-secondary mt-1">
                  <span>0.5x</span>
                  <span>2.0x</span>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <Icon name="Info" size={16} className="text-blue-600 mr-2 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">Regional Disease Weighting</p>
                <p>Multipliers applied to symptom scores when regional diseases are suspected. Higher values increase urgency scores for endemic conditions.</p>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default TriageAlgorithmConfiguration;