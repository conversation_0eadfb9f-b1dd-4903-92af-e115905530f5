import React from 'react';
import Icon from '../../../components/AppIcon';

const SessionStats = ({ 
  stats = {}, 
  className = '' 
}) => {
  const defaultStats = {
    totalSessions: 0,
    activeSessions: 0,
    completedSessions: 0,
    totalDuration: '0h 0m',
    averageRating: 0,
    topAgent: 'N/A',
    ...stats
  };

  const statCards = [
    {
      title: 'Total Sessions',
      value: defaultStats.totalSessions,
      icon: 'BarChart3',
      color: 'primary',
      change: defaultStats.sessionChange || '+12%',
      changeType: 'positive'
    },
    {
      title: 'Active Sessions',
      value: defaultStats.activeSessions,
      icon: 'Play',
      color: 'success',
      change: defaultStats.activeChange || '+3',
      changeType: 'positive'
    },
    {
      title: 'Completed',
      value: defaultStats.completedSessions,
      icon: 'CheckCircle',
      color: 'secondary',
      change: defaultStats.completedChange || '+8',
      changeType: 'positive'
    },
    {
      title: 'Total Duration',
      value: defaultStats.totalDuration,
      icon: 'Clock',
      color: 'accent',
      change: defaultStats.durationChange || '+2.5h',
      changeType: 'positive'
    }
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      primary: 'bg-primary-50 text-primary-600',
      success: 'bg-success-50 text-success-600',
      secondary: 'bg-secondary-50 text-secondary-600',
      accent: 'bg-accent-50 text-accent-600',
      warning: 'bg-warning-50 text-warning-600',
      error: 'bg-error-50 text-error-600'
    };
    return colorMap[color] || colorMap.primary;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-surface border border-border rounded-lg p-6 hover:shadow-elevated transition-smooth">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(stat.color)}`}>
                <Icon name={stat.icon} size={24} />
              </div>
              <div className={`flex items-center space-x-1 text-sm ${
                stat.changeType === 'positive' ? 'text-success-600' : 'text-error-600'
              }`}>
                <Icon 
                  name={stat.changeType === 'positive' ? 'TrendingUp' : 'TrendingDown'} 
                  size={14} 
                />
                <span>{stat.change}</span>
              </div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-text-primary font-data mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-text-secondary">
                {stat.title}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Average Rating */}
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-text-primary">Average Rating</h3>
            <Icon name="Star" size={20} color="var(--color-accent)" />
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-3xl font-bold text-text-primary font-data">
              {defaultStats.averageRating.toFixed(1)}
            </div>
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Icon
                  key={star}
                  name="Star"
                  size={16}
                  color={star <= Math.round(defaultStats.averageRating) ? 'var(--color-accent)' : 'var(--color-secondary-300)'}
                  className={star <= Math.round(defaultStats.averageRating) ? 'fill-current' : ''}
                />
              ))}
            </div>
          </div>
          
          <div className="mt-4 text-sm text-text-secondary">
            Based on {defaultStats.totalSessions} consultation sessions
          </div>
        </div>

        {/* Top Performing Agent */}
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-text-primary">Top Agent</h3>
            <Icon name="Award" size={20} color="var(--color-success)" />
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-success-50 rounded-full flex items-center justify-center">
              <Icon name="User" size={20} color="var(--color-success)" />
            </div>
            <div className="flex-1">
              <div className="font-semibold text-text-primary">
                {defaultStats.topAgent}
              </div>
              <div className="text-sm text-text-secondary">
                {defaultStats.topAgentSpecialty || 'General Practitioner'}
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold text-success-600 font-data">
                {defaultStats.topAgentRating || '4.9'}
              </div>
              <div className="text-xs text-text-secondary">
                Rating
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Session Distribution */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <h3 className="font-semibold text-text-primary mb-4">Session Distribution</h3>
        
        <div className="space-y-4">
          {/* Status Distribution */}
          <div>
            <div className="flex justify-between text-sm text-text-secondary mb-2">
              <span>Completed Sessions</span>
              <span>{Math.round((defaultStats.completedSessions / defaultStats.totalSessions) * 100) || 0}%</span>
            </div>
            <div className="w-full bg-secondary-100 rounded-full h-2">
              <div 
                className="bg-success-500 h-2 rounded-full"
                style={{ width: `${(defaultStats.completedSessions / defaultStats.totalSessions) * 100 || 0}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between text-sm text-text-secondary mb-2">
              <span>Active Sessions</span>
              <span>{Math.round((defaultStats.activeSessions / defaultStats.totalSessions) * 100) || 0}%</span>
            </div>
            <div className="w-full bg-secondary-100 rounded-full h-2">
              <div 
                className="bg-primary-500 h-2 rounded-full"
                style={{ width: `${(defaultStats.activeSessions / defaultStats.totalSessions) * 100 || 0}%` }}
              ></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between text-sm text-text-secondary mb-2">
              <span>Paused Sessions</span>
              <span>{Math.round(((defaultStats.totalSessions - defaultStats.completedSessions - defaultStats.activeSessions) / defaultStats.totalSessions) * 100) || 0}%</span>
            </div>
            <div className="w-full bg-secondary-100 rounded-full h-2">
              <div 
                className="bg-warning-500 h-2 rounded-full"
                style={{ width: `${((defaultStats.totalSessions - defaultStats.completedSessions - defaultStats.activeSessions) / defaultStats.totalSessions) * 100 || 0}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionStats;