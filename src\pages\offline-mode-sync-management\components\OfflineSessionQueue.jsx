import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const OfflineSessionQueue = ({ 
  queuedSessions = [],
  onChangePriority = () => {},
  onRemoveSession = () => {},
  onRetryUpload = () => {},
  className = '' 
}) => {
  const [sortBy, setSortBy] = useState('priority');

  const sortedSessions = [...queuedSessions].sort((a, b) => {
    if (sortBy === 'priority') {
      const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    }
    if (sortBy === 'date') {
      return new Date(b.createdAt) - new Date(a.createdAt);
    }
    if (sortBy === 'size') {
      return b.size - a.size;
    }
    return 0;
  });

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-error-600 bg-error-50';
      case 'medium': return 'text-warning-600 bg-warning-50';
      case 'low': return 'text-success-600 bg-success-50';
      default: return 'text-text-secondary bg-secondary-50';
    }
  };

  const getEstimatedUploadTime = (size) => {
    // Rough estimation based on file size (MB) and average upload speed
    const estimatedMinutes = Math.ceil(size / 2); // Assuming 2MB/min
    if (estimatedMinutes < 1) return '<1 min';
    if (estimatedMinutes < 60) return `${estimatedMinutes} min`;
    return `${Math.ceil(estimatedMinutes / 60)}h ${estimatedMinutes % 60}min`;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const totalSize = queuedSessions.reduce((sum, session) => sum + session.size, 0);

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-warning-50 rounded-lg flex items-center justify-center">
            <Icon name="Upload" size={20} color="var(--color-warning-500)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Upload Queue
            </h3>
            <p className="text-sm text-text-secondary">
              {queuedSessions.length} sessions pending • {totalSize.toFixed(1)}MB total
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="priority">Sort by Priority</option>
            <option value="date">Sort by Date</option>
            <option value="size">Sort by Size</option>
          </select>
        </div>
      </div>

      {/* Queue Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-background rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-error-50 rounded-lg flex items-center justify-center">
              <Icon name="AlertCircle" size={16} color="var(--color-error-500)" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">High Priority</p>
              <p className="text-lg font-semibold text-text-primary">
                {queuedSessions.filter(s => s.priority === 'high').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-warning-50 rounded-lg flex items-center justify-center">
              <Icon name="Clock" size={16} color="var(--color-warning-500)" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Est. Upload Time</p>
              <p className="text-lg font-semibold text-text-primary">
                {getEstimatedUploadTime(totalSize)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-success-50 rounded-lg flex items-center justify-center">
              <Icon name="HardDrive" size={16} color="var(--color-success-500)" />
            </div>
            <div>
              <p className="text-sm text-text-secondary">Storage Used</p>
              <p className="text-lg font-semibold text-text-primary">{totalSize.toFixed(1)}MB</p>
            </div>
          </div>
        </div>
      </div>

      {/* Session List */}
      <div className="space-y-3">
        {sortedSessions.length === 0 ? (
          <div className="text-center py-8">
            <Icon name="CheckCircle" size={48} color="var(--color-success-500)" className="mx-auto mb-4" />
            <h4 className="text-lg font-medium text-text-primary mb-2">All caught up!</h4>
            <p className="text-text-secondary">No sessions waiting to be uploaded</p>
          </div>
        ) : (
          sortedSessions.map((session) => (
            <div key={session.id} className="border border-border rounded-lg p-4 hover:border-border-active transition-fast">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
                    <Icon name="MessageSquare" size={18} color="var(--color-primary-500)" />
                  </div>
                  <div>
                    <h4 className="font-medium text-text-primary">{session.title}</h4>
                    <p className="text-sm text-text-secondary">{formatDate(session.createdAt)}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(session.priority)}`}>
                    {session.priority} priority
                  </span>
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => onRetryUpload(session.id)}
                      className="p-2 text-primary-600 hover:bg-primary-50 rounded-lg transition-fast"
                      title="Retry upload"
                    >
                      <Icon name="RefreshCw" size={16} />
                    </button>
                    <button
                      onClick={() => onRemoveSession(session.id)}
                      className="p-2 text-error-600 hover:bg-error-50 rounded-lg transition-fast"
                      title="Remove from queue"
                    >
                      <Icon name="Trash2" size={16} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4 text-text-muted">
                  <span>{session.size.toFixed(1)}MB</span>
                  <span>•</span>
                  <span>{session.duration} min session</span>
                  <span>•</span>
                  <span>Est. {getEstimatedUploadTime(session.size)}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <label className="text-xs text-text-muted">Priority:</label>
                  <select
                    value={session.priority}
                    onChange={(e) => onChangePriority(session.id, e.target.value)}
                    className="text-xs border border-border rounded px-2 py-1 focus:ring-1 focus:ring-primary-500"
                  >
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>
              </div>

              {session.error && (
                <div className="mt-3 p-3 bg-error-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Icon name="AlertTriangle" size={16} color="var(--color-error-500)" />
                    <span className="text-sm text-error-700">{session.error}</span>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default OfflineSessionQueue;