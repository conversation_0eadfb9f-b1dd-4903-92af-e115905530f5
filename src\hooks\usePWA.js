/**
 * PWA Integration Hook
 * Central hook for managing PWA features: offline status, service worker updates, sync
 */

import { useState, useEffect, useCallback } from 'react';
import swManager from '../services/swManager';
import backgroundSync from '../services/backgroundSync';
import connectionManager from '../services/connectionManager';

const usePWA = () => {
  const [pwaState, setPwaState] = useState({
    // Service Worker state
    swReady: false,
    swState: 'not-registered',
    updateAvailable: false,
    
    // Network state
    isOnline: navigator.onLine,
    
    // Sync state
    syncStatus: 'idle',
    pendingItems: 0,
    syncInProgress: false,
    lastSync: null,
    
    // Cache state
    cacheStatus: null,
    
    // Install state
    isInstallable: false,
    isInstalled: false
  });

  const [installPrompt, setInstallPrompt] = useState(null);

  // Initialize PWA services
  useEffect(() => {
    const initPWA = async () => {
      try {
        // Initialize service worker
        const swReady = await swManager.init();
        setPwaState(prev => ({ 
          ...prev, 
          swReady,
          swState: swManager.getState()
        }));

        // Initialize background sync
        await backgroundSync.init();

        // Get initial sync status
        const syncStatus = await backgroundSync.getSyncStatus();
        setPwaState(prev => ({
          ...prev,
          pendingItems: syncStatus.pendingItems,
          lastSync: syncStatus.lastSync
        }));

        console.log('PWA services initialized');
      } catch (error) {
        console.error('Failed to initialize PWA services:', error);
      }
    };

    initPWA();
  }, []);

  // Set up service worker event listeners
  useEffect(() => {
    const handleSWUpdate = (event) => {
      setPwaState(prev => ({ 
        ...prev, 
        updateAvailable: true,
        swState: 'waiting'
      }));
    };

    const handleSWActivated = (event) => {
      setPwaState(prev => ({ 
        ...prev, 
        swState: 'active',
        updateAvailable: false
      }));
    };

    const handleNetworkChange = (event) => {
      const { isOnline } = event.detail;
      setPwaState(prev => ({ ...prev, isOnline }));
      
      if (isOnline) {
        // Trigger sync when coming back online
        backgroundSync.forcSync().catch(error => {
          console.error('Auto-sync failed after reconnection:', error);
        });
      }
    };

    const handleSyncStatus = (event) => {
      const { type, data } = event.detail;
      
      switch (type) {
        case 'sync-started':
          setPwaState(prev => ({ 
            ...prev, 
            syncStatus: 'syncing',
            syncInProgress: true
          }));
          break;
          
        case 'sync-completed':
          setPwaState(prev => ({ 
            ...prev, 
            syncStatus: 'completed',
            syncInProgress: false,
            lastSync: new Date().toISOString(),
            pendingItems: 0
          }));
          
          // Reset status after delay
          setTimeout(() => {
            setPwaState(prev => ({ ...prev, syncStatus: 'idle' }));
          }, 3000);
          break;
          
        case 'sync-failed':
          setPwaState(prev => ({ 
            ...prev, 
            syncStatus: 'failed',
            syncInProgress: false
          }));
          break;
          
        case 'sync-progress':
          // Update pending items count
          setPwaState(prev => ({ 
            ...prev, 
            pendingItems: data.remaining || 0
          }));
          break;
      }
    };

    const handleCacheStatus = (event) => {
      setPwaState(prev => ({ 
        ...prev, 
        cacheStatus: event.detail
      }));
    };

    // Register event listeners
    swManager.on('update-available', handleSWUpdate);
    swManager.on('activated', handleSWActivated);
    swManager.on('network-change', handleNetworkChange);
    swManager.on('cache-status', handleCacheStatus);
    
    window.addEventListener('background-sync', handleSyncStatus);

    return () => {
      swManager.off('update-available', handleSWUpdate);
      swManager.off('activated', handleSWActivated);
      swManager.off('network-change', handleNetworkChange);
      swManager.off('cache-status', handleCacheStatus);
      
      window.removeEventListener('background-sync', handleSyncStatus);
    };
  }, []);

  // Handle app install prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (event) => {
      event.preventDefault();
      setInstallPrompt(event);
      setPwaState(prev => ({ ...prev, isInstallable: true }));
    };

    const handleAppInstalled = () => {
      setInstallPrompt(null);
      setPwaState(prev => ({ 
        ...prev, 
        isInstallable: false,
        isInstalled: true
      }));
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Check if already installed
    const checkInstallStatus = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isPWA = window.navigator.standalone === true;
      
      if (isStandalone || isPWA) {
        setPwaState(prev => ({ ...prev, isInstalled: true }));
      }
    };

    checkInstallStatus();

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => {
      setPwaState(prev => ({ ...prev, isOnline: true }));
    };

    const handleOffline = () => {
      setPwaState(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  /**
   * Install the PWA
   */
  const installApp = useCallback(async () => {
    if (!installPrompt) {
      throw new Error('App installation not available');
    }

    try {
      const result = await installPrompt.prompt();
      console.log('Install prompt result:', result);
      
      if (result.outcome === 'accepted') {
        setInstallPrompt(null);
        setPwaState(prev => ({ 
          ...prev, 
          isInstallable: false,
          isInstalled: true
        }));
      }
      
      return result.outcome;
    } catch (error) {
      console.error('Failed to install app:', error);
      throw error;
    }
  }, [installPrompt]);

  /**
   * Apply service worker update
   */
  const applyUpdate = useCallback(async () => {
    if (!pwaState.updateAvailable) {
      throw new Error('No update available');
    }

    try {
      await swManager.applyUpdate();
    } catch (error) {
      console.error('Failed to apply update:', error);
      throw error;
    }
  }, [pwaState.updateAvailable]);

  /**
   * Force sync all pending data
   */
  const forceSync = useCallback(async () => {
    if (!pwaState.isOnline) {
      throw new Error('Cannot sync while offline');
    }

    try {
      setPwaState(prev => ({ 
        ...prev, 
        syncStatus: 'syncing',
        syncInProgress: true
      }));
      
      await backgroundSync.forcSync();
      
      // Also trigger service worker background sync
      await swManager.triggerBackgroundSync();
      
      console.log('Force sync completed');
    } catch (error) {
      console.error('Force sync failed:', error);
      setPwaState(prev => ({ 
        ...prev, 
        syncStatus: 'failed',
        syncInProgress: false
      }));
      throw error;
    }
  }, [pwaState.isOnline]);

  /**
   * Clear all caches
   */
  const clearCaches = useCallback(async () => {
    try {
      await swManager.clearCaches();
      console.log('Caches cleared');
    } catch (error) {
      console.error('Failed to clear caches:', error);
      throw error;
    }
  }, []);

  /**
   * Get cache status
   */
  const getCacheStatus = useCallback(async () => {
    try {
      const status = await swManager.getCacheStatus();
      setPwaState(prev => ({ ...prev, cacheStatus: status }));
      return status;
    } catch (error) {
      console.error('Failed to get cache status:', error);
      return null;
    }
  }, []);

  /**
   * Check connection status
   */
  const checkConnection = useCallback(async () => {
    try {
      // Test actual connectivity with a lightweight request
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const isConnected = response.ok;
      setPwaState(prev => ({ ...prev, isOnline: isConnected }));
      
      return isConnected;
    } catch (error) {
      setPwaState(prev => ({ ...prev, isOnline: false }));
      return false;
    }
  }, []);

  /**
   * Get sync statistics
   */
  const getSyncStats = useCallback(async () => {
    try {
      const stats = await backgroundSync.getSyncStatus();
      return {
        pendingItems: stats.pendingItems,
        failedItems: stats.failedItems,
        lastSync: stats.lastSync,
        syncInProgress: stats.syncInProgress
      };
    } catch (error) {
      console.error('Failed to get sync stats:', error);
      return null;
    }
  }, []);

  /**
   * Register for background sync
   */
  const registerBackgroundSync = useCallback(async (tags = []) => {
    try {
      await swManager.triggerBackgroundSync(tags);
    } catch (error) {
      console.error('Failed to register background sync:', error);
      throw error;
    }
  }, []);

  return {
    // State
    ...pwaState,
    
    // Actions
    installApp,
    applyUpdate,
    forceSync,
    clearCaches,
    getCacheStatus,
    checkConnection,
    getSyncStats,
    registerBackgroundSync,
    
    // Utilities
    isSupported: 'serviceWorker' in navigator,
    canInstall: !!installPrompt,
    
    // Status helpers
    isOfflineReady: pwaState.swReady && pwaState.cacheStatus?.hasOfflineCache,
    needsUpdate: pwaState.updateAvailable,
    hasPendingSync: pwaState.pendingItems > 0,
    isFullyOnline: pwaState.isOnline && pwaState.swReady
  };
};

export default usePWA;
