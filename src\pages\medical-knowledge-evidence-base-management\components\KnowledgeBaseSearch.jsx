import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';

const KnowledgeBaseSearch = ({ onSearch, searchResults, isLoading }) => {
  const [query, setQuery] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (value) => {
    setQuery(value);
    onSearch?.(value);
    setShowResults(value.length > 0);
    setSelectedIndex(-1);
  };

  const handleKeyDown = (event) => {
    if (!showResults || searchResults?.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => 
          prev < searchResults.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0) {
          handleResultClick(searchResults[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowResults(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleResultClick = (result) => {
    setQuery(result.title);
    setShowResults(false);
    onSearch?.(result.title);
  };

  const getResultIcon = (type) => {
    switch (type) {
      case 'africa-specific': return 'Globe';
      case 'common-condition': return 'Heart';
      case 'guideline': return 'BookOpen';
      case 'red-flag': return 'AlertTriangle';
      case 'cultural-medicine': return 'Leaf';
      default: return 'Search';
    }
  };

  const getResultTypeLabel = (type) => {
    switch (type) {
      case 'africa-specific': return 'Africa-Specific Disease';
      case 'common-condition': return 'Medical Condition';
      case 'guideline': return 'Clinical Guideline';
      case 'red-flag': return 'Red Flag Pattern';
      case 'cultural-medicine': return 'Cultural Medicine';
      default: return 'Medical Knowledge';
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.9) return 'text-success-600';
    if (confidence >= 0.7) return 'text-warning-600';
    return 'text-error-600';
  };

  return (
    <div ref={searchRef} className="relative">
      <div className="relative">
        <Input
          type="text"
          placeholder="Search medical conditions, symptoms, guidelines, or cultural medicines..."
          value={query}
          onChange={(e) => handleSearch(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-12 pr-4 py-3 text-lg"
        />
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
          {isLoading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
          ) : (
            <Icon name="Search" size={20} className="text-text-secondary" />
          )}
        </div>
        {query && (
          <button
            onClick={() => handleSearch('')}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-text-secondary hover:text-text-primary"
          >
            <Icon name="X" size={20} />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && searchResults?.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-surface border border-border rounded-lg shadow-large z-50 max-h-96 overflow-y-auto">
          <div className="p-3 border-b border-border">
            <p className="text-sm text-text-secondary">
              Found {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
            </p>
          </div>
          
          <div className="py-2">
            {searchResults.map((result, index) => (
              <button
                key={result.id}
                onClick={() => handleResultClick(result)}
                className={`w-full px-4 py-3 text-left hover:bg-surface-hover transition-colors ${
                  selectedIndex === index ? 'bg-primary-50' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <Icon 
                        name={getResultIcon(result.type)} 
                        size={16} 
                        className="text-primary-500 mr-2" 
                      />
                      <h4 className="font-medium text-text-primary text-sm">
                        {result.title}
                      </h4>
                    </div>
                    
                    <p className="text-xs text-text-secondary mb-2">
                      {getResultTypeLabel(result.type)}
                      {result.region && ` • ${result.region}`}
                    </p>
                    
                    {result.symptoms && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {result.symptoms.slice(0, 3).map((symptom, idx) => (
                          <span
                            key={idx}
                            className="bg-blue-50 text-blue-700 px-2 py-0.5 rounded text-xs"
                          >
                            {symptom}
                          </span>
                        ))}
                        {result.symptoms.length > 3 && (
                          <span className="text-xs text-text-secondary">
                            +{result.symptoms.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                    
                    {result.redFlags && result.redFlags.length > 0 && (
                      <div className="flex items-center">
                        <Icon name="AlertTriangle" size={12} className="text-error-500 mr-1" />
                        <span className="text-xs text-error-600">
                          {result.redFlags.length} red flag{result.redFlags.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {result.confidence && (
                    <div className="ml-3 text-right">
                      <div className={`text-xs font-medium ${getConfidenceColor(result.confidence)}`}>
                        {Math.round(result.confidence * 100)}%
                      </div>
                      <div className="text-xs text-text-secondary">confidence</div>
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
          
          <div className="p-3 border-t border-border bg-surface-hover">
            <p className="text-xs text-text-secondary text-center">
              Use ↑↓ to navigate, Enter to select, Esc to close
            </p>
          </div>
        </div>
      )}

      {/* No Results */}
      {showResults && query.length > 2 && searchResults?.length === 0 && !isLoading && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-surface border border-border rounded-lg shadow-large z-50">
          <div className="p-6 text-center">
            <Icon name="Search" size={32} className="text-text-secondary mx-auto mb-3" />
            <h4 className="font-medium text-text-primary mb-1">No results found</h4>
            <p className="text-sm text-text-secondary">
              Try searching for symptoms, conditions, or guidelines
            </p>
          </div>
        </div>
      )}

      {/* Search Suggestions */}
      {!query && (
        <div className="mt-4">
          <p className="text-sm text-text-secondary mb-2">Quick searches:</p>
          <div className="flex flex-wrap gap-2">
            {[
              'malaria symptoms',
              'hypertension guidelines',
              'traditional medicine',
              'red flag chest pain',
              'diabetes management',
              'tuberculosis screening'
            ].map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSearch(suggestion)}
                className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm hover:bg-primary-100 transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default KnowledgeBaseSearch;