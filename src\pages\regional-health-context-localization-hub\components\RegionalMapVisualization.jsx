import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const RegionalMapVisualization = ({ countries, selectedCountry, onCountrySelect }) => {
  const [hoveredCountry, setHoveredCountry] = useState(null);

  // SVG coordinates for African countries (simplified representation)
  const countryPaths = {
    'ghana': {
      path: "M150,280 L160,270 L170,280 L175,290 L170,300 L155,305 L145,295 Z",
      center: { x: 160, y: 287 }
    },
    'nigeria': {
      path: "M180,260 L200,255 L215,265 L220,280 L210,295 L190,300 L175,285 L178,270 Z",
      center: { x: 197, y: 277 }
    },
    'kenya': {
      path: "M280,320 L290,315 L300,325 L305,340 L295,355 L285,350 L275,335 L278,325 Z",
      center: { x: 290, y: 335 }
    },
    'south-africa': {
      path: "M250,420 L280,415 L310,425 L320,445 L305,465 L275,470 L245,455 L240,435 Z",
      center: { x: 280, y: 442 }
    }
  };

  const getCountryById = (id) => countries?.find(c => c.id === id);

  return (
    <div className="bg-surface border border-border rounded-lg p-6">
      {/* Map Container */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Interactive SVG Map */}
        <div className="flex-1">
          <div className="relative bg-primary-25 rounded-lg p-4 h-96 overflow-hidden">
            <svg
              viewBox="0 0 400 500"
              className="w-full h-full"
              style={{ background: 'linear-gradient(180deg, #E0F2FE 0%, #F0F9FF 100%)' }}
            >
              {/* Africa continent outline (simplified) */}
              <path
                d="M100,150 Q120,120 150,130 L200,125 L250,140 L300,135 L350,150 L365,200 L360,250 L350,300 L345,350 L330,400 L300,450 L250,480 L200,475 L150,465 L110,430 L95,380 L90,320 L85,270 L90,220 L100,180 Z"
                fill="#F3F4F6"
                stroke="#D1D5DB"
                strokeWidth="1"
                className="opacity-30"
              />

              {/* Target Countries */}
              {Object.entries(countryPaths).map(([countryId, data]) => {
                const country = getCountryById(countryId);
                const isSelected = selectedCountry === countryId;
                const isHovered = hoveredCountry === countryId;

                return (
                  <g key={countryId}>
                    {/* Country Shape */}
                    <path
                      d={data.path}
                      fill={isSelected ? '#3B82F6' : isHovered ? '#60A5FA' : '#10B981'}
                      stroke={isSelected ? '#1D4ED8' : '#059669'}
                      strokeWidth="2"
                      className="cursor-pointer transition-all duration-200"
                      onMouseEnter={() => setHoveredCountry(countryId)}
                      onMouseLeave={() => setHoveredCountry(null)}
                      onClick={() => onCountrySelect?.(countryId)}
                    />

                    {/* Country Label */}
                    <text
                      x={data.center.x}
                      y={data.center.y}
                      textAnchor="middle"
                      className="text-xs font-medium fill-white pointer-events-none"
                    >
                      {country?.code}
                    </text>

                    {/* Selection Indicator */}
                    {isSelected && (
                      <circle
                        cx={data.center.x}
                        cy={data.center.y - 20}
                        r="3"
                        fill="#EF4444"
                        className="animate-pulse"
                      />
                    )}
                  </g>
                );
              })}

              {/* Legend */}
              <g transform="translate(20, 20)">
                <rect x="0" y="0" width="120" height="70" fill="white" stroke="#D1D5DB" rx="4" />
                <text x="8" y="15" className="text-xs font-medium fill-gray-700">MVP Countries</text>
                
                <circle cx="15" cy="30" r="4" fill="#10B981" />
                <text x="25" y="35" className="text-xs fill-gray-600">Available</text>
                
                <circle cx="15" cy="45" r="4" fill="#3B82F6" />
                <text x="25" y="50" className="text-xs fill-gray-600">Selected</text>
                
                <circle cx="15" cy="60" r="3" fill="#EF4444" />
                <text x="25" y="65" className="text-xs fill-gray-600">Active</text>
              </g>
            </svg>

            {/* Hover Tooltip */}
            {hoveredCountry && (
              <div className="absolute top-4 right-4 bg-surface border border-border rounded-lg shadow-elevated p-3 max-w-48">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">{getCountryById(hoveredCountry)?.flag}</span>
                  <h4 className="font-medium text-text-primary">
                    {getCountryById(hoveredCountry)?.name}
                  </h4>
                </div>
                <div className="space-y-1 text-xs text-text-secondary">
                  <p>Population: {getCountryById(hoveredCountry)?.population}</p>
                  <p>Languages: {getCountryById(hoveredCountry)?.primaryLanguages?.slice(0, 2).join(', ')}</p>
                  <p>Health System: {getCountryById(hoveredCountry)?.healthSystemType?.split(' ').slice(0, 2).join(' ')}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Country Selection Panel */}
        <div className="lg:w-80">
          <h3 className="font-medium text-text-primary mb-4">Target Countries</h3>
          <div className="space-y-3">
            {countries?.map((country) => (
              <button
                key={country.id}
                onClick={() => onCountrySelect?.(country.id)}
                className={`w-full text-left p-4 rounded-lg border transition-all ${
                  selectedCountry === country.id
                    ? 'border-primary-500 bg-primary-50' :'border-border bg-surface hover:border-primary-200 hover:bg-primary-25'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{country.flag}</span>
                    <span className="font-medium text-text-primary">{country.name}</span>
                  </div>
                  {selectedCountry === country.id && (
                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                  )}
                </div>
                
                <div className="space-y-1 text-xs text-text-secondary">
                  <div className="flex items-center gap-1">
                    <Icon name="Users" size={12} />
                    <span>{country.population}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Icon name="MessageSquare" size={12} />
                    <span>{country.primaryLanguages?.slice(0, 2).join(', ')}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Icon name="Building" size={12} />
                    <span>{country.healthcareAccess?.primaryHealthCenters?.toLocaleString()} Centers</span>
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Quick Stats */}
          <div className="mt-6 p-4 bg-secondary-50 rounded-lg">
            <h4 className="font-medium text-text-primary mb-3">Regional Coverage</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-text-secondary">Total Population</span>
                <span className="font-medium text-text-primary">366M+</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Languages Supported</span>
                <span className="font-medium text-text-primary">16+</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Health Centers</span>
                <span className="font-medium text-text-primary">44K+</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegionalMapVisualization;