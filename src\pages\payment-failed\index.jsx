import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { XCircle, RefreshCw, ArrowLeft, HelpCircle, Mail, Phone } from 'lucide-react';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';

const PaymentFailed = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const [failureReason, setFailureReason] = useState('');
  const [reference, setReference] = useState('');

  useEffect(() => {
    const ref = searchParams.get('reference');
    const reason = searchParams.get('reason') || 'Payment was not completed';
    
    setReference(ref || '');
    setFailureReason(reason);
  }, [searchParams]);

  const commonFailureReasons = [
    {
      code: 'insufficient_funds',
      title: 'Insufficient Funds',
      description: 'Your account does not have enough balance to complete this transaction.',
      solution: 'Please ensure your account has sufficient funds and try again.'
    },
    {
      code: 'card_declined',
      title: 'Card Declined',
      description: 'Your bank declined the transaction.',
      solution: 'Contact your bank to authorize online transactions or try a different card.'
    },
    {
      code: 'expired_card',
      title: 'Expired Card',
      description: 'The card you used has expired.',
      solution: 'Please use a valid, non-expired card for payment.'
    },
    {
      code: 'invalid_card',
      title: 'Invalid Card Details',
      description: 'The card details entered are incorrect.',
      solution: 'Please check your card number, expiry date, and CVV, then try again.'
    },
    {
      code: 'network_error',
      title: 'Network Error',
      description: 'There was a connection issue during payment processing.',
      solution: 'Please check your internet connection and try again.'
    },
    {
      code: 'timeout',
      title: 'Transaction Timeout',
      description: 'The transaction took too long to complete.',
      solution: 'Please try again. If the problem persists, contact support.'
    }
  ];

  const getFailureDetails = () => {
    const reason = commonFailureReasons.find(r => 
      failureReason.toLowerCase().includes(r.code) || 
      failureReason.toLowerCase().includes(r.title.toLowerCase())
    );
    
    return reason || {
      title: 'Payment Failed',
      description: failureReason || 'Your payment could not be processed.',
      solution: 'Please try again or contact our support team for assistance.'
    };
  };

  const failureDetails = getFailureDetails();

  const handleRetryPayment = () => {
    navigate('/payment-plans');
  };

  const handleContactSupport = () => {
    // Open support email or chat
    window.location.href = 'mailto:<EMAIL>?subject=Payment Failed - Reference: ' + reference;
  };

  const troubleshootingSteps = [
    'Ensure your card has sufficient funds',
    'Check that your card is valid and not expired',
    'Verify all card details are entered correctly',
    'Try using a different payment method',
    'Contact your bank to authorize online transactions',
    'Clear your browser cache and cookies'
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-2xl mx-auto pt-20 px-4">
        {/* Failure Message */}
        <div className="bg-white rounded-lg shadow-sm border border-red-200 p-8 text-center mb-8">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <XCircle size={40} className="text-red-600" />
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Payment Failed
          </h1>
          
          <p className="text-lg text-gray-600 mb-6">
            We couldn't process your payment. Don't worry, no charges were made to your account.
          </p>

          {/* Failure Details */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6 text-left">
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              {failureDetails.title}
            </h3>
            <p className="text-red-800 mb-3">
              {failureDetails.description}
            </p>
            <p className="text-sm text-red-700 font-medium">
              <strong>Solution:</strong> {failureDetails.solution}
            </p>
            
            {reference && (
              <div className="mt-4 pt-3 border-t border-red-200">
                <p className="text-xs text-red-600">
                  <strong>Reference:</strong> {reference}
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <Button
              onClick={handleRetryPayment}
              className="flex items-center justify-center"
            >
              <RefreshCw size={16} className="mr-2" />
              Try Again
            </Button>
            
            <Button
              onClick={() => navigate('/session-dashboard-history')}
              variant="outline"
              className="flex items-center justify-center"
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <Button
            onClick={handleContactSupport}
            variant="secondary"
            className="flex items-center justify-center w-full sm:w-auto"
          >
            <HelpCircle size={16} className="mr-2" />
            Contact Support
          </Button>
        </div>

        {/* Troubleshooting Guide */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Troubleshooting Steps
          </h2>
          
          <div className="space-y-3">
            {troubleshootingSteps.map((step, index) => (
              <div key={index} className="flex items-start">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                  <span className="text-xs font-bold text-blue-600">{index + 1}</span>
                </div>
                <p className="text-sm text-gray-700">{step}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Alternative Payment Methods */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Alternative Payment Methods
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Bank Transfer</h3>
              <p className="text-sm text-gray-600">
                Pay directly from your bank account using our secure bank transfer option.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">USSD Payment</h3>
              <p className="text-sm text-gray-600">
                Use your mobile phone to complete payment via USSD codes.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Mobile Money</h3>
              <p className="text-sm text-gray-600">
                Pay using your mobile money wallet for instant transactions.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">QR Code</h3>
              <p className="text-sm text-gray-600">
                Scan and pay using your mobile banking app's QR code feature.
              </p>
            </div>
          </div>
        </div>

        {/* Support Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-4">
            Need Help?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <Mail size={20} className="text-blue-600 mr-3" />
              <div>
                <p className="font-medium text-blue-900">Email Support</p>
                <p className="text-sm text-blue-700"><EMAIL></p>
              </div>
            </div>
            
            <div className="flex items-center">
              <Phone size={20} className="text-blue-600 mr-3" />
              <div>
                <p className="font-medium text-blue-900">Phone Support</p>
                <p className="text-sm text-blue-700">+234 (0) ************</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-100 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Support Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (WAT)
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailed;