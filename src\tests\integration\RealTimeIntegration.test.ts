/**
 * REAL-TIME COMMUNICATION INTEGRATION TESTS
 * 
 * Tests for real-time communication integration across goal tracking,
 * steering guidance, and memory management systems.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { realTimeAgentCommunication } from '../../services/RealTimeAgentCommunication';
import { goalTrackerAgent } from '../../agents/GoalTrackerAgent';
import { AgentOrchestrator } from '../../services/AgentOrchestrator';
import { memoryCleanupManager } from '../../services/MemoryCleanupManager';

describe('Real-Time Communication Integration Tests', () => {
  let orchestrator: AgentOrchestrator;
  let testSessionId: string;
  let receivedUpdates: any[] = [];

  beforeEach(async () => {
    testSessionId = `rt-test-${Date.now()}`;
    orchestrator = new AgentOrchestrator();
    await orchestrator.initialize();
    
    // Clear received updates
    receivedUpdates = [];
    
    // Set up real-time listeners
    realTimeAgentCommunication.on('goal-progress-update', (data) => {
      receivedUpdates.push({ type: 'goal-progress', data });
    });
    
    realTimeAgentCommunication.on('steering-guidance-update', (data) => {
      receivedUpdates.push({ type: 'steering-guidance', data });
    });
    
    realTimeAgentCommunication.on('goal-completed', (data) => {
      receivedUpdates.push({ type: 'goal-completed', data });
    });
  });

  afterEach(async () => {
    // Cleanup
    await orchestrator.cleanupSession(testSessionId);
    realTimeAgentCommunication.removeAllListeners();
  });

  describe('Goal Progress Real-Time Updates', () => {
    it('should broadcast goal progress updates in real-time', async () => {
      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'I want to discuss my diabetes management',
        userId: 'test-user',
        urgencyLevel: 'medium' as const
      };

      // Process request
      await orchestrator.processRequest(testRequest);

      // Wait for real-time updates
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verify goal progress updates were received
      const goalUpdates = receivedUpdates.filter(u => u.type === 'goal-progress');
      expect(goalUpdates.length).toBeGreaterThan(0);

      // Verify update structure
      const update = goalUpdates[0];
      expect(update.data).toHaveProperty('sessionId');
      expect(update.data).toHaveProperty('goals');
      expect(update.data.sessionId).toBe(testSessionId);

      console.log('✅ Goal progress real-time updates validated');
    });

    it('should handle offline sync for goal updates', async () => {
      // Simulate offline state
      const originalEmit = realTimeAgentCommunication.emit;
      const queuedUpdates: any[] = [];
      
      realTimeAgentCommunication.emit = vi.fn((event, data) => {
        queuedUpdates.push({ event, data });
      });

      try {
        // Process request while "offline"
        const testRequest = {
          sessionId: testSessionId,
          userMessage: 'Offline test message',
          userId: 'test-user',
          urgencyLevel: 'low' as const
        };

        await orchestrator.processRequest(testRequest);

        // Verify updates were queued
        expect(queuedUpdates.length).toBeGreaterThan(0);

        // Restore connection and process queue
        realTimeAgentCommunication.emit = originalEmit;
        
        // Process queued updates
        queuedUpdates.forEach(({ event, data }) => {
          realTimeAgentCommunication.emit(event, data);
        });

        console.log('✅ Offline sync for goal updates validated');
      } finally {
        realTimeAgentCommunication.emit = originalEmit;
      }
    });
  });

  describe('Steering Guidance Real-Time Updates', () => {
    it('should broadcast steering guidance updates immediately', async () => {
      // Create conversation that should trigger steering
      const requests = [
        {
          sessionId: testSessionId,
          userMessage: 'I have multiple health concerns',
          userId: 'test-user',
          urgencyLevel: 'medium' as const
        },
        {
          sessionId: testSessionId,
          userMessage: 'Let me talk about something else entirely',
          userId: 'test-user',
          urgencyLevel: 'medium' as const
        }
      ];

      for (const request of requests) {
        await orchestrator.processRequest(request);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Verify steering guidance updates
      const steeringUpdates = receivedUpdates.filter(u => u.type === 'steering-guidance');
      expect(steeringUpdates.length).toBeGreaterThan(0);

      // Verify update structure
      const update = steeringUpdates[0];
      expect(update.data).toHaveProperty('sessionId');
      expect(update.data).toHaveProperty('data');
      expect(update.data.sessionId).toBe(testSessionId);

      console.log('✅ Steering guidance real-time updates validated');
    });
  });

  describe('Goal Completion Real-Time Notifications', () => {
    it('should notify when goals are completed', async () => {
      // Simulate goal completion scenario
      const completionRequest = {
        sessionId: testSessionId,
        userMessage: 'Thank you, that completely answers my question about medication timing',
        userId: 'test-user',
        urgencyLevel: 'low' as const
      };

      await orchestrator.processRequest(completionRequest);

      // Wait for completion processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check for completion notifications
      const completionUpdates = receivedUpdates.filter(u => u.type === 'goal-completed');
      
      if (completionUpdates.length > 0) {
        const update = completionUpdates[0];
        expect(update.data).toHaveProperty('sessionId');
        expect(update.data).toHaveProperty('goalId');
        expect(update.data.sessionId).toBe(testSessionId);
      }

      console.log('✅ Goal completion notifications validated');
    });
  });

  describe('Real-Time + Memory Management Integration', () => {
    it('should not interfere with memory cleanup operations', async () => {
      // Generate multiple real-time updates
      const requests = Array.from({ length: 5 }, (_, i) => ({
        sessionId: `${testSessionId}-${i}`,
        userMessage: `Memory test message ${i}`,
        userId: 'test-user',
        urgencyLevel: 'low' as const
      }));

      // Process all requests
      await Promise.all(requests.map(req => orchestrator.processRequest(req)));

      // Wait for real-time updates
      await new Promise(resolve => setTimeout(resolve, 500));

      // Trigger memory cleanup
      const cleanupResults = await memoryCleanupManager.performGlobalCleanup();

      // Verify cleanup didn't break real-time communication
      expect(cleanupResults).toBeDefined();
      expect(Array.isArray(cleanupResults)).toBe(true);

      // Verify real-time communication still works
      const testRequest = {
        sessionId: testSessionId,
        userMessage: 'Post-cleanup test message',
        userId: 'test-user',
        urgencyLevel: 'low' as const
      };

      await orchestrator.processRequest(testRequest);

      console.log('✅ Real-time + Memory management integration validated');
    });
  });

  describe('Real-Time Performance Under Load', () => {
    it('should maintain performance with multiple concurrent sessions', async () => {
      const sessionCount = 10;
      const sessions = Array.from({ length: sessionCount }, (_, i) => `${testSessionId}-load-${i}`);

      const startTime = Date.now();

      // Create concurrent requests for multiple sessions
      const concurrentRequests = sessions.map(sessionId => 
        orchestrator.processRequest({
          sessionId,
          userMessage: 'Load test message',
          userId: 'test-user',
          urgencyLevel: 'low' as const
        })
      );

      await Promise.all(concurrentRequests);

      const processingTime = Date.now() - startTime;

      // Wait for all real-time updates
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verify reasonable performance
      expect(processingTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Verify updates were received for multiple sessions
      const uniqueSessions = new Set(
        receivedUpdates
          .filter(u => u.data.sessionId)
          .map(u => u.data.sessionId)
      );

      expect(uniqueSessions.size).toBeGreaterThan(1);

      console.log(`✅ Real-time performance under load validated - ${processingTime}ms for ${sessionCount} sessions`);
    });

    it('should handle real-time update bursts without loss', async () => {
      const updateCount = 20;
      const initialUpdateCount = receivedUpdates.length;

      // Generate burst of updates
      const burstRequests = Array.from({ length: updateCount }, (_, i) => 
        orchestrator.processRequest({
          sessionId: `${testSessionId}-burst-${i}`,
          userMessage: `Burst test message ${i}`,
          userId: 'test-user',
          urgencyLevel: 'low' as const
        })
      );

      await Promise.all(burstRequests);

      // Wait for all updates to be processed
      await new Promise(resolve => setTimeout(resolve, 2000));

      const finalUpdateCount = receivedUpdates.length;
      const newUpdates = finalUpdateCount - initialUpdateCount;

      // Should receive updates for most/all requests
      expect(newUpdates).toBeGreaterThan(updateCount * 0.8); // At least 80% success rate

      console.log(`✅ Real-time update burst handling validated - ${newUpdates}/${updateCount} updates received`);
    });
  });

  describe('Real-Time Error Handling', () => {
    it('should handle real-time communication failures gracefully', async () => {
      // Simulate communication failure
      const originalEmit = realTimeAgentCommunication.emit;
      realTimeAgentCommunication.emit = vi.fn(() => {
        throw new Error('Real-time communication failed');
      });

      try {
        // Should not crash the main flow
        const testRequest = {
          sessionId: testSessionId,
          userMessage: 'Test message during communication failure',
          userId: 'test-user',
          urgencyLevel: 'medium' as const
        };

        const response = await orchestrator.processRequest(testRequest);

        // Main functionality should still work
        expect(response).toBeDefined();
        expect(response.response).toBeDefined();

        console.log('✅ Real-time communication failure handling validated');
      } finally {
        realTimeAgentCommunication.emit = originalEmit;
      }
    });
  });
});
