import React, { useState, useEffect } from 'react';
import Icon from '../AppIcon';

const ConsultationStatusIndicator = ({ 
  isActive = false, 
  activeAgents = [], 
  sessionProgress = 0,
  consultationPhase = 'ready',
  onEmergencyStop = () => {},
  className = ''
}) => {
  const [isListening, setIsListening] = useState(false);
  const [currentAgent, setCurrentAgent] = useState(null);

  useEffect(() => {
    if (activeAgents.length > 0) {
      setCurrentAgent(activeAgents[0]);
    }
  }, [activeAgents]);

  const getStatusConfig = () => {
    switch (consultationPhase) {
      case 'active':
        return {
          color: 'success',
          icon: 'Mic',
          label: 'Active Consultation',
          description: `${activeAgents.length} agent${activeAgents.length !== 1 ? 's' : ''} active`
        };
      case 'listening':
        return {
          color: 'primary',
          icon: 'MicIcon',
          label: 'Listening',
          description: 'Speak now...'
        };
      case 'processing':
        return {
          color: 'warning',
          icon: 'Brain',
          label: 'Processing',
          description: 'Agents consulting...'
        };
      case 'paused':
        return {
          color: 'secondary',
          icon: 'Pause',
          label: 'Paused',
          description: 'Consultation paused'
        };
      default:
        return {
          color: 'secondary',
          icon: 'Activity',
          label: 'Ready',
          description: 'System ready'
        };
    }
  };

  const status = getStatusConfig();

  return (
    <div className={`fixed top-4 right-4 z-200 ${className}`}>
      <div className="bg-surface border border-border rounded-xl shadow-elevated p-4 min-w-[280px]">
        {/* Status Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              consultationPhase === 'active' ? 'bg-success-50 animate-consultation-pulse' :
              consultationPhase === 'listening' ? 'bg-primary-50 animate-breathe' :
              consultationPhase === 'processing'? 'bg-warning-50 animate-ambient-float' : 'bg-secondary-50'
            }`}>
              <Icon 
                name={status.icon} 
                size={20} 
                color={`var(--color-${status.color})`}
              />
            </div>
            <div>
              <h3 className="font-semibold text-text-primary font-heading">
                {status.label}
              </h3>
              <p className="text-sm text-text-secondary font-caption">
                {status.description}
              </p>
            </div>
          </div>
          
          {/* Emergency Stop Button */}
          {isActive && (
            <button
              onClick={onEmergencyStop}
              className="emergency-control p-2 rounded-lg"
              title="Emergency Stop"
            >
              <Icon name="Square" size={16} />
            </button>
          )}
        </div>

        {/* Progress Bar */}
        {isActive && sessionProgress > 0 && (
          <div className="mb-3">
            <div className="flex justify-between text-xs text-text-secondary mb-1">
              <span>Session Progress</span>
              <span>{Math.round(sessionProgress)}%</span>
            </div>
            <div className="w-full bg-secondary-100 rounded-full h-2">
              <div 
                className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${sessionProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Active Agents */}
        {activeAgents.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-text-primary">Active Agents</h4>
            <div className="space-y-1">
              {activeAgents.map((agent, index) => (
                <div 
                  key={agent.id || index}
                  className="flex items-center justify-between p-2 bg-primary-50 rounded-lg"
                >
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                      <Icon name="User" size={12} color="white" />
                    </div>
                    <span className="text-sm font-medium text-primary-600">
                      {agent.name || `Agent ${index + 1}`}
                    </span>
                  </div>
                  <div className="agent-indicator">
                    {agent.specialty || 'General'}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Voice Visualization */}
        {consultationPhase === 'listening' && (
          <div className="mt-3 pt-3 border-t border-border">
            <div className="flex items-center justify-center space-x-1 h-8">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="voice-waveform-bar"
                  style={{
                    height: `${Math.random() * 20 + 10}px`,
                    animationDelay: `${i * 0.1}s`
                  }}
                ></div>
              ))}
            </div>
            <p className="text-center text-xs text-text-secondary mt-2">
              Voice activity detected
            </p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-4 pt-3 border-t border-border">
          <div className="flex space-x-2">
            <button
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-secondary-50 hover:bg-secondary-100 rounded-lg transition-fast text-sm font-medium text-text-secondary hover:text-text-primary"
              title="View Session Details"
            >
              <Icon name="FileText" size={14} />
              <span>Details</span>
            </button>
            <button
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-secondary-50 hover:bg-secondary-100 rounded-lg transition-fast text-sm font-medium text-text-secondary hover:text-text-primary"
              title="Session Settings"
            >
              <Icon name="Settings" size={14} />
              <span>Settings</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationStatusIndicator;