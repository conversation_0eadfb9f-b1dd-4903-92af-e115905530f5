/**
 * TYPE DEFINITIONS INDEX
 * 
 * Central export point for all TypeScript type definitions
 * used throughout the VoiceHealth AI application.
 */

// Medical data types
export * from './medical';

// Authentication and RBAC types
export * from './auth';

// API and error handling types
export * from './api';

// Enhancement types
export * from './enhancements';

// Re-export commonly used types for convenience
export type {
  // Medical data
  MedicalCondition,
  Medication,
  Symptom,
  PatientProfile,
  Consultation,
  MedicalDataResponse,
  EncryptedMedicalData,
  StoredMedicalData,
  MedicalSeverity,
  UserRole,

  // Services
  MedicalDataService,
  EncryptionService,
  AuditLogger
} from './medical';

export type {
  // Authentication
  User,
  Session,
  UserMetadata,
  AppMetadata,
  AuthResponse,
  RoleDefinition,
  ResourceType,
  Action,
  Permission,
  PermissionCondition,
  AuthContext,
  EmergencyAccessRequest,
  AuthService,
  RBACService
} from './auth';

export type {
  // API
  ApiResponse,
  ApiError,
  PaginatedResponse,
  ApiRequestConfig,
  RateLimitInfo,
  ValidationError,
  ErrorRecoveryStrategy,
  HealthCheckResponse,
  NetworkStatus,
  SyncOperation,
  MedicalDataApiResponse,
  ApiClient
} from './api';

// Constants
export { ROLE_DEFINITIONS } from './auth';
