import React, { useState } from 'react';
import { Users, Plus, Settings, CreditCard, Shield, Star, Edit, Trash2, Eye } from 'lucide-react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Icon from '../../../components/AppIcon';


const FamilyAccountManager = ({ currency }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showAddMember, setShowAddMember] = useState(false);
  const [newMember, setNewMember] = useState({ name: '', email: '', role: 'member' });

  // Mock family data
  const [familyMembers, setFamilyMembers] = useState([
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      creditUsed: 45,
      creditLimit: 100,
      joinDate: '2024-01-15',
      avatar: '👨‍💼'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'member',
      status: 'active',
      creditUsed: 23,
      creditLimit: 75,
      joinDate: '2024-01-15',
      avatar: '👩‍💼'
    },
    {
      id: 3,
      name: 'Teen Doe',
      email: '<EMAIL>',
      role: 'dependent',
      status: 'active',
      creditUsed: 12,
      creditLimit: 50,
      joinDate: '2024-02-01',
      avatar: '👦'
    }
  ]);

  const [familyStats, setFamilyStats] = useState({
    totalCredits: 500,
    usedCredits: 180,
    remainingCredits: 320,
    monthlyAllowance: 600,
    discountPercentage: 20,
    monthlySavings: { NGN: 15000, USD: 30 }
  });

  const familyPlans = [
    {
      id: 'family-basic',
      name: 'Family Basic',
      price: { NGN: 35000, USD: 70 },
      members: 4,
      credits: 400,
      features: ['Shared credit pool', 'Basic AI consultations', 'Family dashboard', '15% family discount']
    },
    {
      id: 'family-premium',
      name: 'Family Premium',
      price: { NGN: 55000, USD: 110 },
      members: 6,
      credits: 800,
      features: ['Shared credit pool', 'All consultation types', 'Advanced family analytics', '20% family discount', 'Priority support'],
      popular: true
    },
    {
      id: 'family-unlimited',
      name: 'Family Unlimited',
      price: { NGN: 75000, USD: 150 },
      members: 'Unlimited',
      credits: 'Unlimited',
      features: ['Unlimited consultations', 'All premium features', 'Dedicated family manager', '25% family discount', '24/7 priority support']
    }
  ];

  const formatPrice = (price, currency) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleAddMember = () => {
    if (newMember.name && newMember.email) {
      const member = {
        id: Date.now(),
        ...newMember,
        status: 'invited',
        creditUsed: 0,
        creditLimit: newMember.role === 'dependent' ? 50 : 75,
        joinDate: new Date().toISOString().split('T')[0],
        avatar: newMember.role === 'dependent' ? '👶' : '👤'
      };
      setFamilyMembers([...familyMembers, member]);
      setNewMember({ name: '', email: '', role: 'member' });
      setShowAddMember(false);
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Family Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Members</p>
              <p className="text-2xl font-bold text-gray-900">{familyMembers.length}</p>
            </div>
            <Users className="text-blue-500" size={24} />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Shared Credits</p>
              <p className="text-2xl font-bold text-gray-900">{familyStats.remainingCredits}</p>
            </div>
            <CreditCard className="text-green-500" size={24} />
          </div>
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${(familyStats.remainingCredits / familyStats.totalCredits) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Monthly Savings</p>
              <p className="text-2xl font-bold text-green-600">
                {formatPrice(familyStats.monthlySavings[currency], currency)}
              </p>
            </div>
            <Star className="text-yellow-500" size={24} />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {familyStats.discountPercentage}% family discount
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Security Score</p>
              <p className="text-2xl font-bold text-blue-600">98%</p>
            </div>
            <Shield className="text-blue-500" size={24} />
          </div>
          <p className="text-xs text-gray-500 mt-1">All members secured</p>
        </div>
      </div>

      {/* Family Members */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Family Members</h3>
            <Button
              size="sm"
              onClick={() => setShowAddMember(true)}
            >
              <Plus size={16} className="mr-1" />
              Add Member
            </Button>
          </div>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            {familyMembers.map((member) => (
              <div key={member.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">{member.avatar}</div>
                  <div>
                    <h4 className="font-medium text-gray-900">{member.name}</h4>
                    <p className="text-sm text-gray-500">{member.email}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        member.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                        member.role === 'member'? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {member.role}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {member.status}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <p className="text-sm text-gray-900">
                    {member.creditUsed}/{member.creditLimit} credits
                  </p>
                  <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${(member.creditUsed / member.creditLimit) * 100}%` }}
                    ></div>
                  </div>
                  <div className="flex items-center space-x-1 mt-2">
                    <Button variant="ghost" size="sm">
                      <Eye size={14} />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit size={14} />
                    </Button>
                    {member.role !== 'admin' && (
                      <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                        <Trash2 size={14} />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Add Member Modal */}
      {showAddMember && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Family Member</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <Input
                  value={newMember.name}
                  onChange={(e) => setNewMember({...newMember, name: e.target.value})}
                  placeholder="Enter full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <Input
                  type="email"
                  value={newMember.email}
                  onChange={(e) => setNewMember({...newMember, email: e.target.value})}
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  value={newMember.role}
                  onChange={(e) => setNewMember({...newMember, role: e.target.value})}
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="member">Family Member</option>
                  <option value="dependent">Dependent (Under 18)</option>
                </select>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowAddMember(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddMember}
                className="flex-1"
              >
                Add Member
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderPlans = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Family Plan Options
        </h3>
        <p className="text-gray-600">
          Choose the perfect plan for your family's health needs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {familyPlans.map((plan) => (
          <div
            key={plan.id}
            className={`bg-white rounded-lg border-2 transition-all duration-200 hover:shadow-lg ${
              plan.popular ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'
            }`}
          >
            {plan.popular && (
              <div className="bg-blue-500 text-white text-center py-2 rounded-t-lg">
                <span className="text-sm font-medium">Most Popular</span>
              </div>
            )}

            <div className="p-6">
              <h4 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h4>
              <div className="mb-4">
                <span className="text-3xl font-bold text-gray-900">
                  {formatPrice(plan.price[currency], currency)}
                </span>
                <span className="text-gray-600">/month</span>
              </div>

              <div className="space-y-2 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Family Members:</span>
                  <span className="font-medium text-gray-900">{plan.members}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Monthly Credits:</span>
                  <span className="font-medium text-gray-900">{plan.credits}</span>
                </div>
              </div>

              <ul className="space-y-2 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle size={16} className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    <span className="text-sm text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                className={`w-full ${
                  plan.popular ? 'bg-blue-600 hover:bg-blue-700' : ''
                }`}
                variant={plan.popular ? 'primary' : 'outline'}
              >
                Choose Plan
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Family Account Settings
        </h3>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Family Account Name
            </label>
            <Input
              value="The Doe Family"
              onChange={() => {}}
              placeholder="Enter family account name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Credit Sharing Rules
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <Input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-gray-700">Allow all members to use shared credits</span>
              </label>
              <label className="flex items-center">
                <Input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-gray-700">Require approval for high-cost consultations</span>
              </label>
              <label className="flex items-center">
                <Input type="checkbox" className="mr-2" />
                <span className="text-sm text-gray-700">Set individual spending limits</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Privacy Settings
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <Input type="checkbox" defaultChecked className="mr-2" />
                <span className="text-sm text-gray-700">Share consultation summaries with family admin</span>
              </label>
              <label className="flex items-center">
                <Input type="checkbox" className="mr-2" />
                <span className="text-sm text-gray-700">Allow family members to view each other's health data</span>
              </label>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <Button>
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Users },
    { id: 'plans', label: 'Family Plans', icon: Star },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600' :'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon size={16} className="mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'plans' && renderPlans()}
      {activeTab === 'settings' && renderSettings()}
    </div>
  );
};

export default FamilyAccountManager;