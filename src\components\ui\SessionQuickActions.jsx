import React, { useState } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

const SessionQuickActions = ({ 
  session = {},
  onDownloadTranscript = () => {},
  onReplayAudio = () => {},
  onResumeConsultation = () => {},
  onShareSession = () => {},
  className = ''
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleDownloadTranscript = async () => {
    setIsDownloading(true);
    try {
      await onDownloadTranscript(session.id);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleReplayAudio = () => {
    setIsPlaying(!isPlaying);
    onReplayAudio(session.id, !isPlaying);
  };

  const handleResumeConsultation = () => {
    onResumeConsultation(session.id);
  };

  const handleShareSession = () => {
    onShareSession(session.id);
  };

  const getSessionStatusColor = () => {
    switch (session.status) {
      case 'completed':
        return 'success';
      case 'active':
        return 'primary';
      case 'paused':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'secondary';
    }
  };

  return (
    <div className={`bg-surface border border-border rounded-lg p-4 ${className}`}>
      {/* Session Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className="font-semibold text-text-primary font-heading">
              {session.title || `Session ${session.id?.slice(-8) || 'Unknown'}`}
            </h3>
            <span className={`session-status ${session.status || 'unknown'}`}>
              <Icon 
                name={
                  session.status === 'completed' ? 'CheckCircle' :
                  session.status === 'active' ? 'Play' :
                  session.status === 'paused' ? 'Pause' :
                  session.status === 'cancelled'? 'XCircle' : 'Clock'
                } 
                size={14} 
              />
              {session.status || 'Unknown'}
            </span>
          </div>
          
          <div className="space-y-1 text-sm text-text-secondary">
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <Icon name="Calendar" size={14} />
                <span>{session.date || 'No date'}</span>
              </span>
              <span className="flex items-center space-x-1">
                <Icon name="Clock" size={14} />
                <span>{session.duration || '0 min'}</span>
              </span>
            </div>
            
            {session.agents && session.agents.length > 0 && (
              <div className="flex items-center space-x-1">
                <Icon name="Users" size={14} />
                <span>{session.agents.length} agent{session.agents.length !== 1 ? 's' : ''}</span>
                <span className="text-text-muted">•</span>
                <span>{session.agents.map(agent => agent.specialty || 'General').join(', ')}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
        {/* Download Transcript */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadTranscript}
          loading={isDownloading}
          disabled={!session.hasTranscript}
          iconName="Download"
          iconPosition="left"
          className="justify-center"
        >
          <span className="hidden sm:inline">Transcript</span>
          <span className="sm:hidden">PDF</span>
        </Button>

        {/* Replay Audio */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleReplayAudio}
          disabled={!session.hasAudio}
          iconName={isPlaying ? "Pause" : "Play"}
          iconPosition="left"
          className="justify-center"
        >
          <span className="hidden sm:inline">{isPlaying ? 'Pause' : 'Replay'}</span>
          <span className="sm:hidden">{isPlaying ? 'Pause' : 'Play'}</span>
        </Button>

        {/* Resume/Continue */}
        {(session.status === 'paused' || session.status === 'active') && (
          <Button
            variant="primary"
            size="sm"
            onClick={handleResumeConsultation}
            iconName="ArrowRight"
            iconPosition="left"
            className="justify-center"
          >
            <span className="hidden sm:inline">Resume</span>
            <span className="sm:hidden">Go</span>
          </Button>
        )}

        {/* Share Session */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleShareSession}
          iconName="Share2"
          iconPosition="left"
          className="justify-center"
        >
          <span className="hidden sm:inline">Share</span>
          <span className="sm:hidden">Share</span>
        </Button>
      </div>

      {/* Session Metrics */}
      {session.metrics && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-text-primary font-data">
                {session.metrics.accuracy || '0'}%
              </div>
              <div className="text-xs text-text-secondary">Accuracy</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-text-primary font-data">
                {session.metrics.interactions || '0'}
              </div>
              <div className="text-xs text-text-secondary">Interactions</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-text-primary font-data">
                {session.metrics.satisfaction || '0'}/5
              </div>
              <div className="text-xs text-text-secondary">Rating</div>
            </div>
          </div>
        </div>
      )}

      {/* Audio Waveform Visualization */}
      {isPlaying && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="flex items-center justify-center space-x-1 h-12 bg-secondary-50 rounded-lg">
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className="voice-waveform-bar bg-primary-500"
                style={{
                  height: `${Math.random() * 30 + 10}px`,
                  animationDelay: `${i * 0.05}s`
                }}
              ></div>
            ))}
          </div>
          <div className="flex justify-between items-center mt-2 text-xs text-text-secondary">
            <span>0:00</span>
            <span className="flex items-center space-x-1">
              <Icon name="Volume2" size={12} />
              <span>Playing audio...</span>
            </span>
            <span>{session.duration || '0:00'}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionQuickActions;