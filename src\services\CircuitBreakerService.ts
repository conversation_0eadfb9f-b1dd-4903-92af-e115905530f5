/**
 * CIRCUIT BREAKER SERVICE
 * 
 * Reusable circuit breaker implementation for external API calls
 * to prevent cascading failures and provide graceful degradation.
 * 
 * FEATURES:
 * - Configurable failure thresholds
 * - Automatic recovery mechanisms
 * - Fallback strategies
 * - Performance monitoring
 * - Emergency bypass for critical operations
 * - HIPAA-compliant error handling
 */

import { auditLogger } from '../utils/auditLogger';

export interface CircuitBreakerConfig {
  name: string;
  failureThreshold: number;
  recoveryTimeoutMs: number;
  monitoringWindowMs: number;
  emergencyBypass: boolean;
  fallbackEnabled: boolean;
}

export interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failures: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
}

export interface CircuitBreakerMetrics {
  name: string;
  state: string;
  failureRate: number;
  successRate: number;
  averageResponseTime: number;
  totalRequests: number;
  uptime: number;
  lastStateChange: string;
}

export type FallbackFunction<T> = () => Promise<T>;
export type ApiFunction<T> = () => Promise<T>;

export class CircuitBreaker<T = any> {
  private config: CircuitBreakerConfig;
  private state: CircuitBreakerState;
  private responseTimes: number[] = [];
  private fallbackFunction?: FallbackFunction<T>;
  private stateChangeListeners: ((state: string) => void)[] = [];

  constructor(config: CircuitBreakerConfig) {
    this.config = config;
    this.state = {
      state: 'closed',
      failures: 0,
      lastFailureTime: 0,
      lastSuccessTime: Date.now(),
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0
    };

    console.log(`🔧 Circuit breaker initialized: ${config.name}`);
  }

  /**
   * Execute API call with circuit breaker protection
   */
  async execute(apiFunction: ApiFunction<T>, emergencyContext?: boolean): Promise<T> {
    const startTime = Date.now();
    this.state.totalRequests++;

    try {
      // Check if circuit breaker should allow the request
      if (!this.shouldAllowRequest(emergencyContext)) {
        throw new CircuitBreakerOpenError(`Circuit breaker is open for ${this.config.name}`);
      }

      // Execute the API call
      const result = await apiFunction();

      // Record success
      this.recordSuccess(Date.now() - startTime);

      return result;

    } catch (error) {
      // Record failure
      this.recordFailure(Date.now() - startTime);

      // Try fallback if available and not in emergency context
      if (this.fallbackFunction && !emergencyContext) {
        console.log(`🔄 Using fallback for ${this.config.name}`);
        try {
          return await this.fallbackFunction();
        } catch (fallbackError) {
          console.error(`❌ Fallback failed for ${this.config.name}:`, fallbackError);
          throw error; // Throw original error
        }
      }

      throw error;
    }
  }

  /**
   * Set fallback function for when circuit is open
   */
  setFallback(fallbackFunction: FallbackFunction<T>): void {
    this.fallbackFunction = fallbackFunction;
    console.log(`🔄 Fallback function set for ${this.config.name}`);
  }

  /**
   * Check if request should be allowed
   */
  private shouldAllowRequest(emergencyContext?: boolean): boolean {
    // Emergency bypass
    if (emergencyContext && this.config.emergencyBypass) {
      console.log(`🚨 Emergency bypass activated for ${this.config.name}`);
      return true;
    }

    const now = Date.now();

    switch (this.state.state) {
      case 'closed':
        return true;

      case 'open':
        // Check if recovery timeout has passed
        if (now - this.state.lastFailureTime >= this.config.recoveryTimeoutMs) {
          this.setState('half-open');
          console.log(`🔄 Circuit breaker ${this.config.name} moved to half-open state`);
          return true;
        }
        return false;

      case 'half-open':
        // Allow limited requests to test if service has recovered
        return true;

      default:
        return false;
    }
  }

  /**
   * Record successful API call
   */
  private recordSuccess(responseTime: number): void {
    this.state.successfulRequests++;
    this.state.lastSuccessTime = Date.now();
    this.responseTimes.push(responseTime);

    // Keep only recent response times for monitoring
    if (this.responseTimes.length > 100) {
      this.responseTimes = this.responseTimes.slice(-100);
    }

    // If in half-open state, consider closing the circuit
    if (this.state.state === 'half-open') {
      this.setState('closed');
      this.state.failures = 0; // Reset failure count
      console.log(`✅ Circuit breaker ${this.config.name} closed after successful recovery`);
    }
  }

  /**
   * Record failed API call
   */
  private recordFailure(responseTime: number): void {
    this.state.failedRequests++;
    this.state.failures++;
    this.state.lastFailureTime = Date.now();
    this.responseTimes.push(responseTime);

    // Check if failure threshold is exceeded
    if (this.state.failures >= this.config.failureThreshold) {
      this.setState('open');
      console.warn(`⚠️ Circuit breaker ${this.config.name} opened after ${this.state.failures} failures`);
    }
  }

  /**
   * Set circuit breaker state
   */
  private setState(newState: 'closed' | 'open' | 'half-open'): void {
    const oldState = this.state.state;
    this.state.state = newState;

    // Notify listeners
    this.stateChangeListeners.forEach(listener => {
      try {
        listener(newState);
      } catch (error) {
        console.error('Error in circuit breaker state change listener:', error);
      }
    });

    // Audit log for monitoring
    auditLogger.logSystemEvent({
      event: 'circuit_breaker_state_change',
      serviceName: this.config.name,
      oldState,
      newState,
      failures: this.state.failures,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add state change listener
   */
  onStateChange(listener: (state: string) => void): void {
    this.stateChangeListeners.push(listener);
  }

  /**
   * Get current circuit breaker metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    const totalRequests = this.state.totalRequests;
    const failureRate = totalRequests > 0 ? (this.state.failedRequests / totalRequests) * 100 : 0;
    const successRate = totalRequests > 0 ? (this.state.successfulRequests / totalRequests) * 100 : 0;
    const averageResponseTime = this.responseTimes.length > 0 
      ? this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length 
      : 0;
    const uptime = this.state.state === 'closed' ? 100 : 0;

    return {
      name: this.config.name,
      state: this.state.state,
      failureRate,
      successRate,
      averageResponseTime,
      totalRequests,
      uptime,
      lastStateChange: new Date(this.state.lastFailureTime || this.state.lastSuccessTime).toISOString()
    };
  }

  /**
   * Reset circuit breaker state
   */
  reset(): void {
    this.state = {
      state: 'closed',
      failures: 0,
      lastFailureTime: 0,
      lastSuccessTime: Date.now(),
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0
    };
    this.responseTimes = [];
    console.log(`🔄 Circuit breaker ${this.config.name} reset`);
  }

  /**
   * Force circuit breaker state (for testing/emergency)
   */
  forceState(state: 'closed' | 'open' | 'half-open'): void {
    console.log(`🔧 Forcing circuit breaker ${this.config.name} to ${state} state`);
    this.setState(state);
  }
}

/**
 * Circuit Breaker Service Manager
 */
export class CircuitBreakerService {
  private circuitBreakers: Map<string, CircuitBreaker> = new Map();
  private defaultConfig: Partial<CircuitBreakerConfig> = {
    failureThreshold: 5,
    recoveryTimeoutMs: 60000, // 1 minute
    monitoringWindowMs: 300000, // 5 minutes
    emergencyBypass: true,
    fallbackEnabled: true
  };

  /**
   * Create or get circuit breaker for a service
   */
  getCircuitBreaker<T>(name: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker<T> {
    if (this.circuitBreakers.has(name)) {
      return this.circuitBreakers.get(name) as CircuitBreaker<T>;
    }

    const fullConfig: CircuitBreakerConfig = {
      name,
      ...this.defaultConfig,
      ...config
    } as CircuitBreakerConfig;

    const circuitBreaker = new CircuitBreaker<T>(fullConfig);
    this.circuitBreakers.set(name, circuitBreaker);

    console.log(`🔧 Created circuit breaker for service: ${name}`);
    return circuitBreaker;
  }

  /**
   * Get all circuit breaker metrics
   */
  getAllMetrics(): CircuitBreakerMetrics[] {
    return Array.from(this.circuitBreakers.values()).map(cb => cb.getMetrics());
  }

  /**
   * Get metrics for specific service
   */
  getServiceMetrics(serviceName: string): CircuitBreakerMetrics | null {
    const circuitBreaker = this.circuitBreakers.get(serviceName);
    return circuitBreaker ? circuitBreaker.getMetrics() : null;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    console.log('🔄 Resetting all circuit breakers...');
    this.circuitBreakers.forEach(cb => cb.reset());
  }

  /**
   * Get circuit breaker status summary
   */
  getStatusSummary(): {
    total: number;
    closed: number;
    open: number;
    halfOpen: number;
  } {
    const metrics = this.getAllMetrics();
    return {
      total: metrics.length,
      closed: metrics.filter(m => m.state === 'closed').length,
      open: metrics.filter(m => m.state === 'open').length,
      halfOpen: metrics.filter(m => m.state === 'half-open').length
    };
  }
}

/**
 * Custom error for circuit breaker open state
 */
export class CircuitBreakerOpenError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CircuitBreakerOpenError';
  }
}

// Export singleton instance
export const circuitBreakerService = new CircuitBreakerService();
export default circuitBreakerService;
