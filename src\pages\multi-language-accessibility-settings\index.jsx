import React, { useState, useEffect } from 'react';
import { ArrowLeft, Settings, Accessibility, Globe, Save, AlertTriangle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import LanguageConfigurationSection from './components/LanguageConfigurationSection';
import VoiceAudioSection from './components/VoiceAudioSection';
import VisualAccessibilitySection from './components/VisualAccessibilitySection';
import InteractionPreferencesSection from './components/InteractionPreferencesSection';
import SettingsPreview from './components/SettingsPreview';

const MultiLanguageAccessibilitySettings = () => {
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] = useState({
    language: false,
    voiceAudio: false,
    visualAccessibility: false,
    interactionPreferences: false
  });

  const [settings, setSettings] = useState({
    // Language settings
    language: 'en-US',
    region: 'US',
    medicalTerminology: 'standard',
    culturalAdaptation: true,

    // Voice and Audio settings
    selectedVoice: 'agent-1',
    speechSpeed: 1.0,
    volume: 0.8,
    audioEnhancements: {
      volumeAmplification: false,
      frequencyAdjustment: false,
      visualSoundIndicators: false
    },

    // Visual Accessibility settings
    visualTheme: 'default',
    fontSize: 'medium',
    colorBlindnessSupport: 'none',
    screenReaderOptimized: false,
    detailedDescriptions: false,
    skipNavigation: false,
    reduceMotion: false,
    autoPlayMedia: true,

    // Interaction Preferences
    inputMethods: {
      touch: true,
      keyboard: false,
      voice: false
    },
    motorAccessibility: {
      stickyKeys: false,
      largeClickTargets: false,
      noDragDrop: false
    },
    gestureControls: {
      swipeNavigation: false,
      pinchZoom: false
    },
    sessionTimeout: 0
  });

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load saved settings on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('voicehealth-accessibility-settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prevSettings => ({ ...prevSettings, ...parsedSettings }));
      } catch (error) {
        console.error('Error loading saved settings:', error);
      }
    }
  }, []);

  // Track changes for unsaved indicator
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setHasUnsavedChanges(true);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [settings]);

  const toggleSection = (sectionName) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  const handleSettingsChange = (newSettings) => {
    setSettings(newSettings);
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    
    try {
      // Save to localStorage
      localStorage.setItem('voicehealth-accessibility-settings', JSON.stringify(settings));
      
      // In a real app, you would also save to a backend service
      // await saveAccessibilitySettings(settings);
      
      setHasUnsavedChanges(false);
      setShowSaveConfirmation(true);
      
      // Hide confirmation after 3 seconds
      setTimeout(() => {
        setShowSaveConfirmation(false);
      }, 3000);
      
    } catch (error) {
      console.error('Error saving settings:', error);
      // Could show error notification here
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetAll = () => {
    if (window.confirm('Are you sure you want to reset all accessibility settings to default values?')) {
      const defaultSettings = {
        language: 'en-US',
        region: 'US',
        medicalTerminology: 'standard',
        culturalAdaptation: true,
        selectedVoice: 'agent-1',
        speechSpeed: 1.0,
        volume: 0.8,
        audioEnhancements: {
          volumeAmplification: false,
          frequencyAdjustment: false,
          visualSoundIndicators: false
        },
        visualTheme: 'default',
        fontSize: 'medium',
        colorBlindnessSupport: 'none',
        screenReaderOptimized: false,
        detailedDescriptions: false,
        skipNavigation: false,
        reduceMotion: false,
        autoPlayMedia: true,
        inputMethods: {
          touch: true,
          keyboard: false,
          voice: false
        },
        motorAccessibility: {
          stickyKeys: false,
          largeClickTargets: false,
          noDragDrop: false
        },
        gestureControls: {
          swipeNavigation: false,
          pinchZoom: false
        },
        sessionTimeout: 0
      };
      
      setSettings(defaultSettings);
      localStorage.removeItem('voicehealth-accessibility-settings');
      setHasUnsavedChanges(false);
    }
  };

  const handleExportSettings = () => {
    // This would be handled by the SettingsPreview component
    console.log('Settings exported');
  };

  const handleShareSettings = () => {
    // This would be handled by the SettingsPreview component
    console.log('Settings shared');
  };

  const handleGoBack = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to leave without saving?')) {
        navigate(-1);
      }
    } else {
      navigate(-1);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-surface border-b border-border sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleGoBack}
                className="p-2 hover:bg-secondary-50 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-text-muted" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary-50 rounded-lg">
                  <Accessibility className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-text-primary">
                    Multi-Language & Accessibility Settings
                  </h1>
                  <p className="text-sm text-text-muted">
                    Customize your VoiceHealth AI experience for optimal accessibility
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {hasUnsavedChanges && (
                <div className="flex items-center space-x-2 text-warning-600 text-sm">
                  <AlertTriangle className="w-4 h-4" />
                  <span>Unsaved changes</span>
                </div>
              )}
              
              {showSaveConfirmation && (
                <div className="flex items-center space-x-2 text-success-600 text-sm">
                  <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                  <span>Settings saved!</span>
                </div>
              )}

              <button
                onClick={handleSaveSettings}
                disabled={isLoading || !hasUnsavedChanges}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  hasUnsavedChanges && !isLoading
                    ? 'bg-primary-600 text-white hover:bg-primary-700' :'bg-secondary-100 text-secondary-400 cursor-not-allowed'
                }`}
              >
                <Save className="w-4 h-4" />
                <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Introduction */}
          <div className="bg-primary-50 border border-primary-200 rounded-xl p-6">
            <div className="flex items-start space-x-3">
              <Globe className="w-6 h-6 text-primary-600 mt-1" />
              <div>
                <h2 className="text-lg font-semibold text-primary-800 mb-2">
                  Inclusive Healthcare Experience
                </h2>
                <p className="text-primary-700 text-sm leading-relaxed">
                  Configure your accessibility preferences to ensure VoiceHealth AI provides the most 
                  effective and comfortable healthcare experience. These settings will synchronize across 
                  all your devices and can be shared with your healthcare providers for comprehensive care coordination.
                </p>
              </div>
            </div>
          </div>

          {/* Settings Sections */}
          <div className="space-y-4">
            {/* Language Configuration */}
            <LanguageConfigurationSection
              isExpanded={expandedSections.language}
              onToggle={() => toggleSection('language')}
              settings={settings}
              onSettingsChange={handleSettingsChange}
            />

            {/* Voice & Audio Settings */}
            <VoiceAudioSection
              isExpanded={expandedSections.voiceAudio}
              onToggle={() => toggleSection('voiceAudio')}
              settings={settings}
              onSettingsChange={handleSettingsChange}
            />

            {/* Visual Accessibility */}
            <VisualAccessibilitySection
              isExpanded={expandedSections.visualAccessibility}
              onToggle={() => toggleSection('visualAccessibility')}
              settings={settings}
              onSettingsChange={handleSettingsChange}
            />

            {/* Interaction Preferences */}
            <InteractionPreferencesSection
              isExpanded={expandedSections.interactionPreferences}
              onToggle={() => toggleSection('interactionPreferences')}
              settings={settings}
              onSettingsChange={handleSettingsChange}
            />
          </div>

          {/* Settings Preview */}
          <SettingsPreview
            settings={settings}
            onResetAll={handleResetAll}
            onExport={handleExportSettings}
            onShare={handleShareSettings}
          />

          {/* Healthcare Provider Integration */}
          <div className="bg-accent-50 border border-accent-200 rounded-xl p-6">
            <div className="flex items-start space-x-3">
              <Settings className="w-6 h-6 text-accent-600 mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-accent-800 mb-2">
                  Healthcare Provider Integration
                </h3>
                <p className="text-accent-700 text-sm leading-relaxed mb-4">
                  Your accessibility settings help ensure effective communication during medical consultations. 
                  These preferences are considered when matching you with appropriate AI agents and can be 
                  shared with healthcare providers for optimal care delivery.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-xs">
                    Cultural Sensitivity
                  </span>
                  <span className="px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-xs">
                    Language Preferences
                  </span>
                  <span className="px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-xs">
                    Accessibility Needs
                  </span>
                  <span className="px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-xs">
                    Communication Style
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiLanguageAccessibilitySettings;