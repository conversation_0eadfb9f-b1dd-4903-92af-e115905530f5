/**
 * MEDICAL PWA SERVICE WORKER
 * 
 * Optimized service worker for medical applications with intelligent
 * caching strategies that prioritize emergency system performance
 * and medical data availability.
 * 
 * CACHING STRATEGIES:
 * - Emergency routes: Cache First (immediate availability)
 * - Medical data: Network First with offline fallback
 * - Static assets: Stale While Revalidate
 * - API responses: Network First with intelligent caching
 * - User-specific data: Runtime caching with encryption
 * 
 * MEDICAL REQUIREMENTS:
 * - Emergency functions must work offline
 * - Medical data integrity is paramount
 * - HIPAA-compliant caching (no sensitive data in cache)
 * - Automatic cache cleanup for privacy
 * - Emergency cache bypass mechanisms
 */

const CACHE_VERSION = 'v1.2.0';
const CACHE_NAMES = {
  EMERGENCY: `emergency-${CACHE_VERSION}`,
  STATIC: `static-${CACHE_VERSION}`,
  MEDICAL_DATA: `medical-data-${CACHE_VERSION}`,
  API: `api-${CACHE_VERSION}`,
  IMAGES: `images-${CACHE_VERSION}`,
  FONTS: `fonts-${CACHE_VERSION}`
};

// Emergency routes that must be available offline
const EMERGENCY_ROUTES = [
  '/',
  '/emergency',
  '/emergency-protocols',
  '/critical-vitals',
  '/emergency-consultation',
  '/offline-dashboard'
];

// Static assets to cache
const STATIC_ASSETS = [
  '/manifest.json',
  '/favicon.ico',
  '/pwa-192x192.png',
  '/pwa-512x512.png'
];

// API endpoints that can be cached safely (non-sensitive)
const CACHEABLE_API_PATTERNS = [
  /\/api\/health$/,
  /\/api\/medications\/search/,
  /\/api\/conditions\/search/,
  /\/api\/public\//
];

// Sensitive API patterns that should never be cached
const SENSITIVE_API_PATTERNS = [
  /\/api\/auth\//,
  /\/api\/users\//,
  /\/api\/medical-records\//,
  /\/api\/consultations\//,
  /\/api\/audit-logs\//
];

/**
 * Install event - cache emergency resources
 */
self.addEventListener('install', event => {
  console.log('Medical SW: Installing service worker');
  
  event.waitUntil(
    Promise.all([
      // Cache emergency routes
      caches.open(CACHE_NAMES.EMERGENCY).then(cache => {
        console.log('Medical SW: Caching emergency routes');
        return cache.addAll(EMERGENCY_ROUTES.map(route => 
          new Request(route, { cache: 'reload' })
        ));
      }),
      
      // Cache static assets
      caches.open(CACHE_NAMES.STATIC).then(cache => {
        console.log('Medical SW: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
    ]).then(() => {
      console.log('Medical SW: Installation complete');
      // Skip waiting to activate immediately for emergency functionality
      return self.skipWaiting();
    }).catch(error => {
      console.error('Medical SW: Installation failed:', error);
    })
  );
});

/**
 * Activate event - cleanup old caches
 */
self.addEventListener('activate', event => {
  console.log('Medical SW: Activating service worker');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        const validCacheNames = Object.values(CACHE_NAMES);
        return Promise.all(
          cacheNames.map(cacheName => {
            if (!validCacheNames.includes(cacheName)) {
              console.log('Medical SW: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients immediately
      self.clients.claim()
    ]).then(() => {
      console.log('Medical SW: Activation complete');
    })
  );
});

/**
 * Fetch event - intelligent caching strategies
 */
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension requests
  if (url.protocol === 'chrome-extension:') {
    return;
  }
  
  event.respondWith(handleRequest(request));
});

/**
 * Handle different types of requests with appropriate caching strategies
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  try {
    // Emergency routes - Cache First (must work offline)
    if (EMERGENCY_ROUTES.includes(pathname) || pathname.includes('/emergency')) {
      return await cacheFirst(request, CACHE_NAMES.EMERGENCY);
    }
    
    // API requests
    if (pathname.startsWith('/api/')) {
      return await handleApiRequest(request);
    }
    
    // Static assets (JS, CSS, images, fonts)
    if (isStaticAsset(pathname)) {
      return await handleStaticAsset(request);
    }
    
    // Medical application routes - Network First with offline fallback
    if (isMedicalRoute(pathname)) {
      return await networkFirstWithFallback(request);
    }
    
    // Default: Network First
    return await networkFirst(request, CACHE_NAMES.STATIC);
    
  } catch (error) {
    console.error('Medical SW: Request handling failed:', error);
    return await getOfflineFallback(request);
  }
}

/**
 * Handle API requests with medical data considerations
 */
async function handleApiRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // Never cache sensitive medical data
  if (SENSITIVE_API_PATTERNS.some(pattern => pattern.test(pathname))) {
    return await networkOnly(request);
  }
  
  // Cache safe API responses
  if (CACHEABLE_API_PATTERNS.some(pattern => pattern.test(pathname))) {
    return await networkFirst(request, CACHE_NAMES.API);
  }
  
  // Health check endpoint - always try network first
  if (pathname === '/api/health') {
    return await networkFirst(request, CACHE_NAMES.API);
  }
  
  // Default for API: Network Only (for security)
  return await networkOnly(request);
}

/**
 * Handle static assets with appropriate caching
 */
async function handleStaticAsset(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // Images
  if (pathname.match(/\.(png|jpg|jpeg|gif|webp|svg)$/)) {
    return await staleWhileRevalidate(request, CACHE_NAMES.IMAGES);
  }
  
  // Fonts
  if (pathname.match(/\.(woff|woff2|ttf|eot)$/)) {
    return await cacheFirst(request, CACHE_NAMES.FONTS);
  }
  
  // JS/CSS bundles
  if (pathname.match(/\.(js|css)$/)) {
    return await staleWhileRevalidate(request, CACHE_NAMES.STATIC);
  }
  
  // Default static asset handling
  return await staleWhileRevalidate(request, CACHE_NAMES.STATIC);
}

/**
 * Check if pathname is a medical application route
 */
function isMedicalRoute(pathname) {
  const medicalRoutes = [
    '/dashboard',
    '/medical',
    '/consultation',
    '/patient',
    '/provider',
    '/admin',
    '/profile',
    '/settings'
  ];
  
  return medicalRoutes.some(route => pathname.startsWith(route));
}

/**
 * Check if pathname is a static asset
 */
function isStaticAsset(pathname) {
  return pathname.match(/\.(js|css|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|eot|ico)$/);
}

/**
 * Cache First strategy - for emergency routes
 */
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Medical SW: Cache First failed:', error);
    throw error;
  }
}

/**
 * Network First strategy - for dynamic content
 */
async function networkFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('Medical SW: Network failed, trying cache:', error);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

/**
 * Network First with offline fallback for medical routes
 */
async function networkFirstWithFallback(request) {
  try {
    return await networkFirst(request, CACHE_NAMES.STATIC);
  } catch (error) {
    // Return offline fallback page for medical routes
    const cache = await caches.open(CACHE_NAMES.EMERGENCY);
    const fallback = await cache.match('/offline-dashboard');
    if (fallback) {
      return fallback;
    }
    
    // If no fallback, return basic offline page
    return new Response(
      `<!DOCTYPE html>
      <html>
        <head>
          <title>VoiceHealth AI - Offline</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline { color: #666; }
            .emergency { background: #fee; border: 1px solid #fcc; padding: 20px; margin: 20px; border-radius: 5px; }
          </style>
        </head>
        <body>
          <h1>VoiceHealth AI</h1>
          <div class="offline">
            <h2>You're currently offline</h2>
            <p>Some features may not be available without an internet connection.</p>
          </div>
          <div class="emergency">
            <h3>🚨 Medical Emergency?</h3>
            <p>If this is a medical emergency, please call your local emergency services immediately.</p>
            <p>Emergency numbers: 911 (US), 999 (UK), 112 (EU)</p>
          </div>
          <button onclick="window.location.reload()">Try Again</button>
        </body>
      </html>`,
      {
        headers: { 'Content-Type': 'text/html' },
        status: 200
      }
    );
  }
}

/**
 * Stale While Revalidate strategy - for static assets
 */
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Fetch in background to update cache
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(error => {
    console.warn('Medical SW: Background fetch failed:', error);
  });
  
  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // If no cache, wait for network
  return await fetchPromise;
}

/**
 * Network Only strategy - for sensitive data
 */
async function networkOnly(request) {
  return await fetch(request);
}

/**
 * Get offline fallback response
 */
async function getOfflineFallback(request) {
  const cache = await caches.open(CACHE_NAMES.EMERGENCY);
  
  // Try to get specific offline fallback
  const fallback = await cache.match('/offline-dashboard');
  if (fallback) {
    return fallback;
  }
  
  // Return generic offline response
  return new Response('Offline - Medical services unavailable', {
    status: 503,
    statusText: 'Service Unavailable'
  });
}

/**
 * Message handling for cache management
 */
self.addEventListener('message', event => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'CLEAR_MEDICAL_CACHE':
      clearMedicalCache().then(() => {
        event.ports[0].postMessage({ success: true });
      }).catch(error => {
        event.ports[0].postMessage({ success: false, error: error.message });
      });
      break;
      
    case 'PRELOAD_EMERGENCY':
      preloadEmergencyRoutes().then(() => {
        event.ports[0].postMessage({ success: true });
      }).catch(error => {
        event.ports[0].postMessage({ success: false, error: error.message });
      });
      break;
      
    default:
      console.warn('Medical SW: Unknown message type:', type);
  }
});

/**
 * Clear medical data cache for privacy
 */
async function clearMedicalCache() {
  const cacheNames = [CACHE_NAMES.MEDICAL_DATA, CACHE_NAMES.API];
  await Promise.all(cacheNames.map(name => caches.delete(name)));
  console.log('Medical SW: Medical cache cleared for privacy');
}

/**
 * Preload emergency routes
 */
async function preloadEmergencyRoutes() {
  const cache = await caches.open(CACHE_NAMES.EMERGENCY);
  await cache.addAll(EMERGENCY_ROUTES);
  console.log('Medical SW: Emergency routes preloaded');
}
