import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const VoicePreviewPlayer = ({ 
  isPlaying = false,
  currentAgent = null,
  sampleText = "",
  onPlay = () => {},
  onPause = () => {},
  onStop = () => {},
  className = ''
}) => {
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  useEffect(() => {
    let interval;
    if (isPlaying) {
      interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 0.1;
          if (newTime >= duration) {
            onStop();
            return 0;
          }
          setProgress((newTime / duration) * 100);
          return newTime;
        });
      }, 100);
    }
    return () => clearInterval(interval);
  }, [isPlaying, duration, onStop]);

  useEffect(() => {
    if (sampleText) {
      // Estimate duration based on text length (rough calculation)
      const wordsPerMinute = 150;
      const words = sampleText.split(' ').length;
      const estimatedDuration = (words / wordsPerMinute) * 60;
      setDuration(estimatedDuration);
    }
  }, [sampleText]);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newProgress = (clickX / rect.width) * 100;
    const newTime = (newProgress / 100) * duration;
    
    setProgress(newProgress);
    setCurrentTime(newTime);
  };

  if (!currentAgent) {
    return null;
  }

  return (
    <div className={`voice-preview-player ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-primary-500 flex items-center justify-center">
            <Icon 
              name={
                currentAgent.type === 'gp' ? 'Stethoscope' :
                currentAgent.type === 'cardiologist' ? 'Heart' :
                currentAgent.type === 'nutritionist'? 'Apple' : 'User'
              } 
              size={16} 
              color="white" 
            />
          </div>
          <div>
            <h4 className="font-medium text-text-primary font-heading">
              {currentAgent.name}
            </h4>
            <p className="text-xs text-text-secondary font-caption">
              Voice Preview
            </p>
          </div>
        </div>
        
        <button
          onClick={onStop}
          className="p-2 hover:bg-secondary-50 rounded-lg transition-fast touch-target"
          title="Close preview"
        >
          <Icon name="X" size={16} color="var(--color-text-secondary)" />
        </button>
      </div>

      {/* Sample Text */}
      <div className="mb-4 p-3 bg-secondary-50 rounded-lg">
        <p className="text-sm text-text-primary font-caption leading-relaxed">
          {sampleText}
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div 
          className="preview-progress"
          onClick={handleProgressClick}
        >
          <div 
            className="preview-progress-fill"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-text-secondary mt-1">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setCurrentTime(0);
            setProgress(0);
          }}
          iconName="SkipBack"
          disabled={!isPlaying && currentTime === 0}
          className="touch-target"
        />
        
        <Button
          variant="primary"
          size="md"
          onClick={isPlaying ? onPause : onPlay}
          iconName={isPlaying ? "Pause" : "Play"}
          className="w-12 h-12 rounded-full touch-target"
        />
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onStop}
          iconName="Square"
          disabled={!isPlaying && currentTime === 0}
          className="touch-target"
        />
      </div>

      {/* Voice Settings Display */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-text-secondary">
              Gender: <span className="text-text-primary font-medium capitalize">
                {currentAgent.voiceProfile?.gender || 'Not specified'}
              </span>
            </span>
            <span className="text-text-secondary">
              Accent: <span className="text-text-primary font-medium">
                {currentAgent.voiceProfile?.accent || 'Not specified'}
              </span>
            </span>
          </div>
          
          {/* Voice Visualization */}
          {isPlaying && (
            <div className="flex items-center space-x-1">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="voice-waveform-bar"
                  style={{
                    animationDelay: `${i * 0.1}s`
                  }}
                ></div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoicePreviewPlayer;