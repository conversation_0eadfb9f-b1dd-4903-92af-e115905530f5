import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SpeechProcessingPanel = () => {
  const [speechMetrics, setSpeechMetrics] = useState({
    whisperAPI: {
      status: 'active',
      accuracy: 94.8,
      latency: 145,
      queueLength: 12,
      processedToday: 2847,
      errorRate: 0.02
    },
    elevenlabs: {
      status: 'active',
      queueStatus: 'normal',
      avgGenerationTime: 2.3,
      voicesActive: 8,
      synthesizedToday: 1923,
      errorRate: 0.01
    }
  });

  const [isLoading, setIsLoading] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-success-600 bg-success-100';
      case 'warning': return 'text-warning-600 bg-warning-100';
      case 'error': return 'text-error-600 bg-error-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getMetricColor = (value, threshold = 90) => {
    if (value >= threshold) return 'text-success-600';
    if (value >= threshold * 0.7) return 'text-warning-600';
    return 'text-error-600';
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  const handleConfigureWhisper = () => {
    console.log('Configure Whisper API settings');
  };

  const handleConfigureElevenLabs = () => {
    console.log('Configure ElevenLabs settings');
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Speech Processing Status
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Real-time monitoring of speech-to-text and text-to-speech systems
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              iconName="RefreshCw"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button
              size="sm"
              variant="primary"
              iconName="Settings"
            >
              Configure
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Whisper API Section */}
        <div className="bg-secondary-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Icon name="Mic" size={24} className="text-primary-500 mr-3" />
              <div>
                <h4 className="font-semibold text-text-primary">Whisper API (Speech-to-Text)</h4>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(speechMetrics.whisperAPI.status)}`}>
                  {speechMetrics.whisperAPI.status}
                </span>
              </div>
            </div>
            <Button
              size="sm"
              variant="outline"
              iconName="Settings"
              onClick={handleConfigureWhisper}
            >
              Configure
            </Button>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-surface rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-text-secondary font-medium">Accuracy</span>
                <span className={`text-sm font-semibold ${getMetricColor(speechMetrics.whisperAPI.accuracy)}`}>
                  {speechMetrics.whisperAPI.accuracy}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-success-500 transition-all duration-300"
                  style={{ width: `${speechMetrics.whisperAPI.accuracy}%` }}
                />
              </div>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Avg Latency</p>
              <p className="text-lg font-semibold text-text-primary">{speechMetrics.whisperAPI.latency}ms</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Queue Length</p>
              <p className="text-lg font-semibold text-text-primary">{speechMetrics.whisperAPI.queueLength}</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Processed Today</p>
              <p className="text-lg font-semibold text-text-primary">{speechMetrics.whisperAPI.processedToday.toLocaleString()}</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Error Rate</p>
              <p className="text-lg font-semibold text-success-600">{speechMetrics.whisperAPI.errorRate}%</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-success-500 rounded-full mr-2 animate-pulse"></div>
                <span className="text-sm text-text-secondary">Processing</span>
              </div>
            </div>
          </div>
        </div>

        {/* ElevenLabs Section */}
        <div className="bg-secondary-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Icon name="Volume2" size={24} className="text-primary-500 mr-3" />
              <div>
                <h4 className="font-semibold text-text-primary">ElevenLabs (Text-to-Speech)</h4>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(speechMetrics.elevenlabs.status)}`}>
                  {speechMetrics.elevenlabs.status}
                </span>
              </div>
            </div>
            <Button
              size="sm"
              variant="outline"
              iconName="Settings"
              onClick={handleConfigureElevenLabs}
            >
              Configure
            </Button>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-surface rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-text-secondary font-medium">Queue Status</span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  speechMetrics.elevenlabs.queueStatus === 'normal' ? 'bg-success-100 text-success-600' :
                  speechMetrics.elevenlabs.queueStatus === 'busy'? 'bg-warning-100 text-warning-600' : 'bg-error-100 text-error-600'
                }`}>
                  {speechMetrics.elevenlabs.queueStatus}
                </span>
              </div>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Avg Generation</p>
              <p className="text-lg font-semibold text-text-primary">{speechMetrics.elevenlabs.avgGenerationTime}s</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Active Voices</p>
              <p className="text-lg font-semibold text-text-primary">{speechMetrics.elevenlabs.voicesActive}</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Synthesized Today</p>
              <p className="text-lg font-semibold text-text-primary">{speechMetrics.elevenlabs.synthesizedToday.toLocaleString()}</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <p className="text-xs text-text-secondary mb-1">Error Rate</p>
              <p className="text-lg font-semibold text-success-600">{speechMetrics.elevenlabs.errorRate}%</p>
            </div>

            <div className="bg-surface rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></div>
                <span className="text-sm text-text-secondary">Synthesizing</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            iconName="Play"
            className="justify-center"
          >
            Test STT
          </Button>
          <Button
            variant="outline"
            iconName="Volume2"
            className="justify-center"
          >
            Test TTS
          </Button>
          <Button
            variant="outline"
            iconName="BarChart"
            className="justify-center"
          >
            View Logs
          </Button>
          <Button
            variant="outline"
            iconName="Download"
            className="justify-center"
          >
            Export Data
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SpeechProcessingPanel;