/**
 * SPECIALIST REFERRAL NETWORK SERVICE
 * 
 * Provides intelligent specialist referral recommendations with regional
 * healthcare network integration, availability-based routing, and quality assurance.
 * 
 * FEATURES:
 * - Regional specialist directory with real-time availability
 * - Intelligent referral matching based on condition and cultural factors
 * - Telemedicine integration for remote consultations
 * - Referral quality tracking and outcome monitoring
 * - Cost-based referral recommendations with insurance verification
 * - Wait time predictions and optimization
 * - Cultural competency matching for specialists
 * - Emergency specialist routing with priority handling
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { culturalAdaptationService } from './CulturalAdaptationService';
import { enhancedClinicalDecisionSupportService } from './ClinicalDecisionSupportService';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface Specialist {
  id: string;
  name: string;
  title: string;
  specialty: string;
  subSpecialties: string[];
  qualifications: Qualification[];
  location: HealthcareLocation;
  contactInfo: ContactInfo;
  availability: AvailabilitySchedule;
  languagesSpoken: string[];
  culturalCompetencies: string[];
  telemedicineCapable: boolean;
  emergencyAvailable: boolean;
  consultationFees: ConsultationFees;
  insuranceAccepted: string[];
  rating: number; // 0-5 stars
  reviewCount: number;
  responseTime: number; // average hours to respond
  waitTime: number; // average days to appointment
  verified: boolean;
  lastUpdated: Date;
}

export interface Qualification {
  degree: string;
  institution: string;
  year: number;
  country: string;
  verified: boolean;
}

export interface HealthcareLocation {
  facilityName: string;
  address: string;
  city: string;
  region: string;
  country: string;
  coordinates: { latitude: number; longitude: number };
  facilityType: 'hospital' | 'clinic' | 'private_practice' | 'telemedicine';
  facilityRating: number;
  emergencyServices: boolean;
}

export interface ContactInfo {
  phoneNumber: string;
  email?: string;
  whatsapp?: string;
  telemedicineUrl?: string;
  bookingUrl?: string;
  preferredContactMethod: 'phone' | 'email' | 'whatsapp' | 'online';
}

export interface AvailabilitySchedule {
  monday?: TimeSlot[];
  tuesday?: TimeSlot[];
  wednesday?: TimeSlot[];
  thursday?: TimeSlot[];
  friday?: TimeSlot[];
  saturday?: TimeSlot[];
  sunday?: TimeSlot[];
  emergencyHours: boolean;
  nextAvailableSlot?: Date;
  bookingLeadTime: number; // days
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string;   // HH:MM format
  type: 'consultation' | 'telemedicine' | 'emergency';
  available: boolean;
}

export interface ConsultationFees {
  currency: string;
  initialConsultation: number;
  followUpConsultation: number;
  telemedicineConsultation: number;
  emergencyConsultation?: number;
  paymentMethods: string[];
  insuranceBilling: boolean;
}

export interface ReferralRequest {
  patientId: string;
  condition: string;
  symptoms: string[];
  urgencyLevel: 'routine' | 'urgent' | 'emergency';
  preferredLocation?: string;
  maxTravelDistance?: number; // kilometers
  culturalPreferences: CulturalPreferences;
  insuranceInfo?: InsuranceInfo;
  budgetConstraints?: BudgetConstraints;
  telemedicinePreference: boolean;
  languagePreference: string;
  previousSpecialists?: string[]; // IDs of previously seen specialists
}

export interface CulturalPreferences {
  genderPreference?: 'male' | 'female' | 'no_preference';
  religiousConsiderations: string[];
  culturalBackground: string;
  familyInvolvementLevel: 'minimal' | 'moderate' | 'high';
  traditionalMedicineOpenness: number; // 1-5 scale
}

export interface InsuranceInfo {
  provider: string;
  policyNumber: string;
  coverageType: string;
  copayAmount?: number;
  deductibleRemaining?: number;
}

export interface BudgetConstraints {
  maxConsultationFee: number;
  currency: string;
  paymentMethod: string;
}

export interface ReferralRecommendation {
  specialist: Specialist;
  matchScore: number; // 0-100
  matchReasons: string[];
  estimatedWaitTime: number; // days
  estimatedCost: number;
  availableSlots: AvailableSlot[];
  culturalCompatibility: number; // 0-100
  qualityScore: number; // 0-100
  distanceFromPatient: number; // kilometers
  telemedicineOption: boolean;
  emergencyAvailable: boolean;
  insuranceCovered: boolean;
  specialNotes: string[];
}

export interface AvailableSlot {
  date: Date;
  time: string;
  type: 'consultation' | 'telemedicine' | 'emergency';
  duration: number; // minutes
  cost: number;
}

export interface ReferralOutcome {
  referralId: string;
  specialistId: string;
  patientId: string;
  appointmentScheduled: boolean;
  appointmentDate?: Date;
  consultationCompleted: boolean;
  patientSatisfaction?: number; // 1-5 scale
  clinicalOutcome?: string;
  followUpRequired: boolean;
  referralQuality: number; // 1-5 scale
  feedback?: string;
}

// =====================================================
// SPECIALIST REFERRAL NETWORK SERVICE
// =====================================================

export class SpecialistReferralNetworkService {
  private supabase: SupabaseClient;
  private cache: Map<string, any> = new Map();
  private readonly cacheTimeout = 10 * 60 * 1000; // 10 minutes

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing for specialist referral network');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    console.log('✅ SpecialistReferralNetworkService initialized');
  }

  /**
   * Get intelligent specialist referral recommendations
   */
  async getSpecialistRecommendations(
    request: ReferralRequest
  ): Promise<ReferralRecommendation[]> {
    try {
      const startTime = performance.now();
      console.log(`🏥 Finding specialist recommendations for: ${request.condition}`);

      const cacheKey = `referrals_${request.condition}_${request.preferredLocation}_${request.urgencyLevel}`;
      
      if (this.cache.has(cacheKey)) {
        console.log('📋 Cache hit for specialist recommendations');
        return this.cache.get(cacheKey);
      }

      // Step 1: Find specialists by specialty and location
      const specialists = await this.findSpecialistsByCondition(
        request.condition,
        request.preferredLocation,
        request.maxTravelDistance
      );

      // Step 2: Filter by availability and urgency
      const availableSpecialists = await this.filterByAvailability(
        specialists,
        request.urgencyLevel
      );

      // Step 3: Calculate match scores
      const recommendations = await Promise.all(
        availableSpecialists.map(specialist => 
          this.calculateSpecialistMatch(specialist, request)
        )
      );

      // Step 4: Sort by match score and quality
      const sortedRecommendations = recommendations
        .filter(rec => rec.matchScore >= 60) // Minimum match threshold
        .sort((a, b) => {
          // Primary sort: urgency-based scoring
          if (request.urgencyLevel === 'emergency') {
            return (b.emergencyAvailable ? 100 : 0) - (a.emergencyAvailable ? 100 : 0) ||
                   b.matchScore - a.matchScore;
          }
          // Secondary sort: overall quality and match
          return (b.matchScore * 0.6 + b.qualityScore * 0.4) - 
                 (a.matchScore * 0.6 + a.qualityScore * 0.4);
        })
        .slice(0, 10); // Top 10 recommendations

      // Cache results
      this.cache.set(cacheKey, sortedRecommendations);
      setTimeout(() => this.cache.delete(cacheKey), this.cacheTimeout);

      const processingTime = performance.now() - startTime;
      console.log(`✅ Found ${sortedRecommendations.length} specialist recommendations in ${processingTime.toFixed(2)}ms`);

      return sortedRecommendations;

    } catch (error) {
      console.error('❌ Error getting specialist recommendations:', error);
      throw error;
    }
  }

  /**
   * Schedule appointment with recommended specialist
   */
  async scheduleAppointment(
    specialistId: string,
    patientId: string,
    preferredSlot: AvailableSlot,
    culturalPreferences: CulturalPreferences
  ): Promise<{
    success: boolean;
    appointmentId?: string;
    confirmationDetails?: any;
    culturalAdaptations?: string[];
    error?: string;
  }> {
    try {
      console.log(`📅 Scheduling appointment with specialist: ${specialistId}`);

      // Get specialist details
      const specialist = await this.getSpecialistById(specialistId);
      if (!specialist) {
        throw new Error('Specialist not found');
      }

      // Verify slot availability
      const slotAvailable = await this.verifySlotAvailability(
        specialistId,
        preferredSlot
      );

      if (!slotAvailable) {
        return {
          success: false,
          error: 'Selected time slot is no longer available'
        };
      }

      // Create appointment record
      const { data: appointment, error } = await this.supabase
        .from('specialist_appointments')
        .insert({
          specialist_id: specialistId,
          patient_id: patientId,
          appointment_date: preferredSlot.date,
          appointment_time: preferredSlot.time,
          appointment_type: preferredSlot.type,
          duration: preferredSlot.duration,
          cost: preferredSlot.cost,
          cultural_preferences: culturalPreferences,
          status: 'scheduled',
          created_at: new Date()
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating appointment:', error);
        throw error;
      }

      // Generate cultural adaptations for appointment
      const culturalAdaptations = await this.generateAppointmentCulturalAdaptations(
        specialist,
        culturalPreferences
      );

      // Send confirmation (simulated - in production would use real communication services)
      const confirmationDetails = {
        appointmentId: appointment.id,
        specialistName: specialist.name,
        facilityName: specialist.location.facilityName,
        date: preferredSlot.date,
        time: preferredSlot.time,
        type: preferredSlot.type,
        cost: preferredSlot.cost,
        contactInfo: specialist.contactInfo,
        culturalNotes: culturalAdaptations
      };

      console.log(`✅ Appointment scheduled successfully: ${appointment.id}`);

      return {
        success: true,
        appointmentId: appointment.id,
        confirmationDetails,
        culturalAdaptations
      };

    } catch (error) {
      console.error('❌ Error scheduling appointment:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Track referral outcomes and quality
   */
  async trackReferralOutcome(
    referralId: string,
    outcome: Partial<ReferralOutcome>
  ): Promise<void> {
    try {
      console.log(`📊 Tracking referral outcome: ${referralId}`);

      const { error } = await this.supabase
        .from('referral_outcomes')
        .upsert({
          referral_id: referralId,
          ...outcome,
          updated_at: new Date()
        });

      if (error) {
        console.error('❌ Error tracking referral outcome:', error);
        throw error;
      }

      // Update specialist quality metrics
      if (outcome.specialistId && outcome.referralQuality) {
        await this.updateSpecialistQualityMetrics(
          outcome.specialistId,
          outcome.referralQuality,
          outcome.patientSatisfaction
        );
      }

      console.log(`✅ Referral outcome tracked successfully`);

    } catch (error) {
      console.error('❌ Error tracking referral outcome:', error);
      throw error;
    }
  }

  /**
   * Get specialist availability in real-time
   */
  async getSpecialistAvailability(
    specialistId: string,
    dateRange: { start: Date; end: Date }
  ): Promise<AvailableSlot[]> {
    try {
      const { data: availability, error } = await this.supabase
        .from('specialist_availability')
        .select('*')
        .eq('specialist_id', specialistId)
        .gte('date', dateRange.start.toISOString())
        .lte('date', dateRange.end.toISOString())
        .eq('available', true)
        .order('date', { ascending: true });

      if (error) {
        console.error('❌ Error getting specialist availability:', error);
        return [];
      }

      return (availability || []).map((slot: any) => ({
        date: new Date(slot.date),
        time: slot.time,
        type: slot.type,
        duration: slot.duration,
        cost: slot.cost
      }));

    } catch (error) {
      console.error('❌ Error getting specialist availability:', error);
      return [];
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private async findSpecialistsByCondition(
    condition: string,
    preferredLocation?: string,
    maxDistance?: number
  ): Promise<Specialist[]> {
    try {
      let query = this.supabase
        .from('specialists')
        .select(`
          *,
          specialist_locations(*),
          specialist_qualifications(*),
          specialist_availability(*)
        `)
        .eq('verified', true)
        .eq('active', true);

      // Filter by specialty based on condition
      const specialty = await this.mapConditionToSpecialty(condition);
      if (specialty) {
        query = query.or(`specialty.eq.${specialty},sub_specialties.cs.{${specialty}}`);
      }

      // Filter by location if specified
      if (preferredLocation) {
        query = query.eq('specialist_locations.country', preferredLocation);
      }

      const { data: specialists, error } = await query;

      if (error) {
        console.error('❌ Error finding specialists:', error);
        return [];
      }

      return (specialists || []).map(this.mapDatabaseSpecialistToInterface);

    } catch (error) {
      console.error('❌ Error finding specialists by condition:', error);
      return [];
    }
  }

  private async filterByAvailability(
    specialists: Specialist[],
    urgencyLevel: string
  ): Promise<Specialist[]> {
    const now = new Date();
    const urgencyThresholds = {
      'emergency': 1, // within 1 day
      'urgent': 7,    // within 1 week
      'routine': 30   // within 1 month
    };

    const maxWaitDays = urgencyThresholds[urgencyLevel] || 30;

    return specialists.filter(specialist => {
      if (urgencyLevel === 'emergency') {
        return specialist.emergencyAvailable;
      }
      
      return specialist.waitTime <= maxWaitDays;
    });
  }

  private async calculateSpecialistMatch(
    specialist: Specialist,
    request: ReferralRequest
  ): Promise<ReferralRecommendation> {
    let matchScore = 0;
    const matchReasons: string[] = [];

    // Specialty match (40% weight)
    const specialtyMatch = await this.calculateSpecialtyMatch(specialist, request.condition);
    matchScore += specialtyMatch * 0.4;
    if (specialtyMatch > 80) {
      matchReasons.push('Excellent specialty match');
    }

    // Cultural compatibility (25% weight)
    const culturalCompatibility = this.calculateCulturalCompatibility(
      specialist,
      request.culturalPreferences
    );
    matchScore += culturalCompatibility * 0.25;
    if (culturalCompatibility > 80) {
      matchReasons.push('High cultural compatibility');
    }

    // Availability match (20% weight)
    const availabilityMatch = this.calculateAvailabilityMatch(specialist, request.urgencyLevel);
    matchScore += availabilityMatch * 0.2;
    if (availabilityMatch > 80) {
      matchReasons.push('Good availability');
    }

    // Location convenience (10% weight)
    const locationMatch = await this.calculateLocationMatch(
      specialist,
      request.preferredLocation,
      request.maxTravelDistance
    );
    matchScore += locationMatch * 0.1;

    // Quality and rating (5% weight)
    const qualityScore = specialist.rating * 20; // Convert 5-star to 100-point scale
    matchScore += qualityScore * 0.05;

    // Get available slots
    const availableSlots = await this.getSpecialistAvailability(
      specialist.id,
      {
        start: new Date(),
        end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      }
    );

    // Calculate estimated cost
    const estimatedCost = this.calculateEstimatedCost(specialist, request);

    // Check insurance coverage
    const insuranceCovered = this.checkInsuranceCoverage(specialist, request.insuranceInfo);

    return {
      specialist,
      matchScore: Math.round(matchScore),
      matchReasons,
      estimatedWaitTime: specialist.waitTime,
      estimatedCost,
      availableSlots: availableSlots.slice(0, 5), // Next 5 available slots
      culturalCompatibility,
      qualityScore,
      distanceFromPatient: await this.calculateDistance(specialist, request.preferredLocation),
      telemedicineOption: specialist.telemedicineCapable && request.telemedicinePreference,
      emergencyAvailable: specialist.emergencyAvailable,
      insuranceCovered,
      specialNotes: this.generateSpecialNotes(specialist, request)
    };
  }

  private async mapConditionToSpecialty(condition: string): Promise<string | null> {
    const conditionSpecialtyMap: { [key: string]: string } = {
      'heart': 'cardiology',
      'cardiac': 'cardiology',
      'hypertension': 'cardiology',
      'diabetes': 'endocrinology',
      'thyroid': 'endocrinology',
      'skin': 'dermatology',
      'rash': 'dermatology',
      'mental': 'psychiatry',
      'depression': 'psychiatry',
      'anxiety': 'psychiatry',
      'pregnancy': 'obstetrics_gynecology',
      'gynecology': 'obstetrics_gynecology',
      'bone': 'orthopedics',
      'joint': 'orthopedics',
      'fracture': 'orthopedics',
      'eye': 'ophthalmology',
      'vision': 'ophthalmology',
      'ear': 'ent',
      'hearing': 'ent',
      'throat': 'ent',
      'kidney': 'nephrology',
      'liver': 'gastroenterology',
      'stomach': 'gastroenterology',
      'lung': 'pulmonology',
      'breathing': 'pulmonology',
      'cancer': 'oncology',
      'tumor': 'oncology'
    };

    const lowerCondition = condition.toLowerCase();
    for (const [keyword, specialty] of Object.entries(conditionSpecialtyMap)) {
      if (lowerCondition.includes(keyword)) {
        return specialty;
      }
    }

    return 'general_medicine'; // Default to general medicine
  }

  private calculateCulturalCompatibility(
    specialist: Specialist,
    preferences: CulturalPreferences
  ): number {
    let score = 70; // Base score

    // Language compatibility
    if (specialist.languagesSpoken.includes(preferences.culturalBackground)) {
      score += 15;
    }

    // Cultural competencies
    if (specialist.culturalCompetencies.includes(preferences.culturalBackground)) {
      score += 10;
    }

    // Gender preference
    if (preferences.genderPreference && preferences.genderPreference !== 'no_preference') {
      // This would need to be added to specialist profile
      score += 5; // Assume some compatibility
    }

    return Math.min(100, score);
  }

  private calculateAvailabilityMatch(specialist: Specialist, urgencyLevel: string): number {
    const urgencyScores = {
      'emergency': specialist.emergencyAvailable ? 100 : 20,
      'urgent': specialist.waitTime <= 7 ? 90 : 50,
      'routine': specialist.waitTime <= 30 ? 80 : 60
    };

    return urgencyScores[urgencyLevel] || 60;
  }

  private async calculateLocationMatch(
    specialist: Specialist,
    preferredLocation?: string,
    maxDistance?: number
  ): Promise<number> {
    if (!preferredLocation) return 70; // Neutral score

    if (specialist.location.country === preferredLocation) {
      return 90;
    }

    // Could implement actual distance calculation here
    return 50;
  }

  private async calculateSpecialtyMatch(specialist: Specialist, condition: string): Promise<number> {
    const expectedSpecialty = await this.mapConditionToSpecialty(condition);
    
    if (specialist.specialty === expectedSpecialty) {
      return 95;
    }

    if (specialist.subSpecialties.includes(expectedSpecialty)) {
      return 85;
    }

    if (specialist.specialty === 'general_medicine') {
      return 70;
    }

    return 50;
  }

  private calculateEstimatedCost(specialist: Specialist, request: ReferralRequest): number {
    if (request.telemedicinePreference && specialist.telemedicineCapable) {
      return specialist.consultationFees.telemedicineConsultation;
    }

    return specialist.consultationFees.initialConsultation;
  }

  private checkInsuranceCoverage(specialist: Specialist, insurance?: InsuranceInfo): boolean {
    if (!insurance) return false;
    return specialist.insuranceAccepted.includes(insurance.provider);
  }

  private async calculateDistance(specialist: Specialist, location?: string): Promise<number> {
    // Simplified distance calculation - in production would use actual geolocation
    return Math.random() * 50; // 0-50 km
  }

  private generateSpecialNotes(specialist: Specialist, request: ReferralRequest): string[] {
    const notes: string[] = [];

    if (specialist.telemedicineCapable && request.telemedicinePreference) {
      notes.push('Telemedicine consultation available');
    }

    if (specialist.emergencyAvailable && request.urgencyLevel === 'emergency') {
      notes.push('Emergency appointments available');
    }

    if (specialist.languagesSpoken.length > 1) {
      notes.push(`Speaks: ${specialist.languagesSpoken.join(', ')}`);
    }

    return notes;
  }

  private async getSpecialistById(specialistId: string): Promise<Specialist | null> {
    try {
      const { data: specialist, error } = await this.supabase
        .from('specialists')
        .select('*')
        .eq('id', specialistId)
        .single();

      if (error || !specialist) {
        return null;
      }

      return this.mapDatabaseSpecialistToInterface(specialist);

    } catch (error) {
      console.error('❌ Error getting specialist by ID:', error);
      return null;
    }
  }

  private async verifySlotAvailability(
    specialistId: string,
    slot: AvailableSlot
  ): Promise<boolean> {
    try {
      const { data: availability, error } = await this.supabase
        .from('specialist_availability')
        .select('available')
        .eq('specialist_id', specialistId)
        .eq('date', slot.date.toISOString().split('T')[0])
        .eq('time', slot.time)
        .single();

      return !error && availability?.available === true;

    } catch (error) {
      console.error('❌ Error verifying slot availability:', error);
      return false;
    }
  }

  private async generateAppointmentCulturalAdaptations(
    specialist: Specialist,
    preferences: CulturalPreferences
  ): Promise<string[]> {
    const adaptations: string[] = [];

    if (preferences.familyInvolvementLevel === 'high') {
      adaptations.push('Family members welcome during consultation');
    }

    if (preferences.religiousConsiderations.length > 0) {
      adaptations.push('Religious considerations will be respected');
    }

    if (preferences.traditionalMedicineOpenness >= 3) {
      adaptations.push('Traditional medicine practices will be discussed respectfully');
    }

    return adaptations;
  }

  private async updateSpecialistQualityMetrics(
    specialistId: string,
    referralQuality: number,
    patientSatisfaction?: number
  ): Promise<void> {
    try {
      // Update specialist rating and metrics
      const { error } = await this.supabase
        .rpc('update_specialist_metrics', {
          p_specialist_id: specialistId,
          p_referral_quality: referralQuality,
          p_patient_satisfaction: patientSatisfaction
        });

      if (error) {
        console.error('❌ Error updating specialist metrics:', error);
      }

    } catch (error) {
      console.error('❌ Error updating specialist quality metrics:', error);
    }
  }

  private mapDatabaseSpecialistToInterface(dbSpecialist: any): Specialist {
    return {
      id: dbSpecialist.id,
      name: dbSpecialist.name,
      title: dbSpecialist.title,
      specialty: dbSpecialist.specialty,
      subSpecialties: dbSpecialist.sub_specialties || [],
      qualifications: dbSpecialist.qualifications || [],
      location: dbSpecialist.location || {},
      contactInfo: dbSpecialist.contact_info || {},
      availability: dbSpecialist.availability || {},
      languagesSpoken: dbSpecialist.languages_spoken || ['en'],
      culturalCompetencies: dbSpecialist.cultural_competencies || [],
      telemedicineCapable: dbSpecialist.telemedicine_capable || false,
      emergencyAvailable: dbSpecialist.emergency_available || false,
      consultationFees: dbSpecialist.consultation_fees || {},
      insuranceAccepted: dbSpecialist.insurance_accepted || [],
      rating: dbSpecialist.rating || 0,
      reviewCount: dbSpecialist.review_count || 0,
      responseTime: dbSpecialist.response_time || 24,
      waitTime: dbSpecialist.wait_time || 7,
      verified: dbSpecialist.verified || false,
      lastUpdated: new Date(dbSpecialist.updated_at || Date.now())
    };
  }

  /**
   * Clear cache for memory management
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Specialist referral network cache cleared');
  }
}

// Export singleton instance
export const specialistReferralNetworkService = new SpecialistReferralNetworkService();
