import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const WebSocketManager = ({
  websocket = null,
  connectionStatus = 'disconnected',
  connectionQuality = 'excellent',
  reconnectAttempts = 0,
  onManualReconnect = () => {},
  showAdvancedMetrics = true,
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [messageCount, setMessageCount] = useState(0);
  const [latency, setLatency] = useState(0);
  const [bandwidth, setBandwidth] = useState('Normal');

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-success-600';
      case 'connecting': return 'text-warning-600';
      case 'reconnecting': return 'text-warning-600';
      case 'disconnected': return 'text-error-600';
      default: return 'text-text-secondary';
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return 'Wifi';
      case 'connecting': return 'Loader';
      case 'reconnecting': return 'RotateCcw';
      case 'disconnected': return 'WifiOff';
      default: return 'AlertCircle';
    }
  };

  const getQualityColor = () => {
    switch (connectionQuality) {
      case 'excellent': return 'text-success-600';
      case 'good': return 'text-primary-600';
      case 'fair': return 'text-warning-600';
      case 'poor': return 'text-error-600';
      default: return 'text-text-secondary';
    }
  };

  const getQualityBars = () => {
    const bars = [];
    const levels = { excellent: 4, good: 3, fair: 2, poor: 1 };
    const activeLevel = levels[connectionQuality] || 0;
    
    for (let i = 1; i <= 4; i++) {
      bars.push(
        <div
          key={i}
          className={`w-1 rounded-full ${
            i <= activeLevel ? 'bg-success-500' : 'bg-secondary-300'
          }`}
          style={{ height: `${i * 3 + 6}px` }}
        />
      );
    }
    return bars;
  };

  React.useEffect(() => {
    if (websocket) {
      // Mock latency calculation
      const interval = setInterval(() => {
        setLatency(Math.random() * 50 + 20); // 20-70ms
        setMessageCount(prev => prev + Math.floor(Math.random() * 3));
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [websocket]);

  const handleConnectionTest = () => {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      const startTime = Date.now();
      websocket.send(JSON.stringify({
        type: 'ping',
        timestamp: startTime
      }));
      
      // Mock latency update
      setTimeout(() => {
        setLatency(Date.now() - startTime);
      }, Math.random() * 100 + 50);
    }
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <Icon 
            name={getConnectionStatusIcon()} 
            size={20} 
            className={`${getConnectionStatusColor()} ${connectionStatus === 'connecting' || connectionStatus === 'reconnecting' ? 'animate-spin' : ''}`}
          />
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              Connection Status
            </h3>
            <p className="text-sm text-text-secondary">
              Real-time audio streaming
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Connection Quality Bars */}
          <div className="flex items-end space-x-0.5">
            {getQualityBars()}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            iconName={showDetails ? "ChevronUp" : "ChevronDown"}
          >
            Details
          </Button>
        </div>
      </div>

      {/* Main Status */}
      <div className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-xs text-text-secondary mb-1">Status</div>
            <div className={`text-sm font-medium capitalize ${getConnectionStatusColor()}`}>
              {connectionStatus}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-text-secondary mb-1">Quality</div>
            <div className={`text-sm font-medium capitalize ${getQualityColor()}`}>
              {connectionQuality}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-text-secondary mb-1">Latency</div>
            <div className="text-sm font-medium text-text-primary">
              {latency.toFixed(0)}ms
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-text-secondary mb-1">Messages</div>
            <div className="text-sm font-medium text-text-primary">
              {messageCount}
            </div>
          </div>
        </div>

        {/* Connection Actions */}
        {connectionStatus === 'disconnected' && (
          <div className="text-center">
            <Button
              onClick={onManualReconnect}
              iconName="RefreshCw"
              iconPosition="left"
              size="sm"
            >
              Reconnect
            </Button>
            {reconnectAttempts > 0 && (
              <p className="text-xs text-text-secondary mt-2">
                Reconnection attempts: {reconnectAttempts}/5
              </p>
            )}
          </div>
        )}

        {connectionStatus === 'connected' && (
          <div className="text-center">
            <Button
              onClick={handleConnectionTest}
              variant="outline"
              iconName="Zap"
              iconPosition="left"
              size="sm"
            >
              Test Connection
            </Button>
          </div>
        )}
      </div>

      {/* Advanced Details */}
      {showDetails && showAdvancedMetrics && (
        <div className="border-t border-border p-4 bg-secondary-50">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-text-secondary">WebSocket State:</span>
              <span className="text-sm font-medium text-text-primary">
                {websocket ? 
                  ['Connecting', 'Open', 'Closing', 'Closed'][websocket.readyState] || 'Unknown' : 
                  'Not initialized'
                }
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-text-secondary">Protocol:</span>
              <span className="text-sm font-medium text-text-primary">
                {websocket?.protocol || 'WebSocket'}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-text-secondary">Bandwidth:</span>
              <span className="text-sm font-medium text-text-primary">
                {bandwidth}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-text-secondary">Buffered Amount:</span>
              <span className="text-sm font-medium text-text-primary">
                {websocket?.bufferedAmount || 0} bytes
              </span>
            </div>

            {/* Connection Health Indicator */}
            <div className="pt-2 border-t border-border">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-text-secondary">Connection Health</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    connectionQuality === 'excellent' ? 'bg-success-500' :
                    connectionQuality === 'good' ? 'bg-primary-500' :
                    connectionQuality === 'fair' ? 'bg-warning-500' : 'bg-error-500'
                  } animate-pulse`}></div>
                  <span className="text-sm font-medium text-text-primary capitalize">
                    {connectionQuality}
                  </span>
                </div>
              </div>
              
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${
                    connectionQuality === 'excellent' ? 'bg-success-500 w-full' :
                    connectionQuality === 'good' ? 'bg-primary-500 w-3/4' :
                    connectionQuality === 'fair' ? 'bg-warning-500 w-1/2' : 'bg-error-500 w-1/4'
                  }`}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Connection Tips */}
      {(connectionQuality === 'poor' || connectionStatus === 'disconnected') && (
        <div className="border-t border-border p-4 bg-orange-50">
          <div className="flex items-start space-x-3">
            <Icon name="AlertTriangle" size={16} className="text-orange-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-orange-800 mb-1">
                Connection Issues Detected
              </h4>
              <ul className="text-xs text-orange-700 space-y-1">
                <li>• Check your internet connection</li>
                <li>• Try moving closer to your WiFi router</li>
                <li>• Close other bandwidth-intensive applications</li>
                <li>• Consider using a wired connection for better stability</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebSocketManager;