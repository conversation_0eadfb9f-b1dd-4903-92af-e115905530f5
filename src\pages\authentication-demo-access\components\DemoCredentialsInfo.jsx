import React from 'react';
import Button from '../../../components/ui/Button';

const DemoCredentialsInfo = ({ isVisible, onClose, validCredentials = [] }) => {
  if (!isVisible) return null;

  const defaultCredentials = [
    { email: '<EMAIL>', password: 'HealthDemo123', role: 'patient' },
    { email: '<EMAIL>', password: 'ProviderDemo123', role: 'provider' },
    { email: '<EMAIL>', password: 'AdminDemo123', role: 'admin' }
  ];

  const credentials = validCredentials.length > 0 ? validCredentials : defaultCredentials;

  const copyToClipboard = (text) => {
    navigator.clipboard?.writeText?.(text);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-surface rounded-xl shadow-elevated max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-text-primary font-heading">
              Demo Account Credentials
            </h3>
            <button
              onClick={onClose}
              className="text-text-muted hover:text-text-secondary"
            >
              ✕
            </button>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-text-muted">
              Use these demo credentials to explore different user roles and features:
            </p>

            {credentials.map((cred, index) => (
              <div key={index} className="bg-background border border-border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-text-primary capitalize">
                    {cred.role} Account
                  </h4>
                  <span className={`px-2 py-1 rounded text-xs ${
                    cred.role === 'admin' ? 'bg-error-100 text-error-700' :
                    cred.role === 'provider'? 'bg-warning-100 text-warning-700' : 'bg-success-100 text-success-700'
                  }`}>
                    {cred.role}
                  </span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-text-muted">Email:</span>
                    <div className="flex items-center space-x-2">
                      <code className="bg-background px-2 py-1 rounded text-xs">
                        {cred.email}
                      </code>
                      <button
                        onClick={() => copyToClipboard(cred.email)}
                        className="text-primary hover:text-primary-hover text-xs"
                      >
                        Copy
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-text-muted">Password:</span>
                    <div className="flex items-center space-x-2">
                      <code className="bg-background px-2 py-1 rounded text-xs">
                        {cred.password}
                      </code>
                      <button
                        onClick={() => copyToClipboard(cred.password)}
                        className="text-primary hover:text-primary-hover text-xs"
                      >
                        Copy
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-3 text-xs text-text-muted">
                  {cred.role === 'patient' && 'Access patient features: consultations, profile setup, session history'}
                  {cred.role === 'provider' && 'Access provider features: patient management, consultation oversight'}
                  {cred.role === 'admin' && 'Access admin features: user management, system analytics, full dashboard'}
                </div>
              </div>
            ))}

            <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
              <h5 className="font-semibold text-primary mb-2">💡 Quick Start Tips</h5>
              <ul className="text-sm text-primary-700 space-y-1">
                <li>• Start with the <strong>Patient</strong> account to experience consultations</li>
                <li>• Use <strong>Provider</strong> account to see healthcare professional features</li>
                <li>• Try <strong>Admin</strong> account for full system management</li>
                <li>• All demo data is reset regularly</li>
              </ul>
            </div>

            <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
              <h5 className="font-semibold text-warning-700 mb-2">⚠️ Important Notes</h5>
              <ul className="text-sm text-warning-700 space-y-1">
                <li>• Demo accounts are for testing purposes only</li>
                <li>• Data may be reset without notice</li>
                <li>• Do not enter real medical information</li>
                <li>• For production use, create your own account</li>
              </ul>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="primary"
              onClick={onClose}
            >
              Got It
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoCredentialsInfo;