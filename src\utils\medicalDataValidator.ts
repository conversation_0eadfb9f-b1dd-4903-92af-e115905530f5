/**
 * HIPAA-COMPLIANT MEDICAL DATA VALIDATION SERVICE
 * 
 * This service provides comprehensive input validation and sanitization
 * for all medical data to ensure:
 * 
 * 1. Data integrity and consistency
 * 2. Prevention of XSS and injection attacks
 * 3. HIPAA compliance for medical data formats
 * 4. Standardized medical terminology validation
 * 5. Patient safety through data validation
 * 
 * SECURITY FEATURES:
 * - Input sanitization to prevent XSS
 * - SQL injection prevention
 * - Medical data format validation
 * - Length limits to prevent buffer overflows
 * - Character set restrictions for security
 * - Medical terminology validation
 */

import type {
  ValidationResult,
  MedicalCondition,
  Medication,
  Symptom,
  MedicalSeverity
} from '../types';
import { MedicalDataError } from '../types/medical';

interface MaxLengths {
  readonly condition_name: number;
  readonly medication_name: number;
  readonly notes: number;
  readonly symptoms: number;
  readonly dosage: number;
  readonly frequency: number;
  readonly allergies: number;
  readonly medical_history: number;
  readonly diagnosis: number;
  readonly treatment_plan: number;
  readonly consultation_notes: number;
  readonly patient_name: number;
  readonly email: number;
  readonly phone: number;
  readonly address: number;
}

interface AllowedPatterns {
  readonly medical_text: RegExp;
  readonly name: RegExp;
  readonly email: RegExp;
  readonly phone: RegExp;
  readonly numeric: RegExp;
  readonly alphanumeric: RegExp;
  readonly uuid: RegExp;
}

class MedicalDataValidator {
  private readonly maxLengths: MaxLengths;
  private readonly allowedPatterns: AllowedPatterns;
  private readonly validMedicalConditions: readonly string[];
  private readonly validMedications: readonly string[];
  private readonly validSeverityLevels: readonly MedicalSeverity[];

  constructor() {
    // Maximum lengths for different data types (HIPAA compliance)
    this.maxLengths = {
      condition_name: 200,
      medication_name: 200,
      notes: 2000,
      symptoms: 500,
      dosage: 100,
      frequency: 100,
      allergies: 1000,
      medical_history: 5000,
      diagnosis: 1000,
      treatment_plan: 3000,
      consultation_notes: 5000,
      patient_name: 100,
      email: 254, // RFC 5321 limit
      phone: 20,
      address: 500
    } as const;

    // Allowed characters for different field types
    this.allowedPatterns = {
      medical_text: /^[a-zA-Z0-9\s\-\.\,\(\)\[\]\/\:\;\'\"\!\?\&\%\+\=\@\#\$\*\n\r]+$/,
      name: /^[a-zA-Z\s\-\.\'\u00C0-\u017F]+$/,
      email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      phone: /^[\+]?[0-9\s\-\(\)\.]+$/,
      numeric: /^[0-9\.]+$/,
      alphanumeric: /^[a-zA-Z0-9\s\-\_]+$/,
      uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    } as const;

    // Common medical conditions for validation
    this.validMedicalConditions = [
      'hypertension', 'diabetes', 'asthma', 'arthritis', 'depression',
      'anxiety', 'migraine', 'allergies', 'heart disease', 'cancer',
      'malaria', 'tuberculosis', 'hiv', 'hepatitis', 'pneumonia',
      'bronchitis', 'sinusitis', 'gastritis', 'ulcer', 'anemia'
    ] as const;

    // Common medications for validation
    this.validMedications = [
      'paracetamol', 'ibuprofen', 'aspirin', 'amoxicillin', 'metformin',
      'lisinopril', 'atorvastatin', 'omeprazole', 'amlodipine', 'simvastatin',
      'levothyroxine', 'azithromycin', 'metoprolol', 'hydrochlorothiazide',
      'losartan', 'gabapentin', 'furosemide', 'sertraline', 'prednisone',
      'tramadol', 'ciprofloxacin', 'doxycycline', 'warfarin', 'insulin'
    ] as const;

    // Valid severity levels
    this.validSeverityLevels = ['mild', 'moderate', 'severe', 'critical'] as const;
  }

  /**
   * Sanitize input to prevent XSS and injection attacks
   */
  sanitizeInput(input: string): string {
    if (typeof input !== 'string') {
      throw new MedicalDataError('Input must be a string', 'INVALID_INPUT_TYPE', 'medium', false);
    }

    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes to prevent injection
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .replace(/\0/g, ''); // Remove null bytes
  }

  /**
   * Validate medical condition
   */
  validateCondition(condition: string): ValidationResult {
    if (!condition || typeof condition !== 'string') {
      return { valid: false, error: 'Condition name is required' };
    }

    const sanitized = this.sanitizeInput(condition);
    
    if (sanitized.length > this.maxLengths.condition_name) {
      return { valid: false, error: 'Condition name is too long' };
    }

    if (!this.allowedPatterns.medical_text.test(sanitized)) {
      return { valid: false, error: 'Condition name contains invalid characters' };
    }

    // Check if it's a known medical condition (case-insensitive)
    const isKnownCondition = this.validMedicalConditions.some(
      validCondition => validCondition.toLowerCase() === sanitized.toLowerCase()
    );

    return { 
      valid: true, 
      sanitized,
      warning: !isKnownCondition ? 'Unknown condition - please verify' : undefined
    };
  }

  /**
   * Validate medication
   */
  validateMedication(medication: string): ValidationResult {
    if (!medication || typeof medication !== 'string') {
      return { valid: false, error: 'Medication name is required' };
    }

    const sanitized = this.sanitizeInput(medication);
    
    if (sanitized.length > this.maxLengths.medication_name) {
      return { valid: false, error: 'Medication name is too long' };
    }

    if (!this.allowedPatterns.medical_text.test(sanitized)) {
      return { valid: false, error: 'Medication name contains invalid characters' };
    }

    // Check if it's a known medication (case-insensitive)
    const isKnownMedication = this.validMedications.some(
      validMed => validMed.toLowerCase() === sanitized.toLowerCase()
    );

    return { 
      valid: true, 
      sanitized,
      isKnownMedication,
      warning: !isKnownMedication ? 'Unknown medication - please verify' : undefined
    };
  }

  /**
   * Validate medical notes
   */
  validateMedicalNotes(notes: string | undefined): ValidationResult {
    if (!notes) {
      return { valid: true, sanitized: '' };
    }

    if (typeof notes !== 'string') {
      return { valid: false, error: 'Notes must be text' };
    }

    const sanitized = this.sanitizeInput(notes);
    
    if (sanitized.length > this.maxLengths.notes) {
      return { valid: false, error: 'Notes are too long' };
    }

    if (!this.allowedPatterns.medical_text.test(sanitized)) {
      return { valid: false, error: 'Notes contain invalid characters' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate severity level
   */
  validateSeverity(severity: string): ValidationResult {
    if (!severity || typeof severity !== 'string') {
      return { valid: false, error: 'Severity level is required' };
    }

    const sanitized = this.sanitizeInput(severity.toLowerCase());
    
    if (!this.validSeverityLevels.includes(sanitized as MedicalSeverity)) {
      return { 
        valid: false, 
        error: `Invalid severity level. Must be one of: ${this.validSeverityLevels.join(', ')}` 
      };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate dosage
   */
  validateDosage(dosage: string): ValidationResult {
    if (!dosage || typeof dosage !== 'string') {
      return { valid: false, error: 'Dosage is required' };
    }

    const sanitized = this.sanitizeInput(dosage);
    
    if (sanitized.length > this.maxLengths.dosage) {
      return { valid: false, error: 'Dosage description is too long' };
    }

    if (!this.allowedPatterns.medical_text.test(sanitized)) {
      return { valid: false, error: 'Dosage contains invalid characters' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate frequency
   */
  validateFrequency(frequency: string): ValidationResult {
    if (!frequency || typeof frequency !== 'string') {
      return { valid: false, error: 'Frequency is required' };
    }

    const sanitized = this.sanitizeInput(frequency);
    
    if (sanitized.length > this.maxLengths.frequency) {
      return { valid: false, error: 'Frequency description is too long' };
    }

    if (!this.allowedPatterns.medical_text.test(sanitized)) {
      return { valid: false, error: 'Frequency contains invalid characters' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate email address
   */
  validateEmail(email: string): ValidationResult {
    if (!email || typeof email !== 'string') {
      return { valid: false, error: 'Email address is required' };
    }

    const sanitized = this.sanitizeInput(email.toLowerCase());
    
    if (sanitized.length > this.maxLengths.email) {
      return { valid: false, error: 'Email address is too long' };
    }

    if (!this.allowedPatterns.email.test(sanitized)) {
      return { valid: false, error: 'Invalid email address format' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate phone number
   */
  validatePhone(phone: string): ValidationResult {
    if (!phone || typeof phone !== 'string') {
      return { valid: false, error: 'Phone number is required' };
    }

    const sanitized = this.sanitizeInput(phone);
    
    if (sanitized.length > this.maxLengths.phone) {
      return { valid: false, error: 'Phone number is too long' };
    }

    if (!this.allowedPatterns.phone.test(sanitized)) {
      return { valid: false, error: 'Invalid phone number format' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate UUID
   */
  validateUUID(uuid: string): ValidationResult {
    if (!uuid || typeof uuid !== 'string') {
      return { valid: false, error: 'UUID is required' };
    }

    const sanitized = this.sanitizeInput(uuid.toLowerCase());
    
    if (!this.allowedPatterns.uuid.test(sanitized)) {
      return { valid: false, error: 'Invalid UUID format' };
    }

    return { valid: true, sanitized };
  }

  /**
   * Validate date string (ISO format)
   */
  validateDate(dateString: string): ValidationResult {
    if (!dateString || typeof dateString !== 'string') {
      return { valid: false, error: 'Date is required' };
    }

    const sanitized = this.sanitizeInput(dateString);
    const date = new Date(sanitized);
    
    if (isNaN(date.getTime())) {
      return { valid: false, error: 'Invalid date format' };
    }

    // Check if date is not in the future (for medical history)
    if (date > new Date()) {
      return { valid: false, error: 'Date cannot be in the future' };
    }

    return { valid: true, sanitized: date.toISOString() };
  }

  /**
   * Comprehensive validation for medical condition data
   */
  validateMedicalConditionData(data: Partial<MedicalCondition>): {
    valid: boolean;
    sanitizedData?: Partial<MedicalCondition> | undefined;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const sanitizedData: any = {};

    // Validate condition name
    if (data.condition_name) {
      const conditionResult = this.validateCondition(data.condition_name);
      if (!conditionResult.valid) {
        errors.push(conditionResult.error!);
      } else {
        sanitizedData.condition_name = conditionResult.sanitized!;
        if (conditionResult.warning) {
          warnings.push(conditionResult.warning);
        }
      }
    }

    // Validate severity
    if (data.severity) {
      const severityResult = this.validateSeverity(data.severity);
      if (!severityResult.valid) {
        errors.push(severityResult.error!);
      } else {
        sanitizedData.severity = severityResult.sanitized as MedicalSeverity;
      }
    }

    // Validate notes
    if (data.notes !== undefined) {
      const notesResult = this.validateMedicalNotes(data.notes);
      if (!notesResult.valid) {
        errors.push(notesResult.error!);
      } else {
        sanitizedData.notes = notesResult.sanitized;
      }
    }

    // Validate diagnosed date
    if (data.diagnosed_date) {
      const dateResult = this.validateDate(data.diagnosed_date);
      if (!dateResult.valid) {
        errors.push(dateResult.error!);
      } else {
        sanitizedData.diagnosed_date = dateResult.sanitized!;
      }
    }

    return {
      valid: errors.length === 0,
      sanitizedData: errors.length === 0 ? sanitizedData as Partial<MedicalCondition> : undefined,
      errors,
      warnings
    };
  }
}

// Export singleton instance
const medicalDataValidator = new MedicalDataValidator();
export default medicalDataValidator;
