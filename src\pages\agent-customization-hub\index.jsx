import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import SpecialistSelectionCard from './components/SpecialistSelectionCard';
import SelectionNavigationControls from './components/SelectionNavigationControls';
import VoicePreviewPlayer from './components/VoicePreviewPlayer';
import ProgressIndicator from '../welcome-language-selection/components/ProgressIndicator';

const AgentCustomizationHub = () => {
  const navigate = useNavigate();
  const [selectedSpecialists, setSelectedSpecialists] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(false);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [currentPreviewAgent, setCurrentPreviewAgent] = useState(null);

  const minSelections = 1;
  const maxSelections = 5; // Updated to allow selection of all specialists

  // Enhanced specialists data with all 5 available specialists
  const specialists = [
    {
      id: 'gp-001',
      type: 'gp',
      name: 'Dr. <PERSON>',
      specialty: 'General Practitioner',
      avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400&h=400&fit=crop&crop=face',
      voiceProfile: {
        gender: 'female',
        accent: 'american',
        speed: 1.0,
        pitch: 1.0
      },
      expertiseFocus: ['General Health', 'Preventive Care', 'Chronic Disease Management'],
      sampleResponse: `Hello! I'm Dr. Sarah Chen, your general practitioner. I'm here to help you with your overall health and wellness. How can I assist you today?`
    },
    {
      id: 'cardio-001',
      type: 'cardiologist',
      name: 'Dr. Michael Rodriguez',
      specialty: 'Cardiologist',
      avatar: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
      voiceProfile: {
        gender: 'male',
        accent: 'british',
        speed: 0.9,
        pitch: 0.8
      },
      expertiseFocus: ['Heart Disease', 'Hypertension', 'Cardiac Rehabilitation'],
      sampleResponse: `Good day, I'm Dr. Michael Rodriguez, a cardiologist specializing in heart health. I focus on comprehensive cardiovascular care. What cardiovascular concerns would you like to discuss?`
    },
    {
      id: 'nutri-001',type: 'nutritionist',name: 'Dr. Emily Watson',specialty: 'Clinical Nutritionist',avatar: 'https://images.unsplash.com/photo-1594824804732-ca8db4dbb4c1?w=400&h=400&fit=crop&crop=face',
      voiceProfile: {
        gender: 'female',accent: 'australian',
        speed: 1.1,
        pitch: 1.2
      },
      expertiseFocus: ['Weight Management', 'Diabetes Nutrition', 'Sports Nutrition'],
      sampleResponse: `Hi there! I'm Dr. Emily Watson, your clinical nutritionist. I'm passionate about helping you achieve your health goals through personalized nutrition strategies. What nutrition goals are you hoping to achieve?`
    },
    {
      id: 'psych-001',type: 'clinical-psychologist',name: 'Dr. James Thompson',specialty: 'Clinical Psychologist',avatar: 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=400&fit=crop&crop=face',
      voiceProfile: {
        gender: 'male',accent: 'canadian',
        speed: 0.9,
        pitch: 0.9
      },
      expertiseFocus: ['Anxiety Management', 'Depression Support', 'Stress Reduction', 'Mental Wellness'],
      sampleResponse: `Hello, I'm Dr. James Thompson, a clinical psychologist. I specialize in mental health and emotional wellbeing. I'm here to provide support and evidence-based strategies for your mental health journey.`
    },
    {
      id: 'pedia-001',type: 'paediatrician',name: 'Dr. Maria Santos',specialty: 'Paediatrician',avatar: 'https://images.unsplash.com/photo-1651008376811-b90baee60c1f?w=400&h=400&fit=crop&crop=face',
      voiceProfile: {
        gender: 'female',accent: 'american',
        speed: 1.0,
        pitch: 1.1
      },
      expertiseFocus: ['Child Development', 'Vaccinations', 'Childhood Illnesses', 'Growth Monitoring'],
      sampleResponse: `Hi! I'm Dr. Maria Santos, a paediatrician specializing in children's health. I love working with families to ensure children grow up healthy and strong. How can I help with your child's health today?`
    },
    {
      id: 'derm-001',
      type: 'dermatologist',
      name: 'Dr. David Kim',
      specialty: 'Dermatologist',
      avatar: 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=400&h=400&fit=crop&crop=face',
      voiceProfile: {
        gender: 'male',
        accent: 'american',
        speed: 1.0,
        pitch: 0.9
      },
      expertiseFocus: ['Acne Treatment', 'Skin Conditions', 'Cosmetic Dermatology', 'Skin Cancer Screening'],
      sampleResponse: `Hello, I'm Dr. David Kim, your dermatologist. I specialize in comprehensive skin care, from medical conditions to cosmetic concerns. What skin health questions can I help you with?`
    }
  ];

  useEffect(() => {
    // Get language from localStorage
    const language = localStorage.getItem('selectedLanguage') || 'en';
    setSelectedLanguage(language);

    // Load any previously selected specialists
    const savedSpecialists = localStorage.getItem('selectedSpecialists');
    if (savedSpecialists) {
      try {
        setSelectedSpecialists(JSON.parse(savedSpecialists));
      } catch (error) {
        console.error('Error parsing saved specialists:', error);
      }
    }
  }, []);

  const getLocalizedText = (key) => {
    const texts = {
      title: {
        en: 'Choose Your AI Health Specialists',
        tw: 'Yi w\'AI Akwahosan Akunini',
        yo: 'Yan Awọn Amoye Ilera AI Rẹ',
        sw: 'Chagua Wataalamu wako wa Afya wa AI',
        af: 'Kies Jou AI Gesondheidspesialiste'
      },
      subtitle: {
        en: 'Select the AI specialists you\'d like to have in your healthcare team. Each specialist brings unique expertise and voice characteristics to provide personalized consultations.',
        tw: 'Yi AI akunini a wobɛpɛ sɛ wɔwɔ w\'akwahosan kuo mu. Okunini biara de nimdeɛ soronko ne nne su ba ma wɔde akwankyerɛ a ɛfa wo ho ma.',
        yo: 'Yan amoye AI ti o fẹ lati ni ninu ẹgbẹ itoju ilera rẹ',
        sw: 'Chagua wataalamu ${minSelections}-${maxSelections} kwa timu yako ya afya',
        af: 'Kies ${minSelections}-${maxSelections} spesialiste vir jou gesondheidsorgspan'
      },
      selectionInfo: {
        en: `Select ${minSelections}-${maxSelections} specialists for your healthcare team`,
        tw: `Yi akunini ${minSelections}-${maxSelections} ma w\'akwahosan kuo`,
        yo: `Yan amoye ${minSelections}-${maxSelections} fun ẹgbẹ itoju ilera rẹ`,
        sw: `Chagua wataalamu ${minSelections}-${maxSelections} kwa timu yako ya afya`,
        af: `Kies ${minSelections}-${maxSelections} spesialiste vir jou gesondheidsorgspan`
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  const handleSpecialistSelect = (specialistId) => {
    setSelectedSpecialists(prev => {
      const isCurrentlySelected = prev.includes(specialistId);
      
      if (isCurrentlySelected) {
        // Remove if already selected
        return prev.filter(id => id !== specialistId);
      } else {
        // Add if not selected and under limit
        if (prev.length < maxSelections) {
          return [...prev, specialistId];
        }
        return prev; // Don't add if at max limit
      }
    });
  };

  const handleVoicePreview = (specialistId, voiceProfile) => {
    const specialist = specialists.find(s => s.id === specialistId);
    if (!specialist) return;

    if (isPreviewPlaying && currentPreviewAgent?.id === specialistId) {
      setIsPreviewPlaying(false);
      setCurrentPreviewAgent(null);
    } else {
      setCurrentPreviewAgent(specialist);
      setIsPreviewPlaying(true);
      
      // Simulate voice preview duration
      setTimeout(() => {
        setIsPreviewPlaying(false);
        setCurrentPreviewAgent(null);
      }, 8000);
    }
  };

  const handleStopPreview = () => {
    setIsPreviewPlaying(false);
    setCurrentPreviewAgent(null);
  };

  const handleNext = () => {
    if (selectedSpecialists.length < minSelections) return;
    
    setIsLoading(true);
    
    // Store selected specialists
    localStorage.setItem('selectedSpecialists', JSON.stringify(selectedSpecialists));
    
    // Navigate to next step (Patient Profile Setup)
    setTimeout(() => {
      navigate('/patient-profile-setup');
    }, 300);
  };

  const handleBack = () => {
    navigate('/health-interests-priorities');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Progress Indicator */}
      <div className="pt-8 px-4">
        <ProgressIndicator currentStep={4} totalSteps={5} />
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center space-y-4 mb-8">
          <h1 className="text-3xl font-bold text-text-primary font-heading">
            {getLocalizedText('title')}
          </h1>
          <p className="text-text-secondary leading-relaxed max-w-3xl mx-auto">
            {getLocalizedText('subtitle')}
          </p>
          <div className="inline-flex items-center space-x-2 px-4 py-2 bg-primary-50 text-primary-600 rounded-full">
            <Icon name="Info" size={16} />
            <span className="text-sm font-medium">
              {getLocalizedText('selectionInfo')}
            </span>
          </div>
        </div>

        {/* Specialists Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {specialists.map((specialist) => (
            <SpecialistSelectionCard
              key={specialist.id}
              agent={specialist}
              isSelected={selectedSpecialists.includes(specialist.id)}
              onSelect={handleSpecialistSelect}
              onVoicePreview={handleVoicePreview}
              isPreviewPlaying={isPreviewPlaying && currentPreviewAgent?.id === specialist.id}
              selectedLanguage={selectedLanguage}
            />
          ))}
        </div>

        {/* Selected Specialists Summary */}
        {selectedSpecialists.length > 0 && (
          <div className="mb-8 p-6 bg-success-50 border border-success-200 rounded-xl">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-success-500 rounded-full flex items-center justify-center">
                <Icon name="Check" size={16} color="white" />
              </div>
              <h3 className="font-semibold text-success-800">Your Selected Healthcare Team</h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {selectedSpecialists.map((specialistId) => {
                const specialist = specialists.find(s => s.id === specialistId);
                return specialist ? (
                  <div key={specialistId} className="flex items-center space-x-3 p-3 bg-white rounded-lg">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-secondary-100">
                      <img
                        src={specialist.avatar}
                        alt={specialist.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-text-primary text-sm">{specialist.name}</p>
                      <p className="text-xs text-text-secondary">{specialist.specialty}</p>
                    </div>
                  </div>
                ) : null;
              })}
            </div>
          </div>
        )}

        {/* Navigation Controls */}
        <div className="max-w-md mx-auto">
          <SelectionNavigationControls
            onBack={handleBack}
            onNext={handleNext}
            isNextDisabled={selectedSpecialists.length < minSelections}
            isLoading={isLoading}
            selectedLanguage={selectedLanguage}
            selectedCount={selectedSpecialists.length}
            minSelections={minSelections}
          />
        </div>
      </div>

      {/* Voice Preview Player */}
      {isPreviewPlaying && currentPreviewAgent && (
        <VoicePreviewPlayer
          isPlaying={isPreviewPlaying}
          currentAgent={currentPreviewAgent}
          sampleText={currentPreviewAgent.sampleResponse}
          onPlay={() => setIsPreviewPlaying(true)}
          onPause={() => setIsPreviewPlaying(false)}
          onStop={handleStopPreview}
        />
      )}
    </div>
  );
};

export default AgentCustomizationHub;