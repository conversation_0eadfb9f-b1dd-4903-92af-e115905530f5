import React, { useState } from 'react';
import { Info, DollarSign, Clock, Mic, FileText, Brain, Shield, Zap, Eye, CheckCircle, AlertCircle } from 'lucide-react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';


const TransparentPricingModel = ({ currency }) => {
  const [expandedSection, setExpandedSection] = useState(null);

  const pricingComponents = [
    {
      id: 'base-consultation',
      title: 'Base Consultation Rates',
      icon: DollarSign,
      description: 'Core pricing for different consultation types',
      items: [
        { service: 'General Health Consultation', price: { NGN: 2500, USD: 5 }, unit: 'per 15 minutes' },
        { service: 'Specialist Consultation', price: { NGN: 5000, USD: 10 }, unit: 'per 15 minutes' },
        { service: 'Emergency Consultation', price: { NGN: 7500, USD: 15 }, unit: 'per 15 minutes' },
        { service: 'Mental Health Session', price: { NGN: 4000, USD: 8 }, unit: 'per 15 minutes' },
        { service: 'Pediatric Consultation', price: { NGN: 3500, USD: 7 }, unit: 'per 15 minutes' }
      ]
    },
    {
      id: 'duration-pricing',
      title: 'Duration-Based Pricing',
      icon: Clock,
      description: 'How consultation duration affects pricing',
      items: [
        { service: 'First 15 minutes', price: { NGN: 0, USD: 0 }, unit: 'Base rate (100%)' },
        { service: 'Additional 5-minute blocks', price: { NGN: 0, USD: 0 }, unit: '+33% of base rate' },
        { service: 'Extended session (45+ min)', price: { NGN: 0, USD: 0 }, unit: '+25% efficiency discount' },
        { service: 'Quick consultation (< 10 min)', price: { NGN: 0, USD: 0 }, unit: '-20% short session discount' }
      ],
      note: 'Pricing is prorated based on actual consultation time'
    },
    {
      id: 'agent-collaboration',
      title: 'Multi-Agent Collaboration',
      icon: Brain,
      description: 'Premium charges for specialist AI agent collaboration',
      items: [
        { service: 'Single AI Agent', price: { NGN: 0, USD: 0 }, unit: 'Base rate (no additional charge)' },
        { service: 'Dual Agent Collaboration', price: { NGN: 0, USD: 0 }, unit: '+50% of base consultation rate' },
        { service: 'Multi-Agent Team (3 agents)', price: { NGN: 0, USD: 0 }, unit: '+100% of base consultation rate' },
        { service: 'Full Specialist Panel (4+ agents)', price: { NGN: 0, USD: 0 }, unit: '+150% of base consultation rate' }
      ],
      note: 'Agent collaboration provides specialized expertise and cross-validation'
    },
    {
      id: 'premium-features',
      title: 'Premium Feature Pricing',
      icon: Zap,
      description: 'Optional add-on features and their costs',
      items: [
        { service: 'Real-time Transcript Generation', price: { NGN: 500, USD: 1 }, unit: 'per session' },
        { service: 'Session Recording & Playback', price: { NGN: 750, USD: 1.5 }, unit: 'per session' },
        { service: 'AI-Generated Health Summary', price: { NGN: 1000, USD: 2 }, unit: 'per report' },
        { service: 'Digital Prescription Generation', price: { NGN: 1500, USD: 3 }, unit: 'per prescription' },
        { service: 'Follow-up Reminder System', price: { NGN: 250, USD: 0.5 }, unit: 'per reminder series' },
        { service: 'Advanced Health Analytics', price: { NGN: 2000, USD: 4 }, unit: 'per detailed report' }
      ]
    },
    {
      id: 'voice-processing',
      title: 'Voice Processing Costs',
      icon: Mic,
      description: 'Costs for voice synthesis and processing',
      items: [
        { service: 'Voice Input Processing', price: { NGN: 100, USD: 0.2 }, unit: 'per minute of audio' },
        { service: 'AI Voice Synthesis', price: { NGN: 150, USD: 0.3 }, unit: 'per minute of generated speech' },
        { service: 'Multi-language Processing', price: { NGN: 200, USD: 0.4 }, unit: 'per minute (additional)' },
        { service: 'Noise Suppression & Enhancement', price: { NGN: 75, USD: 0.15 }, unit: 'per minute (optional)' }
      ],
      note: 'Voice processing costs are automatically calculated based on session requirements'
    },
    {
      id: 'family-discounts',
      title: 'Family Account Benefits',
      icon: Shield,
      description: 'Discounts and benefits for family accounts',
      items: [
        { service: 'Family Account (2 members)', price: { NGN: 0, USD: 0 }, unit: '10% discount on all services' },
        { service: 'Family Account (3 members)', price: { NGN: 0, USD: 0 }, unit: '15% discount on all services' },
        { service: 'Family Account (4+ members)', price: { NGN: 0, USD: 0 }, unit: '20% discount on all services' },
        { service: 'Shared Credit Pool', price: { NGN: 0, USD: 0 }, unit: 'No additional fee' },
        { service: 'Family Health Dashboard', price: { NGN: 0, USD: 0 }, unit: 'Included with family plans' }
      ],
      note: 'Family discounts apply to the total consultation cost after all other charges'
    }
  ];

  const formatPrice = (price, currency) => {
    if (price === 0) return 'Included';
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price);
  };

  const pricingPrinciples = [
    {
      title: 'No Hidden Fees',
      description: 'All costs are clearly displayed before you start any consultation',
      icon: Eye
    },
    {
      title: 'Pay for What You Use',
      description: 'Consultation duration is precisely measured and charged accordingly',
      icon: Clock
    },
    {
      title: 'Transparent Billing',
      description: 'Detailed breakdown of all charges in your consultation history',
      icon: FileText
    },
    {
      title: 'Fair Pricing',
      description: 'Competitive rates with family discounts and bulk purchase benefits',
      icon: CheckCircle
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center mb-4">
          <Info className="text-blue-600 mr-3" size={24} />
          <h2 className="text-xl font-semibold text-gray-900">
            Transparent Pricing Model
          </h2>
        </div>
        <p className="text-gray-600 mb-4">
          We believe in complete pricing transparency. Below is the detailed breakdown of all costs associated with VoiceHealth AI consultations.
        </p>
        
        {/* Pricing Principles */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {pricingPrinciples.map((principle, index) => {
            const Icon = principle.icon;
            return (
              <div key={index} className="flex items-start p-3 bg-blue-50 rounded-lg">
                <Icon size={16} className="text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900">{principle.title}</h4>
                  <p className="text-xs text-blue-700 mt-1">{principle.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Pricing Components */}
      <div className="space-y-4">
        {pricingComponents.map((component) => {
          const Icon = component.icon;
          const isExpanded = expandedSection === component.id;
          
          return (
            <div key={component.id} className="bg-white rounded-lg border border-gray-200">
              <button
                onClick={() => setExpandedSection(isExpanded ? null : component.id)}
                className="w-full p-6 text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Icon size={20} className="text-blue-600 mr-3" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {component.title}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {component.description}
                      </p>
                    </div>
                  </div>
                  <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </button>

              {isExpanded && (
                <div className="px-6 pb-6 border-t border-gray-100">
                  <div className="space-y-3 mt-4">
                    {component.items.map((item, index) => (
                      <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium text-gray-900">{item.service}</span>
                          <p className="text-sm text-gray-600">{item.unit}</p>
                        </div>
                        <div className="text-right">
                          <span className="font-semibold text-gray-900">
                            {item.price ? formatPrice(item.price[currency], currency) : item.unit}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {component.note && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertCircle size={16} className="text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                        <p className="text-sm text-blue-800">{component.note}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Billing Information */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Billing & Payment Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Payment Methods</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Credit/Debit Cards (Visa, Mastercard, Verve)</li>
              <li>• Bank Transfer (Nigerian banks)</li>
              <li>• USSD Payment</li>
              <li>• Mobile Money (MTN, Airtel, Glo, 9mobile)</li>
              <li>• International cards for USD payments</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Billing Cycle</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Pay-per-consultation (immediate charge)</li>
              <li>• Credit pool system (pre-purchase credits)</li>
              <li>• Monthly subscription plans available</li>
              <li>• Family plan billing (consolidated)</li>
              <li>• Auto-renewal options</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-start">
            <CheckCircle size={16} className="text-green-600 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-green-800 mb-1">
                Satisfaction Guarantee
              </h4>
              <p className="text-sm text-green-700">
                If you're not satisfied with your consultation, contact our support team within 24 hours for a full refund or credit adjustment.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Support */}
      <div className="text-center">
        <p className="text-gray-600 mb-4">
          Have questions about our pricing? Our support team is here to help.
        </p>
        <Button
          variant="outline"
          onClick={() => {
            // Navigate to support or open chat
            console.log('Contact support');
          }}
        >
          Contact Support
        </Button>
      </div>
    </div>
  );
};

export default TransparentPricingModel;