import pwaService from './pwaService';
import encryptionService from './encryptionService';
import { supabase } from './supabaseClient';

class OfflineHealthRecordsService {
  constructor() {
    this.dbName = 'VoiceHealthRecords';
    this.dbVersion = 2; // Increment version for encryption support
    this.db = null;
    this.syncQueue = [];

    this.init();
  }

  /**
   * Get current user session token for encryption
   */
  async getSessionToken() {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      return session?.access_token || null;
    } catch (error) {
      console.error('Failed to get session token:', error);
      return null;
    }
  }

  /**
   * Determine if data contains sensitive medical information
   */
  isSensitiveData(data) {
    const sensitiveFields = [
      'condition_name', 'medication_name', 'symptoms', 'diagnosis',
      'notes', 'medical_history', 'allergies', 'dosage', 'frequency',
      'side_effects', 'consultation_notes', 'treatment_plan'
    ];

    return sensitiveFields.some(field => data.hasOwnProperty(field));
  }
  
  async init() {
    try {
      this.db = await this.openDatabase();
      console.log('Offline Health Records database initialized');
    } catch (error) {
      console.error('Failed to initialize offline database:', error);
    }
  }
  
  async openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Medical conditions store
        if (!db.objectStoreNames.contains('medicalConditions')) {
          const store = db.createObjectStore('medicalConditions', { 
            keyPath: 'id' 
          });
          store.createIndex('userId', 'user_id', { unique: false });
          store.createIndex('timestamp', 'created_at', { unique: false });
          store.createIndex('syncStatus', 'syncStatus', { unique: false });
        }
        
        // Medications store
        if (!db.objectStoreNames.contains('medications')) {
          const store = db.createObjectStore('medications', { 
            keyPath: 'id' 
          });
          store.createIndex('userId', 'user_id', { unique: false });
          store.createIndex('timestamp', 'created_at', { unique: false });
          store.createIndex('syncStatus', 'syncStatus', { unique: false });
        }
        
        // Consultation sessions store
        if (!db.objectStoreNames.contains('consultationSessions')) {
          const store = db.createObjectStore('consultationSessions', { 
            keyPath: 'id' 
          });
          store.createIndex('userId', 'patient_id', { unique: false });
          store.createIndex('timestamp', 'created_at', { unique: false });
          store.createIndex('status', 'status', { unique: false });
          store.createIndex('syncStatus', 'syncStatus', { unique: false });
        }
        
        // Conversation messages store
        if (!db.objectStoreNames.contains('conversationMessages')) {
          const store = db.createObjectStore('conversationMessages', { 
            keyPath: 'id' 
          });
          store.createIndex('sessionId', 'session_id', { unique: false });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('syncStatus', 'syncStatus', { unique: false });
        }
        
        // Session recommendations store
        if (!db.objectStoreNames.contains('sessionRecommendations')) {
          const store = db.createObjectStore('sessionRecommendations', { 
            keyPath: 'id' 
          });
          store.createIndex('sessionId', 'session_id', { unique: false });
          store.createIndex('timestamp', 'created_at', { unique: false });
          store.createIndex('syncStatus', 'syncStatus', { unique: false });
        }
        
        // Sync operations store
        if (!db.objectStoreNames.contains('syncOperations')) {
          const store = db.createObjectStore('syncOperations', { 
            keyPath: 'id',
            autoIncrement: true 
          });
          store.createIndex('type', 'type', { unique: false });
          store.createIndex('status', 'status', { unique: false });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('priority', 'priority', { unique: false });
        }
      };
    });
  }
  
  // Generic method to store data offline with HIPAA-compliant encryption
  async storeOfflineData(storeName, data, operation = 'create') {
    if (!this.db) {
      await this.init();
    }

    try {
      const sessionToken = await this.getSessionToken();
      if (!sessionToken) {
        console.warn('No session token available - storing data unencrypted');
      }

      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      // Prepare data with sync metadata
      const dataWithSync = {
        ...data,
        syncStatus: pwaService.isAppOnline() ? 'synced' : 'pending',
        lastModified: Date.now(),
        operation // 'create', 'update', 'delete'
      };

      if (operation === 'delete') {
        dataWithSync.deleted = true;
      }

      // SECURITY: Encrypt sensitive medical data before storage
      let encryptedData;
      if (sessionToken && this.isSensitiveData(data)) {
        try {
          // Encrypt the entire data object
          const encryptedContent = await encryptionService.encryptMedicalData(dataWithSync, sessionToken);
          encryptedData = {
            id: data.id, // Keep ID unencrypted for indexing
            user_id: data.user_id, // Keep user_id unencrypted for queries
            encrypted: true,
            encryptedContent: encryptedContent,
            lastModified: Date.now(),
            operation
          };
        } catch (encryptionError) {
          console.error('Encryption failed, storing unencrypted:', encryptionError);
          encryptedData = { ...dataWithSync, encrypted: false };
        }
      } else {
        encryptedData = { ...dataWithSync, encrypted: false };
      }

      await store.put(encryptedData);

      // Add to sync queue if offline
      if (!pwaService.isAppOnline()) {
        await this.addToSyncQueue(storeName, data, operation);
      }

      return { success: true, data: dataWithSync };
    } catch (error) {
      console.error(`Failed to store offline data in ${storeName}:`, error);
      return { success: false, error: error.message };
    }
  }
  
  // Generic method to retrieve data offline with HIPAA-compliant decryption
  async getOfflineData(storeName, indexName = null, keyValue = null) {
    if (!this.db) {
      await this.init();
    }

    try {
      const sessionToken = await this.getSessionToken();
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);

      let request;
      if (indexName && keyValue !== null) {
        const index = store.index(indexName);
        request = index.getAll(keyValue);
      } else {
        request = store.getAll();
      }

      return new Promise(async (resolve, reject) => {
        request.onsuccess = async () => {
          try {
            // Filter out deleted items
            const rawData = request.result.filter(item => !item.deleted);

            // SECURITY: Decrypt encrypted data
            const decryptedData = [];
            for (const item of rawData) {
              if (item.encrypted && item.encryptedContent) {
                if (sessionToken) {
                  try {
                    const decrypted = await encryptionService.decryptMedicalData(
                      item.encryptedContent,
                      sessionToken
                    );
                    decryptedData.push(decrypted);
                  } catch (decryptionError) {
                    console.error('Failed to decrypt medical data:', decryptionError);
                    // Skip corrupted data rather than failing entirely
                    continue;
                  }
                } else {
                  console.warn('No session token available for decryption');
                  // Skip encrypted data if no session token
                  continue;
                }
              } else {
                // Data is not encrypted
                decryptedData.push(item);
              }
            }

            resolve({ success: true, data: decryptedData });
          } catch (error) {
            reject(error);
          }
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error(`Failed to get offline data from ${storeName}:`, error);
      return { success: false, error: error.message };
    }
  }
  
  // Medical Conditions methods
  async storeMedicalCondition(conditionData, operation = 'create') {
    return this.storeOfflineData('medicalConditions', conditionData, operation);
  }
  
  async getMedicalConditions(userId) {
    return this.getOfflineData('medicalConditions', 'userId', userId);
  }
  
  // Medications methods
  async storeMedication(medicationData, operation = 'create') {
    return this.storeOfflineData('medications', medicationData, operation);
  }
  
  async getMedications(userId) {
    return this.getOfflineData('medications', 'userId', userId);
  }
  
  // Consultation Sessions methods
  async storeConsultationSession(sessionData, operation = 'create') {
    return this.storeOfflineData('consultationSessions', sessionData, operation);
  }
  
  async getConsultationSessions(userId) {
    return this.getOfflineData('consultationSessions', 'userId', userId);
  }
  
  // Conversation Messages methods
  async storeConversationMessage(messageData, operation = 'create') {
    return this.storeOfflineData('conversationMessages', messageData, operation);
  }
  
  async getConversationMessages(sessionId) {
    return this.getOfflineData('conversationMessages', 'sessionId', sessionId);
  }
  
  // Session Recommendations methods
  async storeSessionRecommendation(recommendationData, operation = 'create') {
    return this.storeOfflineData('sessionRecommendations', recommendationData, operation);
  }
  
  async getSessionRecommendations(sessionId) {
    return this.getOfflineData('sessionRecommendations', 'sessionId', sessionId);
  }
  
  // Sync queue management
  async addToSyncQueue(dataType, data, operation) {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['syncOperations'], 'readwrite');
      const store = transaction.objectStore('syncOperations');
      
      const syncOperation = {
        type: dataType,
        operation,
        data,
        status: 'pending',
        timestamp: Date.now(),
        priority: this.getSyncPriority(dataType, operation),
        retryCount: 0,
        maxRetries: 3
      };
      
      await store.add(syncOperation);
      
      // Notify PWA service about pending sync
      pwaService.addPendingAction({
        type: dataType,
        operation,
        data,
        url: this.getApiUrl(dataType, operation),
        method: this.getHttpMethod(operation)
      });
      
    } catch (error) {
      console.error('Failed to add to sync queue:', error);
    }
  }
  
  // Get pending sync operations
  async getPendingSyncOperations() {
    if (!this.db) return [];
    
    try {
      const transaction = this.db.transaction(['syncOperations'], 'readonly');
      const store = transaction.objectStore('syncOperations');
      const index = store.index('status');
      
      return new Promise((resolve, reject) => {
        const request = index.getAll('pending');
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to get pending sync operations:', error);
      return [];
    }
  }
  
  // Process sync queue
  async processSyncQueue() {
    if (!pwaService.isAppOnline()) return;
    
    const pendingOperations = await this.getPendingSyncOperations();
    
    for (const operation of pendingOperations) {
      try {
        await this.processSyncOperation(operation);
        await this.updateSyncOperationStatus(operation.id, 'completed');
      } catch (error) {
        console.error('Sync operation failed:', error);
        await this.handleSyncFailure(operation);
      }
    }
  }
  
  // Process individual sync operation
  async processSyncOperation(operation) {
    const { type, operation: op, data } = operation;
    const url = this.getApiUrl(type, op);
    const method = this.getHttpMethod(op);
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: method !== 'GET' ? JSON.stringify(data) : undefined
    });
    
    if (!response.ok) {
      throw new Error(`Sync failed: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    // Update local data with server response if needed
    if (result.success && result.data) {
      await this.updateLocalDataAfterSync(type, result.data);
    }
    
    return result;
  }
  
  // Update local data after successful sync
  async updateLocalDataAfterSync(dataType, serverData) {
    const storeName = this.getStoreName(dataType);
    if (!storeName) return;
    
    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      
      const updatedData = {
        ...serverData,
        syncStatus: 'synced',
        lastModified: Date.now()
      };
      
      await store.put(updatedData);
    } catch (error) {
      console.error('Failed to update local data after sync:', error);
    }
  }
  
  // Handle sync failures
  async handleSyncFailure(operation) {
    const retryCount = (operation.retryCount || 0) + 1;
    
    if (retryCount < operation.maxRetries) {
      // Retry later
      await this.updateSyncOperationStatus(operation.id, 'pending', {
        retryCount,
        nextRetry: Date.now() + (retryCount * 30000) // Exponential backoff
      });
    } else {
      // Mark as failed
      await this.updateSyncOperationStatus(operation.id, 'failed', {
        retryCount,
        failedAt: Date.now()
      });
    }
  }
  
  // Update sync operation status
  async updateSyncOperationStatus(operationId, status, additionalData = {}) {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['syncOperations'], 'readwrite');
      const store = transaction.objectStore('syncOperations');
      
      const operation = await store.get(operationId);
      if (operation) {
        const updatedOperation = {
          ...operation,
          status,
          ...additionalData,
          updatedAt: Date.now()
        };
        await store.put(updatedOperation);
      }
    } catch (error) {
      console.error('Failed to update sync operation status:', error);
    }
  }
  
  // Utility methods
  getSyncPriority(dataType, operation) {
    const priorities = {
      'medicalConditions': { create: 2, update: 2, delete: 1 },
      'medications': { create: 2, update: 2, delete: 1 },
      'consultationSessions': { create: 3, update: 3, delete: 2 },
      'conversationMessages': { create: 1, update: 1, delete: 1 },
      'sessionRecommendations': { create: 1, update: 1, delete: 1 }
    };
    
    return priorities[dataType]?.[operation] || 1;
  }
  
  getApiUrl(dataType, operation) {
    const baseUrls = {
      'medicalConditions': '/api/medical-conditions',
      'medications': '/api/medications',
      'consultationSessions': '/api/consultation-sessions',
      'conversationMessages': '/api/conversation-messages',
      'sessionRecommendations': '/api/session-recommendations'
    };
    
    return baseUrls[dataType] || '/api/unknown';
  }
  
  getHttpMethod(operation) {
    const methods = {
      'create': 'POST',
      'update': 'PUT',
      'delete': 'DELETE'
    };
    
    return methods[operation] || 'POST';
  }
  
  getStoreName(dataType) {
    const storeNames = {
      'medicalConditions': 'medicalConditions',
      'medications': 'medications',
      'consultationSessions': 'consultationSessions',
      'conversationMessages': 'conversationMessages',
      'sessionRecommendations': 'sessionRecommendations'
    };
    
    return storeNames[dataType];
  }
  
  // Clear all offline data
  async clearAllOfflineData() {
    if (!this.db) return;
    
    const storeNames = [
      'medicalConditions',
      'medications', 
      'consultationSessions',
      'conversationMessages',
      'sessionRecommendations',
      'syncOperations'
    ];
    
    try {
      const transaction = this.db.transaction(storeNames, 'readwrite');
      
      for (const storeName of storeNames) {
        const store = transaction.objectStore(storeName);
        await store.clear();
      }
      
      console.log('All offline data cleared');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
    }
  }
  
  // Get storage usage info
  async getStorageInfo() {
    if (!this.db) return null;
    
    try {
      const storeNames = [
        'medicalConditions',
        'medications',
        'consultationSessions', 
        'conversationMessages',
        'sessionRecommendations'
      ];
      
      const info = {};
      
      for (const storeName of storeNames) {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const countRequest = store.count();
        
        info[storeName] = await new Promise((resolve, reject) => {
          countRequest.onsuccess = () => resolve(countRequest.result);
          countRequest.onerror = () => reject(countRequest.error);
        });
      }
      
      return info;
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return null;
    }
  }
}

// Create singleton instance
const offlineHealthRecordsService = new OfflineHealthRecordsService();

export default offlineHealthRecordsService;