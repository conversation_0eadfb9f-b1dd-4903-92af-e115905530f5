# SECURE BACKEND ENVIRONMENT VARIABLES
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# Server Configuration
NODE_ENV=development
PORT=3001

# Frontend URLs (for CORS)
FRONTEND_URL=http://localhost:4028
PRODUCTION_URL=https://your-production-domain.com

# Paystack Configuration (KEEP SECRET)
PAYSTACK_SECRET_KEY=sk_test_your_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_test_your_public_key_here

# Supabase Configuration (Service Role Key - KEEP SECRET)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Security Configuration
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key

# Monitoring & Logging
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
PAYMENT_RATE_LIMIT_MAX=10
