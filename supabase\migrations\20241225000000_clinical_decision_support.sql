-- Clinical Decision Support & Cultural Enhancement Database Schema
-- VoiceHealth AI - Phase 1 Implementation
-- Created: 2025-01-06

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CLINICAL EVIDENCE & DECISION SUPPORT TABLES
-- =====================================================

-- Extend existing medical_documents table with clinical evidence metadata
DO $$ 
BEGIN
    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medical_documents' AND column_name = 'evidence_level') THEN
        ALTER TABLE medical_documents ADD COLUMN evidence_level VARCHAR(1) CHECK (evidence_level IN ('A', 'B', 'C', 'D'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medical_documents' AND column_name = 'cultural_context') THEN
        ALTER TABLE medical_documents ADD COLUMN cultural_context TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medical_documents' AND column_name = 'region_applicability') THEN
        ALTER TABLE medical_documents ADD COLUMN region_applicability TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medical_documents' AND column_name = 'specialty') THEN
        ALTER TABLE medical_documents ADD COLUMN specialty VARCHAR(100);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medical_documents' AND column_name = 'traditional_medicine_refs') THEN
        ALTER TABLE medical_documents ADD COLUMN traditional_medicine_refs TEXT[];
    END IF;
END $$;

-- Clinical pathways table for evidence-based treatment protocols
CREATE TABLE IF NOT EXISTS clinical_pathways (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    condition VARCHAR(200) NOT NULL,
    region VARCHAR(10) NOT NULL,
    pathway_steps JSONB NOT NULL,
    evidence_level VARCHAR(1) NOT NULL CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    cultural_adaptations JSONB DEFAULT '{}'::JSONB,
    emergency_protocols JSONB DEFAULT '{}'::JSONB,
    traditional_medicine_considerations JSONB DEFAULT '{}'::JSONB,
    specialty VARCHAR(100),
    age_group VARCHAR(50) DEFAULT 'adult',
    gender_specific VARCHAR(20) DEFAULT 'all',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- AFRICAN PHARMACOLOGY & DRUG INTERACTION TABLES
-- =====================================================

-- African population pharmacogenetics data
CREATE TABLE IF NOT EXISTS african_pharmacogenetics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    population VARCHAR(50) NOT NULL, -- West_African, East_African, Southern_African
    gene VARCHAR(20) NOT NULL, -- CYP2D6, CYP2C19, etc.
    variant VARCHAR(100) NOT NULL,
    frequency DECIMAL(5,4) NOT NULL CHECK (frequency >= 0 AND frequency <= 1),
    metabolizer_status VARCHAR(20) NOT NULL, -- poor, intermediate, extensive, ultra-rapid
    clinical_significance TEXT,
    evidence_level VARCHAR(1) NOT NULL CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    source_study VARCHAR(200),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comprehensive drug interactions including traditional medicine
CREATE TABLE IF NOT EXISTS drug_interactions_african (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    drug_a VARCHAR(200) NOT NULL,
    drug_b VARCHAR(200),
    traditional_remedy VARCHAR(200),
    interaction_type VARCHAR(50) NOT NULL, -- pharmacokinetic, pharmacodynamic, additive, etc.
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('mild', 'moderate', 'severe', 'contraindicated')),
    mechanism TEXT,
    clinical_effects TEXT,
    clinical_management TEXT,
    population_specific BOOLEAN DEFAULT FALSE,
    genetic_variants TEXT[],
    evidence_level VARCHAR(1) NOT NULL CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    source_reference VARCHAR(300),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure at least one of drug_b or traditional_remedy is specified
    CONSTRAINT check_interaction_pair CHECK (
        (drug_b IS NOT NULL AND traditional_remedy IS NULL) OR
        (drug_b IS NULL AND traditional_remedy IS NOT NULL) OR
        (drug_b IS NOT NULL AND traditional_remedy IS NOT NULL)
    )
);

-- Population-specific dosing guidelines
CREATE TABLE IF NOT EXISTS african_dosing_guidelines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    medication VARCHAR(200) NOT NULL,
    population VARCHAR(50) NOT NULL,
    indication VARCHAR(200),
    standard_dose VARCHAR(100) NOT NULL,
    adjusted_dose VARCHAR(100) NOT NULL,
    adjustment_factor DECIMAL(3,2) NOT NULL,
    adjustment_reason TEXT,
    genetic_basis TEXT[],
    age_group VARCHAR(50) DEFAULT 'adult',
    weight_range VARCHAR(50),
    evidence_level VARCHAR(1) NOT NULL CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    contraindications TEXT[],
    monitoring_requirements TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TRADITIONAL MEDICINE DATABASE
-- =====================================================

-- Traditional remedies with safety profiles
CREATE TABLE IF NOT EXISTS traditional_remedies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    scientific_name VARCHAR(200),
    local_names JSONB NOT NULL DEFAULT '{}'::JSONB, -- {"twi": "name", "swahili": "name", etc.}
    regions TEXT[] NOT NULL,
    plant_parts_used TEXT[],
    active_compounds TEXT[],
    traditional_uses TEXT[],
    preparation_methods TEXT[],
    dosage_guidelines TEXT[],
    scientific_evidence JSONB DEFAULT '{}'::JSONB,
    safety_profile JSONB NOT NULL DEFAULT '{}'::JSONB,
    contraindications TEXT[],
    side_effects TEXT[],
    drug_interactions JSONB DEFAULT '{}'::JSONB,
    pregnancy_safety VARCHAR(20) DEFAULT 'unknown', -- safe, caution, avoid, unknown
    breastfeeding_safety VARCHAR(20) DEFAULT 'unknown',
    pediatric_safety VARCHAR(20) DEFAULT 'unknown',
    evidence_level VARCHAR(1) CHECK (evidence_level IN ('A', 'B', 'C', 'D')),
    traditional_healer_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Traditional healers network
CREATE TABLE IF NOT EXISTS traditional_healers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    location JSONB NOT NULL, -- {"country": "GH", "region": "Greater Accra", "city": "Accra"}
    specialties TEXT[],
    languages_spoken TEXT[],
    certification_status VARCHAR(50),
    collaboration_level VARCHAR(20) CHECK (collaboration_level IN ('none', 'referral', 'consultation', 'partnership')),
    contact_info JSONB DEFAULT '{}'::JSONB,
    availability_schedule JSONB DEFAULT '{}'::JSONB,
    consultation_fees JSONB DEFAULT '{}'::JSONB,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED CULTURAL PROFILES
-- =====================================================

-- Comprehensive cultural profiles for personalized care
CREATE TABLE IF NOT EXISTS enhanced_cultural_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    culture_code VARCHAR(10) NOT NULL,
    language_preference VARCHAR(10) NOT NULL,
    secondary_languages TEXT[],
    communication_style JSONB NOT NULL DEFAULT '{}'::JSONB,
    religious_considerations JSONB DEFAULT '{}'::JSONB,
    family_involvement_preferences JSONB DEFAULT '{}'::JSONB,
    traditional_medicine_openness INTEGER CHECK (traditional_medicine_openness BETWEEN 1 AND 5),
    gender_sensitivity_level INTEGER CHECK (gender_sensitivity_level BETWEEN 1 AND 5),
    authority_respect_level INTEGER CHECK (authority_respect_level BETWEEN 1 AND 5),
    health_literacy_level VARCHAR(20) CHECK (health_literacy_level IN ('basic', 'intermediate', 'advanced')),
    preferred_explanation_style VARCHAR(20) CHECK (preferred_explanation_style IN ('technical', 'simplified', 'storytelling', 'visual')),
    dietary_restrictions JSONB DEFAULT '{}'::JSONB,
    cultural_taboos TEXT[],
    preferred_consultation_style VARCHAR(20) CHECK (preferred_consultation_style IN ('direct', 'indirect', 'collaborative')),
    emergency_contact_hierarchy JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique profile per user
    UNIQUE(user_id)
);

-- =====================================================
-- MEDICAL TERMINOLOGY TRANSLATIONS
-- =====================================================

-- Multi-language medical terminology database
CREATE TABLE IF NOT EXISTS medical_term_translations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    english_term VARCHAR(200) NOT NULL,
    category VARCHAR(100), -- anatomy, symptoms, conditions, procedures, etc.
    translations JSONB NOT NULL DEFAULT '{}'::JSONB,
    cultural_context TEXT,
    usage_notes TEXT,
    sensitivity_level VARCHAR(10) CHECK (sensitivity_level IN ('low', 'medium', 'high')),
    pronunciation_guide JSONB DEFAULT '{}'::JSONB,
    alternative_terms JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Clinical pathways indexes
CREATE INDEX IF NOT EXISTS idx_clinical_pathways_condition_region ON clinical_pathways(condition, region);
CREATE INDEX IF NOT EXISTS idx_clinical_pathways_evidence_level ON clinical_pathways(evidence_level);
CREATE INDEX IF NOT EXISTS idx_clinical_pathways_specialty ON clinical_pathways(specialty);

-- Drug interactions indexes
CREATE INDEX IF NOT EXISTS idx_drug_interactions_drug_a ON drug_interactions_african(drug_a);
CREATE INDEX IF NOT EXISTS idx_drug_interactions_drug_b ON drug_interactions_african(drug_b);
CREATE INDEX IF NOT EXISTS idx_drug_interactions_traditional ON drug_interactions_african(traditional_remedy);
CREATE INDEX IF NOT EXISTS idx_drug_interactions_severity ON drug_interactions_african(severity);

-- Traditional remedies indexes
CREATE INDEX IF NOT EXISTS idx_traditional_remedies_regions ON traditional_remedies USING GIN(regions);
CREATE INDEX IF NOT EXISTS idx_traditional_remedies_name ON traditional_remedies(name);
CREATE INDEX IF NOT EXISTS idx_traditional_remedies_local_names ON traditional_remedies USING GIN(local_names);

-- Cultural profiles indexes
CREATE INDEX IF NOT EXISTS idx_cultural_profiles_user ON enhanced_cultural_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_cultural_profiles_culture ON enhanced_cultural_profiles(culture_code);
CREATE INDEX IF NOT EXISTS idx_cultural_profiles_language ON enhanced_cultural_profiles(language_preference);

-- Medical documents enhanced indexes
CREATE INDEX IF NOT EXISTS idx_medical_documents_evidence ON medical_documents(evidence_level);
CREATE INDEX IF NOT EXISTS idx_medical_documents_cultural ON medical_documents USING GIN(cultural_context);
CREATE INDEX IF NOT EXISTS idx_medical_documents_region ON medical_documents USING GIN(region_applicability);

-- Medical terminology indexes
CREATE INDEX IF NOT EXISTS idx_medical_terms_english ON medical_term_translations(english_term);
CREATE INDEX IF NOT EXISTS idx_medical_terms_category ON medical_term_translations(category);
CREATE INDEX IF NOT EXISTS idx_medical_terms_translations ON medical_term_translations USING GIN(translations);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE clinical_pathways ENABLE ROW LEVEL SECURITY;
ALTER TABLE african_pharmacogenetics ENABLE ROW LEVEL SECURITY;
ALTER TABLE drug_interactions_african ENABLE ROW LEVEL SECURITY;
ALTER TABLE african_dosing_guidelines ENABLE ROW LEVEL SECURITY;
ALTER TABLE traditional_remedies ENABLE ROW LEVEL SECURITY;
ALTER TABLE traditional_healers ENABLE ROW LEVEL SECURITY;
ALTER TABLE enhanced_cultural_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_term_translations ENABLE ROW LEVEL SECURITY;

-- Basic read policies for clinical data (adjust based on your auth requirements)
CREATE POLICY "clinical_pathways_read" ON clinical_pathways FOR SELECT USING (true);
CREATE POLICY "african_pharmacogenetics_read" ON african_pharmacogenetics FOR SELECT USING (true);
CREATE POLICY "drug_interactions_read" ON drug_interactions_african FOR SELECT USING (true);
CREATE POLICY "dosing_guidelines_read" ON african_dosing_guidelines FOR SELECT USING (true);
CREATE POLICY "traditional_remedies_read" ON traditional_remedies FOR SELECT USING (true);
CREATE POLICY "traditional_healers_read" ON traditional_healers FOR SELECT USING (verified = true);
CREATE POLICY "medical_terms_read" ON medical_term_translations FOR SELECT USING (true);

-- User-specific policies for cultural profiles
CREATE POLICY "cultural_profiles_user_access" ON enhanced_cultural_profiles 
    FOR ALL USING (auth.uid() = user_id);

-- Admin policies for data management (adjust role names as needed)
CREATE POLICY "clinical_data_admin" ON clinical_pathways 
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
CREATE POLICY "pharmacology_data_admin" ON african_pharmacogenetics 
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
CREATE POLICY "traditional_medicine_admin" ON traditional_remedies 
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_clinical_pathways_updated_at 
    BEFORE UPDATE ON clinical_pathways 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_traditional_remedies_updated_at 
    BEFORE UPDATE ON traditional_remedies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_traditional_healers_updated_at 
    BEFORE UPDATE ON traditional_healers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cultural_profiles_updated_at 
    BEFORE UPDATE ON enhanced_cultural_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_terms_updated_at 
    BEFORE UPDATE ON medical_term_translations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE clinical_pathways IS 'Evidence-based clinical treatment pathways with cultural adaptations';
COMMENT ON TABLE african_pharmacogenetics IS 'Pharmacogenetic data specific to African populations';
COMMENT ON TABLE drug_interactions_african IS 'Drug interactions including traditional medicine for African populations';
COMMENT ON TABLE traditional_remedies IS 'Traditional medicine database with safety profiles and interactions';
COMMENT ON TABLE enhanced_cultural_profiles IS 'Comprehensive cultural profiles for personalized healthcare delivery';
COMMENT ON TABLE medical_term_translations IS 'Multi-language medical terminology with cultural context';

-- Migration completed successfully
SELECT 'Clinical Decision Support & Cultural Enhancement schema migration completed successfully' as status;
