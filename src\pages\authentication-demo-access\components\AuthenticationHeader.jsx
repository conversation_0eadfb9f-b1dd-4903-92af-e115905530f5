import React from 'react';
import Icon from '../../../components/AppIcon';

const AuthenticationHeader = () => {
  return (
    <div className="text-center space-y-6 mb-8">
      {/* Logo */}
      <div className="flex justify-center">
        <div className="relative">
          <div className="w-16 h-16 bg-primary-500 rounded-2xl flex items-center justify-center shadow-elevated">
            <Icon name="Activity" size={32} color="white" />
          </div>
          <div className="absolute -top-1 -right-1 w-6 h-6 bg-success-500 rounded-full flex items-center justify-center">
            <Icon name="Mic" size={12} color="white" />
          </div>
        </div>
      </div>

      {/* Brand Name */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-text-primary font-heading">
          VoiceHealth AI
        </h1>
        <p className="text-lg text-text-secondary font-body">
          Intelligent Health Consultations
        </p>
      </div>

      {/* Welcome Message */}
      <div className="max-w-md mx-auto space-y-3">
        <h2 className="text-xl font-semibold text-text-primary">
          Welcome Back
        </h2>
        <p className="text-text-secondary">
          Sign in to continue your AI-powered health consultation journey, or explore our demo to experience the future of healthcare.
        </p>
      </div>

      {/* Features Highlight */}
      <div className="grid grid-cols-3 gap-4 max-w-sm mx-auto pt-4">
        <div className="text-center space-y-2">
          <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center mx-auto">
            <Icon name="Mic" size={18} color="var(--color-primary)" />
          </div>
          <p className="text-xs text-text-secondary">Voice First</p>
        </div>
        <div className="text-center space-y-2">
          <div className="w-10 h-10 bg-success-50 rounded-lg flex items-center justify-center mx-auto">
            <Icon name="Users" size={18} color="var(--color-success)" />
          </div>
          <p className="text-xs text-text-secondary">Multi-Agent</p>
        </div>
        <div className="text-center space-y-2">
          <div className="w-10 h-10 bg-warning-50 rounded-lg flex items-center justify-center mx-auto">
            <Icon name="Shield" size={18} color="var(--color-warning)" />
          </div>
          <p className="text-xs text-text-secondary">Secure</p>
        </div>
      </div>
    </div>
  );
};

export default AuthenticationHeader;