import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CreateAccountPrompt = ({ onCreateAccount, isLoading }) => {
  const benefits = [
    {
      icon: 'UserPlus',
      title: 'Personalized Experience',
      description: 'Tailored health consultations based on your medical history'
    },
    {
      icon: 'Shield',
      title: 'Secure Data Storage',
      description: 'HIPAA-compliant storage of your health information'
    },
    {
      icon: 'History',
      title: 'Session History',
      description: 'Access all your past consultations and recommendations'
    },
    {
      icon: 'Bell',
      title: 'Smart Reminders',
      description: 'Get notified about follow-ups and health checkpoints'
    }
  ];

  return (
    <div className="bg-surface border border-border rounded-xl p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 bg-accent-50 rounded-lg flex items-center justify-center mx-auto">
          <Icon name="Sparkles" size={24} color="var(--color-accent)" />
        </div>
        <h3 className="text-lg font-semibold text-text-primary font-heading">
          New to VoiceHealth AI?
        </h3>
        <p className="text-sm text-text-secondary">
          Create your account to unlock the full potential of AI-powered healthcare
        </p>
      </div>

      {/* Benefits Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {benefits.map((benefit, index) => (
          <div key={index} className="flex items-start space-x-3 p-3 bg-secondary-50 rounded-lg">
            <div className="w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center flex-shrink-0">
              <Icon name={benefit.icon} size={16} color="var(--color-primary)" />
            </div>
            <div className="space-y-1">
              <h4 className="text-sm font-medium text-text-primary">
                {benefit.title}
              </h4>
              <p className="text-xs text-text-secondary">
                {benefit.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Call to Action */}
      <div className="space-y-4">
        <Button
          variant="secondary"
          size="lg"
          fullWidth
          onClick={onCreateAccount}
          iconName="UserPlus"
          iconPosition="left"
          disabled={isLoading}
        >
          Create Free Account
        </Button>
        
        <div className="text-center">
          <p className="text-xs text-text-muted">
            Free forever • No credit card required • 5-minute setup
          </p>
        </div>
      </div>

      {/* Trust Indicators */}
      <div className="border-t border-border pt-4">
        <div className="flex items-center justify-center space-x-6 text-xs text-text-secondary">
          <div className="flex items-center space-x-1">
            <Icon name="Shield" size={12} />
            <span>HIPAA Compliant</span>
          </div>
          <div className="flex items-center space-x-1">
            <Icon name="Lock" size={12} />
            <span>End-to-End Encrypted</span>
          </div>
          <div className="flex items-center space-x-1">
            <Icon name="Award" size={12} />
            <span>SOC 2 Certified</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateAccountPrompt;