import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import HealthInterestCard from './components/HealthInterestCard';
import SelectionCounter from './components/SelectionCounter';
import NavigationControls from './components/NavigationControls';
import HelpSection from './components/HelpSection';
import ProgressIndicator from '../welcome-language-selection/components/ProgressIndicator';

const HealthInterestsPriorities = () => {
  const navigate = useNavigate();
  const [selectedInterests, setSelectedInterests] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [isLoading, setIsLoading] = useState(false);

  const minSelections = 3;
  const maxSelections = 5;

  useEffect(() => {
    // Get language from localStorage
    const language = localStorage.getItem('selectedLanguage') || 'en';
    setSelectedLanguage(language);

    // Load any previously selected interests
    const savedInterests = localStorage.getItem('selectedHealthInterests');
    if (savedInterests) {
      try {
        setSelectedInterests(JSON.parse(savedInterests));
      } catch (error) {
        console.error('Error parsing saved interests:', error);
      }
    }
  }, []);

  const healthInterests = [
    {
      id: 'general-health',
      icon: '🩺',
      title: {
        en: 'General Health',
        tw: 'Akwahosan Ankasa',
        yo: 'Ilera Gbogbogbo',
        sw: 'Afya ya Jumla',
        af: 'Algemene Gesondheid'
      },
      description: {
        en: 'Overall wellness, preventive care, and routine checkups',
        tw: 'Akwahosan ankasa, ahwɛyɛ a ɛsi ɔyare ano, ne nhwehwɛmu a wɔyɛ daa',
        yo: 'Ilera gbogbogbo, itoju idena, ati awọn ayẹwo deede',
        sw: 'Ustawi wa jumla, utunzaji wa kuzuia, na ukaguzi wa kawaida',
        af: 'Algehele welstand, voorkomende sorg, en roetine ondersoeke'
      }
    },
    {
      id: 'heart-health',
      icon: '❤️',
      title: {
        en: 'Heart Health',
        tw: 'Akoma Akwahosan',
        yo: 'Ilera Ọkan',
        sw: 'Afya ya Moyo',
        af: 'Hart Gesondheid'
      },
      description: {
        en: 'Cardiovascular wellness, blood pressure, and heart conditions',
        tw: 'Akoma ne ntini akwahosan, mogya mframa, ne akoma yadeɛ',
        yo: 'Ilera ọkan ati iṣan ẹjẹ, titẹ ẹjẹ, ati awọn ipo ọkan',
        sw: 'Ustawi wa moyo na mishipa ya damu, shinikizo la damu, na hali za moyo',
        af: 'Kardiovaskulêre welstand, bloeddruk, en harttoestande'
      }
    },
    {
      id: 'mental-wellness',
      icon: '🧠',
      title: {
        en: 'Mental Wellness',
        tw: 'Adwene mu Ahotɔ',
        yo: 'Ilera Ọpọlọ',
        sw: 'Ustawi wa Akili',
        af: 'Geestesgesondheid'
      },
      description: {
        en: 'Mental health, stress management, and emotional wellbeing',
        tw: 'Adwene mu akwahosan, dadwen ho nhyehyɛe, ne nkateɛ ahotɔ',
        yo: 'Ilera ọpọlọ, iṣakoso wahala, ati alafia ẹdun',
        sw: 'Afya ya akili, usimamizi wa msongo wa mawazo, na ustawi wa kihisia',
        af: 'Geestesgesondheid, stresbestuur, en emosionele welstand'
      }
    },
    {
      id: 'nutrition-diet',
      icon: '🥗',
      title: {
        en: 'Nutrition & Diet',
        tw: 'Aduane pa ne Aduannoa',
        yo: 'Ounje ati Ounjẹ',
        sw: 'Lishe na Chakula',
        af: 'Voeding & Dieet'
      },
      description: {
        en: 'Healthy eating, nutrition planning, and dietary guidance',
        tw: 'Aduane pa didi, aduane pa nhyehyɛe, ne aduannoa akwankyerɛ',
        yo: 'Jijẹ to dara, eto ounje, ati itọsọna ounje',
        sw: 'Kula vizuri, upangaji wa lishe, na mwongozo wa chakula',
        af: 'Gesonde eetgewoontes, voedingsbeplanning, en dieetleiding'
      }
    },
    {
      id: 'womens-health',
      icon: '👩‍⚕️',
      title: {
        en: 'Women\'s Health',
        tw: 'Mmea Akwahosan',
        yo: 'Ilera Obinrin',
        sw: 'Afya ya Wanawake',
        af: 'Vroue Gesondheid'
      },
      description: {
        en: 'Reproductive health, pregnancy care, and women-specific concerns',
        tw: 'Awo akwahosan, nyinsɛn nhwɛsoɔ, ne mmea nneɛma pɔtee',
        yo: 'Ilera ibisi, itoju oyun, ati awọn ifiyesi pataki fun obinrin',
        sw: 'Afya ya uzazi, huduma za ujauzito, na masuala maalum ya wanawake',
        af: 'Reproduktiewe gesondheid, swangerskapssorg, en vroue-spesifieke kwessies'
      }
    },
    {
      id: 'child-health',
      icon: '👶',
      title: {
        en: 'Child Health',
        tw: 'Mmofra Akwahosan',
        yo: 'Ilera Ọmọ',
        sw: 'Afya ya Watoto',
        af: 'Kinder Gesondheid'
      },
      description: {
        en: 'Pediatric care, child development, and family health',
        tw: 'Mmofra nhwɛsoɔ, mmofra nkɔsoɔ, ne abusua akwahosan',
        yo: 'Itoju ọmọde, idagbasoke ọmọ, ati ilera ebi',
        sw: 'Huduma za kitoto, maendeleo ya mtoto, na afya ya familia',
        af: 'Pediatriese sorg, kinderontwikkeling, en familiegesondheid'
      }
    },
    {
      id: 'chronic-conditions',
      icon: '💊',
      title: {
        en: 'Chronic Conditions',
        tw: 'Yadeɛ a ɛkyɛre',
        yo: 'Awọn Ipo Arun Pipẹ',
        sw: 'Hali za Kudumu',
        af: 'Chroniese Toestande'
      },
      description: {
        en: 'Diabetes, hypertension, and long-term condition management',
        tw: 'Asikyire yadeɛ, mogya mframa soɔ, ne yadeɛ a ɛkyɛre ho nhyehyɛe',
        yo: 'Atogbe, titẹ ẹjẹ giga, ati iṣakoso awọn ipo pipẹ',
        sw: 'Kisukari, shinikizo la juu la damu, na usimamizi wa hali za muda mrefu',
        af: 'Diabetes, hipertensie, en langtermyn toestandbestuur'
      }
    },
    {
      id: 'preventive-care',
      icon: '🛡️',
      title: {
        en: 'Preventive Care',
        tw: 'Ahwɛyɛ a ɛsi ano',
        yo: 'Itoju Idena',
        sw: 'Utunzaji wa Kuzuia',
        af: 'Voorkomende Sorg'
      },
      description: {
        en: 'Vaccinations, screenings, and health maintenance',
        tw: 'Nnuru a wɔde si yadeɛ ano, nhwehwɛmu, ne akwahosan so ahwɛ',
        yo: 'Awọn abẹrẹ ajesara, ayẹwo, ati itoju ilera',
        sw: 'Chanjo, upimaji, na utunzaji wa afya',
        af: 'Inentings, sifting, en gesondheidsonderhoud'
      }
    }
  ];

  const handleInterestSelect = (interestId) => {
    setSelectedInterests(prev => {
      const isCurrentlySelected = prev.includes(interestId);
      
      if (isCurrentlySelected) {
        // Remove if already selected
        return prev.filter(id => id !== interestId);
      } else {
        // Add if not selected and under limit
        if (prev.length < maxSelections) {
          return [...prev, interestId];
        }
        return prev; // Don't add if at max limit
      }
    });
  };

  const handleNext = () => {
    if (selectedInterests.length < minSelections) return;
    
    setIsLoading(true);
    
    // Store selected interests
    localStorage.setItem('selectedHealthInterests', JSON.stringify(selectedInterests));
    
    // Navigate to next step (AI Specialist Selection)
    setTimeout(() => {
      navigate('/agent-customization-hub');
    }, 300);
  };

  const handleBack = () => {
    navigate('/country-regional-selection');
  };

  const getLocalizedText = (key) => {
    const texts = {
      title: {
        en: 'What are your health priorities?',
        tw: 'Ɛdeɛn ne w\'akwahosan nneɛma a ɛho hia?',
        yo: 'Kini ni awọn pataki ilera rẹ?',
        sw: 'Ni vipi vipaumbele vyako vya afya?',
        af: 'Wat is jou gesondheidsprioriteite?'
      },
      subtitle: {
        en: 'Select the health areas you\'d like personalized guidance and consultations for. This helps our AI specialists provide more relevant advice.',
        tw: 'Yi akwahosan mmeae a wobɛpɛ sɛ wɔma wo akwankyerɛ ne nhwehwɛmu a ɛfa wo ho. Eyi boa yɛn AI akunini ma wɔde akwankyerɛ a ɛfata ma.',
        yo: 'Yan awọn agbegbe ilera ti o fẹ itọsọna ti ara ẹni ati awọn ifọrọwanilẹnuwo fun. Eyi ṣe iranlọwọ fun awọn amoye AI wa lati pese imọran ti o yẹ.',
        sw: 'Chagua maeneo ya afya ambayo ungependa mwongozo wa kibinafsi na mashauriano. Hii inasaidia wataalamu wetu wa AI kutoa ushauri unaofaa zaidi.',
        af: 'Kies die gesondheidsareas waarvoor jy persoonlike leiding en konsultasies wil hê. Dit help ons AI-spesialiste om meer relevante advies te gee.'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Progress Indicator */}
      <div className="pt-8 px-4">
        <ProgressIndicator currentStep={3} totalSteps={5} />
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center space-y-4 mb-8">
          <h1 className="text-3xl font-bold text-text-primary font-heading">
            {getLocalizedText('title')}
          </h1>
          <p className="text-text-secondary leading-relaxed max-w-2xl mx-auto">
            {getLocalizedText('subtitle')}
          </p>
        </div>

        {/* Selection Counter */}
        <div className="mb-8">
          <SelectionCounter
            selectedCount={selectedInterests.length}
            minSelections={minSelections}
            maxSelections={maxSelections}
            selectedLanguage={selectedLanguage}
          />
        </div>

        {/* Health Interests Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          {healthInterests.map((interest) => (
            <HealthInterestCard
              key={interest.id}
              interest={interest}
              isSelected={selectedInterests.includes(interest.id)}
              onSelect={handleInterestSelect}
              selectedLanguage={selectedLanguage}
            />
          ))}
        </div>

        {/* Help Section */}
        <div className="mb-8">
          <HelpSection selectedLanguage={selectedLanguage} />
        </div>

        {/* Navigation Controls */}
        <div className="max-w-md mx-auto">
          <NavigationControls
            onBack={handleBack}
            onNext={handleNext}
            isNextDisabled={selectedInterests.length < minSelections}
            isLoading={isLoading}
            selectedLanguage={selectedLanguage}
          />
        </div>
      </div>
    </div>
  );
};

export default HealthInterestsPriorities;