import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';


const EmergencyProtocols = ({ selectedProtocol, onProtocolSelect }) => {
  const [expandedProtocol, setExpandedProtocol] = useState(null);

  const emergencyProtocols = [
    {
      id: 'cardiac',
      name: 'Cardiac Emergency',
      icon: 'Heart',
      severity: 'critical',
      symptoms: ['Chest pain', 'Difficulty breathing', 'Sweating', 'Nausea'],
      steps: [
        'Call emergency services immediately (911)',
        'Help the person sit down and rest',
        'If available, give aspirin (if not allergic)',
        'Loosen tight clothing',
        'Monitor breathing and pulse',
        'Be prepared to perform CPR if needed'
      ],
      warnings: ['Do not leave the person alone', 'Do not give food or water']
    },
    {
      id: 'stroke',
      name: 'Stroke Response',
      icon: 'Brain',
      severity: 'critical',
      symptoms: ['Face drooping', 'Arm weakness', 'Speech difficulty', 'Time is critical'],
      steps: [
        'Call emergency services immediately (911)',
        'Note the time symptoms started',
        'Keep the person calm and comfortable',
        'Do not give food, water, or medication',
        'Check breathing and pulse regularly',
        'Be ready to perform CPR if needed'
      ],
      warnings: ['Every minute counts in stroke treatment', 'Do not attempt to move the person unnecessarily']
    },
    {
      id: 'choking',
      name: 'Choking Emergency',
      icon: 'AlertTriangle',
      severity: 'critical',
      symptoms: ['Cannot speak or cough', 'Clutching throat', 'Blue lips/face'],
      steps: [
        'Stand behind the person',
        'Place arms around their waist',
        'Make a fist above the navel',
        'Give quick upward thrusts',
        'Continue until object is expelled',
        'Call emergency services if unsuccessful'
      ],
      warnings: ['For infants, use back blows and chest thrusts', 'Never put fingers in mouth to remove object']
    },
    {
      id: 'poisoning',
      name: 'Poisoning Response',
      icon: 'Shield',
      severity: 'high',
      symptoms: ['Nausea', 'Vomiting', 'Confusion', 'Difficulty breathing'],
      steps: [
        'Call Poison Control: 1-800-222-1222',
        'Identify the poison if possible',
        'Follow Poison Control instructions',
        'Do not induce vomiting unless instructed',
        'Save poison container/label',
        'Monitor breathing and consciousness'
      ],
      warnings: ['Do not give milk or water unless instructed', 'Do not induce vomiting for corrosive substances']
    },
    {
      id: 'seizure',
      name: 'Seizure Management',
      icon: 'Zap',
      severity: 'high',
      symptoms: ['Uncontrolled shaking', 'Loss of consciousness', 'Confusion after'],
      steps: [
        'Keep the person safe from injury',
        'Place something soft under their head',
        'Turn them on their side',
        'Time the seizure duration',
        'Do not put anything in their mouth',
        'Call 911 if seizure lasts >5 minutes'
      ],
      warnings: ['Never restrain someone having a seizure', 'Do not put objects in mouth']
    },
    {
      id: 'severe_bleeding',
      name: 'Severe Bleeding Control',
      icon: 'Droplet',
      severity: 'high',
      symptoms: ['Heavy bleeding', 'Blood won\'t stop', 'Visible deep cuts'],
      steps: [
        'Apply direct pressure to wound',
        'Use clean cloth or bandage',
        'Elevate injured area above heart',
        'Apply pressure bandage',
        'Call emergency services',
        'Monitor for shock signs'
      ],
      warnings: ['Do not remove embedded objects', 'Maintain pressure until help arrives']
    }
  ];

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return 'AlertCircle';
      case 'high': return 'AlertTriangle';
      default: return 'Alert';
    }
  };

  return (
    <div className="bg-surface rounded-xl shadow-minimal border border-border p-6">
      <div className="flex items-center mb-4">
        <Icon name="FileText" size={24} className="text-primary mr-3" />
        <h3 className="text-xl font-semibold text-text-primary">Emergency Protocols</h3>
      </div>

      <div className="space-y-4">
        {emergencyProtocols.map((protocol) => (
          <div key={protocol.id} className="border border-border rounded-lg overflow-hidden">
            <button
              onClick={() => {
                setExpandedProtocol(expandedProtocol === protocol.id ? null : protocol.id);
                onProtocolSelect?.(protocol);
              }}
              className={`w-full p-4 text-left transition-all duration-200 hover:bg-surface-hover ${
                selectedProtocol === protocol.id ? 'bg-primary-50 border-primary' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg mr-3 ${getSeverityColor(protocol.severity)}`}>
                    <Icon name={protocol.icon} size={20} />
                  </div>
                  <div>
                    <h4 className="font-semibold text-text-primary">{protocol.name}</h4>
                    <div className="flex items-center mt-1">
                      <Icon name={getSeverityIcon(protocol.severity)} size={12} className="mr-1" />
                      <span className="text-xs text-text-secondary uppercase tracking-wide">
                        {protocol.severity} Priority
                      </span>
                    </div>
                  </div>
                </div>
                <Icon
                  name={expandedProtocol === protocol.id ? 'ChevronUp' : 'ChevronDown'}
                  size={20}
                  className="text-text-secondary"
                />
              </div>
            </button>

            {expandedProtocol === protocol.id && (
              <div className="p-4 bg-surface-secondary border-t border-border">
                {/* Symptoms */}
                <div className="mb-4">
                  <h5 className="text-sm font-semibold text-text-primary mb-2 flex items-center">
                    <Icon name="Eye" size={16} className="mr-2" />
                    Key Symptoms
                  </h5>
                  <div className="grid grid-cols-2 gap-2">
                    {protocol.symptoms.map((symptom, index) => (
                      <div key={index} className="text-sm text-text-secondary flex items-center">
                        <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
                        {symptom}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Steps */}
                <div className="mb-4">
                  <h5 className="text-sm font-semibold text-text-primary mb-2 flex items-center">
                    <Icon name="List" size={16} className="mr-2" />
                    Emergency Steps
                  </h5>
                  <ol className="space-y-2">
                    {protocol.steps.map((step, index) => (
                      <li key={index} className="text-sm text-text-secondary flex items-start">
                        <span className="bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                          {index + 1}
                        </span>
                        {step}
                      </li>
                    ))}
                  </ol>
                </div>

                {/* Warnings */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <h5 className="text-sm font-semibold text-red-800 mb-2 flex items-center">
                    <Icon name="AlertTriangle" size={16} className="mr-2" />
                    Important Warnings
                  </h5>
                  <ul className="space-y-1">
                    {protocol.warnings.map((warning, index) => (
                      <li key={index} className="text-sm text-red-700 flex items-start">
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full mr-2 mt-2 flex-shrink-0"></div>
                        {warning}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <Icon name="Info" size={20} className="text-blue-600 mr-3 mt-0.5" />
          <div>
            <h4 className="text-sm font-semibold text-blue-800 mb-1">
              Emergency Services
            </h4>
            <p className="text-sm text-blue-700 mb-2">
              For life-threatening emergencies, always call emergency services first.
            </p>
            <div className="text-lg font-bold text-blue-800">
              📞 911 (Emergency) | 🏥 Local Hospital | ☎️ Poison Control: 1-800-222-1222
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmergencyProtocols;