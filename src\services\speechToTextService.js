/**
 * SECURE Speech-to-Text Service using Backend Proxy
 * Handles audio transcription for voice consultations via secure server-side proxy
 *
 * SECURITY FEATURES:
 * - No client-side API keys
 * - Server-side authentication and authorization
 * - HIPAA-compliant request handling
 * - Comprehensive audit logging
 */
class SpeechToTextService {
  constructor() {
    this.baseUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/speech-to-text`;
    // API key removed - handled server-side for security
  }

  /**
   * Transcribe audio blob to text using secure backend proxy
   */
  async transcribeAudio(audioBlob, options = {}) {
    try {
      console.log('🎤 Starting secure transcription...', {
        size: audioBlob.size,
        type: audioBlob.type
      });

      // Get authentication token
      const authToken = await this.getAuthToken();
      if (!authToken) {
        throw new Error('Authentication required for speech-to-text service');
      }

      // Validate session ID
      if (!options.sessionId) {
        throw new Error('Session ID is required for secure transcription');
      }

      const formData = new FormData();
      formData.append('audio', audioBlob, 'audio.webm');
      formData.append('sessionId', options.sessionId);
      formData.append('language', options.language || 'en');
      formData.append('temperature', options.temperature || 0.2);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          ...(options.emergencyOverride && { 'X-Emergency-Override': 'true' })
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Secure transcription error:', response.status, errorData);

        // Handle specific error codes
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Access denied to this session.');
        } else if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }

        throw new Error(`Transcription service error: ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Transcription failed');
      }

      console.log('✅ Secure transcription successful:', result.data.text);

      return {
        text: result.data.text,
        confidence: result.data.confidence,
        duration: result.data.duration,
        language: result.data.language,
        processingTime: result.data.processing_time
      };

    } catch (error) {
      console.error('❌ Secure transcription error:', error);

      // Enhanced error handling for secure service
      if (error.message.includes('Authentication')) {
        // Clear potentially invalid auth token
        this.clearAuthToken();
      }

      throw error;
    }
  }

  /**
   * Get authentication token from current session
   */
  async getAuthToken() {
    try {
      // Get token from Supabase auth context
      const { supabase } = await import('../utils/supabaseClient');
      const { data: { session } } = await supabase.auth.getSession();
      return session?.access_token;
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  }

  /**
   * Clear authentication token (for error recovery)
   */
  async clearAuthToken() {
    try {
      const { supabase } = await import('../utils/supabaseClient');
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Failed to clear auth token:', error);
    }
  }

  /**
   * Check if service is configured properly
   */
  isConfigured() {
    return Boolean(this.baseUrl);
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages() {
    return [
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Spanish' },
      { code: 'fr', name: 'French' },
      { code: 'de', name: 'German' },
      { code: 'it', name: 'Italian' },
      { code: 'pt', name: 'Portuguese' },
      { code: 'ru', name: 'Russian' },
      { code: 'ja', name: 'Japanese' },
      { code: 'ko', name: 'Korean' },
      { code: 'zh', name: 'Chinese' }
    ];
  }

  /**
   * Validate audio file before transcription
   */
  validateAudioFile(audioBlob) {
    const maxSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = ['audio/webm', 'audio/wav', 'audio/mp3', 'audio/m4a'];

    if (audioBlob.size > maxSize) {
      throw new Error(`Audio file too large. Maximum size is ${maxSize / (1024 * 1024)}MB`);
    }

    if (!allowedTypes.includes(audioBlob.type)) {
      throw new Error(`Unsupported audio format: ${audioBlob.type}. Supported formats: ${allowedTypes.join(', ')}`);
    }

    return true;
  }

  /**
   * Get service health status
   */
  async getHealthStatus() {
    try {
      const authToken = await this.getAuthToken();
      if (!authToken) {
        return { healthy: false, error: 'Authentication required' };
      }

      const response = await fetch(`${this.baseUrl.replace('/speech-to-text', '/health')}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return { healthy: true, data };
      } else {
        return { healthy: false, error: `Service unavailable (${response.status})` };
      }
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }
}

export default new SpeechToTextService();
