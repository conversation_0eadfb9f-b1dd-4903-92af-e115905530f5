import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const APIIntegrationSection = ({ 
  integrations = [],
  onConnectService = () => {},
  onDisconnectService = () => {},
  onConfigureService = () => {},
  className = ''
}) => {
  const [authInProgress, setAuthInProgress] = useState(null);

  const availableServices = [
    {
      id: 'apple-health',
      name: 'Apple Health',
      icon: 'Smartphone',
      description: 'Sync data from iPhone Health app',
      color: 'bg-gray-50',
      isConnected: integrations.find(i => i.id === 'apple-health')?.connected || false,
      dataTypes: ['Steps', 'Heart Rate', 'Sleep', 'Workouts']
    },
    {
      id: 'google-fit',
      name: 'Google Fit',
      icon: 'Activity',
      description: 'Connect with Google Fit ecosystem',
      color: 'bg-green-50',
      isConnected: integrations.find(i => i.id === 'google-fit')?.connected || false,
      dataTypes: ['Activity', 'Steps', 'Weight', 'Nutrition']
    },
    {
      id: 'fitbit',
      name: 'Fitbit',
      icon: 'Watch',
      description: 'Sync Fitbit device data',
      color: 'bg-blue-50',
      isConnected: integrations.find(i => i.id === 'fitbit')?.connected || false,
      dataTypes: ['Steps', 'Heart Rate', 'Sleep', 'Exercise']
    },
    {
      id: 'samsung-health',
      name: 'Samsung Health',
      icon: 'Heart',
      description: 'Samsung Health app integration',
      color: 'bg-purple-50',
      isConnected: integrations.find(i => i.id === 'samsung-health')?.connected || false,
      dataTypes: ['Wellness', 'Fitness', 'Sleep', 'Nutrition']
    },
    {
      id: 'garmin-connect',
      name: 'Garmin Connect',
      icon: 'Navigation',
      description: 'Garmin device and activity data',
      color: 'bg-red-50',
      isConnected: integrations.find(i => i.id === 'garmin-connect')?.connected || false,
      dataTypes: ['GPS', 'Heart Rate', 'Training', 'Recovery']
    },
    {
      id: 'withings',
      name: 'Withings',
      icon: 'Scale',
      description: 'Withings health devices',
      color: 'bg-indigo-50',
      isConnected: integrations.find(i => i.id === 'withings')?.connected || false,
      dataTypes: ['Weight', 'Blood Pressure', 'Sleep', 'Activity']
    }
  ];

  const handleConnect = async (serviceId) => {
    setAuthInProgress(serviceId);
    try {
      // Simulate OAuth flow
      await new Promise(resolve => setTimeout(resolve, 2000));
      onConnectService(serviceId);
    } finally {
      setAuthInProgress(null);
    }
  };

  const getServiceIntegration = (serviceId) => {
    return integrations.find(i => i.id === serviceId);
  };

  const formatLastSync = (timestamp) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-success-50 rounded-lg flex items-center justify-center">
            <Icon name="Link" size={20} color="var(--color-success-500)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              API Integrations
            </h3>
            <p className="text-sm text-text-secondary">
              Connect with third-party health platforms
            </p>
          </div>
        </div>

        <div className="text-right">
          <p className="text-sm text-text-secondary">
            {integrations.filter(i => i.connected).length} of {availableServices.length} connected
          </p>
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {availableServices.map((service) => {
          const integration = getServiceIntegration(service.id);
          const isConnected = service.isConnected;
          const isAuthenticating = authInProgress === service.id;

          return (
            <div key={service.id} className="border border-border rounded-lg p-4 hover:border-border-active transition-fast">
              {/* Service Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 ${service.color} rounded-lg flex items-center justify-center`}>
                    <Icon name={service.icon} size={18} color="var(--color-text-secondary)" />
                  </div>
                  <div>
                    <h4 className="font-medium text-text-primary">{service.name}</h4>
                    <p className="text-sm text-text-secondary">{service.description}</p>
                  </div>
                </div>

                <div className={`w-3 h-3 rounded-full ${
                  isConnected ? 'bg-success-500' : 'bg-secondary-200'
                }`}></div>
              </div>

              {/* Data Types */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {service.dataTypes.map((type) => (
                    <span 
                      key={type}
                      className="px-2 py-1 bg-secondary-50 text-text-secondary text-xs rounded-full"
                    >
                      {type}
                    </span>
                  ))}
                </div>
              </div>

              {/* Connection Status */}
              {isConnected && integration && (
                <div className="mb-4 p-3 bg-success-50 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Icon name="CheckCircle" size={14} color="var(--color-success-500)" />
                      <span className="text-success-700">Connected</span>
                    </div>
                    <span className="text-success-600">
                      {integration.dataPoints || 0} data points
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-success-600 mt-1">
                    <span>Last sync: {formatLastSync(integration.lastSync)}</span>
                    <span>{integration.status || 'Active'}</span>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                {isConnected ? (
                  <>
                    <button
                      onClick={() => onConfigureService(service.id)}
                      className="flex-1 px-3 py-2 bg-secondary-50 text-text-secondary rounded-lg hover:bg-secondary-100 transition-fast text-sm"
                    >
                      Configure
                    </button>
                    <button
                      onClick={() => onDisconnectService(service.id)}
                      className="px-3 py-2 bg-error-50 text-error-600 rounded-lg hover:bg-error-100 transition-fast text-sm"
                    >
                      Disconnect
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => handleConnect(service.id)}
                    disabled={isAuthenticating}
                    className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-fast flex items-center justify-center space-x-2 ${
                      isAuthenticating
                        ? 'bg-secondary-100 text-text-muted cursor-not-allowed' :'bg-primary-500 text-white hover:bg-primary-600'
                    }`}
                  >
                    {isAuthenticating ? (
                      <>
                        <Icon name="Loader" size={14} className="animate-spin" />
                        <span>Connecting...</span>
                      </>
                    ) : (
                      <span>Connect</span>
                    )}
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* OAuth Instructions */}
      <div className="mt-6 p-4 bg-background rounded-lg">
        <div className="flex items-start space-x-3">
          <Icon name="Info" size={16} color="var(--color-primary-500)" />
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-1">Secure Authentication</h4>
            <p className="text-sm text-text-secondary">
              All connections use secure OAuth 2.0 authentication. We never store your login credentials.
              You can revoke access at any time from your account settings.
            </p>
          </div>
        </div>
      </div>

      {/* Connected Services Summary */}
      {integrations.filter(i => i.connected).length > 0 && (
        <div className="mt-6 p-4 bg-success-50 rounded-lg">
          <h4 className="font-medium text-success-800 mb-2">Connected Services</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="text-success-700">
              Total Data Points: {integrations.reduce((sum, i) => sum + (i.dataPoints || 0), 0)}
            </div>
            <div className="text-success-700">
              Active Syncs: {integrations.filter(i => i.status === 'active').length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default APIIntegrationSection;