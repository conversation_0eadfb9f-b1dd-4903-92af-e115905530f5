# VoiceHealth AI - Integration Guide

## Overview

This guide provides step-by-step instructions for integrating VoiceHealth AI's enhanced features into your healthcare applications, with a focus on HIPAA compliance, performance optimization, and emergency protocols.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Core Service Integration](#core-service-integration)
3. [Real-Time Features Setup](#real-time-features-setup)
4. [Security Implementation](#security-implementation)
5. [Performance Monitoring](#performance-monitoring)
6. [Emergency Protocols](#emergency-protocols)
7. [Testing and Validation](#testing-and-validation)
8. [Production Deployment](#production-deployment)

---

## Quick Start

### Prerequisites

- Node.js 18+ or compatible runtime
- Supabase project with proper configuration
- Valid API keys for AI services (OpenAI, Anthropic, etc.)
- SSL certificates for production deployment

### Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Initialize services
npm run setup:services

# Start development server
npm run dev
```

### Basic Configuration

```typescript
// src/config/voicehealth.config.ts
export const VoiceHealthConfig = {
  // Core settings
  environment: process.env.NODE_ENV || 'development',
  apiUrl: process.env.VITE_API_URL || 'http://localhost:3000',
  
  // Supabase configuration
  supabase: {
    url: process.env.VITE_SUPABASE_URL,
    anonKey: process.env.VITE_SUPABASE_ANON_KEY,
    serviceKey: process.env.SUPABASE_SERVICE_KEY // Server-side only
  },
  
  // AI service configuration
  aiServices: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: 'gpt-4',
      maxTokens: 4000
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: 'claude-3-sonnet',
      maxTokens: 4000
    }
  },
  
  // Performance thresholds
  performance: {
    maxResponseTime: 2000, // 2 seconds
    emergencyResponseTime: 2000, // 2 seconds for emergency
    circuitBreakerThreshold: 5,
    memoryCleanupInterval: 3600000 // 1 hour
  },
  
  // Security settings
  security: {
    encryptionEnabled: true,
    auditLogging: true,
    hipaaCompliance: true,
    emergencyBypass: true
  }
};
```

---

## Core Service Integration

### 1. Agent Orchestrator Setup

```typescript
// src/services/setup/orchestrator.ts
import { AgentOrchestrator } from '../AgentOrchestrator';
import { VoiceHealthConfig } from '../../config/voicehealth.config';

export async function initializeOrchestrator(): Promise<AgentOrchestrator> {
  const orchestrator = new AgentOrchestrator();
  
  // Initialize with configuration
  await orchestrator.initialize();
  
  // Set up emergency protocols
  orchestrator.setEmergencyThreshold(VoiceHealthConfig.performance.emergencyResponseTime);
  
  return orchestrator;
}

// Usage in your application
const orchestrator = await initializeOrchestrator();

// Process user requests
const response = await orchestrator.processRequest({
  sessionId: 'user-session-123',
  userMessage: 'I have chest pain and difficulty breathing',
  userId: 'patient-456',
  urgencyLevel: 'high'
});
```

### 2. Circuit Breaker Implementation

```typescript
// src/services/setup/circuitBreakers.ts
import { circuitBreakerService } from '../CircuitBreakerService';

export function setupCircuitBreakers() {
  // AI service circuit breakers
  const openaiBreaker = circuitBreakerService.getCircuitBreaker('openai', {
    failureThreshold: 5,
    recoveryTimeoutMs: 30000,
    emergencyBypass: true
  });
  
  const anthropicBreaker = circuitBreakerService.getCircuitBreaker('anthropic', {
    failureThreshold: 5,
    recoveryTimeoutMs: 30000,
    emergencyBypass: true
  });
  
  // Database circuit breaker
  const dbBreaker = circuitBreakerService.getCircuitBreaker('database', {
    failureThreshold: 3,
    recoveryTimeoutMs: 10000,
    emergencyBypass: true
  });
  
  return { openaiBreaker, anthropicBreaker, dbBreaker };
}

// Usage example
const { openaiBreaker } = setupCircuitBreakers();

async function callAIService(prompt: string) {
  return await openaiBreaker.execute(async () => {
    return await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }]
    });
  });
}
```

### 3. Memory Management Setup

```typescript
// src/services/setup/memoryManagement.ts
import { memoryCleanupManager } from '../MemoryCleanupManager';

export function setupMemoryManagement() {
  // Schedule cleanup for conversation history
  memoryCleanupManager.scheduleCleanup({
    serviceName: 'conversation-history',
    retentionHours: 24,
    cleanupIntervalMs: 3600000, // 1 hour
    emergencyRetentionHours: 72,
    hipaaCompliant: true
  });
  
  // Schedule cleanup for goal tracking
  memoryCleanupManager.scheduleCleanup({
    serviceName: 'goal-tracking',
    retentionHours: 48,
    cleanupIntervalMs: 7200000, // 2 hours
    emergencyRetentionHours: 168, // 7 days
    hipaaCompliant: true
  });
  
  // Schedule cleanup for real-time messages
  memoryCleanupManager.scheduleCleanup({
    serviceName: 'real-time-messages',
    retentionHours: 1,
    cleanupIntervalMs: 900000, // 15 minutes
    emergencyRetentionHours: 24,
    hipaaCompliant: true
  });
}
```

---

## Real-Time Features Setup

### 1. WebSocket Connection

```typescript
// src/services/setup/realTime.ts
import { realTimeAgentCommunication } from '../RealTimeAgentCommunication';

export function setupRealTimeFeatures(sessionId: string) {
  // Set up goal progress tracking
  realTimeAgentCommunication.on('goal-progress-update', (data) => {
    if (data.sessionId === sessionId) {
      updateGoalProgressUI(data.data);
    }
  });
  
  // Set up steering guidance updates
  realTimeAgentCommunication.on('steering-guidance-update', (data) => {
    if (data.sessionId === sessionId) {
      updateSteeringGuidanceUI(data.data);
    }
  });
  
  // Set up goal completion notifications
  realTimeAgentCommunication.on('goal-completed', (data) => {
    if (data.sessionId === sessionId) {
      showGoalCompletionNotification(data);
    }
  });
  
  // Handle connection status
  realTimeAgentCommunication.on('connection-status', (status) => {
    updateConnectionStatusUI(status);
  });
}

// UI update functions
function updateGoalProgressUI(data: any) {
  const progressElement = document.getElementById('goal-progress');
  if (progressElement) {
    progressElement.innerHTML = `
      <div class="progress-bar">
        <div class="progress-fill" style="width: ${data.progressPercentage}%"></div>
      </div>
      <div class="goal-details">
        Active Goals: ${data.goals?.length || 0}
      </div>
    `;
  }
}

function updateSteeringGuidanceUI(data: any) {
  const guidanceElement = document.getElementById('steering-guidance');
  if (guidanceElement && data.steeringGuidance) {
    guidanceElement.innerHTML = data.steeringGuidance.map((guidance: any) => `
      <div class="guidance-item ${guidance.urgency_level}">
        <strong>${guidance.steering_type}:</strong> ${guidance.steering_message}
      </div>
    `).join('');
  }
}
```

### 2. React Component Integration

```tsx
// src/components/RealTimeConsultation.tsx
import React, { useEffect, useState } from 'react';
import { realTimeAgentCommunication } from '../services/RealTimeAgentCommunication';

interface RealTimeConsultationProps {
  sessionId: string;
  userId: string;
}

export const RealTimeConsultation: React.FC<RealTimeConsultationProps> = ({ 
  sessionId, 
  userId 
}) => {
  const [goalProgress, setGoalProgress] = useState<any[]>([]);
  const [steeringGuidance, setSteeringGuidance] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Set up real-time listeners
    const handleGoalProgress = (data: any) => {
      if (data.sessionId === sessionId) {
        setGoalProgress(data.data.goals || []);
      }
    };

    const handleSteeringGuidance = (data: any) => {
      if (data.sessionId === sessionId) {
        setSteeringGuidance(data.data.steeringGuidance || []);
      }
    };

    const handleConnectionStatus = (status: boolean) => {
      setIsConnected(status);
    };

    realTimeAgentCommunication.on('goal-progress-update', handleGoalProgress);
    realTimeAgentCommunication.on('steering-guidance-update', handleSteeringGuidance);
    realTimeAgentCommunication.on('connection-status', handleConnectionStatus);

    // Cleanup
    return () => {
      realTimeAgentCommunication.off('goal-progress-update', handleGoalProgress);
      realTimeAgentCommunication.off('steering-guidance-update', handleSteeringGuidance);
      realTimeAgentCommunication.off('connection-status', handleConnectionStatus);
    };
  }, [sessionId]);

  return (
    <div className="real-time-consultation">
      <div className="connection-status">
        Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
      </div>
      
      <div className="goal-progress">
        <h3>Goal Progress</h3>
        {goalProgress.map((goal, index) => (
          <div key={index} className="goal-item">
            <div>{goal.primaryGoal}</div>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${goal.progressPercentage || 0}%` }}
              />
            </div>
          </div>
        ))}
      </div>
      
      <div className="steering-guidance">
        <h3>Current Guidance</h3>
        {steeringGuidance.map((guidance, index) => (
          <div key={index} className={`guidance-item ${guidance.urgency_level}`}>
            <strong>{guidance.steering_type}:</strong> {guidance.steering_message}
          </div>
        ))}
      </div>
    </div>
  );
};
```

---

## Security Implementation

### 1. Secure Storage Setup

```typescript
// src/services/setup/secureStorage.ts
import { secureStorageService } from '../SecureStorageService';

export function setupSecureStorage(userId: string, userRole: string) {
  // Grant appropriate permissions based on role
  const permissions = getPermissionsByRole(userRole);
  
  secureStorageService.grantPermission({
    userId,
    role: userRole as any,
    permissions,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
  });
}

function getPermissionsByRole(role: string): ('read' | 'write' | 'delete')[] {
  switch (role) {
    case 'admin':
      return ['read', 'write', 'delete'];
    case 'provider':
      return ['read', 'write'];
    case 'patient':
      return ['read'];
    case 'emergency':
      return ['read', 'write', 'delete'];
    default:
      return ['read'];
  }
}

// Usage example
async function uploadMedicalDocument(file: File, userId: string, patientId: string) {
  const uploadResult = await secureStorageService.secureUpload(
    'medical-records',
    `patient-${patientId}/document-${Date.now()}.pdf`,
    file,
    userId,
    {
      documentType: 'medical_report',
      patientId,
      uploadedBy: userId,
      hipaaCompliant: true
    }
  );
  
  if (uploadResult.success) {
    console.log('Document uploaded securely:', uploadResult.fileId);
    return uploadResult;
  } else {
    throw new Error(`Upload failed: ${uploadResult.error}`);
  }
}
```

### 2. Error Sanitization Integration

```typescript
// src/utils/errorHandling.ts
import { errorSanitizationService } from '../services/ErrorSanitizationService';

export function createErrorHandler(component: string) {
  return async function handleError(
    error: Error,
    context: {
      sessionId?: string;
      userId?: string;
      operation?: string;
    }
  ) {
    const sanitizedError = errorSanitizationService.sanitizeError(error, {
      ...context,
      component
    });
    
    // Log technical details (server-side only)
    console.error(`[${sanitizedError.id}] ${sanitizedError.technicalMessage}`);
    
    // Return user-safe message
    return {
      success: false,
      error: sanitizedError.userMessage,
      errorId: sanitizedError.id
    };
  };
}

// Usage in components
const handleError = createErrorHandler('ConsultationComponent');

try {
  await processConsultation();
} catch (error) {
  const result = await handleError(error, {
    sessionId: 'session-123',
    userId: 'user-456',
    operation: 'processConsultation'
  });
  
  showUserMessage(result.error);
}
```

---

## Performance Monitoring

### 1. Monitoring Setup

```typescript
// src/services/setup/monitoring.ts
import { performanceMonitoringService } from '../PerformanceMonitoringService';

export function setupPerformanceMonitoring() {
  // Set up alert handlers
  performanceMonitoringService.onAlert((alert) => {
    switch (alert.level) {
      case 'emergency':
        handleEmergencyAlert(alert);
        break;
      case 'critical':
        handleCriticalAlert(alert);
        break;
      case 'warning':
        handleWarningAlert(alert);
        break;
    }
  });
  
  // Update performance thresholds if needed
  performanceMonitoringService.updateThreshold('agent_orchestrator', 'process_request', {
    emergencyThresholdMs: 2000 // Ensure <2 second emergency response
  });
}

function handleEmergencyAlert(alert: any) {
  console.error(`🚨 EMERGENCY PERFORMANCE ALERT: ${alert.feature}.${alert.operation}`);
  
  // Trigger emergency protocols
  // - Scale resources
  // - Alert administrators
  // - Activate backup systems
}

function handleCriticalAlert(alert: any) {
  console.warn(`⚠️ CRITICAL PERFORMANCE ALERT: ${alert.feature}.${alert.operation}`);
  
  // Trigger performance optimization
  // - Clear caches
  // - Restart services
  // - Load balance
}
```

### 2. Custom Performance Tracking

```typescript
// src/utils/performanceTracking.ts
import { performanceMonitoringService } from '../services/PerformanceMonitoringService';

export function trackOperation<T>(
  feature: string,
  operation: string,
  operationFn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  return performanceMonitoringService.measurePerformance(
    feature,
    operation,
    operationFn,
    metadata
  );
}

// Usage examples
const consultationResult = await trackOperation(
  'consultation',
  'process_user_input',
  async () => {
    return await processUserInput(userMessage);
  },
  { messageLength: userMessage.length, urgency: 'high' }
);

const uploadResult = await trackOperation(
  'file_upload',
  'secure_upload',
  async () => {
    return await secureStorageService.secureUpload(bucket, path, file, userId);
  },
  { fileSize: file.size, fileType: file.type }
);
```

---

## Emergency Protocols

### 1. Emergency Detection and Response

```typescript
// src/services/emergency/protocols.ts
export class EmergencyProtocols {
  private static emergencyKeywords = [
    'cardiac arrest', 'heart attack', 'stroke', 'unconscious',
    'not breathing', 'severe bleeding', 'allergic reaction',
    'chest pain', 'difficulty breathing', 'emergency'
  ];
  
  static detectEmergency(message: string): boolean {
    const lowerMessage = message.toLowerCase();
    return this.emergencyKeywords.some(keyword => 
      lowerMessage.includes(keyword)
    );
  }
  
  static async handleEmergency(
    sessionId: string,
    userId: string,
    emergencyType: string,
    context: any
  ) {
    console.log(`🚨 EMERGENCY DETECTED: ${emergencyType} for session ${sessionId}`);
    
    // 1. Bypass normal processing delays
    const emergencyResponse = await this.getEmergencyResponse(emergencyType, context);
    
    // 2. Alert emergency services (if configured)
    await this.alertEmergencyServices(userId, emergencyType, context);
    
    // 3. Log emergency event
    await this.logEmergencyEvent(sessionId, userId, emergencyType, context);
    
    // 4. Activate emergency data access
    this.activateEmergencyAccess(userId);
    
    return emergencyResponse;
  }
  
  private static async getEmergencyResponse(type: string, context: any) {
    // Provide immediate emergency guidance
    const emergencyResponses = {
      'cardiac_arrest': 'Call 911 immediately. Begin CPR if trained. Do not leave the person alone.',
      'difficulty_breathing': 'Call 911 immediately. Help the person sit upright. Loosen tight clothing.',
      'severe_bleeding': 'Call 911 immediately. Apply direct pressure to the wound with clean cloth.',
      'allergic_reaction': 'Call 911 immediately. If available, use epinephrine auto-injector.',
      'default': 'This appears to be a medical emergency. Call 911 or your local emergency number immediately.'
    };
    
    return emergencyResponses[type] || emergencyResponses.default;
  }
  
  private static async alertEmergencyServices(userId: string, type: string, context: any) {
    // Implementation would depend on integration with emergency services
    console.log(`📞 Emergency services alerted for user ${userId}: ${type}`);
  }
  
  private static async logEmergencyEvent(sessionId: string, userId: string, type: string, context: any) {
    // Log to audit system with high priority
    await auditLogger.logEmergencyEvent({
      sessionId,
      userId,
      emergencyType: type,
      context,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - context.startTime
    });
  }
  
  private static activateEmergencyAccess(userId: string) {
    // Grant temporary emergency access to medical records
    secureStorageService.grantPermission({
      userId,
      role: 'emergency',
      permissions: ['read', 'write'],
      emergencyAccess: true,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    });
  }
}
```

### 2. Emergency Integration in Consultation Flow

```typescript
// src/services/consultation/emergencyIntegration.ts
import { EmergencyProtocols } from '../emergency/protocols';
import { agentOrchestrator } from '../AgentOrchestrator';

export async function processConsultationWithEmergencyCheck(
  sessionId: string,
  userMessage: string,
  userId: string
) {
  const startTime = Date.now();
  
  // Check for emergency keywords
  const isEmergency = EmergencyProtocols.detectEmergency(userMessage);
  
  if (isEmergency) {
    // Handle emergency with <2 second response time requirement
    const emergencyResponse = await EmergencyProtocols.handleEmergency(
      sessionId,
      userId,
      'detected_emergency',
      { userMessage, startTime }
    );
    
    return {
      response: emergencyResponse,
      isEmergency: true,
      responseTime: Date.now() - startTime,
      agentId: 'emergency_protocol'
    };
  }
  
  // Normal consultation processing
  const response = await agentOrchestrator.processRequest({
    sessionId,
    userMessage,
    userId,
    urgencyLevel: 'medium'
  });
  
  return {
    ...response,
    isEmergency: false,
    responseTime: Date.now() - startTime
  };
}
```

---

## Testing and Validation

### 1. Integration Tests

```typescript
// tests/integration/voicehealth.integration.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { setupTestEnvironment, cleanupTestEnvironment } from './helpers/testSetup';

describe('VoiceHealth AI Integration Tests', () => {
  let testSessionId: string;
  let testUserId: string;

  beforeEach(async () => {
    const testEnv = await setupTestEnvironment();
    testSessionId = testEnv.sessionId;
    testUserId = testEnv.userId;
  });

  afterEach(async () => {
    await cleanupTestEnvironment(testSessionId);
  });

  it('should handle complete consultation flow', async () => {
    const response = await processConsultationWithEmergencyCheck(
      testSessionId,
      'I have mild headache',
      testUserId
    );

    expect(response.isEmergency).toBe(false);
    expect(response.responseTime).toBeLessThan(2000);
    expect(response.response).toBeDefined();
  });

  it('should detect and handle emergency scenarios', async () => {
    const response = await processConsultationWithEmergencyCheck(
      testSessionId,
      'I am having a heart attack',
      testUserId
    );

    expect(response.isEmergency).toBe(true);
    expect(response.responseTime).toBeLessThan(2000);
    expect(response.response).toContain('911');
  });

  it('should maintain HIPAA compliance', async () => {
    // Test that sensitive data is properly handled
    const response = await processConsultationWithEmergencyCheck(
      testSessionId,
      'My SSN is *********** and I have diabetes',
      testUserId
    );

    expect(response.response).not.toContain('***********');
  });
});
```

### 2. Performance Validation

```typescript
// tests/performance/performance.test.ts
import { describe, it, expect } from 'vitest';
import { performanceMonitoringService } from '../../src/services/PerformanceMonitoringService';

describe('Performance Requirements', () => {
  it('should meet emergency response time requirements', async () => {
    const startTime = Date.now();
    
    const response = await processConsultationWithEmergencyCheck(
      'test-session',
      'EMERGENCY: Patient is unconscious',
      'test-user'
    );
    
    const responseTime = Date.now() - startTime;
    
    expect(responseTime).toBeLessThan(2000); // <2 seconds
    expect(response.isEmergency).toBe(true);
  });

  it('should handle concurrent requests efficiently', async () => {
    const concurrentRequests = Array.from({ length: 10 }, (_, i) =>
      processConsultationWithEmergencyCheck(
        `test-session-${i}`,
        'Test message',
        `test-user-${i}`
      )
    );

    const startTime = Date.now();
    const responses = await Promise.all(concurrentRequests);
    const totalTime = Date.now() - startTime;

    expect(responses).toHaveLength(10);
    expect(totalTime).toBeLessThan(10000); // <10 seconds for 10 concurrent requests
  });
});
```

---

## Production Deployment

### 1. Environment Configuration

```bash
# Production environment variables
NODE_ENV=production
VITE_API_URL=https://api.voicehealth.ai
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# AI Service Keys
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# Security
VITE_STORAGE_ENCRYPTION_KEY=your-encryption-key
JWT_SECRET=your-jwt-secret

# Performance
PERFORMANCE_MONITORING_ENABLED=true
CIRCUIT_BREAKER_ENABLED=true
MEMORY_CLEANUP_ENABLED=true

# Compliance
HIPAA_COMPLIANCE_ENABLED=true
AUDIT_LOGGING_ENABLED=true
```

### 2. Production Checklist

- [ ] **Security Configuration**
  - [ ] SSL certificates installed
  - [ ] Encryption keys properly managed
  - [ ] CORS configured correctly
  - [ ] Rate limiting enabled

- [ ] **Performance Optimization**
  - [ ] Circuit breakers configured
  - [ ] Memory cleanup scheduled
  - [ ] Performance monitoring active
  - [ ] CDN configured for static assets

- [ ] **Compliance Requirements**
  - [ ] HIPAA compliance validated
  - [ ] Audit logging enabled
  - [ ] Data retention policies configured
  - [ ] Emergency protocols tested

- [ ] **Monitoring and Alerting**
  - [ ] Performance alerts configured
  - [ ] Error tracking enabled
  - [ ] Health checks implemented
  - [ ] Backup systems verified

### 3. Deployment Script

```bash
#!/bin/bash
# deploy.sh

echo "🚀 Deploying VoiceHealth AI to production..."

# Build application
npm run build

# Run tests
npm run test:integration
npm run test:performance

# Deploy to production
npm run deploy:production

# Verify deployment
npm run verify:production

echo "✅ Deployment completed successfully!"
```

---

## Support and Troubleshooting

### Common Issues

1. **Performance Degradation**
   - Check circuit breaker status
   - Review performance monitoring alerts
   - Verify memory cleanup is running

2. **Real-Time Connection Issues**
   - Check WebSocket connection status
   - Verify network connectivity
   - Review real-time service logs

3. **Security Errors**
   - Validate user permissions
   - Check encryption configuration
   - Review audit logs

### Getting Help

- **Documentation**: Refer to API documentation and inline code comments
- **GitHub Issues**: Report bugs and request features
- **Performance Issues**: Check performance monitoring dashboard
- **Security Concerns**: Contact security team immediately

For additional support, refer to the troubleshooting section in the main documentation.
