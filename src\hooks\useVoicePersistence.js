/**
 * Voice Persistence Hook
 * Handles offline voice chunk storage and sync for seamless voice consultations
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import offlineDB from '../services/offlineDatabase';
import connectionManager from '../services/connectionManager';

const useVoicePersistence = (sessionId, options = {}) => {
  const {
    maxChunkSize = 64 * 1024, // 64KB chunks
    maxOfflineStorage = 50 * 1024 * 1024, // 50MB total offline storage
    compressionEnabled = true,
    autoSync = true
  } = options;

  const [persistenceStatus, setPersistenceStatus] = useState({
    isOnline: navigator.onLine,
    storedChunks: 0,
    pendingSyncChunks: 0,
    totalStorageUsed: 0,
    lastSync: null,
    syncInProgress: false
  });

  const [voiceConnection, setVoiceConnection] = useState(null);
  const chunkQueue = useRef([]);
  const syncTimer = useRef(null);

  // Initialize voice connection
  useEffect(() => {
    if (!sessionId) return;

    const initializeConnection = async () => {
      try {
        const connection = await connectionManager.connect('voice-stream', {
          url: `${import.meta.env.VITE_VOICE_WEBSOCKET_URL}/stream/${sessionId}`,
          onOpen: handleConnectionOpen,
          onMessage: handleVoiceMessage,
          onClose: handleConnectionClose,
          onError: handleConnectionError
        });

        setVoiceConnection(connection);
      } catch (error) {
        console.error('Failed to initialize voice connection:', error);
        // Continue in offline mode
        setPersistenceStatus(prev => ({ ...prev, isOnline: false }));
      }
    };

    initializeConnection();

    return () => {
      if (voiceConnection) {
        connectionManager.disconnect('voice-stream');
      }
      if (syncTimer.current) {
        clearInterval(syncTimer.current);
      }
    };
  }, [sessionId]);

  // Monitor storage usage and sync status
  useEffect(() => {
    const updateStorageStatus = async () => {
      try {
        const storedCount = await offlineDB.audioChunks
          .where('sessionId')
          .equals(sessionId)
          .count();

        const pendingCount = await offlineDB.audioChunks
          .where(['sessionId', 'synced'])
          .equals([sessionId, false])
          .count();

        const totalUsage = await estimateStorageUsage();

        setPersistenceStatus(prev => ({
          ...prev,
          storedChunks: storedCount,
          pendingSyncChunks: pendingCount,
          totalStorageUsed: totalUsage
        }));
      } catch (error) {
        console.error('Failed to update storage status:', error);
      }
    };

    updateStorageStatus();

    // Update status every 30 seconds
    const statusInterval = setInterval(updateStorageStatus, 30000);

    return () => clearInterval(statusInterval);
  }, [sessionId]);

  // Auto-sync when online
  useEffect(() => {
    if (autoSync && persistenceStatus.isOnline && persistenceStatus.pendingSyncChunks > 0) {
      startAutoSync();
    } else {
      stopAutoSync();
    }
  }, [persistenceStatus.isOnline, persistenceStatus.pendingSyncChunks, autoSync]);

  // Connection event handlers
  const handleConnectionOpen = () => {
    console.log('Voice connection established');
    setPersistenceStatus(prev => ({ ...prev, isOnline: true }));
    
    // Flush any queued chunks
    flushChunkQueue();
  };

  const handleConnectionClose = () => {
    console.log('Voice connection closed');
    setPersistenceStatus(prev => ({ ...prev, isOnline: false }));
  };

  const handleConnectionError = (error) => {
    console.error('Voice connection error:', error);
    setPersistenceStatus(prev => ({ ...prev, isOnline: false }));
  };

  const handleVoiceMessage = (event) => {
    // Handle voice processing responses from server
    try {
      const message = JSON.parse(event.data);
      
      if (message.type === 'transcription') {
        // Voice transcription received
        console.log('Transcription received:', message.data);
      } else if (message.type === 'chunk_received') {
        // Server acknowledged chunk receipt
        markChunkSynced(message.chunkId);
      }
    } catch (error) {
      console.error('Failed to parse voice message:', error);
    }
  };

  /**
   * Store voice chunk for offline persistence
   */
  const storeVoiceChunk = useCallback(async (audioData, metadata = {}) => {
    try {
      // Check storage limits
      if (persistenceStatus.totalStorageUsed >= maxOfflineStorage) {
        await cleanupOldChunks();
      }

      // Compress audio if enabled
      const processedAudioData = compressionEnabled 
        ? await compressAudioData(audioData)
        : audioData;

      const chunkId = `${sessionId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const chunkData = {
        id: chunkId,
        sessionId,
        audioData: processedAudioData,
        quality: metadata.quality || 'standard',
        timestamp: new Date().toISOString(),
        chunkIndex: metadata.chunkIndex || 0,
        duration: metadata.duration || 0,
        synced: false,
        attempts: 0,
        metadata: {
          originalSize: audioData.byteLength || audioData.length,
          compressedSize: processedAudioData.byteLength || processedAudioData.length,
          ...metadata
        }
      };

      // Store in IndexedDB
      await offlineDB.audioChunks.add(chunkData);

      console.log(`Voice chunk stored offline: ${chunkId}`);

      // Try to send immediately if online
      if (persistenceStatus.isOnline && voiceConnection?.isConnected) {
        await sendVoiceChunk(chunkData);
      } else {
        // Queue for later sending
        chunkQueue.current.push(chunkData);
      }

      return chunkId;
    } catch (error) {
      console.error('Failed to store voice chunk:', error);
      throw error;
    }
  }, [sessionId, persistenceStatus.isOnline, persistenceStatus.totalStorageUsed]);

  /**
   * Send voice chunk to server
   */
  const sendVoiceChunk = async (chunkData) => {
    try {
      if (!voiceConnection?.isConnected) {
        throw new Error('Voice connection not available');
      }

      const message = {
        type: 'audio_chunk',
        sessionId: chunkData.sessionId,
        chunkId: chunkData.id,
        audioData: Array.from(new Uint8Array(chunkData.audioData)),
        metadata: chunkData.metadata,
        timestamp: chunkData.timestamp
      };

      await connectionManager.send('voice-stream', message);
      
      console.log(`Voice chunk sent: ${chunkData.id}`);
      
      return true;
    } catch (error) {
      console.error(`Failed to send voice chunk ${chunkData.id}:`, error);
      
      // Update attempt counter
      await offlineDB.audioChunks.update(chunkData.id, {
        attempts: (chunkData.attempts || 0) + 1,
        lastAttempt: new Date().toISOString()
      });
      
      return false;
    }
  };

  /**
   * Flush queued chunks when connection is restored
   */
  const flushChunkQueue = async () => {
    if (chunkQueue.current.length === 0) return;

    const chunks = chunkQueue.current.splice(0);
    console.log(`Flushing ${chunks.length} queued voice chunks`);

    for (const chunk of chunks) {
      try {
        await sendVoiceChunk(chunk);
      } catch (error) {
        console.error('Failed to flush chunk:', error);
        // Put back in queue
        chunkQueue.current.unshift(chunk);
        break;
      }
    }
  };

  /**
   * Mark chunk as successfully synced
   */
  const markChunkSynced = async (chunkId) => {
    try {
      await offlineDB.audioChunks.update(chunkId, {
        synced: true,
        syncedAt: new Date().toISOString()
      });

      console.log(`Voice chunk marked as synced: ${chunkId}`);
    } catch (error) {
      console.error(`Failed to mark chunk as synced: ${chunkId}`, error);
    }
  };

  /**
   * Start automatic sync process
   */
  const startAutoSync = () => {
    if (syncTimer.current) return;

    syncTimer.current = setInterval(async () => {
      if (!persistenceStatus.isOnline || persistenceStatus.syncInProgress) {
        return;
      }

      try {
        setPersistenceStatus(prev => ({ ...prev, syncInProgress: true }));
        
        const unsyncedChunks = await offlineDB.audioChunks
          .where(['sessionId', 'synced'])
          .equals([sessionId, false])
          .limit(10) // Sync 10 chunks at a time
          .toArray();

        for (const chunk of unsyncedChunks) {
          if (chunk.attempts < 3) { // Max 3 retry attempts
            await sendVoiceChunk(chunk);
          }
        }

        setPersistenceStatus(prev => ({ 
          ...prev, 
          syncInProgress: false,
          lastSync: new Date().toISOString()
        }));

      } catch (error) {
        console.error('Auto-sync failed:', error);
        setPersistenceStatus(prev => ({ ...prev, syncInProgress: false }));
      }
    }, 10000); // Sync every 10 seconds
  };

  /**
   * Stop automatic sync
   */
  const stopAutoSync = () => {
    if (syncTimer.current) {
      clearInterval(syncTimer.current);
      syncTimer.current = null;
    }
  };

  /**
   * Force sync all pending chunks
   */
  const forcSync = async () => {
    if (!persistenceStatus.isOnline) {
      throw new Error('Cannot sync while offline');
    }

    setPersistenceStatus(prev => ({ ...prev, syncInProgress: true }));

    try {
      const unsyncedChunks = await offlineDB.audioChunks
        .where(['sessionId', 'synced'])
        .equals([sessionId, false])
        .toArray();

      let syncedCount = 0;
      for (const chunk of unsyncedChunks) {
        const success = await sendVoiceChunk(chunk);
        if (success) syncedCount++;
      }

      console.log(`Force sync completed: ${syncedCount}/${unsyncedChunks.length} chunks synced`);

      return { success: syncedCount, total: unsyncedChunks.length };
    } finally {
      setPersistenceStatus(prev => ({ 
        ...prev, 
        syncInProgress: false,
        lastSync: new Date().toISOString()
      }));
    }
  };

  /**
   * Clean up old chunks to free storage space
   */
  const cleanupOldChunks = async () => {
    try {
      // Remove synced chunks older than 7 days
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7);

      const oldChunks = await offlineDB.audioChunks
        .where('timestamp')
        .below(cutoffDate.toISOString())
        .and(chunk => chunk.synced === true)
        .primaryKeys();

      if (oldChunks.length > 0) {
        await offlineDB.audioChunks.bulkDelete(oldChunks);
        console.log(`Cleaned up ${oldChunks.length} old voice chunks`);
      }
    } catch (error) {
      console.error('Failed to cleanup old chunks:', error);
    }
  };

  /**
   * Estimate storage usage
   */
  const estimateStorageUsage = async () => {
    try {
      const chunks = await offlineDB.audioChunks
        .where('sessionId')
        .equals(sessionId)
        .toArray();

      return chunks.reduce((total, chunk) => {
        const size = chunk.audioData?.byteLength || chunk.audioData?.length || 0;
        return total + size;
      }, 0);
    } catch (error) {
      console.error('Failed to estimate storage usage:', error);
      return 0;
    }
  };

  /**
   * Compress audio data (placeholder - implement based on requirements)
   */
  const compressAudioData = async (audioData) => {
    // TODO: Implement audio compression if needed
    // For now, return original data
    return audioData;
  };

  /**
   * Get stored chunks for session
   */
  const getStoredChunks = async () => {
    try {
      return await offlineDB.audioChunks
        .where('sessionId')
        .equals(sessionId)
        .toArray();
    } catch (error) {
      console.error('Failed to get stored chunks:', error);
      return [];
    }
  };

  /**
   * Clear all chunks for session
   */
  const clearStoredChunks = async () => {
    try {
      await offlineDB.audioChunks
        .where('sessionId')
        .equals(sessionId)
        .delete();
      
      console.log(`Cleared all voice chunks for session: ${sessionId}`);
    } catch (error) {
      console.error('Failed to clear stored chunks:', error);
    }
  };

  return {
    // Core functionality
    storeVoiceChunk,
    forcSync,
    
    // Status and monitoring
    persistenceStatus,
    
    // Data management
    getStoredChunks,
    clearStoredChunks,
    cleanupOldChunks,
    
    // Connection management
    voiceConnection: voiceConnection?.isConnected || false
  };
};

export default useVoicePersistence;
