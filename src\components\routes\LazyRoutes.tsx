/**
 * LAZY-<PERSON><PERSON><PERSON><PERSON> ROUTES FOR MEDICAL APPLICATION
 * 
 * This file defines all lazy-loaded routes with role-based splitting
 * to optimize bundle size while maintaining emergency system performance.
 * 
 * ROUTE ORGANIZATION:
 * - Emergency routes (preloaded)
 * - Patient routes (lazy loaded)
 * - Healthcare provider routes (lazy loaded)
 * - Admin routes (lazy loaded)
 * - Shared medical components (lazy loaded)
 * 
 * PERFORMANCE TARGETS:
 * - 40% bundle size reduction
 * - Emergency routes load < 100ms
 * - Role-based routes load < 500ms
 * - Offline fallbacks available
 */

import React from 'react';
import { createLazyComponent, withLazyWrapper, createRoleBasedLazyComponent } from '../../utils/lazyLoading';
import type { UserRole } from '../../types';

// =============================================================================
// EMERGENCY ROUTES (PRELOADED FOR CRITICAL MEDICAL OPERATIONS)
// =============================================================================

export const EmergencyProtocols = createLazyComponent(
  () => import('../emergency/EmergencyProtocols'),
  { 
    priority: 'emergency',
    preload: true,
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-red-100 border border-red-300 rounded">
        <h2 className="text-red-800 font-bold">🚨 EMERGENCY SYSTEM ERROR</h2>
        <p className="text-red-700 mt-2">Critical emergency protocols failed to load.</p>
        <p className="text-red-600 text-sm mt-1">Error: {error.message}</p>
        <button 
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry Emergency System
        </button>
      </div>
    )
  }
);

export const EmergencyConsultation = createLazyComponent(
  () => import('../consultation/EmergencyConsultation'),
  { 
    priority: 'emergency',
    preload: true
  }
);

export const CriticalVitals = createLazyComponent(
  () => import('../medical/CriticalVitals'),
  { 
    priority: 'emergency',
    preload: true
  }
);

// =============================================================================
// PATIENT ROUTES (LAZY LOADED)
// =============================================================================

export const PatientDashboard = createLazyComponent(
  () => import('../dashboard/PatientDashboard'),
  { 
    priority: 'high',
    requiredRole: 'patient'
  }
);

export const MedicalHistory = createLazyComponent(
  () => import('../medical/MedicalHistory'),
  { 
    priority: 'normal',
    requiredRole: 'patient'
  }
);

export const SymptomTracker = createLazyComponent(
  () => import('../medical/SymptomTracker'),
  { 
    priority: 'normal',
    requiredRole: 'patient'
  }
);

export const MedicationManager = createLazyComponent(
  () => import('../medical/MedicationManager'),
  { 
    priority: 'normal',
    requiredRole: 'patient'
  }
);

export const AppointmentBooking = createLazyComponent(
  () => import('../appointments/AppointmentBooking'),
  { 
    priority: 'normal',
    requiredRole: 'patient'
  }
);

// =============================================================================
// HEALTHCARE PROVIDER ROUTES (LAZY LOADED)
// =============================================================================

export const ProviderDashboard = createLazyComponent(
  () => import('../dashboard/ProviderDashboard'),
  { 
    priority: 'high',
    requiredRole: 'healthcare_provider'
  }
);

export const PatientManagement = createLazyComponent(
  () => import('../provider/PatientManagement'),
  { 
    priority: 'normal',
    requiredRole: 'healthcare_provider'
  }
);

export const ConsultationInterface = createLazyComponent(
  () => import('../consultation/ConsultationInterface'),
  { 
    priority: 'high',
    requiredRole: 'healthcare_provider'
  }
);

export const MedicalRecords = createLazyComponent(
  () => import('../medical/MedicalRecords'),
  { 
    priority: 'normal',
    requiredRole: 'healthcare_provider'
  }
);

export const DiagnosisTools = createLazyComponent(
  () => import('../provider/DiagnosisTools'),
  { 
    priority: 'normal',
    requiredRole: 'healthcare_provider'
  }
);

// =============================================================================
// ADMIN ROUTES (LAZY LOADED)
// =============================================================================

export const AdminDashboard = createLazyComponent(
  () => import('../dashboard/AdminDashboard'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const UserManagement = createLazyComponent(
  () => import('../admin/UserManagement'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const SystemSettings = createLazyComponent(
  () => import('../admin/SystemSettings'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const AuditLogs = createLazyComponent(
  () => import('../admin/AuditLogs'),
  { 
    priority: 'normal',
    requiredRole: 'admin'
  }
);

export const AnalyticsDashboard = createLazyComponent(
  () => import('../analytics/AnalyticsDashboard'),
  { 
    priority: 'low',
    requiredRole: 'admin'
  }
);

// =============================================================================
// SHARED MEDICAL COMPONENTS (LAZY LOADED)
// =============================================================================

export const HealthMetrics = createLazyComponent(
  () => import('../medical/HealthMetrics'),
  { 
    priority: 'normal'
  }
);

export const MedicalCharts = createLazyComponent(
  () => import('../charts/MedicalCharts'),
  { 
    priority: 'low' // Charts are heavy, load them last
  }
);

export const ReportsGenerator = createLazyComponent(
  () => import('../reports/ReportsGenerator'),
  { 
    priority: 'low'
  }
);

export const DataVisualization = createLazyComponent(
  () => import('../visualization/DataVisualization'),
  { 
    priority: 'low'
  }
);

// =============================================================================
// ROLE-BASED DASHBOARD COMPONENT
// =============================================================================

export const RoleBasedDashboard = createRoleBasedLazyComponent(
  {
    'patient': () => import('../dashboard/PatientDashboard'),
    'healthcare_provider': () => import('../dashboard/ProviderDashboard'),
    'admin': () => import('../dashboard/AdminDashboard'),
    'emergency_responder': () => import('../dashboard/EmergencyDashboard')
  },
  () => import('../dashboard/PatientDashboard'), // Default fallback
  { 
    priority: 'high',
    errorFallback: ({ error, resetErrorBoundary }) => (
      <div className="p-8 bg-yellow-100 border border-yellow-300 rounded">
        <h2 className="text-yellow-800 font-bold">Dashboard Loading Error</h2>
        <p className="text-yellow-700 mt-2">Failed to load role-specific dashboard.</p>
        <p className="text-yellow-600 text-sm mt-1">Error: {error.message}</p>
        <button 
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Retry Dashboard
        </button>
      </div>
    )
  }
);

// =============================================================================
// AUTHENTICATION ROUTES (LAZY LOADED)
// =============================================================================

export const LoginPage = createLazyComponent(
  () => import('../auth/LoginPage'),
  { 
    priority: 'high' // Authentication is important
  }
);

export const RegisterPage = createLazyComponent(
  () => import('../auth/RegisterPage'),
  { 
    priority: 'normal'
  }
);

export const ForgotPassword = createLazyComponent(
  () => import('../auth/ForgotPassword'),
  { 
    priority: 'normal'
  }
);

// =============================================================================
// UTILITY ROUTES (LAZY LOADED)
// =============================================================================

export const ProfileSettings = createLazyComponent(
  () => import('../profile/ProfileSettings'),
  { 
    priority: 'normal'
  }
);

export const NotificationCenter = createLazyComponent(
  () => import('../notifications/NotificationCenter'),
  { 
    priority: 'normal'
  }
);

export const HelpCenter = createLazyComponent(
  () => import('../help/HelpCenter'),
  { 
    priority: 'low'
  }
);

export const PrivacyPolicy = createLazyComponent(
  () => import('../legal/PrivacyPolicy'),
  { 
    priority: 'low'
  }
);

// =============================================================================
// OFFLINE FALLBACK COMPONENTS
// =============================================================================

export const OfflineDashboard = createLazyComponent(
  () => import('../offline/OfflineDashboard'),
  { 
    priority: 'high',
    preload: true // Preload for offline scenarios
  }
);

export const OfflineMedicalData = createLazyComponent(
  () => import('../offline/OfflineMedicalData'),
  { 
    priority: 'high',
    preload: true
  }
);

// =============================================================================
// ROUTE PRELOADING UTILITIES
// =============================================================================

/**
 * Preload routes based on user role
 */
export function preloadRoleBasedRoutes(userRole: UserRole): void {
  const roleRoutes = {
    'patient': [PatientDashboard, MedicalHistory, SymptomTracker, MedicationManager],
    'healthcare_provider': [ProviderDashboard, PatientManagement, ConsultationInterface, MedicalRecords],
    'admin': [AdminDashboard, UserManagement, SystemSettings, AuditLogs],
    'emergency_responder': [EmergencyProtocols, EmergencyConsultation, CriticalVitals]
  };

  const routes = roleRoutes[userRole] || roleRoutes['patient'];
  
  // Preload role-specific routes
  routes.forEach(RouteComponent => {
    // The components are already lazy-loaded, this just triggers the import
    try {
      // This would trigger the lazy loading
      React.createElement(RouteComponent);
    } catch (error) {
      console.warn(`Failed to preload route for role ${userRole}:`, error);
    }
  });
}

/**
 * Get route loading priority based on user role and current route
 */
export function getRouteLoadingPriority(userRole: UserRole, routeName: string): 'low' | 'normal' | 'high' | 'emergency' {
  // Emergency routes always have emergency priority
  if (routeName.includes('emergency') || routeName.includes('critical')) {
    return 'emergency';
  }

  // Dashboard routes have high priority
  if (routeName.includes('dashboard')) {
    return 'high';
  }

  // Medical data routes have normal priority
  if (routeName.includes('medical') || routeName.includes('consultation')) {
    return 'normal';
  }

  // Everything else has low priority
  return 'low';
}
