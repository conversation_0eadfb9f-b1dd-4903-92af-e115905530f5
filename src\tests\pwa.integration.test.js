/**
 * PWA Integration Tests
 * End-to-end testing for offline-first features
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  NetworkSimulator, 
  IndexedDBTestHelper, 
  ServiceWorkerTestHelper,
  VoiceTestHelper,
  PWATestSuite 
} from '../utils/pwaTestUtils';

describe('PWA Integration Tests', () => {
  let networkSim;
  let testSuite;

  beforeEach(() => {
    networkSim = new NetworkSimulator();
    testSuite = new PWATestSuite();
    
    // Mock console methods to reduce test noise
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(async () => {
    networkSim.reset();
    testSuite.cleanup();
    await IndexedDBTestHelper.clearAllDatabases();
    vi.restoreAllMocks();
  });

  describe('Network Simulation', () => {
    it('should simulate offline state', () => {
      expect(navigator.onLine).toBe(true);
      
      networkSim.goOffline();
      expect(navigator.onLine).toBe(false);
      
      networkSim.goOnline();
      expect(navigator.onLine).toBe(true);
    });

    it('should reset to original state', () => {
      const original = navigator.onLine;
      
      networkSim.goOffline();
      networkSim.reset();
      
      expect(navigator.onLine).toBe(original);
    });

    it('should simulate intermittent connectivity', async () => {
      const stopSimulation = networkSim.simulateIntermittent(100);
      const initialState = navigator.onLine;
      
      await new Promise(resolve => {
        setTimeout(() => {
          const afterToggle = navigator.onLine;
          expect(afterToggle).not.toBe(initialState);
          
          stopSimulation();
          resolve();
        }, 150);
      });
    });
  });

  describe('IndexedDB Management', () => {
    it('should get storage estimates', async () => {
      const sizes = await IndexedDBTestHelper.getDatabaseSizes();
      
      if (sizes) {
        expect(sizes).toHaveProperty('quota');
        expect(sizes).toHaveProperty('usage');
        expect(sizes).toHaveProperty('usagePercentage');
        expect(typeof sizes.quota).toBe('number');
        expect(typeof sizes.usage).toBe('number');
      }
    });

    it('should clear all databases', async () => {
      const result = await IndexedDBTestHelper.clearAllDatabases();
      expect(typeof result).toBe('boolean');
    });
  });

  describe('Service Worker Testing', () => {
    it('should get service worker status', async () => {
      const status = await ServiceWorkerTestHelper.getStatus();
      
      expect(status).toHaveProperty('supported');
      expect(typeof status.supported).toBe('boolean');
      
      if (status.supported) {
        expect(status).toHaveProperty('installing');
        expect(status).toHaveProperty('waiting');
        expect(status).toHaveProperty('active');
      }
    });

    it('should handle cache operations', async () => {
      const clearResult = await ServiceWorkerTestHelper.clearAllCaches();
      expect(typeof clearResult).toBe('boolean');
      
      const caches = await ServiceWorkerTestHelper.listCaches();
      expect(Array.isArray(caches)).toBe(true);
    });
  });

  describe('Voice Testing', () => {
    it('should generate fake audio chunks', () => {
      const chunk = VoiceTestHelper.generateFakeAudioChunk(100);
      
      expect(chunk).toBeInstanceOf(ArrayBuffer);
      expect(chunk.byteLength).toBeGreaterThan(0);
    });

    it('should simulate voice recording session', async () => {
      const chunks = [];
      const onChunk = vi.fn((chunk, metadata) => {
        chunks.push({ chunk, metadata });
      });
      
      await VoiceTestHelper.simulateVoiceSession(onChunk, 1, 200);
      
      expect(onChunk).toHaveBeenCalled();
      expect(chunks.length).toBeGreaterThan(0);
      
      chunks.forEach(({ chunk, metadata }) => {
        expect(chunk).toBeInstanceOf(ArrayBuffer);
        expect(metadata).toHaveProperty('chunkIndex');
        expect(metadata).toHaveProperty('duration');
        expect(metadata).toHaveProperty('quality');
      });
    });
  });

  describe('Offline Capability Tests', () => {
    it('should handle offline storage and sync', async () => {
      // Mock voice persistence service
      const mockVoicePersistence = {
        storeVoiceChunk: vi.fn().mockResolvedValue('chunk-123'),
        syncPendingChunks: vi.fn().mockResolvedValue({ success: 1, total: 1 })
      };

      await testSuite.testOfflineCapability(mockVoicePersistence);
      const results = testSuite.getResults();
      
      expect(results).toHaveLength(2);
      
      const storageTest = results.find(r => r.test === 'offline-storage');
      expect(storageTest).toBeDefined();
      expect(storageTest.passed).toBe(true);
      
      const syncTest = results.find(r => r.test === 'offline-sync');
      expect(syncTest).toBeDefined();
      expect(syncTest.passed).toBe(true);
    });

    it('should handle storage errors gracefully', async () => {
      const mockVoicePersistence = {
        storeVoiceChunk: vi.fn().mockRejectedValue(new Error('Storage failed')),
        syncPendingChunks: vi.fn().mockResolvedValue(null)
      };

      await testSuite.testOfflineCapability(mockVoicePersistence);
      const results = testSuite.getResults();
      
      const errorTest = results.find(r => r.test === 'offline-capability');
      expect(errorTest).toBeDefined();
      expect(errorTest.passed).toBe(false);
      expect(errorTest.error).toBe('Storage failed');
    });
  });

  describe('Full Test Suite', () => {
    it('should run all tests successfully', async () => {
      const mockVoicePersistence = {
        storeVoiceChunk: vi.fn().mockResolvedValue('chunk-123'),
        syncPendingChunks: vi.fn().mockResolvedValue({ success: 1, total: 1 })
      };

      const results = await testSuite.runAllTests(mockVoicePersistence);
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      // Check that we have tests for key components
      const testNames = results.map(r => r.test);
      expect(testNames).toContain('service-worker');
      expect(testNames).toContain('storage-estimation');
    });

    it('should handle test failures gracefully', async () => {
      const mockVoicePersistence = {
        storeVoiceChunk: vi.fn().mockRejectedValue(new Error('Test error')),
        syncPendingChunks: vi.fn().mockRejectedValue(new Error('Sync error'))
      };

      const results = await testSuite.runAllTests(mockVoicePersistence);
      
      expect(Array.isArray(results)).toBe(true);
      
      // Should still complete tests even with failures
      const failedTests = results.filter(r => !r.passed);
      expect(failedTests.length).toBeGreaterThan(0);
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle network disconnection during operation', async () => {
      const operations = [];
      
      // Start online
      expect(navigator.onLine).toBe(true);
      operations.push('online-operation');
      
      // Simulate network loss
      networkSim.goOffline();
      expect(navigator.onLine).toBe(false);
      operations.push('offline-operation');
      
      // Simulate network restoration
      networkSim.goOnline();
      expect(navigator.onLine).toBe(true);
      operations.push('sync-operation');
      
      expect(operations).toEqual([
        'online-operation',
        'offline-operation', 
        'sync-operation'
      ]);
    });

    it('should handle rapid network state changes', () => {
      let eventCount = 0;
      
      const handleOffline = () => eventCount++;
      const handleOnline = () => eventCount++;
      
      window.addEventListener('offline', handleOffline);
      window.addEventListener('online', handleOnline);
      
      // Rapid network changes
      networkSim.goOffline();
      networkSim.goOnline();
      networkSim.goOffline();
      networkSim.goOnline();
      
      // Events should have been dispatched
      expect(eventCount).toBeGreaterThan(0);
      
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('online', handleOnline);
    });
  });
});

describe('PWA Health Check', () => {
  it('should perform comprehensive health check', async () => {
    const { runPWAHealthCheck } = await import('../utils/pwaTestUtils');
    
    const health = await runPWAHealthCheck();
    
    expect(health).toHaveProperty('serviceWorker');
    expect(health).toHaveProperty('storage');
    expect(health).toHaveProperty('network');
    expect(health).toHaveProperty('backgroundSync');
    
    expect(typeof health.serviceWorker).toBe('boolean');
    expect(typeof health.storage).toBe('boolean');
    expect(typeof health.network).toBe('boolean');
    expect(typeof health.backgroundSync).toBe('boolean');
  });
});
