import React from 'react';
import CountryCard from './CountryCard';

const CountryGrid = ({ selectedCountry, onCountrySelect, selectedLanguage }) => {
  const countries = [
    {
      code: 'ghana',
      flag: '🇬🇭',
      name: {
        en: 'Ghana',
        tw: 'Ghana',
        yo: 'Ghana',
        sw: 'Ghana',
        af: 'Ghana'
      },
      localName: {
        en: 'Republic of Ghana',
        tw: 'Ghana Ɔman',
        yo: 'Ijọba Ghana',
        sw: 'Jamhuri ya Ghana',
        af: 'Republiek van Ghana'
      },
      description: {
        en: 'Traditional medicine integration with modern healthcare practices',
        tw: 'Atetesɛm aduro ne nnɛ akwahosan nhyehyɛe ka bom',
        yo: 'Isapọ oogun ibile pẹlu awọn iṣe ilera igbalode',
        sw: '<PERSON>ung<PERSON> wa dawa za jadi na mazoea ya kisasa ya huduma za afya',
        af: 'Tradisionele medisyne integrasie met moderne gesondheidsorgpraktyke'
      },
      specialties: ['Tropical Medicine', 'Community Health', 'Maternal Care']
    },
    {
      code: 'nigeria',
      flag: '🇳🇬',
      name: {
        en: 'Nigeria',
        tw: 'Nigeria',
        yo: 'Nàìjíríà',
        sw: 'Nigeria',
        af: 'Nigerië'
      },
      localName: {
        en: 'Federal Republic of Nigeria',
        tw: 'Nigeria Federal Ɔman',
        yo: 'Ijọba Apapo Nàìjíríà',
        sw: 'Jamhuri ya Shirikisho ya Nigeria',
        af: 'Federale Republiek van Nigerië'
      },
      description: {
        en: 'Diverse healthcare solutions for Africa\'s most populous nation',
        tw: 'Akwahosan ano aduru ahodoɔ ma Afrika nnipa dodoɔ pa ara',
        yo: 'Awọn ojutu ilera oriṣiriṣi fun orilẹ-ede ti o ni eniyan julọ ni Afirika',
        sw: 'Suluhisho mbalimbali za afya kwa taifa lenye idadi kubwa zaidi ya watu Afrika',
        af: 'Diverse gesondheidsorg oplossings vir Afrika se mees bevolkte nasie'
      },
      specialties: ['Internal Medicine', 'Pediatrics', 'Public Health']
    },
    {
      code: 'kenya',
      flag: '🇰🇪',
      name: {
        en: 'Kenya',
        tw: 'Kenya',
        yo: 'Kenya',
        sw: 'Kenya',
        af: 'Kenia'
      },
      localName: {
        en: 'Republic of Kenya',
        tw: 'Kenya Ɔman',
        yo: 'Ijọba Kenya',
        sw: 'Jamhuri ya Kenya',
        af: 'Republiek van Kenia'
      },
      description: {
        en: 'Advanced digital health initiatives and mobile health solutions',
        tw: 'Digital akwahosan nnwuma ne mobile akwahosan ano aduru',
        yo: 'Awọn ipilẹṣẹ ilera oni-nọmba to ti ni ilọsiwaju ati awọn ojutu ilera alagbeka',
        sw: 'Miradi ya hali ya juu ya afya ya kidijitali na suluhisho za afya za simu',
        af: 'Gevorderde digitale gesondheid inisiatiewe en mobiele gesondheid oplossings'
      },
      specialties: ['Digital Health', 'Emergency Medicine', 'Rural Health']
    },
    {
      code: 'south-africa',
      flag: '🇿🇦',
      name: {
        en: 'South Africa',
        tw: 'South Africa',
        yo: 'Gúúsù Afirika',
        sw: 'Afrika Kusini',
        af: 'Suid-Afrika'
      },
      localName: {
        en: 'Republic of South Africa',
        tw: 'South Africa Ɔman',
        yo: 'Ijọba Gúúsù Afirika',
        sw: 'Jamhuri ya Afrika Kusini',
        af: 'Republiek van Suid-Afrika'
      },
      description: {
        en: 'Comprehensive healthcare with multilingual and multicultural support',
        tw: 'Akwahosan a ɛyɛ dua ne kasa ahodoɔ ne amammerɛ ahodoɔ mmoa',
        yo: 'Ilera ti o peye pẹlu atilẹyin opolopọ ede ati aṣa pupọ',
        sw: 'Huduma kamili za afya zenye msaada wa lugha nyingi na kitamaduni',
        af: 'Omvattende gesondheidsorg met meertalige en multikulturele ondersteuning'
      },
      specialties: ['HIV/AIDS Care', 'Oncology', 'Mental Health']
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
      {countries.map((country) => (
        <CountryCard
          key={country.code}
          country={country}
          isSelected={selectedCountry === country.code}
          onSelect={() => onCountrySelect(country.code)}
          selectedLanguage={selectedLanguage}
        />
      ))}
    </div>
  );
};

export default CountryGrid;