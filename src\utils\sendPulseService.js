/**
 * SendPulse Email Service
 * Comprehensive email service integration for VoiceHealth AI
 */

class SendPulseService {
  constructor() {
    this.apiUserId = import.meta.env.VITE_SENDPULSE_API_USER_ID;
    this.apiSecret = import.meta.env.VITE_SENDPULSE_API_SECRET;
    this.tokenStorage = import.meta.env.VITE_SENDPULSE_TOKEN_STORAGE || 'sendpulse_token';
    this.fromEmail = import.meta.env.VITE_FROM_EMAIL || '<EMAIL>';
    this.fromName = import.meta.env.VITE_FROM_NAME || 'VoiceHealth AI';
    this.baseUrl = 'https://api.sendpulse.com';
    this.accessToken = null;
    this.tokenExpiry = null;

    if (!this.apiUserId || !this.apiSecret) {
      console.warn('SendPulse API credentials not found in environment variables');
    }
  }

  /**
   * Get access token for SendPulse API
   */
  async getAccessToken() {
    try {
      // Check if we have a valid cached token
      if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
        return this.accessToken;
      }

      // Check localStorage for cached token
      const cachedToken = localStorage.getItem(this.tokenStorage);
      if (cachedToken) {
        const tokenData = JSON.parse(cachedToken);
        if (Date.now() < tokenData.expiry) {
          this.accessToken = tokenData.token;
          this.tokenExpiry = tokenData.expiry;
          return this.accessToken;
        }
      }

      // Get new token
      const response = await fetch(`${this.baseUrl}/oauth/access_token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          grant_type: 'client_credentials',
          client_id: this.apiUserId,
          client_secret: this.apiSecret,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to get access token: ${response.status}`);
      }

      const data = await response.json();
      this.accessToken = data.access_token;
      this.tokenExpiry = Date.now() + (data.expires_in * 1000) - 60000; // 1 minute buffer

      // Cache the token
      localStorage.setItem(this.tokenStorage, JSON.stringify({
        token: this.accessToken,
        expiry: this.tokenExpiry,
      }));

      return this.accessToken;
    } catch (error) {
      console.error('SendPulse authentication error:', error);
      throw new Error('Failed to authenticate with SendPulse API');
    }
  }

  /**
   * Make authenticated API request
   */
  async makeRequest(endpoint, options = {}) {
    try {
      const token = await this.getAccessToken();
      
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`SendPulse API error: ${response.status} - ${errorData}`);
      }

      return await response.json();
    } catch (error) {
      console.error('SendPulse API request failed:', error);
      throw error;
    }
  }

  /**
   * Send transactional email
   */
  async sendEmail({ to, subject, html, text, templateId, templateVariables = {} }) {
    try {
      const emailData = {
        email: {
          from: {
            name: this.fromName,
            email: this.fromEmail,
          },
          to: Array.isArray(to) ? to : [{ email: to }],
          subject,
        },
      };

      // Use template if provided
      if (templateId) {
        emailData.email.template = {
          id: templateId,
          variables: templateVariables,
        };
      } else {
        // Use HTML/text content
        if (html) emailData.email.html = html;
        if (text) emailData.email.text = text;
      }

      const result = await this.makeRequest('/smtp/emails', {
        method: 'POST',
        body: JSON.stringify(emailData),
      });

      return {
        success: true,
        data: result,
        messageId: result?.id || null,
      };
    } catch (error) {
      console.error('Failed to send email:', error);
      return {
        success: false,
        error: error.message || 'Failed to send email',
      };
    }
  }

  /**
   * Send welcome email to new users
   */
  async sendWelcomeEmail(userEmail, userName, verificationLink = null) {
    const subject = 'Welcome to VoiceHealth AI - Your Journey to Better Health Starts Here!';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to VoiceHealth AI</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .features { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
          .feature { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Welcome to VoiceHealth AI! 🎉</h1>
          <p>Your personal AI health consultation platform</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>We're thrilled to have you join the VoiceHealth AI community. You're now part of a revolutionary platform that brings AI-powered healthcare consultations directly to you.</p>
          
          ${verificationLink ? `
            <p><strong>Important:</strong> Please verify your email address to activate your account and start using our services.</p>
            <a href="${verificationLink}" class="button">Verify Email Address</a>
          ` : ''}
          
          <div class="features">
            <div class="feature">
              <h3>🤖 AI Specialists</h3>
              <p>Access multiple AI health agents specialized in different medical fields</p>
            </div>
            <div class="feature">
              <h3>🎙️ Voice Consultations</h3>
              <p>Natural conversation-based health consultations</p>
            </div>
            <div class="feature">
              <h3>📊 Health Insights</h3>
              <p>Personalized health recommendations and tracking</p>
            </div>
            <div class="feature">
              <h3>🔐 Secure & Private</h3>
              <p>Your health data is encrypted and completely confidential</p>
            </div>
          </div>
          
          <h3>What's Next?</h3>
          <ol>
            <li>${verificationLink ? 'Verify your email address' : 'Complete your profile setup'}</li>
            <li>Choose your subscription plan</li>
            <li>Start your first AI consultation</li>
            <li>Explore our specialized health agents</li>
          </ol>
          
          <p>If you have any questions or need help getting started, our support team is here to assist you.</p>
          
          <p>Welcome aboard!<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(userEmail, userName, verificationLink) {
    const subject = 'Verify Your Email - VoiceHealth AI';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #667eea; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
          .security-note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>📧 Email Verification</h1>
          <p>VoiceHealth AI</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Thank you for signing up with VoiceHealth AI. To complete your registration and start using our AI health consultation services, please verify your email address.</p>
          
          <a href="${verificationLink}" class="button">Verify Email Address</a>
          
          <p>This verification link will expire in 24 hours.</p>
          
          <div class="security-note">
            <h4>🔐 Security Notice</h4>
            <p>If you didn't create an account with VoiceHealth AI, please ignore this email. Your email address will not be used for any further communications.</p>
          </div>
          
          <p>If you're having trouble clicking the button above, copy and paste the following link into your browser:</p>
          <p style="word-break: break-all; color: #667eea;">${verificationLink}</p>
          
          <p>Best regards,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(userEmail, userName, resetLink) {
    const subject = 'Reset Your Password - VoiceHealth AI';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #e74c3c; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
          .security-note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🔐 Password Reset</h1>
          <p>VoiceHealth AI</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>We received a request to reset your password for your VoiceHealth AI account. If you made this request, click the button below to reset your password.</p>
          
          <a href="${resetLink}" class="button">Reset Password</a>
          
          <p>This password reset link will expire in 1 hour for security reasons.</p>
          
          <div class="security-note">
            <h4>🔐 Security Notice</h4>
            <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged. Consider reviewing your account security if you receive unexpected password reset emails.</p>
          </div>
          
          <p>If you're having trouble clicking the button above, copy and paste the following link into your browser:</p>
          <p style="word-break: break-all; color: #e74c3c;">${resetLink}</p>
          
          <p>Best regards,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send consultation confirmation email
   */
  async sendConsultationConfirmation(userEmail, userName, sessionData) {
    const subject = `Consultation Scheduled - ${sessionData?.title || 'Health Consultation'}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Consultation Confirmed</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #27ae60; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .session-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #27ae60; }
          .button { display: inline-block; background: #27ae60; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>✅ Consultation Confirmed!</h1>
          <p>VoiceHealth AI</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Your AI health consultation has been successfully scheduled. Our specialized AI agents are ready to provide you with personalized health insights.</p>
          
          <div class="session-details">
            <h3>📋 Consultation Details</h3>
            <p><strong>Session Title:</strong> ${sessionData?.title || 'General Health Consultation'}</p>
            <p><strong>Primary Agent:</strong> ${sessionData?.agentName || 'General Health AI'}</p>
            <p><strong>Specialty:</strong> ${sessionData?.specialty || 'General Medicine'}</p>
            <p><strong>Session ID:</strong> #${sessionData?.id || 'N/A'}</p>
            <p><strong>Status:</strong> ${sessionData?.status || 'Scheduled'}</p>
          </div>
          
          <h3>🎯 What to Expect</h3>
          <ul>
            <li>Natural voice-based conversation with AI specialists</li>
            <li>Personalized health recommendations</li>
            <li>Real-time transcription of your consultation</li>
            <li>Detailed session summary and insights</li>
            <li>Follow-up recommendations if needed</li>
          </ul>
          
          <h3>📝 Preparation Tips</h3>
          <ul>
            <li>Prepare a list of your symptoms or concerns</li>
            <li>Have your medical history ready</li>
            <li>Ensure you're in a quiet environment</li>
            <li>Test your microphone and speakers</li>
          </ul>
          
          <p>You can access your consultation through your dashboard or by clicking the button below:</p>
          
          <a href="${process.env.NODE_ENV === 'production' ? 'https://voicehealthai.com' : 'http://localhost:3000'}/voice-consultation" class="button">Start Consultation</a>
          
          <p>If you need to reschedule or have any questions, please contact our support team.</p>
          
          <p>Best of health,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send consultation completion email
   */
  async sendConsultationSummary(userEmail, userName, sessionData, recommendations = []) {
    const subject = `Your Health Consultation Summary - ${sessionData?.title || 'Consultation Complete'}`;
    
    const recommendationsHtml = recommendations?.length > 0 ? `
      <div class="recommendations">
        <h3>🎯 AI Recommendations</h3>
        ${recommendations.map(rec => `
          <div class="recommendation">
            <h4>${rec.title}</h4>
            <p><strong>Priority:</strong> ${rec.priority}</p>
            <p><strong>Category:</strong> ${rec.category}</p>
            <p>${rec.content}</p>
          </div>
        `).join('')}
      </div>
    ` : '';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Consultation Summary</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3498db; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .session-summary { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db; }
          .recommendations { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .recommendation { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 10px 0; }
          .button { display: inline-block; background: #3498db; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>📋 Consultation Complete</h1>
          <p>Your Health Summary is Ready</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Thank you for using VoiceHealth AI for your health consultation. Below is a summary of your session and personalized recommendations from our AI specialists.</p>
          
          <div class="session-summary">
            <h3>📊 Session Overview</h3>
            <p><strong>Session:</strong> ${sessionData?.title || 'Health Consultation'}</p>
            <p><strong>Date:</strong> ${sessionData?.date ? new Date(sessionData.date).toLocaleDateString() : new Date().toLocaleDateString()}</p>
            <p><strong>Duration:</strong> ${sessionData?.duration || 'N/A'}</p>
            <p><strong>Primary Agent:</strong> ${sessionData?.agentName || 'General Health AI'}</p>
            <p><strong>Status:</strong> Completed</p>
          </div>
          
          ${recommendationsHtml}
          
          <h3>📈 Next Steps</h3>
          <ul>
            <li>Review and follow the AI recommendations</li>
            <li>Schedule follow-up consultations if needed</li>
            <li>Track your progress in your health dashboard</li>
            <li>Consult with healthcare professionals for serious concerns</li>
          </ul>
          
          <p><strong>Important:</strong> This consultation is for informational purposes only and should not replace professional medical advice. Always consult with qualified healthcare providers for medical decisions.</p>
          
          <a href="${process.env.NODE_ENV === 'production' ? 'https://voicehealthai.com' : 'http://localhost:3000'}/session-dashboard-history" class="button">View Full History</a>
          
          <p>Thank you for trusting VoiceHealth AI with your health journey!</p>
          
          <p>Stay healthy,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(userEmail, userName, paymentData, subscriptionData) {
    const subject = `Payment Confirmed - ${subscriptionData?.name || 'Subscription Plan'}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2ecc71; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .payment-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2ecc71; }
          .button { display: inline-block; background: #2ecc71; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
          .features { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>💳 Payment Successful!</h1>
          <p>Your subscription is now active</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Thank you for your payment! Your VoiceHealth AI subscription has been activated successfully. You now have full access to our AI health consultation platform.</p>
          
          <div class="payment-details">
            <h3>💰 Payment Details</h3>
            <p><strong>Plan:</strong> ${subscriptionData?.name || 'N/A'}</p>
            <p><strong>Amount:</strong> ₦${paymentData?.amount?.toLocaleString() || 'N/A'}</p>
            <p><strong>Payment Method:</strong> ${paymentData?.payment_method || 'Card'}</p>
            <p><strong>Transaction ID:</strong> ${paymentData?.reference || 'N/A'}</p>
            <p><strong>Date:</strong> ${paymentData?.date ? new Date(paymentData.date).toLocaleDateString() : new Date().toLocaleDateString()}</p>
            <p><strong>Status:</strong> Completed</p>
          </div>
          
          <div class="features">
            <h3>🎉 Your Plan Includes</h3>
            ${subscriptionData?.features?.map(feature => `<p>✅ ${feature}</p>`).join('') || '<p>✅ AI Health Consultations</p>'}
            <p><strong>Duration:</strong> ${subscriptionData?.duration_days ? `${subscriptionData.duration_days} days` : '30 days'}</p>
            <p><strong>Consultation Credits:</strong> ${subscriptionData?.consultation_credits === 0 ? 'Unlimited' : (subscriptionData?.consultation_credits || 'N/A')}</p>
          </div>
          
          <h3>🚀 Get Started</h3>
          <ol>
            <li>Complete your health profile</li>
            <li>Choose your specialized AI agents</li>
            <li>Start your first consultation</li>
            <li>Explore advanced features</li>
          </ol>
          
          <a href="${process.env.NODE_ENV === 'production' ? 'https://voicehealthai.com' : 'http://localhost:3000'}/voice-consultation" class="button">Start Your First Consultation</a>
          
          <p>Your receipt and subscription details are available in your account dashboard. If you have any questions about your subscription or need assistance, our support team is here to help.</p>
          
          <p>Welcome to premium healthcare powered by AI!</p>
          
          <p>Best regards,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send payment failed notification
   */
  async sendPaymentFailed(userEmail, userName, paymentData) {
    const subject = 'Payment Failed - VoiceHealth AI';
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Failed</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #e74c3c; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .payment-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #e74c3c; }
          .button { display: inline-block; background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
          .help-section { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>❌ Payment Failed</h1>
          <p>We couldn't process your payment</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>We encountered an issue processing your payment for VoiceHealth AI subscription. Don't worry - no charges were made to your account.</p>
          
          <div class="payment-details">
            <h3>💳 Payment Details</h3>
            <p><strong>Amount:</strong> ₦${paymentData?.amount?.toLocaleString() || 'N/A'}</p>
            <p><strong>Reference:</strong> ${paymentData?.reference || 'N/A'}</p>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            <p><strong>Status:</strong> Failed</p>
          </div>
          
          <h3>🔧 Common Solutions</h3>
          <ul>
            <li>Check that your card has sufficient funds</li>
            <li>Verify your card details are correct</li>
            <li>Ensure your card is enabled for online transactions</li>
            <li>Try a different payment method</li>
            <li>Contact your bank if the issue persists</li>
          </ul>
          
          <div class="help-section">
            <h4>💡 Need Help?</h4>
            <p>Our support team is available 24/7 to assist you with payment issues. We accept various payment methods including cards, bank transfers, and mobile money.</p>
          </div>
          
          <a href="${process.env.NODE_ENV === 'production' ? 'https://voicehealthai.com' : 'http://localhost:3000'}/payment-plans" class="button">Try Payment Again</a>
          
          <p>You can retry your payment at any time through your account dashboard. Your subscription will be activated immediately once payment is successful.</p>
          
          <p>Thank you for choosing VoiceHealth AI!</p>
          
          <p>Best regards,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send subscription expiry reminder
   */
  async sendSubscriptionReminder(userEmail, userName, subscriptionData, daysRemaining) {
    const subject = `Subscription Reminder - ${daysRemaining} days remaining`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Subscription Reminder</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f39c12; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .subscription-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f39c12; }
          .button { display: inline-block; background: #f39c12; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>⏰ Subscription Expiring Soon</h1>
          <p>${daysRemaining} days remaining</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Your VoiceHealth AI subscription will expire in ${daysRemaining} days. To continue enjoying uninterrupted access to our AI health consultation services, please renew your subscription.</p>
          
          <div class="subscription-details">
            <h3>📋 Current Subscription</h3>
            <p><strong>Plan:</strong> ${subscriptionData?.name || 'N/A'}</p>
            <p><strong>Expires:</strong> ${subscriptionData?.expires_at ? new Date(subscriptionData.expires_at).toLocaleDateString() : 'N/A'}</p>
            <p><strong>Remaining Credits:</strong> ${subscriptionData?.consultation_credits_remaining === 0 ? 'Unlimited' : (subscriptionData?.consultation_credits_remaining || 'N/A')}</p>
          </div>
          
          <h3>💡 Why Renew?</h3>
          <ul>
            <li>Continue your personalized health journey</li>
            <li>Access to all specialized AI agents</li>
            <li>Maintain your consultation history</li>
            <li>Keep your health insights and recommendations</li>
          </ul>
          
          <a href="${process.env.NODE_ENV === 'production' ? 'https://voicehealthai.com' : 'http://localhost:3000'}/payment-plans" class="button">Renew Subscription</a>
          
          <p>After expiry, your account will remain active but with limited access. You can renew at any time to restore full functionality.</p>
          
          <p>Thank you for being a valued member of VoiceHealth AI!</p>
          
          <p>Best regards,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Send general notification email
   */
  async sendNotification(userEmail, userName, title, message, actionUrl = null, actionText = 'Learn More') {
    const subject = title;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #9b59b6; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #9b59b6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>📢 ${title}</h1>
          <p>VoiceHealth AI</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>${message}</p>
          
          ${actionUrl ? `<a href="${actionUrl}" class="button">${actionText}</a>` : ''}
          
          <p>Best regards,<br>
          <strong>The VoiceHealth AI Team</strong></p>
        </div>
      </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
    });
  }

  /**
   * Test email configuration
   */
  async testConfiguration() {
    try {
      const token = await this.getAccessToken();
      return {
        success: true,
        message: 'SendPulse configuration is working correctly',
        token: token ? 'Token received' : 'No token'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Configuration test failed'
      };
    }
  }
}

const sendPulseService = new SendPulseService();
export default sendPulseService;