/**
 * Profile Completion Hook
 * 
 * React hook for managing profile completion state and tracking
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/SimpleAuthContext';
import { profileCompletionService, ProfileCompletionData, ProfileValidationResult } from '../services/ProfileCompletionService';

export interface UseProfileCompletionReturn {
  // State
  completionData: ProfileCompletionData | null;
  validationResult: ProfileValidationResult | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  refreshCompletion: () => Promise<void>;
  validateForConsultation: () => Promise<ProfileValidationResult>;
  getCompletionSummary: () => Promise<{
    percentage: number;
    nextSteps: string[];
    benefits: string[];
  }>;
  
  // Computed values
  isConsultationReady: boolean;
  criticalFieldsMissing: boolean;
  completionPercentage: number;
  nextPriorityField: string | null;
}

export const useProfileCompletion = (autoRefresh: boolean = true): UseProfileCompletionReturn => {
  const { user } = useAuth();
  const [completionData, setCompletionData] = useState<ProfileCompletionData | null>(null);
  const [validationResult, setValidationResult] = useState<ProfileValidationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Refresh profile completion data
   */
  const refreshCompletion = useCallback(async () => {
    if (!user?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const data = await profileCompletionService.calculateProfileCompletion(user.id);
      setCompletionData(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load profile completion';
      setError(errorMessage);
      console.error('Profile completion error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  /**
   * Validate profile for consultation readiness
   */
  const validateForConsultation = useCallback(async (): Promise<ProfileValidationResult> => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await profileCompletionService.validateForConsultation(user.id);
      setValidationResult(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate profile';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  /**
   * Get completion summary for dashboard
   */
  const getCompletionSummary = useCallback(async () => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    return await profileCompletionService.getCompletionSummary(user.id);
  }, [user?.id]);

  // Auto-refresh on mount and user change
  useEffect(() => {
    if (autoRefresh && user?.id) {
      refreshCompletion();
    }
  }, [user?.id, autoRefresh, refreshCompletion]);

  // Computed values
  const isConsultationReady = validationResult?.isValid ?? false;
  const criticalFieldsMissing = (completionData?.criticalMissing.length ?? 0) > 0;
  const completionPercentage = completionData?.completionPercentage ?? 0;
  const nextPriorityField = completionData?.missingFields[0]?.label ?? null;

  return {
    // State
    completionData,
    validationResult,
    isLoading,
    error,
    
    // Actions
    refreshCompletion,
    validateForConsultation,
    getCompletionSummary,
    
    // Computed values
    isConsultationReady,
    criticalFieldsMissing,
    completionPercentage,
    nextPriorityField
  };
};

/**
 * Hook for profile completion with real-time updates
 */
export const useProfileCompletionWithUpdates = () => {
  const profileCompletion = useProfileCompletion(true);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());

  /**
   * Trigger update when profile data changes
   */
  const triggerUpdate = useCallback(() => {
    setLastUpdateTime(new Date());
    profileCompletion.refreshCompletion();
  }, [profileCompletion]);

  /**
   * Update completion after profile changes
   */
  const updateAfterProfileChange = useCallback(async (section: string) => {
    // Add a small delay to ensure database updates are complete
    setTimeout(() => {
      triggerUpdate();
    }, 500);
  }, [triggerUpdate]);

  return {
    ...profileCompletion,
    lastUpdateTime,
    triggerUpdate,
    updateAfterProfileChange
  };
};

/**
 * Hook for section-specific completion tracking
 */
export const useSectionCompletion = (section: 'personalInfo' | 'emergencyInfo' | 'medicalHistory' | 'lifestyle') => {
  const { completionData, refreshCompletion } = useProfileCompletion();
  
  const sectionProgress = completionData?.sectionProgress[section] ?? 0;
  const sectionMissingFields = completionData?.missingFields.filter(field => field.section === section) ?? [];
  const isSectionComplete = sectionProgress >= 100;
  const nextFieldInSection = sectionMissingFields[0]?.label ?? null;

  return {
    progress: sectionProgress,
    missingFields: sectionMissingFields,
    isComplete: isSectionComplete,
    nextField: nextFieldInSection,
    refreshCompletion
  };
};

export default useProfileCompletion;
