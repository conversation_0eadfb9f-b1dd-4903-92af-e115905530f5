import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import triageService from '../../../utils/triageService';

const AutomaticAssessmentEngine = ({ 
  onAssessmentComplete, 
  currentAssessment, 
  systemConfig, 
  userProfile 
}) => {
  const [selectedSymptoms, setSelectedSymptoms] = useState([]);
  const [patientInfo, setPatientInfo] = useState({
    age: userProfile?.age || '',
    gender: userProfile?.gender || '',
    medicalHistory: userProfile?.medical_conditions || [],
    currentMedications: userProfile?.medications || []
  });
  const [assessmentInProgress, setAssessmentInProgress] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const availableSymptoms = [
    { id: 'chest_pain', label: 'Chest Pain', category: 'cardiovascular', severity: 'high' },
    { id: 'difficulty_breathing', label: 'Difficulty Breathing', category: 'respiratory', severity: 'high' },
    { id: 'severe_headache', label: 'Severe Headache', category: 'neurological', severity: 'high' },
    { id: 'high_fever', label: 'High Fever (>38.5°C)', category: 'infectious', severity: 'medium' },
    { id: 'persistent_cough', label: 'Persistent Cough', category: 'respiratory', severity: 'medium' },
    { id: 'abdominal_pain', label: 'Abdominal Pain', category: 'gastrointestinal', severity: 'medium' },
    { id: 'nausea_vomiting', label: 'Nausea/Vomiting', category: 'gastrointestinal', severity: 'low' },
    { id: 'dizziness', label: 'Dizziness', category: 'neurological', severity: 'medium' },
    { id: 'fatigue', label: 'Extreme Fatigue', category: 'general', severity: 'low' },
    { id: 'skin_rash', label: 'Skin Rash', category: 'dermatological', severity: 'low' },
    { id: 'joint_pain', label: 'Joint Pain', category: 'musculoskeletal', severity: 'low' },
    { id: 'loss_of_consciousness', label: 'Loss of Consciousness', category: 'neurological', severity: 'critical' },
    { id: 'severe_bleeding', label: 'Severe Bleeding', category: 'trauma', severity: 'critical' },
    { id: 'inability_to_speak', label: 'Inability to Speak', category: 'neurological', severity: 'critical' },
    { id: 'severe_dehydration', label: 'Severe Dehydration', category: 'general', severity: 'high' }
  ];

  const symptomCategories = [
    { id: 'cardiovascular', name: 'Heart & Circulation', icon: 'Heart', color: 'red' },
    { id: 'respiratory', name: 'Breathing', icon: 'Zap', color: 'blue' },
    { id: 'neurological', name: 'Brain & Nerves', icon: 'Brain', color: 'purple' },
    { id: 'infectious', name: 'Infection', icon: 'Thermometer', color: 'orange' },
    { id: 'gastrointestinal', name: 'Stomach & Digestion', icon: 'Coffee', color: 'yellow' },
    { id: 'general', name: 'General Symptoms', icon: 'User', color: 'gray' },
    { id: 'dermatological', name: 'Skin', icon: 'Layers', color: 'green' },
    { id: 'musculoskeletal', name: 'Muscles & Bones', icon: 'Zap', color: 'indigo' },
    { id: 'trauma', name: 'Injury/Trauma', icon: 'Shield', color: 'red' }
  ];

  useEffect(() => {
    if (currentAssessment) {
      setShowResults(true);
    }
  }, [currentAssessment]);

  const handleSymptomToggle = (symptomId) => {
    setSelectedSymptoms(prev => {
      if (prev.includes(symptomId)) {
        return prev.filter(id => id !== symptomId);
      } else {
        return [...prev, symptomId];
      }
    });
  };

  const handleRunAssessment = async () => {
    if (selectedSymptoms.length === 0) {
      alert('Please select at least one symptom to assess.');
      return;
    }

    setAssessmentInProgress(true);
    try {
      const result = await triageService.assessUrgency(selectedSymptoms, {
        ...userProfile,
        ...patientInfo
      });

      if (result.success) {
        onAssessmentComplete(result.data);
        setShowResults(true);
      } else {
        alert(`Assessment failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Assessment error:', error);
      alert('Failed to complete assessment. Please try again.');
    } finally {
      setAssessmentInProgress(false);
    }
  };

  const handleClearAssessment = () => {
    setSelectedSymptoms([]);
    setShowResults(false);
  };

  const getSeverityColor = (severity) => {
    const colors = {
      critical: 'red',
      high: 'orange', 
      medium: 'yellow',
      low: 'green'
    };
    return colors[severity] || 'gray';
  };

  const getUrgencyColor = (level) => {
    const colors = {
      critical: 'red',
      urgent: 'orange',
      semi_urgent: 'yellow',
      routine: 'green',
      self_care: 'blue'
    };
    return colors[level] || 'gray';
  };

  return (
    <div className="space-y-6">
      
      {/* Assessment Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">
            Automatic Assessment Engine
          </h3>
          <p className="text-text-secondary">
            AI-powered symptom analysis with regional disease pattern recognition
          </p>
        </div>
        
        {selectedSymptoms.length > 0 && (
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClearAssessment}
              iconName="X"
              iconPosition="left"
              size="sm"
            >
              Clear
            </Button>
            <Button
              onClick={handleRunAssessment}
              loading={assessmentInProgress}
              iconName="Play"
              iconPosition="left"
              size="sm"
            >
              Run Assessment
            </Button>
          </div>
        )}
      </div>

      {/* Patient Information Panel */}
      <div className="bg-surface-secondary rounded-lg p-4 border border-border">
        <h4 className="font-medium text-text-primary mb-3 flex items-center">
          <Icon name="User" size={18} className="mr-2" />
          Patient Information
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">Age</label>
            <input
              type="number"
              value={patientInfo.age}
              onChange={(e) => setPatientInfo(prev => ({ ...prev, age: e.target.value }))}
              className="w-full px-3 py-2 border border-border rounded-md bg-background"
              placeholder="Enter age"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">Gender</label>
            <select
              value={patientInfo.gender}
              onChange={(e) => setPatientInfo(prev => ({ ...prev, gender: e.target.value }))}
              className="w-full px-3 py-2 border border-border rounded-md bg-background"
            >
              <option value="">Select gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">Region</label>
            <div className="w-full px-3 py-2 border border-border rounded-md bg-gray-50 text-text-secondary">
              {userProfile?.country || 'Ghana'} (Auto-detected)
            </div>
          </div>
        </div>
      </div>

      {/* Symptom Selection */}
      <div>
        <h4 className="font-medium text-text-primary mb-4 flex items-center">
          <Icon name="Stethoscope" size={18} className="mr-2" />
          Select Symptoms ({selectedSymptoms.length} selected)
        </h4>

        {symptomCategories.map(category => {
          const categorySymptoms = availableSymptoms.filter(s => s.category === category.id);
          
          return (
            <div key={category.id} className="mb-6">
              <div className={`flex items-center mb-3 text-${category.color}-600`}>
                <Icon name={category.icon} size={18} className="mr-2" />
                <h5 className="font-medium">{category.name}</h5>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {categorySymptoms.map(symptom => {
                  const isSelected = selectedSymptoms.includes(symptom.id);
                  const severityColor = getSeverityColor(symptom.severity);
                  
                  return (
                    <button
                      key={symptom.id}
                      onClick={() => handleSymptomToggle(symptom.id)}
                      className={`
                        p-3 rounded-lg border-2 transition-all duration-200 text-left
                        ${isSelected 
                          ? `border-${severityColor}-500 bg-${severityColor}-50` 
                          : 'border-border bg-background hover:bg-surface-hover'
                        }
                      `}
                    >
                      <div className="flex items-center justify-between">
                        <span className={`font-medium ${isSelected ? `text-${severityColor}-800` : 'text-text-primary'}`}>
                          {symptom.label}
                        </span>
                        
                        <div className="flex items-center space-x-2">
                          <span className={`text-xs px-2 py-1 rounded bg-${severityColor}-100 text-${severityColor}-800`}>
                            {symptom.severity}
                          </span>
                          {isSelected && <Icon name="Check" size={16} className={`text-${severityColor}-600`} />}
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>

      {/* Assessment Results */}
      {showResults && currentAssessment && (
        <div className="bg-surface rounded-lg border border-border p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-text-primary flex items-center">
              <Icon name="BarChart3" size={20} className="mr-2" />
              Assessment Results
            </h4>
            <span className="text-sm text-text-secondary">
              {new Date(currentAssessment.timestamp).toLocaleString()}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            
            {/* Urgency Score */}
            <div className="bg-surface-secondary rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-secondary">Urgency Score</span>
                <div className={`text-2xl font-bold text-${getUrgencyColor(currentAssessment.urgencyLevel)}-600`}>
                  {currentAssessment.urgencyScore}/10
                </div>
              </div>
              <div className={`text-lg font-semibold text-${getUrgencyColor(currentAssessment.urgencyLevel)}-800`}>
                {currentAssessment.urgencyLevel?.replace('_', ' ').toUpperCase()}
              </div>
              <div className="text-sm text-text-secondary mt-1">
                Time Frame: {currentAssessment.timeFrame}
              </div>
            </div>

            {/* Red Flags */}
            <div className="bg-surface-secondary rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-secondary">Red Flags</span>
                <div className={`text-2xl font-bold ${currentAssessment.redFlags?.length > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {currentAssessment.redFlags?.length || 0}
                </div>
              </div>
              {currentAssessment.redFlags?.length > 0 ? (
                <div className="text-sm text-red-800">
                  Critical symptoms detected
                </div>
              ) : (
                <div className="text-sm text-green-800">
                  No critical symptoms
                </div>
              )}
            </div>

            {/* Detected Conditions */}
            <div className="bg-surface-secondary rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-secondary">Possible Conditions</span>
                <div className="text-2xl font-bold text-blue-600">
                  {currentAssessment.detectedConditions?.length || 0}
                </div>
              </div>
              <div className="text-sm text-blue-800">
                Regional patterns analyzed
              </div>
            </div>
          </div>

          {/* Red Flags Details */}
          {currentAssessment.redFlags?.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h5 className="font-semibold text-red-800 mb-2 flex items-center">
                <Icon name="AlertTriangle" size={16} className="mr-2" />
                Critical Symptoms Detected
              </h5>
              <ul className="space-y-1">
                {currentAssessment.redFlags.map((flag, index) => (
                  <li key={index} className="text-sm text-red-700 flex items-center">
                    <Icon name="Alert" size={14} className="mr-2" />
                    {flag.replace('_', ' ').toUpperCase()}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Detected Conditions Details */}
          {currentAssessment.detectedConditions?.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h5 className="font-semibold text-blue-800 mb-2 flex items-center">
                <Icon name="FileText" size={16} className="mr-2" />
                Possible Regional Conditions
              </h5>
              <div className="space-y-2">
                {currentAssessment.detectedConditions.map((condition, index) => (
                  <div key={index} className="bg-white rounded p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-blue-900 capitalize">
                        {condition.disease?.replace('_', ' ')}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        condition.confidence === 'high' ? 'bg-red-100 text-red-800' :
                        condition.confidence === 'medium'? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {condition.confidence} confidence
                      </span>
                    </div>
                    <p className="text-sm text-blue-700">{condition.recommendation}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommended Actions */}
          {currentAssessment.recommendedProtocols?.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h5 className="font-semibold text-green-800 mb-2 flex items-center">
                <Icon name="CheckCircle" size={16} className="mr-2" />
                Recommended Actions
              </h5>
              <div className="space-y-2">
                {currentAssessment.recommendedProtocols.map((protocol, index) => (
                  <div key={index} className="bg-white rounded p-3">
                    <div className="font-medium text-green-900 capitalize mb-1">
                      {protocol.name?.replace('_', ' ')}
                    </div>
                    <p className="text-sm text-green-700 mb-2">{protocol.instructions}</p>
                    <div className="text-xs text-green-600">
                      Time limit: {protocol.timeLimit} minutes
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

        </div>
      )}

    </div>
  );
};

export default AutomaticAssessmentEngine;