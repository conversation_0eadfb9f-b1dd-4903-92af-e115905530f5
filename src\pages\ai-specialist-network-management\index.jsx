import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import NetworkTopologyVisualization from './components/NetworkTopologyVisualization';
import SpecialtyAgentPanel from './components/SpecialtyAgentPanel';
import AgentConfigurationSection from './components/AgentConfigurationSection';
import NetworkOrchestrationControls from './components/NetworkOrchestrationControls';
import RealTimeMonitoring from './components/RealTimeMonitoring';
import ExpertiseMatrix from './components/ExpertiseMatrix';
import CollaborationPatterns from './components/CollaborationPatterns';

const AISpecialistNetworkManagement = () => {
  const navigate = useNavigate();
  const [activeView, setActiveView] = useState('overview');
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [networkStatus, setNetworkStatus] = useState('healthy');
  const [isLoading, setIsLoading] = useState(true);

  // Enhanced data for the five-specialty AI agent network
  const enhancedHealthSpecialties = [
    {
      id: 'general-practitioner',
      name: 'General Practitioner',
      shortName: 'GP',
      description: 'Enhanced with family medicine focus, comprehensive primary care protocols, preventive medicine guidelines, and chronic disease management workflows',
      avatar: '👨‍⚕️',
      status: 'active',
      confidenceScore: 96,
      consultationsToday: 52,
      averageResponseTime: '1.8s',
      expertise: [
        'Family Medicine',
        'Primary Care Protocols', 
        'Preventive Medicine',
        'Chronic Disease Management',
        'Health Screening',
        'Vaccination Schedules',
        'Patient Triage',
        'Referral Management'
      ],
      performance: {
        accuracy: 97,
        patientSatisfaction: 4.8,
        consultationVolume: 1456,
        responseTime: 1.8,
        successfulReferrals: 94,
        chronicDiseaseManagement: 92
      },
      currentLoad: 72,
      queueLength: 4,
      enhancedCapabilities: {
        familyMedicineFocus: true,
        preventiveCareProtocols: true,
        chronicDiseaseWorkflows: true,
        multiGenerationCare: true
      },
      collaborationPartners: ['dietician', 'mental-health', 'paediatrician', 'dermatologist'],
      emergencyEscalation: true,
      aiModelVersion: 'v3.2.1',
      lastUpdated: '2024-12-17T10:30:00Z'
    },
    {
      id: 'dietician',
      name: 'Dietician',
      shortName: 'Diet',
      description: 'Upgraded with nutritional counseling, meal planning algorithms, nutritional deficiency detection, and cultural dietary adaptation',
      avatar: '🥗',
      status: 'active',
      confidenceScore: 94,
      consultationsToday: 38,
      averageResponseTime: '1.5s',
      expertise: [
        'Nutritional Counseling',
        'Meal Planning Algorithms',
        'Cultural Dietary Adaptation',
        'Nutritional Deficiency Detection',
        'Weight Management',
        'Diabetes Nutrition',
        'Sports Nutrition',
        'Eating Disorder Support'
      ],
      performance: {
        accuracy: 95,
        patientSatisfaction: 4.9,
        consultationVolume: 1124,
        responseTime: 1.5,
        mealPlanCompliance: 88,
        nutritionalGoalsAchieved: 91
      },
      currentLoad: 58,
      queueLength: 2,
      enhancedCapabilities: {
        mealPlanningAlgorithms: true,
        culturalAdaptation: true,
        deficiencyDetection: true,
        macroNutrientOptimization: true
      },
      collaborationPartners: ['general-practitioner', 'mental-health', 'paediatrician'],
      emergencyEscalation: false,
      aiModelVersion: 'v3.1.8',
      lastUpdated: '2024-12-17T09:45:00Z'
    },
    {
      id: 'clinical-psychologist',
      name: 'Clinical Psychologist',
      shortName: 'PSY',
      description: 'Newly integrated mental health specialist with cognitive behavioral therapy protocols, crisis intervention capabilities, and mental health screening tools',
      avatar: '🧠',
      status: 'active',
      confidenceScore: 93,
      consultationsToday: 71,
      averageResponseTime: '2.4s',
      expertise: [
        'Cognitive Behavioral Therapy',
        'Crisis Intervention',
        'Mental Health Screening',
        'Anxiety Management',
        'Depression Support',
        'Trauma-Informed Care',
        'Mindfulness Techniques',
        'Stress Reduction Protocols'
      ],
      performance: {
        accuracy: 94,
        patientSatisfaction: 4.9,
        consultationVolume: 1789,
        responseTime: 2.4,
        crisisInterventionSuccess: 97,
        therapyProtocolAdherence: 89
      },
      currentLoad: 85,
      queueLength: 8,
      enhancedCapabilities: {
        cbtProtocols: true,
        crisisIntervention: true,
        mentalHealthScreening: true,
        traumaInformedApproach: true
      },
      collaborationPartners: ['general-practitioner', 'dietician', 'paediatrician'],
      emergencyEscalation: true,
      aiModelVersion: 'v3.3.0',
      lastUpdated: '2024-12-17T11:15:00Z'
    },
    {
      id: 'paediatrician',
      name: 'Paediatrician',
      shortName: 'PED',
      description: 'Enhanced with child development tracking, vaccination scheduling, developmental milestone monitoring, and parent communication protocols',
      avatar: '👶',
      status: 'active',
      confidenceScore: 95,
      consultationsToday: 34,
      averageResponseTime: '2.1s',
      expertise: [
        'Child Development Tracking',
        'Vaccination Scheduling',
        'Developmental Milestones',
        'Parent Communication',
        'Childhood Illnesses',
        'Growth Monitoring',
        'Behavioral Assessment',
        'Adolescent Health'
      ],
      performance: {
        accuracy: 96,
        patientSatisfaction: 4.8,
        consultationVolume: 823,
        responseTime: 2.1,
        vaccinationCompliance: 94,
        developmentalAccuracy: 93
      },
      currentLoad: 41,
      queueLength: 3,
      enhancedCapabilities: {
        developmentTracking: true,
        vaccinationScheduling: true,
        milestoneMonitoring: true,
        parentCommunication: true
      },
      collaborationPartners: ['general-practitioner', 'clinical-psychologist', 'dietician'],
      emergencyEscalation: true,
      aiModelVersion: 'v3.2.4',
      lastUpdated: '2024-12-17T10:00:00Z'
    },
    {
      id: 'dermatologist',
      name: 'Dermatologist',
      shortName: 'DERM',
      description: 'Upgraded with visual diagnosis capabilities, image analysis integration, skin condition databases, and treatment progression monitoring',
      avatar: '🔬',
      status: 'active',
      confidenceScore: 91,
      consultationsToday: 46,
      averageResponseTime: '1.9s',
      expertise: [
        'Visual Diagnosis',
        'Image Analysis Integration',
        'Skin Condition Database',
        'Treatment Progression',
        'Acne Management',
        'Skin Cancer Screening',
        'Cosmetic Dermatology',
        'Dermatopathology'
      ],
      performance: {
        accuracy: 92,
        patientSatisfaction: 4.7,
        consultationVolume: 1067,
        responseTime: 1.9,
        imageAnalysisAccuracy: 89,
        treatmentSuccessRate: 87
      },
      currentLoad: 63,
      queueLength: 5,
      enhancedCapabilities: {
        visualDiagnosis: true,
        imageAnalysis: true,
        skinConditionDatabase: true,
        progressionMonitoring: true
      },
      collaborationPartners: ['general-practitioner', 'clinical-psychologist'],
      emergencyEscalation: false,
      aiModelVersion: 'v3.1.9',
      lastUpdated: '2024-12-17T09:30:00Z'
    }
  ];

  const enhancedNetworkMetrics = {
    totalActiveAgents: 5,
    totalConsultationsToday: 241,
    averageNetworkResponseTime: '1.9s',
    networkUptime: '99.8%',
    emergencyOverrideActive: false,
    loadBalancingStatus: 'optimal',
    interAgentCollaborations: 47,
    escalationRate: '3.2%',
    patientSatisfactionAverage: 4.8,
    networkEfficiencyScore: 94
  };

  useEffect(() => {
    const loadNetworkData = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1200));
      setIsLoading(false);
    };

    loadNetworkData();
  }, []);

  const handleAgentSelect = (agentId) => {
    const agent = enhancedHealthSpecialties.find(a => a.id === agentId);
    setSelectedAgent(agent);
    setActiveView('agent-config');
  };

  const handleEmergencyOverride = () => {
    console.log('Emergency override activated for enhanced network');
    setNetworkStatus('emergency');
  };

  const handleNetworkFailover = () => {
    console.log('Enhanced network failover initiated');
    setNetworkStatus('failover');
  };

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mb-6 mx-auto animate-pulse">
              <Icon name="Network" size={32} color="var(--primary-600)" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">Initializing Enhanced AI Network</h3>
            <p className="text-text-secondary">Loading specialist configurations and performance metrics...</p>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'agent-config':
        return (
          <AgentConfigurationSection
            agent={selectedAgent}
            onBack={() => setActiveView('overview')}
            onSave={(updatedAgent) => {
              console.log('Enhanced agent configuration saved:', updatedAgent);
              setActiveView('overview');
            }}
          />
        );

      case 'expertise-matrix':
        return (
          <ExpertiseMatrix
            agents={enhancedHealthSpecialties}
            onAgentSelect={handleAgentSelect}
          />
        );

      case 'collaboration-patterns':
        return (
          <CollaborationPatterns
            agents={enhancedHealthSpecialties}
            collaborationData={enhancedNetworkMetrics}
          />
        );

      case 'network-controls':
        return (
          <NetworkOrchestrationControls
            networkMetrics={enhancedNetworkMetrics}
            agents={enhancedHealthSpecialties}
            onEmergencyOverride={handleEmergencyOverride}
            onNetworkFailover={handleNetworkFailover}
          />
        );

      case 'monitoring':
        return (
          <RealTimeMonitoring
            agents={enhancedHealthSpecialties}
            networkMetrics={enhancedNetworkMetrics}
          />
        );

      default:
        return (
          <div className="space-y-8">
            {/* Enhanced Network Overview */}
            <div className="bg-gradient-to-r from-primary-50 to-accent-50 rounded-xl p-6 border border-primary-200">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-text-primary font-heading">
                    Enhanced AI Specialist Network Status
                  </h2>
                  <p className="text-text-secondary mt-1">
                    Comprehensive oversight of five-specialty agent network with advanced capabilities
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2 px-3 py-2 bg-white rounded-lg border border-primary-200">
                    <div className="w-2 h-2 rounded-full bg-success-500 animate-pulse"></div>
                    <span className="text-sm font-medium text-success-700">Network Healthy</span>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary-600">{enhancedNetworkMetrics.networkEfficiencyScore}%</div>
                    <div className="text-xs text-text-secondary">Efficiency Score</div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg p-4 border border-primary-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon name="Users" size={16} className="text-primary-500" />
                    <span className="text-sm font-medium text-text-primary">Active Specialists</span>
                  </div>
                  <div className="text-2xl font-bold text-text-primary">{enhancedNetworkMetrics.totalActiveAgents}</div>
                </div>
                
                <div className="bg-white rounded-lg p-4 border border-primary-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon name="MessageCircle" size={16} className="text-success-500" />
                    <span className="text-sm font-medium text-text-primary">Consultations Today</span>
                  </div>
                  <div className="text-2xl font-bold text-text-primary">{enhancedNetworkMetrics.totalConsultationsToday}</div>
                </div>
                
                <div className="bg-white rounded-lg p-4 border border-primary-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon name="Zap" size={16} className="text-warning-500" />
                    <span className="text-sm font-medium text-text-primary">Avg Response</span>
                  </div>
                  <div className="text-2xl font-bold text-text-primary">{enhancedNetworkMetrics.averageNetworkResponseTime}</div>
                </div>
                
                <div className="bg-white rounded-lg p-4 border border-primary-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon name="Heart" size={16} className="text-error-500" />
                    <span className="text-sm font-medium text-text-primary">Satisfaction</span>
                  </div>
                  <div className="text-2xl font-bold text-text-primary">{enhancedNetworkMetrics.patientSatisfactionAverage}</div>
                </div>
              </div>
            </div>

            {/* Network Topology Visualization */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Enhanced Network Topology & Agent Relationships
              </h2>
              <NetworkTopologyVisualization
                agents={enhancedHealthSpecialties}
                networkStatus={networkStatus}
                showCollaborationLinks={true}
              />
            </div>

            {/* Enhanced Specialty Agent Panels */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-text-primary font-heading">
                  Enhanced Health Specialty Agents
                </h2>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveView('expertise-matrix')}
                    iconName="Grid"
                  >
                    Expertise Matrix
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setActiveView('collaboration-patterns')}
                    iconName="Share2"
                  >
                    Collaboration Patterns
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {enhancedHealthSpecialties.map((agent) => (
                  <SpecialtyAgentPanel
                    key={agent.id}
                    agent={agent}
                    onConfigure={() => handleAgentSelect(agent.id)}
                    enhanced={true}
                  />
                ))}
              </div>
            </div>

            {/* Advanced Network Operations */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                Advanced Network Operations
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { 
                    id: 'network-controls', 
                    label: 'Dynamic Routing', 
                    icon: 'Route', 
                    description: 'Load balancing & smart routing',
                    color: 'text-primary-500',
                    bg: 'bg-primary-100',
                    badge: 'Advanced'
                  },
                  { 
                    id: 'monitoring', 
                    label: 'Real-time Analytics', 
                    icon: 'BarChart3', 
                    description: 'Live performance metrics',
                    color: 'text-success-500',
                    bg: 'bg-success-100',
                    badge: 'Live'
                  },
                  { 
                    id: 'collaboration-patterns', 
                    label: 'Multi-Agent Collaboration', 
                    icon: 'Users', 
                    description: 'Inter-specialist coordination',
                    color: 'text-accent-500',
                    bg: 'bg-accent-100',
                    badge: 'Enhanced'
                  },
                  { 
                    id: 'emergency', 
                    label: 'Emergency Protocols', 
                    icon: 'AlertTriangle', 
                    description: 'Crisis intervention & escalation',
                    color: 'text-error-500',
                    bg: 'bg-error-100',
                    badge: 'Critical'
                  }
                ].map((action) => (
                  <button
                    key={action.id}
                    onClick={() => {
                      if (action.id === 'emergency') {
                        handleEmergencyOverride();
                      } else {
                        setActiveView(action.id);
                      }
                    }}
                    className="bg-surface border border-border rounded-lg p-4 hover:shadow-medium transition-all text-left relative overflow-hidden group"
                  >
                    <div className="absolute top-2 right-2">
                      <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full font-medium">
                        {action.badge}
                      </span>
                    </div>
                    <div className={`p-3 rounded-lg ${action.bg} w-fit mb-3 group-hover:scale-110 transition-transform`}>
                      <Icon name={action.icon} size={24} className={action.color} />
                    </div>
                    <h3 className="font-medium text-text-primary mb-1">{action.label}</h3>
                    <p className="text-sm text-text-secondary">{action.description}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Quick Stats Summary */}
            <div className="bg-surface border border-border rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text-primary mb-4">Network Performance Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-success-500">{enhancedNetworkMetrics.networkUptime}</div>
                  <div className="text-sm text-text-secondary">Uptime</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-500">{enhancedNetworkMetrics.interAgentCollaborations}</div>
                  <div className="text-sm text-text-secondary">Collaborations</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-warning-500">{enhancedNetworkMetrics.escalationRate}</div>
                  <div className="text-sm text-text-secondary">Escalation Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-accent-500">{enhancedNetworkMetrics.loadBalancingStatus}</div>
                  <div className="text-sm text-text-secondary">Load Balance</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-error-500">{enhancedNetworkMetrics.networkEfficiencyScore}%</div>
                  <div className="text-sm text-text-secondary">Efficiency</div>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="mb-4 lg:mb-0">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
                <Icon name="Network" size={24} className="text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-text-primary font-heading">
                  AI Specialist Network Management
                </h1>
                <div className="flex items-center gap-2 mt-1">
                  <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full font-medium">
                    Enhanced
                  </span>
                  <span className="text-text-secondary text-sm">Five-Specialty Network</span>
                </div>
              </div>
            </div>
            <p className="text-text-secondary">
              Comprehensive oversight and configuration of enhanced AI agent network with detailed specialist profiles, 
              advanced capabilities, and intelligent collaboration patterns
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex items-center gap-2 px-4 py-2 bg-surface rounded-lg border border-border">
              <div className={`w-3 h-3 rounded-full ${
                networkStatus === 'healthy' ? 'bg-success-500 animate-pulse' :
                networkStatus === 'emergency' ? 'bg-error-500' :
                networkStatus === 'failover' ? 'bg-warning-500' : 'bg-text-secondary'
              }`}></div>
              <span className="text-sm font-medium text-text-primary">
                {networkStatus === 'healthy' ? 'Network Optimal' : 
                 networkStatus === 'emergency' ? 'Emergency Mode' :
                 networkStatus === 'failover' ? 'Failover Active' : 'Network Status'}
              </span>
            </div>
            
            <Button
              variant="outline"
              onClick={() => navigate('/enhanced-voice-consultation-interface')}
              iconName="MessageCircle"
            >
              Start Enhanced Consultation
            </Button>
            
            <Button
              variant="primary"
              onClick={() => setActiveView('monitoring')}
              iconName="Activity"
            >
              Live Analytics
            </Button>
          </div>
        </div>

        {/* Main Content */}
        {renderMainContent()}
      </main>
    </div>
  );
};

export default AISpecialistNetworkManagement;