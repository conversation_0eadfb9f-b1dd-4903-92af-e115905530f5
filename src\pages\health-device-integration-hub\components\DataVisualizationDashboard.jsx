import React, { useState } from 'react';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';
import Icon from '../../../components/AppIcon';

const DataVisualizationDashboard = ({ 
  healthData = {},
  onExportData = () => {},
  className = ''
}) => {
  const [selectedMetric, setSelectedMetric] = useState('heart-rate');
  const [timeRange, setTimeRange] = useState('7d');

  // Mock data - in real app, this would come from connected devices
  const mockData = {
    'heart-rate': [
      { date: '2024-01-10', value: 72, device: 'Apple Watch' },
      { date: '2024-01-11', value: 75, device: 'Apple Watch' },
      { date: '2024-01-12', value: 68, device: 'Apple Watch' },
      { date: '2024-01-13', value: 71, device: 'Apple Watch' },
      { date: '2024-01-14', value: 74, device: 'Apple Watch' },
      { date: '2024-01-15', value: 69, device: 'Apple Watch' },
      { date: '2024-01-16', value: 73, device: 'Apple Watch' }
    ],
    'blood-pressure': [
      { date: '2024-01-10', systolic: 120, diastolic: 80, device: 'Omron Monitor' },
      { date: '2024-01-12', systolic: 118, diastolic: 79, device: 'Omron Monitor' },
      { date: '2024-01-14', systolic: 122, diastolic: 82, device: 'Omron Monitor' },
      { date: '2024-01-16', systolic: 119, diastolic: 78, device: 'Omron Monitor' }
    ],
    'steps': [
      { date: '2024-01-10', value: 8500, device: 'Fitbit' },
      { date: '2024-01-11', value: 9200, device: 'Fitbit' },
      { date: '2024-01-12', value: 7800, device: 'Fitbit' },
      { date: '2024-01-13', value: 10100, device: 'Fitbit' },
      { date: '2024-01-14', value: 8900, device: 'Fitbit' },
      { date: '2024-01-15', value: 11200, device: 'Fitbit' },
      { date: '2024-01-16', value: 9600, device: 'Fitbit' }
    ],
    'weight': [
      { date: '2024-01-10', value: 75.2, device: 'Smart Scale' },
      { date: '2024-01-13', value: 75.0, device: 'Smart Scale' },
      { date: '2024-01-16', value: 74.8, device: 'Smart Scale' }
    ]
  };

  const metrics = [
    {
      id: 'heart-rate',
      name: 'Heart Rate',
      icon: 'Heart',
      unit: 'bpm',
      color: '#EF4444',
      type: 'line'
    },
    {
      id: 'blood-pressure',
      name: 'Blood Pressure',
      icon: 'Activity',
      unit: 'mmHg',
      color: '#F59E0B',
      type: 'dual-line'
    },
    {
      id: 'steps',
      name: 'Daily Steps',
      icon: 'Footprints',
      unit: 'steps',
      color: '#10B981',
      type: 'bar'
    },
    {
      id: 'weight',
      name: 'Weight',
      icon: 'Scale',
      unit: 'kg',
      color: '#3B82F6',
      type: 'line'
    }
  ];

  const timeRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ];

  const selectedMetricData = mockData[selectedMetric] || [];
  const currentMetric = metrics.find(m => m.id === selectedMetric);

  const getLatestValue = () => {
    if (selectedMetricData.length === 0) return 'N/A';
    const latest = selectedMetricData[selectedMetricData.length - 1];
    
    if (selectedMetric === 'blood-pressure') {
      return `${latest.systolic}/${latest.diastolic}`;
    }
    return latest.value;
  };

  const getValueChange = () => {
    if (selectedMetricData.length < 2) return null;
    const latest = selectedMetricData[selectedMetricData.length - 1];
    const previous = selectedMetricData[selectedMetricData.length - 2];
    
    let change;
    if (selectedMetric === 'blood-pressure') {
      change = latest.systolic - previous.systolic;
    } else {
      change = latest.value - previous.value;
    }
    
    return {
      value: Math.abs(change),
      direction: change >= 0 ? 'up' : 'down',
      isPositive: selectedMetric === 'steps' ? change >= 0 : change <= 0
    };
  };

  const renderChart = () => {
    if (selectedMetricData.length === 0) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Icon name="BarChart3" size={48} color="var(--color-text-muted)" className="mx-auto mb-4" />
            <p className="text-text-secondary">No data available for selected metric</p>
          </div>
        </div>
      );
    }

    if (currentMetric?.type === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={selectedMetricData}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
            <XAxis dataKey="date" stroke="var(--color-text-muted)" />
            <YAxis stroke="var(--color-text-muted)" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'var(--color-surface)', 
                border: '1px solid var(--color-border)',
                borderRadius: '8px'
              }} 
            />
            <Bar dataKey="value" fill={currentMetric.color} radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      );
    }

    if (currentMetric?.type === 'dual-line') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={selectedMetricData}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
            <XAxis dataKey="date" stroke="var(--color-text-muted)" />
            <YAxis stroke="var(--color-text-muted)" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'var(--color-surface)', 
                border: '1px solid var(--color-border)',
                borderRadius: '8px'
              }} 
            />
            <Line 
              type="monotone" 
              dataKey="systolic" 
              stroke={currentMetric.color} 
              strokeWidth={2}
              dot={{ fill: currentMetric.color, strokeWidth: 2 }}
              name="Systolic"
            />
            <Line 
              type="monotone" 
              dataKey="diastolic" 
              stroke="#8B5CF6" 
              strokeWidth={2}
              dot={{ fill: '#8B5CF6', strokeWidth: 2 }}
              name="Diastolic"
            />
          </LineChart>
        </ResponsiveContainer>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={selectedMetricData}>
          <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
          <XAxis dataKey="date" stroke="var(--color-text-muted)" />
          <YAxis stroke="var(--color-text-muted)" />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'var(--color-surface)', 
              border: '1px solid var(--color-border)',
              borderRadius: '8px'
            }} 
          />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke={currentMetric.color} 
            strokeWidth={2}
            dot={{ fill: currentMetric.color, strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  const valueChange = getValueChange();

  return (
    <div className={`bg-surface rounded-xl p-6 shadow-elevated ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
            <Icon name="BarChart3" size={20} color="var(--color-primary-500)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Health Data Visualization
            </h3>
            <p className="text-sm text-text-secondary">
              Aggregated data from connected devices
            </p>
          </div>
        </div>

        <button
          onClick={onExportData}
          className="flex items-center space-x-2 px-4 py-2 bg-secondary-50 text-text-secondary rounded-lg hover:bg-secondary-100 transition-fast"
        >
          <Icon name="Download" size={16} />
          <span className="text-sm font-medium">Export</span>
        </button>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <label className="block text-sm font-medium text-text-primary mb-2">Metric</label>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
            {metrics.map((metric) => (
              <button
                key={metric.id}
                onClick={() => setSelectedMetric(metric.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-fast ${
                  selectedMetric === metric.id
                    ? 'bg-primary-50 text-primary-600 border-2 border-primary-200' :'bg-background text-text-secondary hover:bg-secondary-50'
                }`}
              >
                <Icon name={metric.icon} size={16} />
                <span className="font-medium">{metric.name}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="sm:w-48">
          <label className="block text-sm font-medium text-text-primary mb-2">Time Range</label>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            {timeRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Current Value */}
      <div className="mb-6 p-4 bg-background rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-text-secondary">Current {currentMetric?.name}</p>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-text-primary">
                {getLatestValue()}
              </span>
              <span className="text-sm text-text-secondary">{currentMetric?.unit}</span>
            </div>
          </div>

          {valueChange && (
            <div className={`flex items-center space-x-1 ${
              valueChange.isPositive ? 'text-success-600' : 'text-error-600'
            }`}>
              <Icon 
                name={valueChange.direction === 'up' ? 'TrendingUp' : 'TrendingDown'} 
                size={16} 
              />
              <span className="text-sm font-medium">
                {valueChange.value} {currentMetric?.unit}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Chart */}
      <div className="mb-6">
        {renderChart()}
      </div>

      {/* Data Sources */}
      <div className="border-t border-border pt-4">
        <h4 className="text-sm font-medium text-text-primary mb-3">Data Sources</h4>
        <div className="flex flex-wrap gap-2">
          {Array.from(new Set(selectedMetricData.map(d => d.device))).map((device) => (
            <span 
              key={device}
              className="px-3 py-1 bg-secondary-50 text-text-secondary text-sm rounded-full"
            >
              {device}
            </span>
          ))}
        </div>
      </div>

      {/* Insights */}
      {selectedMetricData.length > 0 && (
        <div className="mt-4 p-4 bg-primary-50 rounded-lg">
          <div className="flex items-start space-x-3">
            <Icon name="Lightbulb" size={16} color="var(--color-primary-600)" />
            <div>
              <h4 className="text-sm font-medium text-primary-800 mb-1">AI Insights</h4>
              <p className="text-sm text-primary-700">
                Your {currentMetric?.name?.toLowerCase()} data shows a stable trend over the selected period. 
                Continue monitoring for any significant changes that might warrant discussion during your next consultation.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataVisualizationDashboard;