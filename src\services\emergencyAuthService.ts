/**
 * Emergency Authentication Service
 * Provides emergency bypass mechanisms for critical patient safety scenarios
 * Maintains HIPAA compliance while ensuring < 2 second response times
 */

import { EmergencyStopEvent, EmergencyProtocolResult } from '../types/audio';

interface EmergencySession {
  readonly sessionId: string;
  readonly userId: string;
  readonly emergencyToken: string;
  readonly createdAt: string;
  readonly expiresAt: string;
  readonly reason: string;
  readonly authorizedBy: string;
  readonly isActive: boolean;
}

interface EmergencyAuthContext {
  readonly emergencyOverride: boolean;
  readonly emergencyToken?: string;
  readonly reason?: string;
  readonly authorizedBy?: string;
  readonly sessionId?: string;
  readonly bypassAuthentication: boolean;
}

class EmergencyAuthService {
  private emergencySessions: Map<string, EmergencySession> = new Map();
  private preValidatedTokens: Map<string, string> = new Map();
  private emergencyTokenCache: Map<string, { token: string; expires: number }> = new Map();

  constructor() {
    // Pre-generate emergency tokens for instant access
    this.initializeEmergencyTokens();
    
    // Clean up expired sessions every 5 minutes
    setInterval(() => this.cleanupExpiredSessions(), 5 * 60 * 1000);
  }

  /**
   * Pre-generate emergency tokens for instant access
   * These tokens are cached and ready for immediate use
   */
  private async initializeEmergencyTokens(): Promise<void> {
    try {
      // Generate 10 pre-validated emergency tokens
      for (let i = 0; i < 10; i++) {
        const emergencyToken = this.generateEmergencyToken();
        const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
        
        this.emergencyTokenCache.set(`emergency_${i}`, {
          token: emergencyToken,
          expires
        });
      }
      
      console.log('✅ Emergency tokens pre-generated and cached');
    } catch (error) {
      console.error('❌ Failed to initialize emergency tokens:', error);
    }
  }

  /**
   * Generate cryptographically secure emergency token
   */
  private generateEmergencyToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Create emergency session with bypass authentication
   * Returns immediately without waiting for external auth services
   */
  async createEmergencySession(
    userId: string,
    sessionId: string,
    reason: string,
    authorizedBy: string = 'system'
  ): Promise<EmergencySession> {
    const startTime = performance.now();
    
    try {
      // Check if session already exists (for concurrent requests)
      const existingSession = this.emergencySessions.get(sessionId);
      if (existingSession && existingSession.isActive) {
        const responseTime = performance.now() - startTime;
        console.log(`⚡ Existing emergency session returned in ${responseTime.toFixed(2)}ms`);
        return existingSession;
      }

      // Get pre-cached emergency token for instant access
      const emergencyToken = this.getPreCachedEmergencyToken();

      const emergencySession: EmergencySession = {
        sessionId,
        userId,
        emergencyToken,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (2 * 60 * 60 * 1000)).toISOString(), // 2 hours
        reason,
        authorizedBy,
        isActive: true
      };

      // Store session for immediate access
      this.emergencySessions.set(sessionId, emergencySession);
      this.preValidatedTokens.set(emergencyToken, sessionId);

      const responseTime = performance.now() - startTime;
      console.log(`✅ Emergency session created in ${responseTime.toFixed(2)}ms`);

      // Log emergency session creation for HIPAA compliance (async, don't wait)
      this.logEmergencySessionCreation(emergencySession).catch(error => {
        console.error('Emergency session audit logging failed:', error);
      });

      return emergencySession;
    } catch (error) {
      const responseTime = performance.now() - startTime;
      console.error(`❌ Emergency session creation failed in ${responseTime.toFixed(2)}ms:`, error);
      throw error;
    }
  }

  /**
   * Get pre-cached emergency token for instant access
   */
  private getPreCachedEmergencyToken(): string {
    // Find first available non-expired token
    for (const [key, tokenData] of this.emergencyTokenCache.entries()) {
      if (tokenData.expires > Date.now()) {
        // Remove from cache and regenerate
        this.emergencyTokenCache.delete(key);
        this.regenerateEmergencyToken(key);
        return tokenData.token;
      }
    }

    // Fallback: generate new token immediately
    return this.generateEmergencyToken();
  }

  /**
   * Regenerate emergency token in background
   */
  private async regenerateEmergencyToken(key: string): Promise<void> {
    try {
      const newToken = this.generateEmergencyToken();
      const expires = Date.now() + (24 * 60 * 60 * 1000);
      
      this.emergencyTokenCache.set(key, {
        token: newToken,
        expires
      });
    } catch (error) {
      console.error('Failed to regenerate emergency token:', error);
    }
  }

  /**
   * Validate emergency authentication context
   * Returns immediately without external API calls
   */
  async validateEmergencyAuth(authContext: EmergencyAuthContext): Promise<boolean> {
    const startTime = performance.now();
    
    try {
      // If emergency override is enabled, bypass normal authentication
      if (authContext.emergencyOverride && authContext.bypassAuthentication) {
        const responseTime = performance.now() - startTime;
        console.log(`✅ Emergency auth bypass validated in ${responseTime.toFixed(2)}ms`);
        return true;
      }

      // Check if emergency token is valid
      if (authContext.emergencyToken) {
        const sessionId = this.preValidatedTokens.get(authContext.emergencyToken);
        if (sessionId) {
          const session = this.emergencySessions.get(sessionId);
          if (session && session.isActive && new Date(session.expiresAt) > new Date()) {
            const responseTime = performance.now() - startTime;
            console.log(`✅ Emergency token validated in ${responseTime.toFixed(2)}ms`);
            return true;
          }
        }
      }

      const responseTime = performance.now() - startTime;
      console.log(`❌ Emergency auth validation failed in ${responseTime.toFixed(2)}ms`);
      return false;
    } catch (error) {
      const responseTime = performance.now() - startTime;
      console.error(`❌ Emergency auth validation error in ${responseTime.toFixed(2)}ms:`, error);
      return false;
    }
  }

  /**
   * Get emergency session by session ID
   */
  getEmergencySession(sessionId: string): EmergencySession | null {
    return this.emergencySessions.get(sessionId) || null;
  }

  /**
   * Deactivate emergency session
   */
  async deactivateEmergencySession(sessionId: string): Promise<void> {
    const session = this.emergencySessions.get(sessionId);
    if (session) {
      const deactivatedSession = { ...session, isActive: false };
      this.emergencySessions.set(sessionId, deactivatedSession);
      
      // Remove from pre-validated tokens
      this.preValidatedTokens.delete(session.emergencyToken);
      
      // Log deactivation for HIPAA compliance
      await this.logEmergencySessionDeactivation(deactivatedSession);
    }
  }

  /**
   * Clean up expired emergency sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.emergencySessions.entries()) {
      if (new Date(session.expiresAt) <= now) {
        expiredSessions.push(sessionId);
        this.preValidatedTokens.delete(session.emergencyToken);
      }
    }

    expiredSessions.forEach(sessionId => {
      this.emergencySessions.delete(sessionId);
    });

    if (expiredSessions.length > 0) {
      console.log(`🧹 Cleaned up ${expiredSessions.length} expired emergency sessions`);
    }
  }

  /**
   * Log emergency session creation for HIPAA compliance
   */
  private async logEmergencySessionCreation(session: EmergencySession): Promise<void> {
    try {
      const { default: auditLogger } = await import('../utils/auditLogger');
      await auditLogger.logEmergencyAccess(
        session.userId,
        session.authorizedBy,
        `Emergency authentication session created: ${session.reason}`,
        {
          session_id: session.sessionId,
          emergency_token_id: session.emergencyToken.substring(0, 8) + '...',
          reason: session.reason,
          expires_at: session.expiresAt,
          created_at: session.createdAt
        }
      );
    } catch (error) {
      console.error('Failed to log emergency session creation:', error);
    }
  }

  /**
   * Log emergency session deactivation for HIPAA compliance
   */
  private async logEmergencySessionDeactivation(session: EmergencySession): Promise<void> {
    try {
      const { default: auditLogger } = await import('../utils/auditLogger');
      await auditLogger.logEmergencyAccess(
        session.userId,
        session.authorizedBy,
        `Emergency authentication session deactivated: ${session.reason}`,
        {
          session_id: session.sessionId,
          emergency_token_id: session.emergencyToken.substring(0, 8) + '...',
          reason: session.reason,
          deactivated_at: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Failed to log emergency session deactivation:', error);
    }
  }

  /**
   * Check if emergency authentication is available
   */
  isEmergencyAuthAvailable(): boolean {
    return this.emergencyTokenCache.size > 0;
  }

  /**
   * Get emergency authentication statistics
   */
  getEmergencyAuthStats(): {
    activeSessions: number;
    availableTokens: number;
    totalSessions: number;
  } {
    const activeSessions = Array.from(this.emergencySessions.values())
      .filter(session => session.isActive).length;
    
    const availableTokens = Array.from(this.emergencyTokenCache.values())
      .filter(token => token.expires > Date.now()).length;

    return {
      activeSessions,
      availableTokens,
      totalSessions: this.emergencySessions.size
    };
  }
}

// Export singleton instance
export const emergencyAuthService = new EmergencyAuthService();
export default emergencyAuthService;
