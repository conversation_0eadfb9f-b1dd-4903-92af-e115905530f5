import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SessionControlPanel = ({ 
  sessionStatus = 'active',
  collaborationPhase = 'assessment',
  onPauseCollaboration = () => {},
  onResumeCollaboration = () => {},
  onInjectQuestion = () => {},
  onRedirectTopic = () => {},
  onEndSession = () => {},
  userInterventionEnabled = true,
  className = ''
}) => {
  const [showQuestionInput, setShowQuestionInput] = useState(false);
  const [showTopicInput, setShowTopicInput] = useState(false);
  const [questionText, setQuestionText] = useState('');
  const [topicText, setTopicText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleInjectQuestion = async () => {
    if (!questionText.trim()) return;
    
    setIsProcessing(true);
    try {
      await onInjectQuestion(questionText);
      setQuestionText('');
      setShowQuestionInput(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRedirectTopic = async () => {
    if (!topicText.trim()) return;
    
    setIsProcessing(true);
    try {
      await onRedirectTopic(topicText);
      setTopicText('');
      setShowTopicInput(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const getSessionStatusConfig = () => {
    switch (sessionStatus) {
      case 'active':
        return {
          color: 'success',
          icon: 'Play',
          label: 'Active Session',
          description: 'Agents are collaborating'
        };
      case 'paused':
        return {
          color: 'warning',
          icon: 'Pause',
          label: 'Session Paused',
          description: 'Collaboration temporarily stopped'
        };
      case 'waiting':
        return {
          color: 'secondary',
          icon: 'Clock',
          label: 'Waiting',
          description: 'Preparing for collaboration'
        };
      default:
        return {
          color: 'secondary',
          icon: 'Circle',
          label: 'Unknown',
          description: 'Session status unclear'
        };
    }
  };

  const getPhaseProgress = () => {
    const phases = ['initializing', 'assessment', 'collaboration', 'synthesis', 'completed'];
    const currentIndex = phases.indexOf(collaborationPhase);
    return ((currentIndex + 1) / phases.length) * 100;
  };

  const status = getSessionStatusConfig();

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 ${className}`}>
      {/* Session Status Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            `bg-${status.color}-50`
          }`}>
            <Icon 
              name={status.icon}
              size={20}
              color={`var(--color-${status.color})`}
            />
          </div>
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              {status.label}
            </h3>
            <p className="text-sm text-text-secondary font-caption">
              {status.description}
            </p>
          </div>
        </div>

        {/* Emergency Stop */}
        <Button
          variant="danger"
          size="sm"
          onClick={onEndSession}
          iconName="Square"
          iconPosition="left"
          className="emergency-control"
        >
          End Session
        </Button>
      </div>

      {/* Phase Progress */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-text-secondary mb-2">
          <span>Collaboration Progress</span>
          <span className="capitalize">{collaborationPhase}</span>
        </div>
        <div className="w-full bg-secondary-100 rounded-full h-2">
          <div 
            className="bg-primary-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${getPhaseProgress()}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-text-muted mt-1">
          <span>Start</span>
          <span>Assessment</span>
          <span>Collaboration</span>
          <span>Synthesis</span>
          <span>Complete</span>
        </div>
      </div>

      {/* Primary Controls */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        {sessionStatus === 'active' ? (
          <Button
            variant="warning"
            onClick={onPauseCollaboration}
            iconName="Pause"
            iconPosition="left"
            fullWidth
          >
            Pause
          </Button>
        ) : (
          <Button
            variant="success"
            onClick={onResumeCollaboration}
            iconName="Play"
            iconPosition="left"
            fullWidth
          >
            Resume
          </Button>
        )}

        <Button
          variant="outline"
          onClick={() => setShowQuestionInput(!showQuestionInput)}
          iconName="MessageCircle"
          iconPosition="left"
          disabled={!userInterventionEnabled}
          fullWidth
        >
          Ask Question
        </Button>
      </div>

      {/* Question Injection Interface */}
      {showQuestionInput && (
        <div className="mb-6 p-4 bg-primary-50 rounded-lg border border-primary-200">
          <div className="flex items-center space-x-2 mb-3">
            <Icon name="MessageCircle" size={16} color="var(--color-primary)" />
            <span className="font-medium text-primary-600">Inject Question</span>
          </div>
          
          <textarea
            value={questionText}
            onChange={(e) => setQuestionText(e.target.value)}
            placeholder="Type your question for the agents..."
            className="w-full p-3 border border-border rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            rows="3"
          />
          
          <div className="flex space-x-2 mt-3">
            <Button
              variant="primary"
              size="sm"
              onClick={handleInjectQuestion}
              loading={isProcessing}
              disabled={!questionText.trim()}
              iconName="Send"
              iconPosition="right"
            >
              Send Question
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowQuestionInput(false);
                setQuestionText('');
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Advanced Controls */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-text-primary">
            Advanced Controls
          </span>
          <button
            onClick={() => setShowTopicInput(!showTopicInput)}
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            {showTopicInput ? 'Hide' : 'Show'}
          </button>
        </div>

        {showTopicInput && (
          <div className="p-4 bg-warning-50 rounded-lg border border-warning-200">
            <div className="flex items-center space-x-2 mb-3">
              <Icon name="Navigation" size={16} color="var(--color-warning)" />
              <span className="font-medium text-warning-600">Redirect Discussion</span>
            </div>
            
            <input
              type="text"
              value={topicText}
              onChange={(e) => setTopicText(e.target.value)}
              placeholder="Enter topic to focus on..."
              className="w-full p-3 border border-border rounded-lg focus:ring-2 focus:ring-warning-500 focus:border-warning-500"
            />
            
            <div className="flex space-x-2 mt-3">
              <Button
                variant="warning"
                size="sm"
                onClick={handleRedirectTopic}
                loading={isProcessing}
                disabled={!topicText.trim()}
                iconName="ArrowRight"
                iconPosition="right"
              >
                Redirect
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowTopicInput(false);
                  setTopicText('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Quick Topic Suggestions */}
        <div className="flex flex-wrap gap-2">
          {[
            'Side effects',
            'Alternative treatments',
            'Lifestyle changes',
            'Follow-up care',
            'Emergency signs'
          ].map((topic) => (
            <button
              key={topic}
              onClick={() => {
                setTopicText(topic);
                setShowTopicInput(true);
              }}
              className="px-3 py-1.5 bg-secondary-100 hover:bg-secondary-200 text-text-secondary hover:text-text-primary rounded-lg text-xs font-medium transition-fast"
            >
              {topic}
            </button>
          ))}
        </div>
      </div>

      {/* Session Info */}
      <div className="mt-6 pt-4 border-t border-border">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {Math.floor(Math.random() * 15 + 5)}m
            </div>
            <div className="text-xs text-text-secondary">Duration</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-text-primary font-data">
              {Math.floor(Math.random() * 20 + 10)}
            </div>
            <div className="text-xs text-text-secondary">Exchanges</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionControlPanel;