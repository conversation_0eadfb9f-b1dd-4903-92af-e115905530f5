import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const EvidenceBasedGuidelinesPanel = ({ searchQuery }) => {
  const [guidelines, setGuidelines] = useState([]);
  const [selectedGuideline, setSelectedGuideline] = useState(null);
  const [filterSource, setFilterSource] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  const sources = [
    { id: 'all', label: 'All Sources', icon: 'BookOpen' },
    { id: 'who', label: 'WHO', icon: 'Globe' },
    { id: 'cdc', label: 'CDC', icon: 'Shield' },
    { id: 'nice', label: 'NICE', icon: 'Award' },
    { id: 'cochrane', label: 'Cochrane', icon: 'Search' },
    { id: 'local', label: 'Local Guidelines', icon: 'MapPin' }
  ];

  const mockGuidelines = [
    {
      id: 'who_malaria_2023',
      title: 'WHO Guidelines for Malaria Treatment',
      source: 'who',
      version: '2023.1',
      lastUpdated: '2023-12-15T00:00:00Z',
      evidenceLevel: 'High',
      conditions: ['Malaria', 'Severe Malaria'],
      regions: ['Sub-Saharan Africa'],
      keyRecommendations: [
        'Artemisinin-based combination therapy (ACT) for uncomplicated P. falciparum malaria',
        'IV Artesunate as first-line treatment for severe malaria',
        'Seasonal malaria chemoprevention in high-transmission areas',
        'Intermittent preventive treatment in pregnancy (IPTp)'
      ],
      contraindications: [
        'Known hypersensitivity to artemisinin derivatives',
        'Severe cardiac arrhythmias',
        'First trimester pregnancy (for some ACTs)'
      ],
      monitoringRequirements: [
        'Parasitemia clearance monitoring',
        'Clinical response assessment',
        'Adverse event monitoring',
        'Drug resistance surveillance'
      ],
      qualityOfEvidence: 'Grade A - High quality evidence',
      implementationNotes: 'Adapted for resource-limited settings',
      costEffectiveness: 'Cost-effective in endemic areas',
      culturalAdaptations: 'Community engagement programs recommended',
      status: 'active'
    },
    {
      id: 'cdc_hypertension_2023',
      title: 'CDC Hypertension Management Guidelines',
      source: 'cdc',
      version: '2023.2',
      lastUpdated: '2023-11-20T00:00:00Z',
      evidenceLevel: 'High',
      conditions: ['Essential Hypertension', 'Secondary Hypertension'],
      regions: ['Global', 'Africa'],
      keyRecommendations: [
        'BP target <140/90 mmHg for most adults',
        'Lifestyle modifications as first-line therapy',
        'ACE inhibitors or ARBs as preferred first-line medications',
        'Combination therapy for BP >20/10 mmHg above target'
      ],
      contraindications: [
        'Bilateral renal artery stenosis (ACE inhibitors/ARBs)',
        'Pregnancy (ACE inhibitors/ARBs)',
        'Hyperkalemia (potassium-sparing diuretics)'
      ],
      monitoringRequirements: [
        'Home blood pressure monitoring',
        'Annual cardiovascular risk assessment',
        'Kidney function monitoring',
        'Electrolyte monitoring'
      ],
      qualityOfEvidence: 'Grade A - Strong recommendation',
      implementationNotes: 'Consider cost and availability in low-resource settings',
      costEffectiveness: 'Highly cost-effective for cardiovascular prevention',
      culturalAdaptations: 'Dietary modifications adapted to local foods',
      status: 'active'
    },
    {
      id: 'nice_diabetes_2023',
      title: 'NICE Type 2 Diabetes Management',
      source: 'nice',
      version: '2023.1',
      lastUpdated: '2023-10-08T00:00:00Z',
      evidenceLevel: 'High',
      conditions: ['Type 2 Diabetes', 'Prediabetes'],
      regions: ['UK', 'Adapted for Africa'],
      keyRecommendations: [
        'HbA1c target <53 mmol/mol (7%) for most adults',
        'Metformin as first-line therapy',
        'Lifestyle interventions with structured education',
        'Cardiovascular risk assessment and management'
      ],
      contraindications: [
        'Severe renal impairment (Metformin)',
        'Lactic acidosis risk factors',
        'Severe heart failure (certain medications)'
      ],
      monitoringRequirements: [
        'HbA1c every 3-6 months',
        'Annual diabetic eye screening',
        'Foot examination annually',
        'Kidney function monitoring'
      ],
      qualityOfEvidence: 'Grade A - High quality evidence',
      implementationNotes: 'Adapted recommendations for low-resource settings',
      costEffectiveness: 'Cost-effective with generic medications',
      culturalAdaptations: 'Dietary advice adapted to local foods and practices',
      status: 'active'
    },
    {
      id: 'local_tb_ghana_2023',
      title: 'Ghana National TB Treatment Guidelines',
      source: 'local',
      version: '2023.1',
      lastUpdated: '2023-09-15T00:00:00Z',
      evidenceLevel: 'Moderate',
      conditions: ['Tuberculosis', 'MDR-TB'],
      regions: ['Ghana'],
      keyRecommendations: [
        'GeneXpert as initial diagnostic test',
        'First-line regimen: 2HRZE/4HR',
        'DOT supervision for all patients',
        'Contact tracing and screening'
      ],
      contraindications: [
        'Severe liver disease (Isoniazid, Rifampin)',
        'Optic neuritis (Ethambutol)',
        'Pregnancy (Streptomycin)'
      ],
      monitoringRequirements: [
        'Monthly sputum monitoring',
        'Liver function tests',
        'Visual acuity monitoring',
        'Treatment adherence monitoring'
      ],
      qualityOfEvidence: 'Grade B - Moderate quality evidence',
      implementationNotes: 'Free treatment program available',
      costEffectiveness: 'Fully subsidized treatment',
      culturalAdaptations: 'Community health worker support, stigma reduction',
      status: 'active'
    }
  ];

  useEffect(() => {
    const loadGuidelines = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 900));
      setGuidelines(mockGuidelines);
      setIsLoading(false);
    };

    loadGuidelines();
  }, []);

  const filteredGuidelines = guidelines.filter(guideline => {
    const matchesSource = filterSource === 'all' || guideline.source === filterSource;
    const matchesSearch = !searchQuery || 
      guideline.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      guideline.conditions?.some(condition => condition.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesSource && matchesSearch;
  });

  const getEvidenceLevelColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'high': return 'bg-success-100 text-success-700';
      case 'moderate': return 'bg-warning-100 text-warning-700';
      case 'low': return 'bg-error-100 text-error-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getSourceIcon = (source) => {
    const sourceData = sources.find(s => s.id === source);
    return sourceData?.icon || 'BookOpen';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading evidence-based guidelines...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Source Filter */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <div className="flex flex-wrap gap-2">
          {sources.map((source) => (
            <button
              key={source.id}
              onClick={() => setFilterSource(source.id)}
              className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                filterSource === source.id
                  ? 'bg-primary-500 text-white' :'bg-primary-50 text-primary-700 hover:bg-primary-100'
              }`}
            >
              <Icon name={source.icon} size={16} className="mr-2" />
              {source.label}
            </button>
          ))}
        </div>
      </div>

      {/* Guidelines Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="BookOpen" size={24} className="text-primary-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">{filteredGuidelines.length}</p>
              <p className="text-sm text-text-secondary">Active Guidelines</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Award" size={24} className="text-success-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredGuidelines.filter(g => g.evidenceLevel === 'High').length}
              </p>
              <p className="text-sm text-text-secondary">High Evidence</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Globe" size={24} className="text-warning-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredGuidelines.filter(g => g.source === 'who').length}
              </p>
              <p className="text-sm text-text-secondary">WHO Guidelines</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="MapPin" size={24} className="text-error-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredGuidelines.filter(g => g.regions?.some(r => r.includes('Africa'))).length}
              </p>
              <p className="text-sm text-text-secondary">Africa-Specific</p>
            </div>
          </div>
        </div>
      </div>

      {/* Guidelines List */}
      <div className="space-y-4">
        {filteredGuidelines.map((guideline) => (
          <div
            key={guideline.id}
            className={`bg-surface rounded-lg border transition-all ${
              selectedGuideline?.id === guideline.id
                ? 'border-primary-500 shadow-medium'
                : 'border-border hover:border-primary-300 hover:shadow-small'
            }`}
          >
            <div 
              className="p-6 cursor-pointer"
              onClick={() => setSelectedGuideline(selectedGuideline?.id === guideline.id ? null : guideline)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <Icon name={getSourceIcon(guideline.source)} size={20} className="text-primary-500 mr-2" />
                    <h3 className="text-lg font-semibold text-text-primary">
                      {guideline.title}
                    </h3>
                  </div>
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getEvidenceLevelColor(guideline.evidenceLevel)}`}>
                      {guideline.evidenceLevel} Evidence
                    </span>
                    <span className="text-sm text-text-secondary">
                      Version {guideline.version}
                    </span>
                    <span className="text-sm text-text-secondary">
                      Updated: {new Date(guideline.lastUpdated).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-text-secondary">Conditions:</span>
                    {guideline.conditions?.slice(0, 3).map((condition, index) => (
                      <span
                        key={index}
                        className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                      >
                        {condition}
                      </span>
                    ))}
                  </div>
                </div>
                <Icon 
                  name={selectedGuideline?.id === guideline.id ? "ChevronUp" : "ChevronDown"} 
                  size={20} 
                  className="text-text-secondary" 
                />
              </div>

              {/* Expanded Details */}
              {selectedGuideline?.id === guideline.id && (
                <div className="mt-6 pt-6 border-t border-border space-y-6">
                  {/* Key Recommendations */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Key Recommendations</h4>
                    <div className="space-y-2">
                      {guideline.keyRecommendations?.map((recommendation, index) => (
                        <div key={index} className="flex items-start">
                          <Icon name="CheckCircle" size={16} className="text-success-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-text-secondary">{recommendation}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Contraindications */}
                  <div>
                    <div className="flex items-center mb-3">
                      <Icon name="AlertTriangle" size={20} className="text-error-500 mr-2" />
                      <h4 className="text-sm font-medium text-text-primary">Contraindications</h4>
                    </div>
                    <div className="space-y-2">
                      {guideline.contraindications?.map((contraindication, index) => (
                        <div key={index} className="flex items-start">
                          <Icon name="X" size={16} className="text-error-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-text-secondary">{contraindication}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Monitoring Requirements */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Monitoring Requirements</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {guideline.monitoringRequirements?.map((requirement, index) => (
                        <div key={index} className="flex items-center">
                          <Icon name="Eye" size={16} className="text-primary-500 mr-2" />
                          <span className="text-sm text-text-secondary">{requirement}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Implementation & Cultural Adaptations */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Implementation Notes</h4>
                      <p className="text-sm text-text-secondary">{guideline.implementationNotes}</p>
                      <div className="mt-2">
                        <span className="text-xs text-text-secondary">Cost-effectiveness: </span>
                        <span className="text-xs text-success-600 font-medium">{guideline.costEffectiveness}</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Cultural Adaptations</h4>
                      <p className="text-sm text-text-secondary">{guideline.culturalAdaptations}</p>
                      <div className="mt-2">
                        <span className="text-xs text-text-secondary">Quality of Evidence: </span>
                        <span className="text-xs text-primary-600 font-medium">{guideline.qualityOfEvidence}</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" iconName="Edit">
                      Update Guideline
                    </Button>
                    <Button variant="outline" size="sm" iconName="Download">
                      Download PDF
                    </Button>
                    <Button variant="outline" size="sm" iconName="Share">
                      Share with Team
                    </Button>
                    <Button variant="outline" size="sm" iconName="Clock">
                      Version History
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredGuidelines.length === 0 && (
        <div className="text-center py-12">
          <Icon name="BookOpen" size={48} className="text-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">No guidelines found</h3>
          <p className="text-text-secondary">
            Try selecting a different source or adjusting your search
          </p>
        </div>
      )}
    </div>
  );
};

export default EvidenceBasedGuidelinesPanel;