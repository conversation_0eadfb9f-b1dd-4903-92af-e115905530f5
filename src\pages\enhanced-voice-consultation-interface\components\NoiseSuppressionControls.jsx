import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const NoiseSuppressionControls = ({
  noiseReduction = true,
  echoCancellation = true,
  autoGainControl = true,
  audioQuality = 'high',
  onNoiseReductionChange = () => {},
  onEchoCancellationChange = () => {},
  onAutoGainControlChange = () => {},
  onAudioQualityChange = () => {},
  className = ''
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [customSettings, setCustomSettings] = useState({
    noiseSuppression: 0.8,
    echoCancellationLevel: 0.9,
    gainLevel: 0.7,
    frequencyFilter: 'voice'
  });

  const audioQualityOptions = [
    { value: 'low', label: 'Low (32kbps)', description: 'Best for poor connections' },
    { value: 'medium', label: 'Medium (64kbps)', description: 'Balanced quality and bandwidth' },
    { value: 'high', label: 'High (128kbps)', description: 'Best quality for good connections' },
    { value: 'ultra', label: 'Ultra (256kbps)', description: 'Highest quality for excellent connections' }
  ];

  const frequencyFilterOptions = [
    { value: 'voice', label: 'Voice Optimized', description: 'Focus on human speech frequencies' },
    { value: 'full', label: 'Full Spectrum', description: 'Capture all audio frequencies' },
    { value: 'medical', label: 'Medical Audio', description: 'Optimized for medical consultations' }
  ];

  const handleCustomSettingChange = (setting, value) => {
    setCustomSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const getQualityIcon = () => {
    switch (audioQuality) {
      case 'low': return 'Volume';
      case 'medium': return 'Volume1';
      case 'high': return 'Volume2';
      case 'ultra': return 'VolumeX';
      default: return 'Volume2';
    }
  };

  const getQualityColor = () => {
    switch (audioQuality) {
      case 'low': return 'text-error-600';
      case 'medium': return 'text-warning-600';
      case 'high': return 'text-success-600';
      case 'ultra': return 'text-primary-600';
      default: return 'text-text-secondary';
    }
  };

  return (
    <div className={`bg-surface border border-border rounded-xl shadow-minimal ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <Icon name="Settings" size={20} color="var(--color-text-primary)" />
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              Audio Processing
            </h3>
            <p className="text-sm text-text-secondary">
              Advanced noise suppression and echo cancellation
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Icon 
            name={getQualityIcon()} 
            size={16} 
            className={getQualityColor()} 
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            iconName={showAdvanced ? "ChevronUp" : "ChevronDown"}
          >
            Advanced
          </Button>
        </div>
      </div>

      {/* Main Controls */}
      <div className="p-4 space-y-4">
        {/* Audio Quality Selection */}
        <div>
          <label className="text-sm font-medium text-text-primary mb-3 block">
            Audio Quality
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {audioQualityOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => onAudioQualityChange(option.value)}
                className={`p-3 rounded-lg border text-left transition-fast ${
                  audioQuality === option.value
                    ? 'bg-primary-50 border-primary-200 text-primary-700' :'bg-secondary-50 border-secondary-200 hover:bg-secondary-100'
                }`}
              >
                <div className="text-sm font-medium">{option.label}</div>
                <div className="text-xs text-text-secondary mt-1">
                  {option.description}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Main Audio Features */}
        <div className="space-y-3">
          {/* Noise Reduction */}
          <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Icon 
                name={noiseReduction ? "VolumeOff" : "Volume"} 
                size={18} 
                className={noiseReduction ? "text-success-600" : "text-secondary-600"} 
              />
              <div>
                <div className="text-sm font-medium text-text-primary">
                  Background Noise Suppression
                </div>
                <div className="text-xs text-text-secondary">
                  {noiseReduction ? 'Active - Reducing ambient noise' : 'Disabled - Full audio capture'}
                </div>
              </div>
            </div>
            
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={noiseReduction}
                onChange={(e) => onNoiseReductionChange(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-success-500"></div>
            </label>
          </div>

          {/* Echo Cancellation */}
          <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Icon 
                name={echoCancellation ? "Headphones" : "Speaker"} 
                size={18} 
                className={echoCancellation ? "text-success-600" : "text-secondary-600"} 
              />
              <div>
                <div className="text-sm font-medium text-text-primary">
                  Echo Cancellation
                </div>
                <div className="text-xs text-text-secondary">
                  {echoCancellation ? 'Active - Preventing audio feedback' : 'Disabled - May cause echo'}
                </div>
              </div>
            </div>
            
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={echoCancellation}
                onChange={(e) => onEchoCancellationChange(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-success-500"></div>
            </label>
          </div>

          {/* Auto Gain Control */}
          <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Icon 
                name={autoGainControl ? "TrendingUp" : "Minus"} 
                size={18} 
                className={autoGainControl ? "text-success-600" : "text-secondary-600"} 
              />
              <div>
                <div className="text-sm font-medium text-text-primary">
                  Automatic Gain Control
                </div>
                <div className="text-xs text-text-secondary">
                  {autoGainControl ? 'Active - Normalizing audio levels' : 'Disabled - Manual control'}
                </div>
              </div>
            </div>
            
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoGainControl}
                onChange={(e) => onAutoGainControlChange(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-success-500"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      {showAdvanced && (
        <div className="border-t border-border p-4 bg-secondary-50">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-text-primary">
              Advanced Audio Processing
            </h4>

            {/* Custom Noise Suppression Level */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm text-text-secondary">
                  Noise Suppression Level
                </label>
                <span className="text-sm font-medium text-text-primary">
                  {Math.round(customSettings.noiseSuppression * 100)}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={customSettings.noiseSuppression}
                onChange={(e) => handleCustomSettingChange('noiseSuppression', parseFloat(e.target.value))}
                className="w-full h-2 bg-secondary-200 rounded-lg appearance-none cursor-pointer"
                disabled={!noiseReduction}
              />
              <div className="flex justify-between text-xs text-text-muted mt-1">
                <span>Minimal</span>
                <span>Moderate</span>
                <span>Maximum</span>
              </div>
            </div>

            {/* Echo Cancellation Level */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm text-text-secondary">
                  Echo Cancellation Level
                </label>
                <span className="text-sm font-medium text-text-primary">
                  {Math.round(customSettings.echoCancellationLevel * 100)}%
                </span>
              </div>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={customSettings.echoCancellationLevel}
                onChange={(e) => handleCustomSettingChange('echoCancellationLevel', parseFloat(e.target.value))}
                className="w-full h-2 bg-secondary-200 rounded-lg appearance-none cursor-pointer"
                disabled={!echoCancellation}
              />
              <div className="flex justify-between text-xs text-text-muted mt-1">
                <span>Light</span>
                <span>Standard</span>
                <span>Aggressive</span>
              </div>
            </div>

            {/* Frequency Filter */}
            <div>
              <label className="text-sm text-text-secondary mb-2 block">
                Frequency Filter
              </label>
              <div className="space-y-2">
                {frequencyFilterOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleCustomSettingChange('frequencyFilter', option.value)}
                    className={`w-full p-2 rounded border text-left transition-fast ${
                      customSettings.frequencyFilter === option.value
                        ? 'bg-primary-50 border-primary-200 text-primary-700' :'bg-surface border-secondary-200 hover:bg-secondary-50'
                    }`}
                  >
                    <div className="text-sm font-medium">{option.label}</div>
                    <div className="text-xs text-text-secondary">
                      {option.description}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Reset to Defaults */}
            <div className="pt-3 border-t border-border">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setCustomSettings({
                    noiseSuppression: 0.8,
                    echoCancellationLevel: 0.9,
                    gainLevel: 0.7,
                    frequencyFilter: 'voice'
                  });
                }}
                iconName="RotateCcw"
                iconPosition="left"
                className="w-full"
              >
                Reset to Recommended Settings
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Processing Status */}
      <div className="border-t border-border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-text-secondary">
              Audio processing active
            </span>
          </div>
          
          <div className="text-sm text-text-secondary">
            Quality: <span className={`font-medium ${getQualityColor()}`}>
              {audioQuality.charAt(0).toUpperCase() + audioQuality.slice(1)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoiseSuppressionControls;