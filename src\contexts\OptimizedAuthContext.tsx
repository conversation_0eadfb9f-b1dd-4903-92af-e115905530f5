/**
 * OPTIMIZED AUTH CONTEXT FOR MEDICAL PERFORMANCE
 * 
 * This optimized context provides:
 * - Context splitting to prevent unnecessary re-renders
 * - Memoization strategies for expensive medical data computations
 * - Context selectors to minimize component re-renders
 * - Context debugging tools for development and monitoring
 * - Context state persistence for offline medical workflows
 * - Audit logging for medical compliance
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - Authentication state must be reliable and consistent
 * - Medical data access must be properly authorized
 * - Context updates must maintain audit trail
 * - Performance optimizations must not compromise security
 * - Offline functionality must preserve patient safety
 */

import React, { 
  createContext, 
  useContext, 
  useEffect, 
  useState, 
  useCallback, 
  useMemo, 
  useRef,
  memo
} from 'react';
import type { User, UserProfile, AuthError } from '../types/auth';
import type { MedicalDataPriority } from '../types/cache';

import authService from '../utils/authService';
import auditLogger from '../utils/auditLogger';
import intelligentCacheManager from '../utils/intelligentCacheManager';

// Split contexts for better performance
interface AuthStateContext {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  authError: AuthError | null;
  isAuthenticated: boolean;
  userRole: string | null;
  permissions: string[];
  emergencyAccess: {
    active: boolean;
    expiresAt: number | null;
    justification: string | null;
  };
}

interface AuthActionsContext {
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData?: any) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<{ success: boolean; error?: string }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ success: boolean; error?: string }>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  clearError: () => void;
  requestEmergencyAccess: (justification: string) => Promise<{ success: boolean; error?: string }>;
  revokeEmergencyAccess: () => Promise<{ success: boolean; error?: string }>;
  refreshPermissions: () => Promise<void>;
}

interface AuthMetricsContext {
  sessionDuration: number;
  lastActivity: number;
  authAttempts: number;
  emergencyAccessCount: number;
  performanceMetrics: {
    authTime: number;
    profileLoadTime: number;
    permissionCheckTime: number;
  };
}

// Create separate contexts
const AuthStateContext = createContext<AuthStateContext | null>(null);
const AuthActionsContext = createContext<AuthActionsContext | null>(null);
const AuthMetricsContext = createContext<AuthMetricsContext | null>(null);

// Context debugging interface
interface AuthContextDebugInfo {
  renderCount: number;
  lastRender: number;
  stateChanges: Array<{
    timestamp: number;
    property: string;
    oldValue: any;
    newValue: any;
  }>;
  performanceWarnings: string[];
}

// Memoized selectors for specific auth properties
export const useAuthUser = () => {
  const context = useContext(AuthStateContext);
  if (!context) throw new Error('useAuthUser must be used within OptimizedAuthProvider');
  return context.user;
};

export const useAuthLoading = () => {
  const context = useContext(AuthStateContext);
  if (!context) throw new Error('useAuthLoading must be used within OptimizedAuthProvider');
  return context.loading;
};

export const useAuthError = () => {
  const context = useContext(AuthStateContext);
  if (!context) throw new Error('useAuthError must be used within OptimizedAuthProvider');
  return context.authError;
};

export const useAuthPermissions = () => {
  const context = useContext(AuthStateContext);
  if (!context) throw new Error('useAuthPermissions must be used within OptimizedAuthProvider');
  return context.permissions;
};

export const useAuthActions = () => {
  const context = useContext(AuthActionsContext);
  if (!context) throw new Error('useAuthActions must be used within OptimizedAuthProvider');
  return context;
};

export const useAuthMetrics = () => {
  const context = useContext(AuthMetricsContext);
  if (!context) throw new Error('useAuthMetrics must be used within OptimizedAuthProvider');
  return context;
};

// Optimized Auth Provider with context splitting
export const OptimizedAuthProvider: React.FC<{ children: React.ReactNode }> = memo(({ children }) => {
  // State management with performance tracking
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState<AuthError | null>(null);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [emergencyAccess, setEmergencyAccess] = useState({
    active: false,
    expiresAt: null as number | null,
    justification: null as string | null
  });

  // Performance metrics
  const [sessionStartTime] = useState(Date.now());
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [authAttempts, setAuthAttempts] = useState(0);
  const [emergencyAccessCount, setEmergencyAccessCount] = useState(0);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    authTime: 0,
    profileLoadTime: 0,
    permissionCheckTime: 0
  });

  // Debug information
  const renderCount = useRef(0);
  const stateChanges = useRef<AuthContextDebugInfo['stateChanges']>([]);
  const performanceWarnings = useRef<string[]>([]);

  // Increment render count for debugging
  renderCount.current++;

  // Memoized computed values
  const isAuthenticated = useMemo(() => !!user, [user]);
  const userRole = useMemo(() => user?.app_metadata?.role || null, [user?.app_metadata?.role]);
  const sessionDuration = useMemo(() => Date.now() - sessionStartTime, [sessionStartTime]);

  // Memoized auth state context value
  const authStateValue = useMemo<AuthStateContext>(() => ({
    user,
    userProfile,
    loading,
    authError,
    isAuthenticated,
    userRole,
    permissions,
    emergencyAccess
  }), [user, userProfile, loading, authError, isAuthenticated, userRole, permissions, emergencyAccess]);

  // Memoized metrics context value
  const authMetricsValue = useMemo<AuthMetricsContext>(() => ({
    sessionDuration,
    lastActivity,
    authAttempts,
    emergencyAccessCount,
    performanceMetrics
  }), [sessionDuration, lastActivity, authAttempts, emergencyAccessCount, performanceMetrics]);

  // Track state changes for debugging
  const trackStateChange = useCallback((property: string, oldValue: any, newValue: any) => {
    if (process.env.NODE_ENV === 'development') {
      stateChanges.current.push({
        timestamp: Date.now(),
        property,
        oldValue,
        newValue
      });

      // Keep only last 100 changes
      if (stateChanges.current.length > 100) {
        stateChanges.current = stateChanges.current.slice(-100);
      }
    }
  }, []);

  // Optimized sign in function with performance tracking
  const signIn = useCallback(async (email: string, password: string) => {
    const startTime = performance.now();
    
    try {
      setAuthError(null);
      setAuthAttempts(prev => prev + 1);
      
      const result = await authService.signIn(email, password);
      
      if (!result?.success) {
        setAuthError(result?.error || 'Login failed');
        
        await auditLogger.logSecurityEvent(
          'authentication_failed',
          'medium',
          {
            email,
            reason: result?.error || 'unknown',
            attempt_count: authAttempts + 1
          }
        );
        
        return { success: false, error: result?.error };
      }

      const authTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, authTime }));
      
      // Cache auth state for offline access
      await intelligentCacheManager.set(
        `auth:user:${result.data.user.id}`,
        result.data.user,
        {
          priority: 'high' as MedicalDataPriority,
          ttl: 24 * 60 * 60 * 1000, // 24 hours
          dataType: 'user_preferences'
        }
      );

      await auditLogger.logMedicalDataAccess(
        'authentication_success',
        'user_data',
        result.data.user.id,
        {
          auth_time: authTime,
          cached: true,
          session_start: Date.now()
        }
      );

      return { success: true, data: result.data };
    } catch (error) {
      const errorMsg = 'Something went wrong during login. Please try again.';
      setAuthError(errorMsg);
      
      await auditLogger.logSecurityEvent(
        'authentication_error',
        'high',
        {
          email,
          error: error instanceof Error ? error.message : 'unknown',
          attempt_count: authAttempts + 1
        }
      );
      
      return { success: false, error: errorMsg };
    }
  }, [authAttempts]);

  // Optimized sign out with cleanup
  const signOut = useCallback(async () => {
    try {
      setAuthError(null);
      
      const result = await authService.signOut();
      
      if (!result?.success) {
        setAuthError(result?.error || 'Logout failed');
        return { success: false, error: result?.error };
      }

      // Clear cached auth data
      if (user?.id) {
        await intelligentCacheManager.remove(`auth:user:${user.id}`);
        
        await auditLogger.logMedicalDataAccess(
          'authentication_logout',
          'user_data',
          user.id,
          {
            session_duration: sessionDuration,
            emergency_access_used: emergencyAccess.active,
            cached_data_cleared: true
          }
        );
      }

      // Reset all state
      setUser(null);
      setUserProfile(null);
      setPermissions([]);
      setEmergencyAccess({ active: false, expiresAt: null, justification: null });
      setLoading(false);

      return { success: true };
    } catch (error) {
      const errorMsg = 'Something went wrong during logout. Please try again.';
      setAuthError(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, [user?.id, sessionDuration, emergencyAccess.active]);

  // Emergency access request with audit logging
  const requestEmergencyAccess = useCallback(async (justification: string) => {
    if (!user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    try {
      const result = await authService.requestEmergencyAccess(user.id, justification);
      
      if (result.success) {
        setEmergencyAccess({
          active: true,
          expiresAt: Date.now() + (60 * 60 * 1000), // 1 hour
          justification
        });
        
        setEmergencyAccessCount(prev => prev + 1);

        await auditLogger.logEmergencyAccess(
          user.id,
          user.id,
          justification,
          {
            request_time: Date.now(),
            auto_approved: true,
            expires_at: Date.now() + (60 * 60 * 1000)
          }
        );
      }

      return result;
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Emergency access request failed' 
      };
    }
  }, [user?.id]);

  // Refresh permissions with caching
  const refreshPermissions = useCallback(async () => {
    if (!user?.id) return;

    const startTime = performance.now();
    
    try {
      const result = await authService.getUserPermissions(user.id);
      
      if (result.success) {
        setPermissions(result.data);
        
        // Cache permissions
        await intelligentCacheManager.set(
          `auth:permissions:${user.id}`,
          result.data,
          {
            priority: 'high' as MedicalDataPriority,
            ttl: 30 * 60 * 1000, // 30 minutes
            dataType: 'user_preferences'
          }
        );
      }

      const permissionCheckTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, permissionCheckTime }));
      
    } catch (error) {
      console.error('Failed to refresh permissions:', error);
    }
  }, [user?.id]);

  // Memoized actions context value
  const authActionsValue = useMemo<AuthActionsContext>(() => ({
    signIn,
    signUp: async (email: string, password: string, userData = {}) => {
      // Implementation similar to signIn but for signup
      return { success: false, error: 'Not implemented' };
    },
    signOut,
    updateProfile: async (updates: Partial<UserProfile>) => {
      // Implementation for profile updates
      return { success: false, error: 'Not implemented' };
    },
    resetPassword: async (email: string) => {
      // Implementation for password reset
      return { success: false, error: 'Not implemented' };
    },
    clearError: () => setAuthError(null),
    requestEmergencyAccess,
    revokeEmergencyAccess: async () => {
      setEmergencyAccess({ active: false, expiresAt: null, justification: null });
      return { success: true };
    },
    refreshPermissions
  }), [signIn, signOut, requestEmergencyAccess, refreshPermissions]);

  // Initialize auth state with performance tracking
  useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      const startTime = performance.now();
      
      try {
        setLoading(true);
        setAuthError(null);

        // Try to get cached auth state first
        const cachedUser = await intelligentCacheManager.get('auth:current_user');
        if (cachedUser.success && cachedUser.data) {
          setUser(cachedUser.data);
        }

        const sessionResult = await authService.getSession();

        if (sessionResult?.success && sessionResult?.data?.session?.user && isMounted) {
          const authUser = sessionResult.data.session.user;
          setUser(authUser);
          trackStateChange('user', null, authUser);

          // Load user profile
          const profileStartTime = performance.now();
          const profileResult = await authService.getUserProfile(authUser.id);

          if (profileResult?.success && isMounted) {
            setUserProfile(profileResult.data);
            trackStateChange('userProfile', null, profileResult.data);
            
            const profileLoadTime = performance.now() - profileStartTime;
            setPerformanceMetrics(prev => ({ ...prev, profileLoadTime }));
          }

          // Load permissions
          await refreshPermissions();
        }
      } catch (error) {
        if (isMounted) {
          setAuthError('Failed to initialize authentication');
          console.error('Auth initialization error:', error);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
          const totalTime = performance.now() - startTime;
          
          if (totalTime > 2000) {
            performanceWarnings.current.push(
              `Auth initialization took ${totalTime.toFixed(2)}ms - consider optimization`
            );
          }
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = authService.onAuthStateChange(async (event, session) => {
      if (!isMounted) return;

      setLastActivity(Date.now());
      setAuthError(null);

      if (event === 'SIGNED_IN' && session?.user) {
        setUser(session.user);
        trackStateChange('user', user, session.user);
        
        // Cache new user data
        await intelligentCacheManager.set(
          'auth:current_user',
          session.user,
          { priority: 'high' as MedicalDataPriority }
        );
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setUserProfile(null);
        setPermissions([]);
        trackStateChange('user', user, null);
      }
    });

    return () => {
      isMounted = false;
      subscription?.unsubscribe?.();
    };
  }, [refreshPermissions, trackStateChange, user]);

  // Activity tracking for session management
  useEffect(() => {
    const handleActivity = () => setLastActivity(Date.now());
    
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, []);

  // Emergency access expiration check
  useEffect(() => {
    if (emergencyAccess.active && emergencyAccess.expiresAt) {
      const timeUntilExpiry = emergencyAccess.expiresAt - Date.now();
      
      if (timeUntilExpiry > 0) {
        const timeout = setTimeout(() => {
          setEmergencyAccess({ active: false, expiresAt: null, justification: null });
          
          auditLogger.logEmergencyAccess(
            user?.id || 'unknown',
            user?.id || 'unknown',
            'Emergency access expired',
            {
              expiry_time: Date.now(),
              auto_expired: true
            }
          );
        }, timeUntilExpiry);

        return () => clearTimeout(timeout);
      }
    }
  }, [emergencyAccess, user?.id]);

  return (
    <AuthStateContext.Provider value={authStateValue}>
      <AuthActionsContext.Provider value={authActionsValue}>
        <AuthMetricsContext.Provider value={authMetricsValue}>
          {children}
        </AuthMetricsContext.Provider>
      </AuthActionsContext.Provider>
    </AuthStateContext.Provider>
  );
});

OptimizedAuthProvider.displayName = 'OptimizedAuthProvider';

// Context debugging hook for development
export const useAuthDebugInfo = (): AuthContextDebugInfo => {
  if (process.env.NODE_ENV !== 'development') {
    throw new Error('useAuthDebugInfo is only available in development mode');
  }

  return {
    renderCount: renderCount.current,
    lastRender: Date.now(),
    stateChanges: stateChanges.current,
    performanceWarnings: performanceWarnings.current
  };
};

// Composite hook for full auth context (use sparingly)
export const useAuth = () => {
  const state = useContext(AuthStateContext);
  const actions = useContext(AuthActionsContext);
  const metrics = useContext(AuthMetricsContext);

  if (!state || !actions || !metrics) {
    throw new Error('useAuth must be used within OptimizedAuthProvider');
  }

  return { ...state, ...actions, ...metrics };
};

export default {
  OptimizedAuthProvider,
  useAuthUser,
  useAuthLoading,
  useAuthError,
  useAuthPermissions,
  useAuthActions,
  useAuthMetrics,
  useAuth
};
