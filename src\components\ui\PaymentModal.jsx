import React, { useState, useEffect } from 'react';
import { X, CreditCard, Smartphone, Building, QrCode, Loader2 } from 'lucide-react';
import Button from './Button';
import { usePayment } from '../../contexts/PaymentContext';

const PaymentModal = ({ 
  isOpen, 
  onClose, 
  paymentType = 'subscription', // 'subscription' or 'consultation'
  planId = null,
  consultationId = null,
  amount = 0,
  currency = 'NGN',
  planDetails = null
}) => {
  const { openPaymentPopup, isProcessingPayment, paymentError, clearError } = usePayment();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');
  const [paymentStep, setPaymentStep] = useState('method'); // 'method', 'processing', 'success', 'error'

  useEffect(() => {
    if (isOpen) {
      setPaymentStep('method');
      clearError();
    }
  }, [isOpen, clearError]);

  useEffect(() => {
    if (paymentError) {
      setPaymentStep('error');
    }
  }, [paymentError]);

  const handlePayment = async () => {
    try {
      setPaymentStep('processing');

      // Get user profile from localStorage or context
      const userEmail = JSON.parse(localStorage.getItem('voicehealth_user'))?.email;
      
      if (!userEmail) {
        throw new Error('User email not found');
      }

      const paymentData = {
        email: userEmail,
        amount: amount,
        currency: currency,
        onClose: () => {
          setPaymentStep('method');
          onClose();
        },
        callback: (response, verificationResult) => {
          if (verificationResult.success && verificationResult.data.status === 'success') {
            setPaymentStep('success');
            setTimeout(() => {
              onClose();
              // Refresh the page or redirect as needed
              if (paymentType === 'subscription') {
                window.location.href = '/payment-success';
              }
            }, 2000);
          } else {
            setPaymentStep('error');
          }
        }
      };

      const result = openPaymentPopup(paymentData);
      
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Payment error:', error);
      setPaymentStep('error');
    }
  };

  const paymentMethods = [
    {
      id: 'card',
      name: 'Card Payment',
      description: 'Pay with your debit or credit card',
      icon: CreditCard,
      available: true
    },
    {
      id: 'bank',
      name: 'Bank Transfer',
      description: 'Direct bank account transfer',
      icon: Building,
      available: true
    },
    {
      id: 'ussd',
      name: 'USSD',
      description: 'Pay using your mobile phone',
      icon: Smartphone,
      available: true
    },
    {
      id: 'qr',
      name: 'QR Code',
      description: 'Scan to pay with your mobile app',
      icon: QrCode,
      available: true
    }
  ];

  const formatAmount = (amount, currency) => {
    const formatter = new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    });
    return formatter.format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {paymentType === 'subscription' ? 'Subscribe to Plan' : 'Pay for Consultation'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        <div className="p-6">
          {/* Payment Summary */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-start mb-2">
              <span className="text-sm text-gray-600">
                {paymentType === 'subscription' ? 'Plan' : 'Service'}
              </span>
              <span className="text-lg font-semibold text-gray-900">
                {formatAmount(amount, currency)}
              </span>
            </div>
            {planDetails && (
              <div>
                <h3 className="font-medium text-gray-900 mb-1">
                  {planDetails.name}
                </h3>
                <p className="text-sm text-gray-600">
                  {planDetails.description}
                </p>
                {planDetails.features && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-500 mb-1">Includes:</p>
                    <ul className="text-xs text-gray-600 space-y-1">
                      {planDetails.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mr-2"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Payment Steps */}
          {paymentStep === 'method' && (
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                Choose Payment Method
              </h3>
              <div className="space-y-3 mb-6">
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    onClick={() => method.available && setSelectedPaymentMethod(method.id)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedPaymentMethod === method.id
                        ? 'border-blue-500 bg-blue-50'
                        : method.available
                        ? 'border-gray-200 hover:border-gray-300' :'border-gray-100 bg-gray-50 cursor-not-allowed opacity-50'
                    }`}
                  >
                    <div className="flex items-center">
                      <method.icon 
                        size={20} 
                        className={`mr-3 ${
                          selectedPaymentMethod === method.id ? 'text-blue-600' : 'text-gray-500'
                        }`} 
                      />
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          {method.name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {method.description}
                        </p>
                      </div>
                      {selectedPaymentMethod === method.id && (
                        <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <Button
                onClick={handlePayment}
                className="w-full"
                disabled={isProcessingPayment}
              >
                {isProcessingPayment ? (
                  <>
                    <Loader2 size={16} className="animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  `Pay ${formatAmount(amount, currency)}`
                )}
              </Button>
            </div>
          )}

          {paymentStep === 'processing' && (
            <div className="text-center py-8">
              <Loader2 size={48} className="animate-spin text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Processing Payment
              </h3>
              <p className="text-gray-600">
                Please complete the payment in the popup window
              </p>
            </div>
          )}

          {paymentStep === 'success' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Payment Successful!
              </h3>
              <p className="text-gray-600">
                {paymentType === 'subscription' ?'Your subscription has been activated' :'Payment completed successfully'
                }
              </p>
            </div>
          )}

          {paymentStep === 'error' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X size={32} className="text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Payment Failed
              </h3>
              <p className="text-gray-600 mb-4">
                {paymentError || 'Something went wrong with your payment'}
              </p>
              <Button
                onClick={() => setPaymentStep('method')}
                variant="outline"
                className="mr-3"
              >
                Try Again
              </Button>
              <Button onClick={onClose} variant="secondary">
                Close
              </Button>
            </div>
          )}

          {/* Security Notice */}
          {paymentStep === 'method' && (
            <div className="mt-6 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600 text-center">
                🔒 Your payment is secured by Paystack's industry-standard encryption
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;