import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AfricaSpecificDiseasesPanel = ({ searchQuery }) => {
  const [diseases, setDiseases] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedDisease, setSelectedDisease] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const countries = [
    { id: 'all', label: 'All Countries', flag: '🌍' },
    { id: 'ghana', label: 'Ghana', flag: '🇬🇭' },
    { id: 'nigeria', label: 'Nigeria', flag: '🇳🇬' },
    { id: 'kenya', label: 'Kenya', flag: '🇰🇪' },
    { id: 'south-africa', label: 'South Africa', flag: '🇿🇦' }
  ];

  const mockDiseases = [
    {
      id: 'malaria_pf',
      name: 'Malaria - Plasmodium falciparum',
      countries: ['ghana', 'nigeria', 'kenya'],
      prevalence: 'Very High',
      seasonality: 'Year-round, peaks in rainy season',
      symptoms: ['fever', 'chills', 'headache', 'nausea', 'vomiting', 'muscle aches'],
      redFlags: ['cerebral malaria signs', 'severe anemia', 'respiratory distress', 'hypoglycemia'],
      diagnosticMethods: ['Rapid diagnostic test (RDT)', 'Microscopy', 'PCR'],
      treatmentOptions: [
        'Artemisinin-based combination therapy (ACT)',
        'Severe cases: IV Artesunate',
        'Chemoprevention: SMC, IPTp'
      ],
      resistancePatterns: 'Artemisinin resistance emerging in Southeast Asia',
      preventionStrategies: ['ITNs', 'IRS', 'Chemoprevention', 'Vector control'],
      culturalConsiderations: 'Traditional herbal remedies common, education needed',
      costImplications: 'ACT subsidized, prevention cost-effective',
      lastUpdated: '2024-01-15'
    },
    {
      id: 'tuberculosis',
      name: 'Tuberculosis (TB)',
      countries: ['ghana', 'nigeria', 'kenya', 'south-africa'],
      prevalence: 'High',
      seasonality: 'Year-round',
      symptoms: ['persistent cough', 'chest pain', 'weight loss', 'night sweats', 'fever', 'fatigue'],
      redFlags: ['hemoptysis', 'severe weight loss', 'HIV co-infection', 'drug resistance'],
      diagnosticMethods: ['Sputum smear microscopy', 'GeneXpert MTB/RIF', 'Chest X-ray'],
      treatmentOptions: [
        'First-line: HRZE regimen',
        'MDR-TB: Longer regimens with second-line drugs',
        'DOT supervision essential'
      ],
      resistancePatterns: 'MDR-TB and XDR-TB present',
      preventionStrategies: ['Contact tracing', 'BCG vaccination', 'Infection control'],
      culturalConsiderations: 'Stigma issues, traditional medicine interactions',
      costImplications: 'Free treatment programs, but loss of income during treatment',
      lastUpdated: '2024-01-12'
    },
    {
      id: 'sickle_cell',
      name: 'Sickle Cell Disease',
      countries: ['ghana', 'nigeria'],
      prevalence: 'High (genetic)',
      seasonality: 'Year-round with crisis triggers',
      symptoms: ['pain crises', 'anemia', 'fatigue', 'delayed growth', 'infections'],
      redFlags: ['acute chest syndrome', 'stroke', 'splenic sequestration', 'priapism'],
      diagnosticMethods: ['Hemoglobin electrophoresis', 'HPLC', 'Genetic testing'],
      treatmentOptions: [
        'Pain management',
        'Hydroxyurea therapy',
        'Blood transfusions',
        'Bone marrow transplant (rare)'
      ],
      resistancePatterns: 'N/A - genetic condition',
      preventionStrategies: ['Genetic counseling', 'Newborn screening', 'Prophylactic measures'],
      culturalConsiderations: 'Genetic counseling sensitivity, family planning',
      costImplications: 'Lifelong care costs, insurance challenges',
      lastUpdated: '2024-01-10'
    },
    {
      id: 'schistosomiasis',
      name: 'Schistosomiasis',
      countries: ['ghana', 'nigeria', 'kenya'],
      prevalence: 'Moderate to High',
      seasonality: 'Related to water contact patterns',
      symptoms: ['hematuria', 'dysuria', 'abdominal pain', 'diarrhea', 'hepatosplenomegaly'],
      redFlags: ['bladder cancer risk', 'chronic kidney disease', 'portal hypertension'],
      diagnosticMethods: ['Urine microscopy', 'Stool examination', 'Serology'],
      treatmentOptions: [
        'Praziquantel (standard treatment)',
        'Mass drug administration programs',
        'Surgical intervention for complications'
      ],
      resistancePatterns: 'Rare praziquantel resistance reported',
      preventionStrategies: ['Improved sanitation', 'Safe water access', 'Snail control'],
      culturalConsiderations: 'Water contact practices, occupational risks',
      costImplications: 'MDA programs cost-effective, prevention investment needed',
      lastUpdated: '2024-01-08'
    }
  ];

  useEffect(() => {
    const loadDiseases = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setDiseases(mockDiseases);
      setIsLoading(false);
    };

    loadDiseases();
  }, []);

  const filteredDiseases = diseases.filter(disease => {
    const matchesCountry = selectedCountry === 'all' || disease.countries?.includes(selectedCountry);
    const matchesSearch = !searchQuery || 
      disease.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      disease.symptoms?.some(symptom => symptom.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCountry && matchesSearch;
  });

  const getPrevalenceColor = (prevalence) => {
    switch (prevalence.toLowerCase()) {
      case 'very high': return 'bg-error-100 text-error-700';
      case 'high': return 'bg-warning-100 text-warning-700';
      case 'moderate to high': return 'bg-warning-100 text-warning-700';
      case 'moderate': return 'bg-primary-100 text-primary-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading Africa-specific diseases...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Country Filter */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <div className="flex flex-wrap gap-2">
          {countries.map((country) => (
            <button
              key={country.id}
              onClick={() => setSelectedCountry(country.id)}
              className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedCountry === country.id
                  ? 'bg-primary-500 text-white' :'bg-primary-50 text-primary-700 hover:bg-primary-100'
              }`}
            >
              <span className="mr-2">{country.flag}</span>
              {country.label}
            </button>
          ))}
        </div>
      </div>

      {/* Disease Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Globe" size={24} className="text-error-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">{filteredDiseases.length}</p>
              <p className="text-sm text-text-secondary">Endemic Diseases</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="AlertTriangle" size={24} className="text-warning-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredDiseases.filter(d => d.prevalence === 'Very High').length}
              </p>
              <p className="text-sm text-text-secondary">Very High Risk</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Shield" size={24} className="text-success-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredDiseases.filter(d => d.preventionStrategies?.length > 0).length}
              </p>
              <p className="text-sm text-text-secondary">Preventable</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="DollarSign" size={24} className="text-primary-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredDiseases.filter(d => d.costImplications?.includes('cost-effective')).length}
              </p>
              <p className="text-sm text-text-secondary">Cost-Effective Tx</p>
            </div>
          </div>
        </div>
      </div>

      {/* Diseases List */}
      <div className="space-y-4">
        {filteredDiseases.map((disease) => (
          <div
            key={disease.id}
            className={`bg-surface rounded-lg border transition-all ${
              selectedDisease?.id === disease.id
                ? 'border-primary-500 shadow-medium'
                : 'border-border hover:border-primary-300 hover:shadow-small'
            }`}
          >
            <div 
              className="p-6 cursor-pointer"
              onClick={() => setSelectedDisease(selectedDisease?.id === disease.id ? null : disease)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-text-primary mb-2">
                    {disease.name}
                  </h3>
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPrevalenceColor(disease.prevalence)}`}>
                      {disease.prevalence} Prevalence
                    </span>
                    <div className="flex items-center space-x-1">
                      {disease.countries?.map(countryId => {
                        const country = countries.find(c => c.id === countryId);
                        return country ? (
                          <span key={countryId} className="text-lg" title={country.label}>
                            {country.flag}
                          </span>
                        ) : null;
                      })}
                    </div>
                  </div>
                  <p className="text-sm text-text-secondary">{disease.seasonality}</p>
                </div>
                <Icon 
                  name={selectedDisease?.id === disease.id ? "ChevronUp" : "ChevronDown"} 
                  size={20} 
                  className="text-text-secondary" 
                />
              </div>

              {/* Key Symptoms */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-text-primary mb-2">Key Symptoms</h4>
                <div className="flex flex-wrap gap-1">
                  {disease.symptoms?.slice(0, 5).map((symptom, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >
                      {symptom}
                    </span>
                  ))}
                  {disease.symptoms?.length > 5 && (
                    <span className="text-xs text-text-secondary">
                      +{disease.symptoms.length - 5} more
                    </span>
                  )}
                </div>
              </div>

              {/* Expanded Details */}
              {selectedDisease?.id === disease.id && (
                <div className="mt-6 pt-6 border-t border-border space-y-6">
                  {/* Red Flags */}
                  <div>
                    <div className="flex items-center mb-3">
                      <Icon name="AlertTriangle" size={20} className="text-error-500 mr-2" />
                      <h4 className="text-sm font-medium text-text-primary">Red Flags & Complications</h4>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {disease.redFlags?.map((flag, index) => (
                        <span
                          key={index}
                          className="bg-error-50 text-error-700 px-3 py-1 rounded-full text-sm"
                        >
                          {flag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Diagnostic Methods */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Diagnostic Methods</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {disease.diagnosticMethods?.map((method, index) => (
                        <div key={index} className="flex items-center">
                          <Icon name="CheckCircle" size={16} className="text-success-500 mr-2" />
                          <span className="text-sm text-text-secondary">{method}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Treatment Options */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Treatment Options</h4>
                    <div className="space-y-2">
                      {disease.treatmentOptions?.map((treatment, index) => (
                        <div key={index} className="flex items-start">
                          <Icon name="Pill" size={16} className="text-primary-500 mr-2 mt-0.5" />
                          <span className="text-sm text-text-secondary">{treatment}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Prevention Strategies */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Prevention Strategies</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {disease.preventionStrategies?.map((strategy, index) => (
                        <div key={index} className="flex items-center">
                          <Icon name="Shield" size={16} className="text-success-500 mr-2" />
                          <span className="text-sm text-text-secondary">{strategy}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Cultural & Cost Considerations */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Cultural Considerations</h4>
                      <p className="text-sm text-text-secondary">{disease.culturalConsiderations}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Cost Implications</h4>
                      <p className="text-sm text-text-secondary">{disease.costImplications}</p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" iconName="Edit">
                      Edit Disease Profile
                    </Button>
                    <Button variant="outline" size="sm" iconName="BarChart3">
                      View Epidemiology
                    </Button>
                    <Button variant="outline" size="sm" iconName="Users">
                      Patient Education
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredDiseases.length === 0 && (
        <div className="text-center py-12">
          <Icon name="Globe" size={48} className="text-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">No diseases found</h3>
          <p className="text-text-secondary">
            Try selecting a different country or adjusting your search
          </p>
        </div>
      )}
    </div>
  );
};

export default AfricaSpecificDiseasesPanel;