import React from 'react';
import { Setting<PERSON>, ChevronDown, Keyboard, Mic2, Hand, Timer } from 'lucide-react';

const InteractionPreferencesSection = ({ isExpanded, onToggle, settings, onSettingsChange }) => {
  const inputMethods = [
    {
      id: 'touch',
      name: 'Touch/Click',
      icon: Hand,
      description: 'Standard mouse and touch interactions',
      enabled: settings?.inputMethods?.touch ?? true
    },
    {
      id: 'keyboard',
      name: 'Keyboard Navigation',
      icon: Keyboard,
      description: 'Full keyboard control with shortcuts',
      enabled: settings?.inputMethods?.keyboard ?? false
    },
    {
      id: 'voice',
      name: 'Voice Commands',
      icon: Mic2,
      description: 'Control interface with voice commands',
      enabled: settings?.inputMethods?.voice ?? false
    }
  ];

  const keyboardShortcuts = [
    { key: 'Ctrl + M', action: 'Mute/Unmute audio' },
    { key: 'Ctrl + P', action: 'Pause consultation' },
    { key: 'Ctrl + R', action: 'Resume consultation' },
    { key: 'Ctrl + T', action: 'Toggle transcription' },
    { key: 'Ctrl + H', action: 'Show/Hide chat history' },
    { key: 'Tab', action: 'Navigate between elements' },
    { key: 'Enter', action: 'Activate focused element' },
    { key: 'Esc', action: 'Close modal/dialog' }
  ];

  const voiceCommands = [
    'Start consultation',
    'End consultation',
    'Repeat last response',
    'Show medical history',
    'Save session notes',
    'Switch to specialist',
    'Emergency help',
    'Accessibility menu'
  ];

  const timeoutOptions = [
    { value: 0, label: 'No timeout' },
    { value: 300, label: '5 minutes' },
    { value: 600, label: '10 minutes' },
    { value: 900, label: '15 minutes' },
    { value: 1800, label: '30 minutes' }
  ];

  const handleInputMethodToggle = (methodId) => {
    onSettingsChange?.({
      ...settings,
      inputMethods: {
        ...settings?.inputMethods,
        [methodId]: !settings?.inputMethods?.[methodId]
      }
    });
  };

  const handleTimeoutChange = (timeout) => {
    onSettingsChange?.({
      ...settings,
      sessionTimeout: timeout
    });
  };

  return (
    <div className="bg-surface border border-border rounded-xl overflow-hidden">
      <button
        onClick={onToggle}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-secondary-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-warning-50 rounded-lg">
            <Settings className="w-5 h-5 text-warning-600" />
          </div>
          <div className="text-left">
            <h3 className="text-lg font-semibold text-text-primary">Interaction Preferences</h3>
            <p className="text-sm text-text-muted">Input methods and navigation options</p>
          </div>
        </div>
        <ChevronDown className={`w-5 h-5 text-text-muted transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
      </button>

      {isExpanded && (
        <div className="px-6 pb-6 space-y-6">
          {/* Input Methods */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Available Input Methods
            </label>
            <div className="space-y-3">
              {inputMethods.map((method) => (
                <div
                  key={method.id}
                  className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <method.icon className="w-5 h-5 text-primary-600" />
                    <div>
                      <h4 className="font-medium text-text-primary">{method.name}</h4>
                      <p className="text-sm text-text-muted">{method.description}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleInputMethodToggle(method.id)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      method.enabled ? 'bg-primary-600' : 'bg-secondary-300'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        method.enabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Keyboard Shortcuts */}
          {settings?.inputMethods?.keyboard && (
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Available Keyboard Shortcuts
              </label>
              <div className="bg-secondary-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {keyboardShortcuts.map((shortcut, index) => (
                    <div key={index} className="flex items-center justify-between py-2">
                      <span className="text-sm text-text-primary">{shortcut.action}</span>
                      <kbd className="px-2 py-1 bg-white border border-border rounded text-xs font-mono text-text-muted">
                        {shortcut.key}
                      </kbd>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Voice Commands */}
          {settings?.inputMethods?.voice && (
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Available Voice Commands
              </label>
              <div className="bg-secondary-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {voiceCommands.map((command, index) => (
                    <div key={index} className="flex items-center space-x-2 py-1">
                      <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                      <span className="text-sm text-text-primary">"{command}"</span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-primary-50 rounded-lg">
                  <p className="text-sm text-primary-700">
                    <strong>Tip:</strong> Voice commands work best in quiet environments. 
                    You can also say "Help" to hear all available commands.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Motor Accessibility Options */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Motor Accessibility</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Sticky Keys Support</h5>
                  <p className="text-sm text-text-muted">Enable single-key combinations for shortcuts</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    motorAccessibility: {
                      ...settings?.motorAccessibility,
                      stickyKeys: !settings?.motorAccessibility?.stickyKeys
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.motorAccessibility?.stickyKeys ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.motorAccessibility?.stickyKeys ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Large Click Targets</h5>
                  <p className="text-sm text-text-muted">Increase button and link sizes for easier interaction</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    motorAccessibility: {
                      ...settings?.motorAccessibility,
                      largeClickTargets: !settings?.motorAccessibility?.largeClickTargets
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.motorAccessibility?.largeClickTargets ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.motorAccessibility?.largeClickTargets ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Drag and Drop Alternative</h5>
                  <p className="text-sm text-text-muted">Provide click-based alternatives to drag operations</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    motorAccessibility: {
                      ...settings?.motorAccessibility,
                      noDragDrop: !settings?.motorAccessibility?.noDragDrop
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.motorAccessibility?.noDragDrop ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.motorAccessibility?.noDragDrop ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Session Timeout */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">
              Session Timeout
            </label>
            <div className="flex items-center space-x-3">
              <Timer className="w-5 h-5 text-text-muted" />
              <select
                value={settings?.sessionTimeout || 0}
                onChange={(e) => handleTimeoutChange(parseInt(e.target.value))}
                className="flex-1 px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {timeoutOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            <p className="text-sm text-text-muted mt-2">
              Automatically end inactive sessions to preserve privacy and resources
            </p>
          </div>

          {/* Gesture Controls */}
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">Gesture Controls</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Swipe Navigation</h5>
                  <p className="text-sm text-text-muted">Navigate between screens with swipe gestures</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    gestureControls: {
                      ...settings?.gestureControls,
                      swipeNavigation: !settings?.gestureControls?.swipeNavigation
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.gestureControls?.swipeNavigation ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.gestureControls?.swipeNavigation ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                <div>
                  <h5 className="font-medium text-text-primary">Pinch to Zoom</h5>
                  <p className="text-sm text-text-muted">Zoom in on content with pinch gestures</p>
                </div>
                <button
                  onClick={() => onSettingsChange?.({
                    ...settings,
                    gestureControls: {
                      ...settings?.gestureControls,
                      pinchZoom: !settings?.gestureControls?.pinchZoom
                    }
                  })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings?.gestureControls?.pinchZoom ? 'bg-primary-600' : 'bg-secondary-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings?.gestureControls?.pinchZoom ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InteractionPreferencesSection;