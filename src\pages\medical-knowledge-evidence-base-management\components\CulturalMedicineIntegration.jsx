import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CulturalMedicineIntegration = ({ searchQuery }) => {
  const [culturalMedicines, setCulturalMedicines] = useState([]);
  const [selectedMedicine, setSelectedMedicine] = useState(null);
  const [filterSafety, setFilterSafety] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  const safetyLevels = [
    { id: 'all', label: 'All Safety Levels', color: 'bg-gray-100 text-gray-700' },
    { id: 'safe', label: 'Generally Safe', color: 'bg-success-100 text-success-700' },
    { id: 'caution', label: 'Use with Caution', color: 'bg-warning-100 text-warning-700' },
    { id: 'unsafe', label: 'Potentially Unsafe', color: 'bg-error-100 text-error-700' }
  ];

  const mockCulturalMedicines = [
    {
      id: 'artemisia_annua',
      name: '<PERSON><PERSON> annua (Sweet Wormwood)',
      localNames: ['<PERSON><PERSON> (Chinese)', '<PERSON><PERSON><PERSON>ji (Swahili)', 'Wormwood'],
      region: 'Ghana, Nigeria, Kenya',
      traditionalUses: ['Malaria treatment', 'Fever reduction', 'Digestive issues'],
      activeCompounds: ['Artemisinin', 'Flavonoids', 'Essential oils'],
      safetyLevel: 'safe',
      modernValidation: 'High - WHO-approved antimalarial',
      interactions: [
        {
          type: 'synergistic',
          drug: 'Artemisinin-based therapies',
          effect: 'Enhanced antimalarial activity',
          evidence: 'Strong clinical evidence'
        },
        {
          type: 'contraindicated',
          drug: 'Warfarin',
          effect: 'Increased bleeding risk',
          evidence: 'Case reports'
        }
      ],
      preparationMethods: [
        'Dried leaf tea (2-4g daily)',
        'Alcoholic extract (standardized)',
        'Fresh leaf juice (traditional)'
      ],
      contraindications: [
        'Pregnancy (first trimester)',
        'Severe liver disease',
        'Known allergies to Asteraceae family'
      ],
      qualityControl: 'Artemisinin content should be >0.5%',
      culturalSignificance: 'Sacred plant in traditional Chinese medicine',
      costEffectiveness: 'Cost-effective for malaria prevention',
      lastUpdated: '2024-01-15'
    },
    {
      id: 'aloe_vera',
      name: 'Aloe vera',
      localNames: ['Aloe (English)', 'Sabila (Spanish)', 'Sewti (Amharic)'],
      region: 'South Africa, Kenya, Ghana',
      traditionalUses: ['Wound healing', 'Burns treatment', 'Digestive disorders', 'Skin conditions'],
      activeCompounds: ['Aloin', 'Polysaccharides', 'Glycoproteins', 'Amino acids'],
      safetyLevel: 'caution',
      modernValidation: 'Moderate - Evidence for topical use',
      interactions: [
        {
          type: 'potential',
          drug: 'Anticoagulants',
          effect: 'May increase bleeding risk',
          evidence: 'Theoretical risk'
        },
        {
          type: 'beneficial',
          drug: 'Topical antibiotics',
          effect: 'Enhanced wound healing',
          evidence: 'Limited studies'
        }
      ],
      preparationMethods: [
        'Fresh gel for topical use',
        'Dried latex (cathartic - not recommended)',
        'Standardized gel preparations'
      ],
      contraindications: [
        'Oral use during pregnancy/lactation',
        'Inflammatory bowel disease',
        'Kidney disease (oral use)'
      ],
      qualityControl: 'Avoid products with aloin for internal use',
      culturalSignificance: 'Traditional healing in African communities',
      costEffectiveness: 'Very cost-effective for minor wounds',
      lastUpdated: '2024-01-12'
    },
    {
      id: 'moringa_oleifera',
      name: 'Moringa oleifera (Drumstick Tree)',
      localNames: ['Zogale (Hausa)', 'Mlonge (Swahili)', 'Moringa'],
      region: 'Nigeria, Ghana, Kenya',
      traditionalUses: ['Malnutrition', 'Hypertension', 'Diabetes', 'Inflammation'],
      activeCompounds: ['Isothiocyanates', 'Flavonoids', 'Phenolic acids', 'Vitamins A, C, E'],
      safetyLevel: 'safe',
      modernValidation: 'Moderate - Nutritional benefits established',
      interactions: [
        {
          type: 'beneficial',
          drug: 'Antidiabetic medications',
          effect: 'May enhance glucose control',
          evidence: 'Small clinical trials'
        },
        {
          type: 'potential',
          drug: 'Antihypertensive drugs',
          effect: 'Additive blood pressure lowering',
          evidence: 'Animal studies'
        }
      ],
      preparationMethods: [
        'Fresh leaves in food (10-20g daily)',
        'Dried leaf powder (2-6g daily)',
        'Seed oil for topical use'
      ],
      contraindications: [
        'Root bark during pregnancy',
        'Large doses may cause gastric irritation'
      ],
      qualityControl: 'Ensure leaves are properly dried and stored',
      culturalSignificance: 'Miracle tree in West African nutrition',
      costEffectiveness: 'Highly cost-effective nutritional supplement',
      lastUpdated: '2024-01-10'
    },
    {
      id: 'cryptolepis_sanguinolenta',
      name: 'Cryptolepis sanguinolenta (Yellow-dye root)',
      localNames: ['Nibima (Twi)', 'Gangamau (Hausa)', 'Kadze (Ewe)'],
      region: 'Ghana, Nigeria',
      traditionalUses: ['Malaria', 'Fever', 'Rheumatism', 'Wound healing'],
      activeCompounds: ['Cryptolepine', 'Alkaloids', 'Tannins'],
      safetyLevel: 'caution',
      modernValidation: 'Limited - Some antimalarial studies',
      interactions: [
        {
          type: 'unknown',
          drug: 'Antimalarial drugs',
          effect: 'Potential interactions unclear',
          evidence: 'No systematic studies'
        },
        {
          type: 'hepatotoxic',
          drug: 'Hepatotoxic medications',
          effect: 'Possible liver damage',
          evidence: 'Case reports'
        }
      ],
      preparationMethods: [
        'Root decoction (traditional)',
        'Standardized extract (research)',
        'Powder preparation'
      ],
      contraindications: [
        'Liver disease',
        'Pregnancy and lactation',
        'Children under 12'
      ],
      qualityControl: 'Cryptolepine content standardization needed',
      culturalSignificance: 'Important traditional malaria remedy',
      costEffectiveness: 'Needs further research for cost-benefit analysis',
      lastUpdated: '2024-01-08'
    }
  ];

  useEffect(() => {
    const loadCulturalMedicines = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1100));
      setCulturalMedicines(mockCulturalMedicines);
      setIsLoading(false);
    };

    loadCulturalMedicines();
  }, []);

  const filteredMedicines = culturalMedicines.filter(medicine => {
    const matchesSafety = filterSafety === 'all' || medicine.safetyLevel === filterSafety;
    const matchesSearch = !searchQuery || 
      medicine.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      medicine.localNames?.some(name => name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      medicine.traditionalUses?.some(use => use.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesSafety && matchesSearch;
  });

  const getSafetyColor = (level) => {
    switch (level) {
      case 'safe': return 'bg-success-100 text-success-700';
      case 'caution': return 'bg-warning-100 text-warning-700';
      case 'unsafe': return 'bg-error-100 text-error-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getSafetyIcon = (level) => {
    switch (level) {
      case 'safe': return 'CheckCircle';
      case 'caution': return 'AlertTriangle';
      case 'unsafe': return 'XCircle';
      default: return 'Info';
    }
  };

  const getInteractionColor = (type) => {
    switch (type) {
      case 'synergistic': case'beneficial': return 'text-success-600';
      case 'contraindicated': case'hepatotoxic': return 'text-error-600';
      case 'potential': return 'text-warning-600';
      default: return 'text-text-secondary';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-success-500 mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading cultural medicine database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Safety Filter */}
      <div className="bg-surface rounded-lg border border-border p-4">
        <div className="flex flex-wrap gap-2">
          {safetyLevels.map((level) => (
            <button
              key={level.id}
              onClick={() => setFilterSafety(level.id)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                filterSafety === level.id
                  ? level.color.replace('100', '500').replace('700', 'white')
                  : level.color + ' hover:opacity-80'
              }`}
            >
              {level.label}
            </button>
          ))}
        </div>
      </div>

      {/* Cultural Medicine Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Leaf" size={24} className="text-success-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredMedicines.filter(m => m.safetyLevel === 'safe').length}
              </p>
              <p className="text-sm text-text-secondary">Generally Safe</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="AlertTriangle" size={24} className="text-warning-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredMedicines.filter(m => m.safetyLevel === 'caution').length}
              </p>
              <p className="text-sm text-text-secondary">Use with Caution</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="Award" size={24} className="text-primary-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredMedicines.filter(m => m.modernValidation?.includes('High')).length}
              </p>
              <p className="text-sm text-text-secondary">High Validation</p>
            </div>
          </div>
        </div>
        
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center">
            <Icon name="DollarSign" size={24} className="text-success-500 mr-3" />
            <div>
              <p className="text-2xl font-bold text-text-primary">
                {filteredMedicines.filter(m => m.costEffectiveness?.includes('cost-effective')).length}
              </p>
              <p className="text-sm text-text-secondary">Cost-Effective</p>
            </div>
          </div>
        </div>
      </div>

      {/* Cultural Medicines List */}
      <div className="space-y-4">
        {filteredMedicines.map((medicine) => (
          <div
            key={medicine.id}
            className={`bg-surface rounded-lg border transition-all ${
              selectedMedicine?.id === medicine.id
                ? 'border-primary-500 shadow-medium'
                : 'border-border hover:border-primary-300 hover:shadow-small'
            }`}
          >
            <div 
              className="p-6 cursor-pointer"
              onClick={() => setSelectedMedicine(selectedMedicine?.id === medicine.id ? null : medicine)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <Icon name="Leaf" size={20} className="text-success-500 mr-2" />
                    <h3 className="text-lg font-semibold text-text-primary">
                      {medicine.name}
                    </h3>
                  </div>
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSafetyColor(medicine.safetyLevel)}`}>
                      <Icon name={getSafetyIcon(medicine.safetyLevel)} size={14} className="inline mr-1" />
                      {medicine.safetyLevel.charAt(0).toUpperCase() + medicine.safetyLevel.slice(1)}
                    </span>
                    <span className="text-sm text-text-secondary">
                      {medicine.region}
                    </span>
                  </div>
                  <div className="mb-3">
                    <span className="text-sm text-text-secondary">Local names: </span>
                    <span className="text-sm text-text-primary">
                      {medicine.localNames?.join(', ')}
                    </span>
                  </div>
                </div>
                <Icon 
                  name={selectedMedicine?.id === medicine.id ? "ChevronUp" : "ChevronDown"} 
                  size={20} 
                  className="text-text-secondary" 
                />
              </div>

              {/* Traditional Uses */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-text-primary mb-2">Traditional Uses</h4>
                <div className="flex flex-wrap gap-2">
                  {medicine.traditionalUses?.map((use, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >
                      {use}
                    </span>
                  ))}
                </div>
              </div>

              {/* Expanded Details */}
              {selectedMedicine?.id === medicine.id && (
                <div className="mt-6 pt-6 border-t border-border space-y-6">
                  {/* Active Compounds */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Active Compounds</h4>
                    <div className="flex flex-wrap gap-2">
                      {medicine.activeCompounds?.map((compound, index) => (
                        <span
                          key={index}
                          className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm"
                        >
                          {compound}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Modern Validation */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-2">Modern Validation</h4>
                    <p className="text-sm text-text-secondary">{medicine.modernValidation}</p>
                  </div>

                  {/* Drug Interactions */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Drug Interactions</h4>
                    <div className="space-y-3">
                      {medicine.interactions?.map((interaction, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center mb-1">
                                <span className={`text-sm font-medium ${getInteractionColor(interaction.type)}`}>
                                  {interaction.type.charAt(0).toUpperCase() + interaction.type.slice(1)}
                                </span>
                                <span className="text-sm text-text-secondary ml-2">
                                  with {interaction.drug}
                                </span>
                              </div>
                              <p className="text-sm text-text-secondary">{interaction.effect}</p>
                            </div>
                            <span className="text-xs text-text-secondary bg-white px-2 py-1 rounded">
                              {interaction.evidence}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Preparation Methods */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-3">Preparation Methods</h4>
                    <div className="space-y-2">
                      {medicine.preparationMethods?.map((method, index) => (
                        <div key={index} className="flex items-start">
                          <Icon name="Beaker" size={16} className="text-primary-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-text-secondary">{method}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Contraindications */}
                  <div>
                    <div className="flex items-center mb-3">
                      <Icon name="XCircle" size={20} className="text-error-500 mr-2" />
                      <h4 className="text-sm font-medium text-text-primary">Contraindications</h4>
                    </div>
                    <div className="space-y-1">
                      {medicine.contraindications?.map((contraindication, index) => (
                        <div key={index} className="flex items-start">
                          <Icon name="AlertTriangle" size={14} className="text-error-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-text-secondary">{contraindication}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Quality Control & Cultural Significance */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Quality Control</h4>
                      <p className="text-sm text-text-secondary">{medicine.qualityControl}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-text-primary mb-2">Cultural Significance</h4>
                      <p className="text-sm text-text-secondary">{medicine.culturalSignificance}</p>
                    </div>
                  </div>

                  {/* Cost-Effectiveness */}
                  <div>
                    <h4 className="text-sm font-medium text-text-primary mb-2">Cost-Effectiveness</h4>
                    <p className="text-sm text-success-600">{medicine.costEffectiveness}</p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" iconName="Edit">
                      Edit Entry
                    </Button>
                    <Button variant="outline" size="sm" iconName="BookOpen">
                      Research Papers
                    </Button>
                    <Button variant="outline" size="sm" iconName="Users">
                      Patient Education
                    </Button>
                    <Button variant="outline" size="sm" iconName="AlertTriangle">
                      Safety Alerts
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredMedicines.length === 0 && (
        <div className="text-center py-12">
          <Icon name="Leaf" size={48} className="text-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">No cultural medicines found</h3>
          <p className="text-text-secondary">
            Try selecting a different safety level or adjusting your search
          </p>
        </div>
      )}

      {/* Add New Medicine Button */}
      <div className="flex justify-center pt-6">
        <Button variant="primary" iconName="Plus">
          Add New Cultural Medicine
        </Button>
      </div>
    </div>
  );
};

export default CulturalMedicineIntegration;