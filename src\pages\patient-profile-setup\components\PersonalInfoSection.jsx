import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';
import Button from '../../../components/ui/Button';
import Image from '../../../components/AppImage';

const PersonalInfoSection = ({ 
  data = {}, 
  onUpdate = () => {}, 
  isExpanded = true, 
  onToggle = () => {},
  errors = {}
}) => {
  const [profilePhoto, setProfilePhoto] = useState(data.profilePhoto || null);
  const [photoPreview, setPhotoPreview] = useState(data.profilePhoto || null);

  const handleInputChange = (field, value) => {
    onUpdate({ ...data, [field]: value });
  };

  const handlePhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const photoUrl = e.target.result;
        setProfilePhoto(file);
        setPhotoPreview(photoUrl);
        handleInputChange('profilePhoto', photoUrl);
      };
      reader.readAsDataURL(file);
    }
  };

  const removePhoto = () => {
    setProfilePhoto(null);
    setPhotoPreview(null);
    handleInputChange('profilePhoto', null);
  };

  return (
    <div className="bg-surface border border-border rounded-xl shadow-minimal overflow-hidden">
      {/* Section Header */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-6 hover:bg-secondary-50 transition-fast"
      >
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-50 rounded-lg flex items-center justify-center">
            <Icon name="User" size={20} color="var(--color-primary)" />
          </div>
          <div className="text-left">
            <h3 className="font-semibold text-text-primary font-heading">
              Personal Information
            </h3>
            <p className="text-sm text-text-secondary font-caption">
              Basic details for your health profile
            </p>
          </div>
        </div>
        <Icon 
          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
          size={20} 
          color="var(--color-text-secondary)" 
        />
      </button>

      {/* Section Content */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-border">
          <div className="space-y-6">
            {/* Profile Photo Upload */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="flex-shrink-0">
                <div className="relative">
                  {photoPreview ? (
                    <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-primary-100">
                      <Image
                        src={photoPreview}
                        alt="Profile photo"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-24 h-24 bg-secondary-100 rounded-full flex items-center justify-center border-2 border-dashed border-secondary-300">
                      <Icon name="Camera" size={24} color="var(--color-text-secondary)" />
                    </div>
                  )}
                  
                  {photoPreview && (
                    <button
                      onClick={removePhoto}
                      className="absolute -top-2 -right-2 w-6 h-6 bg-error-500 rounded-full flex items-center justify-center hover:bg-error-600 transition-fast"
                    >
                      <Icon name="X" size={12} color="white" />
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex-1">
                <label className="block">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    iconName="Upload"
                    iconPosition="left"
                    className="w-full sm:w-auto"
                  >
                    {photoPreview ? 'Change Photo' : 'Upload Photo'}
                  </Button>
                </label>
                <p className="text-xs text-text-secondary mt-2">
                  JPG, PNG or GIF. Max size 5MB.
                </p>
              </div>
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Full Name */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Full Name *
                </label>
                <Input
                  type="text"
                  placeholder="Enter your full name"
                  value={data.fullName || ''}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                  required
                  className={errors.fullName ? 'border-error-500' : ''}
                />
                {errors.fullName && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.fullName}</span>
                  </p>
                )}
              </div>

              {/* Date of Birth */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Date of Birth *
                </label>
                <Input
                  type="date"
                  value={data.dateOfBirth || ''}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  required
                  className={errors.dateOfBirth ? 'border-error-500' : ''}
                />
                {errors.dateOfBirth && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.dateOfBirth}</span>
                  </p>
                )}
              </div>

              {/* Gender */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Gender *
                </label>
                <select
                  value={data.gender || ''}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  required
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer_not_to_say">Prefer not to say</option>
                </select>
                {errors.gender && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.gender}</span>
                  </p>
                )}
              </div>

              {/* Phone Number */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Phone Number *
                </label>
                <Input
                  type="tel"
                  placeholder="+****************"
                  value={data.phoneNumber || ''}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  required
                  className={errors.phoneNumber ? 'border-error-500' : ''}
                />
                {errors.phoneNumber && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.phoneNumber}</span>
                  </p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Email Address *
                </label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={data.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                  className={errors.email ? 'border-error-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.email}</span>
                  </p>
                )}
              </div>

              {/* Country */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Country *
                </label>
                <select
                  value={data.country || ''}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  required
                >
                  <option value="">Select country</option>
                  <option value="NG">Nigeria</option>
                  <option value="GH">Ghana</option>
                  <option value="KE">Kenya</option>
                  <option value="ZA">South Africa</option>
                  <option value="EG">Egypt</option>
                  <option value="US">United States</option>
                  <option value="CA">Canada</option>
                  <option value="GB">United Kingdom</option>
                  <option value="DE">Germany</option>
                  <option value="FR">France</option>
                  <option value="IN">India</option>
                  <option value="AU">Australia</option>
                  <option value="BR">Brazil</option>
                  <option value="OTHER">Other</option>
                </select>
                {errors.country && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.country}</span>
                  </p>
                )}
              </div>

              {/* Occupation */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Occupation
                </label>
                <Input
                  type="text"
                  placeholder="Your occupation"
                  value={data.occupation || ''}
                  onChange={(e) => handleInputChange('occupation', e.target.value)}
                  className={errors.occupation ? 'border-error-500' : ''}
                />
                {errors.occupation && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.occupation}</span>
                  </p>
                )}
              </div>

              {/* Insurance Status */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Insurance Status
                </label>
                <select
                  value={data.insuranceStatus || ''}
                  onChange={(e) => handleInputChange('insuranceStatus', e.target.value)}
                  className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                >
                  <option value="">Select insurance status</option>
                  <option value="insured">Insured</option>
                  <option value="uninsured">Uninsured</option>
                  <option value="government">Government Insurance</option>
                  <option value="private">Private Insurance</option>
                  <option value="unknown">Unknown</option>
                </select>
                {errors.insuranceStatus && (
                  <p className="text-sm text-error-500 mt-1 flex items-center space-x-1">
                    <Icon name="AlertCircle" size={14} />
                    <span>{errors.insuranceStatus}</span>
                  </p>
                )}
              </div>

              {/* Emergency Contact */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Emergency Contact
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <Input
                    type="text"
                    placeholder="Contact name"
                    value={data.emergencyContactName || ''}
                    onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                  />
                  <Input
                    type="tel"
                    placeholder="Contact phone"
                    value={data.emergencyContactPhone || ''}
                    onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                  />
                  <select
                    value={data.emergencyContactRelationship || ''}
                    onChange={(e) => handleInputChange('emergencyContactRelationship', e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-surface text-text-primary"
                  >
                    <option value="">Relationship</option>
                    <option value="spouse">Spouse</option>
                    <option value="parent">Parent</option>
                    <option value="child">Child</option>
                    <option value="sibling">Sibling</option>
                    <option value="friend">Friend</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              {/* Address */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Address
                </label>
                <div className="space-y-4">
                  <Input
                    type="text"
                    placeholder="Street address"
                    value={data.streetAddress || ''}
                    onChange={(e) => handleInputChange('streetAddress', e.target.value)}
                  />
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <Input
                      type="text"
                      placeholder="City"
                      value={data.city || ''}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                    />
                    <Input
                      type="text"
                      placeholder="State/Province"
                      value={data.state || ''}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                    />
                    <Input
                      type="text"
                      placeholder="ZIP/Postal Code"
                      value={data.zipCode || ''}
                      onChange={(e) => handleInputChange('zipCode', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PersonalInfoSection;