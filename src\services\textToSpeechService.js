/**
 * SECURE Text-to-Speech Service using Backend Proxy
 * Handles speech synthesis for voice consultations via secure server-side proxy
 *
 * SECURITY FEATURES:
 * - No client-side API keys
 * - Server-side authentication and authorization
 * - HIPAA-compliant request handling
 * - Comprehensive audit logging
 */
class TextToSpeechService {
  constructor() {
    this.baseUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/text-to-speech`;
    // API key removed - handled server-side for security
    this.defaultVoiceId = 'ErXwobaYiN019PkySvjV'; // <PERSON><PERSON> voice (clear, professional)
    this.fallbackVoiceId = 'EXAVITQu4vr4xnSDxMaL'; // Sarah voice (warm, caring)

    // Voice configurations for different agent types
    this.agentVoices = {
      'general-practitioner': 'ErXwobaYiN019PkySvjV', // Antoni - professional
      'cardiologist': 'VR6AewLTigWG4xSOukaG', // Arnold - authoritative
      'pediatrician': 'EXAVITQu4vr4xnSDxMaL', // Sarah - warm
      'mental-health': 'pNInz6obpgDQGcFmaJgB', // <PERSON> - calm
      'default': 'ErXwobaYiN019PkySvjV'
    };
  }

  /**
   * Convert text to speech using secure backend proxy
   */
  async synthesizeSpeech(text, options = {}) {
    try {
      console.log('🔊 Starting secure text-to-speech synthesis...', {
        text: text.substring(0, 50) + '...',
        voice: options.voiceId || 'default'
      });

      // Get authentication token
      const authToken = await this.getAuthToken();
      if (!authToken) {
        throw new Error('Authentication required for text-to-speech service');
      }

      // Validate session ID
      if (!options.sessionId) {
        throw new Error('Session ID is required for secure text-to-speech');
      }

      // Validate text length
      if (text.length > 5000) {
        throw new Error('Text too long. Maximum 5000 characters allowed.');
      }

      const voiceId = this.getVoiceForAgent(options.agentType) || options.voiceId || this.defaultVoiceId;

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
          ...(options.emergencyOverride && { 'X-Emergency-Override': 'true' })
        },
        body: JSON.stringify({
          text,
          sessionId: options.sessionId,
          voiceId,
          stability: options.stability || 0.5,
          similarity_boost: options.similarity_boost || 0.75
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Secure text-to-speech error:', response.status, errorData);

        // Handle specific error codes
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Access denied to this session.');
        } else if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }

        throw new Error(`Text-to-speech service error: ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Text-to-speech synthesis failed');
      }

      // Convert base64 audio data to blob
      const audioData = result.data.audioData;
      const audioBytes = Uint8Array.from(atob(audioData), c => c.charCodeAt(0));
      const audioBlob = new Blob([audioBytes], { type: result.data.audioFormat });

      console.log('✅ Secure speech synthesis successful:', {
        size: audioBlob.size,
        duration: result.data.duration,
        processingTime: result.data.processing_time
      });

      return {
        audioBlob,
        audioUrl: URL.createObjectURL(audioBlob),
        duration: result.data.duration,
        voiceId: result.data.voiceId,
        processingTime: result.data.processing_time
      };

    } catch (error) {
      console.error('❌ Secure text-to-speech error:', error);

      // Enhanced error handling for secure service
      if (error.message.includes('Authentication')) {
        // Clear potentially invalid auth token
        this.clearAuthToken();
      }

      throw error;
    }
  }

  /**
   * Get authentication token from current session
   */
  async getAuthToken() {
    try {
      // Get token from Supabase auth context
      const { supabase } = await import('../utils/supabaseClient');
      const { data: { session } } = await supabase.auth.getSession();
      return session?.access_token;
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  }

  /**
   * Clear authentication token (for error recovery)
   */
  async clearAuthToken() {
    try {
      const { supabase } = await import('../utils/supabaseClient');
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Failed to clear auth token:', error);
    }
  }

  /**
   * Get appropriate voice for agent type
   */
  getVoiceForAgent(agentType) {
    return this.agentVoices[agentType] || this.agentVoices.default;
  }

  /**
   * Estimate speech duration based on text length
   */
  estimateDuration(text) {
    // Average speaking rate: ~150 words per minute
    const words = text.split(' ').length;
    const minutes = words / 150;
    return Math.max(1, Math.round(minutes * 60 * 1000)); // Convert to milliseconds
  }

  /**
   * Mock speech synthesis for development/testing
   */
  getMockSpeech(text) {
    const duration = this.estimateDuration(text);
    
    // Create a simple audio context for silence
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const buffer = audioContext.createBuffer(1, audioContext.sampleRate * (duration / 1000), audioContext.sampleRate);
    
    // Create a simple tone instead of silence for better testing
    const channelData = buffer.getChannelData(0);
    for (let i = 0; i < channelData.length; i++) {
      channelData[i] = Math.sin(2 * Math.PI * 440 * i / audioContext.sampleRate) * 0.1;
    }

    return new Promise((resolve) => {
      const offlineContext = new OfflineAudioContext(1, buffer.length, audioContext.sampleRate);
      const source = offlineContext.createBufferSource();
      source.buffer = buffer;
      source.connect(offlineContext.destination);
      source.start(0);
      
      offlineContext.startRendering().then((renderedBuffer) => {
        // Convert to wav blob
        const audioBlob = this.bufferToWav(renderedBuffer);
        
        resolve({
          audioBlob,
          audioUrl: URL.createObjectURL(audioBlob),
          duration,
          voiceId: 'mock-voice',
          isMock: true
        });
      });
    });
  }

  /**
   * Convert AudioBuffer to WAV blob
   */
  bufferToWav(buffer) {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    
    // WAV header
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Audio data
    const channelData = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  /**
   * Play synthesized audio
   */
  async playAudio(audioUrl, options = {}) {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl);
      
      audio.onloadedmetadata = () => {
        console.log('🎵 Audio loaded, duration:', audio.duration);
      };
      
      audio.onended = () => {
        console.log('✅ Audio playback completed');
        URL.revokeObjectURL(audioUrl); // Clean up
        resolve();
      };
      
      audio.onerror = (error) => {
        console.error('❌ Audio playback error:', error);
        reject(error);
      };
      
      audio.volume = options.volume || 0.8;
      audio.playbackRate = options.speed || 1.0;
      
      audio.play().catch(reject);
    });
  }

  /**
   * Check if service is configured properly
   */
  isConfigured() {
    return Boolean(this.apiKey);
  }

  /**
   * Health check for the service
   */
  async healthCheck() {
    try {
      if (!this.apiKey) {
        return { status: 'error', message: 'API key not configured' };
      }
      
      // Test with a simple voices endpoint
      const response = await fetch(`${this.baseUrl}/voices`, {
        headers: {
          'xi-api-key': this.apiKey
        }
      });
      
      if (response.ok) {
        return { status: 'healthy', message: 'Service configured and ready' };
      } else {
        return { status: 'error', message: `API returned ${response.status}` };
      }
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Get available voices for text-to-speech
   */
  getAvailableVoices() {
    return [
      {
        id: 'en-US-female-1',
        name: 'Sarah',
        language: 'en-US',
        gender: 'female',
        quality: 'high'
      },
      {
        id: 'en-US-male-1',
        name: 'David',
        language: 'en-US',
        gender: 'male',
        quality: 'high'
      },
      {
        id: 'en-GB-female-1',
        name: 'Emma',
        language: 'en-GB',
        gender: 'female',
        quality: 'high'
      }
    ];
  }

  /**
   * Estimate duration of text-to-speech synthesis
   */
  estimateDuration(text, options = {}) {
    if (!text || typeof text !== 'string') {
      return 0;
    }

    // Rough estimation: ~150 words per minute average speaking rate
    const wordsPerMinute = options.speed === 'fast' ? 180 : options.speed === 'slow' ? 120 : 150;
    const wordCount = text.trim().split(/\s+/).length;
    const durationMinutes = wordCount / wordsPerMinute;

    return Math.max(0.5, durationMinutes * 60); // Return duration in seconds, minimum 0.5s
  }
}

export default new TextToSpeechService();
