// PWA Service for managing service worker, offline capabilities, and sync
class PWAService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.serviceWorker = null;
    this.syncInProgress = false;
    this.pendingActions = [];
    this.subscribers = new Set();
    
    this.init();
  }
  
  async init() {
    // Register service worker
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorker = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered successfully');
        
        // Listen for service worker updates
        this.serviceWorker.addEventListener('updatefound', () => {
          this.notifySubscribers('updateAvailable', null);
        });
        
        // Listen for messages from service worker
        navigator.serviceWorker.addEventListener('message', (event) => {
          this.handleServiceWorkerMessage(event.data);
        });
        
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
    
    // Setup online/offline event listeners
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.notifySubscribers('online', true);
      this.processPendingActions();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifySubscribers('offline', false);
    });
    
    // Setup background sync if supported
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      console.log('Background sync is supported');
    }
    
    // Setup periodic sync check
    setInterval(() => {
      this.checkSyncStatus();
    }, 60000); // Check every minute
  }
  
  // Subscribe to PWA events
  subscribe(callback) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }
  
  // Notify all subscribers
  notifySubscribers(event, data) {
    this.subscribers.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in PWA subscriber callback:', error);
      }
    });
  }
  
  // Handle messages from service worker
  handleServiceWorkerMessage(data) {
    const { type, payload } = data;
    
    switch (type) {
      case 'SYNC_SUCCESS':
        this.notifySubscribers('syncSuccess', payload);
        break;
      case 'SYNC_FAILED':
        this.notifySubscribers('syncFailed', payload);
        break;
      case 'CACHE_STATUS':
        this.notifySubscribers('cacheStatus', payload);
        break;
      case 'CACHE_CLEARED':
        this.notifySubscribers('cacheCleared', null);
        break;
    }
  }
  
  // Check if app is online
  isAppOnline() {
    return this.isOnline;
  }
  
  // Add action to pending queue for offline processing
  addPendingAction(action) {
    this.pendingActions.push({
      ...action,
      timestamp: Date.now(),
      id: Math.random().toString(36).substr(2, 9)
    });
    
    this.notifySubscribers('pendingActionAdded', {
      count: this.pendingActions.length
    });
  }
  
  // Process pending actions when back online
  async processPendingActions() {
    if (!this.isOnline || this.syncInProgress || this.pendingActions.length === 0) {
      return;
    }
    
    this.syncInProgress = true;
    this.notifySubscribers('syncStarted', null);
    
    const actionsToProcess = [...this.pendingActions];
    const successfulActions = [];
    const failedActions = [];
    
    for (const action of actionsToProcess) {
      try {
        await this.processAction(action);
        successfulActions.push(action);
      } catch (error) {
        console.error('Failed to process pending action:', error);
        failedActions.push(action);
      }
    }
    
    // Remove successful actions
    this.pendingActions = this.pendingActions.filter(
      action => !successfulActions.includes(action)
    );
    
    this.syncInProgress = false;
    this.notifySubscribers('syncCompleted', {
      successful: successfulActions.length,
      failed: failedActions.length,
      remaining: this.pendingActions.length
    });
  }
  
  // Process a single action
  async processAction(action) {
    const { type, data, url, method = 'POST' } = action;
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...action.headers
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`Action processing failed: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  // Force sync data
  async forceSync(syncType = null) {
    if (this.serviceWorker) {
      const messageChannel = new MessageChannel();
      this.serviceWorker.postMessage(
        { type: 'FORCE_SYNC', payload: { syncType } },
        [messageChannel.port2]
      );
    }
  }
  
  // Get cache status
  async getCacheStatus() {
    if (this.serviceWorker) {
      return new Promise((resolve) => {
        const messageChannel = new MessageChannel();
        messageChannel.port1.onmessage = (event) => {
          if (event.data.type === 'CACHE_STATUS') {
            resolve(event.data.payload);
          }
        };
        
        this.serviceWorker.postMessage(
          { type: 'GET_CACHE_STATUS' },
          [messageChannel.port2]
        );
      });
    }
    return null;
  }
  
  // Clear cache
  async clearCache(cacheType = null) {
    if (this.serviceWorker) {
      const messageChannel = new MessageChannel();
      this.serviceWorker.postMessage(
        { type: 'CLEAR_CACHE', payload: { cacheType } },
        [messageChannel.port2]
      );
    }
  }
  
  // Check sync status
  async checkSyncStatus() {
    if (!this.isOnline) return;
    
    try {
      // Ping a lightweight endpoint to check connectivity
      const response = await fetch('/api/health-check', {
        method: 'GET',
        cache: 'no-cache'
      });
      
      if (response.ok && this.pendingActions.length > 0) {
        this.processPendingActions();
      }
    } catch (error) {
      // Network is still having issues
      console.log('Network check failed:', error);
    }
  }
  
  // Update service worker
  async updateServiceWorker() {
    if (this.serviceWorker) {
      const messageChannel = new MessageChannel();
      this.serviceWorker.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }
  
  // Get pending actions count
  getPendingActionsCount() {
    return this.pendingActions.length;
  }
  
  // Check if specific data type needs sync
  needsSync(dataType) {
    return this.pendingActions.some(action => action.type === dataType);
  }
  
  // Install app prompt
  async showInstallPrompt() {
    if (this.deferredPrompt) {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      console.log(`User ${outcome} the install prompt`);
      this.deferredPrompt = null;
      return outcome === 'accepted';
    }
    return false;
  }
  
  // Check if app can be installed
  canInstall() {
    return !!this.deferredPrompt;
  }
}

// Create singleton instance
const pwaService = new PWAService();

// Handle install prompt
window.addEventListener('beforeinstallprompt', (e) => {
  e.preventDefault();
  pwaService.deferredPrompt = e;
  pwaService.notifySubscribers('installable', true);
});

// Handle app installed
window.addEventListener('appinstalled', () => {
  pwaService.notifySubscribers('installed', true);
  pwaService.deferredPrompt = null;
});

export default pwaService;