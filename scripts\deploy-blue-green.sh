#!/bin/bash

# VoiceHealth AI - Blue-Green Deployment Script
# Phase 4: Deployment Automation
# Implements zero-downtime blue-green deployment with health checks

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_TIMEOUT=600  # 10 minutes
HEALTH_CHECK_INTERVAL=10  # 10 seconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 --region REGION --image IMAGE --environment ENV [OPTIONS]

Deploy VoiceHealth AI using blue-green deployment strategy.

Required Arguments:
    --region REGION         Target region (ghana, kenya, nigeria, south-africa)
    --image IMAGE          Docker image to deploy
    --environment ENV      Environment (staging, production)

Optional Arguments:
    --timeout SECONDS      Deployment timeout (default: 600)
    --health-check-url URL Custom health check URL
    --skip-backup         Skip database backup
    --force               Force deployment even if health checks fail
    --dry-run             Show what would be deployed without executing
    --help                Show this help message

Examples:
    $0 --region ghana --image voicehealth:v1.2.3 --environment production
    $0 --region kenya --image voicehealth:latest --environment staging --dry-run

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --region)
                REGION="$2"
                shift 2
                ;;
            --image)
                IMAGE="$2"
                shift 2
                ;;
            --environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --timeout)
                DEPLOYMENT_TIMEOUT="$2"
                shift 2
                ;;
            --health-check-url)
                HEALTH_CHECK_URL="$2"
                shift 2
                ;;
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --force)
                FORCE_DEPLOYMENT=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [[ -z "${REGION:-}" ]]; then
        log_error "Region is required"
        usage
        exit 1
    fi

    if [[ -z "${IMAGE:-}" ]]; then
        log_error "Image is required"
        usage
        exit 1
    fi

    if [[ -z "${ENVIRONMENT:-}" ]]; then
        log_error "Environment is required"
        usage
        exit 1
    fi

    # Validate region
    case $REGION in
        ghana|kenya|nigeria|south-africa)
            ;;
        *)
            log_error "Invalid region: $REGION"
            log_error "Valid regions: ghana, kenya, nigeria, south-africa"
            exit 1
            ;;
    esac

    # Set defaults
    SKIP_BACKUP=${SKIP_BACKUP:-false}
    FORCE_DEPLOYMENT=${FORCE_DEPLOYMENT:-false}
    DRY_RUN=${DRY_RUN:-false}
    HEALTH_CHECK_URL=${HEALTH_CHECK_URL:-"https://${ENVIRONMENT}-${REGION}.voicehealth.ai/health"}
}

# Load regional configuration
load_regional_config() {
    local config_file="$PROJECT_ROOT/config/regions/$REGION.json"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "Regional configuration not found: $config_file"
        exit 1
    fi

    log_info "Loading regional configuration for $REGION"
    
    # Extract configuration values using jq
    REGION_CODE=$(jq -r '.countryCode' "$config_file")
    EMERGENCY_NUMBER=$(jq -r '.healthcareSystem.emergencyServices.emergencyNumber' "$config_file")
    PRIMARY_LANGUAGE=$(jq -r '.languages[] | select(.primary == true) | .code' "$config_file")
    CULTURAL_CONTEXT=$(jq -r '.cultural.primaryCultures[0]' "$config_file")
    
    log_info "Region: $REGION ($REGION_CODE)"
    log_info "Primary Language: $PRIMARY_LANGUAGE"
    log_info "Cultural Context: $CULTURAL_CONTEXT"
    log_info "Emergency Number: $EMERGENCY_NUMBER"
}

# Validate deployment readiness
validate_deployment_readiness() {
    log_info "Validating deployment readiness for $REGION..."

    # Check if image exists
    if ! docker manifest inspect "$IMAGE" > /dev/null 2>&1; then
        log_error "Docker image not found: $IMAGE"
        exit 1
    fi

    # Validate regional compliance
    log_info "Validating regional compliance..."
    if ! python3 "$SCRIPT_DIR/validate-regional-compliance.py" --region "$REGION" --environment "$ENVIRONMENT"; then
        log_error "Regional compliance validation failed"
        exit 1
    fi

    # Check database connectivity
    log_info "Checking database connectivity..."
    if ! "$SCRIPT_DIR/check-database-health.sh" --region "$REGION"; then
        log_error "Database health check failed"
        exit 1
    fi

    # Validate cultural configurations
    log_info "Validating cultural configurations..."
    if ! python3 "$SCRIPT_DIR/validate-cultural-config.py" --region "$REGION"; then
        log_error "Cultural configuration validation failed"
        exit 1
    fi

    log_success "Deployment readiness validation passed"
}

# Backup current deployment
backup_current_deployment() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        log_warning "Skipping backup as requested"
        return 0
    fi

    log_info "Creating backup of current deployment..."

    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="$PROJECT_ROOT/backups/${REGION}/${backup_timestamp}"

    mkdir -p "$backup_dir"

    # Backup database
    log_info "Backing up database..."
    if ! "$SCRIPT_DIR/backup-database.sh" --region "$REGION" --output "$backup_dir/database.sql"; then
        log_error "Database backup failed"
        exit 1
    fi

    # Backup configuration
    log_info "Backing up configuration..."
    cp -r "$PROJECT_ROOT/config/regions/$REGION.json" "$backup_dir/"
    
    # Save current image tag
    echo "$IMAGE" > "$backup_dir/previous_image.txt"

    log_success "Backup completed: $backup_dir"
    echo "$backup_dir" > "/tmp/voicehealth_backup_path_${REGION}"
}

# Deploy to green environment
deploy_green_environment() {
    log_info "Deploying to green environment..."

    local green_compose_file="$PROJECT_ROOT/deployment/${ENVIRONMENT}/docker-compose.green.yml"
    
    # Generate green environment configuration
    log_info "Generating green environment configuration..."
    envsubst < "$PROJECT_ROOT/deployment/${ENVIRONMENT}/docker-compose.green.yml.template" > "$green_compose_file"

    # Set environment variables for deployment
    export VOICEHEALTH_IMAGE="$IMAGE"
    export VOICEHEALTH_REGION="$REGION"
    export VOICEHEALTH_ENVIRONMENT="$ENVIRONMENT"
    export VOICEHEALTH_PRIMARY_LANGUAGE="$PRIMARY_LANGUAGE"
    export VOICEHEALTH_CULTURAL_CONTEXT="$CULTURAL_CONTEXT"
    export VOICEHEALTH_EMERGENCY_NUMBER="$EMERGENCY_NUMBER"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Would deploy with the following configuration:"
        echo "Image: $IMAGE"
        echo "Region: $REGION"
        echo "Environment: $ENVIRONMENT"
        echo "Primary Language: $PRIMARY_LANGUAGE"
        echo "Cultural Context: $CULTURAL_CONTEXT"
        return 0
    fi

    # Deploy green environment
    log_info "Starting green environment deployment..."
    if ! docker-compose -f "$green_compose_file" up -d; then
        log_error "Green environment deployment failed"
        exit 1
    fi

    log_success "Green environment deployed successfully"
}

# Health check function
perform_health_check() {
    local url="$1"
    local max_attempts=60  # 10 minutes with 10-second intervals
    local attempt=1

    log_info "Performing health check on: $url"

    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts..."

        if curl -f -s -o /dev/null --max-time 10 "$url"; then
            log_success "Health check passed"
            return 0
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Health check failed after $max_attempts attempts"
            return 1
        fi

        sleep $HEALTH_CHECK_INTERVAL
        ((attempt++))
    done
}

# Validate green environment
validate_green_environment() {
    log_info "Validating green environment..."

    # Basic health check
    local green_health_url="${HEALTH_CHECK_URL/blue/green}"
    if ! perform_health_check "$green_health_url"; then
        if [[ "$FORCE_DEPLOYMENT" != "true" ]]; then
            log_error "Green environment health check failed"
            exit 1
        else
            log_warning "Health check failed but continuing due to --force flag"
        fi
    fi

    # Test emergency response time
    log_info "Testing emergency response time..."
    if ! python3 "$SCRIPT_DIR/test-emergency-response.py" --region "$REGION" --environment green; then
        log_error "Emergency response time test failed"
        exit 1
    fi

    # Test cultural adaptations
    log_info "Testing cultural adaptations..."
    if ! python3 "$SCRIPT_DIR/test-cultural-adaptations.py" --region "$REGION" --environment green; then
        log_error "Cultural adaptation test failed"
        exit 1
    fi

    # Test AI agent functionality
    log_info "Testing AI agent functionality..."
    if ! "$SCRIPT_DIR/test-ai-agents.sh" --region "$REGION" --environment green; then
        log_error "AI agent functionality test failed"
        exit 1
    fi

    log_success "Green environment validation passed"
}

# Switch traffic to green
switch_traffic_to_green() {
    log_info "Switching traffic to green environment..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Would switch traffic to green environment"
        return 0
    fi

    # Update load balancer configuration
    if ! "$SCRIPT_DIR/update-load-balancer.sh" --region "$REGION" --target green; then
        log_error "Failed to switch traffic to green environment"
        exit 1
    fi

    # Wait for traffic to stabilize
    log_info "Waiting for traffic to stabilize..."
    sleep 30

    # Verify traffic switch
    if ! "$SCRIPT_DIR/verify-traffic-switch.sh" --region "$REGION" --target green; then
        log_error "Traffic switch verification failed"
        exit 1
    fi

    log_success "Traffic successfully switched to green environment"
}

# Cleanup old blue environment
cleanup_blue_environment() {
    log_info "Cleaning up old blue environment..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN: Would cleanup blue environment"
        return 0
    fi

    # Stop blue environment containers
    local blue_compose_file="$PROJECT_ROOT/deployment/${ENVIRONMENT}/docker-compose.blue.yml"
    if [[ -f "$blue_compose_file" ]]; then
        docker-compose -f "$blue_compose_file" down
    fi

    # Clean up unused images
    docker image prune -f

    log_success "Blue environment cleanup completed"
}

# Rollback function
rollback_deployment() {
    log_error "Deployment failed, initiating rollback..."

    local backup_path
    if [[ -f "/tmp/voicehealth_backup_path_${REGION}" ]]; then
        backup_path=$(cat "/tmp/voicehealth_backup_path_${REGION}")
    else
        log_error "Backup path not found, cannot rollback"
        exit 1
    fi

    # Restore from backup
    if ! "$SCRIPT_DIR/restore-from-backup.sh" --region "$REGION" --backup-path "$backup_path"; then
        log_error "Rollback failed"
        exit 1
    fi

    log_success "Rollback completed successfully"
}

# Main deployment function
main() {
    log_info "Starting VoiceHealth AI blue-green deployment"
    log_info "Region: $REGION"
    log_info "Image: $IMAGE"
    log_info "Environment: $ENVIRONMENT"

    # Set up error handling
    trap rollback_deployment ERR

    # Load configuration
    load_regional_config

    # Validate deployment readiness
    validate_deployment_readiness

    # Create backup
    backup_current_deployment

    # Deploy to green environment
    deploy_green_environment

    # Validate green environment
    validate_green_environment

    # Switch traffic to green
    switch_traffic_to_green

    # Cleanup old blue environment
    cleanup_blue_environment

    # Update deployment tracking
    echo "$IMAGE" > "$PROJECT_ROOT/deployment/${ENVIRONMENT}/${REGION}/current_image.txt"
    date > "$PROJECT_ROOT/deployment/${ENVIRONMENT}/${REGION}/last_deployment.txt"

    log_success "Blue-green deployment completed successfully!"
    log_info "New image: $IMAGE"
    log_info "Region: $REGION"
    log_info "Environment: $ENVIRONMENT"
}

# Parse arguments and run main function
parse_args "$@"
main
