import React from 'react';
import Icon from '../../../components/AppIcon';

const OfflineModeIndicator = ({ isOffline, lastSyncTime, emergencyContacts }) => {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div className="flex items-start">
        <div className="bg-red-100 rounded-full p-2 mr-3">
          <Icon name="WifiOff" size={20} className="text-red-600" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Emergency Offline Mode Active
          </h3>
          <p className="text-sm text-red-700 mb-3">
            {isOffline 
              ? 'No internet connection detected. Using cached medical protocols.' :'Offline mode manually activated for emergency situations.'
            }
          </p>
          
          {lastSyncTime && (
            <p className="text-xs text-red-600 mb-3">
              Last protocol update: {lastSyncTime?.toLocaleString()}
            </p>
          )}

          <div className="bg-red-100 rounded-lg p-3">
            <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center">
              <Icon name="Phone" size={16} className="mr-2" />
              Emergency Contacts
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
              {emergencyContacts?.map((contact, index) => (
                <div key={index} className="flex items-center">
                  <span className="font-medium text-red-800">{contact.name}:</span>
                  <span className="ml-1 text-red-700">{contact.number}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfflineModeIndicator;