/**
 * CRITICAL FIXES VALIDATION TEST SUITE
 * 
 * Integration tests to verify that the critical fixes from Phase 1 remediation
 * are working correctly and prevent runtime failures.
 * 
 * Tests cover:
 * - Service method name fixes
 * - Service export/import chain
 * - Database foreign key constraints
 * - Cross-service integration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Test service imports to verify export/import chain
import { 
  aiOrchestrator,
  clinicalDocumentationService,
  advancedRiskStratificationService,
  culturalValidationService,
  authenticationService,
  encryptionService,
  productionMonitoringDashboard,
  securityAuditService
} from '../../services';

// =====================================================
// SERVICE METHOD NAME FIXES VALIDATION
// =====================================================

describe('Critical Fixes Validation - Service Method Names', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should call performRiskAssessment method correctly', async () => {
    // Mock the actual service method
    const mockRiskAssessment = {
      overallRiskScore: 65,
      riskCategory: 'moderate',
      conditionSpecificRisks: [],
      regionalRiskFactors: [],
      modifiableRiskFactors: [],
      nonModifiableRiskFactors: [],
      predictiveAnalytics: {},
      recommendations: [],
      urgentActions: [],
      followUpSchedule: {},
      culturalConsiderations: [],
      assessmentMetadata: {
        assessmentDate: new Date(),
        assessmentVersion: '3.0',
        dataCompleteness: 85,
        confidenceLevel: 0.8,
        limitationsNoted: [],
        dataSourcesUsed: [],
        processingTime: 1500
      }
    };

    vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
      .mockResolvedValue(mockRiskAssessment);

    const mockPatientData = {
      id: 'patient-test-123',
      demographics: {
        age: 45,
        gender: 'female',
        ethnicity: 'akan',
        region: 'Greater Accra',
        country: 'GH'
      },
      symptoms: ['headache', 'fever'],
      medicalHistory: ['hypertension'],
      behavioralFactors: {
        smokingStatus: 'never',
        physicalActivity: 'moderate'
      },
      socioeconomicFactors: {
        incomeLevel: 'middle',
        healthcareAccess: 'moderate'
      },
      environmentalFactors: ['urban_environment']
    };

    const culturalContext = {
      cultureCode: 'akan',
      languagePreference: 'en',
      familyInvolvementLevel: 'high'
    };

    // Test that AI orchestrator can call the correct method
    const result = await aiOrchestrator.performRiskStratification(
      mockPatientData,
      culturalContext
    );

    expect(result.success).toBe(true);
    expect(result.riskAssessment).toBeDefined();
    expect(advancedRiskStratificationService.performRiskAssessment).toHaveBeenCalledWith({
      patientId: mockPatientData.id,
      demographics: mockPatientData.demographics,
      medicalHistory: mockPatientData.medicalHistory,
      currentSymptoms: mockPatientData.symptoms,
      behavioralFactors: mockPatientData.behavioralFactors,
      socioeconomicFactors: mockPatientData.socioeconomicFactors,
      culturalFactors: culturalContext,
      environmentalFactors: mockPatientData.environmentalFactors
    });
  });

  it('should call validateCulturalContent method correctly', async () => {
    // Mock the actual service method
    const mockValidation = {
      overallScore: 88,
      culturalSensitivity: {
        score: 92,
        issues: [],
        recommendations: []
      },
      languageAppropriateness: {
        score: 95,
        issues: [],
        improvements: []
      },
      culturalAccuracy: {
        score: 85,
        inaccuracies: [],
        corrections: []
      },
      biasDetection: {
        biasDetected: false,
        biasTypes: [],
        confidence: 0.95
      },
      recommendations: [],
      flaggedContent: [],
      validationStatus: 'approved',
      processingTime: 800
    };

    vi.spyOn(culturalValidationService, 'validateCulturalContent')
      .mockResolvedValue(mockValidation);

    const testContent = 'Please take your medication as prescribed and follow up with your family doctor.';
    const culturalContext = {
      cultureCode: 'akan',
      languagePreference: 'en',
      familyInvolvementLevel: 'high'
    };

    // Test that AI orchestrator can call the correct method
    const result = await aiOrchestrator.validateCulturalContent(
      testContent,
      culturalContext
    );

    expect(result.success).toBe(true);
    expect(result.validation).toBeDefined();
    expect(culturalValidationService.validateCulturalContent).toHaveBeenCalledWith({
      content: testContent,
      contentType: 'medical_advice',
      targetCulture: culturalContext.cultureCode,
      language: culturalContext.languagePreference,
      context: culturalContext
    });
  });

  it('should not have method not found errors', async () => {
    // Test that the methods exist on the services
    expect(typeof advancedRiskStratificationService.performRiskAssessment).toBe('function');
    expect(typeof culturalValidationService.validateCulturalContent).toBe('function');
    expect(typeof clinicalDocumentationService.generateVoiceToNote).toBe('function');
    
    // Verify the old method names don't exist (should be undefined)
    expect((advancedRiskStratificationService as any).assessRisk).toBeUndefined();
    expect((culturalValidationService as any).validateContent).toBeUndefined();
  });
});

// =====================================================
// SERVICE EXPORT/IMPORT CHAIN VALIDATION
// =====================================================

describe('Critical Fixes Validation - Service Export/Import Chain', () => {
  it('should import all Phase 1 services correctly', () => {
    expect(clinicalDocumentationService).toBeDefined();
    expect(typeof clinicalDocumentationService.generateVoiceToNote).toBe('function');
    
    expect(advancedRiskStratificationService).toBeDefined();
    expect(typeof advancedRiskStratificationService.performRiskAssessment).toBe('function');
    
    expect(culturalValidationService).toBeDefined();
    expect(typeof culturalValidationService.validateCulturalContent).toBe('function');
  });

  it('should import all Phase 2 services correctly', () => {
    expect(authenticationService).toBeDefined();
    expect(typeof authenticationService.authenticate).toBe('function');
    
    expect(encryptionService).toBeDefined();
    expect(typeof encryptionService.encryptPHI).toBe('function');
  });

  it('should import all Phase 3 services correctly', () => {
    expect(productionMonitoringDashboard).toBeDefined();
    expect(typeof productionMonitoringDashboard.getSystemHealthOverview).toBe('function');
    
    expect(securityAuditService).toBeDefined();
    expect(typeof securityAuditService.performComprehensiveAudit).toBe('function');
  });

  it('should import AI orchestrator correctly', () => {
    expect(aiOrchestrator).toBeDefined();
    expect(typeof aiOrchestrator.generateClinicalDocumentation).toBe('function');
    expect(typeof aiOrchestrator.performRiskStratification).toBe('function');
    expect(typeof aiOrchestrator.validateCulturalContent).toBe('function');
  });

  it('should not have circular dependency issues', () => {
    // If we can import all services without errors, there are no circular dependencies
    expect(() => {
      const services = {
        aiOrchestrator,
        clinicalDocumentationService,
        advancedRiskStratificationService,
        culturalValidationService,
        authenticationService,
        encryptionService,
        productionMonitoringDashboard,
        securityAuditService
      };
      
      // Verify all services are objects/functions
      Object.values(services).forEach(service => {
        expect(typeof service).toBe('object');
      });
    }).not.toThrow();
  });
});

// =====================================================
// CROSS-SERVICE INTEGRATION VALIDATION
// =====================================================

describe('Critical Fixes Validation - Cross-Service Integration', () => {
  it('should integrate clinical documentation with AI orchestrator', async () => {
    const mockResult = {
      success: true,
      documentation: {
        clinicalNote: {
          id: 'note-123',
          chiefComplaint: 'Test complaint',
          historyOfPresentIllness: 'Test history'
        },
        culturalAdaptations: [],
        suggestedCodes: { icd10: [], cpt: [] },
        qualityMetrics: {
          completeness: 90,
          accuracy: 85,
          clarity: 88,
          culturalSensitivity: 92,
          complianceScore: 94
        }
      }
    };

    vi.spyOn(clinicalDocumentationService, 'generateVoiceToNote')
      .mockResolvedValue(mockResult);

    const result = await aiOrchestrator.generateClinicalDocumentation(
      'Test transcription',
      'patient-123',
      'provider-456'
    );

    expect(result.success).toBe(true);
    expect(clinicalDocumentationService.generateVoiceToNote).toHaveBeenCalled();
  });

  it('should handle service errors gracefully', async () => {
    vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
      .mockRejectedValue(new Error('Test service error'));

    const mockPatientData = {
      id: 'patient-error-test',
      demographics: { age: 30, gender: 'male' }
    };

    // Should not throw but handle error gracefully
    await expect(
      aiOrchestrator.performRiskStratification(mockPatientData)
    ).rejects.toThrow('Test service error');
  });

  it('should validate service method signatures match orchestrator calls', () => {
    // Verify that the method signatures are compatible
    const riskAssessmentMethod = advancedRiskStratificationService.performRiskAssessment;
    const culturalValidationMethod = culturalValidationService.validateCulturalContent;
    const clinicalDocMethod = clinicalDocumentationService.generateVoiceToNote;

    expect(riskAssessmentMethod).toBeDefined();
    expect(riskAssessmentMethod.length).toBe(1); // Should accept 1 parameter (request object)
    
    expect(culturalValidationMethod).toBeDefined();
    expect(culturalValidationMethod.length).toBe(1); // Should accept 1 parameter (request object)
    
    expect(clinicalDocMethod).toBeDefined();
    expect(clinicalDocMethod.length).toBe(1); // Should accept 1 parameter (request object)
  });
});

// =====================================================
// PERFORMANCE VALIDATION
// =====================================================

describe('Critical Fixes Validation - Performance', () => {
  it('should complete service method calls within acceptable time', async () => {
    const startTime = Date.now();

    // Mock quick responses
    vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
      .mockResolvedValue({} as any);
    vi.spyOn(culturalValidationService, 'validateCulturalContent')
      .mockResolvedValue({} as any);

    const mockPatientData = { id: 'perf-test', demographics: {} };
    const mockContent = 'test content';
    const mockContext = { cultureCode: 'akan' };

    await Promise.all([
      aiOrchestrator.performRiskStratification(mockPatientData),
      aiOrchestrator.validateCulturalContent(mockContent, mockContext)
    ]);

    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // Should complete within reasonable time (allowing for mocking overhead)
    expect(totalTime).toBeLessThan(1000); // 1 second
  });

  it('should handle concurrent service calls', async () => {
    vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
      .mockResolvedValue({} as any);

    const mockPatientData = { id: 'concurrent-test', demographics: {} };

    // Make multiple concurrent calls
    const promises = Array.from({ length: 5 }, (_, i) =>
      aiOrchestrator.performRiskStratification({
        ...mockPatientData,
        id: `concurrent-test-${i}`
      })
    );

    // Should handle concurrent calls without errors
    await expect(Promise.all(promises)).resolves.toBeDefined();
  });
});

// =====================================================
// ERROR HANDLING VALIDATION
// =====================================================

describe('Critical Fixes Validation - Error Handling', () => {
  it('should handle missing service dependencies gracefully', () => {
    // Test that services can be instantiated even if some dependencies are missing
    expect(() => {
      const testOrchestrator = aiOrchestrator;
      expect(testOrchestrator).toBeDefined();
    }).not.toThrow();
  });

  it('should provide meaningful error messages for method failures', async () => {
    const errorMessage = 'Specific test error for validation';
    vi.spyOn(advancedRiskStratificationService, 'performRiskAssessment')
      .mockRejectedValue(new Error(errorMessage));

    try {
      await aiOrchestrator.performRiskStratification({ id: 'error-test' });
      fail('Should have thrown an error');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect((error as Error).message).toBe(errorMessage);
    }
  });
});
