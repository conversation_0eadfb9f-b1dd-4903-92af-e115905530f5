import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import ErrorBoundary from './components/ErrorBoundary';
import FloatingLogoutButton from './components/ui/FloatingLogoutButton';
// Add your imports here
import LandingPage from "./pages/marketing/LandingPage";
import PricingPage from "./pages/marketing/PricingPage";
import ContactPage from "./pages/marketing/ContactPage";
import AuthenticationDemoAccess from "./pages/authentication-demo-access";
import WelcomeLanguageSelection from "./pages/welcome-language-selection";
import CountryRegionalSelection from "./pages/country-regional-selection";
import HealthInterestsPriorities from "./pages/health-interests-priorities";
import AgentCustomizationHub from "./pages/agent-customization-hub";
import RealTimeMultiAgentCollaboration from "./pages/real-time-multi-agent-collaboration";
import SessionDashboardHistory from "./pages/session-dashboard-history";
import PatientProfileSetup from "./pages/patient-profile-setup";
import VoiceConsultationInterface from "./pages/voice-consultation-interface";
import EnhancedVoiceConsultationInterface from "./pages/enhanced-voice-consultation-interface";
import PaymentPlans from "./pages/payment-plans";
import PaymentSuccess from "./pages/payment-success";
import PaymentFailed from "./pages/payment-failed";
import AdminDashboard from "./pages/admin-dashboard";
import AnalyticsInsightsDashboard from "./pages/analytics-insights-dashboard";
import MultiLanguageAccessibilitySettings from "./pages/multi-language-accessibility-settings";
import OfflineModeSync from "./pages/offline-mode-sync-management";
import HealthDeviceIntegrationHub from "./pages/health-device-integration-hub";
import EmergencyOfflineConsultation from "./pages/emergency-offline-consultation";
import SpeechProcessingAIEngineControlCenter from "./pages/speech-processing-ai-engine-control-center";
import AgentNetworkManagement from "./pages/agent-network-management";
import AISpecialistNetworkManagement from "./pages/ai-specialist-network-management";
import RegionalHealthContextLocalizationHub from "./pages/regional-health-context-localization-hub";
import EnhancedPricingConsultationCostCalculator from "./pages/enhanced-pricing-consultation-cost-calculator";
import MedicalKnowledgeEvidenceBaseManagement from "./pages/medical-knowledge-evidence-base-management";
import AIAgentPromptEngineeringContextManagement from "./pages/ai-agent-prompt-engineering-context-management";
import IntelligentTriageEmergencyEscalationSystem from "./pages/intelligent-triage-emergency-escalation-system";
import PWATestPage from "./components/PWATestPage";
import NotFound from "./pages/NotFound";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your routes here */}
        <Route path="/" element={<LandingPage />} />
        <Route path="/authentication-demo-access" element={<AuthenticationDemoAccess />} />
        <Route path="/pricing" element={<PricingPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/welcome-language-selection" element={<WelcomeLanguageSelection />} />
        <Route path="/country-regional-selection" element={<CountryRegionalSelection />} />
        <Route path="/health-interests-priorities" element={<HealthInterestsPriorities />} />
        <Route path="/agent-customization-hub" element={<AgentCustomizationHub />} />
        <Route path="/real-time-multi-agent-collaboration" element={<RealTimeMultiAgentCollaboration />} />
        <Route path="/session-dashboard-history" element={<SessionDashboardHistory />} />
        <Route path="/patient-profile-setup" element={<PatientProfileSetup />} />
        <Route path="/voice-consultation-interface" element={<VoiceConsultationInterface />} />
        <Route path="/enhanced-voice-consultation-interface" element={<EnhancedVoiceConsultationInterface />} />
        <Route path="/payment-plans" element={<PaymentPlans />} />
        <Route path="/payment-success" element={<PaymentSuccess />} />
        <Route path="/payment-failed" element={<PaymentFailed />} />
        <Route path="/admin-dashboard" element={<AdminDashboard />} />
        <Route path="/analytics-insights-dashboard" element={<AnalyticsInsightsDashboard />} />
        <Route path="/multi-language-accessibility-settings" element={<MultiLanguageAccessibilitySettings />} />
        <Route path="/offline-mode-sync-management" element={<OfflineModeSync />} />
        <Route path="/health-device-integration-hub" element={<HealthDeviceIntegrationHub />} />
        <Route path="/emergency-offline-consultation" element={<EmergencyOfflineConsultation />} />
        <Route path="/speech-processing-ai-engine-control-center" element={<SpeechProcessingAIEngineControlCenter />} />
        <Route path="/agent-network-management" element={<AgentNetworkManagement />} />
        <Route path="/ai-specialist-network-management" element={<AISpecialistNetworkManagement />} />
        <Route path="/regional-health-context-localization-hub" element={<RegionalHealthContextLocalizationHub />} />
        <Route path="/enhanced-pricing-consultation-cost-calculator" element={<EnhancedPricingConsultationCostCalculator />} />
        <Route path="/medical-knowledge-evidence-base-management" element={<MedicalKnowledgeEvidenceBaseManagement />} />
        <Route path="/ai-agent-prompt-engineering-context-management" element={<AIAgentPromptEngineeringContextManagement />} />
        <Route path="/intelligent-triage-emergency-escalation-system" element={<IntelligentTriageEmergencyEscalationSystem />} />
        <Route path="/pwa-testing" element={<PWATestPage />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      
      {/* Global UI Components - Inside Router Context */}
      <FloatingLogoutButton />
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;