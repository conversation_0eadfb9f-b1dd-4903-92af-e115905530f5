import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import DataVisualizationPanel from './components/DataVisualizationPanel';
import AdvancedFilterControls from './components/AdvancedFilterControls';
import CustomReportBuilder from './components/CustomReportBuilder';
import RealTimeMonitoringWidget from './components/RealTimeMonitoringWidget';

const AnalyticsInsightsDashboard = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('behavior');
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({});
  const [analyticsData, setAnalyticsData] = useState({});

  const analyticsCategories = [
    { id: 'behavior', label: 'User Behavior', icon: 'Users', description: 'User engagement and interaction patterns' },
    { id: 'medical', label: 'Medical Insights', icon: 'Heart', description: 'Health outcomes and medical trends' },
    { id: 'performance', label: 'System Performance', icon: 'Zap', description: 'Technical metrics and system health' },
    { id: 'business', label: 'Business Metrics', icon: 'TrendingUp', description: 'Revenue, growth, and business KPIs' }
  ];

  // Mock analytics data
  const mockAnalyticsData = {
    behavior: {
      title: 'User Behavior Analytics',
      kpis: [
        { label: 'Active Users', value: '12,543', change: '****%', trend: 'up' },
        { label: 'Session Duration', value: '12.5 min', change: '**** min', trend: 'up' },
        { label: 'Return Rate', value: '78%', change: '****%', trend: 'up' },
        { label: 'Feature Adoption', value: '65%', change: '+12%', trend: 'up' }
      ],
      insights: [
        'Peak usage hours: 2-4 PM and 7-9 PM daily',
        'Mobile users show 35% higher engagement',
        'Profile completion drives 45% better retention',
        'Weekend consultations increased by 23%'
      ]
    },
    medical: {
      title: 'Medical Insights',
      kpis: [
        { label: 'Consultations', value: '1,847', change: '+15.2%', trend: 'up' },
        { label: 'Satisfaction Score', value: '4.8/5', change: '+0.2', trend: 'up' },
        { label: 'Resolution Rate', value: '94%', change: '+3%', trend: 'up' },
        { label: 'Follow-up Rate', value: '67%', change: '+8%', trend: 'up' }
      ],
      insights: [
        'Cardiology leads with 34% of consultations',
        'Mental health consultations up 67% this quarter',
        'Average diagnosis accuracy: 92%',
        'Patient satisfaction highest for nutrition consultations'
      ]
    },
    performance: {
      title: 'System Performance',
      kpis: [
        { label: 'Uptime', value: '99.9%', change: '+0.1%', trend: 'up' },
        { label: 'Response Time', value: '145ms', change: '-15ms', trend: 'down' },
        { label: 'Error Rate', value: '0.02%', change: '-0.05%', trend: 'down' },
        { label: 'Throughput', value: '2.3MB/s', change: '+0.4MB/s', trend: 'up' }
      ],
      insights: [
        'Voice processing improved by 23%',
        'Peak performance during 10-11 AM',
        'Cloud infrastructure scaling efficiently',
        'API response times consistently under 200ms'
      ]
    },
    business: {
      title: 'Business Metrics',
      kpis: [
        { label: 'Revenue', value: '$127K', change: '+18.5%', trend: 'up' },
        { label: 'New Subscriptions', value: '234', change: '+28%', trend: 'up' },
        { label: 'Churn Rate', value: '2.1%', change: '-0.8%', trend: 'down' },
        { label: 'LTV', value: '$890', change: '+12%', trend: 'up' }
      ],
      insights: [
        'Premium subscriptions growing 34% month-over-month',
        'Customer acquisition cost decreased by 15%',
        'Highest conversion rate from mobile users',
        'Referral program driving 23% of new signups'
      ]
    }
  };

  useEffect(() => {
    // Simulate loading
    const loadAnalytics = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAnalyticsData(mockAnalyticsData);
      setIsLoading(false);
    };

    loadAnalytics();
  }, []);

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    console.log('Filters updated:', newFilters);
  };

  const handleGenerateReport = (reportConfig) => {
    console.log('Generating report:', reportConfig);
    // Simulate report generation
    alert(`Report "${reportConfig.name}" is being generated. You'll receive an email when it's ready.`);
  };

  const currentCategoryData = analyticsData[activeTab] || mockAnalyticsData.behavior;

  const renderKPICards = (kpis) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {kpis?.map((kpi, index) => (
          <div
            key={index}
            className="bg-surface rounded-lg border border-border shadow-minimal p-4 hover:shadow-small transition-all"
          >
            <p className="text-sm font-medium text-text-secondary mb-2">{kpi.label}</p>
            <p className="text-2xl font-bold text-text-primary mb-2">{kpi.value}</p>
            <div className="flex items-center">
              <Icon
                name={kpi.trend === 'up' ? 'TrendingUp' : kpi.trend === 'down' ? 'TrendingDown' : 'Minus'}
                size={16}
                className={`mr-1 ${
                  kpi.trend === 'up' ? 'text-success-600' : 
                  kpi.trend === 'down'? 'text-success-600' : 'text-text-secondary'
                }`}
              />
              <span className="text-sm font-medium text-success-600">{kpi.change}</span>
              <span className="text-text-secondary text-sm ml-1">vs last month</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderInsights = (insights) => {
    return (
      <div className="bg-secondary-50 rounded-lg p-4 mb-6">
        <h4 className="text-sm font-medium text-text-primary mb-3 flex items-center">
          <Icon name="Lightbulb" size={16} className="mr-2 text-warning-500" />
          Key Insights
        </h4>
        <ul className="space-y-2">
          {insights?.map((insight, index) => (
            <li key={index} className="flex items-start">
              <Icon name="ArrowRight" size={14} className="text-primary-500 mr-2 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-text-secondary">{insight}</span>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-3xl font-bold text-text-primary font-heading">
              Analytics & Insights Dashboard
            </h1>
            <p className="text-text-secondary mt-2">
              Comprehensive analytics and business intelligence for VoiceHealth AI platform
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/admin-dashboard')}
              iconName="Shield"
            >
              Admin Dashboard
            </Button>
            <Button
              variant="primary"
              iconName="FileBarChart"
              onClick={() => document.getElementById('reports-section')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Generate Report
            </Button>
          </div>
        </div>

        {/* Category Navigation */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {analyticsCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveTab(category.id)}
                className={`flex items-center px-4 py-3 rounded-lg border transition-all ${
                  activeTab === category.id
                    ? 'bg-primary-100 border-primary-200 text-primary-700' :'bg-surface border-border text-text-secondary hover:text-text-primary hover:bg-secondary-50'
                }`}
              >
                <Icon name={category.icon} size={18} className="mr-2" />
                <div className="text-left">
                  <div className="font-medium">{category.label}</div>
                  <div className="text-xs opacity-75">{category.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
                <Icon name="BarChart3" size={24} color="var(--primary-500)" />
              </div>
              <p className="text-text-secondary">Loading analytics data...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Current Category Analytics */}
            <div>
              <h2 className="text-xl font-semibold text-text-primary font-heading mb-6">
                {currentCategoryData.title}
              </h2>
              
              {/* KPI Cards */}
              {renderKPICards(currentCategoryData.kpis)}
              
              {/* Insights */}
              {renderInsights(currentCategoryData.insights)}
            </div>

            {/* Advanced Filters */}
            <AdvancedFilterControls
              onFiltersChange={handleFiltersChange}
              initialFilters={filters}
            />

            {/* Data Visualization */}
            <DataVisualizationPanel data={analyticsData[activeTab]} />

            {/* Real-time Monitoring */}
            <RealTimeMonitoringWidget onAlertTriggered={(alert) => console.log('Alert:', alert)} />

            {/* Custom Report Builder */}
            <div id="reports-section">
              <CustomReportBuilder onGenerateReport={handleGenerateReport} />
            </div>

            {/* Export & Share Options */}
            <div className="bg-surface rounded-lg border border-border shadow-minimal p-6">
              <h3 className="text-lg font-semibold text-text-primary font-heading mb-4">
                Export & Share
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  fullWidth
                  iconName="Download"
                  onClick={() => console.log('Exporting dashboard data')}
                >
                  Export Dashboard
                </Button>
                <Button
                  variant="outline"
                  fullWidth
                  iconName="Share"
                  onClick={() => console.log('Sharing dashboard')}
                >
                  Share Dashboard
                </Button>
                <Button
                  variant="outline"
                  fullWidth
                  iconName="Calendar"
                  onClick={() => console.log('Scheduling reports')}
                >
                  Schedule Reports
                </Button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default AnalyticsInsightsDashboard;