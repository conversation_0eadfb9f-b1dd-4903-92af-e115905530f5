/**
 * COMPREHENSIVE UNIT TESTS FOR ENHANCED MEDICAL DATA SERVICE
 * 
 * This test suite provides comprehensive coverage for the enhanced medical data service
 * with focus on:
 * - HIPAA compliance and audit logging
 * - Type safety and data validation
 * - Offline/online synchronization
 * - Encryption for sensitive data
 * - Emergency access protocols
 * - Error handling and recovery
 * - Performance optimization
 * 
 * PATIENT SAFETY REQUIREMENTS:
 * - All medical data operations must be tested for integrity
 * - Emergency protocols must be thoroughly validated
 * - Audit logging must be verified for all operations
 * - Encryption must be tested for all sensitive data
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedMedicalDataService } from '../../utils/enhancedMedicalDataService';

// Mock dependencies
vi.mock('../../utils/supabaseClient', () => ({
  supabase: global.testHelpers.createMockSupabaseClient()
}));

vi.mock('../../utils/pwaService', () => ({
  default: {
    isOnline: vi.fn(() => true),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  }
}));

vi.mock('../../utils/offlineHealthRecordsService', () => ({
  default: {
    storeMedicalCondition: vi.fn(() => Promise.resolve({ success: true })),
    getMedicalConditions: vi.fn(() => Promise.resolve({ 
      success: true, 
      data: [global.mockMedicalData.condition] 
    })),
    storeMedication: vi.fn(() => Promise.resolve({ success: true })),
    getMedications: vi.fn(() => Promise.resolve({ 
      success: true, 
      data: [global.mockMedicalData.medication] 
    })),
    storeSymptom: vi.fn(() => Promise.resolve({ success: true })),
    getSymptoms: vi.fn(() => Promise.resolve({ 
      success: true, 
      data: [global.mockMedicalData.symptom] 
    }))
  }
}));

vi.mock('../../utils/medicalDataValidator', () => ({
  default: {
    validateCondition: vi.fn(() => ({ valid: true, sanitized: 'Hypertension' })),
    validateMedication: vi.fn(() => ({ valid: true, sanitized: 'Lisinopril' })),
    validateSymptom: vi.fn(() => ({ valid: true, sanitized: 'Headache' })),
    validateMedicalNotes: vi.fn(() => ({ valid: true, sanitized: 'Test notes' }))
  }
}));

vi.mock('../../utils/auditLogger', () => ({
  default: global.testHelpers.createMockAuditLogger()
}));

vi.mock('../../utils/encryptionService', () => ({
  default: global.testHelpers.createMockEncryptionService()
}));

describe('EnhancedMedicalDataService', () => {
  let service;
  let mockSupabase;
  let mockAuditLogger;
  let mockEncryptionService;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Create fresh service instance
    service = new EnhancedMedicalDataService();
    
    // Get mock references
    mockSupabase = global.testHelpers.createMockSupabaseClient();
    mockAuditLogger = global.testHelpers.createMockAuditLogger();
    mockEncryptionService = global.testHelpers.createMockEncryptionService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Medical Conditions Management', () => {
    describe('getUserConditions', () => {
      it('should successfully retrieve user conditions with audit logging', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const expectedConditions = [global.mockMedicalData.condition];
        
        mockSupabase.from().select().eq().then.mockResolvedValue({
          data: expectedConditions,
          error: null
        });

        // Act
        const result = await service.getUserConditions(userId);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toEqual(expectedConditions);
        expect(result.source).toBe('online');
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'read',
          `user_${userId}_conditions`,
          true,
          expect.objectContaining({
            operation: 'get_user_conditions',
            user_id: userId,
            source: 'online_attempt'
          })
        );
      });

      it('should fallback to offline data when online fails', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const offlineConditions = [global.mockMedicalData.condition];
        
        mockSupabase.from().select().eq().then.mockRejectedValue(new Error('Network error'));

        // Act
        const result = await service.getUserConditions(userId);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toEqual(offlineConditions);
        expect(result.source).toBe('offline');
        expect(result.message).toContain('offline data');
      });

      it('should handle emergency conditions with priority', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const emergencyCondition = global.mockEmergencyData.condition;
        
        mockSupabase.from().select().eq().then.mockResolvedValue({
          data: [emergencyCondition],
          error: null
        });

        // Act
        const result = await service.getUserConditions(userId);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data[0].is_emergency).toBe(true);
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'read',
          `user_${userId}_conditions`,
          true,
          expect.objectContaining({
            emergency_data: true
          })
        );
      });

      it('should validate user ID parameter', async () => {
        // Act
        const result = await service.getUserConditions(null);

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('User ID is required');
      });

      it('should handle encryption for sensitive condition data', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const sensitiveCondition = {
          ...global.mockMedicalData.condition,
          notes: 'Sensitive medical information'
        };
        
        mockSupabase.from().select().eq().then.mockResolvedValue({
          data: [sensitiveCondition],
          error: null
        });

        // Act
        const result = await service.getUserConditions(userId);

        // Assert
        expect(result.success).toBe(true);
        expect(mockEncryptionService.encryptMedicalData).toHaveBeenCalled();
      });
    });

    describe('addCondition', () => {
      it('should successfully add a new condition with validation', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const conditionData = {
          name: 'Diabetes',
          severity: 'moderate',
          diagnosed_date: '2024-01-01',
          notes: 'Type 2 diabetes'
        };
        
        mockSupabase.from().insert().select().single().mockResolvedValue({
          data: { ...conditionData, id: 'new-condition-id', user_id: userId },
          error: null
        });

        // Act
        const result = await service.addCondition(userId, conditionData);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data.name).toBe(conditionData.name);
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'create',
          expect.any(String),
          true,
          expect.objectContaining({
            operation: 'add_condition',
            condition_name: conditionData.name
          })
        );
      });

      it('should validate condition data before insertion', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const invalidConditionData = {
          name: '', // Invalid empty name
          severity: 'invalid-severity'
        };

        // Act
        const result = await service.addCondition(userId, invalidConditionData);

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('validation');
      });

      it('should handle emergency condition addition with priority', async () => {
        // Arrange
        const userId = global.mockUser.id;
        const emergencyCondition = {
          name: 'Chest Pain',
          severity: 'critical',
          is_emergency: true,
          notes: 'Acute chest pain'
        };
        
        mockSupabase.from().insert().select().single().mockResolvedValue({
          data: { ...emergencyCondition, id: 'emergency-condition-id', user_id: userId },
          error: null
        });

        // Act
        const result = await service.addCondition(userId, emergencyCondition);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data.is_emergency).toBe(true);
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'create',
          expect.any(String),
          true,
          expect.objectContaining({
            emergency_condition: true,
            priority: 'critical'
          })
        );
      });
    });

    describe('updateCondition', () => {
      it('should successfully update condition with audit trail', async () => {
        // Arrange
        const conditionId = global.mockMedicalData.condition.id;
        const updates = {
          severity: 'severe',
          notes: 'Updated notes'
        };
        
        mockSupabase.from().update().eq().select().single().mockResolvedValue({
          data: { ...global.mockMedicalData.condition, ...updates },
          error: null
        });

        // Act
        const result = await service.updateCondition(conditionId, updates);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data.severity).toBe(updates.severity);
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'update',
          conditionId,
          true,
          expect.objectContaining({
            operation: 'update_condition',
            changes: updates
          })
        );
      });

      it('should validate condition ID parameter', async () => {
        // Act
        const result = await service.updateCondition(null, { severity: 'mild' });

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('Condition ID is required');
      });
    });

    describe('deleteCondition', () => {
      it('should successfully delete condition with audit logging', async () => {
        // Arrange
        const conditionId = global.mockMedicalData.condition.id;
        
        mockSupabase.from().delete().eq().mockResolvedValue({
          data: null,
          error: null
        });

        // Act
        const result = await service.deleteCondition(conditionId);

        // Assert
        expect(result.success).toBe(true);
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'delete',
          conditionId,
          true,
          expect.objectContaining({
            operation: 'delete_condition'
          })
        );
      });

      it('should prevent deletion of emergency conditions without proper authorization', async () => {
        // Arrange
        const emergencyConditionId = global.mockEmergencyData.condition.id;

        // Act
        const result = await service.deleteCondition(emergencyConditionId, { isEmergency: true });

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('emergency condition');
        expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
          'delete_attempt',
          emergencyConditionId,
          false,
          expect.objectContaining({
            reason: 'unauthorized_emergency_deletion'
          })
        );
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle network errors gracefully', async () => {
      // Arrange
      const userId = global.mockUser.id;
      mockSupabase.from().select().eq().then.mockRejectedValue(new Error('Network timeout'));

      // Act
      const result = await service.getUserConditions(userId);

      // Assert
      expect(result.success).toBe(true); // Should fallback to offline
      expect(result.source).toBe('offline');
      expect(result.message).toContain('offline data');
    });

    it('should handle database errors with proper logging', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const dbError = new Error('Database connection failed');
      mockSupabase.from().select().eq().then.mockRejectedValue(dbError);

      // Act
      const result = await service.getUserConditions(userId);

      // Assert
      expect(mockAuditLogger.logConditionAccess).toHaveBeenCalledWith(
        'read',
        `user_${userId}_conditions`,
        false,
        expect.objectContaining({
          error: dbError.message
        })
      );
    });

    it('should implement retry logic for transient failures', async () => {
      // Arrange
      const userId = global.mockUser.id;
      let callCount = 0;
      
      mockSupabase.from().select().eq().then.mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          return Promise.reject(new Error('Transient error'));
        }
        return Promise.resolve({
          data: [global.mockMedicalData.condition],
          error: null
        });
      });

      // Act
      const result = await service.getUserConditions(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(callCount).toBe(3); // Should retry twice before success
    });
  });

  describe('Performance and Optimization', () => {
    it('should cache frequently accessed medical data', async () => {
      // Arrange
      const userId = global.mockUser.id;
      
      mockSupabase.from().select().eq().then.mockResolvedValue({
        data: [global.mockMedicalData.condition],
        error: null
      });

      // Act - First call
      await service.getUserConditions(userId);
      // Act - Second call (should use cache)
      const result = await service.getUserConditions(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.cached).toBe(true);
    });

    it('should prioritize emergency data in performance optimization', async () => {
      // Arrange
      const userId = global.mockUser.id;
      const emergencyCondition = global.mockEmergencyData.condition;
      
      mockSupabase.from().select().eq().then.mockResolvedValue({
        data: [emergencyCondition, global.mockMedicalData.condition],
        error: null
      });

      // Act
      const result = await service.getUserConditions(userId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data[0].is_emergency).toBe(true); // Emergency data should be first
    });
  });
});
