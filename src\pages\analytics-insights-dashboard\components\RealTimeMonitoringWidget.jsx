import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RealTimeMonitoringWidget = ({ onAlertTriggered }) => {
  const [realTimeData, setRealTimeData] = useState({
    activeSessions: 0,
    concurrentUsers: 0,
    systemLoad: 0,
    responseTime: 0,
    errorRate: 0,
    lastUpdated: new Date()
  });

  const [isLive, setIsLive] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds

  // Mock real-time data simulation
  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        activeSessions: Math.max(0, prev.activeSessions + Math.floor(Math.random() * 10 - 5)),
        concurrentUsers: Math.max(0, prev.concurrentUsers + Math.floor(Math.random() * 20 - 10)),
        systemLoad: Math.max(0, Math.min(100, prev.systemLoad + Math.floor(Math.random() * 10 - 5))),
        responseTime: Math.max(50, prev.responseTime + Math.floor(Math.random() * 40 - 20)),
        errorRate: Math.max(0, Math.min(5, prev.errorRate + (Math.random() * 0.2 - 0.1))),
        lastUpdated: new Date()
      }));
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [isLive, refreshInterval]);

  // Initial data load
  useEffect(() => {
    setRealTimeData({
      activeSessions: 234,
      concurrentUsers: 1847,
      systemLoad: 67,
      responseTime: 145,
      errorRate: 0.02,
      lastUpdated: new Date()
    });
  }, []);

  const metrics = [
    {
      id: 'sessions',
      label: 'Active Sessions',
      value: realTimeData.activeSessions,
      unit: '',
      icon: 'Activity',
      color: 'text-primary-500',
      bgColor: 'bg-primary-100',
      change: '+12',
      trend: 'up'
    },
    {
      id: 'users',
      label: 'Concurrent Users',
      value: realTimeData.concurrentUsers.toLocaleString(),
      unit: '',
      icon: 'Users',
      color: 'text-success-500',
      bgColor: 'bg-success-100',
      change: '+89',
      trend: 'up'
    },
    {
      id: 'load',
      label: 'System Load',
      value: realTimeData.systemLoad,
      unit: '%',
      icon: 'Cpu',
      color: realTimeData.systemLoad > 80 ? 'text-error-500' : realTimeData.systemLoad > 60 ? 'text-warning-500' : 'text-success-500',
      bgColor: realTimeData.systemLoad > 80 ? 'bg-error-100' : realTimeData.systemLoad > 60 ? 'bg-warning-100' : 'bg-success-100',
      change: realTimeData.systemLoad > 80 ? 'High' : realTimeData.systemLoad > 60 ? 'Medium' : 'Normal',
      trend: realTimeData.systemLoad > 80 ? 'warning' : 'stable'
    },
    {
      id: 'response',
      label: 'Response Time',
      value: realTimeData.responseTime,
      unit: 'ms',
      icon: 'Zap',
      color: realTimeData.responseTime > 300 ? 'text-error-500' : realTimeData.responseTime > 200 ? 'text-warning-500' : 'text-success-500',
      bgColor: realTimeData.responseTime > 300 ? 'bg-error-100' : realTimeData.responseTime > 200 ? 'bg-warning-100' : 'bg-success-100',
      change: realTimeData.responseTime > 300 ? 'Slow' : realTimeData.responseTime > 200 ? 'Fair' : 'Fast',
      trend: realTimeData.responseTime > 300 ? 'warning' : 'stable'
    }
  ];

  const alerts = [
    {
      id: 1,
      level: 'info',
      message: 'Peak usage detected - scaling resources',
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      resolved: false
    },
    {
      id: 2,
      level: 'warning',
      message: 'Response time above 200ms threshold',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      resolved: false
    }
  ];

  const getAlertIcon = (level) => {
    switch (level) {
      case 'critical': return 'AlertTriangle';
      case 'warning': return 'AlertCircle';
      case 'info': return 'Info';
      default: return 'Bell';
    }
  };

  const getAlertColor = (level) => {
    switch (level) {
      case 'critical': return 'text-error-600 bg-error-50 border-error-200';
      case 'warning': return 'text-warning-600 bg-warning-50 border-warning-200';
      case 'info': return 'text-primary-600 bg-primary-50 border-primary-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatTimeAgo = (date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Real-Time Monitoring
            </h3>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-success-500 animate-pulse' : 'bg-gray-400'}`} />
              <span className="text-sm text-text-secondary">
                {isLive ? 'Live' : 'Paused'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="px-3 py-1 border border-border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value={1000}>1s</option>
              <option value={5000}>5s</option>
              <option value={10000}>10s</option>
              <option value={30000}>30s</option>
            </select>
            
            <Button
              variant={isLive ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setIsLive(!isLive)}
              iconName={isLive ? 'Pause' : 'Play'}
            >
              {isLive ? 'Pause' : 'Resume'}
            </Button>
          </div>
        </div>
        
        <p className="text-text-secondary text-sm mt-2">
          Last updated: {realTimeData.lastUpdated.toLocaleTimeString()}
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {metrics.map((metric) => (
            <div
              key={metric.id}
              className="bg-secondary-50 rounded-lg p-4 border border-border"
            >
              <div className="flex items-center justify-between mb-2">
                <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                  <Icon name={metric.icon} size={20} className={metric.color} />
                </div>
                <div className="text-right">
                  <p className="text-xs text-text-secondary font-medium">{metric.label}</p>
                  <p className={`text-lg font-bold ${metric.color}`}>
                    {metric.value}{metric.unit}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-text-secondary">{metric.change}</span>
                <Icon
                  name={metric.trend === 'up' ? 'TrendingUp' : metric.trend === 'warning' ? 'AlertTriangle' : 'Minus'}
                  size={14}
                  className={
                    metric.trend === 'up' ? 'text-success-500' :
                    metric.trend === 'warning'? 'text-warning-500' : 'text-text-secondary'
                  }
                />
              </div>
            </div>
          ))}
        </div>

        {/* System Health Indicators */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-text-primary mb-3">System Health</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">API Health</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-success-500 rounded-full" />
                <span className="text-sm text-success-600 font-medium">Healthy</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Database</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-success-500 rounded-full" />
                <span className="text-sm text-success-600 font-medium">Connected</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Voice Services</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-warning-500 rounded-full" />
                <span className="text-sm text-warning-600 font-medium">Degraded</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Storage</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-success-500 rounded-full" />
                <span className="text-sm text-success-600 font-medium">Available</span>
              </div>
            </div>
          </div>
        </div>

        {/* Real-time Alerts */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-text-primary">Recent Alerts</h4>
            <Button variant="ghost" size="sm" iconName="Bell">
              View All
            </Button>
          </div>
          
          <div className="space-y-2">
            {alerts.map((alert) => (
              <div
                key={alert.id}
                className={`border rounded-lg p-3 ${getAlertColor(alert.level)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-2">
                    <Icon name={getAlertIcon(alert.level)} size={16} className="mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">{alert.message}</p>
                      <p className="text-xs opacity-75">{formatTimeAgo(alert.timestamp)}</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" iconName="X" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Predictive Scaling */}
        <div className="mt-6 bg-primary-50 rounded-lg p-4 border border-primary-200">
          <div className="flex items-center gap-2 mb-2">
            <Icon name="TrendingUp" size={16} className="text-primary-600" />
            <h4 className="text-sm font-medium text-primary-800">Predictive Scaling</h4>
          </div>
          <p className="text-sm text-primary-700 mb-3">
            Based on current trends, we recommend scaling resources by 15% in the next 2 hours.
          </p>
          <div className="flex gap-2">
            <Button variant="primary" size="sm">
              Auto-scale Now
            </Button>
            <Button variant="outline" size="sm">
              View Recommendations
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeMonitoringWidget;