-- Missing Database Tables - Phase 1
-- VoiceHealth AI - Critical Gap Resolution
-- Created: 2025-01-06

-- =====================================================
-- MEDICAL TERMINOLOGY TRANSLATIONS
-- =====================================================

-- Medical terminology translations for multi-language support
CREATE TABLE IF NOT EXISTS medical_terminology_translations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    english_term TEXT NOT NULL,
    translated_term TEXT NOT NULL,
    target_language VARCHAR(10) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('symptom', 'diagnosis', 'medication', 'procedure', 'anatomy', 'general')),
    cultural_context VARCHAR(50),
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    verified_by UUID,
    verification_date TIMESTAMP WITH TIME ZONE,
    usage_frequency INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(english_term, target_language, cultural_context)
);

-- =====================================================
-- CULTURAL FOCUS GROUPS
-- =====================================================

-- Cultural focus groups for validation and feedback
CREATE TABLE IF NOT EXISTS cultural_focus_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_data JSONB NOT NULL,
    region VARCHAR(100) NOT NULL,
    country VARCHAR(10) NOT NULL,
    cultural_group VARCHAR(100) NOT NULL,
    session_date TIMESTAMP WITH TIME ZONE NOT NULL,
    facilitator VARCHAR(200),
    participant_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled')),
    overall_satisfaction DECIMAL(3,2) CHECK (overall_satisfaction >= 0 AND overall_satisfaction <= 5),
    cultural_appropriateness DECIMAL(3,2) CHECK (cultural_appropriateness >= 0 AND cultural_appropriateness <= 5),
    feedback_summary TEXT,
    recommendations TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- REGIONAL DEPLOYMENTS
-- =====================================================

-- Regional deployment tracking and management
CREATE TABLE IF NOT EXISTS regional_deployments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    country VARCHAR(10) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    region VARCHAR(100) NOT NULL,
    deployment_phase VARCHAR(20) NOT NULL CHECK (deployment_phase IN ('planning', 'pilot', 'limited_rollout', 'full_deployment', 'optimization')),
    status JSONB NOT NULL,
    configuration JSONB NOT NULL,
    compliance JSONB,
    customizations JSONB DEFAULT '[]'::jsonb,
    partnerships JSONB DEFAULT '[]'::jsonb,
    timeline JSONB,
    risk_assessment JSONB,
    performance_metrics JSONB,
    rollback_plan JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(country, region)
);

-- =====================================================
-- PERFORMANCE METRICS
-- =====================================================

-- System performance metrics collection
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    component VARCHAR(100) NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('response_time', 'throughput', 'error_rate', 'cpu_usage', 'memory_usage', 'disk_usage', 'network')),
    metric_value DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    region VARCHAR(10),
    environment VARCHAR(20) DEFAULT 'production',
    additional_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SYSTEM HEALTH CHECKS
-- =====================================================

-- System health check results
CREATE TABLE IF NOT EXISTS system_health_checks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    component VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('healthy', 'warning', 'critical', 'unknown')),
    response_time INTEGER, -- milliseconds
    error_rate DECIMAL(5,2), -- percentage
    last_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    dependencies TEXT[],
    metrics JSONB,
    error_details TEXT,
    region VARCHAR(10),
    environment VARCHAR(20) DEFAULT 'production',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- DEPLOYMENT CONFIGURATIONS
-- =====================================================

-- Environment and deployment configurations
CREATE TABLE IF NOT EXISTS deployment_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    environment VARCHAR(20) NOT NULL CHECK (environment IN ('development', 'staging', 'production')),
    region VARCHAR(10) NOT NULL,
    configuration_type VARCHAR(50) NOT NULL CHECK (configuration_type IN ('application', 'database', 'infrastructure', 'security', 'monitoring')),
    configuration_data JSONB NOT NULL,
    version VARCHAR(20) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(environment, region, configuration_type, version)
);

-- =====================================================
-- MONITORING DASHBOARDS
-- =====================================================

-- Monitoring dashboard configurations
CREATE TABLE IF NOT EXISTS monitoring_dashboards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    dashboard_type VARCHAR(20) NOT NULL CHECK (dashboard_type IN ('executive', 'operational', 'clinical', 'technical', 'regional')),
    widgets JSONB NOT NULL DEFAULT '[]'::jsonb,
    layout JSONB NOT NULL,
    permissions JSONB DEFAULT '[]'::jsonb,
    refresh_interval INTEGER DEFAULT 30, -- seconds
    active BOOLEAN DEFAULT TRUE,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ALERT CONFIGURATIONS
-- =====================================================

-- Alert rules and configurations
CREATE TABLE IF NOT EXISTS alert_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_name VARCHAR(200) NOT NULL,
    component VARCHAR(100) NOT NULL,
    metric VARCHAR(100) NOT NULL,
    condition VARCHAR(20) NOT NULL CHECK (condition IN ('greater_than', 'less_than', 'equals', 'not_equals', 'contains')),
    threshold DECIMAL(10,4) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    notification_channels TEXT[] NOT NULL,
    escalation_rules JSONB,
    active BOOLEAN DEFAULT TRUE,
    region VARCHAR(10),
    environment VARCHAR(20) DEFAULT 'production',
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INCIDENT MANAGEMENT
-- =====================================================

-- Incident tracking and management
CREATE TABLE IF NOT EXISTS incident_management (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('investigating', 'identified', 'monitoring', 'resolved')),
    component VARCHAR(100) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- minutes
    impact JSONB,
    root_cause TEXT,
    resolution TEXT,
    assigned_to UUID,
    timeline JSONB DEFAULT '[]'::jsonb,
    post_mortem JSONB,
    region VARCHAR(10),
    environment VARCHAR(20) DEFAULT 'production',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE BASELINES
-- =====================================================

-- Performance baseline data for comparison
CREATE TABLE IF NOT EXISTS performance_baselines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    component VARCHAR(100) NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    baseline_value DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    percentile_95 DECIMAL(10,4),
    percentile_99 DECIMAL(10,4),
    measurement_period VARCHAR(20) NOT NULL, -- daily, weekly, monthly
    region VARCHAR(10),
    environment VARCHAR(20) DEFAULT 'production',
    established_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(component, metric_type, measurement_period, region, environment)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Medical terminology translations indexes
CREATE INDEX IF NOT EXISTS idx_medical_translations_language ON medical_terminology_translations(target_language);
CREATE INDEX IF NOT EXISTS idx_medical_translations_category ON medical_terminology_translations(category);
CREATE INDEX IF NOT EXISTS idx_medical_translations_term ON medical_terminology_translations(english_term);

-- Cultural focus groups indexes
CREATE INDEX IF NOT EXISTS idx_focus_groups_region ON cultural_focus_groups(region, country);
CREATE INDEX IF NOT EXISTS idx_focus_groups_date ON cultural_focus_groups(session_date);
CREATE INDEX IF NOT EXISTS idx_focus_groups_status ON cultural_focus_groups(status);

-- Regional deployments indexes
CREATE INDEX IF NOT EXISTS idx_regional_deployments_country ON regional_deployments(country);
CREATE INDEX IF NOT EXISTS idx_regional_deployments_phase ON regional_deployments(deployment_phase);
CREATE INDEX IF NOT EXISTS idx_regional_deployments_updated ON regional_deployments(updated_at);

-- Performance metrics indexes
CREATE INDEX IF NOT EXISTS idx_performance_metrics_component ON performance_metrics(component);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_region ON performance_metrics(region);

-- System health checks indexes
CREATE INDEX IF NOT EXISTS idx_health_checks_component ON system_health_checks(component);
CREATE INDEX IF NOT EXISTS idx_health_checks_status ON system_health_checks(status);
CREATE INDEX IF NOT EXISTS idx_health_checks_timestamp ON system_health_checks(last_check);

-- Deployment configurations indexes
CREATE INDEX IF NOT EXISTS idx_deployment_configs_env ON deployment_configurations(environment, region);
CREATE INDEX IF NOT EXISTS idx_deployment_configs_type ON deployment_configurations(configuration_type);
CREATE INDEX IF NOT EXISTS idx_deployment_configs_active ON deployment_configurations(active);

-- Monitoring dashboards indexes
CREATE INDEX IF NOT EXISTS idx_dashboards_type ON monitoring_dashboards(dashboard_type);
CREATE INDEX IF NOT EXISTS idx_dashboards_active ON monitoring_dashboards(active);

-- Alert configurations indexes
CREATE INDEX IF NOT EXISTS idx_alerts_component ON alert_configurations(component);
CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alert_configurations(severity);
CREATE INDEX IF NOT EXISTS idx_alerts_active ON alert_configurations(active);

-- Incident management indexes
CREATE INDEX IF NOT EXISTS idx_incidents_status ON incident_management(status);
CREATE INDEX IF NOT EXISTS idx_incidents_severity ON incident_management(severity);
CREATE INDEX IF NOT EXISTS idx_incidents_component ON incident_management(component);
CREATE INDEX IF NOT EXISTS idx_incidents_start_time ON incident_management(start_time);

-- Performance baselines indexes
CREATE INDEX IF NOT EXISTS idx_baselines_component ON performance_baselines(component, metric_type);
CREATE INDEX IF NOT EXISTS idx_baselines_region ON performance_baselines(region, environment);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE medical_terminology_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE cultural_focus_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_deployments ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_health_checks ENABLE ROW LEVEL SECURITY;
ALTER TABLE deployment_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE monitoring_dashboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE alert_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE incident_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_baselines ENABLE ROW LEVEL SECURITY;

-- Public read policies for translations
CREATE POLICY "public_read_translations" ON medical_terminology_translations 
    FOR SELECT USING (true);

-- Admin policies for system management tables
CREATE POLICY "admin_system_management" ON performance_metrics 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_health_checks" ON system_health_checks 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_deployments" ON regional_deployments 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_configurations" ON deployment_configurations 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_dashboards" ON monitoring_dashboards 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_alerts" ON alert_configurations 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_incidents" ON incident_management 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

CREATE POLICY "admin_baselines" ON performance_baselines 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'system'));

-- Cultural focus groups policies (researchers and admins)
CREATE POLICY "researcher_focus_groups" ON cultural_focus_groups 
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'researcher', 'cultural_expert'));

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

CREATE TRIGGER update_medical_translations_updated_at 
    BEFORE UPDATE ON medical_terminology_translations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_focus_groups_updated_at 
    BEFORE UPDATE ON cultural_focus_groups 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_regional_deployments_updated_at 
    BEFORE UPDATE ON regional_deployments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deployment_configs_updated_at 
    BEFORE UPDATE ON deployment_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dashboards_updated_at 
    BEFORE UPDATE ON monitoring_dashboards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alerts_updated_at 
    BEFORE UPDATE ON alert_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_incidents_updated_at 
    BEFORE UPDATE ON incident_management 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_baselines_updated_at 
    BEFORE UPDATE ON performance_baselines 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE medical_terminology_translations IS 'Multi-language medical terminology translations with cultural context';
COMMENT ON TABLE cultural_focus_groups IS 'Cultural focus group sessions for validation and feedback collection';
COMMENT ON TABLE regional_deployments IS 'Regional deployment tracking and configuration management';
COMMENT ON TABLE performance_metrics IS 'System performance metrics collection and monitoring';
COMMENT ON TABLE system_health_checks IS 'Component health check results and status tracking';
COMMENT ON TABLE deployment_configurations IS 'Environment-specific deployment configurations';
COMMENT ON TABLE monitoring_dashboards IS 'Monitoring dashboard configurations and layouts';
COMMENT ON TABLE alert_configurations IS 'Alert rules, thresholds, and notification configurations';
COMMENT ON TABLE incident_management IS 'Incident tracking, resolution, and post-mortem management';
COMMENT ON TABLE performance_baselines IS 'Performance baseline data for comparison and trend analysis';

-- Migration completed successfully
SELECT 'Missing database tables Phase 1 migration completed successfully' as status;
