/**
 * AGENT SYSTEM INTEGRATION TEST
 * 
 * Tests the complete agent system to ensure it works correctly
 * and replaces the hardcoded agent management.
 */

import { agentOrchestrator } from '../services/AgentOrchestrator';
import { agentRegistry } from '../services/AgentRegistry';
import { memoryManager } from '../services/MemoryManager';

describe('Agent System Integration Tests', () => {
  const testSessionId = `agent-test-${Date.now()}`;

  beforeAll(async () => {
    // Initialize the agent system
    await agentOrchestrator.initialize();
  });

  afterAll(async () => {
    // Clean up test data
    await memoryManager.clearConversationMemory(testSessionId);
    await agentOrchestrator.shutdown();
  });

  describe('Agent Registry', () => {
    test('should have registered core agents', () => {
      const stats = agentRegistry.getRegistryStats();
      expect(stats.totalAgents).toBeGreaterThan(0);
      expect(stats.healthyAgents).toBeGreaterThan(0);
    });

    test('should have triage agent', () => {
      const triageAgents = agentRegistry.getAgentsByRole('triage');
      expect(triageAgents).toHaveLength(1);
      expect(triageAgents[0].name).toBe('Nurse Triage Specialist');
    });

    test('should have emergency agent', () => {
      const emergencyAgents = agentRegistry.getAgentsByRole('emergency');
      expect(emergencyAgents).toHaveLength(1);
      expect(emergencyAgents[0].name).toBe('Dr. Emergency Response');
    });

    test('should have general practitioner agent', () => {
      const gpAgents = agentRegistry.getAgentsByRole('general_practitioner');
      expect(gpAgents).toHaveLength(1);
      expect(gpAgents[0].name).toBe('Dr. Sarah Chen');
    });
  });

  describe('Agent Orchestrator', () => {
    test('should initialize successfully', async () => {
      const stats = agentOrchestrator.getOrchestratorStats();
      expect(stats.registryStats.totalAgents).toBeGreaterThan(0);
    });

    test('should route new conversations to triage agent', async () => {
      const response = await agentOrchestrator.processRequest({
        sessionId: testSessionId,
        userMessage: 'Hello, I need medical advice',
        urgencyLevel: 'medium'
      });

      expect(response).toBeDefined();
      expect(response.agentResponse.agentName).toBe('Nurse Triage Specialist');
      expect(response.agentResponse.content).toContain('triage');
    });

    test('should detect emergency situations', async () => {
      const emergencySessionId = `emergency-test-${Date.now()}`;
      
      const response = await agentOrchestrator.processRequest({
        sessionId: emergencySessionId,
        userMessage: 'I am having severe chest pain and cannot breathe',
        urgencyLevel: 'critical'
      });

      expect(response).toBeDefined();
      expect(response.agentResponse.emergencyFlags).toBeDefined();
      expect(response.agentResponse.emergencyFlags!.length).toBeGreaterThan(0);
      
      // Clean up
      await memoryManager.clearConversationMemory(emergencySessionId);
    });

    test('should handle agent handoffs', async () => {
      const handoffSessionId = `handoff-test-${Date.now()}`;
      
      // First message to triage
      await agentOrchestrator.processRequest({
        sessionId: handoffSessionId,
        userMessage: 'I have general health questions',
        urgencyLevel: 'low'
      });

      // Request handoff to GP
      const handoffResult = await agentOrchestrator.performHandoff(
        handoffSessionId,
        'general_practitioner',
        'Patient needs general practitioner consultation'
      );

      expect(handoffResult).toBe(true);
      
      const currentAgent = agentOrchestrator.getCurrentAgent(handoffSessionId);
      expect(currentAgent?.role).toBe('general_practitioner');
      
      // Clean up
      await memoryManager.clearConversationMemory(handoffSessionId);
    });
  });

  describe('Agent Capabilities', () => {
    test('should have proper capabilities defined', () => {
      const allAgents = agentRegistry.getAllAgents();
      
      allAgents.forEach(agent => {
        expect(agent.capabilities).toBeDefined();
        expect(agent.capabilities.length).toBeGreaterThan(0);
        expect(agent.role).toBeDefined();
        expect(agent.name).toBeDefined();
      });
    });

    test('should calculate confidence scores', () => {
      const triageAgent = agentRegistry.getAgentsByRole('triage')[0];
      
      const request = {
        sessionId: testSessionId,
        userMessage: 'I need medical help',
        conversationHistory: [],
        urgencyLevel: 'medium' as const
      };

      const confidence = triageAgent.getConfidenceScore(request);
      expect(confidence).toBeGreaterThan(0);
      expect(confidence).toBeLessThanOrEqual(1);
    });

    test('should perform health checks', async () => {
      const allAgents = agentRegistry.getAllAgents();
      
      for (const agent of allAgents) {
        const health = await agent.healthCheck();
        expect(health).toBeDefined();
        expect(health.healthy).toBeDefined();
        expect(health.details).toBeDefined();
      }
    });
  });

  describe('Memory Integration', () => {
    test('should save agent responses to memory', async () => {
      const memorySessionId = `memory-test-${Date.now()}`;
      
      const response = await agentOrchestrator.processRequest({
        sessionId: memorySessionId,
        userMessage: 'Test message for memory',
        urgencyLevel: 'low'
      });

      expect(response).toBeDefined();
      
      // Check that message was saved to memory
      const conversationHistory = await memoryManager.getConversationHistory(memorySessionId);
      expect(conversationHistory.length).toBeGreaterThan(0);
      
      const agentMessage = conversationHistory.find(msg => msg.speaker_type === 'agent');
      expect(agentMessage).toBeDefined();
      expect(agentMessage!.speaker_name).toBe(response.agentResponse.agentName);
      
      // Clean up
      await memoryManager.clearConversationMemory(memorySessionId);
    });

    test('should maintain conversation context', async () => {
      const contextSessionId = `context-test-${Date.now()}`;
      
      // Send first message
      await agentOrchestrator.processRequest({
        sessionId: contextSessionId,
        userMessage: 'Hello, I am a new patient',
        urgencyLevel: 'low'
      });

      // Send follow-up message
      const response = await agentOrchestrator.processRequest({
        sessionId: contextSessionId,
        userMessage: 'I have questions about my health',
        urgencyLevel: 'low'
      });

      expect(response).toBeDefined();
      
      // Check conversation context
      const context = await memoryManager.getConversationContext(contextSessionId);
      expect(context).toBeDefined();
      expect(context!.total_messages).toBeGreaterThan(1);
      
      // Clean up
      await memoryManager.clearConversationMemory(contextSessionId);
    });
  });

  describe('Performance Tests', () => {
    test('should respond within reasonable time', async () => {
      const startTime = Date.now();
      
      const response = await agentOrchestrator.processRequest({
        sessionId: `perf-test-${Date.now()}`,
        userMessage: 'Quick health question',
        urgencyLevel: 'low'
      });

      const responseTime = Date.now() - startTime;
      
      expect(response).toBeDefined();
      expect(responseTime).toBeLessThan(10000); // Should respond within 10 seconds
    });

    test('emergency agent should respond quickly', async () => {
      const startTime = Date.now();
      
      const response = await agentOrchestrator.processRequest({
        sessionId: `emergency-perf-test-${Date.now()}`,
        userMessage: 'EMERGENCY: I cannot breathe',
        urgencyLevel: 'critical'
      });

      const responseTime = Date.now() - startTime;
      
      expect(response).toBeDefined();
      expect(responseTime).toBeLessThan(3000); // Emergency should be fast
      
      if (response.agentResponse.emergencyFlags && response.agentResponse.emergencyFlags.length > 0) {
        const criticalFlags = response.agentResponse.emergencyFlags.filter(flag => flag.severity === 'critical');
        if (criticalFlags.length > 0) {
          expect(responseTime).toBeLessThan(2000); // Critical emergencies must be <2 seconds
        }
      }
    });
  });
});

/**
 * Manual test function for development
 */
export async function testAgentSystemManually() {
  console.log('🧪 Testing Agent System...');
  
  try {
    // Initialize
    await agentOrchestrator.initialize();
    
    // Test agent registry
    console.log('1. Testing agent registry...');
    const stats = agentRegistry.getRegistryStats();
    console.log('Registry stats:', stats);
    
    // Test agent selection
    console.log('2. Testing agent selection...');
    const testSession = `manual-test-${Date.now()}`;
    
    const response = await agentOrchestrator.processRequest({
      sessionId: testSession,
      userMessage: 'I need medical advice about chest pain',
      urgencyLevel: 'high'
    });
    
    console.log('Agent response:', {
      agent: response.agentResponse.agentName,
      confidence: response.agentResponse.confidence,
      hasEmergencyFlags: response.agentResponse.emergencyFlags?.length > 0,
      suggestedHandoffs: response.agentResponse.suggestedHandoffs?.length || 0
    });
    
    // Test memory persistence
    console.log('3. Testing memory persistence...');
    const context = await memoryManager.getConversationContext(testSession);
    console.log('Conversation context:', {
      sessionId: context?.session_id,
      messageCount: context?.total_messages,
      activeAgents: context?.active_agents
    });
    
    // Clean up
    await memoryManager.clearConversationMemory(testSession);
    
    console.log('✅ Agent system test completed successfully!');
    
  } catch (error) {
    console.error('❌ Agent system test failed:', error);
  }
}
