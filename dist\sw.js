const CACHE_VERSION = '2.0.0';
const CACHE_NAME = `voicehealth-ai-v${CACHE_VERSION}`;
const STATIC_CACHE = `voicehealth-static-v${CACHE_VERSION}`;
const DYNAMIC_CACHE = `voicehealth-dynamic-v${CACHE_VERSION}`;
const HEALTH_RECORDS_CACHE = `voicehealth-records-v${CACHE_VERSION}`;
const VOICE_CACHE = `voicehealth-voice-v${CACHE_VERSION}`;

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico',
  '/assets/images/no_image.png',
  '/offline.html'
];

// API endpoints that should be cached for offline access
const CACHEABLE_APIS = [
  '/api/medical-conditions',
  '/api/medications',
  '/api/consultation-sessions',
  '/api/ai-agents'
];

// Background sync tag names
const SYNC_TAGS = {
  HEALTH_RECORDS: 'health-records-sync',
  CONSULTATION_DATA: 'consultation-data-sync',
  MEDICAL_CONDITIONS: 'medical-conditions-sync',
  MEDICATIONS: 'medications-sync',
  VOICE_CHUNKS: 'voice-chunks-sync',
  TRIAGE_RESULTS: 'triage-results-sync'
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log(`Service Worker: Installing version ${CACHE_VERSION}...`);
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS).catch(err => {
          console.error('Failed to cache some static assets:', err);
          // Continue installation even if some assets fail
          return Promise.resolve();
        });
      }),
      caches.open(VOICE_CACHE), // Initialize voice cache
      self.skipWaiting() // Force immediate activation
    ])
  );
  
  // Notify clients about update
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({
        type: 'SW_UPDATE_INSTALLING',
        version: CACHE_VERSION
      });
    });
  });
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log(`Service Worker: Activating version ${CACHE_VERSION}...`);
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        const deletePromises = cacheNames
          .filter(cacheName => {
            // Keep current version caches
            return !cacheName.endsWith(`v${CACHE_VERSION}`) && 
                   (cacheName.includes('voicehealth-') || cacheName.includes('doctalks-'));
          })
          .map(cacheName => {
            console.log(`Service Worker: Deleting old cache ${cacheName}`);
            return caches.delete(cacheName);
          });
        
        return Promise.all(deletePromises);
      }),
      
      // Take control of all clients immediately
      self.clients.claim()
    ])
  );
  
  // Notify clients about activation
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({
        type: 'SW_UPDATE_ACTIVATED',
        version: CACHE_VERSION
      });
    });
  });
  
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== HEALTH_RECORDS_CACHE) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      self.clients.claim()
    ])
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle different types of requests with appropriate caching strategies
  if (request.method === 'GET') {
    // Static assets - cache first
    if (isStaticAsset(url)) {
      event.respondWith(cacheFirst(request, STATIC_CACHE));
    }
    // Health records APIs - network first with fallback
    else if (isHealthRecordAPI(url)) {
      event.respondWith(networkFirstWithSync(request, HEALTH_RECORDS_CACHE));
    }
    // Other API requests - network first
    else if (isAPIRequest(url)) {
      event.respondWith(networkFirst(request, DYNAMIC_CACHE));
    }
    // Navigation requests - network first with offline fallback
    else if (request.mode === 'navigate') {
      event.respondWith(handleNavigate(request));
    }
    // Default - network first
    else {
      event.respondWith(networkFirst(request, DYNAMIC_CACHE));
    }
  }
  // Handle POST/PUT/DELETE requests for health records
  else if (isHealthRecordAPI(url) && ['POST', 'PUT', 'DELETE'].includes(request.method)) {
    event.respondWith(handleHealthRecordMutation(request));
  }
});

// Background sync event
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered:', event.tag);
  
  switch (event.tag) {
    case SYNC_TAGS.HEALTH_RECORDS:
      event.waitUntil(syncHealthRecords());
      break;
    case SYNC_TAGS.CONSULTATION_DATA:
      event.waitUntil(syncConsultationData());
      break;
    case SYNC_TAGS.MEDICAL_CONDITIONS:
      event.waitUntil(syncMedicalConditions());
      break;
    case SYNC_TAGS.MEDICATIONS:
      event.waitUntil(syncMedications());
      break;
  }
});

// Message event - handle commands from main thread
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    case 'GET_CACHE_STATUS':
      getCacheStatus().then(status => {
        event.ports[0].postMessage({ type: 'CACHE_STATUS', payload: status });
      });
      break;
    case 'CLEAR_CACHE':
      clearCache(payload?.cacheType).then(() => {
        event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
      });
      break;
    case 'FORCE_SYNC':
      forceSyncData(payload?.syncType);
      break;
  }
});

// Cache strategies implementation
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    return new Response('Offline content not available', { status: 503 });
  }
}

async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Network unavailable and no cached data found',
      offline: true 
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

async function networkFirstWithSync(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
      
      // Store in IndexedDB for offline access
      const data = await networkResponse.clone().json();
      await storeHealthRecordData(request.url, data);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying offline data:', error);
    
    // Try to get from IndexedDB first
    const offlineData = await getOfflineHealthRecordData(request.url);
    if (offlineData) {
      return new Response(JSON.stringify(offlineData), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Fallback to cache
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response(JSON.stringify({
      success: false,
      error: 'No offline data available',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

async function handleNavigate(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    // Return offline page for navigation requests when network fails
    const cache = await caches.open(STATIC_CACHE);
    const offlinePage = await cache.match('/offline.html');
    return offlinePage || new Response('Offline', { status: 503 });
  }
}

async function handleHealthRecordMutation(request) {
  try {
    // Try to send the request immediately
    const response = await fetch(request);
    
    if (response.ok) {
      return response;
    } else {
      throw new Error('Network request failed');
    }
  } catch (error) {
    console.log('Health record mutation failed, storing for sync:', error);
    
    // Store the request for background sync
    await storeFailedRequest(request);
    
    // Register background sync
    await self.registration.sync.register(SYNC_TAGS.HEALTH_RECORDS);
    
    return new Response(JSON.stringify({
      success: true,
      offline: true,
      message: 'Changes saved offline and will sync when connection is restored'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Utility functions
function isStaticAsset(url) {
  const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2'];
  return staticExtensions.some(ext => url.pathname.includes(ext)) || 
         url.pathname === '/' || 
         url.pathname === '/manifest.json';
}

function isHealthRecordAPI(url) {
  const healthRecordPaths = [
    '/api/medical-conditions',
    '/api/medications',
    '/api/consultation-sessions',
    '/api/session-recommendations'
  ];
  return healthRecordPaths.some(path => url.pathname.includes(path));
}

function isAPIRequest(url) {
  return url.pathname.startsWith('/api/') || url.hostname.includes('supabase.co');
}

// IndexedDB operations for health records
async function openHealthRecordsDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('VoiceHealthRecords', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Health records store
      if (!db.objectStoreNames.contains('healthRecords')) {
        const store = db.createObjectStore('healthRecords', { keyPath: 'url' });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
      
      // Failed requests store
      if (!db.objectStoreNames.contains('failedRequests')) {
        const store = db.createObjectStore('failedRequests', { 
          keyPath: 'id', 
          autoIncrement: true 
        });
        store.createIndex('timestamp', 'timestamp', { unique: false });
        store.createIndex('type', 'type', { unique: false });
      }
      
      // Sync queue store
      if (!db.objectStoreNames.contains('syncQueue')) {
        const store = db.createObjectStore('syncQueue', { 
          keyPath: 'id', 
          autoIncrement: true 
        });
        store.createIndex('priority', 'priority', { unique: false });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
}

async function storeHealthRecordData(url, data) {
  try {
    const db = await openHealthRecordsDB();
    const transaction = db.transaction(['healthRecords'], 'readwrite');
    const store = transaction.objectStore('healthRecords');
    
    await store.put({
      url,
      data,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Failed to store health record data:', error);
  }
}

async function getOfflineHealthRecordData(url) {
  try {
    const db = await openHealthRecordsDB();
    const transaction = db.transaction(['healthRecords'], 'readonly');
    const store = transaction.objectStore('healthRecords');
    
    return new Promise((resolve, reject) => {
      const request = store.get(url);
      request.onsuccess = () => {
        resolve(request.result?.data || null);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Failed to get offline health record data:', error);
    return null;
  }
}

async function storeFailedRequest(request) {
  try {
    const db = await openHealthRecordsDB();
    const transaction = db.transaction(['failedRequests'], 'readwrite');
    const store = transaction.objectStore('failedRequests');
    
    const body = request.method !== 'GET' ? await request.clone().text() : null;
    
    await store.add({
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      body,
      timestamp: Date.now(),
      type: 'health-record'
    });
  } catch (error) {
    console.error('Failed to store failed request:', error);
  }
}

// Background sync functions
async function syncHealthRecords() {
  try {
    const db = await openHealthRecordsDB();
    const transaction = db.transaction(['failedRequests'], 'readwrite');
    const store = transaction.objectStore('failedRequests');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = async () => {
        const failedRequests = request.result;
        const syncPromises = [];
        
        for (const failedRequest of failedRequests) {
          const syncPromise = retrySyncRequest(failedRequest)
            .then(success => {
              if (success) {
                // Remove successful request from queue
                store.delete(failedRequest.id);
              }
            })
            .catch(error => {
              console.error('Sync retry failed:', error);
            });
          
          syncPromises.push(syncPromise);
        }
        
        Promise.all(syncPromises).then(() => resolve()).catch(reject);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Health records sync failed:', error);
    throw error;
  }
}

async function retrySyncRequest(failedRequest) {
  try {
    const { url, method, headers, body } = failedRequest;
    
    const request = new Request(url, {
      method,
      headers,
      body: body ? body : undefined
    });
    
    const response = await fetch(request);
    
    if (response.ok) {
      console.log('Successfully synced request:', url);
      
      // Notify clients about successful sync
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'SYNC_SUCCESS',
          payload: { url, method }
        });
      });
      
      return true;
    } else {
      console.log('Sync request failed with status:', response.status);
      return false;
    }
  } catch (error) {
    console.log('Sync request failed:', error);
    return false;
  }
}

async function syncConsultationData() {
  console.log('Syncing consultation data...');
  // Implementation for consultation-specific sync
  return syncHealthRecords();
}

async function syncMedicalConditions() {
  console.log('Syncing medical conditions...');
  // Implementation for medical conditions-specific sync
  return syncHealthRecords();
}

async function syncMedications() {
  console.log('Syncing medications...');
  // Implementation for medications-specific sync  
  return syncHealthRecords();
}

async function syncVoiceChunks() {
  console.log('Syncing voice chunks...');
  
  try {
    // Open IndexedDB to get pending voice chunks
    const db = await openVoiceDB();
    const transaction = db.transaction(['audioChunks'], 'readonly');
    const store = transaction.objectStore('audioChunks');
    const index = store.index('synced');
    const request = index.getAll(false); // Get unsynced chunks
    
    return new Promise((resolve, reject) => {
      request.onsuccess = async () => {
        const unsyncedChunks = request.result;
        console.log(`Found ${unsyncedChunks.length} unsynced voice chunks`);
        
        const syncPromises = [];
        
        for (const chunk of unsyncedChunks) {
          const syncPromise = syncVoiceChunk(chunk)
            .then(() => {
              // Mark as synced in IndexedDB
              return markVoiceChunkSynced(chunk.id);
            })
            .catch(error => {
              console.error('Voice chunk sync failed:', error);
            });
          
          syncPromises.push(syncPromise);
        }
        
        Promise.all(syncPromises).then(() => resolve()).catch(reject);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Voice chunks sync failed:', error);
    throw error;
  }
}

async function syncTriageResults() {
  console.log('Syncing triage results...');
  
  try {
    // Open IndexedDB to get pending triage results
    const db = await openVoiceDB();
    const transaction = db.transaction(['triageResults'], 'readonly');
    const store = transaction.objectStore('triageResults');
    const index = store.index('synced');
    const request = index.getAll(false); // Get unsynced results
    
    return new Promise((resolve, reject) => {
      request.onsuccess = async () => {
        const unsyncedResults = request.result;
        console.log(`Found ${unsyncedResults.length} unsynced triage results`);
        
        const syncPromises = [];
        
        for (const result of unsyncedResults) {
          const syncPromise = syncTriageResult(result)
            .then(() => {
              return markTriageResultSynced(result.id);
            })
            .catch(error => {
              console.error('Triage result sync failed:', error);
            });
          
          syncPromises.push(syncPromise);
        }
        
        Promise.all(syncPromises).then(() => resolve()).catch(reject);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Triage results sync failed:', error);
    throw error;
  }
}

// Utility functions for cache management
async function getCacheStatus() {
  const cacheNames = await caches.keys();
  const status = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    status[cacheName] = {
      count: keys.length,
      size: await estimateCacheSize(cache, keys)
    };
  }
  
  return status;
}

async function estimateCacheSize(cache, keys) {
  let totalSize = 0;
  
  for (const key of keys.slice(0, 10)) { // Sample first 10 for estimation
    try {
      const response = await cache.match(key);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    } catch (error) {
      // Ignore errors for size estimation
    }
  }
  
  return Math.round((totalSize * keys.length) / Math.min(keys.length, 10));
}

async function clearCache(cacheType) {
  if (cacheType) {
    await caches.delete(cacheType);
  } else {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
  }
}

async function forceSyncData(syncType) {
  switch (syncType) {
    case 'health-records':
      await self.registration.sync.register(SYNC_TAGS.HEALTH_RECORDS);
      break;
    case 'consultation':
      await self.registration.sync.register(SYNC_TAGS.CONSULTATION_DATA);
      break;
    default:
      // Register all sync types
      await Promise.all([
        self.registration.sync.register(SYNC_TAGS.HEALTH_RECORDS),
        self.registration.sync.register(SYNC_TAGS.CONSULTATION_DATA),
        self.registration.sync.register(SYNC_TAGS.MEDICAL_CONDITIONS),
        self.registration.sync.register(SYNC_TAGS.MEDICATIONS)
      ]);
  }
}

// Background sync event listeners
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag);
  
  switch (event.tag) {
    case SYNC_TAGS.HEALTH_RECORDS:
      event.waitUntil(syncHealthRecords());
      break;
    case SYNC_TAGS.CONSULTATION_DATA:
      event.waitUntil(syncConsultationData());
      break;
    case SYNC_TAGS.MEDICAL_CONDITIONS:
      event.waitUntil(syncMedicalConditions());
      break;
    case SYNC_TAGS.MEDICATIONS:
      event.waitUntil(syncMedications());
      break;
    case SYNC_TAGS.VOICE_CHUNKS:
      event.waitUntil(syncVoiceChunks());
      break;
    case SYNC_TAGS.TRIAGE_RESULTS:
      event.waitUntil(syncTriageResults());
      break;
    default:
      console.log('Unknown sync tag:', event.tag);
  }
});

// Message handler for client communication
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    case 'CACHE_VOICE_CHUNK':
      event.waitUntil(cacheVoiceChunk(data));
      break;
    case 'GET_CACHE_STATUS':
      event.waitUntil(
        getCacheStatus().then(status => {
          event.ports[0].postMessage({ type: 'CACHE_STATUS', data: status });
        })
      );
      break;
    case 'CLEAR_CACHE':
      event.waitUntil(
        clearCache(data.cacheType).then(() => {
          event.ports[0].postMessage({ type: 'CACHE_CLEARED', data: data.cacheType });
        })
      );
      break;
    case 'FORCE_SYNC':
      event.waitUntil(
        forceSyncData(data.syncType).then(() => {
          event.ports[0].postMessage({ type: 'SYNC_COMPLETED', data: data.syncType });
        })
      );
      break;
  }
});

// Voice-related helper functions
async function openVoiceDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('VoiceHealthDB', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Create audio chunks store if it doesn't exist
      if (!db.objectStoreNames.contains('audioChunks')) {
        const store = db.createObjectStore('audioChunks', { keyPath: 'id' });
        store.createIndex('synced', 'synced', { unique: false });
        store.createIndex('sessionId', 'sessionId', { unique: false });
      }
      
      // Create triage results store if it doesn't exist
      if (!db.objectStoreNames.contains('triageResults')) {
        const store = db.createObjectStore('triageResults', { keyPath: 'id' });
        store.createIndex('synced', 'synced', { unique: false });
        store.createIndex('sessionId', 'sessionId', { unique: false });
      }
    };
  });
}

async function cacheVoiceChunk(chunkData) {
  try {
    const cache = await caches.open(VOICE_CACHE);
    const chunkUrl = `voice-chunk-${chunkData.sessionId}-${chunkData.chunkIndex}`;
    
    const response = new Response(chunkData.audioData, {
      headers: {
        'Content-Type': 'audio/wav',
        'X-Chunk-Index': chunkData.chunkIndex.toString(),
        'X-Session-ID': chunkData.sessionId,
        'X-Timestamp': chunkData.timestamp
      }
    });
    
    await cache.put(chunkUrl, response);
    console.log(`Voice chunk cached: ${chunkUrl}`);
    
  } catch (error) {
    console.error('Failed to cache voice chunk:', error);
  }
}

async function syncVoiceChunk(chunk) {
  const formData = new FormData();
  formData.append('sessionId', chunk.sessionId);
  formData.append('chunkIndex', chunk.chunkIndex);
  formData.append('audioData', new Blob([chunk.audioData], { type: 'audio/wav' }));
  formData.append('quality', chunk.quality);
  formData.append('timestamp', chunk.timestamp);
  
  const response = await fetch('/api/audio/chunks', {
    method: 'POST',
    body: formData
  });
  
  if (!response.ok) {
    throw new Error(`Voice chunk sync failed: ${response.status}`);
  }
  
  return response.json();
}

async function syncTriageResult(result) {
  const response = await fetch('/api/triage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      sessionId: result.sessionId,
      result: result.result,
      confidence: result.confidence,
      timestamp: result.timestamp
    })
  });
  
  if (!response.ok) {
    throw new Error(`Triage result sync failed: ${response.status}`);
  }
  
  return response.json();
}

async function markVoiceChunkSynced(chunkId) {
  const db = await openVoiceDB();
  const transaction = db.transaction(['audioChunks'], 'readwrite');
  const store = transaction.objectStore('audioChunks');
  
  return new Promise((resolve, reject) => {
    const request = store.get(chunkId);
    request.onsuccess = () => {
      const chunk = request.result;
      if (chunk) {
        chunk.synced = true;
        chunk.syncedAt = new Date().toISOString();
        const updateRequest = store.put(chunk);
        updateRequest.onsuccess = () => resolve();
        updateRequest.onerror = () => reject(updateRequest.error);
      } else {
        resolve();
      }
    };
    request.onerror = () => reject(request.error);
  });
}

async function markTriageResultSynced(resultId) {
  const db = await openVoiceDB();
  const transaction = db.transaction(['triageResults'], 'readwrite');
  const store = transaction.objectStore('triageResults');
  
  return new Promise((resolve, reject) => {
    const request = store.get(resultId);
    request.onsuccess = () => {
      const result = request.result;
      if (result) {
        result.synced = true;
        result.syncedAt = new Date().toISOString();
        const updateRequest = store.put(result);
        updateRequest.onsuccess = () => resolve();
        updateRequest.onerror = () => reject(updateRequest.error);
      } else {
        resolve();
      }
    };
    request.onerror = () => reject(request.error);
  });
}

console.log(`Service Worker: Loaded and ready - Version ${CACHE_VERSION}`);