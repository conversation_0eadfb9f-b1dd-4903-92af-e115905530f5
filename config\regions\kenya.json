{"countryCode": "KE", "countryName": "Kenya", "region": "East Africa", "capital": "Nairobi", "timezone": "EAT", "currency": "KES", "languages": [{"code": "en", "name": "English", "localName": "English", "primary": true, "supportLevel": "full", "voiceSupport": true, "medicalTerminology": true, "culturalAdaptation": true}, {"code": "sw", "name": "Swahili", "localName": "Kiswahili", "primary": true, "supportLevel": "full", "voiceSupport": true, "medicalTerminology": true, "culturalAdaptation": true}, {"code": "ki", "name": "<PERSON><PERSON><PERSON>", "localName": "Gĩkũyũ", "primary": false, "supportLevel": "partial", "voiceSupport": false, "medicalTerminology": false, "culturalAdaptation": true}, {"code": "luo", "name": "<PERSON><PERSON>", "localName": "<PERSON><PERSON><PERSON>", "primary": false, "supportLevel": "basic", "voiceSupport": false, "medicalTerminology": false, "culturalAdaptation": true}], "healthcareSystem": {"systemType": "mixed", "primaryCareStructure": "Community health units and dispensaries", "specialistAccess": "moderate", "emergencyServices": {"emergencyNumber": "999", "responseTime": 30, "coverage": "urban_and_rural", "integration": "moderate"}, "traditionalMedicine": {"recognition": "formal", "regulation": "moderate", "integration": "collaborative", "safetyProtocols": ["traditional healer certification", "herb-drug interaction database", "integrated care protocols", "patient safety monitoring"]}, "healthInsurance": {"coverage": "expanding", "providers": ["NHIF", "Private"], "digitalIntegration": true, "aiCoverage": false}, "digitalHealthReadiness": "high"}, "regulatory": {"healthAuthority": "Ministry of Health Kenya", "dataProtectionLaw": "Data Protection Act 2019", "medicalDeviceRegulation": "Pharmacy and Poisons Board Medical Device Regulation", "aiRegulation": "Digital Health Strategy 2030", "telemedicineRegulation": "Telemedicine Practice Guidelines 2021", "requiredApprovals": [{"authority": "Ministry of Health", "approvalType": "Digital Health Platform License", "status": "not_started", "conditions": [], "validUntil": null}, {"authority": "Pharmacy and Poisons Board", "approvalType": "Medical Device Registration", "status": "not_started", "conditions": [], "validUntil": null}, {"authority": "Office of the Data Protection Commissioner", "approvalType": "Data Controller Registration", "status": "not_started", "conditions": [], "validUntil": null}], "complianceRequirements": [{"requirement": "Patient data localization", "category": "data_protection", "status": "not_assessed", "evidence": [], "lastAssessed": "2025-01-06T00:00:00Z", "nextAssessment": "2025-02-06T00:00:00Z"}, {"requirement": "Medical AI validation", "category": "medical_device", "status": "not_assessed", "evidence": [], "lastAssessed": "2025-01-06T00:00:00Z", "nextAssessment": "2025-02-06T00:00:00Z"}, {"requirement": "Traditional medicine integration standards", "category": "traditional_medicine", "status": "not_assessed", "evidence": [], "lastAssessed": "2025-01-06T00:00:00Z", "nextAssessment": "2025-02-06T00:00:00Z"}]}, "technical": {"infrastructure": {"cloudProvider": "AWS", "region": "af-south-1", "dataCenter": "Cape Town", "backupRegion": "eu-west-1", "scalingStrategy": "auto"}, "connectivity": {"internetPenetration": 87.2, "mobileNetworkCoverage": 96.2, "averageSpeed": 25.2, "reliability": "high", "costPerGB": 1.8}, "security": {"encryptionStandard": "AES-256-GCM", "authenticationMethod": "JWT + MFA", "accessControls": ["RBAC", "ABAC"], "auditRequirements": ["Data Protection Act", "Health Records Standards"], "incidentResponsePlan": "Kenya Incident Response Plan v1.0"}, "integration": {"existingSystems": [{"name": "KHIS", "type": "his", "vendor": "Ministry of Health Kenya", "version": "2.0", "integrationComplexity": "low", "integrationStatus": "not_started"}, {"name": "NHIF System", "type": "insurance", "vendor": "NHIF", "version": "3.0", "integrationComplexity": "medium", "integrationStatus": "not_started"}], "apiStandards": ["HL7 FHIR", "REST", "OpenHIE"], "dataFormats": ["JSON", "XML", "HL7"], "interoperabilityLevel": "high"}}, "cultural": {"primaryCultures": ["kikuyu", "luhya", "luo", "kalenjin", "kamba"], "communicationStyles": ["respectful", "community-oriented", "hierarchical"], "familyStructures": ["extended", "nuclear", "clan-based"], "religiousConsiderations": ["christian", "islamic", "traditional"], "genderConsiderations": ["respect for elders", "gender equality", "family consultation"], "ageRespectLevels": ["high elder respect", "age-based wisdom"], "traditionalPractices": ["herbal medicine", "spiritual healing", "community healing rituals", "traditional midwifery"], "culturalSensitivities": ["respect for traditional authority", "importance of community involvement", "spiritual aspects of health", "gender-sensitive care"]}, "emergency": {"protocols": [{"severity": "critical", "protocol": "Emergency services + community notification", "culturalAdaptations": ["Community involvement in emergency care", "Traditional healer consultation", "Family-centered decision making"], "responseTime": 2, "escalationRules": ["Contact emergency services", "Notify community health worker", "Alert family and clan elders"]}, {"severity": "high", "protocol": "Urgent care + cultural integration", "culturalAdaptations": ["Traditional medicine integration", "Community support mobilization"], "responseTime": 5, "escalationRules": ["Schedule urgent care", "Coordinate with traditional healers", "Engage community health network"]}], "culturalAdaptations": ["Community-centered care", "Traditional healer collaboration", "Clan and family involvement", "Spiritual care integration"], "familyNotificationRules": ["Notify immediate family first", "Include clan elders for major decisions", "Respect traditional hierarchy"], "traditionalHealerIntegration": true, "responseTimeTargets": {"critical": 2, "high": 5, "medium": 15, "low": 60}}, "demographics": {"population": 54027000, "urbanPopulation": 28.0, "medianAge": 20.0, "literacyRate": 81.5, "healthLiteracyRate": 52.0, "internetUsers": 87.2, "mobileSubscribers": 118.8}, "economicFactors": {"gdpPerCapita": 2007, "healthcareSpendingPerCapita": 66, "outOfPocketHealthSpending": 25.0, "povertyRate": 36.1, "unemploymentRate": 5.7}, "diseaseProfile": {"topCauses": ["malaria", "respiratory infections", "tuberculosis", "diarrheal diseases", "hypertension", "diabetes", "HIV/AIDS", "maternal conditions"], "endemicDiseases": ["malaria", "tuberculosis", "HIV/AIDS", "rift valley fever", "leishmaniasis"], "emergingThreats": ["non-communicable diseases", "antimicrobial resistance", "climate-related diseases"]}, "seasonalFactors": {"longRainsMonths": [3, 4, 5], "shortRainsMonths": [10, 11, 12], "drySeasonMonths": [1, 2, 6, 7, 8, 9], "malariaSeasonPeak": [4, 5, 11, 12], "riftValleyFeverRisk": [3, 4, 5, 10, 11, 12]}, "deployment": {"pilotRegions": ["Nairobi", "Central"], "rolloutPhases": [{"phase": 1, "regions": ["Nairobi"], "duration": "3 months", "targetPopulation": 4500000}, {"phase": 2, "regions": ["Central", "Eastern"], "duration": "6 months", "targetPopulation": 8000000}, {"phase": 3, "regions": ["Western", "<PERSON><PERSON><PERSON>", "Rift Valley"], "duration": "12 months", "targetPopulation": 20000000}], "successMetrics": ["User adoption rate > 20%", "Clinical accuracy > 88%", "Cultural appropriateness > 92%", "Emergency response time < 2 seconds", "Patient satisfaction > 4.2/5.0"]}}