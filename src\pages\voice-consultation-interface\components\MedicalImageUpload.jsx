/**
 * MEDICAL IMAGE UPLOAD COMPONENT
 * 
 * Secure image upload component for medical consultations that enables
 * multi-modal diagnostic capabilities. Integrates with Supabase Storage
 * and provides HIPAA-compliant image handling.
 * 
 * FEATURES:
 * - Secure image upload to Supabase Storage
 * - Medical image format validation
 * - Real-time upload progress
 * - Image preview and metadata
 * - Session-based organization
 * - HIPAA-compliant handling
 */

import React, { useState, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import { supabase } from '../../../utils/supabaseClient';
import { auditLogger } from '../../../utils/auditLogger';

const MedicalImageUpload = ({ 
  sessionId,
  onImageUploaded,
  onUploadError,
  disabled = false,
  className = ''
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [syncingWithDatabase, setSyncingWithDatabase] = useState(false);
  const fileInputRef = useRef(null);

  // Supported medical image formats
  const supportedFormats = {
    'image/jpeg': '.jpg,.jpeg',
    'image/png': '.png',
    'image/webp': '.webp',
    'image/bmp': '.bmp',
    'image/tiff': '.tiff'
  };

  const maxFileSize = 10 * 1024 * 1024; // 10MB limit
  const maxImages = 5; // Maximum 5 images per session

  /**
   * Load existing images from database on component mount
   */
  React.useEffect(() => {
    if (sessionId) {
      loadExistingImages();
    }
  }, [sessionId]);

  /**
   * Load existing images for the session from database
   */
  const loadExistingImages = async () => {
    try {
      setSyncingWithDatabase(true);

      const { data: existingImages, error } = await supabase
        .from('medical_images')
        .select('*')
        .eq('session_id', sessionId)
        .neq('analysis_status', 'deleted')
        .order('created_at', { ascending: false });

      if (error) {
        console.warn('Failed to load existing images:', error);
        return;
      }

      if (existingImages && existingImages.length > 0) {
        const imageMetadata = existingImages.map(img => {
          // Get public URL for each image
          const { data: urlData } = supabase.storage
            .from('medical-images')
            .getPublicUrl(img.storage_path);

          return {
            id: img.id,
            fileName: img.storage_path,
            originalName: img.original_filename,
            url: urlData.publicUrl,
            size: img.file_size,
            type: img.mime_type,
            uploadedAt: img.upload_timestamp,
            sessionId: img.session_id,
            analysisStatus: img.analysis_status,
            dbRecord: img
          };
        });

        setUploadedImages(imageMetadata);
        console.log(`✅ Loaded ${imageMetadata.length} existing images from database`);
      }

    } catch (error) {
      console.error('❌ Error loading existing images:', error);
    } finally {
      setSyncingWithDatabase(false);
    }
  };

  /**
   * Handle drag and drop events
   */
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (disabled) return;
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  /**
   * Handle file drop
   */
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled) return;
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  /**
   * Handle file input change
   */
  const handleFileInput = (e) => {
    if (disabled) return;
    
    const files = Array.from(e.target.files);
    handleFiles(files);
  };

  /**
   * Process and upload files
   */
  const handleFiles = async (files) => {
    if (uploadedImages.length >= maxImages) {
      onUploadError?.('Maximum number of images reached');
      return;
    }

    const validFiles = [];
    
    // Validate files
    for (const file of files) {
      if (!Object.keys(supportedFormats).includes(file.type)) {
        onUploadError?.(`Unsupported file type: ${file.type}`);
        continue;
      }
      
      if (file.size > maxFileSize) {
        onUploadError?.(`File too large: ${file.name}. Maximum size is 10MB`);
        continue;
      }
      
      if (uploadedImages.length + validFiles.length >= maxImages) {
        onUploadError?.('Maximum number of images reached');
        break;
      }
      
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    // Upload files
    for (const file of validFiles) {
      await uploadImage(file);
    }
  };

  /**
   * Upload single image to Supabase Storage
   */
  const uploadImage = async (file) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // Generate unique filename
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2);
      const fileExtension = file.name.split('.').pop();
      const fileName = `${sessionId}/${timestamp}_${randomId}.${fileExtension}`;

      console.log('📸 Uploading medical image:', fileName);

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('medical-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false,
          metadata: {
            sessionId,
            originalName: file.name,
            uploadedAt: new Date().toISOString(),
            fileSize: file.size.toString(),
            mimeType: file.type
          }
        });

      if (error) {
        throw new Error(`Upload failed: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('medical-images')
        .getPublicUrl(fileName);

      // Create database record for image metadata
      const { data: imageRecord, error: dbError } = await supabase
        .from('medical_images')
        .insert({
          session_id: sessionId,
          storage_path: data.path,
          original_filename: file.name,
          file_size: file.size,
          mime_type: file.type,
          analysis_status: 'pending'
        })
        .select()
        .single();

      if (dbError) {
        console.error('❌ Failed to create image database record:', dbError);
        // Clean up uploaded file if database insert fails
        await supabase.storage
          .from('medical-images')
          .remove([fileName]);
        throw new Error(`Database record creation failed: ${dbError.message}`);
      }

      // Create image metadata for frontend
      const imageMetadata = {
        id: imageRecord.id, // Use database ID instead of random ID
        fileName: data.path,
        originalName: file.name,
        url: urlData.publicUrl,
        size: file.size,
        type: file.type,
        uploadedAt: imageRecord.created_at,
        sessionId,
        analysisStatus: imageRecord.analysis_status,
        dbRecord: imageRecord // Include full database record
      };

      // Add to uploaded images
      setUploadedImages(prev => [...prev, imageMetadata]);

      // Audit log the upload with database record ID
      await auditLogger.logDataAccess('medical_image_upload', sessionId, true, {
        operation: 'upload_medical_image',
        session_id: sessionId,
        image_id: imageRecord.id,
        file_name: fileName,
        file_size: file.size,
        mime_type: file.type,
        storage_path: data.path,
        database_record_created: true
      });

      // Notify parent component
      onImageUploaded?.(imageMetadata);

      console.log('✅ Medical image uploaded successfully:', imageMetadata);

    } catch (error) {
      console.error('❌ Image upload failed:', error);
      
      // Audit log the failure
      await auditLogger.logDataAccess('medical_image_upload', sessionId, false, {
        operation: 'upload_medical_image',
        session_id: sessionId,
        error_message: error.message,
        file_name: file.name,
        file_size: file.size,
        database_record_created: false
      });

      onUploadError?.(error.message);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  /**
   * Remove uploaded image
   */
  const removeImage = async (imageId) => {
    try {
      const image = uploadedImages.find(img => img.id === imageId);
      if (!image) return;

      // Delete from Supabase Storage
      const { error } = await supabase.storage
        .from('medical-images')
        .remove([image.fileName]);

      if (error) {
        console.warn('Failed to delete image from storage:', error);
      }

      // Remove from local state
      setUploadedImages(prev => prev.filter(img => img.id !== imageId));

      console.log('🗑️ Medical image removed:', image.fileName);

    } catch (error) {
      console.error('❌ Failed to remove image:', error);
      onUploadError?.('Failed to remove image');
    }
  };

  return (
    <div className={`medical-image-upload ${className}`}>
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all ${
          dragActive 
            ? 'border-primary-500 bg-primary-50' 
            : disabled
              ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
              : 'border-gray-300 hover:border-primary-400 hover:bg-primary-50 cursor-pointer'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={Object.values(supportedFormats).join(',')}
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled || uploading || uploadedImages.length >= maxImages}
        />
        
        <div className="space-y-4">
          <div className="w-12 h-12 mx-auto bg-primary-100 rounded-full flex items-center justify-center">
            <Icon 
              name={uploading ? "Loader" : "Camera"} 
              size={20} 
              className={`text-primary-600 ${uploading ? "animate-spin" : ""}`}
            />
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              {uploading ? 'Uploading...' : 'Upload Medical Images'}
            </h4>
            <p className="text-sm text-gray-600">
              Drop images here or click to browse
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Supports JPG, PNG, WebP, BMP, TIFF up to 10MB each
            </p>
          </div>
          
          {uploading && (
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Uploaded Images */}
      {uploadedImages.length > 0 && (
        <div className="mt-4 space-y-3">
          <h5 className="text-sm font-medium text-gray-900">
            Uploaded Images ({uploadedImages.length}/{maxImages})
          </h5>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {uploadedImages.map((image) => (
              <div key={image.id} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={image.url}
                    alt={image.originalName}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Image Info */}
                <div className="mt-1">
                  <p className="text-xs text-gray-600 truncate">
                    {image.originalName}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(image.size / 1024 / 1024).toFixed(1)} MB
                  </p>
                </div>
                
                {/* Remove Button */}
                <button
                  onClick={() => removeImage(image.id)}
                  className="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="X" size={12} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Usage Info */}
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <Icon name="Info" size={16} className="text-blue-600 mt-0.5 mr-2" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Medical Image Guidelines</p>
            <ul className="text-xs space-y-1">
              <li>• Ensure good lighting and clear focus</li>
              <li>• Include a reference object for scale if relevant</li>
              <li>• Take multiple angles if helpful for diagnosis</li>
              <li>• Images are securely stored and HIPAA-compliant</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicalImageUpload;
