import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import ConsultationStatusIndicator from '../../components/ui/ConsultationStatusIndicator';
import ProfileCompletionPrompt from '../../components/ui/ProfileCompletionPrompt';
import SessionCard from './components/SessionCard';
import SessionFilters from './components/SessionFilters';
import SessionDetailModal from './components/SessionDetailModal';
import SessionStats from './components/SessionStats';

const SessionDashboardHistory = () => {
  const navigate = useNavigate();
  const [sessions, setSessions] = useState([]);
  const [filteredSessions, setFilteredSessions] = useState([]);
  const [selectedSession, setSelectedSession] = useState(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState({});
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'list'
  const [sortBy, setSortBy] = useState('date'); // 'date', 'duration', 'rating'
  const [sortOrder, setSortOrder] = useState('desc'); // 'asc' or 'desc'

  // Mock session data
  const mockSessions = [
    {
      id: 'sess_001',
      title: 'General Health Consultation',
      date: '2024-01-15',
      duration: '45 min',
      status: 'completed',
      agents: [
        { name: 'Dr. Sarah Chen', specialty: 'General Practitioner', voiceProfile: 'Professional Female, American' },
        { name: 'Dr. Michael Rodriguez', specialty: 'Cardiologist', voiceProfile: 'Warm Male, Spanish' }
      ],
      keyTopics: ['Blood Pressure', 'Diet Planning', 'Exercise Routine', 'Medication Review'],
      summary: `Comprehensive health assessment focusing on cardiovascular health and lifestyle modifications. Patient showed good understanding of recommendations.`,
      detailedSummary: `The consultation began with a thorough review of the patient's current health status and concerns about recent blood pressure readings. Dr. Sarah Chen conducted the initial assessment, gathering information about symptoms, lifestyle factors, and medical history.\n\nDr. Michael Rodriguez joined the consultation to provide specialized cardiovascular insights. Together, they developed a comprehensive care plan addressing both immediate concerns and long-term health optimization.\n\nKey findings included slightly elevated blood pressure readings that warrant monitoring and lifestyle modifications. The patient demonstrated excellent engagement and understanding throughout the session.`,
      recommendations: [
        'Monitor blood pressure daily for 2 weeks','Reduce sodium intake to less than 2300mg per day','Increase physical activity to 150 minutes moderate exercise weekly','Schedule follow-up appointment in 4 weeks'
      ],
      followUpActions: [
        'Blood pressure log to be reviewed at next appointment','Nutritionist consultation scheduled for next week','Medication adjustment if readings remain elevated'
      ],
      transcript: [
        {
          speaker: 'Dr. Sarah Chen',timestamp: '0:00',content: `Good morning! I'm Dr. Sarah Chen, your general practitioner for today's consultation. I see you've been having some concerns about your blood pressure readings. Can you tell me more about what you've been experiencing?`
        },
        {
          speaker: 'Patient',timestamp: '0:15',content: `Hi Dr. Chen. Yes, I've been checking my blood pressure at home and it's been consistently around 145/90. I'm a bit worried because it used to be much lower.`
        },
        {
          speaker: 'Dr. Sarah Chen',
          timestamp: '0:30',
          content: `I understand your concern. Those readings are indeed elevated. Let me bring in Dr. Rodriguez, our cardiologist, to provide additional insights. Dr. Rodriguez, could you join us?`
        },
        {
          speaker: 'Dr. Michael Rodriguez',
          timestamp: '0:45',
          content: `Hello! I'm Dr. Michael Rodriguez. I've been reviewing your readings, and while they are elevated, this is something we can definitely address with the right approach. Let's discuss your current lifestyle and see where we can make some positive changes.`
        }
      ],
      insights: {
        healthScore: 78,
        riskFactors: 2,
        patterns: [
          'Blood pressure elevation correlates with stress periods','Dietary sodium intake above recommended levels','Sedentary lifestyle contributing to cardiovascular risk'
        ],
        priorityRecommendations: [
          { priority: 'high', text: 'Implement daily blood pressure monitoring' },
          { priority: 'medium', text: 'Reduce dietary sodium intake' },
          { priority: 'low', text: 'Gradually increase physical activity' }
        ]
      },
      metrics: {
        accuracy: 94,
        interactions: 23,
        satisfaction: 4.8
      },
      hasTranscript: true,
      hasAudio: true,
      createdAt: '2024-01-15T09:00:00Z',currentAgent: 'Dr. Sarah Chen',playbackTime: '12:34'
    },
    {
      id: 'sess_002',title: 'Nutrition & Weight Management',date: '2024-01-12',duration: '32 min',status: 'completed',
      agents: [
        { name: 'Dr. Lisa Park', specialty: 'Nutritionist', voiceProfile: 'Friendly Female, Korean-American' }
      ],
      keyTopics: ['Weight Loss', 'Meal Planning', 'Metabolic Health'],
      summary: `Focused nutrition consultation addressing weight management goals and metabolic health optimization.`,
      detailedSummary: `Comprehensive nutritional assessment with personalized meal planning and metabolic health evaluation. Patient received detailed guidance on sustainable weight management strategies.`,
      recommendations: [
        'Follow Mediterranean-style diet pattern','Track daily food intake for 2 weeks','Increase protein intake to 1.2g per kg body weight'
      ],
      followUpActions: [
        'Weekly weigh-ins and progress photos','Meal prep session scheduled','Metabolic panel blood work in 6 weeks'
      ],
      transcript: [
        {
          speaker: 'Dr. Lisa Park',timestamp: '0:00',content: `Hello! I'm Dr. Lisa Park, your nutritionist. I'm excited to work with you on your weight management goals. Let's start by discussing your current eating patterns and what you'd like to achieve.`
        },
        {
          speaker: 'Patient',timestamp: '0:12',content: `Hi Dr. Park. I've been struggling with my weight for a while now. I want to lose about 20 pounds, but I've tried so many diets and nothing seems to stick.`
        }
      ],
      insights: {
        healthScore: 82,
        riskFactors: 1,
        patterns: [
          'Emotional eating patterns identified','Irregular meal timing affecting metabolism'
        ],
        priorityRecommendations: [
          { priority: 'high', text: 'Establish regular meal schedule' },
          { priority: 'medium', text: 'Address emotional eating triggers' }
        ]
      },
      metrics: {
        accuracy: 91,
        interactions: 18,
        satisfaction: 4.6
      },
      hasTranscript: true,
      hasAudio: true,
      createdAt: '2024-01-12T14:30:00Z'
    },
    {
      id: 'sess_003',title: 'Mental Health Check-in',date: '2024-01-10',duration: '28 min',status: 'active',
      agents: [
        { name: 'Dr. James Wilson', specialty: 'Psychiatrist', voiceProfile: 'Calm Male, British' }
      ],
      keyTopics: ['Anxiety Management', 'Sleep Quality', 'Stress Reduction'],
      summary: `Ongoing mental health consultation focusing on anxiety management and sleep improvement strategies.`,
      detailedSummary: `Active session addressing anxiety symptoms and sleep disturbances. Patient is responding well to cognitive behavioral techniques.`,
      recommendations: [
        'Continue daily mindfulness practice','Implement sleep hygiene routine','Practice progressive muscle relaxation'
      ],
      followUpActions: [
        'Daily mood tracking','Sleep diary for one week','Anxiety management app installation'
      ],
      transcript: [
        {
          speaker: 'Dr. James Wilson',timestamp: '0:00',content: `Good afternoon. I'm Dr. James Wilson. How have you been feeling since our last session? I'd like to check in on your anxiety levels and sleep patterns.`
        }
      ],
      insights: {
        healthScore: 75,
        riskFactors: 2,
        patterns: [
          'Anxiety peaks during evening hours','Sleep quality improving with routine'
        ],
        priorityRecommendations: [
          { priority: 'high', text: 'Maintain consistent sleep schedule' },
          { priority: 'medium', text: 'Continue anxiety management techniques' }
        ]
      },
      metrics: {
        accuracy: 89,
        interactions: 15,
        satisfaction: 4.7
      },
      hasTranscript: true,
      hasAudio: true,
      createdAt: '2024-01-10T16:00:00Z'
    },
    {
      id: 'sess_004',title: 'Dermatology Consultation',date: '2024-01-08',duration: '22 min',status: 'paused',
      agents: [
        { name: 'Dr. Emma Thompson', specialty: 'Dermatologist', voiceProfile: 'Professional Female, Australian' }
      ],
      keyTopics: ['Skin Care', 'Acne Treatment', 'Sun Protection'],
      summary: `Dermatology consultation paused for additional test results review.`,
      detailedSummary: `Consultation paused pending additional dermatological assessment and test results review.`,
      recommendations: [
        'Continue current skincare routine','Avoid sun exposure until follow-up','Document any changes in skin condition'
      ],
      followUpActions: [
        'Dermatology lab results review','Photo documentation of skin changes','Schedule in-person examination if needed'
      ],
      transcript: [
        {
          speaker: 'Dr. Emma Thompson',timestamp: '0:00',content: `Hello, I'm Dr. Emma Thompson, your dermatologist. I've reviewed your photos and I'd like to discuss your current skin concerns and treatment options.`
        }
      ],
      insights: {
        healthScore: 88,
        riskFactors: 1,
        patterns: [
          'Seasonal skin sensitivity patterns',
          'Positive response to current treatment'
        ],
        priorityRecommendations: [
          { priority: 'medium', text: 'Continue prescribed treatment regimen' },
          { priority: 'low', text: 'Monitor for any adverse reactions' }
        ]
      },
      metrics: {
        accuracy: 92,
        interactions: 12,
        satisfaction: 4.5
      },
      hasTranscript: true,
      hasAudio: true,
      createdAt: '2024-01-08T11:15:00Z'
    }
  ];

  // Mock stats data
  const mockStats = {
    totalSessions: 24,
    activeSessions: 2,
    completedSessions: 20,
    totalDuration: '18h 45m',
    averageRating: 4.6,
    topAgent: 'Dr. Sarah Chen',
    topAgentSpecialty: 'General Practitioner',
    topAgentRating: '4.8',
    sessionChange: '+15%',
    activeChange: '+2',
    completedChange: '+8',
    durationChange: '+3.2h'
  };

  useEffect(() => {
    // Simulate loading
    const loadSessions = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSessions(mockSessions);
      setFilteredSessions(mockSessions);
      setIsLoading(false);
    };

    loadSessions();
  }, []);

  useEffect(() => {
    // Apply filters and search
    let filtered = [...sessions];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(session =>
        session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        session.keyTopics.some(topic => topic.toLowerCase().includes(searchQuery.toLowerCase())) ||
        session.agents.some(agent => agent.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Status filter
    if (activeFilters.status && activeFilters.status !== 'all') {
      filtered = filtered.filter(session => session.status === activeFilters.status);
    }

    // Agent filter
    if (activeFilters.agents && activeFilters.agents.length > 0) {
      filtered = filtered.filter(session =>
        session.agents.some(agent =>
          activeFilters.agents.some(filterAgent =>
            agent.specialty.toLowerCase().includes(filterAgent)
          )
        )
      );
    }

    // Topic filter
    if (activeFilters.topics && activeFilters.topics.length > 0) {
      filtered = filtered.filter(session =>
        session.keyTopics.some(topic =>
          activeFilters.topics.includes(topic)
        )
      );
    }

    // Date range filter
    if (activeFilters.dateRange && (activeFilters.dateRange.start || activeFilters.dateRange.end)) {
      filtered = filtered.filter(session => {
        const sessionDate = new Date(session.date);
        const startDate = activeFilters.dateRange.start ? new Date(activeFilters.dateRange.start) : null;
        const endDate = activeFilters.dateRange.end ? new Date(activeFilters.dateRange.end) : null;

        if (startDate && sessionDate < startDate) return false;
        if (endDate && sessionDate > endDate) return false;
        return true;
      });
    }

    // Sort sessions
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'duration':
          aValue = parseInt(a.duration);
          bValue = parseInt(b.duration);
          break;
        case 'rating':
          aValue = a.metrics?.satisfaction || 0;
          bValue = b.metrics?.satisfaction || 0;
          break;
        default:
          aValue = new Date(a.date);
          bValue = new Date(b.date);
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredSessions(filtered);
  }, [sessions, searchQuery, activeFilters, sortBy, sortOrder]);

  const handleViewDetails = (sessionId) => {
    const session = sessions.find(s => s.id === sessionId);
    setSelectedSession(session);
    setIsDetailModalOpen(true);
  };

  const handleDownloadTranscript = async (sessionId) => {
    // Simulate download
    await new Promise(resolve => setTimeout(resolve, 1500));
    console.log(`Downloading transcript for session ${sessionId}`);
  };

  const handleDownloadAudio = async (sessionId) => {
    // Simulate download
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log(`Downloading audio for session ${sessionId}`);
  };

  const handleReplayAudio = (sessionId, isPlaying) => {
    console.log(`${isPlaying ? 'Playing' : 'Pausing'} audio for session ${sessionId}`);
  };

  const handleResumeSession = (sessionId) => {
    navigate('/voice-consultation-interface', { state: { resumeSessionId: sessionId } });
  };

  const handleShareSession = (sessionId) => {
    // Simulate sharing
    navigator.clipboard.writeText(`${window.location.origin}/session/${sessionId}`);
    console.log(`Shared session ${sessionId}`);
  };

  const handleStartNewConsultation = () => {
    navigate('/voice-consultation-interface');
  };

  const handleCompleteProfile = () => {
    navigate('/patient-profile-setup');
  };

  const activeSession = sessions.find(s => s.status === 'active');

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Status Indicators */}
      {activeSession && (
        <ConsultationStatusIndicator
          isActive={true}
          activeAgents={activeSession.agents}
          sessionProgress={65}
          consultationPhase="active"
          onEmergencyStop={() => console.log('Emergency stop')}
        />
      )}

      <ProfileCompletionPrompt
        profileCompletion={75}
        missingFields={[
          { key: 'emergency_contact', label: 'Emergency Contact' },
          { key: 'insurance_info', label: 'Insurance Information' }
        ]}
        onCompleteProfile={handleCompleteProfile}
        onDismiss={() => console.log('Profile prompt dismissed')}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-3xl font-bold text-text-primary font-heading">
              Session Dashboard
            </h1>
            <p className="text-text-secondary mt-2">
              Manage and review your consultation history
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/agent-customization-hub')}
              iconName="Settings"
              iconPosition="left"
            >
              Customize Agents
            </Button>
            <Button
              variant="primary"
              onClick={handleStartNewConsultation}
              iconName="Plus"
              iconPosition="left"
            >
              New Consultation
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <SessionStats stats={mockStats} className="mb-8" />

        {/* Filters */}
        <SessionFilters
          onFilterChange={setActiveFilters}
          onSearchChange={setSearchQuery}
          activeFilters={activeFilters}
          className="mb-6"
        />

        {/* View Controls */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div className="flex items-center space-x-4 mb-4 sm:mb-0">
            <span className="text-sm text-text-secondary">
              {filteredSessions.length} session{filteredSessions.length !== 1 ? 's' : ''}
            </span>
            
            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1 bg-secondary-50 rounded-lg p-1">
              <button
                onClick={() => setViewMode('cards')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-fast ${
                  viewMode === 'cards' ?'bg-surface text-text-primary shadow-minimal' :'text-text-secondary hover:text-text-primary'
                }`}
              >
                <Icon name="Grid3X3" size={16} className="inline mr-1" />
                Cards
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-fast ${
                  viewMode === 'list' ?'bg-surface text-text-primary shadow-minimal' :'text-text-secondary hover:text-text-primary'
                }`}
              >
                <Icon name="List" size={16} className="inline mr-1" />
                List
              </button>
            </div>
          </div>

          {/* Sort Controls */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-text-secondary">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 bg-surface border border-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="date">Date</option>
              <option value="duration">Duration</option>
              <option value="rating">Rating</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-1 hover:bg-secondary-50 rounded-lg transition-fast"
              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
            >
              <Icon 
                name={sortOrder === 'asc' ? 'ArrowUp' : 'ArrowDown'} 
                size={16} 
                color="var(--color-text-secondary)"
              />
            </button>
          </div>
        </div>

        {/* Sessions Content */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-breathe">
                <Icon name="Activity" size={24} color="var(--color-primary)" />
              </div>
              <p className="text-text-secondary">Loading your sessions...</p>
            </div>
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-secondary-50 rounded-full flex items-center justify-center mb-4 mx-auto">
              <Icon name="Calendar" size={32} color="var(--color-text-secondary)" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              No sessions found
            </h3>
            <p className="text-text-secondary mb-6">
              {searchQuery || Object.keys(activeFilters).length > 0
                ? 'Try adjusting your search or filters' :'Start your first consultation to see your session history here'
              }
            </p>
            <Button
              variant="primary"
              onClick={handleStartNewConsultation}
              iconName="Plus"
              iconPosition="left"
            >
              Start New Consultation
            </Button>
          </div>
        ) : (
          <div className={
            viewMode === 'cards' ?'grid grid-cols-1 lg:grid-cols-2 gap-6' :'space-y-4'
          }>
            {filteredSessions.map((session) => (
              <SessionCard
                key={session.id}
                session={session}
                onViewDetails={handleViewDetails}
                onDownloadTranscript={handleDownloadTranscript}
                onReplayAudio={handleReplayAudio}
                onResumeSession={handleResumeSession}
                className={viewMode === 'list' ? 'lg:flex lg:items-center lg:space-x-6' : ''}
              />
            ))}
          </div>
        )}
      </main>

      {/* Session Detail Modal */}
      <SessionDetailModal
        session={selectedSession}
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        onDownloadTranscript={handleDownloadTranscript}
        onDownloadAudio={handleDownloadAudio}
        onShareSession={handleShareSession}
      />
    </div>
  );
};

export default SessionDashboardHistory;