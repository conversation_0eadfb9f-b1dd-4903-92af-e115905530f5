import React from 'react';
import Button from 'components/ui/Button';
import { ArrowLeft, ArrowRight } from 'lucide-react';

const NavigationControls = ({
  onBack,
  onNext,
  isNextDisabled,
  isLoading,
  selectedLanguage = 'en'
}) => {
  const getLocalizedText = (key) => {
    const texts = {
      back: {
        en: 'Back',
        tw: 'San akyi',
        yo: 'Pada sẹhin',
        sw: 'Nyuma',
        af: 'Terug'
      },
      next: {
        en: 'Next',
        tw: 'Ɛdi hɔ',
        yo: 'Tókàn',
        sw: 'Ifuatayo',
        af: 'Volgende'
      }
    };
    return texts[key]?.[selectedLanguage] || texts[key]?.en || '';
  };

  return (
    <div className="flex gap-4 pt-8">
      {/* Back Button */}
      <Button
        variant="outline"
        size="lg"
        onClick={onBack}
        className="flex-1 min-h-12"
        icon={<ArrowLeft size={20} />}
        iconPosition="left"
      >
        {getLocalizedText('back')}
      </Button>

      {/* Next Button */}
      <Button
        variant="primary"
        size="lg"
        onClick={onNext}
        disabled={isNextDisabled}
        loading={isLoading}
        className="flex-1 min-h-12"
        icon={<ArrowRight size={20} />}
        iconPosition="right"
      >
        {getLocalizedText('next')}
      </Button>
    </div>
  );
};

export default NavigationControls;