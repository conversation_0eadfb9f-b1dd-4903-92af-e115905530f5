import React, { useState, useCallback } from 'react';
import { Eye, Download, Share2, RotateCcw } from 'lucide-react';

const SettingsPreview = ({ settings, onResetAll, onExport, onShare }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [renderError, setRenderError] = useState(false);

  const getPreviewStyles = useCallback(() => {
    try {
      const styles = {
        fontSize: '1rem',
        backgroundColor: '#FFFFFF',
        color: '#1E293B',
        filter: 'none',
        transition: 'all 0.3s ease-out',
        willChange: 'background-color, color, filter, font-size'
      };

      // Apply font size
      switch (settings?.fontSize) {
        case 'small':
          styles.fontSize = '0.875rem';
          break;
        case 'large':
          styles.fontSize = '1.125rem';
          break;
        case 'extra-large':
          styles.fontSize = '1.25rem';
          break;
        default:
          styles.fontSize = '1rem';
      }

      // Apply theme
      switch (settings?.visualTheme) {
        case 'dark':
          styles.backgroundColor = '#1E293B';
          styles.color = '#F8FAFC';
          break;
        case 'high-contrast':
          styles.backgroundColor = '#FFFFFF';
          styles.color = '#000000';
          break;
        case 'dark-high-contrast':
          styles.backgroundColor = '#000000';
          styles.color = '#FFFFFF';
          break;
        default:
          styles.backgroundColor = '#FFFFFF';
          styles.color = '#1E293B';
      }

      // Apply color blindness filters
      switch (settings?.colorBlindnessSupport) {
        case 'protanopia':
          styles.filter = 'sepia(1) saturate(0.8) hue-rotate(90deg)';
          break;
        case 'deuteranopia':
          styles.filter = 'sepia(1) saturate(0.6) hue-rotate(180deg)';
          break;
        case 'tritanopia':
          styles.filter = 'sepia(1) saturate(0.4) hue-rotate(270deg)';
          break;
        case 'monochrome':
          styles.filter = 'grayscale(1)';
          break;
        default:
          styles.filter = 'none';
      }

      return styles;
    } catch (error) {
      console.error('Preview styles error:', error);
      setRenderError(true);
      return {
        fontSize: '1rem',
        backgroundColor: '#FFFFFF',
        color: '#1E293B',
        filter: 'none'
      };
    }
  }, [settings]);

  const previewStyles = getPreviewStyles();

  const settingsSummary = [
    {
      category: 'Language',
      value: settings?.language ? 
        `${settings.language} ${settings?.region ? `(${settings.region})` : ''}` : 
        'English (US)'
    },
    {
      category: 'Voice',
      value: settings?.selectedVoice === 'agent-1' ? 'Dr. Sarah Chen' :
             settings?.selectedVoice === 'agent-2' ? 'Dr. Michael Rodriguez' :
             settings?.selectedVoice === 'agent-3' ? 'Dr. Aisha Patel' :
             settings?.selectedVoice === 'agent-4'? 'Dr. James Thompson' : 'Dr. Sarah Chen'
    },
    {
      category: 'Theme',
      value: settings?.visualTheme === 'dark' ? 'Dark Mode' :
             settings?.visualTheme === 'high-contrast' ? 'High Contrast' :
             settings?.visualTheme === 'dark-high-contrast'? 'Dark High Contrast' : 'Default Light'
    },
    {
      category: 'Font Size',
      value: settings?.fontSize === 'small' ? 'Small' :
             settings?.fontSize === 'large' ? 'Large' :
             settings?.fontSize === 'extra-large'? 'Extra Large' : 'Medium'
    }
  ];

  const handleExport = async () => {
    try {
      setIsExporting(true);
      
      const exportData = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        settings: settings,
        profile: {
          name: `VoiceHealth Accessibility Profile`,
          description: 'Exported accessibility settings for VoiceHealth AI platform'
        }
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `voicehealth-accessibility-profile-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      onExport?.();
    } catch (error) {
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleShare = async () => {
    try {
      const shareData = {
        settings: settings,
        summary: settingsSummary
      };

      if (navigator.share) {
        try {
          await navigator.share({
            title: 'VoiceHealth AI Accessibility Settings',
            text: 'My accessibility preferences for VoiceHealth AI',
            url: window.location.href
          });
        } catch (error) {
          // Fallback to copying to clipboard
          copyToClipboard(JSON.stringify(shareData, null, 2));
        }
      } else {
        // Fallback to copying to clipboard
        copyToClipboard(JSON.stringify(shareData, null, 2));
      }

      onShare?.();
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could show a toast notification here
      console.log('Settings copied to clipboard');
    }).catch(error => {
      console.error('Clipboard error:', error);
    });
  };

  if (renderError) {
    return (
      <div className="settings-preview-container">
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-secondary-50 rounded-lg">
              <Eye className="w-5 h-5 text-secondary-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary">Settings Preview</h3>
              <p className="text-sm text-error-600">Preview temporarily unavailable</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="settings-preview-container gpu-accelerated">
      <div className="px-6 py-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-secondary-50 rounded-lg">
            <Eye className="w-5 h-5 text-secondary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-text-primary">Settings Preview</h3>
            <p className="text-sm text-text-muted">Real-time preview of your accessibility settings</p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Live Preview */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-3">
            Interface Preview
          </label>
          <div 
            className="settings-preview-content border-2 border-dashed border-border rounded-lg p-6 gpu-accelerated"
            style={previewStyles}
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold">VoiceHealth AI Dashboard</h4>
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-current opacity-50 rounded-full"></div>
                  <div className="w-3 h-3 bg-current opacity-75 rounded-full"></div>
                  <div className="w-3 h-3 bg-current rounded-full"></div>
                </div>
              </div>
              <div className="space-y-2">
                <p className="opacity-80">
                  Hello! I'm your AI health assistant. How can I help you today?
                </p>
                <div className="flex space-x-2">
                  <div className="px-3 py-1 bg-current opacity-20 rounded-lg text-sm">
                    Start Consultation
                  </div>
                  <div className="px-3 py-1 bg-current opacity-20 rounded-lg text-sm">
                    View History
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Settings Summary */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-3">
            Current Settings Summary
          </label>
          <div className="bg-secondary-50 rounded-lg p-4 gpu-accelerated">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {settingsSummary.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">{item.category}:</span>
                  <span className="text-sm font-medium text-text-primary">{item.value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Accessibility Features Status */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-3">
            Active Accessibility Features
          </label>
          <div className="space-y-2">
            {settings?.screenReaderOptimized && (
              <div className="flex items-center space-x-2 text-sm text-success-600">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <span>Screen Reader Optimization</span>
              </div>
            )}
            {settings?.audioEnhancements?.volumeAmplification && (
              <div className="flex items-center space-x-2 text-sm text-success-600">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <span>Volume Amplification</span>
              </div>
            )}
            {settings?.inputMethods?.keyboard && (
              <div className="flex items-center space-x-2 text-sm text-success-600">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <span>Keyboard Navigation</span>
              </div>
            )}
            {settings?.inputMethods?.voice && (
              <div className="flex items-center space-x-2 text-sm text-success-600">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <span>Voice Commands</span>
              </div>
            )}
            {settings?.reduceMotion && (
              <div className="flex items-center space-x-2 text-sm text-success-600">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <span>Reduced Motion</span>
              </div>
            )}
            {settings?.motorAccessibility?.largeClickTargets && (
              <div className="flex items-center space-x-2 text-sm text-success-600">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <span>Large Click Targets</span>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3 pt-4 border-t border-border">
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors touch-target gpu-accelerated disabled:opacity-50"
          >
            <Download className="w-4 h-4" />
            <span>{isExporting ? 'Exporting...' : 'Export Profile'}</span>
          </button>
          
          <button
            onClick={handleShare}
            className="flex items-center space-x-2 px-4 py-2 bg-secondary-100 text-secondary-700 rounded-lg hover:bg-secondary-200 transition-colors touch-target gpu-accelerated"
          >
            <Share2 className="w-4 h-4" />
            <span>Share Settings</span>
          </button>
          
          <button
            onClick={onResetAll}
            className="flex items-center space-x-2 px-4 py-2 bg-error-50 text-error-600 rounded-lg hover:bg-error-100 transition-colors touch-target gpu-accelerated"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset All</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPreview;