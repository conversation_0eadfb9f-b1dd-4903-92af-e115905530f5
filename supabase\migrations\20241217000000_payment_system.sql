-- VoiceHealth AI Payment System Integration with Paystack
-- Location: supabase/migrations/20241217000000_payment_system.sql

-- 1. Payment-related Types
CREATE TYPE public.payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded', 'cancelled');
CREATE TYPE public.subscription_status AS ENUM ('active', 'inactive', 'cancelled', 'expired', 'trial');
CREATE TYPE public.payment_method AS ENUM ('card', 'bank', 'ussd', 'qr', 'bank_transfer');
CREATE TYPE public.currency_type AS ENUM ('NGN', 'USD', 'GHS', 'ZAR', 'KES');

-- 2. Subscription Plans Table
CREATE TABLE public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price_ngn DECIMAL(10,2) NOT NULL,
    price_usd DECIMAL(10,2) NOT NULL,
    duration_days INTEGER NOT NULL,
    consultation_credits INTEGER DEFAULT 0, -- 0 means unlimited
    features JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT true,
    is_popular BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 3. User Subscriptions Table
CREATE TABLE public.user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES public.subscription_plans(id),
    status public.subscription_status DEFAULT 'active'::public.subscription_status,
    started_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ NOT NULL,
    is_trial BOOLEAN DEFAULT false,
    consultation_credits_remaining INTEGER DEFAULT 0,
    auto_renew BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 4. Payment Transactions Table
CREATE TABLE public.payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES public.user_subscriptions(id),
    consultation_session_id UUID REFERENCES public.consultation_sessions(id),
    paystack_reference TEXT UNIQUE NOT NULL,
    paystack_transaction_id TEXT,
    amount DECIMAL(10,2) NOT NULL,
    currency public.currency_type NOT NULL,
    payment_method public.payment_method,
    status public.payment_status DEFAULT 'pending'::public.payment_status,
    gateway_response JSONB,
    payment_date TIMESTAMPTZ,
    verification_date TIMESTAMPTZ,
    refund_date TIMESTAMPTZ,
    refund_amount DECIMAL(10,2),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 5. Payment Methods Table (for saved cards)
CREATE TABLE public.user_payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    paystack_authorization_code TEXT NOT NULL,
    card_type TEXT,
    last_four TEXT,
    exp_month TEXT,
    exp_year TEXT,
    bank TEXT,
    brand TEXT,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 6. Payment Webhooks Log
CREATE TABLE public.payment_webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type TEXT NOT NULL,
    paystack_event_id TEXT UNIQUE,
    reference TEXT,
    status TEXT,
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 7. Add payment_required column to consultation_sessions
ALTER TABLE public.consultation_sessions 
ADD COLUMN payment_required BOOLEAN DEFAULT false,
ADD COLUMN consultation_fee DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN payment_status public.payment_status DEFAULT 'pending'::public.payment_status;

-- 8. Essential Indexes
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_expires_at ON public.user_subscriptions(expires_at);
CREATE INDEX idx_payment_transactions_user_id ON public.payment_transactions(user_id);
CREATE INDEX idx_payment_transactions_reference ON public.payment_transactions(paystack_reference);
CREATE INDEX idx_payment_transactions_status ON public.payment_transactions(status);
CREATE INDEX idx_payment_transactions_created_at ON public.payment_transactions(created_at);
CREATE INDEX idx_user_payment_methods_user_id ON public.user_payment_methods(user_id);
CREATE INDEX idx_payment_webhooks_event_id ON public.payment_webhooks(paystack_event_id);
CREATE INDEX idx_payment_webhooks_processed ON public.payment_webhooks(processed);

-- 9. Enable Row Level Security
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_webhooks ENABLE ROW LEVEL SECURITY;

-- 10. RLS Policies

-- Subscription Plans (Public read access)
CREATE POLICY "public_read_subscription_plans" ON public.subscription_plans FOR SELECT
TO authenticated USING (is_active = true);

CREATE POLICY "admins_manage_subscription_plans" ON public.subscription_plans FOR ALL
USING (public.is_admin()) WITH CHECK (public.is_admin());

-- User Subscriptions (Users own their subscriptions)
CREATE POLICY "users_own_subscriptions" ON public.user_subscriptions FOR ALL
USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "admins_view_all_subscriptions" ON public.user_subscriptions FOR SELECT
USING (public.is_admin());

-- Payment Transactions (Users own their transactions)
CREATE POLICY "users_own_transactions" ON public.payment_transactions FOR ALL
USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "admins_view_all_transactions" ON public.payment_transactions FOR SELECT
USING (public.is_admin());

-- User Payment Methods (Users own their payment methods)
CREATE POLICY "users_own_payment_methods" ON public.user_payment_methods FOR ALL
USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Payment Webhooks (Admin only)
CREATE POLICY "admins_manage_webhooks" ON public.payment_webhooks FOR ALL
USING (public.is_admin()) WITH CHECK (public.is_admin());

-- 11. Helper Functions

-- Function to check if user has active subscription
CREATE OR REPLACE FUNCTION public.user_has_active_subscription(user_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_subscriptions
    WHERE user_id = user_uuid 
    AND status = 'active'::public.subscription_status
    AND expires_at > CURRENT_TIMESTAMP
)
$$;

-- Function to get user's consultation credits
CREATE OR REPLACE FUNCTION public.get_user_consultation_credits(user_uuid UUID)
RETURNS INTEGER
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT COALESCE(
    (SELECT consultation_credits_remaining 
     FROM public.user_subscriptions 
     WHERE user_id = user_uuid 
     AND status = 'active'::public.subscription_status 
     AND expires_at > CURRENT_TIMESTAMP 
     ORDER BY expires_at DESC 
     LIMIT 1), 0
)
$$;

-- Function to deduct consultation credit
CREATE OR REPLACE FUNCTION public.deduct_consultation_credit(user_uuid UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_credits INTEGER;
    subscription_row RECORD;
BEGIN
    -- Get active subscription with remaining credits
    SELECT * INTO subscription_row
    FROM public.user_subscriptions
    WHERE user_id = user_uuid 
    AND status = 'active'::public.subscription_status
    AND expires_at > CURRENT_TIMESTAMP
    AND consultation_credits_remaining > 0
    ORDER BY expires_at DESC
    LIMIT 1;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Deduct one credit
    UPDATE public.user_subscriptions
    SET consultation_credits_remaining = consultation_credits_remaining - 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = subscription_row.id;

    RETURN TRUE;
END;
$$;

-- 12. Triggers for automatic updates

-- Update subscription status when expired
CREATE OR REPLACE FUNCTION public.update_expired_subscriptions()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.user_subscriptions
    SET status = 'expired'::public.subscription_status,
        updated_at = CURRENT_TIMESTAMP
    WHERE expires_at <= CURRENT_TIMESTAMP
    AND status = 'active'::public.subscription_status;
    
    RETURN NULL;
END;
$$;

-- 13. Default Subscription Plans
INSERT INTO public.subscription_plans (name, description, price_ngn, price_usd, duration_days, consultation_credits, features, is_popular)
VALUES
    ('Basic Plan', 'Perfect for occasional consultations', 5000.00, 12.00, 30, 3, 
     '["3 consultations per month", "Basic AI agents", "Text transcripts", "Email support"]'::jsonb, false),
    
    ('Premium Plan', 'Most popular for regular health monitoring', 12000.00, 28.00, 30, 10, 
     '["10 consultations per month", "All AI specialists", "Audio recordings", "Priority support", "Health insights"]'::jsonb, true),
    
    ('Professional Plan', 'Unlimited access for comprehensive care', 25000.00, 60.00, 30, 0, 
     '["Unlimited consultations", "All premium features", "Personal health dashboard", "24/7 priority support", "Advanced analytics"]'::jsonb, false),
    
    ('Annual Basic', 'Basic plan with annual discount', 50000.00, 120.00, 365, 36, 
     '["3 consultations per month", "Basic AI agents", "Text transcripts", "Email support", "2 months free"]'::jsonb, false),
    
    ('Annual Premium', 'Premium plan with annual discount', 120000.00, 280.00, 365, 120, 
     '["10 consultations per month", "All AI specialists", "Audio recordings", "Priority support", "Health insights", "2 months free"]'::jsonb, false);

-- Final success message
SELECT 'VoiceHealth AI Payment System Setup Complete with Paystack Integration! 💳' as status;