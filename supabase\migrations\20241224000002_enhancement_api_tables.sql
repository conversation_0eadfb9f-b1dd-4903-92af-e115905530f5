-- Enhancement API Tables Migration
-- Creates tables for vocal analysis, educational content, and feedback
-- Date: 2024-12-24
-- Purpose: Support new enhancement API endpoints

-- Vocal Analysis Results Table
CREATE TABLE IF NOT EXISTS public.vocal_analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    sentiment TEXT NOT NULL CHECK (sentiment IN ('positive', 'negative', 'neutral')),
    arousal DECIMAL(3,2) CHECK (arousal >= 0 AND arousal <= 1),
    valence DECIMAL(3,2) CHECK (valence >= -1 AND valence <= 1),
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    emotional_cues TEXT[] DEFAULT '{}',
    intensity TEXT CHECK (intensity IN ('low', 'medium', 'high')),
    stability DECIMAL(3,2) CHECK (stability >= 0 AND stability <= 1),
    analysis_results JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Educational Content Table
CREATE TABLE IF NOT EXISTS public.educational_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    content_type TEXT NOT NULL CHECK (content_type IN ('explanation', 'instructions', 'prevention', 'lifestyle', 'medication', 'warning_signs')),
    target_audience TEXT NOT NULL CHECK (target_audience IN ('patient', 'family', 'caregiver')),
    cultural_adaptations TEXT[] DEFAULT '{}',
    language_code TEXT DEFAULT 'en',
    health_literacy_level TEXT NOT NULL CHECK (health_literacy_level IN ('basic', 'intermediate', 'advanced')),
    estimated_reading_time INTEGER DEFAULT 0,
    key_takeaways TEXT[] DEFAULT '{}',
    action_items TEXT[] DEFAULT '{}',
    resources JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Educational Content Feedback Table
CREATE TABLE IF NOT EXISTS public.educational_content_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES public.educational_content(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES public.consultation_sessions(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    helpful BOOLEAN NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_vocal_analysis_session_id ON public.vocal_analysis_results(session_id);
CREATE INDEX IF NOT EXISTS idx_vocal_analysis_user_id ON public.vocal_analysis_results(user_id);
CREATE INDEX IF NOT EXISTS idx_vocal_analysis_created_at ON public.vocal_analysis_results(created_at);

CREATE INDEX IF NOT EXISTS idx_educational_content_session_id ON public.educational_content(session_id);
CREATE INDEX IF NOT EXISTS idx_educational_content_user_id ON public.educational_content(user_id);
CREATE INDEX IF NOT EXISTS idx_educational_content_type ON public.educational_content(content_type);
CREATE INDEX IF NOT EXISTS idx_educational_content_created_at ON public.educational_content(created_at);

CREATE INDEX IF NOT EXISTS idx_educational_feedback_content_id ON public.educational_content_feedback(content_id);
CREATE INDEX IF NOT EXISTS idx_educational_feedback_user_id ON public.educational_content_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_educational_feedback_session_id ON public.educational_content_feedback(session_id);

-- Enable Row Level Security
ALTER TABLE public.vocal_analysis_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.educational_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.educational_content_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for vocal_analysis_results
CREATE POLICY "Users can view their own vocal analysis results" ON public.vocal_analysis_results
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own vocal analysis results" ON public.vocal_analysis_results
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Healthcare providers can view vocal analysis for their sessions" ON public.vocal_analysis_results
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.consultation_sessions cs
            JOIN public.user_profiles up ON up.id = auth.uid()
            WHERE cs.id = vocal_analysis_results.session_id
            AND up.role IN ('provider', 'admin')
        )
    );

-- RLS Policies for educational_content
CREATE POLICY "Users can view their own educational content" ON public.educational_content
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own educational content" ON public.educational_content
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Healthcare providers can view educational content for their sessions" ON public.educational_content
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.consultation_sessions cs
            JOIN public.user_profiles up ON up.id = auth.uid()
            WHERE cs.id = educational_content.session_id
            AND up.role IN ('provider', 'admin')
        )
    );

-- RLS Policies for educational_content_feedback
CREATE POLICY "Users can view their own educational content feedback" ON public.educational_content_feedback
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own educational content feedback" ON public.educational_content_feedback
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Healthcare providers can view feedback for their sessions" ON public.educational_content_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.consultation_sessions cs
            JOIN public.user_profiles up ON up.id = auth.uid()
            WHERE cs.id = educational_content_feedback.session_id
            AND up.role IN ('provider', 'admin')
        )
    );

-- Create RPC function for user image statistics
CREATE OR REPLACE FUNCTION public.get_user_image_stats(p_user_id UUID)
RETURNS TABLE (
    total_images BIGINT,
    pending_analysis BIGINT,
    completed_analysis BIGINT,
    failed_analysis BIGINT,
    total_sessions BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_images,
        COUNT(*) FILTER (WHERE analysis_status = 'pending') as pending_analysis,
        COUNT(*) FILTER (WHERE analysis_status = 'completed') as completed_analysis,
        COUNT(*) FILTER (WHERE analysis_status = 'failed') as failed_analysis,
        COUNT(DISTINCT session_id) as total_sessions
    FROM public.medical_images
    WHERE user_id = p_user_id
    AND analysis_status != 'deleted';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger function for updating timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_vocal_analysis_results_updated_at
    BEFORE UPDATE ON public.vocal_analysis_results
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_educational_content_updated_at
    BEFORE UPDATE ON public.educational_content
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.vocal_analysis_results IS 'Stores vocal tone and emotional analysis results from consultations';
COMMENT ON TABLE public.educational_content IS 'Stores personalized educational content generated for patients';
COMMENT ON TABLE public.educational_content_feedback IS 'Stores patient feedback on educational content';

COMMENT ON COLUMN public.vocal_analysis_results.sentiment IS 'Overall sentiment: positive, negative, or neutral';
COMMENT ON COLUMN public.vocal_analysis_results.arousal IS 'Emotional arousal level (0-1 scale)';
COMMENT ON COLUMN public.vocal_analysis_results.valence IS 'Emotional valence (-1 to 1 scale)';
COMMENT ON COLUMN public.vocal_analysis_results.confidence IS 'Analysis confidence score (0-1 scale)';
COMMENT ON COLUMN public.vocal_analysis_results.emotional_cues IS 'Array of detected emotional cues';

COMMENT ON COLUMN public.educational_content.content_type IS 'Type of educational content: explanation, instructions, prevention, etc.';
COMMENT ON COLUMN public.educational_content.target_audience IS 'Intended audience: patient, family, or caregiver';
COMMENT ON COLUMN public.educational_content.health_literacy_level IS 'Content adapted for basic, intermediate, or advanced health literacy';
COMMENT ON COLUMN public.educational_content.cultural_adaptations IS 'Array of cultural considerations applied';
COMMENT ON COLUMN public.educational_content.resources IS 'JSON array of additional resources and contacts';

COMMENT ON COLUMN public.educational_content_feedback.rating IS 'User rating from 1-5 stars';
COMMENT ON COLUMN public.educational_content_feedback.helpful IS 'Boolean indicating if content was helpful';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.vocal_analysis_results TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.educational_content TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.educational_content_feedback TO authenticated;

GRANT EXECUTE ON FUNCTION public.get_user_image_stats(UUID) TO authenticated;
