-- Verification Script for Enhanced User Profiles Migration
-- Run this after applying the migration to verify everything works correctly

-- 1. Check if new columns exist
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
  AND table_schema = 'public'
  AND column_name IN ('country', 'city', 'occupation', 'insurance_status', 'emergency_contact')
ORDER BY column_name;

-- 2. Check if indexes were created
SELECT 
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename = 'user_profiles' 
  AND schemaname = 'public'
  AND indexname LIKE 'idx_user_profiles_%'
ORDER BY indexname;

-- 3. Check if functions exist
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('calculate_profile_completion', 'get_user_geographic_context')
ORDER BY routine_name;

-- 4. Check if triggers exist
SELECT 
  trigger_name,
  event_manipulation,
  action_timing,
  action_statement
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
  AND event_object_table = 'user_profiles'
ORDER BY trigger_name;

-- 5. Test profile completion calculation
DO $$
DECLARE
  test_user_id UUID;
  completion_before INTEGER;
  completion_after INTEGER;
BEGIN
  -- Get a test user
  SELECT id INTO test_user_id
  FROM public.user_profiles
  LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Calculate completion before update
    SELECT public.calculate_profile_completion(test_user_id) INTO completion_before;
    RAISE NOTICE 'Profile completion before: %', completion_before;
    
    -- Update with some demographic data
    UPDATE public.user_profiles
    SET 
      country = 'NG',
      city = 'Lagos',
      occupation = 'Doctor'
    WHERE id = test_user_id;
    
    -- Calculate completion after update
    SELECT public.calculate_profile_completion(test_user_id) INTO completion_after;
    RAISE NOTICE 'Profile completion after: %', completion_after;
    
    -- Check if completion percentage was automatically updated
    SELECT profile_completion_percentage INTO completion_after
    FROM public.user_profiles
    WHERE id = test_user_id;
    RAISE NOTICE 'Auto-calculated completion: %', completion_after;
    
  ELSE
    RAISE NOTICE 'No users found for testing';
  END IF;
END $$;

-- 6. Test geographic context function
DO $$
DECLARE
  test_user_id UUID;
  geo_context RECORD;
BEGIN
  -- Get a test user with country data
  SELECT id INTO test_user_id
  FROM public.user_profiles
  WHERE country IS NOT NULL
  LIMIT 1;
  
  IF test_user_id IS NOT NULL THEN
    -- Test geographic context function
    SELECT * INTO geo_context
    FROM public.get_user_geographic_context(test_user_id)
    LIMIT 1;
    
    RAISE NOTICE 'Geographic context - Country: %, City: %', 
      geo_context.user_country, geo_context.user_city;
  ELSE
    RAISE NOTICE 'No users with country data found for geographic context testing';
  END IF;
END $$;

-- 7. Check RLS policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'user_profiles'
ORDER BY policyname;

-- 8. Sample data verification
SELECT 
  id,
  full_name,
  country,
  city,
  occupation,
  insurance_status,
  emergency_contact,
  profile_completion_percentage
FROM public.user_profiles
WHERE country IS NOT NULL OR city IS NOT NULL
LIMIT 5;

-- Success message
DO $$
BEGIN
  RAISE NOTICE '🎉 Enhanced User Profiles Migration Verification Complete!';
  RAISE NOTICE 'Check the output above to ensure all components are working correctly.';
END $$;
