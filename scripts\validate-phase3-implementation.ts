#!/usr/bin/env tsx

/**
 * PHASE 3 IMPLEMENTATION VALIDATION SCRIPT
 * 
 * Comprehensive validation of Phase 3: Testing & Validation implementation.
 * Verifies that all components are working correctly and integration is complete.
 * 
 * Validation includes:
 * - Integration test coverage validation
 * - Performance monitoring integration verification
 * - API documentation alignment check
 * - Cross-service error handling validation
 * - Regional configuration testing
 * - End-to-end workflow validation
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import path from 'path';

// ANSI color codes for output formatting
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

interface ValidationResult {
  name: string;
  passed: boolean;
  score?: number;
  error?: string;
  details?: string;
}

class Phase3Validator {
  private results: ValidationResult[] = [];
  private targetCoverage = 90;

  constructor() {
    console.log(`${colors.cyan}🧪 VoiceHealth AI - Phase 3 Implementation Validation${colors.reset}`);
    console.log(`${colors.cyan}======================================================${colors.reset}\n`);
  }

  /**
   * Run all Phase 3 validation checks
   */
  async runValidation(): Promise<void> {
    try {
      await this.validateIntegrationTests();
      await this.validatePerformanceMonitoring();
      await this.validateAPIDocumentation();
      await this.validateErrorHandling();
      await this.validateRegionalConfigurations();
      await this.validateEndToEndWorkflows();
      await this.validateTestCoverage();
      
      this.printResults();
      
      const failedTests = this.results.filter(r => !r.passed);
      if (failedTests.length > 0) {
        console.log(`\n${colors.red}❌ ${failedTests.length} validation(s) failed${colors.reset}`);
        process.exit(1);
      } else {
        console.log(`\n${colors.green}✅ All Phase 3 validations passed successfully!${colors.reset}`);
        process.exit(0);
      }
    } catch (error) {
      console.error(`${colors.red}❌ Validation failed with error:${colors.reset}`, error);
      process.exit(1);
    }
  }

  /**
   * Validate integration test implementation
   */
  private async validateIntegrationTests(): Promise<void> {
    console.log(`${colors.blue}🔗 Validating Integration Tests...${colors.reset}`);
    
    const requiredTestFiles = [
      'src/tests/integration/cross-phase-integration.test.ts',
      'src/tests/integration/end-to-end-workflows.test.ts',
      'src/tests/integration/critical-fixes-validation.test.ts'
    ];

    const missingFiles = requiredTestFiles.filter(file => 
      !existsSync(path.join(process.cwd(), file))
    );

    if (missingFiles.length > 0) {
      this.addResult('Integration Test Files', false, 
        `Missing test files: ${missingFiles.join(', ')}`);
      return;
    }

    // Check test content quality
    try {
      const crossPhaseTest = readFileSync(
        path.join(process.cwd(), 'src/tests/integration/cross-phase-integration.test.ts'), 
        'utf8'
      );

      const requiredTestCases = [
        'Phase 1 → Phase 2 Integration',
        'Phase 1 → Phase 3 Integration', 
        'Phase 2 → Phase 3 Integration',
        'Authentication with Monitoring',
        'Encryption with Security Monitoring'
      ];

      const missingTestCases = requiredTestCases.filter(testCase => 
        !crossPhaseTest.includes(testCase)
      );

      if (missingTestCases.length > 0) {
        this.addResult('Integration Test Coverage', false,
          `Missing test cases: ${missingTestCases.join(', ')}`);
      } else {
        this.addResult('Integration Test Coverage', true,
          'All required integration test cases present');
      }

      // Check for performance monitoring in tests
      if (crossPhaseTest.includes('performanceValidationService.recordMetric')) {
        this.addResult('Performance Monitoring in Tests', true,
          'Performance monitoring integration verified in tests');
      } else {
        this.addResult('Performance Monitoring in Tests', false,
          'Performance monitoring not properly tested');
      }

    } catch (error) {
      this.addResult('Integration Test Content', false, (error as Error).message);
    }
  }

  /**
   * Validate performance monitoring integration
   */
  private async validatePerformanceMonitoring(): Promise<void> {
    console.log(`${colors.blue}📊 Validating Performance Monitoring Integration...${colors.reset}`);
    
    try {
      // Check if performance monitoring wrapper exists
      const wrapperPath = path.join(process.cwd(), 'src/utils/performanceMonitoringWrapper.ts');
      if (!existsSync(wrapperPath)) {
        this.addResult('Performance Monitoring Wrapper', false, 'Wrapper file not found');
        return;
      }

      const wrapperContent = readFileSync(wrapperPath, 'utf8');
      
      // Check for required components
      const requiredComponents = [
        'MonitorPerformance',
        'wrapWithPerformanceMonitoring',
        'MonitoringConfigurations',
        'monitorEmergencyProtocol',
        'monitorCulturalAdaptation'
      ];

      const missingComponents = requiredComponents.filter(component => 
        !wrapperContent.includes(component)
      );

      if (missingComponents.length > 0) {
        this.addResult('Performance Monitoring Components', false,
          `Missing components: ${missingComponents.join(', ')}`);
      } else {
        this.addResult('Performance Monitoring Components', true,
          'All required monitoring components present');
      }

      // Check AI Orchestrator integration
      const orchestratorPath = path.join(process.cwd(), 'src/services/aiOrchestrator.ts');
      if (existsSync(orchestratorPath)) {
        const orchestratorContent = readFileSync(orchestratorPath, 'utf8');
        
        if (orchestratorContent.includes('@MonitorPerformance')) {
          this.addResult('AI Orchestrator Monitoring', true,
            'Performance monitoring decorators applied to AI Orchestrator');
        } else {
          this.addResult('AI Orchestrator Monitoring', false,
            'Performance monitoring not applied to AI Orchestrator methods');
        }
      }

    } catch (error) {
      this.addResult('Performance Monitoring Integration', false, (error as Error).message);
    }
  }

  /**
   * Validate API documentation alignment
   */
  private async validateAPIDocumentation(): Promise<void> {
    console.log(`${colors.blue}📚 Validating API Documentation Alignment...${colors.reset}`);
    
    try {
      const apiDocPath = path.join(process.cwd(), 'docs/api/voicehealth-ai-api-specification.md');
      if (!existsSync(apiDocPath)) {
        this.addResult('API Documentation', false, 'API documentation file not found');
        return;
      }

      const apiDoc = readFileSync(apiDocPath, 'utf8');
      
      // Check for updated response formats
      const requiredUpdates = [
        'conditionSpecificRisks',
        'regionalRiskFactors',
        'culturalConsiderations',
        'assessmentMetadata',
        'processingTime',
        'culturalAccuracy',
        'validationStatus'
      ];

      const missingUpdates = requiredUpdates.filter(update => 
        !apiDoc.includes(update)
      );

      if (missingUpdates.length > 0) {
        this.addResult('API Documentation Updates', false,
          `Missing updated fields: ${missingUpdates.join(', ')}`);
      } else {
        this.addResult('API Documentation Updates', true,
          'API documentation aligned with service implementations');
      }

      // Check for emergency protocol documentation
      if (apiDoc.includes('< 2000ms') && apiDoc.includes('emergency_protocol')) {
        this.addResult('Emergency Protocol Documentation', true,
          'Emergency protocol timing requirements documented');
      } else {
        this.addResult('Emergency Protocol Documentation', false,
          'Emergency protocol requirements not properly documented');
      }

    } catch (error) {
      this.addResult('API Documentation Validation', false, (error as Error).message);
    }
  }

  /**
   * Validate standardized error handling
   */
  private async validateErrorHandling(): Promise<void> {
    console.log(`${colors.blue}⚠️  Validating Standardized Error Handling...${colors.reset}`);
    
    try {
      const errorHandlerPath = path.join(process.cwd(), 'src/utils/standardErrorHandler.ts');
      if (!existsSync(errorHandlerPath)) {
        this.addResult('Standard Error Handler', false, 'Error handler file not found');
        return;
      }

      const errorHandlerContent = readFileSync(errorHandlerPath, 'utf8');
      
      // Check for required error handling components
      const requiredComponents = [
        'ErrorCode',
        'ErrorSeverity',
        'StandardError',
        'handleServiceError',
        'handleEmergencyError',
        'handleCulturalError'
      ];

      const missingComponents = requiredComponents.filter(component => 
        !errorHandlerContent.includes(component)
      );

      if (missingComponents.length > 0) {
        this.addResult('Error Handling Components', false,
          `Missing components: ${missingComponents.join(', ')}`);
      } else {
        this.addResult('Error Handling Components', true,
          'All required error handling components present');
      }

      // Check for HIPAA compliance in error handling
      if (errorHandlerContent.includes('hipaaCompliant') && 
          errorHandlerContent.includes('sanitizeErrorMessage')) {
        this.addResult('HIPAA Compliant Error Handling', true,
          'Error handling includes HIPAA compliance features');
      } else {
        this.addResult('HIPAA Compliant Error Handling', false,
          'HIPAA compliance not properly implemented in error handling');
      }

      // Check for cultural sensitivity in error handling
      if (errorHandlerContent.includes('localizeCulturalErrorMessage')) {
        this.addResult('Cultural Error Handling', true,
          'Cultural sensitivity implemented in error handling');
      } else {
        this.addResult('Cultural Error Handling', false,
          'Cultural sensitivity missing in error handling');
      }

    } catch (error) {
      this.addResult('Error Handling Validation', false, (error as Error).message);
    }
  }

  /**
   * Validate regional configurations
   */
  private async validateRegionalConfigurations(): Promise<void> {
    console.log(`${colors.blue}🌍 Validating Regional Configurations...${colors.reset}`);
    
    const requiredRegions = ['ghana', 'kenya', 'nigeria', 'south-africa', 'ethiopia'];
    const configPath = path.join(process.cwd(), 'src/config/regions');
    
    if (!existsSync(configPath)) {
      this.addResult('Regional Configuration Directory', false, 'Config directory not found');
      return;
    }

    const missingConfigs = requiredRegions.filter(region => 
      !existsSync(path.join(configPath, `${region}.json`))
    );

    if (missingConfigs.length > 0) {
      this.addResult('Regional Configuration Files', false,
        `Missing configs: ${missingConfigs.join(', ')}`);
    } else {
      this.addResult('Regional Configuration Files', true,
        'All required regional configuration files present');
    }

    // Check regional test file
    const regionalTestPath = path.join(process.cwd(), 'src/tests/regional/regional-configuration-validation.test.ts');
    if (existsSync(regionalTestPath)) {
      const testContent = readFileSync(regionalTestPath, 'utf8');
      
      const requiredCultures = ['akan', 'kikuyu', 'yoruba', 'zulu', 'amhara'];
      const missingCultureTests = requiredCultures.filter(culture => 
        !testContent.includes(culture)
      );

      if (missingCultureTests.length > 0) {
        this.addResult('Regional Culture Tests', false,
          `Missing culture tests: ${missingCultureTests.join(', ')}`);
      } else {
        this.addResult('Regional Culture Tests', true,
          'All required cultural validation tests present');
      }
    } else {
      this.addResult('Regional Test File', false, 'Regional configuration test file not found');
    }
  }

  /**
   * Validate end-to-end workflows
   */
  private async validateEndToEndWorkflows(): Promise<void> {
    console.log(`${colors.blue}🔄 Validating End-to-End Workflows...${colors.reset}`);
    
    try {
      const e2eTestPath = path.join(process.cwd(), 'src/tests/integration/end-to-end-workflows.test.ts');
      if (!existsSync(e2eTestPath)) {
        this.addResult('End-to-End Test File', false, 'E2E test file not found');
        return;
      }

      const e2eContent = readFileSync(e2eTestPath, 'utf8');
      
      // Check for complete workflow tests
      const requiredWorkflows = [
        'Complete Patient Consultation Workflow',
        'Provider Authentication',
        'Cultural Content Validation',
        'Clinical Documentation Generation',
        'Advanced Risk Stratification',
        'Data Encryption for HIPAA Compliance'
      ];

      const missingWorkflows = requiredWorkflows.filter(workflow => 
        !e2eContent.includes(workflow)
      );

      if (missingWorkflows.length > 0) {
        this.addResult('End-to-End Workflows', false,
          `Missing workflows: ${missingWorkflows.join(', ')}`);
      } else {
        this.addResult('End-to-End Workflows', true,
          'All required end-to-end workflows present');
      }

      // Check for cultural adaptation in workflows
      if (e2eContent.includes('culturalAdaptations') && 
          e2eContent.includes('traditional_medicine')) {
        this.addResult('Cultural Workflow Integration', true,
          'Cultural adaptations properly integrated in workflows');
      } else {
        this.addResult('Cultural Workflow Integration', false,
          'Cultural adaptations missing in workflow tests');
      }

      // Check for performance validation in workflows
      if (e2eContent.includes('totalWorkflowTime') && 
          e2eContent.includes('toBeLessThan(15000)')) {
        this.addResult('Workflow Performance Validation', true,
          'Performance validation included in workflow tests');
      } else {
        this.addResult('Workflow Performance Validation', false,
          'Performance validation missing in workflow tests');
      }

    } catch (error) {
      this.addResult('End-to-End Workflow Validation', false, (error as Error).message);
    }
  }

  /**
   * Validate test coverage
   */
  private async validateTestCoverage(): Promise<void> {
    console.log(`${colors.blue}📈 Validating Test Coverage...${colors.reset}`);
    
    try {
      // Try to run coverage analysis
      const coverageOutput = execSync('npm run test:coverage -- --reporter=json', { 
        stdio: 'pipe',
        timeout: 60000 
      }).toString();

      const coverage = JSON.parse(coverageOutput);
      const totalCoverage = coverage.total?.lines?.pct || 0;

      if (totalCoverage >= this.targetCoverage) {
        this.addResult('Test Coverage', true, 
          `Coverage: ${totalCoverage}% (target: ${this.targetCoverage}%)`, totalCoverage);
      } else {
        this.addResult('Test Coverage', false,
          `Coverage: ${totalCoverage}% (below target: ${this.targetCoverage}%)`, totalCoverage);
      }

      // Check integration test coverage specifically
      const integrationCoverage = coverage.integration?.lines?.pct || 0;
      if (integrationCoverage >= 85) {
        this.addResult('Integration Test Coverage', true,
          `Integration coverage: ${integrationCoverage}%`, integrationCoverage);
      } else {
        this.addResult('Integration Test Coverage', false,
          `Integration coverage: ${integrationCoverage}% (target: 85%)`, integrationCoverage);
      }

    } catch (error) {
      // If coverage command fails, check for test files manually
      const testDirectories = [
        'src/tests/integration',
        'src/tests/regional',
        'src/tests/database'
      ];

      const testFileCount = testDirectories.reduce((count, dir) => {
        const dirPath = path.join(process.cwd(), dir);
        if (existsSync(dirPath)) {
          const files = require('fs').readdirSync(dirPath);
          return count + files.filter((f: string) => f.endsWith('.test.ts')).length;
        }
        return count;
      }, 0);

      if (testFileCount >= 5) {
        this.addResult('Test Coverage (File Count)', true,
          `${testFileCount} test files found (manual validation)`);
      } else {
        this.addResult('Test Coverage (File Count)', false,
          `Only ${testFileCount} test files found (insufficient coverage)`);
      }
    }
  }

  /**
   * Add a validation result
   */
  private addResult(name: string, passed: boolean, details?: string, score?: number): void {
    this.results.push({ name, passed, details, score });
    
    const status = passed ? `${colors.green}✅` : `${colors.red}❌`;
    const scoreText = score ? ` (${score}%)` : '';
    const message = details || '';
    
    console.log(`  ${status} ${name}${scoreText}: ${message}${colors.reset}`);
  }

  /**
   * Print final validation results
   */
  private printResults(): void {
    console.log(`\n${colors.cyan}📊 Phase 3 Validation Results Summary${colors.reset}`);
    console.log(`${colors.cyan}=====================================${colors.reset}`);
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const passRate = Math.round((passed / total) * 100);
    
    console.log(`\n${colors.white}Total Validations: ${total}${colors.reset}`);
    console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
    console.log(`${colors.red}Failed: ${total - passed}${colors.reset}`);
    console.log(`${colors.yellow}Pass Rate: ${passRate}%${colors.reset}`);
    
    // Calculate average score for scored validations
    const scoredResults = this.results.filter(r => r.score !== undefined);
    if (scoredResults.length > 0) {
      const avgScore = scoredResults.reduce((sum, r) => sum + (r.score || 0), 0) / scoredResults.length;
      console.log(`${colors.blue}Average Score: ${Math.round(avgScore)}%${colors.reset}`);
    }
    
    if (total - passed > 0) {
      console.log(`\n${colors.red}Failed Validations:${colors.reset}`);
      this.results
        .filter(r => !r.passed)
        .forEach(result => {
          console.log(`  ${colors.red}❌ ${result.name}${colors.reset}`);
          if (result.details) {
            console.log(`     ${result.details}`);
          }
        });
    }

    // Phase 3 specific summary
    console.log(`\n${colors.cyan}Phase 3 Implementation Status:${colors.reset}`);
    console.log(`  Integration Tests: ${this.getStatusIcon('Integration Test Coverage')}`);
    console.log(`  Performance Monitoring: ${this.getStatusIcon('Performance Monitoring Components')}`);
    console.log(`  API Documentation: ${this.getStatusIcon('API Documentation Updates')}`);
    console.log(`  Error Handling: ${this.getStatusIcon('Error Handling Components')}`);
    console.log(`  Regional Configurations: ${this.getStatusIcon('Regional Configuration Files')}`);
    console.log(`  End-to-End Workflows: ${this.getStatusIcon('End-to-End Workflows')}`);
  }

  private getStatusIcon(resultName: string): string {
    const result = this.results.find(r => r.name === resultName);
    return result?.passed ? `${colors.green}✅${colors.reset}` : `${colors.red}❌${colors.reset}`;
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new Phase3Validator();
  validator.runValidation().catch(error => {
    console.error(`${colors.red}Phase 3 validation failed:${colors.reset}`, error);
    process.exit(1);
  });
}

export { Phase3Validator };
