import React from 'react';
import Icon from '../../../components/AppIcon';


const SocialAuthOptions = ({ onSocialLogin, isLoading }) => {
  const socialProviders = [
    {
      id: 'google',
      name: 'Google',
      icon: 'Chrome',
      color: 'var(--color-error-500)',
      bgColor: 'bg-error-50 hover:bg-error-100'
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: 'Smartphone',
      color: 'var(--color-text-primary)',
      bgColor: 'bg-secondary-50 hover:bg-secondary-100'
    }
  ];

  const handleSocialLogin = (provider) => {
    onSocialLogin(provider);
  };

  return (
    <div className="space-y-3">
      <div className="text-center">
        <p className="text-sm text-text-secondary mb-4">
          Quick access with your existing account
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {socialProviders.map((provider) => (
          <button
            key={provider.id}
            onClick={() => handleSocialLogin(provider.id)}
            disabled={isLoading}
            className={`flex items-center justify-center space-x-3 px-4 py-3 border border-border rounded-lg transition-fast ${provider.bgColor} disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <Icon 
              name={provider.icon} 
              size={18} 
              color={provider.color}
            />
            <span className="font-medium text-text-primary">
              {provider.name}
            </span>
          </button>
        ))}
      </div>
      
      <div className="text-center pt-2">
        <p className="text-xs text-text-muted">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </p>
      </div>
    </div>
  );
};

export default SocialAuthOptions;