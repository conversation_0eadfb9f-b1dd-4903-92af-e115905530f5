/**
 * Installation Tab - PWA installation and features showcase
 */

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/Card';
import PWAInstallPrompt from '../PWAInstallPrompt';
import { Smartphone, Download, Wifi, Database, RotateCcw, Bell } from 'lucide-react';

export const InstallationTab = () => {
  return (
    <div className="space-y-6">
      {/* Installation Prompt */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Install PWA
          </CardTitle>
        </CardHeader>
        <CardContent>
          <PWAInstallPrompt />
        </CardContent>
      </Card>

      {/* PWA Features Showcase */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            PWA Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start gap-3">
              <Wifi className="h-6 w-6 text-blue-500 mt-1" />
              <div>
                <h4 className="font-medium text-gray-900">Offline Functionality</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Continue using the app even when offline. Voice recordings are saved locally and synced when connection is restored.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Database className="h-6 w-6 text-green-500 mt-1" />
              <div>
                <h4 className="font-medium text-gray-900">Local Storage</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Your data is stored securely on your device with IndexedDB, providing fast access and offline availability.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <RotateCcw className="h-6 w-6 text-purple-500 mt-1" />
              <div>
                <h4 className="font-medium text-gray-900">Background Sync</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Automatic synchronization in the background when your connection is restored, ensuring no data is lost.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Bell className="h-6 w-6 text-orange-500 mt-1" />
              <div>
                <h4 className="font-medium text-gray-900">Push Notifications</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Stay updated with important health notifications and consultation reminders.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Installation Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Installation Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">On Mobile (Chrome/Safari)</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                <li>Tap the browser menu (three dots or share icon)</li>
                <li>Look for "Add to Home Screen" or "Install App"</li>
                <li>Follow the prompts to install</li>
                <li>The app will appear on your home screen like a regular app</li>
              </ol>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">On Desktop (Chrome/Edge)</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                <li>Look for the install icon in the address bar</li>
                <li>Click "Install VoiceHealth AI"</li>
                <li>The app will be added to your applications</li>
                <li>Launch it like any desktop application</li>
              </ol>
            </div>

            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
              <p className="text-sm text-blue-800">
                <strong>Pro Tip:</strong> Once installed, the PWA works offline and provides a native app experience with faster loading times and better performance.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
