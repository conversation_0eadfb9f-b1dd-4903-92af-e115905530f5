import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const AudioVisualization = ({ 
  activeAgent = null,
  isPlaying = false,
  volume = 0.7,
  onVolumeChange = () => {},
  onPlayPause = () => {},
  onMute = () => {},
  isMuted = false,
  audioData = [],
  className = ''
}) => {
  const [visualizationData, setVisualizationData] = useState([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const animationRef = useRef();

  useEffect(() => {
    // Generate dynamic waveform data
    const generateWaveform = () => {
      const newData = Array.from({ length: 40 }, (_, i) => ({
        id: i,
        height: isPlaying 
          ? Math.random() * 60 + 20 
          : Math.random() * 20 + 5,
        intensity: isPlaying 
          ? Math.random() * 0.8 + 0.2 
          : Math.random() * 0.3 + 0.1
      }));
      setVisualizationData(newData);
    };

    if (isPlaying) {
      const interval = setInterval(generateWaveform, 150);
      return () => clearInterval(interval);
    } else {
      generateWaveform();
    }
  }, [isPlaying]);

  useEffect(() => {
    // Simulate audio progress
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 1;
          if (newTime >= duration) {
            return 0;
          }
          return newTime;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, duration]);

  useEffect(() => {
    // Set random duration for demo
    setDuration(Math.floor(Math.random() * 300 + 120)); // 2-7 minutes
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getAgentColor = () => {
    if (!activeAgent) return 'secondary';
    
    switch (activeAgent.specialty) {
      case 'General Practitioner':
        return 'primary';
      case 'Cardiologist':
        return 'success';
      case 'Nutritionist':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getVolumeIcon = () => {
    if (isMuted || volume === 0) return 'VolumeX';
    if (volume < 0.3) return 'Volume';
    if (volume < 0.7) return 'Volume1';
    return 'Volume2';
  };

  return (
    <div className={`bg-surface border border-border rounded-xl p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            `bg-${getAgentColor()}-50`
          } ${isPlaying ? 'animate-pulse' : ''}`}>
            <Icon 
              name="Headphones"
              size={20}
              color={`var(--color-${getAgentColor()})`}
            />
          </div>
          <div>
            <h3 className="font-semibold text-text-primary font-heading">
              Audio Monitor
            </h3>
            <p className="text-sm text-text-secondary font-caption">
              {activeAgent 
                ? `Listening to ${activeAgent.name}` 
                : 'Multi-agent conversation'
              }
            </p>
          </div>
        </div>

        {/* Status Indicator */}
        <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-full ${
          isPlaying 
            ? 'bg-success-50 text-success-600' :'bg-secondary-50 text-text-secondary'
        }`}>
          <div className={`w-2 h-2 rounded-full ${
            isPlaying ? 'bg-success-500 animate-pulse' : 'bg-secondary-400'
          }`}></div>
          <span className="text-sm font-medium">
            {isPlaying ? 'Live' : 'Paused'}
          </span>
        </div>
      </div>

      {/* Waveform Visualization */}
      <div className="mb-6">
        <div className="flex items-end justify-center space-x-1 h-32 bg-secondary-50 rounded-lg p-4">
          {visualizationData.map((bar) => (
            <div
              key={bar.id}
              className={`w-2 rounded-full transition-all duration-150 ${
                `bg-${getAgentColor()}-500`
              }`}
              style={{
                height: `${bar.height}px`,
                opacity: bar.intensity,
                animationDelay: `${bar.id * 0.05}s`
              }}
            ></div>
          ))}
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex justify-between text-xs text-text-secondary mb-1">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
          <div className="w-full bg-secondary-200 rounded-full h-1">
            <div 
              className={`h-1 rounded-full transition-all duration-300 ${
                `bg-${getAgentColor()}-500`
              }`}
              style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Audio Controls */}
      <div className="flex items-center justify-between">
        {/* Play/Pause */}
        <Button
          variant="primary"
          size="sm"
          onClick={onPlayPause}
          iconName={isPlaying ? "Pause" : "Play"}
          iconPosition="left"
        >
          {isPlaying ? 'Pause' : 'Play'}
        </Button>

        {/* Volume Control */}
        <div className="flex items-center space-x-3">
          <button
            onClick={onMute}
            className="p-2 hover:bg-secondary-50 rounded-lg transition-fast"
            title={isMuted ? 'Unmute' : 'Mute'}
          >
            <Icon 
              name={getVolumeIcon()}
              size={18}
              color="var(--color-text-secondary)"
            />
          </button>
          
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={isMuted ? 0 : volume}
              onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
              className="w-20 h-1 bg-secondary-200 rounded-lg appearance-none cursor-pointer slider"
              disabled={isMuted}
            />
            <span className="text-xs text-text-secondary font-data min-w-[3ch]">
              {Math.round((isMuted ? 0 : volume) * 100)}%
            </span>
          </div>
        </div>
      </div>

      {/* Agent Voice Profiles */}
      {activeAgent && (
        <div className="mt-6 pt-4 border-t border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                `bg-${getAgentColor()}-50`
              }`}>
                <Icon 
                  name={
                    activeAgent.specialty === 'General Practitioner' ? 'Stethoscope' :
                    activeAgent.specialty === 'Cardiologist' ? 'Heart' :
                    activeAgent.specialty === 'Nutritionist'? 'Apple' : 'User'
                  }
                  size={16}
                  color={`var(--color-${getAgentColor()})`}
                />
              </div>
              <div>
                <div className="font-medium text-text-primary text-sm">
                  {activeAgent.name}
                </div>
                <div className="text-xs text-text-secondary">
                  {activeAgent.voiceProfile || 'Professional, Clear'}
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm font-medium text-text-primary">
                {activeAgent.gender || 'Female'} Voice
              </div>
              <div className="text-xs text-text-secondary">
                {activeAgent.accent || 'American English'}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Audio Quality Indicator */}
      <div className="mt-4 flex items-center justify-center space-x-4 text-xs text-text-secondary">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-success-500 rounded-full"></div>
          <span>HD Audio</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-success-500 rounded-full"></div>
          <span>Low Latency</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-success-500 rounded-full"></div>
          <span>Noise Cancelled</span>
        </div>
      </div>
    </div>
  );
};

export default AudioVisualization;