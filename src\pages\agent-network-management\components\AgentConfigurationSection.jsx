import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';


const AgentConfigurationSection = ({ agent, onBack, onSave }) => {
  const [config, setConfig] = useState({
    personalityTraits: {
      empathy: agent?.personalityTraits?.empathy || 85,
      professionalism: agent?.personalityTraits?.professionalism || 90,
      friendliness: agent?.personalityTraits?.friendliness || 75,
      confidence: agent?.personalityTraits?.confidence || 88
    },
    communicationStyle: agent?.communicationStyle || 'balanced',
    responseSpeed: agent?.responseSpeed || 'normal',
    knowledgeUpdateFrequency: agent?.knowledgeUpdateFrequency || 'daily',
    handoffThreshold: agent?.handoffThreshold || 70,
    escalationProtocol: agent?.escalationProtocol || 'automatic',
    collaborationMode: agent?.collaborationMode || 'enabled'
  });

  const [activeTab, setActiveTab] = useState('personality');

  const handlePersonalityChange = (trait, value) => {
    setConfig(prev => ({
      ...prev,
      personalityTraits: {
        ...prev.personalityTraits,
        [trait]: value
      }
    }));
  };

  const handleConfigChange = (field, value) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    const updatedAgent = {
      ...agent,
      ...config
    };
    onSave?.(updatedAgent);
  };

  const renderPersonalityTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">Personality Calibration</h3>
      
      {Object.entries(config.personalityTraits).map(([trait, value]) => (
        <div key={trait}>
          <div className="flex justify-between items-center mb-2">
            <label className="text-sm font-medium text-text-primary capitalize">
              {trait}
            </label>
            <span className="text-sm font-bold text-primary-600">{value}%</span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            value={value}
            onChange={(e) => handlePersonalityChange(trait, parseInt(e.target.value))}
            className="w-full h-2 bg-background rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-text-secondary mt-1">
            <span>Low</span>
            <span>High</span>
          </div>
        </div>
      ))}
    </div>
  );

  const renderCommunicationTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">Communication Settings</h3>
      
      {/* Communication Style */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Communication Style
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {[
            { value: 'formal', label: 'Formal', description: 'Professional medical language' },
            { value: 'balanced', label: 'Balanced', description: 'Mix of professional and casual' },
            { value: 'casual', label: 'Casual', description: 'Friendly, conversational tone' }
          ].map((style) => (
            <button
              key={style.value}
              onClick={() => handleConfigChange('communicationStyle', style.value)}
              className={`p-4 border rounded-lg text-left transition-all ${
                config.communicationStyle === style.value
                  ? 'border-primary-500 bg-primary-50' :'border-border hover:border-primary-300'
              }`}
            >
              <div className="font-medium text-text-primary">{style.label}</div>
              <div className="text-sm text-text-secondary mt-1">{style.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Response Speed */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Response Speed Priority
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {[
            { value: 'fast', label: 'Fast', description: 'Quick responses, may sacrifice detail' },
            { value: 'normal', label: 'Normal', description: 'Balanced speed and accuracy' },
            { value: 'thorough', label: 'Thorough', description: 'Detailed responses, slower speed' }
          ].map((speed) => (
            <button
              key={speed.value}
              onClick={() => handleConfigChange('responseSpeed', speed.value)}
              className={`p-4 border rounded-lg text-left transition-all ${
                config.responseSpeed === speed.value
                  ? 'border-primary-500 bg-primary-50' :'border-border hover:border-primary-300'
              }`}
            >
              <div className="font-medium text-text-primary">{speed.label}</div>
              <div className="text-sm text-text-secondary mt-1">{speed.description}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderKnowledgeTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">Knowledge Base Management</h3>
      
      {/* Update Frequency */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Knowledge Update Frequency
        </label>
        <select
          value={config.knowledgeUpdateFrequency}
          onChange={(e) => handleConfigChange('knowledgeUpdateFrequency', e.target.value)}
          className="w-full p-3 border border-border rounded-lg bg-surface text-text-primary"
        >
          <option value="realtime">Real-time</option>
          <option value="hourly">Hourly</option>
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
        </select>
      </div>

      {/* Specialized Knowledge Areas */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Specialized Knowledge Areas
        </label>
        <div className="space-y-2">
          {agent?.expertise?.map((area, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-background rounded-lg">
              <span className="text-text-primary">{area}</span>
              <div className="flex items-center gap-2">
                <span className="text-sm text-success-600 font-medium">Active</span>
                <Icon name="Check" size={16} className="text-success-500" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Training Modules */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Available Training Modules
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[
            'Latest Medical Guidelines',
            'Patient Communication Best Practices',
            'Emergency Response Protocols',
            'Cross-specialty Collaboration'
          ].map((module, index) => (
            <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg">
              <span className="text-text-primary text-sm">{module}</span>
              <Button variant="outline" size="sm">
                Update
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderCollaborationTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-text-primary mb-4">Inter-Agent Collaboration</h3>
      
      {/* Handoff Threshold */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="text-sm font-medium text-text-primary">
            Consultation Handoff Threshold
          </label>
          <span className="text-sm font-bold text-primary-600">{config.handoffThreshold}%</span>
        </div>
        <input
          type="range"
          min="0"
          max="100"
          value={config.handoffThreshold}
          onChange={(e) => handleConfigChange('handoffThreshold', parseInt(e.target.value))}
          className="w-full h-2 bg-background rounded-lg appearance-none cursor-pointer slider"
        />
        <p className="text-xs text-text-secondary mt-1">
          When confidence drops below this threshold, suggest consultation handoff
        </p>
      </div>

      {/* Escalation Protocol */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Escalation Protocol
        </label>
        <div className="space-y-2">
          {[
            { value: 'automatic', label: 'Automatic', description: 'Auto-escalate based on thresholds' },
            { value: 'manual', label: 'Manual', description: 'Require manual approval for escalation' },
            { value: 'hybrid', label: 'Hybrid', description: 'Auto-escalate urgent cases, manual for others' }
          ].map((protocol) => (
            <label key={protocol.value} className="flex items-center p-3 border border-border rounded-lg cursor-pointer hover:bg-surface">
              <input
                type="radio"
                name="escalationProtocol"
                value={protocol.value}
                checked={config.escalationProtocol === protocol.value}
                onChange={(e) => handleConfigChange('escalationProtocol', e.target.value)}
                className="mr-3"
              />
              <div>
                <div className="font-medium text-text-primary">{protocol.label}</div>
                <div className="text-sm text-text-secondary">{protocol.description}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Collaboration Settings */}
      <div>
        <label className="block text-sm font-medium text-text-primary mb-3">
          Multi-Agent Collaboration
        </label>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.collaborationMode === 'enabled'}
              onChange={(e) => handleConfigChange('collaborationMode', e.target.checked ? 'enabled' : 'disabled')}
              className="mr-3"
            />
            <span className="text-text-primary">Enable real-time agent collaboration</span>
          </label>
          
          <div className="ml-6 space-y-2 text-sm text-text-secondary">
            <div>• Share consultation context with relevant specialists</div>
            <div>• Participate in multi-agent discussions</div>
            <div>• Provide specialty insights for complex cases</div>
          </div>
        </div>
      </div>
    </div>
  );

  const tabs = [
    { id: 'personality', label: 'Personality', icon: 'User' },
    { id: 'communication', label: 'Communication', icon: 'MessageCircle' },
    { id: 'knowledge', label: 'Knowledge', icon: 'BookOpen' },
    { id: 'collaboration', label: 'Collaboration', icon: 'Users' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          onClick={onBack}
          iconName="ArrowLeft"
          size="sm"
        >
          Back
        </Button>
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center text-2xl">
            {agent?.avatar}
          </div>
          <div>
            <h2 className="text-xl font-semibold text-text-primary">{agent?.name} Configuration</h2>
            <p className="text-text-secondary">Customize agent behavior and capabilities</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Tab Navigation */}
        <div className="lg:col-span-1">
          <div className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all ${
                  activeTab === tab.id
                    ? 'bg-primary-50 border border-primary-200 text-primary-700' :'hover:bg-surface border border-transparent'
                }`}
              >
                <Icon name={tab.icon} size={18} />
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="lg:col-span-3">
          <div className="bg-surface rounded-lg border border-border p-6">
            {activeTab === 'personality' && renderPersonalityTab()}
            {activeTab === 'communication' && renderCommunicationTab()}
            {activeTab === 'knowledge' && renderKnowledgeTab()}
            {activeTab === 'collaboration' && renderCollaborationTab()}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-6 border-t border-border">
        <Button variant="outline" onClick={onBack}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSave}>
          Save Configuration
        </Button>
      </div>
    </div>
  );
};

export default AgentConfigurationSection;