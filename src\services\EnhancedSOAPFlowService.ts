/**
 * Enhanced SOAP Flow Service
 * 
 * Provides systematic conversation flow control and automatic phase progression
 * for medical consultations using the SOAP framework.
 */

import { SOAPAssessment, DiagnosticFrameworkService } from './DiagnosticFrameworkService';

export interface ConversationState {
  sessionId: string;
  currentPhase: SOAPPhase;
  phaseProgress: Record<SOAPPhase, number>;
  conversationTurn: number;
  lastUserMessage: string;
  lastAgentMessage: string;
  phaseTransitionHistory: PhaseTransition[];
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  clinicalFlags: ClinicalFlag[];
  nextRequiredQuestions: string[];
  estimatedCompletionTime: number; // minutes
}

export type SOAPPhase = 'subjective' | 'objective' | 'assessment' | 'plan' | 'complete';

export interface PhaseTransition {
  fromPhase: SOAPPhase;
  toPhase: SOAPPhase;
  timestamp: string;
  trigger: 'automatic' | 'manual' | 'urgency' | 'completion';
  reason: string;
  completionPercentage: number;
}

export interface ClinicalFlag {
  type: 'red_flag' | 'yellow_flag' | 'information_gap' | 'specialist_referral';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  triggeredAt: string;
  phase: SOAPPhase;
}

export interface FlowControlDecision {
  shouldProgressPhase: boolean;
  nextPhase?: SOAPPhase;
  requiredQuestions: string[];
  urgencyEscalation?: boolean;
  clinicalFlags: ClinicalFlag[];
  estimatedTimeToCompletion: number;
  conversationGuidance: string;
}

class EnhancedSOAPFlowService {
  private diagnosticService: DiagnosticFrameworkService;
  private conversationStates: Map<string, ConversationState> = new Map();

  // Phase completion thresholds
  private readonly PHASE_COMPLETION_THRESHOLDS = {
    subjective: 75, // 75% of subjective data collected
    objective: 60,  // 60% of objective data collected
    assessment: 80, // 80% of assessment complete
    plan: 90       // 90% of plan formulated
  };

  // Minimum questions per phase
  private readonly MIN_QUESTIONS_PER_PHASE = {
    subjective: 5,
    objective: 3,
    assessment: 2,
    plan: 2
  };

  constructor() {
    this.diagnosticService = new DiagnosticFrameworkService();
  }

  /**
   * Initialize conversation state for a new session
   */
  initializeConversationState(sessionId: string, chiefComplaint: string): ConversationState {
    const state: ConversationState = {
      sessionId,
      currentPhase: 'subjective',
      phaseProgress: {
        subjective: 0,
        objective: 0,
        assessment: 0,
        plan: 0,
        complete: 0
      },
      conversationTurn: 0,
      lastUserMessage: chiefComplaint,
      lastAgentMessage: '',
      phaseTransitionHistory: [],
      urgencyLevel: this.assessInitialUrgency(chiefComplaint),
      clinicalFlags: [],
      nextRequiredQuestions: this.generateInitialQuestions(chiefComplaint),
      estimatedCompletionTime: this.estimateConsultationTime(chiefComplaint)
    };

    this.conversationStates.set(sessionId, state);
    return state;
  }

  /**
   * Process conversation turn and determine flow control decisions
   */
  processConversationTurn(
    sessionId: string,
    userMessage: string,
    soapAssessment: SOAPAssessment
  ): FlowControlDecision {
    const state = this.conversationStates.get(sessionId);
    if (!state) {
      throw new Error(`Conversation state not found for session: ${sessionId}`);
    }

    // Update conversation state
    state.conversationTurn++;
    state.lastUserMessage = userMessage;

    // Analyze user message for clinical flags
    const newFlags = this.analyzeForClinicalFlags(userMessage, state.currentPhase);
    state.clinicalFlags.push(...newFlags);

    // Update phase progress
    this.updatePhaseProgress(state, soapAssessment);

    // Determine if phase should progress
    const flowDecision = this.determineFlowControl(state, soapAssessment);

    // Handle phase transition if needed
    if (flowDecision.shouldProgressPhase && flowDecision.nextPhase) {
      this.transitionPhase(state, flowDecision.nextPhase, 'automatic', 'Phase completion threshold reached');
    }

    // Update urgency level if escalated
    if (flowDecision.urgencyEscalation) {
      this.escalateUrgency(state);
    }

    // Update state
    this.conversationStates.set(sessionId, state);

    return flowDecision;
  }

  /**
   * Determine flow control decisions based on current state
   */
  private determineFlowControl(
    state: ConversationState,
    soapAssessment: SOAPAssessment
  ): FlowControlDecision {
    const currentPhaseProgress = state.phaseProgress[state.currentPhase];
    const threshold = this.PHASE_COMPLETION_THRESHOLDS[state.currentPhase];
    const minQuestions = this.MIN_QUESTIONS_PER_PHASE[state.currentPhase];

    // Check for critical flags that require immediate attention
    const criticalFlags = state.clinicalFlags.filter(flag => flag.severity === 'critical');
    if (criticalFlags.length > 0) {
      return {
        shouldProgressPhase: true,
        nextPhase: 'assessment', // Jump to assessment for critical issues
        requiredQuestions: this.generateCriticalQuestions(criticalFlags),
        urgencyEscalation: true,
        clinicalFlags: criticalFlags,
        estimatedTimeToCompletion: 5, // Urgent completion
        conversationGuidance: 'Critical symptoms detected. Prioritizing immediate assessment.'
      };
    }

    // Check if current phase is sufficiently complete
    const shouldProgress = (
      currentPhaseProgress >= threshold &&
      state.conversationTurn >= minQuestions &&
      this.isPhaseDataSufficient(state.currentPhase, soapAssessment)
    );

    const nextPhase = shouldProgress ? this.getNextPhase(state.currentPhase) : undefined;
    const requiredQuestions = shouldProgress 
      ? this.generatePhaseTransitionQuestions(nextPhase || state.currentPhase)
      : this.generateContinuationQuestions(state.currentPhase, soapAssessment);

    return {
      shouldProgressPhase: shouldProgress,
      nextPhase,
      requiredQuestions,
      urgencyEscalation: false,
      clinicalFlags: state.clinicalFlags.filter(flag => flag.severity === 'high'),
      estimatedTimeToCompletion: this.calculateRemainingTime(state),
      conversationGuidance: this.generateConversationGuidance(state, shouldProgress)
    };
  }

  /**
   * Generate phase-appropriate questions
   */
  generatePhaseQuestions(phase: SOAPPhase, soapAssessment: SOAPAssessment, urgencyLevel: string): string[] {
    switch (phase) {
      case 'subjective':
        return this.generateSubjectiveQuestions(soapAssessment, urgencyLevel);
      case 'objective':
        return this.generateObjectiveQuestions(soapAssessment, urgencyLevel);
      case 'assessment':
        return this.generateAssessmentQuestions(soapAssessment, urgencyLevel);
      case 'plan':
        return this.generatePlanQuestions(soapAssessment, urgencyLevel);
      default:
        return [];
    }
  }

  /**
   * Generate subjective phase questions
   */
  private generateSubjectiveQuestions(soapAssessment: SOAPAssessment, urgencyLevel: string): string[] {
    const questions: string[] = [];
    const subjective = soapAssessment.subjective;

    // Chief complaint exploration
    if (!subjective.completionStatus.chiefComplaint) {
      questions.push("Can you describe your main concern or symptom in detail?");
    }

    // History of Present Illness (OLDCARTS)
    const hpi = subjective.historyOfPresentIllness;
    if (!hpi.completionStatus.onset) {
      questions.push("When did this symptom first start? Was it sudden or gradual?");
    }
    if (!hpi.completionStatus.location) {
      questions.push("Can you point to exactly where you feel this symptom?");
    }
    if (!hpi.completionStatus.duration) {
      questions.push("How long does this symptom last when it occurs?");
    }
    if (!hpi.completionStatus.character) {
      questions.push("How would you describe the quality of this symptom? (sharp, dull, burning, etc.)");
    }
    if (!hpi.completionStatus.severity) {
      questions.push("On a scale of 1-10, how severe is this symptom at its worst?");
    }

    // Prioritize based on urgency
    if (urgencyLevel === 'high' || urgencyLevel === 'critical') {
      return questions.slice(0, 3); // Focus on most critical questions
    }

    return questions.slice(0, 5);
  }

  /**
   * Generate objective phase questions
   */
  private generateObjectiveQuestions(soapAssessment: SOAPAssessment, urgencyLevel: string): string[] {
    const questions: string[] = [];

    questions.push("Have you taken your temperature recently? If so, what was it?");
    questions.push("Have you noticed any visible changes like swelling, redness, or rash?");
    questions.push("Are you experiencing any difficulty breathing or changes in your breathing pattern?");

    if (urgencyLevel === 'low') {
      questions.push("Have you noticed any changes in your appetite or weight recently?");
      questions.push("How has your sleep been affected by these symptoms?");
    }

    return questions;
  }

  /**
   * Generate assessment phase questions
   */
  private generateAssessmentQuestions(soapAssessment: SOAPAssessment, urgencyLevel: string): string[] {
    const questions: string[] = [];

    questions.push("Based on your symptoms, are you most concerned about a specific condition?");
    questions.push("Have you experienced anything similar to this before?");

    if (urgencyLevel !== 'critical') {
      questions.push("Is there anything else about your symptoms that you think might be important?");
    }

    return questions;
  }

  /**
   * Generate plan phase questions
   */
  private generatePlanQuestions(soapAssessment: SOAPAssessment, urgencyLevel: string): string[] {
    const questions: string[] = [];

    questions.push("What would you like to achieve from this consultation?");
    questions.push("Are there any treatments or medications you'd prefer to avoid?");

    if (urgencyLevel === 'low') {
      questions.push("How would you prefer to follow up on this condition?");
    }

    return questions;
  }

  /**
   * Assess initial urgency from chief complaint
   */
  private assessInitialUrgency(chiefComplaint: string): 'low' | 'medium' | 'high' | 'critical' {
    const criticalKeywords = ['chest pain', 'difficulty breathing', 'severe headache', 'unconscious', 'bleeding heavily'];
    const highKeywords = ['severe pain', 'high fever', 'vomiting blood', 'severe allergic reaction'];
    const mediumKeywords = ['moderate pain', 'fever', 'persistent cough', 'rash'];

    const complaint = chiefComplaint.toLowerCase();

    if (criticalKeywords.some(keyword => complaint.includes(keyword))) {
      return 'critical';
    }
    if (highKeywords.some(keyword => complaint.includes(keyword))) {
      return 'high';
    }
    if (mediumKeywords.some(keyword => complaint.includes(keyword))) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Get conversation state for a session
   */
  getConversationState(sessionId: string): ConversationState | undefined {
    return this.conversationStates.get(sessionId);
  }

  /**
   * Update phase progress based on SOAP assessment
   */
  private updatePhaseProgress(state: ConversationState, soapAssessment: SOAPAssessment): void {
    state.phaseProgress.subjective = this.calculateSubjectiveProgress(soapAssessment.subjective);
    state.phaseProgress.objective = this.calculateObjectiveProgress(soapAssessment.objective);
    state.phaseProgress.assessment = this.calculateAssessmentProgress(soapAssessment.assessment);
    state.phaseProgress.plan = this.calculatePlanProgress(soapAssessment.plan);
  }

  /**
   * Calculate subjective phase progress
   */
  private calculateSubjectiveProgress(subjective: any): number {
    const completionStatus = subjective.completionStatus;
    const totalFields = Object.keys(completionStatus).length;
    const completedFields = Object.values(completionStatus).filter(Boolean).length;
    return Math.round((completedFields / totalFields) * 100);
  }

  /**
   * Calculate objective phase progress
   */
  private calculateObjectiveProgress(objective: any): number {
    // Simplified calculation - can be enhanced based on objective data structure
    return objective.vitalSigns ? 60 : 20;
  }

  /**
   * Calculate assessment phase progress
   */
  private calculateAssessmentProgress(assessment: any): number {
    let progress = 0;
    if (assessment.differentialDiagnosis?.length > 0) progress += 40;
    if (assessment.workingDiagnosis) progress += 30;
    if (assessment.riskFactors?.length > 0) progress += 30;
    return progress;
  }

  /**
   * Calculate plan phase progress
   */
  private calculatePlanProgress(plan: any): number {
    let progress = 0;
    if (plan.immediateActions?.length > 0) progress += 30;
    if (plan.medications?.length > 0) progress += 25;
    if (plan.followUp) progress += 25;
    if (plan.patientEducation) progress += 20;
    return progress;
  }

  /**
   * Analyze user message for clinical flags
   */
  private analyzeForClinicalFlags(message: string, phase: SOAPPhase): ClinicalFlag[] {
    const flags: ClinicalFlag[] = [];
    const lowerMessage = message.toLowerCase();

    // Red flags (critical symptoms)
    const redFlagKeywords = [
      'chest pain', 'difficulty breathing', 'severe headache', 'loss of consciousness',
      'severe bleeding', 'severe allergic reaction', 'suicidal thoughts'
    ];

    // Yellow flags (concerning symptoms)
    const yellowFlagKeywords = [
      'high fever', 'persistent vomiting', 'severe pain', 'vision changes',
      'numbness', 'weakness', 'confusion'
    ];

    redFlagKeywords.forEach(keyword => {
      if (lowerMessage.includes(keyword)) {
        flags.push({
          type: 'red_flag',
          severity: 'critical',
          description: `Patient reports: ${keyword}`,
          recommendation: 'Immediate medical attention required',
          triggeredAt: new Date().toISOString(),
          phase
        });
      }
    });

    yellowFlagKeywords.forEach(keyword => {
      if (lowerMessage.includes(keyword)) {
        flags.push({
          type: 'yellow_flag',
          severity: 'high',
          description: `Patient reports: ${keyword}`,
          recommendation: 'Urgent evaluation recommended',
          triggeredAt: new Date().toISOString(),
          phase
        });
      }
    });

    return flags;
  }

  /**
   * Transition to next phase
   */
  private transitionPhase(
    state: ConversationState,
    nextPhase: SOAPPhase,
    trigger: 'automatic' | 'manual' | 'urgency' | 'completion',
    reason: string
  ): void {
    const transition: PhaseTransition = {
      fromPhase: state.currentPhase,
      toPhase: nextPhase,
      timestamp: new Date().toISOString(),
      trigger,
      reason,
      completionPercentage: state.phaseProgress[state.currentPhase]
    };

    state.phaseTransitionHistory.push(transition);
    state.currentPhase = nextPhase;
  }

  /**
   * Escalate urgency level
   */
  private escalateUrgency(state: ConversationState): void {
    const urgencyLevels = ['low', 'medium', 'high', 'critical'];
    const currentIndex = urgencyLevels.indexOf(state.urgencyLevel);
    if (currentIndex < urgencyLevels.length - 1) {
      state.urgencyLevel = urgencyLevels[currentIndex + 1] as any;
    }
  }

  /**
   * Get next phase in SOAP sequence
   */
  private getNextPhase(currentPhase: SOAPPhase): SOAPPhase | undefined {
    const phaseSequence: SOAPPhase[] = ['subjective', 'objective', 'assessment', 'plan', 'complete'];
    const currentIndex = phaseSequence.indexOf(currentPhase);
    return currentIndex < phaseSequence.length - 1 ? phaseSequence[currentIndex + 1] : undefined;
  }

  /**
   * Check if phase has sufficient data
   */
  private isPhaseDataSufficient(phase: SOAPPhase, assessment: SOAPAssessment): boolean {
    switch (phase) {
      case 'subjective':
        return assessment.subjective.completionStatus.chiefComplaint &&
               assessment.subjective.completionStatus.hpi;
      case 'objective':
        return assessment.objective.vitalSigns !== undefined;
      case 'assessment':
        return assessment.assessment.workingDiagnosis !== '';
      case 'plan':
        return assessment.plan.immediateActions.length > 0;
      default:
        return true;
    }
  }

  /**
   * Generate initial questions for new consultation
   */
  private generateInitialQuestions(complaint: string): string[] {
    return [
      "Can you tell me more about your main concern?",
      "When did this symptom first start?",
      "How would you rate the severity on a scale of 1-10?"
    ];
  }

  /**
   * Estimate consultation time based on complaint
   */
  private estimateConsultationTime(complaint: string): number {
    const urgency = this.assessInitialUrgency(complaint);
    switch (urgency) {
      case 'critical': return 5;
      case 'high': return 10;
      case 'medium': return 15;
      default: return 20;
    }
  }

  /**
   * Generate questions for phase transitions
   */
  private generatePhaseTransitionQuestions(phase: SOAPPhase): string[] {
    switch (phase) {
      case 'objective':
        return ["Now let's discuss any physical symptoms you can observe..."];
      case 'assessment':
        return ["Based on what you've told me, let me assess your condition..."];
      case 'plan':
        return ["Let's discuss the best approach for managing your condition..."];
      default:
        return [];
    }
  }

  /**
   * Generate continuation questions for current phase
   */
  private generateContinuationQuestions(phase: SOAPPhase, assessment: SOAPAssessment): string[] {
    return this.generatePhaseQuestions(phase, assessment, 'medium');
  }

  /**
   * Generate critical questions for urgent situations
   */
  private generateCriticalQuestions(flags: ClinicalFlag[]): string[] {
    return [
      "Are you experiencing this symptom right now?",
      "How severe is this on a scale of 1-10?",
      "Do you need immediate emergency care?"
    ];
  }

  /**
   * Calculate remaining consultation time
   */
  private calculateRemainingTime(state: ConversationState): number {
    const baseTime = state.estimatedCompletionTime;
    const progressWeight = Object.values(state.phaseProgress).reduce((a, b) => a + b, 0) / 400;
    return Math.max(2, Math.round(baseTime * (1 - progressWeight)));
  }

  /**
   * Generate conversation guidance for agent
   */
  private generateConversationGuidance(state: ConversationState, shouldProgress: boolean): string {
    if (shouldProgress) {
      return `Ready to progress to ${this.getNextPhase(state.currentPhase)} phase. Current phase is ${state.phaseProgress[state.currentPhase]}% complete.`;
    }
    return `Continue gathering ${state.currentPhase} information. Current progress: ${state.phaseProgress[state.currentPhase]}%.`;
  }
}

export const enhancedSOAPFlowService = new EnhancedSOAPFlowService();
export default enhancedSOAPFlowService;
