/**
 * CLIENT-SIDE RATE LIMITING MANAGER
 * 
 * This service provides intelligent client-side rate limiting to prevent
 * unnecessary API calls, reduce server load, and provide better user
 * experience while maintaining emergency system responsiveness.
 * 
 * FEATURES:
 * - Proactive rate limiting before server limits
 * - Emergency operation prioritization
 * - Request queuing and batching
 * - Automatic retry with exponential backoff
 * - User feedback for rate limiting
 * - Integration with offline capabilities
 * 
 * MEDICAL REQUIREMENTS:
 * - Emergency operations bypass all limits
 * - Critical medical data has priority queuing
 * - Graceful degradation for medical workflows
 * - Clear user feedback for rate limiting
 * - Offline fallback integration
 */

import type { UserRole } from '../types';
import auditLogger from './auditLogger';

interface RateLimitConfig {
  readonly maxRequests: number;
  readonly windowMs: number;
  readonly priority: number; // Higher number = higher priority
  readonly bypassable: boolean;
}

interface QueuedRequest {
  readonly id: string;
  readonly url: string;
  readonly options: RequestInit;
  readonly priority: number;
  readonly operationType: string;
  readonly timestamp: number;
  readonly resolve: (response: Response) => void;
  readonly reject: (error: Error) => void;
  readonly retryCount: number;
  readonly maxRetries: number;
}

interface RateLimitStatus {
  readonly operationType: string;
  readonly remaining: number;
  readonly resetTime: number;
  readonly blocked: boolean;
}

type OperationType = 'emergency' | 'critical_medical' | 'medical' | 'auth' | 'general' | 'public';

class ClientRateLimitManager {
  private readonly configs: Record<OperationType, RateLimitConfig>;
  private readonly requestCounts: Map<string, { count: number; windowStart: number }>;
  private readonly requestQueue: QueuedRequest[];
  private readonly activeRequests: Set<string>;
  private readonly retryTimeouts: Map<string, NodeJS.Timeout>;
  private userRole: UserRole;
  private isProcessingQueue: boolean;

  constructor() {
    this.configs = {
      emergency: {
        maxRequests: 1000,
        windowMs: 60000,
        priority: 100,
        bypassable: false
      },
      critical_medical: {
        maxRequests: 150,
        windowMs: 60000,
        priority: 90,
        bypassable: true
      },
      medical: {
        maxRequests: 80,
        windowMs: 60000,
        priority: 70,
        bypassable: true
      },
      auth: {
        maxRequests: 15,
        windowMs: 900000, // 15 minutes
        priority: 60,
        bypassable: false
      },
      general: {
        maxRequests: 50,
        windowMs: 60000,
        priority: 50,
        bypassable: true
      },
      public: {
        maxRequests: 25,
        windowMs: 60000,
        priority: 30,
        bypassable: false
      }
    };

    this.requestCounts = new Map();
    this.requestQueue = [];
    this.activeRequests = new Set();
    this.retryTimeouts = new Map();
    this.userRole = 'patient';
    this.isProcessingQueue = false;

    this.startQueueProcessor();
  }

  /**
   * Set user role for rate limit adjustments
   */
  setUserRole(role: UserRole): void {
    this.userRole = role;
    this.adjustLimitsForRole();
  }

  /**
   * Adjust rate limits based on user role
   */
  private adjustLimitsForRole(): void {
    const multipliers: Record<UserRole, number> = {
      'emergency_responder': 5.0,
      'admin': 3.0,
      'healthcare_provider': 2.0,
      'patient': 1.0
    };

    const multiplier = multipliers[this.userRole] || 1.0;

    // Update configs with role-based multipliers
    Object.keys(this.configs).forEach(key => {
      const operationType = key as OperationType;
      const baseConfig = this.configs[operationType];
      this.configs[operationType] = {
        ...baseConfig,
        maxRequests: Math.floor(baseConfig.maxRequests * multiplier)
      };
    });
  }

  /**
   * Determine operation type from URL and options
   */
  private getOperationType(url: string, options: RequestInit = {}): OperationType {
    const headers = options.headers as Record<string, string> || {};
    
    // Emergency operations
    if (url.includes('/emergency') || 
        url.includes('/critical') ||
        headers['x-emergency-override'] === 'true') {
      return 'emergency';
    }

    // Critical medical operations
    if ((url.includes('/medical') || url.includes('/consultation')) && 
        (options.method === 'POST' || options.method === 'PUT' || options.method === 'PATCH')) {
      return 'critical_medical';
    }

    // Medical data operations
    if (url.includes('/medical') || 
        url.includes('/consultation') || 
        url.includes('/patient')) {
      return 'medical';
    }

    // Authentication operations
    if (url.includes('/auth') || url.includes('/login') || url.includes('/register')) {
      return 'auth';
    }

    // Public operations
    if (url.includes('/public') || url.includes('/search') || url.includes('/health')) {
      return 'public';
    }

    return 'general';
  }

  /**
   * Check if request should be rate limited
   */
  private shouldRateLimit(operationType: OperationType): { allowed: boolean; resetTime?: number } {
    const config = this.configs[operationType];
    const now = Date.now();
    const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
    const key = `${operationType}:${windowStart}`;

    let requestData = this.requestCounts.get(key);
    if (!requestData) {
      requestData = { count: 0, windowStart };
      this.requestCounts.set(key, requestData);
    }

    // Clean up old entries
    const cutoff = now - config.windowMs * 2;
    for (const [entryKey, entry] of this.requestCounts.entries()) {
      if (entry.windowStart < cutoff) {
        this.requestCounts.delete(entryKey);
      }
    }

    if (requestData.count >= config.maxRequests) {
      return {
        allowed: false,
        resetTime: windowStart + config.windowMs
      };
    }

    return { allowed: true };
  }

  /**
   * Add request to queue
   */
  private queueRequest(
    url: string,
    options: RequestInit,
    operationType: OperationType,
    priority: number
  ): Promise<Response> {
    return new Promise((resolve, reject) => {
      const request: QueuedRequest = {
        id: crypto.randomUUID(),
        url,
        options,
        priority,
        operationType,
        timestamp: Date.now(),
        resolve,
        reject,
        retryCount: 0,
        maxRetries: operationType === 'emergency' ? 5 : 3
      };

      // Insert request in priority order
      const insertIndex = this.requestQueue.findIndex(r => r.priority < priority);
      if (insertIndex === -1) {
        this.requestQueue.push(request);
      } else {
        this.requestQueue.splice(insertIndex, 0, request);
      }
    });
  }

  /**
   * Process request queue
   */
  private startQueueProcessor(): void {
    setInterval(() => {
      if (!this.isProcessingQueue && this.requestQueue.length > 0) {
        this.processQueue();
      }
    }, 100); // Check every 100ms
  }

  /**
   * Process queued requests
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.requestQueue.length > 0) {
        const request = this.requestQueue.shift()!;
        const { allowed, resetTime } = this.shouldRateLimit(request.operationType as OperationType);

        if (!allowed && resetTime) {
          // Re-queue request if not emergency
          if (request.operationType !== 'emergency') {
            const delay = resetTime - Date.now();
            if (delay > 0 && request.retryCount < request.maxRetries) {
              setTimeout(() => {
                request.retryCount++;
                this.requestQueue.unshift(request);
              }, Math.min(delay, 5000)); // Max 5 second delay
              continue;
            } else {
              request.reject(new Error('Rate limit exceeded'));
              continue;
            }
          }
        }

        // Execute request
        try {
          const response = await this.executeRequest(request);
          request.resolve(response);
        } catch (error) {
          if (request.retryCount < request.maxRetries) {
            request.retryCount++;
            const delay = Math.min(1000 * Math.pow(2, request.retryCount), 10000);
            setTimeout(() => {
              this.requestQueue.unshift(request);
            }, delay);
          } else {
            request.reject(error as Error);
          }
        }

        // Small delay between requests to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Execute individual request
   */
  private async executeRequest(request: QueuedRequest): Promise<Response> {
    const { url, options, operationType } = request;
    
    // Update request count
    const config = this.configs[operationType as OperationType];
    const now = Date.now();
    const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
    const key = `${operationType}:${windowStart}`;
    
    const requestData = this.requestCounts.get(key) || { count: 0, windowStart };
    requestData.count++;
    this.requestCounts.set(key, requestData);

    // Add request tracking
    this.activeRequests.add(request.id);

    try {
      const response = await fetch(url, options);
      
      // Handle rate limit response from server
      if (response.status === 429) {
        const retryAfter = response.headers.get('retry-after');
        const delay = retryAfter ? parseInt(retryAfter) * 1000 : 1000;
        
        // Log rate limit hit
        await auditLogger.logMedicalDataAccess(
          'client_rate_limit_hit',
          'rate_limiting',
          request.id,
          {
            operation_type: operationType,
            url,
            retry_after: delay,
            user_role: this.userRole
          }
        );

        throw new Error(`Rate limited by server. Retry after ${delay}ms`);
      }

      return response;
    } finally {
      this.activeRequests.delete(request.id);
    }
  }

  /**
   * Make rate-limited request
   */
  async makeRequest(url: string, options: RequestInit = {}): Promise<Response> {
    const operationType = this.getOperationType(url, options);
    const config = this.configs[operationType];
    
    // Emergency operations bypass rate limiting
    if (operationType === 'emergency') {
      return await this.executeRequest({
        id: crypto.randomUUID(),
        url,
        options,
        priority: config.priority,
        operationType,
        timestamp: Date.now(),
        resolve: () => {},
        reject: () => {},
        retryCount: 0,
        maxRetries: 5
      });
    }

    // Check immediate rate limit
    const { allowed, resetTime } = this.shouldRateLimit(operationType);
    
    if (allowed) {
      // Execute immediately
      return await this.executeRequest({
        id: crypto.randomUUID(),
        url,
        options,
        priority: config.priority,
        operationType,
        timestamp: Date.now(),
        resolve: () => {},
        reject: () => {},
        retryCount: 0,
        maxRetries: 3
      });
    } else {
      // Queue request
      return await this.queueRequest(url, options, operationType, config.priority);
    }
  }

  /**
   * Get current rate limit status
   */
  getRateLimitStatus(): RateLimitStatus[] {
    const now = Date.now();
    
    return Object.entries(this.configs).map(([operationType, config]) => {
      const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
      const key = `${operationType}:${windowStart}`;
      const requestData = this.requestCounts.get(key) || { count: 0, windowStart };
      
      return {
        operationType,
        remaining: Math.max(0, config.maxRequests - requestData.count),
        resetTime: windowStart + config.windowMs,
        blocked: requestData.count >= config.maxRequests
      };
    });
  }

  /**
   * Request emergency bypass
   */
  async requestEmergencyBypass(operationType: string, justification: string): Promise<boolean> {
    try {
      const response = await fetch('/api/emergency-bypass', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          operationType,
          justification,
          duration: 3600000 // 1 hour
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        // Log emergency bypass request
        await auditLogger.logEmergencyAccess(
          'client_emergency_bypass',
          operationType,
          justification,
          {
            expires_at: result.expiresAt,
            user_role: this.userRole
          }
        );

        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to request emergency bypass:', error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): {
    queueLength: number;
    activeRequests: number;
    byPriority: Record<string, number>;
  } {
    const byPriority: Record<string, number> = {};
    
    this.requestQueue.forEach(request => {
      byPriority[request.operationType] = (byPriority[request.operationType] || 0) + 1;
    });

    return {
      queueLength: this.requestQueue.length,
      activeRequests: this.activeRequests.size,
      byPriority
    };
  }

  /**
   * Clear all queued requests (emergency use)
   */
  clearQueue(): void {
    this.requestQueue.length = 0;
    console.warn('Request queue cleared for emergency');
  }
}

// Global rate limit manager instance
export const clientRateLimitManager = new ClientRateLimitManager();

// Enhanced fetch function with rate limiting
export async function rateLimitedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  return await clientRateLimitManager.makeRequest(url, options);
}

export default clientRateLimitManager;
