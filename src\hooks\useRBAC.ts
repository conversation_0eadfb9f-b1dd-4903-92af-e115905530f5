/**
 * RBAC REACT HOOK
 * 
 * This hook provides React components with role-based access control
 * functionality, including:
 * 
 * 1. Permission checking for UI elements
 * 2. Emergency access request handling
 * 3. Real-time permission updates
 * 4. Context-aware authorization
 * 5. Loading states and error handling
 * 
 * USAGE EXAMPLES:
 * - const { hasPermission, requestEmergencyAccess } = useRBAC();
 * - const canEdit = hasPermission('medical_conditions', 'update', { userId: patientId });
 * - if (canEdit) { // show edit button }
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import type {
  User,
  ResourceType,
  Action,
  Permission,
  EmergencyAccessRequest
} from '../types';
import { useAuth } from '../contexts/SimpleAuthContext';
import rbacService from '../services/rbacService';

interface PermissionContext {
  readonly userId?: string;
  readonly resourceOwnerId?: string;
  readonly emergencyOverride?: boolean;
  readonly patientId?: string;
  readonly urgency?: 'routine' | 'urgent' | 'emergency';
  readonly justification?: string;
}

interface EmergencyAccessState {
  readonly isRequesting: boolean;
  readonly activeRequests: EmergencyAccessRequest[];
  readonly error?: string;
}

interface RBACHookReturn {
  // Permission checking
  readonly hasPermission: (
    resource: ResourceType, 
    action: Action, 
    context?: PermissionContext
  ) => boolean;
  
  readonly getUserPermissions: () => Permission[];
  readonly canEmergencyOverride: boolean;
  
  // Emergency access
  readonly requestEmergencyAccess: (
    patientId: string, 
    reason: string, 
    urgency: 'high' | 'critical'
  ) => Promise<EmergencyAccessRequest>;
  
  readonly emergencyAccess: EmergencyAccessState;
  
  // Utility functions
  readonly isLoading: boolean;
  readonly error?: string;
  readonly userRole: string;
}

export const useRBAC = (): RBACHookReturn => {
  const { user, loading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [emergencyAccess, setEmergencyAccess] = useState<EmergencyAccessState>({
    isRequesting: false,
    activeRequests: []
  });

  // Memoized user permissions
  const userPermissions = useMemo(() => {
    if (!user) return [];
    return rbacService.getUserPermissions(user);
  }, [user]);

  // Memoized user role
  const userRole = useMemo(() => {
    return user?.user_metadata?.role || 'patient';
  }, [user]);

  // Memoized emergency override capability
  const canEmergencyOverride = useMemo(() => {
    if (!user) return false;
    return rbacService.checkEmergencyOverride(user);
  }, [user]);

  /**
   * Check if user has permission for a specific resource and action
   */
  const hasPermission = useCallback((
    resource: ResourceType,
    action: Action,
    context: PermissionContext = {}
  ): boolean => {
    if (!user) {
      return false;
    }

    try {
      return rbacService.hasPermission(user, resource, action, context);
    } catch (error) {
      console.error('Error checking permission:', error);
      setError(error instanceof Error ? error.message : 'Permission check failed');
      return false; // Fail-safe: deny by default
    }
  }, [user]);

  /**
   * Get all permissions for the current user
   */
  const getUserPermissions = useCallback((): Permission[] => {
    return userPermissions;
  }, [userPermissions]);

  /**
   * Request emergency access to patient data
   */
  const requestEmergencyAccess = useCallback(async (
    patientId: string,
    reason: string,
    urgency: 'high' | 'critical'
  ): Promise<EmergencyAccessRequest> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setEmergencyAccess(prev => ({ ...prev, isRequesting: true, error: undefined }));

    try {
      const request = await rbacService.requestEmergencyAccess(patientId, reason, urgency);
      
      setEmergencyAccess(prev => ({
        ...prev,
        isRequesting: false,
        activeRequests: [...prev.activeRequests, request]
      }));

      return request;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Emergency access request failed';
      setEmergencyAccess(prev => ({
        ...prev,
        isRequesting: false,
        error: errorMessage
      }));
      throw error;
    }
  }, [user]);

  /**
   * Load active emergency access requests
   */
  const loadEmergencyRequests = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      // This would typically fetch from the database
      // For now, we'll just clear any errors
      setEmergencyAccess(prev => ({ ...prev, error: undefined }));
    } catch (error) {
      console.error('Failed to load emergency requests:', error);
      setError(error instanceof Error ? error.message : 'Failed to load emergency requests');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Clear errors
   */
  const clearError = useCallback(() => {
    setError(undefined);
    setEmergencyAccess(prev => ({ ...prev, error: undefined }));
  }, []);

  // Load emergency requests when user changes
  useEffect(() => {
    if (user && !authLoading) {
      loadEmergencyRequests();
    }
  }, [user, authLoading, loadEmergencyRequests]);

  // Auto-clear errors after 10 seconds
  useEffect(() => {
    if (error || emergencyAccess.error) {
      const timer = setTimeout(clearError, 10000);
      return () => clearTimeout(timer);
    }
  }, [error, emergencyAccess.error, clearError]);

  return {
    hasPermission,
    getUserPermissions,
    canEmergencyOverride,
    requestEmergencyAccess,
    emergencyAccess,
    isLoading: isLoading || authLoading,
    error: error || emergencyAccess.error,
    userRole
  };
};

export default useRBAC;
