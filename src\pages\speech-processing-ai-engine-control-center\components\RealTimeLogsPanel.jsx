import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RealTimeLogsPanel = () => {
  const [logs, setLogs] = useState([]);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [selectedLogLevel, setSelectedLogLevel] = useState('all');
  const [selectedComponent, setSelectedComponent] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const logLevels = ['all', 'error', 'warning', 'info', 'debug'];
  const components = ['all', 'whisper-api', 'elevenlabs', 'gpt-4.1', 'claude-3.5', 'gemini-pro', 'orchestrator'];

  const initialLogs = [
    {
      id: 1,
      timestamp: new Date(Date.now() - 1000 * 30),
      level: 'info',
      component: 'whisper-api',
      message: 'Speech recognition completed successfully',
      details: 'Processing time: 145ms, Accuracy: 94.8%',
      sessionId: 'session_001'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 1000 * 45),
      level: 'warning',
      component: 'elevenlabs',
      message: 'High queue length detected',
      details: 'Current queue: 12 items, Average wait: 2.3s',
      sessionId: null
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 1000 * 60),
      level: 'error',
      component: 'gpt-4.1',
      message: 'Rate limit exceeded',
      details: 'Requests: 46/min, Limit: 45/min. Falling back to Claude 3.5',
      sessionId: 'session_002'
    },
    {
      id: 4,
      timestamp: new Date(Date.now() - 1000 * 90),
      level: 'info',
      component: 'orchestrator',
      message: 'Agent handoff completed',
      details: 'From: General Practitioner -> To: Specialist, Context transferred successfully',
      sessionId: 'session_003'
    },
    {
      id: 5,
      timestamp: new Date(Date.now() - 1000 * 120),
      level: 'debug',
      component: 'claude-3.5',
      message: 'Model parameters updated',
      details: 'Temperature: 0.8, Max tokens: 8192, Top-p: 0.95',
      sessionId: null
    },
    {
      id: 6,
      timestamp: new Date(Date.now() - 1000 * 150),
      level: 'warning',
      component: 'whisper-api',
      message: 'Audio quality below threshold',
      details: 'Quality score: 0.72, Recommended minimum: 0.8. Applied noise reduction.',
      sessionId: 'session_004'
    },
    {
      id: 7,
      timestamp: new Date(Date.now() - 1000 * 180),
      level: 'info',
      component: 'gemini-pro',
      message: 'Backup engine activated',
      details: 'Load balancer triggered fallback due to high load on primary engines',
      sessionId: 'session_005'
    }
  ];

  useEffect(() => {
    setLogs(initialLogs);
  }, []);

  useEffect(() => {
    let interval;
    if (isAutoRefresh) {
      interval = setInterval(() => {
        // Simulate new log entries
        const newLog = {
          id: Date.now(),
          timestamp: new Date(),
          level: ['info', 'warning', 'error', 'debug'][Math.floor(Math.random() * 4)],
          component: components[Math.floor(Math.random() * (components.length - 1)) + 1],
          message: 'New system event occurred',
          details: 'Automated log entry for demonstration',
          sessionId: Math.random() > 0.5 ? `session_${Math.floor(Math.random() * 100)}` : null
        };
        
        setLogs(prev => [newLog, ...prev].slice(0, 50)); // Keep only last 50 logs
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isAutoRefresh]);

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'error': return 'text-error-600 bg-error-100 border-error-200';
      case 'warning': return 'text-warning-600 bg-warning-100 border-warning-200';
      case 'info': return 'text-primary-600 bg-primary-100 border-primary-200';
      case 'debug': return 'text-gray-600 bg-gray-100 border-gray-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getLogLevelIcon = (level) => {
    switch (level) {
      case 'error': return 'AlertCircle';
      case 'warning': return 'AlertTriangle';
      case 'info': return 'Info';
      case 'debug': return 'Bug';
      default: return 'MessageCircle';
    }
  };

  const getComponentColor = (component) => {
    const colors = {
      'whisper-api': 'text-blue-600',
      'elevenlabs': 'text-green-600',
      'gpt-4.1': 'text-purple-600',
      'claude-3.5': 'text-orange-600',
      'gemini-pro': 'text-pink-600',
      'orchestrator': 'text-indigo-600'
    };
    return colors[component] || 'text-gray-600';
  };

  const filteredLogs = logs.filter(log => {
    const matchesLevel = selectedLogLevel === 'all' || log.level === selectedLogLevel;
    const matchesComponent = selectedComponent === 'all' || log.component === selectedComponent;
    const matchesSearch = searchTerm === '' || 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesLevel && matchesComponent && matchesSearch;
  });

  const formatTimestamp = (timestamp) => {
    return timestamp.toLocaleTimeString() + '.' + timestamp.getMilliseconds().toString().padStart(3, '0');
  };

  const handleExportLogs = () => {
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="bg-surface rounded-lg border border-border shadow-minimal">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-text-primary font-heading">
              Real-Time Processing Logs
            </h3>
            <p className="text-text-secondary text-sm mt-1">
              Detailed processing insights with error tracking and performance optimization
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsAutoRefresh(!isAutoRefresh)}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isAutoRefresh
                  ? 'bg-success-100 text-success-700 border border-success-200' :'bg-gray-100 text-gray-700 border border-gray-200'
              }`}
            >
              <Icon name={isAutoRefresh ? "Pause" : "Play"} size={16} className="mr-2" />
              {isAutoRefresh ? 'Live' : 'Paused'}
            </button>
            <Button
              size="sm"
              variant="outline"
              iconName="Download"
              onClick={handleExportLogs}
            >
              Export
            </Button>
            <Button
              size="sm"
              variant="outline"
              iconName="Trash2"
              onClick={handleClearLogs}
            >
              Clear
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-xs font-medium text-text-secondary mb-1">Search</label>
            <div className="relative">
              <Icon name="Search" size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-border rounded-md text-sm"
              />
            </div>
          </div>

          <div>
            <label className="block text-xs font-medium text-text-secondary mb-1">Log Level</label>
            <select
              value={selectedLogLevel}
              onChange={(e) => setSelectedLogLevel(e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md text-sm"
            >
              {logLevels.map(level => (
                <option key={level} value={level}>
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-xs font-medium text-text-secondary mb-1">Component</label>
            <select
              value={selectedComponent}
              onChange={(e) => setSelectedComponent(e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md text-sm"
            >
              {components.map(component => (
                <option key={component} value={component}>
                  {component === 'all' ? 'All Components' : component.charAt(0).toUpperCase() + component.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <div className="text-sm text-text-secondary">
              Showing {filteredLogs.length} of {logs.length} logs
            </div>
          </div>
        </div>
      </div>

      {/* Logs Content */}
      <div className="h-96 overflow-y-auto">
        {filteredLogs.length > 0 ? (
          <div className="divide-y divide-border">
            {filteredLogs.map((log) => (
              <div key={log.id} className="p-4 hover:bg-secondary-50 transition-colors">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center">
                    <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border mr-3 ${getLogLevelColor(log.level)}`}>
                      <Icon name={getLogLevelIcon(log.level)} size={12} className="mr-1" />
                      {log.level.toUpperCase()}
                    </div>
                    <span className={`text-sm font-medium ${getComponentColor(log.component)}`}>
                      {log.component}
                    </span>
                    {log.sessionId && (
                      <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                        {log.sessionId}
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-text-secondary font-mono">
                    {formatTimestamp(log.timestamp)}
                  </span>
                </div>
                
                <div className="mb-2">
                  <p className="text-sm text-text-primary font-medium">{log.message}</p>
                  {log.details && (
                    <p className="text-sm text-text-secondary mt-1">{log.details}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Icon name="FileText" size={48} className="text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-medium text-text-primary mb-2">No Logs Found</h3>
              <p className="text-text-secondary">
                {logs.length === 0 
                  ? 'No system logs available' :'No logs match the current filters'
                }
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="p-4 border-t border-border bg-secondary-50">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-error-600">
              {logs.filter(log => log.level === 'error').length}
            </div>
            <div className="text-xs text-text-secondary">Errors</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-warning-600">
              {logs.filter(log => log.level === 'warning').length}
            </div>
            <div className="text-xs text-text-secondary">Warnings</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-primary-600">
              {logs.filter(log => log.level === 'info').length}
            </div>
            <div className="text-xs text-text-secondary">Info</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-600">
              {logs.filter(log => log.level === 'debug').length}
            </div>
            <div className="text-xs text-text-secondary">Debug</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeLogsPanel;