/**
 * CONTEXT INTEGRATION VALIDATION SCRIPT
 * 
 * Quick validation script to test that context integration fixes are working properly.
 * This script can be run manually to verify the context flow.
 */

import { enhancedPatientContextService } from '../services/EnhancedPatientContextService.js';
import { contextAssemblyService } from '../services/ContextAssemblyService.js';
import { agentOrchestrator } from '../services/AgentOrchestrator.js';
import GeneralPractitionerAgent from '../agents/GeneralPractitionerAgent.js';
import { memoryManager } from '../services/MemoryManager.js';

// Mock patient data for testing
const mockPatientContext = {
  patientProfile: {
    fullName: '<PERSON>',
    age: 45,
    gender: 'male',
    country: 'GH',
    city: 'Accra',
    occupation: 'Teacher',
    preferredLanguage: 'English'
  },
  medicalHistory: {
    conditions: [
      { conditionName: 'Hypertension', severity: 'moderate', isCurrent: true },
      { conditionName: 'Type 2 Diabetes', severity: 'well_controlled', isCurrent: true }
    ],
    medications: [
      { medicationName: 'Lisinopril', dosage: '10mg', frequency: 'once daily', isCurrent: true },
      { medicationName: 'Metformin', dosage: '500mg', frequency: 'twice daily', isCurrent: true }
    ],
    chronicConditions: ['Hypertension', 'Type 2 Diabetes'],
    allergies: ['Penicillin']
  },
  regionalContext: {
    countryCode: 'GH',
    countryName: 'Ghana',
    commonConditions: ['Malaria', 'Hypertension', 'Diabetes'],
    endemicDiseases: ['Malaria', 'Yellow fever'],
    currentSeason: 'dry_season',
    healthcareAccessLevel: 'limited'
  }
};

/**
 * Test context assembly
 */
async function testContextAssembly() {
  console.log('🧪 Testing Context Assembly...');
  
  try {
    const assembledContext = await contextAssemblyService.assembleContext(
      mockPatientContext,
      [],
      'I have chest pain and shortness of breath',
      {
        urgencyLevel: 'high',
        includeRegionalContext: true,
        includeCulturalContext: true
      }
    );

    console.log('✅ Context Assembly Results:');
    console.log('- Token Count:', assembledContext.tokenUsage.contextTokens);
    console.log('- Priority Flags:', assembledContext.priorityFlags.length);
    console.log('- Has Patient Profile:', assembledContext.contextBlock.patientProfile.includes('John Doe'));
    console.log('- Has Medical History:', assembledContext.contextBlock.medicalHistory.includes('Hypertension'));
    console.log('- Has Regional Context:', assembledContext.contextBlock.regionalContext.includes('Ghana'));
    
    return assembledContext;
  } catch (error) {
    console.error('❌ Context Assembly Failed:', error);
    return null;
  }
}

/**
 * Test simplified context assembly
 */
async function testSimplifiedContextAssembly() {
  console.log('🧪 Testing Simplified Context Assembly...');
  
  try {
    const simplifiedContext = await contextAssemblyService.assembleSimplifiedContext(
      mockPatientContext,
      [],
      'I have chest pain and shortness of breath',
      {
        maxTokens: 800,
        urgencyLevel: 'high',
        includeRegionalContext: true
      }
    );

    console.log('✅ Simplified Context Results:');
    console.log('- Context Summary Length:', simplifiedContext.contextSummary.length);
    console.log('- Priority Alerts:', simplifiedContext.priorityAlerts.length);
    console.log('- Key Recommendations:', simplifiedContext.keyRecommendations.length);
    console.log('- Token Count:', simplifiedContext.tokenCount);
    console.log('- Contains Patient Name:', simplifiedContext.contextSummary.includes('John Doe'));
    console.log('- Contains Medical History:', simplifiedContext.contextSummary.includes('Hypertension'));
    
    return simplifiedContext;
  } catch (error) {
    console.error('❌ Simplified Context Assembly Failed:', error);
    return null;
  }
}

/**
 * Test agent context integration
 */
async function testAgentContextIntegration() {
  console.log('🧪 Testing Agent Context Integration...');
  
  try {
    // Create GP agent
    const gpAgent = new GeneralPractitionerAgent(memoryManager);
    await gpAgent.initialize();

    // Create agent request with context
    const agentRequest = {
      sessionId: 'test-session-123',
      userMessage: 'I have chest pain and shortness of breath',
      conversationHistory: [],
      patientContext: mockPatientContext,
      assembledContext: await testContextAssembly(),
      urgencyLevel: 'high'
    };

    // Mock AI orchestrator to capture the request
    let capturedRequest = null;
    const originalGenerateResponse = gpAgent.aiOrchestrator?.generateResponse;
    
    if (gpAgent.aiOrchestrator) {
      gpAgent.aiOrchestrator.generateResponse = async (options) => {
        capturedRequest = options;
        return {
          success: true,
          data: {
            content: 'Mocked context-aware response for John Doe with Hypertension',
            agentType: 'general-practitioner',
            usage: { completion_tokens: 50, total_tokens: 100 },
            processingTime: 1000
          },
          timestamp: new Date().toISOString()
        };
      };
    }

    // Process the request
    const response = await gpAgent.handleMessage(agentRequest);

    console.log('✅ Agent Context Integration Results:');
    console.log('- Response Generated:', !!response.content);
    console.log('- Response Length:', response.content.length);
    console.log('- Confidence Score:', response.confidence);
    console.log('- Emergency Flags:', response.emergencyFlags?.length || 0);
    
    if (capturedRequest) {
      console.log('- Context Passed to AI:', !!capturedRequest.patientContext);
      console.log('- Assembled Context Passed:', !!capturedRequest.assembledContext);
      console.log('- System Prompt Includes Patient:', 
        capturedRequest.messages?.some(msg => 
          msg.role === 'system' && msg.content.includes('John Doe')
        )
      );
    }

    // Restore original method
    if (originalGenerateResponse && gpAgent.aiOrchestrator) {
      gpAgent.aiOrchestrator.generateResponse = originalGenerateResponse;
    }
    
    return response;
  } catch (error) {
    console.error('❌ Agent Context Integration Failed:', error);
    return null;
  }
}

/**
 * Run all validation tests
 */
export async function runContextIntegrationValidation() {
  console.log('🚀 Starting Context Integration Validation...\n');
  
  const results = {
    contextAssembly: false,
    simplifiedContext: false,
    agentIntegration: false
  };

  // Test 1: Context Assembly
  const assembledContext = await testContextAssembly();
  results.contextAssembly = !!assembledContext;
  console.log('');

  // Test 2: Simplified Context Assembly
  const simplifiedContext = await testSimplifiedContextAssembly();
  results.simplifiedContext = !!simplifiedContext;
  console.log('');

  // Test 3: Agent Context Integration
  const agentResponse = await testAgentContextIntegration();
  results.agentIntegration = !!agentResponse;
  console.log('');

  // Summary
  console.log('📊 VALIDATION SUMMARY:');
  console.log('- Context Assembly:', results.contextAssembly ? '✅ PASS' : '❌ FAIL');
  console.log('- Simplified Context:', results.simplifiedContext ? '✅ PASS' : '❌ FAIL');
  console.log('- Agent Integration:', results.agentIntegration ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\n🎯 OVERALL RESULT:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  if (allPassed) {
    console.log('🎉 Context integration fixes are working correctly!');
  } else {
    console.log('⚠️ Some context integration issues detected. Review the logs above.');
  }

  return results;
}

// Export for manual testing
export {
  testContextAssembly,
  testSimplifiedContextAssembly,
  testAgentContextIntegration
};
